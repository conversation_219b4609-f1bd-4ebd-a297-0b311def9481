<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="816650b5-6236-423e-ba39-82e16b240280" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="TypeScript JSX File" />
        <option value="TypeScript File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="main" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2mnXaTdV1yijkHVLDO7qPQM6MlS" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Node.js.middleware.ts.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "feat/edit-job-settings",
    "javascript.nodejs.core.library.configured.version": "20.17.0",
    "javascript.nodejs.core.library.typings.version": "20.17.58",
    "js.debugger.nextJs.config.created.client": "true",
    "junie.onboarding.icon.badge.shown": "true",
    "last_opened_file_path": "/Users/<USER>/Documents/liberty/getlinked_main_frontend/src/app/(website-main)/e/teams/misc/api",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_interpreter_path": "/usr/local/bin/node",
    "nodejs_package_manager_path": "npm",
    "npm.dev.executor": "Run",
    "prettierjs.PrettierConfiguration.Package": "/Users/<USER>/Documents/liberty/getlinked_main_frontend/node_modules/prettier",
    "settings.editor.selected.configurable": "preferences.lookFeel",
    "to.speed.mode.migration.done": "true",
    "ts.external.directory.path": "/Users/<USER>/Documents/liberty/getlinked_main_frontend/node_modules/typescript/lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/src/app/(website-main)/e/teams/misc/api" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/src/app/(website-main)/e/assessments-and-interviews/assessments/[assessment_id]/components" />
      <recent name="$PROJECT_DIR$/src/app/(website-main)/e/assessments-and-interviews/assessments/[assessment_id]/edit/settings/components/proctoring" />
    </key>
  </component>
  <component name="RunManager" selected="npm.dev">
    <configuration name="Next.js: debug client-side" type="JavascriptDebugType" uri="http://localhost:3000/">
      <method v="2" />
    </configuration>
    <configuration name="middleware.ts" type="NodeJSConfigurationType" temporary="true" nameIsGenerated="true" path-to-js-file="$PROJECT_DIR$/src/middleware.ts" typescript-loader="bundled" working-dir="$PROJECT_DIR$/src">
      <method v="2" />
    </configuration>
    <configuration name="dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.dev" />
        <item itemvalue="Node.js.middleware.ts" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-WS-251.26094.131" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="816650b5-6236-423e-ba39-82e16b240280" name="Changes" comment="" />
      <created>1727713377677</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1727713377677</updated>
      <workItem from="1727713380398" duration="28000" />
      <workItem from="1727734026433" duration="10504000" />
      <workItem from="1727866885858" duration="55000" />
      <workItem from="1727866954112" duration="13358000" />
      <workItem from="1727954797360" duration="345000" />
      <workItem from="1727958526775" duration="1472000" />
      <workItem from="1727984845582" duration="111000" />
      <workItem from="1727992519580" duration="78000" />
      <workItem from="1728032928338" duration="234000" />
      <workItem from="1728033178487" duration="5293000" />
      <workItem from="1728251135793" duration="4980000" />
      <workItem from="1728478969359" duration="8000" />
      <workItem from="1731438624648" duration="889000" />
      <workItem from="1732087508078" duration="15814000" />
      <workItem from="1742380230611" duration="1359000" />
      <workItem from="1742816438386" duration="2208000" />
      <workItem from="1742977929617" duration="499000" />
      <workItem from="1742983677617" duration="4759000" />
      <workItem from="1743114253307" duration="906000" />
      <workItem from="1743147046643" duration="6882000" />
      <workItem from="1743340940279" duration="6493000" />
      <workItem from="1743503914047" duration="3717000" />
      <workItem from="1743581890997" duration="77000" />
      <workItem from="1743586754541" duration="1177000" />
      <workItem from="1750233368783" duration="381000" />
      <workItem from="1750234109083" duration="7020000" />
      <workItem from="1750251955473" duration="3461000" />
      <workItem from="1750340554231" duration="548000" />
      <workItem from="1750345386031" duration="2072000" />
      <workItem from="1750378282506" duration="4147000" />
      <workItem from="1752483774660" duration="5127000" />
      <workItem from="1752743693119" duration="257000" />
      <workItem from="1753085549089" duration="250000" />
      <workItem from="1753095334303" duration="1916000" />
      <workItem from="1753176446605" duration="1007000" />
      <workItem from="1753867933679" duration="4253000" />
      <workItem from="1754063424138" duration="228000" />
      <workItem from="1755246393752" duration="3848000" />
      <workItem from="1755256665428" duration="1063000" />
      <workItem from="1755262871979" duration="3953000" />
      <workItem from="1755808911453" duration="141000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
    <option name="exactExcludedFiles">
      <list>
        <option value="$PROJECT_DIR$/src/components/icons/SearchIcon.jsx" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="playground" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/app/(assessments)/company-test/start-test/page.tsx</url>
          <line>211</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>