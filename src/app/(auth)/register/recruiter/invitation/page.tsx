'use client'

import * as React from 'react';
import Image from 'next/image';
import { RecruiterInviteSignUpFlow } from '@/app/(auth)/misc/components/recruiter';
import AuthLeftSide from '@/app/(auth)/misc/components/shared/AuthLeftSide';
import { RecruiterLoginSlideshowContent } from '@/app/(auth)/misc/constants/sideshow-options';
import AuthContentPage from '@/app/(auth)/misc/components/shared/AuthContentPage';



export default function RecruiterSignupPage() {
  return (
    <div className="w-screen flex flex-col-reverse lg:grid lg:max-xl:grid-cols-[0.9fr,1fr] xl:grid-cols-2 h-screen justify-between overflow-auto bg-[#150021]  md:pb-0">
      <AuthLeftSide slideShowContent={RecruiterLoginSlideshowContent} />



      <AuthContentPage>
        <RecruiterInviteSignUpFlow />
      </AuthContentPage>
    </div>
  );
}
