'use client'

import Image from 'next/image';
import * as React from 'react';

import { ConfirmActionModal } from '@/components/shared';
import { useUser } from '@/lib/contexts/UserContext';
import useUserStore from '@/store/userInfoStore';
import { getAccessToken, hasTokenExpired, removeAccessToken } from '@/utils/tokens';
// import { AnimatePresence, motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { TalentSignUpFlow, TalentSlideshow } from '../../misc/components/talent';
import { RecruiterLoginSlideshowContent } from '../../misc/constants/sideshow-options';
import AuthLeftSide from '../../misc/components/shared/AuthLeftSide';
import AuthContentPage from '../../misc/components/shared/AuthContentPage';



export default function OnboardingLayout() {
  const { user, isLoading } = useUser();
  const router = useRouter();
  const [showLogoutModal, setShowLogoutModal] = React.useState(false);


  const { setUser: setSavedUser } = useUserStore();
  const [validSession, setValidSession] = useState(false);

  const logout = () => {
    removeAccessToken();
    setSavedUser(null);
    setShowLogoutModal(false);
  };
  const accessToken = getAccessToken();
  useEffect(() => {
    if ((accessToken && hasTokenExpired(accessToken)) || !accessToken) {
      setValidSession(false);
    } else {
      setValidSession(true);
    }
  }, [accessToken]);

  return (
    <div className="w-screen flex flex-col-reverse lg:grid lg:max-xl:grid-cols-[0.9fr,1fr] xl:grid-cols-2 h-screen justify-between overflow-auto bg-[#150021]  md:pb-0">

      <AuthLeftSide slideShowContent={RecruiterLoginSlideshowContent} />
      {/* <TalentSlideshow /> */}


      <section className="grow flex items-center justify-center relative px-4 pb-0 lg:m-0 lg:py-0 lg:flex lg:basis-1/2 lg:flex-col lg:justify-center lg:overflow-y-auto  lg:[@media(min-height:520px)]:items-center max-lg:max-h-[84vh]">
        {/* {!isLoading && user && validSession ? (
          <AnimatePresence>
            <div className='relative z-[3] gap-6 overflow-y-scroll  my-auto  max-lg:h-full w-full no-scrollbar '>
              <div className='flex items-center justify-center relative overflow-y-scroll rounded-2xl mt-[10vh] mb-[5vh] lg:max-xl:my-[6vh] xl:my-[6.5vh] w-full '>
                <article className='relative flex flex-col items-center justify-center px-6 md:max-lg:px-10 py-6 lg:px-8 rounded-2xl max-h-full overflow-y-scroll overflow-x-hidden backdrop-blur-lg w-[95%] md:max-lg:!w-[75%] lg:max-xl:!w-[85%]   2xl:w-[600px] max-w-[610px] lg:min-h-[85vh]'>
                  <AnimatePresence>
                    <motion.div className={cn("flex flex-col items-center justify-center w-full  min-h-[58vh] lg:min-h-[72vh] 3xl:min-h-[750px] h-full my-auto text-white",)}
                        initial={{ x: '110%', }}
                        animate={{ x: "0%", }}
                        exit={{ x: '-120%', }}
                        transition={{ duration: 0.5, ease: 'linear' }}
                      >
                        
                        <div className="text-center p-6 rounded-lg bg-white/10 backdrop-blur-sm">
                          <h2 className="text-xl text-white mb-4">You are currently logged in</h2>
                          <div className="flex gap-4 justify-center">
                            <button
                              onClick={() => router.push('/e/profile')}
                              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/80 transition-colors"
                            >
                              Go to Profile
                            </button>
                            <button
                              onClick={() => setShowLogoutModal(true)}
                              className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                            >
                              Logout to Continue
                            </button>
                          </div>
                        </div>
                    </motion.div>
                  </AnimatePresence>
                  <div className='absolute inset-0 z-[-1] rounded-[1.1875rem] from-[#EDF4FF] from-[-31.2%] to-white/20 to-[24.74%] opacity-40 bg-gradient-358'></div>
                </article>
              </div>
            </div>
          </AnimatePresence>
        ): ( */}
        <AuthContentPage>
          <TalentSignUpFlow />
        </AuthContentPage>
        {/* )} */}

        {/* <div className="absolute inset-y-0 left-[-10%] right-0 lg:fixed lg:left-auto lg:max-xl:w-[51.75vw] lg:w-[50vw]" aria-hidden>
          <Image
            alt="Job seekers in a queue"
            blurDataURL="eDI4z}~TIW_49a]yyD=_Ip%N00nh%MogxtI[^iS$of9ajDxaNexCoz"
            className="w-full object-cover object-bottom"
            placeholder="blur"
            sizes="100vw"
            src="/images/auth-page/recruiter-register-image.png"
            fill
          />
          <div className='absolute inset-0 -scale-x-100 from-[#030004] from-[-3.89%] to-[#502B67] to-[108.49%] bg-gradient-[262deg] opacity-75' />
        </div> */}

        <ConfirmActionModal
          isModalOpen={showLogoutModal}
          closeModal={() => setShowLogoutModal(false)}
          title="Confirm Logout"
          confirmFunction={logout}
        >
          <p className='text-[#8C8CA1] text-sm font-normal'>
            Are you sure you want to log out? You will need to log in again to access your account.
          </p>
        </ConfirmActionModal>

      </section>
    </div>
  );
}
