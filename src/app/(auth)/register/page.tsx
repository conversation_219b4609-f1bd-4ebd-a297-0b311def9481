'use client'

import * as React from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion'
import { Briefcase, User } from 'iconsax-react';
import Link from 'next/link';

import { cn } from '@/utils';

import { SharedSlideshow } from '../misc/components/shared';



export default function OnboardingLayoutCompleteRegistration() {



    return (
        <div className="w-screen flex flex-col-reverse lg:grid lg:max-xl:grid-cols-[0.9fr,1fr] xl:grid-cols-2 h-screen justify-between overflow-auto bg-[#150021]  md:pb-0">
            <SharedSlideshow />


            <section className="grow flex items-center justify-center relative px-4 pb-0 lg:m-0 lg:py-0 lg:flex lg:basis-1/2 lg:flex-col lg:justify-center lg:overflow-y-auto  lg:[@media(min-height:520px)]:items-center max-lg:max-h-[84vh]">
                <div className='relative z-[3] gap-6 overflow-y-scroll my-auto  max-lg:h-full w-full no-scrollbar '>
                    <div className='flex items-center justify-center relative overflow-y-scroll rounded-2xl mt-[10vh] mb-[5vh] lg:max-xl:my-[6vh] xl:my-[6.5vh] w-full '>
                        <article className='relative flex items-center justify-center px-6 md:max-lg:px-10 py-6 lg:px-10 rounded-2xl max-h-full overflow-y-scroll  w-[95%] md:max-lg:!w-[75%] lg:max-xl:!w-[85%] 2xl:w-[600px] max-w-[610px] lg:min-h-[85vh] backdrop-blur-lg'>
                            <motion.div className={cn("flex flex-col justify-center w-full  min-h-[58vh] lg:min-h-[72vh] 3xl:min-h-[750px] h-full my-auto transition-transform duration-300 text-white",)}>
                                <header className='flex flex-col gap-1.5 self-start mb-8'>
                                    <h1 className='text-3xl lg:text-[2.35rem] text-left font-clash font-semibold'>Register</h1>
                                    <p className="text-[#BCBCBC] text-sm md:font-base">Create an account on Getlinked.<br className='md:hidden' /> Continue by clicking on the type of account you want to create.</p>
                                </header>



                                <section className='flex flex-col gap-6 w-full mt-[5vh]'>
                                    <Link
                                        href="/register/recruiter"
                                        className="flex items-start gap-4 rounded-lg p-4 bg-white/20 backdrop-blur-lg hover:bg-primary/30 hover:text-white hover:backdrop-blur-lg "
                                    >
                                        <div className="block grow items-start justify-center rounded-full bg-white p-3">
                                            <Briefcase size={25} className="text-[#755AE2]" />
                                        </div>

                                        <div className="flex flex-col">
                                            <h3 className="text-[1.35rem] font-medium text-white font-clash">
                                                Recruiter
                                            </h3>
                                            <p className="text-[0.9rem] font-light">
                                                Unlock the power of elite talent acquisition. Our all-in-one platform transforms your hiring from tedious to triumphant. From job creation to candidate selection, gain access to world-class tools and tap into a goldmine of top professionals.
                                                {/* Don't just fill positions—build empires. */}
                                            </p>
                                        </div>
                                    </Link>
                                    <Link
                                        href="/register/talent"
                                        className="flex items-start gap-4 rounded-lg p-4 bg-white/20 backdrop-blur-lg hover:bg-primary/30 hover:text-white hover:backdrop-blur-lg "
                                    >
                                        <div className="block grow items-start justify-center rounded-full bg-white p-3">
                                            <User size={25} className="text-[#755AE2]" />
                                        </div>
                                        <div className="flex flex-col">
                                            <h3 className="text-[1.35rem] font-medium text-white font-clash">
                                                Talent
                                            </h3>
                                            <p className="text-[0.95rem] font-light">
                                                Forge your dream career path with precision. Our talent platform doesn't just find jobs—it alllows you to showcase your unique skills, polish your resume,
                                                and ace interviews. From coveted internships to career-defining roles, your next big break is waiting.
                                            </p>
                                        </div>
                                    </Link>
                                </section>



                            </motion.div>
                            <div className='absolute inset-0 z-[-1] rounded-[1.1875rem] from-[#EDF4FF] from-[-31.2%] to-white/20 to-[24.74%] opacity-40 bg-gradient-358 '></div>
                        </article>
                    </div>
                </div>

                <div className="absolute inset-y-0 left-[-10%] right-0 lg:fixed lg:left-auto lg:max-xl:w-[51.75vw] lg:w-[50vw]" aria-hidden>
                    <Image
                        alt="Job seekers in a queue"
                        blurDataURL="eDI4z}~TIW_49a]yyD=_Ip%N00nh%MogxtI[^iS$of9ajDxaNexCoz"
                        className="w-full object-cover object-bottom"
                        placeholder="blur"
                        sizes="100vw"
                        src="/images/auth-page/recruiter-register-image.png"
                        fill
                    />
                    <div className='absolute inset-0 -scale-x-100 from-[#030004] from-[-3.89%] to-[#502B67] to-[108.49%] bg-gradient-[262deg] opacity-75' /></div>
            </section>
        </div>
    );
}
