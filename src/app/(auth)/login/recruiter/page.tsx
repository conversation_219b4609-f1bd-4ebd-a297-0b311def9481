'use client'

import * as React from 'react';
import Image from 'next/image';

import { RecruiterSignInFlow } from '../../misc/components/recruiter';
import AuthContentPage from '../../misc/components/shared/AuthContentPage';

import AuthLeftSide from '../../misc/components/shared/AuthLeftSide';
import { RecruiterLoginSlideshowContent } from '../../misc/constants/sideshow-options';


export default function OnboardingLayout() {



  return (
    <div className="w-screen flex flex-col-reverse lg:grid lg:max-xl:grid-cols-[0.9fr,1fr] xl:grid-cols-2 h-screen justify-between overflow-auto bg-[#12001D]  md:pb-0">

      <AuthLeftSide slideShowContent={RecruiterLoginSlideshowContent} />

      <AuthContentPage>
        <RecruiterSignInFlow />
      </AuthContentPage>
    </div>
  );
}
