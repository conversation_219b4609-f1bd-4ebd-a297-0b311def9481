"use client"
import React from 'react'
import Link from 'next/link'

import <PERSON><PERSON><PERSON><PERSON><PERSON> from '../misc/icons/GetLinkLogo'
import BriefcaseIcon from '../misc/icons/BriefcaseIcon'
import UserIcon from '../misc/icons/UserIcon'

const LoginPage = () => {
    return (
        <div className='grow flex flex-col w-screen h-screen bg-[#F5F3FF] p-[1rem] sm:p-[2.94rem] '>
            <div className="flex justify-between items-center z-[5] relative">
                <div className="flex gap-x-[0.8rem] items-center">
                    <GetLinkLogo />
                    <h2 className='text-[1.5rem] font-bold  text-primary font-grotesk'>Getlinked</h2>
                </div>
            </div>
            <div className="grow flex justify-center items-center flex-1 h-full flex-col">
                <div className="">
                    <h2 className='text-primary text-[1.875rem] font-medium'>Select login option</h2>
                </div>

                <div className="flex justify-center flex-col sm:flex-row items-center gap-[2rem] mt-[2rem] translate-y-[10%]">

                    <Link href={'/login/talent'} className={`bg-[url('/images/talent.svg')] hover:bg-[url('/images/recruiter.svg')] transition-all duration-500 placeholder: border-2 border-transparent hover:border-primary w-[14.25rem] h-[12.98rem] rounded-[1.3rem] bg-cover bg-no-repeat flex justify-center items-center flex-col`}>
                        <div className="bg-white w-[5.5rem] h-[5.5rem] rounded-full flex justify-center items-center">
                            <UserIcon />
                        </div>
                        <p className='text-primary text-[1.25rem] font-medium mt-6'>Login as talent</p>
                    </Link>
                    <Link href={'/login/recruiter'} className={`bg-[url('/images/talent.svg')] hover:bg-[url('/images/recruiter.svg')] transition-all duration-500 border-2 border-transparent hover:border-primary w-full sm:w-[14.25rem] rounded-[1.3rem] h-[12.98rem] bg-cover bg-no-repeat flex justify-center items-center flex-col`}>
                        <div className="bg-white w-[5.5rem] h-[5.5rem] rounded-full flex justify-center items-center">
                            <BriefcaseIcon />
                        </div>
                        <p className='text-primary text-[1.25rem] font-medium mt-6'>Login as recruiter</p>
                    </Link>
                </div>
            </div>
        </div>
    )
}

export default LoginPage