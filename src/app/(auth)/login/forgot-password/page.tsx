'use client';

import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import * as React from 'react';
// import { RecruiterSlideshow } from '../../misc/components/recruiter';
import { ForgotPasswordFlow } from '../../misc/components/shared';
import { TalentSlideshow } from '../../misc/components/talent';
import { RecruiterLoginSlideshowContent, TalentslideshowContent } from '../../misc/constants/sideshow-options';
import AuthLeftSide from '../../misc/components/shared/AuthLeftSide';
import AuthContentPage from '../../misc/components/shared/AuthContentPage';

const ForgotPasswordPage = () => {
  const params = useSearchParams();
  const user = params.get('user') || 'talent';
  return (
    <div className="flex h-screen w-screen flex-col-reverse justify-between overflow-auto bg-[#150021] md:pb-0 lg:grid xl:grid-cols-2  lg:max-xl:grid-cols-[0.9fr,1fr]">

      <AuthLeftSide slideShowContent={user === 'talent' ? TalentslideshowContent : RecruiterLoginSlideshowContent} />


      <AuthContentPage>
        <ForgotPasswordFlow />
      </AuthContentPage>
      {/* <section className="relative flex grow items-center justify-center px-4 pb-0 lg:m-0 lg:flex lg:basis-1/2 lg:flex-col lg:justify-center lg:overflow-y-auto lg:py-0  max-lg:max-h-[84vh] lg:[@media(min-height:520px)]:items-center">
        <ForgotPasswordFlow />

        <div
          className="absolute inset-y-0 left-[-10%] right-0 lg:fixed lg:left-auto lg:w-[50vw] lg:max-xl:w-[51.75vw]"
          aria-hidden
        >
          <Image
            alt="Job seekers in a queue"
            blurDataURL="eDI4z}~TIW_49a]yyD=_Ip%N00nh%MogxtI[^iS$of9ajDxaNexCoz"
            className="w-full object-cover object-bottom"
            placeholder="blur"
            sizes="100vw"
            src="/images/auth-page/recruiter-register-image.png"
            fill
          />
          <div className="absolute inset-0 -scale-x-100 from-[#030004] from-[-3.89%] to-[#502B67] to-[108.49%] opacity-80 bg-gradient-[262deg]" />
        </div>
      </section> */}
    </div>
  );
};
export default ForgotPasswordPage;
