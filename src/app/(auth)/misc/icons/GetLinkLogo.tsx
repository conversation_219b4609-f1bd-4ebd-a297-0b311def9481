import * as React from "react";
import { SVGProps } from "react";
const GetLinkLogo = (props: SVGProps<SVGSVGElement>) => (
    <svg
        width={48}
        height={27}
        viewBox="0 0 48 27"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            d="M24 13.5C24 19.299 19.299 24 13.5 24C7.70101 24 3 19.299 3 13.5C3 7.70101 7.70101 3 13.5 3C19.299 3 24 7.70101 24 13.5Z"
            stroke={props.stroke || "#755AE2"}
            strokeWidth={6}
        />
        <path
            d="M46.687 13.5C46.687 20.6797 40.8667 26.5 33.687 26.5C26.5073 26.5 20.687 20.6797 20.687 13.5C20.687 6.3203 26.5073 0.5 33.687 0.5C40.8667 0.5 46.687 6.3203 46.687 13.5Z"
            stroke={props.stroke || "#755AE2"}
        />
    </svg>
);
export default GetLinkLogo;
