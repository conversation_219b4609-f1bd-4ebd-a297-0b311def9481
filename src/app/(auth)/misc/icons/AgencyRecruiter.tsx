import * as React from "react";
import { SVGProps } from "react";
const AgencyRecruiter = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width={40}
        height={40}
        viewBox="0 0 40 40"
        fill="none"
        {...props}
    >
        <path
            d="M20.0001 10.6667C22.9334 10.6667 25.3334 8.26667 25.3334 5.33333C25.3334 2.4 22.9334 0 20.0001 0C17.0667 0 14.6667 2.4 14.6667 5.33333C14.6667 8.26667 17.0667 10.6667 20.0001 10.6667Z"
            fill={props.fill ? props.fill : '#755AE2'}
        />
        <path
            d="M30 15.3333C30 13.4667 28.5333 12 26.6667 12H13.3333C11.4667 12 10 13.4667 10 15.3333V24H30V15.3333Z"
            fill={props.fill ? props.fill : '#755AE2'}
        />
        <path
            d="M35.3333 29.3987V28.6654C35.3333 27.532 34.4667 26.6654 33.3333 26.6654H20.6667V25.332H19.3333V26.6654H6.66667C5.53333 26.6654 4.66667 27.532 4.66667 28.6654V29.3987C2.06667 29.732 0 31.9987 0 34.6654C0 37.5987 2.4 39.9987 5.33333 39.9987C8.26667 39.9987 10.6667 37.5987 10.6667 34.6654C10.6667 31.932 8.6 29.732 6 29.3987V28.6654C6 28.2654 6.26667 27.9987 6.66667 27.9987H19.3333V29.3987C16.7333 29.732 14.6667 31.9987 14.6667 34.6654C14.6667 37.5987 17.0667 39.9987 20 39.9987C22.9333 39.9987 25.3333 37.5987 25.3333 34.6654C25.3333 31.932 23.2667 29.732 20.6667 29.3987V27.9987H33.3333C33.7333 27.9987 34 28.2654 34 28.6654V29.3987C31.4 29.732 29.3333 31.9987 29.3333 34.6654C29.3333 37.5987 31.7333 39.9987 34.6667 39.9987C37.6 39.9987 40 37.5987 40 34.6654C40 31.932 37.9333 29.732 35.3333 29.3987Z"
            fill={props.fill ? props.fill : '#755AE2'}
        />
    </svg>
);
export default AgencyRecruiter;
