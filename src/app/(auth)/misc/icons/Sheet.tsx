import * as React from "react";
import { SVGProps } from "react";
const Sheet = (props: SVGProps<SVGSVGElement>) => (
    <svg
        width={44}
        height={44}
        viewBox="0 0 44 44"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            d="M38.5 12.8327V31.166C38.5 36.666 35.75 40.3327 29.3333 40.3327H14.6667C8.25 40.3327 5.5 36.666 5.5 31.166V12.8327C5.5 7.33268 8.25 3.66602 14.6667 3.66602H29.3333C35.75 3.66602 38.5 7.33268 38.5 12.8327Z"
            stroke="white"
            strokeWidth={1.5}
            strokeMiterlimit={10}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M26.5833 8.25V11.9167C26.5833 13.9333 28.2333 15.5833 30.2499 15.5833H33.9166"
            stroke="white"
            strokeWidth={1.5}
            strokeMiterlimit={10}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M14.6667 23.834H22.0001"
            stroke="white"
            strokeWidth={1.5}
            strokeMiterlimit={10}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M14.6667 31.166H29.3334"
            stroke="white"
            strokeWidth={1.5}
            strokeMiterlimit={10}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);
export default Sheet;
