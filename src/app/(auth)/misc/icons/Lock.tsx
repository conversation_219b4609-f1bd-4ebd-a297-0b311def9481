import * as React from "react";
import { SVGProps } from "react";
const Lock = (props: SVGProps<SVGSVGElement>) => (
    <svg
        width={20}
        height={20}
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            d="M5 8.33366V6.66699C5 3.90866 5.83333 1.66699 10 1.66699C14.1667 1.66699 15 3.90866 15 6.66699V8.33366"
            stroke={props.stroke || "#292D32"}
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M14.1666 18.333H5.83329C2.49996 18.333 1.66663 17.4997 1.66663 14.1663V12.4997C1.66663 9.16634 2.49996 8.33301 5.83329 8.33301H14.1666C17.5 8.33301 18.3333 9.16634 18.3333 12.4997V14.1663C18.3333 17.4997 17.5 18.333 14.1666 18.333Z"
            stroke={props.stroke || "#292D32"}
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M13.3304 13.3337H13.3379"
            stroke={props.stroke || "#292D32"}
            strokeWidth={2}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M9.9962 13.3337H10.0037"
            stroke={props.stroke || "#292D32"}
            strokeWidth={2}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M6.66209 13.3337H6.66957"
            stroke={props.stroke || "#292D32"}
            strokeWidth={2}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);
export default Lock;
