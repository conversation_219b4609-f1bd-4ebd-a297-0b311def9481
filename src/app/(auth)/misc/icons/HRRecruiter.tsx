import * as React from "react";
import { SVGProps } from "react";
const HRRecruiter = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width={34}
        height={34}
        viewBox="0 0 34 34"
        fill="none"
        {...props}
    >
        <path
            d="M28.6875 14.0569V4.00563C28.6875 2.38 27.37 1.0625 25.7444 1.0625H8.25563C6.63 1.0625 5.3125 2.38 5.3125 4.00563V14.0675C5.3125 15.6931 6.63 17.0106 8.25563 17.0106H12.155L14.4819 20.8994C15.0131 21.7812 15.98 22.3231 17 22.3231C18.02 22.3231 18.9975 21.7812 19.5181 20.8994L21.845 17.0106H25.7444C27.37 17.0106 28.6875 15.6931 28.6875 14.0675V14.0569ZM10.625 5.84375H11.6875C12.2719 5.84375 12.75 6.32188 12.75 6.90625C12.75 7.49062 12.2719 7.96875 11.6875 7.96875H10.625C10.0406 7.96875 9.5625 7.49062 9.5625 6.90625C9.5625 6.32188 10.0406 5.84375 10.625 5.84375ZM19.125 12.2188H10.625C10.0406 12.2188 9.5625 11.7406 9.5625 11.1562C9.5625 10.5719 10.0406 10.0938 10.625 10.0938H19.125C19.7094 10.0938 20.1875 10.5719 20.1875 11.1562C20.1875 11.7406 19.7094 12.2188 19.125 12.2188ZM23.375 12.2188H22.3125C21.7281 12.2188 21.25 11.7406 21.25 11.1562C21.25 10.5719 21.7281 10.0938 22.3125 10.0938H23.375C23.9594 10.0938 24.4375 10.5719 24.4375 11.1562C24.4375 11.7406 23.9594 12.2188 23.375 12.2188ZM23.375 7.96875H14.875C14.2906 7.96875 13.8125 7.49062 13.8125 6.90625C13.8125 6.32188 14.2906 5.84375 14.875 5.84375H23.375C23.9594 5.84375 24.4375 6.32188 24.4375 6.90625C24.4375 7.49062 23.9594 7.96875 23.375 7.96875Z"
            fill={props.fill ? props.fill : '#755AE2'}
        />
        <path
            d="M27.625 25.5C29.3854 25.5 30.8125 24.0729 30.8125 22.3125C30.8125 20.5521 29.3854 19.125 27.625 19.125C25.8646 19.125 24.4375 20.5521 24.4375 22.3125C24.4375 24.0729 25.8646 25.5 27.625 25.5Z"
            fill={props.fill ? props.fill : '#755AE2'}
        />
        <path
            d="M31.4075 26.0312C30.4406 27.0088 29.1019 27.625 27.625 27.625C26.1481 27.625 24.8094 27.0088 23.8425 26.0312C22.8969 26.9875 22.3125 28.305 22.3125 29.75V30.2812C22.3125 31.7475 23.5025 32.9375 24.9688 32.9375H30.2812C31.7475 32.9375 32.9375 31.7475 32.9375 30.2812V29.75C32.9375 28.305 32.3531 26.9875 31.4075 26.0312Z"
            fill={props.fill ? props.fill : '#755AE2'}
        />
        <path
            d="M6.375 25.5C8.13541 25.5 9.5625 24.0729 9.5625 22.3125C9.5625 20.5521 8.13541 19.125 6.375 19.125C4.61459 19.125 3.1875 20.5521 3.1875 22.3125C3.1875 24.0729 4.61459 25.5 6.375 25.5Z"
            fill={props.fill ? props.fill : '#755AE2'}
        />
        <path
            d="M6.375 27.625C4.89812 27.625 3.55938 27.0088 2.5925 26.0312C1.64688 26.9875 1.0625 28.305 1.0625 29.75V30.2812C1.0625 31.7475 2.2525 32.9375 3.71875 32.9375H9.03125C10.4975 32.9375 11.6875 31.7475 11.6875 30.2812V29.75C11.6875 28.305 11.1031 26.9875 10.1575 26.0312C9.19063 27.0088 7.85187 27.625 6.375 27.625Z"
        />
    </svg>
);
export default HRRecruiter;
