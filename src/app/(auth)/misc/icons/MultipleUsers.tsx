import * as React from "react";
import { SVGProps } from "react";
const MultipleUsers = (props: SVGProps<SVGSVGElement>) => (
    <svg
        width={44}
        height={44}
        viewBox="0 0 44 44"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            d="M16.7934 19.9277C16.6101 19.9093 16.3901 19.9093 16.1884 19.9277C11.8251 19.781 8.36011 16.206 8.36011 11.806C8.36011 7.31435 11.9901 3.66602 16.5001 3.66602C20.9918 3.66602 24.6401 7.31435 24.6401 11.806C24.6218 16.206 21.1568 19.781 16.7934 19.9277Z"
            stroke="white"
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M30.085 7.33398C33.6417 7.33398 36.5017 10.2123 36.5017 13.7507C36.5017 17.2157 33.7517 20.039 30.3233 20.1673C30.1767 20.149 30.0117 20.149 29.8467 20.1673"
            stroke="white"
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M7.62657 26.694C3.18991 29.664 3.18991 34.504 7.62657 37.4557C12.6682 40.829 20.9366 40.829 25.9782 37.4557C30.4149 34.4857 30.4149 29.6457 25.9782 26.694C20.9549 23.339 12.6866 23.339 7.62657 26.694Z"
            stroke="white"
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M33.6233 36.666C34.9433 36.391 36.19 35.8593 37.2166 35.071C40.0766 32.926 40.0766 29.3877 37.2166 27.2427C36.2083 26.4727 34.98 25.9593 33.6783 25.666"
            stroke="white"
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);
export default MultipleUsers;
