import * as React from "react";
import { SVGProps } from "react";
const PersonalRecruiter = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width={40}
        height={40}
        viewBox="0 0 45 45"
        fill="none"
        {...props}
    >
        <path
            d="M34.3469 12.7636C33.4653 9.62808 31.7262 6.8008 29.3253 4.59989C26.9243 2.39899 23.9568 0.911807 20.7566 0.305691C17.5564 -0.300426 14.2506 -0.00142167 11.2111 1.16906C8.17162 2.33954 5.51909 4.33504 3.55201 6.93102C1.58493 9.52701 0.381351 12.6204 0.0767193 15.8632C-0.227913 19.106 0.378487 22.3695 1.82768 25.2864C3.27688 28.2033 5.51136 30.6579 8.27969 32.374C11.048 34.0901 14.2403 34.9995 17.4974 35C17.9273 35 18.3572 35 18.7833 34.9543C22.086 34.7053 25.2508 33.5262 27.9115 31.5536C30.5721 29.581 32.6199 26.8955 33.8181 23.8076C33.8789 23.663 33.9284 23.5223 33.9778 23.3777C35.2012 19.9645 35.3341 16.2556 34.3583 12.7636H34.3469ZM29.4887 18.2609H31.4175C31.3511 19.5705 31.0948 20.8635 30.6566 22.0995C30.6495 22.1279 30.6406 22.1558 30.63 22.1832C30.5996 22.2707 30.5691 22.3582 30.5349 22.4457C30.5264 22.4729 30.5163 22.4996 30.5045 22.5255C29.681 24.6413 28.3557 26.5252 26.6425 28.015C24.9294 29.5048 22.8798 30.5559 20.6702 31.0777C19.9639 31.2462 19.2451 31.3569 18.5208 31.4087C18.4333 31.4087 18.3458 31.4087 18.2583 31.4087V29.3125C18.2583 29.1107 18.1781 28.9172 18.0354 28.7745C17.8927 28.6318 17.6992 28.5516 17.4974 28.5516C17.2956 28.5516 17.1021 28.6318 16.9594 28.7745C16.8167 28.9172 16.7365 29.1107 16.7365 29.3125V31.4201C13.3087 31.2289 10.0715 29.7811 7.64388 27.3535C5.21626 24.9259 3.76847 21.6887 3.57729 18.2609H5.5061C5.70789 18.2609 5.90142 18.1807 6.04411 18.038C6.18681 17.8953 6.26697 17.7018 6.26697 17.5C6.26697 17.2982 6.18681 17.1047 6.04411 16.962C5.90142 16.8193 5.70789 16.7391 5.5061 16.7391H3.57729C3.76765 13.311 5.21517 10.0734 7.64297 7.64557C10.0708 5.21777 13.3084 3.77025 16.7365 3.5799V5.32609C16.7365 5.52789 16.8167 5.72142 16.9594 5.86411C17.1021 6.0068 17.2956 6.08696 17.4974 6.08696C17.6992 6.08696 17.8927 6.0068 18.0354 5.86411C18.1781 5.72142 18.2583 5.52789 18.2583 5.32609V3.5799C21.1492 3.74178 23.9184 4.79821 26.1825 6.60301C28.4467 8.4078 30.094 10.8717 30.8963 13.6538C30.9056 13.676 30.9133 13.6989 30.9191 13.7223C31.1982 14.7059 31.3654 15.718 31.4175 16.7391H29.4887C29.2869 16.7391 29.0934 16.8193 28.9507 16.962C28.808 17.1047 28.7278 17.2982 28.7278 17.5C28.7278 17.7018 28.808 17.8953 28.9507 18.038C29.0934 18.1807 29.2869 18.2609 29.4887 18.2609Z"
            fill={props.fill ? props.fill : '#755AE2'}
        />
        <path
            d="M23.3522 23.1529V25.7665C23.3522 25.9955 23.2612 26.2152 23.0993 26.3771C22.9373 26.5391 22.7177 26.6301 22.4886 26.6301H11.9544C11.7254 26.6301 11.5057 26.5391 11.3438 26.3771C11.1818 26.2152 11.0908 25.9955 11.0908 25.7665V23.1529C11.1084 22.2915 11.3418 21.4482 11.7699 20.7004C12.198 19.9527 12.8069 19.3243 13.5408 18.873C14.2986 18.369 15.1542 18.0307 16.0517 17.8801C16.4374 17.813 16.8281 17.7786 17.2196 17.7773C20.6017 17.7926 23.3522 20.1969 23.3522 23.1529Z"
            fill={props.fill ? props.fill : '#755AE2'}
        />
        <path
            d="M21.3659 13.1095C21.3635 14.1604 20.9631 15.1712 20.2451 15.9386C19.5272 16.7059 18.5452 17.1727 17.4968 17.2449H17.2191C17.018 17.2461 16.817 17.2321 16.618 17.203C15.9926 17.1095 15.3968 16.8745 14.8759 16.5159C14.3551 16.1572 13.923 15.6844 13.6126 15.1334C13.2599 14.5145 13.075 13.8143 13.0762 13.1019C13.0782 12.0038 13.5153 10.9512 14.2918 10.1747C15.0684 9.39814 16.121 8.961 17.2191 8.95899C17.8583 8.95767 18.489 9.10621 19.0604 9.39269C19.7529 9.73585 20.3357 10.2657 20.743 10.9224C21.1504 11.5792 21.3661 12.3367 21.3659 13.1095Z"
            fill={props.fill ? props.fill : '#755AE2'}
        />
    </svg>
);
export default PersonalRecruiter;
