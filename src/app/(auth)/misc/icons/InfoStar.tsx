import * as React from "react";
import { SVGProps } from "react";
const InfoStar = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width={21}
        height={20}
        viewBox="0 0 21 20"
        fill="none"
        {...props}
    >
        <path
            d="M9.9042 2.0416C10.4875 1.54993 11.4292 1.54993 11.9959 2.0416L13.3125 3.1666C13.5625 3.37493 14.0375 3.54993 14.3709 3.54993H15.7875C16.6709 3.54993 17.3959 4.27494 17.3959 5.15827V6.57493C17.3959 6.90827 17.5709 7.37493 17.7792 7.62493L18.9042 8.9416C19.3959 9.52494 19.3959 10.4666 18.9042 11.0333L17.7792 12.3499C17.5709 12.5999 17.3959 13.0666 17.3959 13.3999V14.8166C17.3959 15.6999 16.6709 16.4249 15.7875 16.4249H14.3709C14.0375 16.4249 13.5709 16.5999 13.3209 16.8083L12.0042 17.9333C11.4209 18.4249 10.4792 18.4249 9.91253 17.9333L8.59587 16.8083C8.34587 16.5999 7.87087 16.4249 7.54587 16.4249H6.08753C5.2042 16.4249 4.4792 15.6999 4.4792 14.8166V13.3916C4.4792 13.0666 4.31253 12.5916 4.1042 12.3499L2.9792 11.0249C2.49587 10.4499 2.49587 9.5166 2.9792 8.9416L4.1042 7.6166C4.31253 7.3666 4.4792 6.89993 4.4792 6.57493V5.1666C4.4792 4.28327 5.2042 3.55827 6.08753 3.55827H7.5292C7.86253 3.55827 8.3292 3.38327 8.5792 3.17493L9.9042 2.0416Z"
            fill="white"
        />
        <path
            d="M10.9459 14.0583C10.4875 14.0583 10.1125 13.6833 10.1125 13.2249C10.1125 12.7666 10.4792 12.3916 10.9459 12.3916C11.4042 12.3916 11.7792 12.7666 11.7792 13.2249C11.7792 13.6833 11.4125 14.0583 10.9459 14.0583Z"
            fill="#755AE2"
        />
        <path
            d="M10.9458 11.4337C10.6041 11.4337 10.3208 11.1504 10.3208 10.8087V6.77539C10.3208 6.43372 10.6041 6.15039 10.9458 6.15039C11.2875 6.15039 11.5708 6.43372 11.5708 6.77539V10.8004C11.5708 11.1504 11.2958 11.4337 10.9458 11.4337Z"
            fill="#755AE2"
        />
    </svg>
);
export default InfoStar;