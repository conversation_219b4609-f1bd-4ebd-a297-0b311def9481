import * as React from "react";
import { SVGProps } from "react";
const User = (props: SVGProps<SVGSVGElement>) => (
    <svg
        width={20}
        height={20}
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            d="M10.1333 9.05866C10.05 9.05033 9.94999 9.05033 9.85832 9.05866C7.87499 8.99199 6.29999 7.36699 6.29999 5.36699C6.29999 3.32533 7.94999 1.66699 9.99999 1.66699C12.0417 1.66699 13.7 3.32533 13.7 5.36699C13.6917 7.36699 12.1167 8.99199 10.1333 9.05866Z"
            stroke={props.stroke || "#292D32"}
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M5.96666 12.133C3.95 13.483 3.95 15.683 5.96666 17.0247C8.25833 18.558 12.0167 18.558 14.3083 17.0247C16.325 15.6747 16.325 13.4747 14.3083 12.133C12.025 10.608 8.26666 10.608 5.96666 12.133Z"
            stroke={props.stroke || "#292D32"}
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);
export default User;
