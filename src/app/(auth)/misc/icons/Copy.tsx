import * as React from "react";
import { SVGProps } from "react";
const Copy = (props: SVGProps<SVGSVGElement>) => (
    <svg
        width={44}
        height={44}
        viewBox="0 0 44 44"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            d="M31.1667 24.5673V30.0673C31.1667 37.4007 28.2334 40.334 20.9001 40.334H13.9334C6.60008 40.334 3.66675 37.4007 3.66675 30.0673V23.1007C3.66675 15.7673 6.60008 12.834 13.9334 12.834H19.4334"
            stroke="white"
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M31.1667 24.5673H25.3C20.9 24.5673 19.4333 23.1006 19.4333 18.7007V12.834L31.1667 24.5673Z"
            stroke="white"
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M21.2666 3.66602H28.5999"
            stroke="white"
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M12.8333 9.16602C12.8333 6.12268 15.2899 3.66602 18.3333 3.66602H23.1366"
            stroke="white"
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M40.3333 14.666V26.0143C40.3333 28.856 38.0233 31.166 35.1816 31.166"
            stroke="white"
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M40.3333 14.666H34.8333C30.7083 14.666 29.3333 13.291 29.3333 9.16602V3.66602L40.3333 14.666Z"
            stroke="white"
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);
export default Copy;
