import { AuthSliderContent } from "../types/SigupTypes";
import { Copy, UsersRound, Plane, Sheet } from 'lucide-react';

export const RecruiterLoginSlideshowContent: AuthSliderContent[] = [
    {
        heading: 'Parse Bulk CVs ',
        text: 'Sift through bulk CVs with ease and effortlessly identify and shortlist candidates for your job openings.',
        imageURL: '/images/auth/1.png',
        icon: Copy
    },
    {
        heading: 'Create assessments',
        text: 'Access well curated questions and send out assessment invite to candidates to get the best talent for every position',
        imageURL: '/images/auth/1.png',
        icon: Sheet

    },
    {
        heading: 'Conduct recruitment',
        text: 'Create job posting and move applicants within your job pipeline, conduct interviews, assessments and hire with ease.',
        imageURL: '/images/auth/1.png',
        icon: UsersRound

    },
    {
        heading: 'Request Talents',
        text: 'Discover and request top talents to enhance your team, ensuring a seamless hiring process.',
        imageURL: '/images/auth/1.png',
        icon: Plane

    },
];


export const TalentslideshowContent: AuthSliderContent[] = [
    {
        heading: 'Showcase your skills',
        text: 'Create a compelling profile and showcase your skills, making it easy for recruiters to find and connect with you.',
        icon: Sheet,
        imageURL: '/images/auth/1.png',

    },
    {
        heading: 'Explore opportunities',
        text: 'Discover exciting job opportunities tailored to your skills and preferences, and take the next step in your career.',
        icon: Plane,
        imageURL: '/images/auth/1.png',

    },
    {
        heading: 'Engage in assessments',
        text: 'Participate in skill assessments to highlight your strengths and stand out as a top candidate for potential employers.',
        icon: UsersRound,
        imageURL: '/images/auth/1.png',

    },
    {
        heading: 'Manage applications',
        text: 'Efficiently track and manage your job applications, ensuring you stay organized throughout the hiring process.',
        icon: Copy,
        imageURL: '/images/auth/1.png',

    },

];