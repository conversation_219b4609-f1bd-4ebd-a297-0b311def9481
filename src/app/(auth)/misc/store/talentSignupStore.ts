import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'

interface UserData {
  email: string
  first_name: string
  password: string
  [key: string]: any
}

interface TalentRegisterState {
  step: number
  userData: UserData
}

interface TalentRegisterActions {
  setStep: (payload: number) => void
  setUserData: (payload: Partial<UserData>) => void
  moveToNextStep: () => void
  resetStore: () => void
  clearStorage: () => void
}

type TalentRegisterStore = TalentRegisterState & TalentRegisterActions

const initialState: TalentRegisterState = {
  step: 1,
  userData: { email: '', first_name: '', password: '' },
}

const useTalentRegisterDetails = create<TalentRegisterStore>()(
  persist(
    (set, get) => ({
      ...initialState,
      setStep: (data) => set({ step: data }),
      moveToNextStep: () => set((state) => ({ step: state.step + 1 })),
      setUserData: (data) => set((state) => ({ userData: { ...state.userData, ...data } })),
      resetStore: () => set(initialState),
      clearStorage: () => {
        localStorage.removeItem('talent-signup-storage')
      },
    }),
    {
      name: 'talent-signup-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
)

export default useTalentRegisterDetails