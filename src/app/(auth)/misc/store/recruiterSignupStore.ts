import { userInterface } from '@/types/userInfoTypes';
import { create } from 'zustand';
import { createJSONStorage, persist, StateStorage } from 'zustand/middleware';

interface UserPersonalData {
  email: string;
  first_name: string;
  last_name: string;
  country_code?: string;
  password: string;
  recruiter_type: string;
  phone_number: string;
  user?: userInterface | null;
}

interface UserCompanyData {
  name: string;
  company_email: string;
  website: string;
  industry: string;
  size: string;
  description: string;
  role?: string;
  company_domain: string;
  address: string
  logo: File | undefined
}

interface RecruiterRegisterState {
  step: number;
  userPersonalData: UserPersonalData;
  userCompanyData: UserCompanyData;
}

interface RecruiterRegisterActions {
  setStep: (payload: number) => void;
  setRecruiterType: (payload: string) => void;
  setUserPersonalData: (payload: Partial<UserPersonalData>) => void;
  setUserCompanyData: (payload: Partial<UserCompanyData>) => void;
  moveToNextStep: () => void;
  resetStore: () => void;
  clearStorage: () => void;

}

type RecruiterRegisterStore = RecruiterRegisterState & RecruiterRegisterActions;

const initialState: RecruiterRegisterState = {
  step: 1,
  userPersonalData: { email: '', first_name: '', last_name: '', password: '', country_code: '+234', phone_number: '', recruiter_type: "HR_PERSONNEL" },
  userCompanyData: { name: '', company_email: '', website: '', industry: '', size: '', description: '', role: '', company_domain: '', address: '', logo: undefined },
};

const useRecruiterRegisterDetails = create<RecruiterRegisterStore>()(
  persist(
    (set, get) => ({
      ...initialState,
      setStep: (data) => set({ step: data }),
      moveToNextStep: () => set((state) => ({ step: state.step + 1 })),
      setRecruiterType: (data) => set((state) => ({
        userPersonalData: { ...state.userPersonalData, recruiter_type: data }
      })),
      setUserPersonalData: (data) => set((state) => ({
        userPersonalData: { ...state.userPersonalData, ...data }
      })),
      setUserCompanyData: (data) => set((state) => ({
        userCompanyData: { ...state.userCompanyData, ...data }
      })),
      resetStore: () => set(initialState),
      clearStorage: () => {
        const storage = createJSONStorage(() => localStorage);
        if (storage) {
          storage.removeItem('recruiter-signup-storage');
        }
      },
    }),
    {
      name: 'recruiter-signup-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);

export default useRecruiterRegisterDetails;