import * as yup from 'yup';
// Define a function to check if the file type is valid
export const isValidFileType = (file: File | any) => {
    if (!file) {
        return true; 
    }

    const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    return allowedTypes.includes(file.type);
};


export const isFileSizeWithinLimit = (file: File | any, maxSizeInBytes: number) => {
    if (!file) {
        return true; // No file, no validation needed
    }

    return file.size <= maxSizeInBytes;
};

interface CustomFile {
    custom_file: File;
}

interface CustomText {
    custom_text: string;
}
type CustomJobRequirement = {
    custom_file: File;
    custom_text: string;
}
export interface FormDataProps {
    email: string;
    name: string;
    years_of_experience: string;
    password: string;
    confirm_password: string;
    country: string;
    phone_number: string;
    state: string;
    amount: string;
    currency: string;
    cv: string;
    cv_and_resume_id: string;
    cover_letter_id: string;
    cover_letter_file: File | null;
    cover_letter_text: string;
    acceptCV: boolean;
    hasCoverLetter: boolean;
    custom_job_requirements: Array<CustomJobRequirement>;
    gender: 'male' | 'female' | 'other';
    portfolio_link: string;
    linkedin_link: string;
};

export interface FileObject {
    file: File;
}

export const isFile = (value: any): value is FileObject => {
    return value && value.file instanceof File;
}


// export const fileSchema = yup.object().shape({
//     cover_letter_file: yup.mixed().when(['cover_letter_text', 'hasCoverLetter'], {
//         is: (coverLetterText: string, hasCoverLetter: boolean) => hasCoverLetter && coverLetterText === "" || coverLetterText === "<p><br/></p>",
//         then: (schema) => schema.required("Needed")
//             .test('filePresence', 'File is required', (value) => !!value)
//             .test('fileType', 'Invalid file type', isValidFileType)
//             .test('fileSize', 'File size exceeds 10MB limit', (value) =>
//                 isFileSizeWithinLimit(value, 10 * 1024 * 1024)
//             ),
//         otherwise: (schema) => schema.nullable(), // technically this otherwise isnt needed
//     })
// })
// export const textSchema = yup.object({
//     cover_letter_text: yup.string().when(['hasCoverLetter', 'cover_letter_file'], {
//         is: (hasCoverLetter: boolean, cover_letter_file: any) => hasCoverLetter && cover_letter_file?.length === 0,
//         then: (schema) => schema.required("Needed"),
//         otherwise: (schema) => schema.nullable(), // technically this otherwise isnt needed
//     })
// })

let customJobRequirementSchema = yup.array().of(
    yup.object().shape({
        custom_file: yup
            .mixed()
            .test('custom_file', 'Custom file is required', function (value) {
                const customText = this.parent.custom_text;

                if (customText !== "" && value === "") {
                    return this.createError({ message: 'Custom file is required', path: 'custom_file' });
                }

                return true;
            })
            // .test('fileType', 'Invalid file type', isValidFileType)
            .test('fileSize', 'File size exceeds 10MB limit', (value) => isFileSizeWithinLimit(value, 10 * 1024 * 1024)),
        custom_text: yup
            .string()
            .test('custom_text', 'Custom text is required', function (value) {
                const customFile = this.parent.custom_file;

                if (customFile !== null && (value === null)) {
                    return this.createError({ message: 'Custom text is required', path: 'custom_text' });
                }

                return true;
            })
            .min(2),
    })
);
// Example usage:


export const nonTalentSchema = yup.object().shape({
    email: yup.string().email('Invalid email format').required().trim(),
    name: yup.string().required('Name is required'),
    years_of_experience: yup.string().required(),
    hasCoverLetter: yup.boolean(),
    phone_number: yup.string().required(),
    password: yup.string().required(),
    confirm_password: yup.string().oneOf([yup.ref('password')], 'Passwords must match'),
    country: yup.string().required(),
    state: yup.string(),
    amount: yup.string().required(),
    portfolio_link: yup.string(),
    linkedin_link: yup.string(),
    gender: yup.string().required('Gender is required').oneOf(['male', 'female', 'other'], 'Invalid gender'),

    currency: yup.string().required(),
    cv: yup.mixed().when('acceptCV', {
        is: true,
        then: (schema) => schema.required("Needed")
            .test('filePresence', 'File is required', (value) => !!value)
            .test('fileType', 'Invalid file type', isValidFileType)
            .test('fileSize', 'File size exceeds 10MB limit', (value) =>
                isFileSizeWithinLimit(value, 10 * 1024 * 1024)
            ),
        otherwise: (schema) => schema, // technically this otherwise isnt needed
    }),
    // cover_letter_file: fileSchema.fields.cover_letter_file,
    // cover_letter_text: textSchema.fields.cover_letter_text,
    acceptCV: yup.boolean(),

    custom_job_requirements: customJobRequirementSchema

    //@ts-ignore
}, ["cover_letter_text", "cover_letter_file"]);







export type FormDataPropsWithoutPasswords = Omit<FormDataProps, 'password' | 'confirm_password'>;

export const talentSchema = yup.object().shape({
    email: yup.string().email('Invalid email format').required().trim(),
    name: yup.string().required('Name is required'),
    years_of_experience: yup.string().required(),
    hasCoverLetter: yup.boolean(),
    phone_number: yup.string().required(),
    country: yup.string().required(),
    state: yup.string(),
    amount: yup.string().required(),
    portfolio_link: yup.string(),
    linkedin_link: yup.string(),
    gender: yup.string().required('Gender is required').oneOf(['male', 'female', 'other'], 'Invalid gender'),

    currency: yup.string().required(),
    // cv: yup.mixed().when('acceptCV', {
    //     is: true,
    //     then: (schema) => schema.test('filePresence', 'File is required', (value) => !!value)
    //         .test('fileType', 'Invalid file type', isValidFileType)
    //         .test('fileSize', 'File size exceeds 10MB limit', (value) =>
    //             isFileSizeWithinLimit(value, 10 * 1024 * 1024)
    //         ),
    //     // otherwise: (schema) => schema, // technically this otherwise isnt needed
    // }),
    acceptCV: yup.boolean(),

    custom_job_requirements: customJobRequirementSchema

  
})



