import { ZodError, z } from "zod";
const MAX_FILE_SIZE = 1000000;

export const existingCompanyForm = z.object({
    name: z.string({ required_error: 'Enter company name.' }).min(1, { message: 'Name is required' }),
    company_email: z.string({ required_error: 'Enter company email.' }).email({ message: 'Enter a valid email' }),
    role: z.string({ required_error: 'Enter your role in your organization.' }).min(1, { message: 'Role is required' }),
});
export const newCompanyForm = z.object({
    name: z.string({ required_error: 'Enter company name.' }).min(1, { message: 'Name is required' }),
    company_email: z.string({ required_error: 'Enter company email.' }).email({ message: 'Enter a valid email' }),
    website: z.string({ required_error: 'Enter company website.' }).min(1, { message: 'Enter company website.' }),
    industry: z.string(),
    size: z.string(),
    description: z.string({ required_error: 'Enter company overview.' }).min(1, { message: 'Enter company overview' }),
    logo: z.any().nullable().refine(
        file => {
            if (!file) {
                throw ZodError.create([{
                    path: ['logo'],
                    message: 'Please select a file.',
                    code: 'custom',
                }]);
            }
            if (!file.type.startsWith('image/')) {
                throw ZodError.create([{
                    path: ['logo'],
                    message: 'Please select an image file.',
                    code: 'custom',
                }]);
            }
            return file.size <= MAX_FILE_SIZE;
        },

        {
            message: 'Max image size is 10MB.',
        }
    ),
    company_domain: z.string({ required_error: 'Enter company domain.' }).min(1, { message: 'Enter company email domain.' }),
    address: z.string({ required_error: 'Enter company address.' }).min(1, { message: 'Enter company address.' }),
    team_member_emails: z.array(z.string().email()).optional(),
});