import { LucideIcon } from "lucide-react";

export type SignupFormData = {
  first_name: string;
  last_name: string;
  email: string;
  gender: string;
  password: string;
  confirm_password: string;
  country_code?: string;
  phone_number: string;
};


export type ForgotPasswordFormData = {
  otp: string;
  email: string;
  new_password: string;
  confirm_password: string;
};

export type VerifyTokemFormData = {
  otp: string;
  recipient: string;
};
export type RecruiterSigninFormData = {
  email: string;
  password: string;
  recruiter_type?: string;
};
export type TalentSigninFormData = {
  email: string;
  password: string;
  first_name: string;
};
export type RecruiterSignupFormData = {
  first_name: string;
  last_name: string;
  email: string;
  gender: string;
  password: string;
  confirm_password: string;
  country_code?: string;
  phone_number: string;
  recruiter_type: string;
};
export type createorRegisterUnderCompanyData = {
  name?: string;
  company_email: string;
  website?: string;
  industry?: string;
  size?: string;
  description?: string;
  logo?: File;
  company_domain: string;
  type_of_company?: string;
  address?: string;
  role: string;
  team_member_emails?: string[];
};

export type createCompanyData = {
  name: string;
  address: string;
  website?: string;
  industry?: string;
  size?: string;
  description?: string;
  logo?: File;
  company_domain: string;
  type_of_company: string;
  team_member_emails: string[];
  invite_link: string;
};

export type createPersonalCompanyData = {
  name: string;
  address?: string;
  website?: string;
  industry?: string;
  description?: string;
  logo?: File;
  type_of_company: string;
};

export type createRecruiterData = {
  company: string;
  company_email: string;
  role?: string;
  type_of_recruiter: string;
};

export type createPersonalRecruiterData = {
  company: string;
  role?: string;
  type_of_recruiter: string;
};

export type InviteMembersFormData = {
  invited_members: {
    recipient_email: string;
    role_id: number;
    type_of_recruiter: string;
  }[];
};


export type AuthSliderContent = {
  heading: string;
  text: string;
  imageURL: string;
  icon: LucideIcon
}
