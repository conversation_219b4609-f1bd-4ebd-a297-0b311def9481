"use client"
import React, { useState } from 'react';
import { SignupFormData } from '../../types/SigupTypes';
import { NewTalentBanner, TalentDetailsForm, TalentEmailVerification, TalentSignupForm } from '.';
import { useTalentRegisterDetails } from '../../store';
import { ConfirmActionModal } from '@/components/shared';
import { Rotate } from '@/components/shared/icons';
import { useBooleanStateControl } from '@/hooks';
import { cn } from '@/utils';
import { Back } from 'iconsax-react';


const TalentSignUpFlow = () => {
  const { step, userData, moveToNextStep, setUserData, resetStore, setStep } = useTalentRegisterDetails();
  const handleSignUp = (data: SignupFormData) => {
    setUserData(data);
    moveToNextStep();
  };

  const handleVerification = () => {
    moveToNextStep();
  };

  const handleDetailsSubmit = (details: any) => {
    setUserData({ ...userData, ...details });
    moveToNextStep();
  };

  const {
    state: isConfirmRestartModalOpen,
    setTrue: openConfirmRestartModal,
    setFalse: closeConfirmRestartModal,
  } = useBooleanStateControl()

  const confirmRestartRegistartion = () => {
    resetStore()
    closeConfirmRestartModal()
  }



  return (
    <>
{/* 
      <div className='relative z-[3] gap-6 overflow-y-scroll my-auto  max-lg:h-full w-full no-scrollbar '>
        <div className='flex flex-col items-center justify-center relative overflow-y-scroll rounded-2xl mt-[10vh] mb-[5vh] lg:max-xl:my-[6vh] xl:my-[6.5vh] w-full '>
          <article className='relative flex flex-col items-center justify-center px-6 md:max-lg:px-10 py-6 lg:px-8 rounded-2xl max-h-full overflow-y-scroll overflow-x-hidden backdrop-blur-lg w-[95%] md:max-lg:!w-[75%] lg:max-xl:!w-[85%]   2xl:w-[600px] max-w-[610px] lg:min-h-[85vh]'>

            {step === 1 && <TalentSignupForm onNext={handleSignUp} />}
            {step === 2 && <TalentEmailVerification user={userData} onVerified={handleVerification} />}
            {step === 3 && <TalentDetailsForm user={userData} onDetailsSubmit={handleDetailsSubmit} />}

            <div className='absolute inset-0 z-[-1] rounded-[1.1875rem] from-[#EDF4FF] from-[-31.2%] to-white/20 to-[24.74%] opacity-40 bg-gradient-358'></div>
            <button className={cn("sticky top-0 border-white border-[1.7px] text-white flex items-center justify-center gap-2 w-full disabled:bg-primary-light py-2 px-3 rounded-lg text-center disabled:opacity-50", step == 1 && "hidden")} onClick={openConfirmRestartModal}>
              <span className=' text-sm md:text-[0.9rem] '>Start new registration</span>
              <Back fill="white" width={20} height={20} />
            </button>
          </article>

          
        </div>
      </div> */}

      <div className="flex flex-col">

        {step === 1 && <TalentSignupForm onNext={handleSignUp} />}
        {step === 2 && <TalentEmailVerification user={userData} onVerified={handleVerification} />}
        {step === 3 && <TalentDetailsForm user={userData} onDetailsSubmit={handleDetailsSubmit} />}


        {/* <div className='absolute inset-0 z-[-1] rounded-[1.1875rem] from-[#EDF4FF] from-[-31.2%] to-white/20 to-[24.74%] opacity-40 bg-gradient-358'></div> */}
        <button className={cn("sticky top-0 border-white border-[1.7px] text-white flex items-center justify-center gap-2 w-full disabled:bg-primary-light py-2 px-3 rounded-lg text-center disabled:opacity-50", step == 1 && "hidden")} onClick={openConfirmRestartModal}>
          <span className=' text-sm md:text-[0.9rem] '>Start new registration</span>
          <Back fill="white" width={20} height={20} />
        </button>
        <ConfirmActionModal
            isModalOpen={isConfirmRestartModalOpen}
            closeModal={closeConfirmRestartModal}
            title="Restart Registration"
            confirmFunction={confirmRestartRegistartion}
            icon={<Rotate width={44} height={44} fill='white' />}
            hideCancelButton
          >
            <p className='text-[#8C8CA1] text-sm font-normal'>
              You are about to <span className='!text-header-text font-bold mr-1'>restart your registration</span>.
              Please be aware that you will no longer be able to use this email "<span className='text-header-text font-medium mr-1'>{userData?.email}</span>" for another registration if you confirm this action.
            </p>
          </ConfirmActionModal>
      </div>



    </>
  );
};

export default TalentSignUpFlow;