"use client";
import StepCard from "./StepCard";
import React, { useEffect } from "react";
import { <PERSON><PERSON>, LinkButton } from "@/components/shared";
import { useUser } from "@/lib/contexts/UserContext";
import { DoubleForward } from "@/components/shared/icons";
import { useTalentRegisterDetails } from "../../store";

interface BannerProps {
    user: { first_name: string | 'User' | undefined, };
}


const TalentSignupStep4UploadCV: React.FC<BannerProps> = ({ user }) => {
    const { user: loggedUser, isLoading } = useUser()
    const { userData, resetStore, clearStorage } = useTalentRegisterDetails();


    useEffect(() => {
        const handleLocationChange = () => {
            clearStorage();
            resetStore()
        };
        window.addEventListener("popstate", handleLocationChange);
        return () => {
            window.removeEventListener("popstate", handleLocationChange);
        };
    }, []);

    return (
        <div className="fixed top-0 right-0 bottom-0 left-0 z-[100000000000000] bg-white flex items-center justify-center lg:max-h-screen ">
            <section className=" flex h-full overflow-y-scroll flex-col gap-8 w-full bg-primary-light  p-4 sm:p-6 rounded-xl lg:p-10 lg:pb-5 lg:pt-[auto]">
                <div>

                    <div className="mx-auto max-w-[50rem] pt-8 lg:mx-0 lg:pt-32 ">
                        <h1 className="pb-2 font-clash text-[clamp(2.1rem,3.25vw,2.8rem)] text-left font-semibold text-header leading-[1.25] max-w-[18ch]">
                            Hello <span>{user.first_name || (!isLoading && loggedUser?.first_name) || (!user?.first_name && !isLoading && !loggedUser && "User")}</span>👋, <br className="max-lg:hidden" />
                            let&apos;s get you ready for your dream job.
                        </h1>
                        <p className="mt-1 mb-3 text-md text-gray-600 lg:mt-5  lg:text-lg text-left">
                            Our platform is designed to showcase, promote and connect
                            talented individuals like you with their dream jobs. To get started on
                            your journey, follow and complete these simple steps:
                        </p>
                    </div>

                </div>

                <section className="grid grid-cols-[repeat(auto-fit,minmax(300px,1fr))] items-stretch justify-center lg:justify-start  gap-5 xl:flex-row max-2xl:flex-wrap w-full ">

                    <article className="  lg:w-3xl lg:max-w-[375px] flex  flex-col gap-5 text-white max-md:w-full max-lg:max-w-[650px]">
                        <aside className="w-max rounded-full bg-primary-light-hover px-3 py-1 text-sm text-primary-dark">
                            1 of 4
                        </aside>
                        <section className="grow rounded-2xl bg-primary px-5 py-5">
                            <h3 className="font-clash text-base font-semibold">Upload CV</h3>
                            <p className="mt-3 text-[0.85rem]">
                                To showcase your incredible skills, experience, and achievements
                                to potential employers, we need a bit more from you through your
                                CV.
                            </p>
                            <section className="flex items-center justify-between mt-5 ">
                                <LinkButton size='small' variant='white' href="/t/showcase/uploadcv">Upload CV</LinkButton>
                                <a className="text-base px-7 py-2 rounded-xl transition-colors hover:bg-primary-darker hover:text-white" href="/resume-parser">Do have CV?</a>
                            </section>
                        </section>
                    </article>

                    <StepCard
                        number={2}
                        title="Skill assessment test"
                        desc="By taking the test, you empower us to match your abilities with the most suitable roles and projects within our network, setting you on a path of success and fulfilment."
                    />
                    <StepCard
                        number={3}
                        title="Personality test"
                        desc="The insights from this assessment enable us to match you with roles and teams that align perfectly with your personality,
                fostering a sense of belonging and fulfilment in your career."
                    />
                    <StepCard
                        number={4}
                        title="Technical test"
                        desc="This assessment helps us pinpoint your technical strengths and
                skills, ensuring we match you with roles and projects that align with your expertise, promoting career fulfilment and growth."
                    />
                </section>


                <LinkButton href="/t/dashboard" className="text-[0.9rem] mt-8 md:!py-2.5 bgg-[#opoc7c2e0] max-w-max" size="capsule" variant='extralight'>
                    Skip all
                    <DoubleForward fill="#c7c2e0" />
                </LinkButton>
            </section>
        </div>

    );
};

export default TalentSignupStep4UploadCV