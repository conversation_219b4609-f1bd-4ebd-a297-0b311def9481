'use client';

import { Input, LoaderBtn } from '@/components/shared';
import useUserStore from '@/store/userInfoStore';
import { cn, setAccessToken } from '@/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { z } from 'zod';
import { useRequestPasswordResetOTP, useSignIn } from '../../api';
import { Lock, Mail, RightUpArrow } from '../../icons';
import { useTalentRegisterDetails } from '../../store';

export type FormData = {
  email: string;
  password: string;
  first_name: string;
};
interface SignUpFormProps { }

const registerForm = z.object({
  email: z.string({ required_error: 'Enter email.' }).email(''),
  password: z
    .string({ required_error: 'Enter password.' })
    .min(8, 'Password must be at least 8 characters'),
});

const TalentSigninForm = () => {
  const router = useRouter();
  const params = useSearchParams();
  const { step, userData, setUserData, resetStore, setStep } =
    useTalentRegisterDetails();

  const {
    handleSubmit,
    register,
    formState: { errors, isDirty, isValid },
    setError,
  } = useForm({ resolver: zodResolver(registerForm) });
  const [isChecked, setIsChecked] = useState(false);
  const [slideout, setslideout] = useState(false);
  const [show, setShow] = useState(false);

  const { mutateAsync: signIn, isLoading: isSIgningIn } = useSignIn();
  const { mutate: requestOTP } = useRequestPasswordResetOTP();
  const { setUser: setSavedUser, getUser: getSavedUser } = useUserStore();

  const redirectError = params.get('session');
  useEffect(() => {
    if (redirectError === 'session') {
      toast.error('Session Expired! Log in to continue', { id: '234' });
    } else if (redirectError === 'logout') {
      toast.error('Log in again to continue', { id: '236' });
    }
  }, []);

  const onSubmit = async (data: any) => {
    signIn(data, {
      onSuccess: async response => {
        if (
          response?.data?.data?.message === 'USER PROFILE is not verified.'
        ) {
          setUserData({ ...userData, ...data });
          setStep(2);
          router.push('/register/complete-registration');
          return;
        }
        await setAccessToken(response?.data?.data?.access);
        setSavedUser(null);
        const redirect = params.get('redirect');

        if (redirect) {
          router.push(redirect);
        } else {
          router.push('/t/dashboard');
        }
      },

      onError: (error: any) => {
        console.log(error?.response.data, "ERRRRR")
        toast.error(error?.response?.data?.message, {
          id: 'error',
        });

        if (error?.response?.data.error == "Invalid credentials.") {
          setError('email', {
            message: "Invalid Credentials",
            type: 'custom'
          })
          setError('password', {
            message: "Invalid Credentials",
            type: 'custom'
          })
        }
        if (error?.response?.status === 412) {
          if (
            error?.response?.data?.errors.message ==
            'USER PROFILE is not verified.'
          ) {
            setUserData({ ...userData, ...data });
            setStep(2);
            router.push('/register/talent');
          }
        }
      },
    });

  };

  return (
    <div
      className={`my-auto flex h-full min-h-[58vh] w-full flex-col  justify-center md:h-full lg:min-h-[72vh] lg:px-8 3xl:min-h-[750px] ${slideout && 'translate-x-[-120%]'
        } transition-all duration-500`}
    >
      <header>
        <h1 className="flex items-center gap-4 font-clash text-3xl font-semibold text-white lg:text-[2.35rem]">
          <span>Welcome back 👋</span>
        </h1>
        <p className="mb-8 mt-1.5 text-base font-normal text-white">
          Enter details to login{' '}
        </p>
      </header>

      <form onSubmit={handleSubmit(onSubmit)} className=" flex flex-col">
        <div className="my-2">
          <Input
            type="email"
            variant="transparent"
            label="Email"
            hideLabel
            placeholder="Enter email"
            {...register('email')}
            id="email"
            autoCapitalize='off'
            className={cn('!bg-white/30 !text-white placeholder:opacity-100')}
            hasError={!!errors.email}
            errorMessage={errors.email?.message as string}
            leftIcon={<Mail stroke="white" />}
          />
        </div>

        <div className=" my-2">
          <Input
            type="password"
            variant="transparent"
            label="Enter Password"
            hideLabel
            placeholder="Enter password"
            {...register('password')}
            id="password"
            autoCapitalize='off'
            className={cn('!bg-white/30 !text-white placeholder:opacity-100')}
            hasError={!!errors.password}
            errorMessage={errors.password?.message as string}
            leftIcon={<Lock stroke="white" />}
          />
        </div>

        <div>
          <div className="mb-8 mt-2 flex flex-wrap justify-between gap-2 text-xs text-white/80 xs:flex-nowrap">
            <span className="flex items-center justify-center gap-1">
              <input
                type="checkbox"
                checked={isChecked}
                onChange={() => setIsChecked(!isChecked)}
                className="h-4 w-4 rounded-sm border-2"
              />
              Keep me logged in
            </span>

            <Link
              className=" hover:underline"
              href={'./forgot-password?user=talent'}
            >
              Forgot password?
            </Link>
          </div>

          <button
            className="mt-8 flex w-full items-center justify-center rounded-lg bg-white px-3 py-[0.4rem] text-center text-primary disabled:bg-grey/70 lg:mt-16 "
            type="submit"
            disabled={!isDirty || isSIgningIn}
          >
            <span className="mx-auto flex flex-1 items-center justify-center gap-2 text-sm font-medium text-secondary-dark md:text-[1.0125rem]">
              Proceed {isSIgningIn && <LoaderBtn />}
            </span>
            <span className="ml-auto flex h-9 w-9 items-center justify-center rounded-full bg-primary">
              <RightUpArrow height={14} width={14} className="translate-y-0" />
            </span>
          </button>
          <div className="mt-4 flex w-full flex-wrap items-center justify-center gap-1 rounded-lg border-[1px] border-white px-2 py-3.5 text-xs text-white/75 ">
            <span className="block ">Don&apos;t have an account?</span>{' '}
            <Link href={'/register/talent'} className="text-white">
              Sign up
            </Link>
          </div>
        </div>
      </form>
    </div>
  );
};

export default TalentSigninForm;
