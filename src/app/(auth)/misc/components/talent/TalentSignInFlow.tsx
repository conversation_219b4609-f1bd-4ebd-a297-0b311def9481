'use client';

import Link from 'next/link';
import React, { useState } from 'react';
import { LinkButton } from '@/components/shared';
import TalentSigninForm from './TalentSigninForm';
import TalentEmailVerification from './TalentSignupStep2EmailVerification';
import TalentDetailsForm from './TalentSignupStep3OnboardingDetails';
import { TalentSigninFormData } from '../../types/SigupTypes';
import NewTalentBanner from './TalentSignupStep4UploadCV';


const TalentSignInFlow = () => {



  return (
    <div className='relative z-[3] gap-6 overflow-y-scroll my-auto  max-lg:h-full w-full no-scrollbar '>
      <div className='flex items-center justify-center relative overflow-y-scroll rounded-2xl mt-[10vh] mb-[5vh] lg:max-xl:my-[6vh] xl:my-[6.5vh] w-full '>
        <article className='relative flex items-center justify-center px-6 md:max-lg:px-10 py-6 lg:px-8 rounded-2xl max-h-full overflow-y-scroll  w-[95%] md:max-lg:!w-[75%] lg:max-xl:!w-[85%]   2xl:w-[600px] max-w-[610px] lg:min-h-[85vh]'>
         <TalentSigninForm />
        
          <div className='absolute inset-0 z-[-1] rounded-[1.1875rem] from-[#EDF4FF] from-[-31.2%] to-white/20 to-[24.74%] opacity-40 bg-gradient-358 backdrop-blur-lg'></div>
        </article>
      </div>
    </div>

  );
};


export default TalentSignInFlow;