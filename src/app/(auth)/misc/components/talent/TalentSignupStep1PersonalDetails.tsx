"use client"

import { AxiosError } from 'axios';
import { motion } from 'framer-motion';
import Link from 'next/link';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z, ZodError } from 'zod';

import { Button, Checkbox2, ErrorModal, Input, LoaderBtn, Select } from '@/components/shared';
import { useErrorModalState } from '@/hooks';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { zodResolver } from '@hookform/resolvers/zod';

import { debounce } from 'lodash';
import { useSignUp } from '../../api';
import { Lock, Mail, RightUpArrow, User } from '../../icons';
import { useTalentRegisterDetails } from '../../store';
import { SignupFormData } from '../../types/SigupTypes';


interface TalentSignupFormProps {
  onNext: (data: SignupFormData) => void;
};
const genders = [
  { name: 'Male', value: 'M' },
  { name: 'Female', value: 'F' },
]





const registerForm = z.object({
  first_name: z.string({ required_error: 'Enter first name.' }).min(1, { message: 'First name is required' }),
  last_name: z.string({ required_error: 'Enter last name.' }).min(1, { message: 'Last name is required' }),
  email: z.string({ required_error: 'Enter email.' }).email({ message: 'Enter valid email' }),
  country_code: z.string({ required_error: 'Select a country.' }),
  phone_number: z.string({ required_error: 'Enter phone number.' }),
  gender: z.string({ required_error: 'Please select gender.' }),
  password: z.string({ required_error: 'Enter password.' }).min(8, "Password must be at least 8 characters").regex(/(?=.*\d)/, "Password must contain a number").regex(/(?=.*[A-Z])/, "Password must contain an uppercase letter")
    .regex(/(?=.*[a-z])/, "Password must contain a lowercase letter").regex(/(?=.*[@#$%^&+=])/, "Password must contain a special character (@#$%^&+=)"),
  confirm_password: z.string({ required_error: 'Confirm your password.' }),
}).refine((data) => {
  if (data.password !== data.confirm_password) {
    throw ZodError.create([{
      path: ['confirm_password'],
      message: 'Passwords do not match.',
      code: 'custom',
    }])
  }
  return true
});








const TalentSignupForm: React.FC<TalentSignupFormProps> = ({ onNext }) => {
  const { userData, moveToNextStep, setUserData } = useTalentRegisterDetails();
  const { handleSubmit, register, formState: { errors, isDirty, isValid }, setError, control, watch, setValue } = useForm<SignupFormData>({
    defaultValues: {
      first_name: userData.first_name,
      last_name: userData.last_name,
      email: userData.email,
      country_code: userData.country_code,
      phone_number: userData.phone_number,
      password: userData.password,
      confirm_password: userData.confirm_password
    },
    resolver: zodResolver(registerForm),
    mode: 'onChange'
  });


  const { mutateAsync: signUp, isLoading: isSigningUp } = useSignUp();
  const [hasAgreedToTerms, setAgreeToTerms] = useState(false);


  const countries = [
    { value: '+234', flag: '🇳🇬', name: 'NG' },
    { value: '+44', flag: '🇬🇧', name: 'UK' },
    { value: '+1', flag: '🇺🇸', name: 'US' },
  ];



  React.useEffect(() => {
    const debouncedFn = debounce(setUserData, 1000);
    const subscription = watch(debouncedFn);

    return () => subscription.unsubscribe();
  }, [watch]);



  ////////////////////////////////////////////////////////////////////////////////////////////
  ////////////////////////////////////////////////////////////////////////////////////////////
  ////////////                  SUBMIT FORM AND CREATE USER                          /////////
  ////////////////////////////////////////////////////////////////////////////////////////////
  ////////////////////////////////////////////////////////////////////////////////////////////
  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState()
  const onSubmit = async (data: SignupFormData) => {
    if (data.password !== data.confirm_password) {
      setError('confirm_password', { type: 'validate', message: 'Passwords do not match' });
      return;
    }


    const combinedPhone = `${data.country_code}${data.phone_number}`;

    const dataToSubmit = {
      first_name: data.first_name,
      last_name: data.last_name,
      email: data.email,
      phone_number: combinedPhone,
      gender: data.gender,
      password: data.password,
      confirm_password: data.confirm_password,
    };

    signUp(dataToSubmit, {
      onSuccess: async () => {
        onNext(data);
      },
      onError: (error: any) => {
        console.log(error)
        const errorMessage = formatAxiosErrorMessage(error as AxiosError);
        console.log(errorMessage, "ERROR MESSAGE")
        openErrorModalWithMessage(errorMessage || error?.response?.data?.error);
        if (error.response?.status === 400) {
          if (
            error.response?.data?.email &&
            error.response.data?.email[0] === "USER PROFILE with this email already exists."
          ) {
            setError("email", {
              type: "manual",
              message: "An account with this email already exists.",
            });
          } else if (error.response?.data?.data.message === "User already exists.") {
            setError("phone_number", {
              type: "manual",
              message: "An account with this phone number already exists.",
            });
          }

        }
      }
    })
  };








  return (


    <motion.div className={`w-full h-full pt-5 md:pt-[3.5vh] `}
      initial={{ opacity: 1, x: '110%', }}
      animate={{ opacity: 1, x: "0%", transition: { duration: 0.6, type: 'tween', ease: 'linear' } }}
      exit={{ opacity: 0.75, x: '-120%', transition: { duration: 0.3, type: 'tween', ease: 'linear' } }}
    >

      <header>
        <h1 className='flex items-center gap-4 text-3xl lg:text-[2.35rem] text-white font-clash font-semibold'>
          <span>
            Get Started
          </span>
          <span className='inline-flex items-center justify-center text-lg bg-white/20 px-4 py-0.5 rounded-lg '>
            1/2
          </span>
        </h1>
        <p className='text-white mt-1.5 mb-8 text-base font-normal'>Take the next step to the land of opportunities</p>
      </header>

      <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-5">
        <div className='flex flex-col md:flex-row md:gap-4 '>
          <Input
            hasError={!!errors.first_name}
            errorMessage={errors?.first_name?.message}
            {...register('first_name')}
            placeholder="Enter first name"
            label='First name'
            variant="transparent"
            leftIcon={<User stroke='white' />}
          />

          <Input
            hasError={!!errors.last_name}
            errorMessage={errors?.last_name?.message}
            {...register('last_name')}
            placeholder="Enter last name"
            label='Last name'
            variant="transparent"
            leftIcon={<User stroke='white' />}
          />
        </div>


        <Input
          hasError={!!errors.email}
          errorMessage={errors?.email?.message}
          {...register('email')}
          type="email"
          placeholder="Enter email"
          label='Email'
          variant="transparent"
          leftIcon={<Mail stroke='white' />}
        />


        <div>
          <label className='!text-white text-sm mt-2' htmlFor="combined_phone">Phone number</label>
          <div className="flex gap-4 items-start m-0" id='combined_phone'>
            <Select
              className=' w-full'
              variant="transparent"
              triggerColor='white'
              labelKey='name'
              valueKey='value'
              {...register('country_code')}
              onChange={(value) => setValue('country_code', value)}
              options={countries}
              placeholder="Country"
              hasError={!!errors.country_code}
              errorMessage={errors?.country_code?.message}
            />
            <Input
              containerClassName='flex-1'
              {...register('phone_number')}
              placeholder="Phone number"
              variant="transparent"
              hasError={!!errors.phone_number}
              errorMessage={errors?.phone_number?.message}

            />
          </div>
        </div>

        <Select
          className=' w-full'
          labelKey="name"
          valueKey="value"
          options={genders}
          placeholder="Select your gender"
          label='Gender'
          triggerColor='white'
          hasError={!!errors.gender}
          errorMessage={errors?.gender?.message}
          {...register('gender')}
          onChange={(value) => setValue('gender', value)}
          variant="transparent"
        />




        <Input type="password"
          label="Enter Password"
          placeholder="Enter password"
          variant="transparent"
          {...register('password')}
          hasError={!!errors.password}
          errorMessage={errors?.password?.message}
          leftIcon={<Lock stroke='white' />}
        />

        <Input type="password"
          label="Confirm Password"
          placeholder="Confirm password"
          variant="transparent"
          {...register('confirm_password')}
          leftIcon={<Lock stroke='white' />}
        />


        <div className='mt-12 w-full'>

          <label htmlFor="terms-conditions" className='flex items-center gap-2 py-5'>
            <Checkbox2
              id="terms-conditions"
              checked={hasAgreedToTerms}
              onCheckedChange={() => setAgreeToTerms(!hasAgreedToTerms)}
              className="border-white"
              checkClassName="!text-white"
            />
            <span className='text-white/60 text-sm'>By registering and signing in, you agree to our <Link className='font-semibold text-[0.78rem] text-white hover:underline ml-0.5' href={'/terms'}>Terms & Conditions</Link></span>
          </label>


          <button
            className="bg-white flex items-center justify-center w-full disabled:bg-primary-light py-1.5 px-3 rounded-lg text-primary text-center"
            disabled={!hasAgreedToTerms || isSigningUp}
            type="submit"
            title={!hasAgreedToTerms ? "Please agree to the terms and conditions before proceeding" : ""}
          >
            <span className='flex items-center justify-center gap-4 text-sm md:text-base text-secondary-dark font-medium mx-auto flex-1'>Proceed {isSigningUp && <LoaderBtn />}</span>
            <span className='flex items-center justify-center ml-auto bg-primary rounded-full w-8 h-8'><RightUpArrow height={14} width={14} className='translate-y-0.5' /></span>
          </button>
          <div className='flex flex-wrap items-center justify-center gap-1 mt-2 mb-8 px-6 py-3 border-[1px] border-white/60 rounded-lg w-full text-sm text-white/70 '>Already have an account? <Link className='text-white text-medium text-base' href={'/login/talent'}>Sign in</Link></div>
        </div>
      </form>


      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          typeof errorModalMessage === 'string'
            ? errorModalMessage
            : (errorModalMessage as {error: string})?.error || 'Something went wrong.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 hover:border-red-950 hover:text-red-950 px-1.5 sm:text-sm md:px-6"
            type="button"
            onClick={closeErrorModal}
          >
            Close
          </Button>
        </div>
      </ErrorModal>
    </motion.div>
  );
};

export default TalentSignupForm;
