

interface StepCardProps {
  number: number;
  title: string;
  desc: string;
}
const titleClassName = (number: number) => {
  if (number == 2) {
    return "text-primary";
  } else if (number == 3) {
    return "text-[#095892]";
  } else if (number == 4) {
    return "text-[#095892]";
  }
};
const backgroundClassName = (number: number) => {
  if (number == 2) {
    return "bg-primary-light-active";
  } else if (number == 3) {
    return "bg-[#DCF0FF]";
  } else if (number == 4) {
    return "bg-[#F4F9FF]";
  }
};
const textClassName = (number: number) => {
  if (number == 2) {
    return "text-[#675E8B]";
  } else if (number == 3) {
    return "text-[#0066B2]";
  } else if (number == 4) {
    return "text-[#0066B2]";
  }
};

const StepCard: React.FC<StepCardProps> = ({
  number,
  title,
  desc,
}) => {
  return (
    <article className="lg:w-3xl lg:max-w-[375px] flex w-full flex-col gap-5 max-md:max-w-full max-lg:max-w-[650px]">
      <aside className="w-max rounded-full px-3 py-1 text-sm text-primary-dark bg-primary-light-hover">
        {number} of 4
      </aside>
      <section className={`rounded-2xl bg-slate-50 flex grow`}>
        <div className={`flex flex-col m-[12px] px-5 py-5 rounded-2xl ${backgroundClassName(number)}`}>
          <h3 className={`font-clash text-base font-semibold ${titleClassName(number)}`}>
            {title}
          </h3>
          <p className={`mt-3 text-[0.85rem] ${textClassName(number)}`}>
            {desc}
          </p>
        </div>
      </section>
    </article>
  );
};

export default StepCard