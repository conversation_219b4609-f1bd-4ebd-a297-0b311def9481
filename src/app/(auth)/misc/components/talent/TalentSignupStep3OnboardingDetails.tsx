"use client"

import React, { useEffect, useState } from 'react';
import { z } from 'zod';
import { Country, State, City, ICountry, IState } from 'country-state-city';
import { FieldValues, SubmitHandler, useForm } from 'react-hook-form';
import { motion } from 'framer-motion'


import { cn, setAccessToken } from '@/utils';
import { Select, LoaderBtn, RadioGroup, SelectSingleCombo, ErrorModal, Button, LoadingOverlay, Input } from '@/components/shared';
import { zodResolver } from '@hookform/resolvers/zod';
import { job_type, work_experiences } from '@/app/(website-main)/e/jobs/misc/constants';
;
import { useSignIn } from '../../api/useSignIn';
import { useTalentOnboardMutation } from '../../api/useOnboardTalent';
import { RightUpArrow } from '../../icons';
import { useErrorModalState } from '@/hooks';
import { useTalentRegisterDetails } from '../../store';
import { useRouter } from 'next/navigation';
import { debounce } from 'lodash';

interface TalentDetailsFormProps {
    onDetailsSubmit: (details: any) => void;
    user: { first_name: string | undefined, email: string, password: string };

}

const currently_employed = [
    { name: 'Yes', value: 'true' },
    { name: 'No', value: 'false' },
]
const willing_to_relocate = [
    { name: 'Yes', value: 'true' },
    { name: 'No', value: 'false' },
]


type FormData = {
    address: string,
    desired_role: string,
    years_of_experience: string,
    stack: string,
    job_type: string,
    currently_employed: string,
    willing_to_relocate: string,
    country: string,
    state: string,
};

const TalentDetailsFormResolver = z.object({
    address: z.string({ required_error: 'Enter address.' }).min(1, { message: 'Home address is required' }),
    desired_role: z.string({ required_error: 'Enter last name.' }).min(1, { message: 'Last name is required' }),
    years_of_experience: z.string({ required_error: 'Enter years of experience.' }),
    stack: z.string({ required_error: 'Enter stack.' }),
    job_type: z.string({ required_error: 'Enter preffered job type.' }),
    currently_employed: z.string({ required_error: 'Choose current employment status.' }),
    willing_to_relocate: z.string({ required_error: 'Choose relocation preference.' }),
    country: z.string({ required_error: 'Please select country.' }),
    state: z.string({ required_error: 'Please select state.' }),
});






const TalentDetailsForm: React.FC<TalentDetailsFormProps> = ({ user, onDetailsSubmit }) => {
    const { userData, moveToNextStep, setUserData, clearStorage, resetStore, setStep } = useTalentRegisterDetails();
    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();




    const signIn = useSignIn();
    const { mutateAsync: onBoardTalent, isLoading: isOnboardingTalent } = useTalentOnboardMutation();
    const [slide, setslide] = useState<string>("stagnant")
    const router = useRouter();

    useEffect(() => {
        const loginData = {
            email: user.email || userData.email,
            password: user.password || userData.email,
        }

        signIn.mutateAsync(loginData, {
            onSuccess: async (response) => {
                await setAccessToken(response?.data?.data.access)
            }
        });
    }, []);






    //////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////
    //////////                          FORM                    //////////
    //////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////
    //////////////////////////////////////////////////////////////////////
    const { handleSubmit, register, formState: { errors, isDirty, isValid }, control, watch, setValue } = useForm<FormData>({
        defaultValues: {
            address: userData.address,
            desired_role: userData.desired_role,
            years_of_experience: userData.years_of_experience,
            stack: userData.stack,
            job_type: userData.job_type,
            currently_employed: userData.currently_employed,
            willing_to_relocate: userData.willing_to_relocate,
            country: userData.country,
            state: userData.state,

        },
        resolver: zodResolver(TalentDetailsFormResolver)
    });;


    //////////////////////////////////////////////////////////////////////
    ///////               SAVE INPUTS TO STORE                     ///////
    //////////////////////////////////////////////////////////////////////
    React.useEffect(() => {
        const debouncedFn = debounce(setUserData, 1000);
        const subscription = watch(debouncedFn);
        return () => subscription.unsubscribe();
    }, [watch]);


    //////////////////////////////////////////////////////////////////////
    ///////               COUNTRY AND STATE SELECTION              ///////
    //////////////////////////////////////////////////////////////////////
    const [countryList, setCountryList] = useState<ICountry[]>()
    const [stateList, setStateList] = useState<IState[]>()
    const [countryCode, setCountryCode] = useState("")

    useEffect(() => {
        setCountryList(Country?.getAllCountries())
        setCountryCode(Country?.getAllCountries().find(country => country.name == userData.country)?.isoCode || "")
    }, [])
    useEffect(() => {
        setStateList(State?.getStatesOfCountry(String(countryCode)))
    }, [countryCode])

    const countryOptions = countryList?.map(country => ({ value: country?.name, label: country.name, code: country?.isoCode }))
    const stateOptions = React.useMemo(() => {
        return stateList?.map(state => ({
            value: state?.name,
            name: state.name,
            code: state?.isoCode
        }));
    }, [stateList, countryCode]);


    //////////////////////////////////////////////////////////////////////
    ///////                  HANDLE SUBMIT FORM                    ///////
    //////////////////////////////////////////////////////////////////////
    const onSubmit: SubmitHandler<FieldValues> = async (data) => {
        try {

            const userData = {
                address: data.address,
                desired_role: data.desired_role,
                years_of_experience: data.years_of_experience,
                stack: data.stack.split(','),
                job_type: data.job_type,
                currently_employed: data.currently_employed,
                willing_to_relocate: data.willing_to_relocate,
            };

            onBoardTalent(userData, {
                onSuccess: (response) => {
                    window.postMessage('userTypeChange', window.location.href);
                    clearStorage()
                    router.push(`/t/onboarding?user_email=${user.email}`)
                    resetStore()
                }
            });
        } catch (error: any) {
            console.error('Something went wrong:', error.response);
        }
    };






    return (
        <motion.form onSubmit={handleSubmit(onSubmit)} className={cn("relative flex flex-col py-4 transition-transform",)}
            initial={{ opacity: 0.75, x: '110%', }}
            animate={{ opacity: 1, x: "0%", transition: { duration: 0.4, type: 'spring', ease: 'linear' } }}
            exit={{ opacity: 0.75, x: '-120%', transition: { duration: 0.6, type: 'spring', ease: 'linear' } }}
        >
            <header className='flex flex-col self-start '>
                <h1 className=' text-3xl md:text-[2.35rem] text-white font-clash font-medium'>
                    Let&apos;s help set up your <br className='max-xs:hidden lg:max-xl:hidden' /> profile
                    <span className='inline-block items-center justify-center text-lg bg-white/20 px-4 py-0.5 rounded-lg ml-2.5 translate-y-[-1.5px]'>
                        2/2
                    </span>
                </h1>
                <p className="text-[#BCBCBC] text-sm md:font-base mt-1 mb-6">
                    Kindly complete your profile setup as this would help employers get a quick knowledge of your when you profile is viewed
                </p>
            </header>


            <div className='flex flex-col gap-3.5'>
                <Input
                    label='Home address'
                    placeholder='Enter address'
                    {...register('address')}
                    variant="transparent"
                    hasError={!!errors.address}
                    errorMessage={errors.address?.message}
                />


                <div className="flex flex-col ">
                    <label className='text-white text-sm'>Current Location</label>
                    <div className="grid w-full flex-col sm:grid-cols-2 items-center sm:gap-4">
                        <SelectSingleCombo
                            name='country'
                            placeholder="Select Country"
                            value={watch("country")}
                            onChange={(val) => {
                                setValue("country", val!)
                                const chosen = countryOptions && countryOptions.filter((country) => country.value.toLowerCase() == val.toLocaleLowerCase())[0]
                                setValue("country", chosen?.value!)
                                countryOptions && setCountryCode(chosen?.code!)
                            }}
                            containerClass='!my-2 transparent'
                            itemClass='text-xs'
                            options={countryOptions! || [] as any}
                            valueKey='value'
                            labelKey='label'
                            triggerColor='white'
                            variant="transparent"
                        />

                        <SelectSingleCombo
                            name='state'
                            placeholder="Select state"
                            value={watch("state")}
                            options={stateOptions || [] as any}
                            valueKey='value'
                            labelKey={'name'}
                            onChange={(val) =>
                                setValue("state", val)
                            }
                            containerClass='!my-2 transparent'
                            itemClass='text-xs'
                            isLoadingOptions={!stateOptions || watch('country') == undefined}
                            triggerColor='white'
                            variant="transparent"
                        />
                    </div>
                </div>

                <div className=" !my-2">
                    <Input
                        type="text"
                        label="What best describes your desired role?"
                        placeholder="Enter desired role"
                        {...register('desired_role')}
                        id="desired_role"
                        variant="transparent"
                        hasError={!!errors.desired_role}
                        errorMessage={errors.desired_role?.message}
                    />
                </div>

                <Select
                    triggerColor='white'
                    {...register('years_of_experience')}
                    onChange={(value) => setValue('years_of_experience', value)}
                    options={work_experiences}
                    labelKey='name'
                    valueKey='value'
                    label='Work experience in the stated role'
                    variant="transparent"
                    placeholder="Select years of experience"
                    containerClass='!my-2 transparent'
                    hasError={!!errors.years_of_experience}
                    errorMessage={errors.years_of_experience?.message}
                />

                <div className="!my-2 text-white">
                    <Input type="text"
                        label="What is your stack? (comma-separated)"
                        placeholder="e.g. HTML, CSS, Javascript" {...register('stack')} id="stack"
                        variant="transparent"
                        hasError={!!errors.stack}
                        errorMessage={errors.stack?.message}
                    />
                </div>


                <Select
                    {...register('job_type')}
                    triggerColor='white'
                    onChange={(value) => setValue('job_type', value)}
                    labelKey='name'
                    valueKey='value'
                    options={job_type}
                    label='Employment type'
                    labelClass='!text-white'
                    itemClass='text-xs'
                    placeholder="Select job employment type"
                    hasError={!!errors.job_type}
                    errorMessage={errors.job_type?.message}
                    variant="transparent"
                />


                <RadioGroup
                    options={currently_employed}
                    onChange={(value) => setValue('currently_employed', value)}
                    label="Are you currently employed?"
                    labelClass='!text-white'
                    containerClass='!my-2'
                    errors={errors}
                    value={watch('currently_employed')}
                    name='job_type'
                    variant="offwhite"
                    size='small'
                    arrangement='row'
                    hasError={!!errors.currently_employed}
                    errorMessage={errors.currently_employed?.message}

                />
                <RadioGroup
                    options={willing_to_relocate}
                    onChange={(value) => setValue('willing_to_relocate', value)}
                    label="Are you willing to relocate?"
                    labelClass='!text-white'
                    containerClass='!my-2'
                    errors={errors}
                    value={watch('willing_to_relocate')}
                    name='job_type'
                    variant="offwhite"
                    size='small'
                    arrangement='row'
                    hasError={!!errors.willing_to_relocate}
                    errorMessage={errors.willing_to_relocate?.message}
                />
            </div>





            <button className="flex items-center justify-center w-full mt-12 bg-white disabled:bg-primary-light py-2 px-3 rounded-lg text-primary text-center" type="submit" disabled={!isDirty}>
                <span className='flex items-center justify-center flex-1 grow gap-x-2 text-sm md:text-base text-secondary-dark font-medium mx-auto'>Create Profile {isOnboardingTalent && <LoaderBtn />}</span>
                <span className='flex items-center shrink justify-center ml-auto bg-primary rounded-full w-9 h-9'><RightUpArrow height={14} width={14} className='translate-y-0.5' /></span>
            </button>








            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={
                    errorModalMessage || 'Please check your inputs and try again.'
                }
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button
                        className="grow bg-red-950 hover:border-red-950 hover:text-red-950 px-1.5 sm:text-sm md:px-6"
                        type="button"
                        onClick={closeErrorModal}
                    >
                        Okay
                    </Button>
                </div>
            </ErrorModal>

            <LoadingOverlay isOpen={isOnboardingTalent} />
        </motion.form>
    );
};

export default TalentDetailsForm;
