"use client"

import { AxiosError } from 'axios';
import { AnimatePresence, motion } from 'framer-motion';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { ZodError, z } from 'zod';

import { SuitcaseWhite } from '@/app/(website-main)/e/jobs/misc/icons';
import { Button, Checkbox2, ErrorModal, Input, LinkButton, Loader, LoaderBtn, Modal, Select } from '@/components/shared';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { cn } from '@/utils';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { zodResolver } from '@hookform/resolvers/zod';

import { debounce } from 'lodash';
import { useAcceptTeamInvite, useSignUp } from '../../api';
import { Lock, Mail, RightUpArrow, User } from '../../icons';
import { useRecruiterRegisterDetails } from '../../store';
import { RecruiterSignupFormData } from '../../types/SigupTypes';
import useDecodeInvitationToken from '../../api/decodeInvitationToken';
import { useRouter } from 'next/navigation';


interface RecruiterSignUpStep2Props {
    onNext: (data: RecruiterSignupFormData) => void;
    recruiter_type: string;
};
const genders = [
    { name: 'Male', value: 'M' },
    { name: 'Female', value: 'F' },
]
const countries = [
    { value: '+1', flag: '🇺🇸', name: 'US' },
    { value: '+44', flag: '🇬🇧', name: 'UK' },
    { value: '+234', flag: '🇳🇬', name: 'NG' },
    { value: '+27', flag: '🇿🇦', name: 'ZA' },
    { value: '+233', flag: '🇬🇭', name: 'GH' },
    { value: '+254', flag: '🇰🇪', name: 'KE' },
    { value: '+33', flag: '🇫🇷', name: 'FR' }
];







const RecruiterSignUpStep2: React.FC<RecruiterSignUpStep2Props> = ({ onNext, recruiter_type }) => {
    const params = useSearchParams()
    const router = useRouter();
    const invitation_link = params.get('invitation_token')
    const isInvited = !!invitation_link ? true : false
    const registerForm = z.object({
        first_name: z.string({ required_error: 'Enter first name.' }).min(1, { message: 'First name is required' }),
        last_name: z.string({ required_error: 'Enter last name.' }).min(1, { message: 'Last name is required' }),
        email: z.string().email().optional(),
        country_code: z.string({ required_error: 'Select a country.' }),
        phone_number: z.string({ required_error: 'Enter phone number.' }),
        gender: z.string({ required_error: 'Select your gender' }),
        password: z.string({ required_error: 'Enter password.' }).min(8, "Password must be at least 8 characters").regex(/(?=.*\d)/, "Password must contain a number").regex(/(?=.*[A-Z])/, "Password must contain an uppercase letter")
            .regex(/(?=.*[a-z])/, "Password must contain a lowercase letter").regex(/(?=.*[!@#$%^&*()_\+\-=\[\]{}|;:,.<>?])/, "Password must contain a special character (!@#$%^&*()_+-=[]{}|;:,.<>?)"),
        confirm_password: z.string({ required_error: 'Confirm your password.' }),
    }).refine((data) => {
        if (data.password !== data.confirm_password) {
            throw ZodError.create([{
                path: ['confirm_password'],
                message: 'Passwords do not match.',
                code: 'custom',
            }])
        }
        if (!isInvited && (data.email === "" || !data.email)) {
            throw ZodError.create([{
                path: ['email'],
                message: 'Email is required.',
                code: 'custom',
            }])

        }
        return true
    });
    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState()
    const {
        state: isSuccessModalOpen,
        setTrue: openSuccessModal,
    } = useBooleanStateControl()

    const { userPersonalData, setUserPersonalData, clearStorage } = useRecruiterRegisterDetails();
    const defaultValues = isInvited ? {} : {
        first_name: userPersonalData.first_name,
        last_name: userPersonalData.last_name,
        email: isInvited ? "<EMAIL>" : userPersonalData.email,
        country_code: userPersonalData.country_code,
        phone_number: userPersonalData.phone_number,
        password: userPersonalData.password,
        confirm_password: '',
    }


    const { handleSubmit, register, formState: { errors, isDirty, isValid }, setError, control, watch, setValue } = useForm<RecruiterSignupFormData>({
        defaultValues,
        resolver: zodResolver(registerForm),
        mode: 'onChange'

    });

    const [hasAgreedToTerms, setAgreeToTerms] = useState(false);
    const { mutateAsync: signUp, isLoading: isSigningUp } = useSignUp();


    React.useEffect(() => {
        const debouncedFn = debounce((value) => {
            setUserPersonalData(value);
            if (!isInvited) {
            }
        }, 1000);
        const subscription = watch(debouncedFn);
        return () => subscription.unsubscribe();
    }, [watch]);




    ////  submit form and register user
    const { mutate: acceptInvite, isLoading: isAcceptingInvite } = useAcceptTeamInvite()
    const { data: invitationData, isLoading: isInvitationDataLoading, refetch, } = useDecodeInvitationToken(invitation_link as string);

    useEffect(() => {
        const refetchInvitationData = async () => {
            if (invitation_link) {

                const result = await refetch();
                console.log(result, "INVITATION DATA")
                if (result.isError) {

                    openErrorModalWithMessage("Invalid invitation link")
                }

            }
        }
        refetchInvitationData()
    }, [invitation_link])


    const onSubmit = async (data: RecruiterSignupFormData) => {
        const combinedPhone = `${data.country_code}${data.phone_number}`;

        if (isInvited) {
            if (invitation_link) {
                const dataToSubmit = {
                    invitation_token: invitation_link,
                    first_name: data.first_name,
                    last_name: data.last_name,
                    phone_number: combinedPhone,
                    gender: data.gender,
                    password: data.password,
                    confirm_password: data.confirm_password,
                };
                console.log(dataToSubmit, "DATA TO SUBMIT")
                // return
                acceptInvite(dataToSubmit, {
                    onSuccess: async () => {
                        openSuccessModal()
                        clearStorage()

                    },
                    onError: (error: any) => {
                        // console.log(error, "ACCEPT INVITE ERROR")
                        const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                        openErrorModalWithMessage(error?.response?.data?.error || errorMessage);
                    }
                })
            } else {
                openErrorModalWithMessage("Check the link in your email and try again")

            }
            return

        }
        else {
            const dataToSubmit = {
                first_name: data.first_name,
                last_name: data.last_name,
                email: data.email,
                phone_number: combinedPhone,
                gender: data.gender,
                password: data.password,
                confirm_password: data.confirm_password,
            };
            signUp(dataToSubmit, {
                onSuccess: async () => {
                    onNext({ ...dataToSubmit, recruiter_type });
                },
                onError: (error: any) => {
                    console.log(error, "PERSONAL DETAIL ERROR")
                    if (error?.response?.data?.error) {
                        error.response.data.errors = error?.response?.data?.error
                    }

                    const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                    console.log(errorMessage, "ERROR MESSAGE")
                    openErrorModalWithMessage(errorMessage || error?.response?.data?.error);

                    if (error.response?.status === 400) {
                        if (
                            error.response?.data?.email &&
                            error.response.data?.email[0] === "USER PROFILE with this email already exists."
                        ) {
                            setError("email", {
                                type: "manual",
                                message: "An account with this email already exists.",
                            });
                        } else if (error.response?.data?.data?.message === "User already exists.") {
                            setError("phone_number", {
                                type: "manual",
                                message: "An account with this phone number already exists.",
                            });
                        }
                    }


                }
            })
        }
    };






    return (
        <AnimatePresence>
            {isInvited && isInvitationDataLoading ?
                <Loader color={"#fff"} /> :
                <motion.div className={cn("flex flex-col items-center justify-center w-full  min-h-[58vh] lg:min-h-[72vh] 3xl:min-h-[750px] h-full my-auto",)}
                    initial={{ x: '110%', }}
                    animate={{ x: "0%", }}
                    exit={{ x: '-120%', }}
                    transition={{ duration: 0.5, ease: 'linear' }}
                >

                    <header className='flex flex-col self-start '>

                        <h1 className='flex items-center gap-4 text-3xl lg:text-[2.35rem] text-white font-clash font-medium'>
                            <span>Personal details</span>
                            <span className={cn('inline-flex items-center justify-center text-lg bg-white/20 px-4 py-0.5 rounded-lg', isInvited && "hidden")}>
                                1/2
                            </span>
                        </h1>
                        {isInvited ? (
                            <p className='text-[#b5b5c8] mt-2 mb-8 text-sm lg:text-[1.0125rem]'>
                                You’ve been invited to join <strong>{invitationData?.company_name}</strong> as a <strong>{invitationData?.role.toLowerCase().replace('_', ' ')}</strong>. Complete your profile to get started.
                            </p>
                        ) :
                            <p className='text-[#b5b5c8] mt-1 mb-8 text-sm lg:text-[1.0125rem]'>
                                Take the next step to an enjoyable recruiting experience by filling in your personal details.
                            </p>
                        }
                    </header>
                    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-3.5">
                        <div className='flex flex-col md:flex-row md:gap-4 '>
                            <Input
                                hasError={!!errors.first_name}
                                errorMessage={errors?.first_name?.message}
                                {...register('first_name')}
                                placeholder="Enter first name"
                                label='First name'
                                variant="transparent"
                                leftIcon={<User stroke='white' />}
                            />

                            <Input
                                hasError={!!errors.last_name}
                                errorMessage={errors?.last_name?.message}
                                {...register('last_name')}
                                placeholder="Enter last name"
                                label='Last name'
                                variant="transparent"
                                leftIcon={<User stroke='white' />}
                            />
                        </div>

                        {!isInvited &&
                            <Input
                                hasError={!!errors.email}
                                errorMessage={errors?.email?.message}
                                {...register('email')}
                                type="email"
                                placeholder="Enter email"
                                label='Email'
                                variant="transparent"
                                leftIcon={<Mail stroke='white' />}
                            />

                        }
                        <div>
                            <label className='!text-white text-sm mt-2' htmlFor="combined_phone">Phone number</label>
                            <div className="flex gap-4 items-start m-0" id='combined_phone'>
                                <Select
                                    className=' w-full'
                                    variant="transparent"
                                    triggerColor='white'
                                    labelKey='name'
                                    valueKey='value'
                                    {...register('country_code')}
                                    onChange={(value) => setValue('country_code', value)}
                                    options={countries}
                                    placeholder="Country"
                                    hasError={!!errors.country_code}
                                    errorMessage={errors?.country_code?.message}

                                />
                                <Input
                                    containerClassName='flex-1'
                                    {...register('phone_number')}
                                    placeholder="Phone number"
                                    variant="transparent"
                                    hasError={!!errors.phone_number}
                                    errorMessage={errors?.phone_number?.message}

                                />
                            </div>
                        </div>

                        <Select
                            className=' w-full'
                            labelKey="name"
                            valueKey="value"
                            options={genders}
                            placeholder="Select your gender"
                            label='Gender'
                            triggerColor='white'
                            hasError={!!errors.gender}
                            errorMessage={errors?.gender?.message}
                            {...register('gender')}
                            onChange={(value) => setValue('gender', value)}
                            variant="transparent"
                        />




                        <Input type="password"
                            label="Enter Password"
                            placeholder="Enter password"
                            variant="transparent"
                            {...register('password')}
                            hasError={!!errors.password}
                            errorMessage={errors?.password?.message}
                            leftIcon={<Lock stroke='white' />}
                        />

                        <Input type="password"
                            label="Confirm Password"
                            placeholder="Confirm password"
                            hasError={!!errors.confirm_password}
                            errorMessage={errors?.confirm_password?.message}
                            variant="transparent"
                            {...register('confirm_password')}
                            leftIcon={<Lock stroke='white' />}
                        />


                        <div className='mt-12 w-full'>

                            <label htmlFor="terms-conditions" className='flex items-center gap-2 py-5'>
                                <Checkbox2
                                    id="terms-conditions"
                                    checked={hasAgreedToTerms}
                                    onCheckedChange={() => setAgreeToTerms(!hasAgreedToTerms)}
                                    className="border-white"
                                    checkClassName="!text-white"
                                />
                                <span className='text-white/60 text-sm'>By registering and signing in, you agree to our <Link className='font-semibold text-[0.78rem] text-white hover:underline ml-0.5' target='blank' href={'/terms'}>Terms & Conditions</Link></span>
                            </label>


                            <button
                                className="bg-white flex items-center justify-center w-full disabled:bg-primary-light py-1.5 px-3 rounded-lg text-primary text-center"
                                disabled={!hasAgreedToTerms || isSigningUp || isAcceptingInvite}
                                type="submit"
                                title={!hasAgreedToTerms ? "Please agree to the terms and conditions before proceeding" : ""}
                            >
                                <span className='flex items-center justify-center gap-4 text-sm md:text-base text-secondary-dark font-medium mx-auto flex-1'>Proceed {isSigningUp || isAcceptingInvite && <LoaderBtn />}</span>
                                <span className='flex items-center justify-center ml-auto bg-primary rounded-full w-8 h-8'><RightUpArrow height={14} width={14} className='translate-y-0.5' /></span>
                            </button>
                            {!isInvited && (
                                <div className='flex flex-wrap items-center justify-center gap-1 mt-2 mb-8 px-6 py-3 border-[1px] border-white/60 rounded-lg w-full text-sm text-white/70 '>Already have an account? <Link className='text-white text-medium text-base' href={'/login/talent'}>Sign in</Link></div>
                            )}
                        </div>
                    </form>



                    <ErrorModal
                        isErrorModalOpen={isErrorModalOpen}
                        setErrorModalState={setErrorModalState}
                        subheading={
                            errorModalMessage || 'Something went wrong.'
                        }
                    >
                        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                            <Button
                                className="grow bg-red-950 hover:border-red-950 hover:text-red-950 px-1.5 sm:text-sm md:px-6"
                                type="button"
                                onClick={() => {
                                    if (isInvited) {
                                        closeErrorModal()
                                        router.push('/login/recruiter')

                                    } else {
                                        closeErrorModal()
                                    }
                                }}
                            >
                                Close
                            </Button>
                        </div>
                    </ErrorModal>

                    <Modal
                        isModalOpen={isSuccessModalOpen}
                        heading='Success'
                        allowDismiss={false}
                        color='purple'
                        body={
                            <div className='flex flex-col items-center justify-center gap-4 '>
                                <div className='self-start  rounded-full bg-primary p-4'>
                                    <SuitcaseWhite />
                                </div>
                                <div>
                                    <h3 className='font-medium text-xl text-primary'>Invite accepted</h3>
                                    <p className='text-[#8C8CA1] text-[0.875rem]'>
                                        You have successfully accepted an invite to a team on Getlinked. A recruiter account has been created for you, login now to collaborate with other team members.
                                    </p>
                                </div>

                            </div>
                        }

                        footer={
                            <div className='w-full flex items-center justify-end gap-4 ml-auto bg-white p-5 rounded-[1rem]'>
                                <LinkButton href={`/login/recruiter`} size='thin' className='!px-6'>
                                    Login
                                </LinkButton>
                            </div>
                        }
                    />
                </motion.div>
            }
        </AnimatePresence>
    );
};

export default RecruiterSignUpStep2
    ;
