'use client';

import axios from 'axios';
import { AnimatePresence, motion } from 'framer-motion';
import React, { FormEvent, useEffect, useState } from 'react';
import { Button, ErrorModal, LoaderBtn } from '@/components/shared';
import { useErrorModalState } from '@/hooks';
import { cn } from '@/utils';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { useRequestPasswordResetOTP, useResendOTP } from '../../api';
import { InfoStar, MessageIcon, RightUpArrow } from '../../icons';
import { useRecruiterRegisterDetails } from '../../store';

type RecruiterSignUpStep3Props = {
  user: { email: string };
  onVerified: () => void;
};

const RecruiterSignUpStep3: React.FC<RecruiterSignUpStep3Props> = ({
  user,
  onVerified,
}) => {
  const [tokenError, setTokenError] = useState(false);
  const [timeLeft, setTimeLeft] = useState(600);
  const [isTimerActive, setIsTimerActive] = useState(true);
  const [verificationCode, setVerificationCode] = useState<string[]>([
    '',
    '',
    '',
    '',
    '',
    '',
  ]);
  const inputRefs = Array(6)
    .fill(0)
    .map(() => React.createRef<HTMLInputElement>());
  const { userPersonalData } = useRecruiterRegisterDetails();

  const {
    isErrorModalOpen,
    openErrorModalWithMessage,
    setErrorModalState,
    errorModalMessage,
    closeErrorModal,
  } = useErrorModalState();

  useEffect(() => {
    const token = verificationCode.join('');
    if (token.length === 6) {
      handleFormSubmit();
    }
  }, [verificationCode]);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (isTimerActive && timeLeft > 0) {
      timer = setInterval(() => {
        setTimeLeft(prevTime => prevTime - 1);
      }, 1000);
    } else if (timeLeft === 0) {
      setIsTimerActive(false);
    }

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [timeLeft, isTimerActive]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    const newValue = e.target.value;

    if (/^\d*$/.test(newValue)) {
      const newVerificationCode = [...verificationCode];

      if (newValue.length <= 1) {
        newVerificationCode[index] = newValue;
        setVerificationCode(newVerificationCode);

        if (newValue && index < 5) {
          inputRefs[index + 1].current?.focus();
        }
      } else if (newValue.length > 1) {
        const newValuesArrays = newValue.split('');

        newValuesArrays.forEach((value, i) => {
          if (newVerificationCode[index + i] !== undefined) {
            newVerificationCode[index + i] = value;
          }
        });

        setVerificationCode(newVerificationCode);

        if (index + newValuesArrays.length < 5) {
          inputRefs[index + newValuesArrays.length].current?.focus();
        } else {
          inputRefs[5].current?.focus();
        }
      }
    }
  };

  const handleInputBackspace = (
    e: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    if (e.key === 'Backspace' && !verificationCode[index] && index > 0) {
      const newVerificationCode = [...verificationCode];
      newVerificationCode[index - 1] = '';
      setVerificationCode(newVerificationCode);
      inputRefs[index - 1].current?.focus();
    }
  };

  const handleFormSubmit = async (e?: FormEvent<HTMLFormElement>) => {
    e?.preventDefault();
    const code = verificationCode.join('');
    if (code.length < 6) {
      setTokenError(true);
      return;
    }

    try {
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/user/auth/verify/`,
        { otp: code, recipient: user.email }
      );

      if (response.status === 200) {
        onVerified();
      }

      return response;
    } catch (error: any) {
      const errorMessage = formatAxiosErrorMessage(error);
      console.log(errorMessage, 'ERROR MESSAGE');
      if (error.response.status === 400) {
        console.log(error.response.data);
        // if (error?.response?.data?.message == 'invalid or expired OTP.') {
        if (!error?.response?.data?.success) {
          setTokenError(true);
          openErrorModalWithMessage(error?.response?.data?.error);
        }
      } else {
        openErrorModalWithMessage(errorMessage);
      }
    }
  };

  const { mutate: resendOTP, isLoading: isRequestingOTP } = useResendOTP();
  const getOTP = async () => {
    resendOTP(
      { email: user?.email || userPersonalData.email },
      {
        onSuccess() {
          setTimeLeft(600);
          setIsTimerActive(true);
        },
        onError(error) {
          const errorMessage = formatAxiosErrorMessage(error as any);
          openErrorModalWithMessage(errorMessage);
        },
      }
    );
  };

  return (
    <AnimatePresence>
      <motion.div
        className={cn(
          'my-auto flex h-full min-h-[58vh] w-full  flex-col items-center justify-center py-6 text-white transition-transform duration-300 sm:px-5 md:py-[3.5vh] lg:min-h-[72vh] 3xl:min-h-[750px]'
        )}
        initial={{ x: '110%' }}
        animate={{ x: '0%' }}
        exit={{ x: '-120%' }}
        transition={{ duration: 0.5, ease: 'linear' }}
      >
        <header className="flex flex-col self-start">
          <h1 className="text-left font-clash text-3xl font-medium lg:text-[2.35rem]">
            Verify your account
          </h1>
          <p className="md:font-base mb-6 text-sm text-[#BCBCBC]">
            An OTP code has been sent to{' '}
            <span className="font-medium text-white">
              {' '}
              {user.email || userPersonalData.email}. {' '}
            </span>
            Enter the 6-digit OTP below to verify your account
          </p>
        </header>

        <form
          onSubmit={handleFormSubmit}
          className="flex w-full flex-col gap-6 "
        >
          <div className="confirm-title text-center xs:mb-20">
            <div className="color-black flex max-w-[325.5px] justify-between gap-2 ">
              {verificationCode.map((digit, index) => (
                <input
                  key={index}
                  className="text-bold aspect-[1/1] w-[calc(25%_-_5px)] max-w-[45px] rounded-lg  border-[1.5px] border-white/50 bg-white/20 text-center font-clash text-xl text-white outline-none backdrop-blur-lg transition-all focus:outline-[1.75px] focus:outline-offset-2 focus:outline-white md:rounded-xl"
                  type="text"
                  maxLength={6}
                  value={digit}
                  onChange={e => handleInputChange(e, index)}
                  onKeyDown={e => handleInputBackspace(e, index)}
                  ref={inputRefs[index]}
                />
              ))}
            </div>
            {tokenError && (
              <span className="mt-1 font-[0.9rem] text-red-500">
                Invalid or expired token
              </span>
            )}
            <aside className="my-4 flex w-full justify-between text-xs text-white">
              <h2>Didn't receive the OTP?</h2>
              {isTimerActive ? (
                <div className="flex items-center gap-2.5">
                  <span className="flex h-7 w-7 items-center justify-center rounded-full bg-[#d9d9d926]">
                    <MessageIcon width={18} height={18} />
                  </span>
                  {formatTime(timeLeft)}
                </div>
              ) : (
                <button
                  type="button"
                  id="resendOTP"
                  className="flex items-center gap-2.5"
                  onClick={getOTP}
                >
                  <span className="flex h-7 w-7 items-center justify-center rounded-full bg-[#d9d9d926]">
                    <MessageIcon width={18} height={18} />
                  </span>
                  Resend OTP
                  {isRequestingOTP && (
                    <LoaderBtn width={12} height={12} color="white" />
                  )}
                </button>
              )}
            </aside>
          </div>

          <section className="w-fulll flex flex-col gap-4">
            <span className="btn-secondary flex w-full items-center justify-center gap-3.5 rounded-xl bg-white/50 px-1.5 py-4 text-center text-[0.7rem] text-white">
              <InfoStar />
              If you did not see the email in your inbox, kindly check your spam
              folder
            </span>
          </section>
        </form>

        <ErrorModal
          isErrorModalOpen={isErrorModalOpen}
          setErrorModalState={setErrorModalState}
          subheading={errorModalMessage}
          heading="Something went wrong"
        >
          <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
            <Button
              className="grow bg-red-950 px-1.5 hover:border-red-950 hover:text-red-950 sm:text-sm md:px-6"
              type="button"
              onClick={closeErrorModal}
            >
              Okay
            </Button>
          </div>
        </ErrorModal>
      </motion.div>
    </AnimatePresence>
  );
};

export default RecruiterSignUpStep3;
