'use client';

import * as React from 'react';
import { useFormContext } from 'react-hook-form';

import { Button, Command, CommandEmpty, CommandInput, CommandItem, CommandList, Popover, PopoverContent, PopoverTrigger, } from '@/components/shared';
import { getInitials } from '@/utils/strings';
import { cn } from '@/utils';

import useGetCompanies from '../../api/getAllCompanies'
import Image from 'next/image';
import { Company } from '../../api/getAllCompanies';

interface CompanyListProps {
    id?: string;
    onChange?: (value: string) => void;
    setSelectedCompany: (company:Company | null) => void;
    setIsExistingCompany: React.Dispatch<React.SetStateAction<boolean | null>>;
}

export function CompanyList({
    id,
    onChange,
    setSelectedCompany,
    setIsExistingCompany,
}: CompanyListProps) {
    const [open, setOpen] = React.useState(false);
    const [search, setSearch] = React.useState('');
    const { setValue } = useFormContext();
    const { data: companies, isLoading: isFetchingCompanies } = useGetCompanies();


    const options = companies?.map((company) => {

        const { id, name, address, description, website, industry, company_domain, logo } = company
        return {
            value: id,
            name: name,
            company_domain: company_domain,
            logo: logo,
            initials: (`${name[0]}${name[1]}`).toUpperCase(),
        };
    });



    const filteredOptions = options?.filter((option) => {
        const searchString = search.toLowerCase();
        const name = option.name.toLowerCase();
        const domain = option.company_domain?.toLowerCase();
        const initials = option.initials.toLowerCase();
        const id = String(option.value).toLowerCase();

        return (
            name?.includes(searchString) ||
            domain?.includes(searchString) ||
            initials?.includes(searchString) ||
            id?.includes(searchString)
        );
    });



    const [selectedValue, setSelectedValue] = React.useState<string>("");
    const handleSelect = (currentValue: string) => {
        const selected = companies?.find((option) => option.id.toString() === currentValue.toString());
        setSelectedValue(currentValue);
        setValue('name', selected?.name);
        setSelectedCompany(selected!!)
        setIsExistingCompany(true);
        setOpen(false);
    };

    const handleNoResultClick = () => {
        setSelectedCompany(null);
        setIsExistingCompany(false);
    };





    return (
        <>

            <Popover open={open} onOpenChange={setOpen}>
                <Command className='!bg-white/20 backdrop-blur-lg text-white mb-36'>
                    <PopoverTrigger>
                        <div
                            className='px-4 py-[0.875rem] text-[0.875rem] text-left'
                        >
                            Select registered company or register a new company
                        </div>
                    </PopoverTrigger>

                    <PopoverContent align="start" className='!p-0 w-[85vw] md:w-[45vw] lg:max-w-[550px] min-h-max max-h-[45vh] sm:[@media(min-height:1200px)]:max-h-[450px]'>
                        <CommandInput
                            isLoading={isFetchingCompanies}
                            placeholder={'Search registered companies or create a new company on Getlinked'}
                            value={search}
                            onValueChange={(text) => setSearch(text)}
                            className='sticky !top-0 !px-6 !bg-white z-[51]'
                        />
                        <CommandEmpty className="p-1">
                            {
                                filteredOptions && filteredOptions?.length < 1 &&
                                <>
                                    <p className="px-2 py-3 pl-6 pt-1 text-xs">No matches found.</p>
                                    <Button size='thin' variant='extralight' className='w-full' onClick={handleNoResultClick}>
                                        Add new company
                                    </Button>
                                </>
                            }
                        </CommandEmpty>

                        <CommandList className="relative max-h-96 pb-5 px-4 overflow-y-auto">
                            {
                                filteredOptions?.map((option) => {
                                    const initials = getInitials(option.name);

                                    return (
                                        <div
                                            className='relative flex select-none items-center rounded-md p-1.5 text-sm outline-none aria-selected:bg-blue-100/70 aria-selected:text-primary hover:bg-primary-light cursor-pointer'
                                            onClick={() => handleSelect(String(option.value))}
                                            key={option.value}

                                        >
                                            {/* <svg
                                                className={cn(
                                                    'mr-2 h-4 w-4',
                                                    selectedValue == String(option.value)
                                                        ? 'opacity-100'
                                                        : 'opacity-0'
                                                )}
                                                fill="none"
                                                height={16}
                                                viewBox="0 0 16 16"
                                                width={16}
                                                xmlns="http://www.w3.org/2000/svg"
                                                aria-hidden
                                            >
                                                <path
                                                    d="m14.53 5.03-8 8a.751.751 0 0 1-1.062 0l-3.5-3.5a.751.751 0 1 1 1.063-1.062L6 11.438l7.47-7.469a.751.751 0 0 1 1.062 1.063l-.001-.002Z"
                                                    fill="#755AE2"
                                                />
                                            </svg> */}

                                            <span className="flex items-center gap-3.5">
                                                {
                                                    option.logo ?
                                                        <Image src={option.logo}
                                                            alt={`${option.name} companylogo`}
                                                            height={32}
                                                            width={32}
                                                            className='rounded-full'
                                                        />
                                                        :
                                                        <span className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-xs text-white">

                                                            {initials}
                                                        </span>
                                                }

                                                <span className="flex flex-col">
                                                    <span>{option.name}</span>
                                                    <span
                                                        className={cn(
                                                            'text-xxs text-[#646464] text-opacity-80'
                                                        )}
                                                    >
                                                        {option.company_domain}
                                                    </span>
                                                </span>
                                            </span>

                                            <input type="radio" name="" id="" className='ml-auto' checked={selectedValue == String(option.value)} />

                                        </div>
                                    );
                                })
                            }
                           
                        </CommandList>
                         {
                                filteredOptions?.length!! > 0 &&
                                <div className='bg-white shadow-lg p-4 pt-1 sticky bottom-0'>
                                    <span className='text-xs text-[#3C1356]'>Cannot find your company name ? click button below</span>
                                    <Button size='thin' variant='extralight' className='w-full' onClick={handleNoResultClick}>
                                        Add new company
                                    </Button>
                                </div>
                            }
                    </PopoverContent>

                </Command>
            </Popover>


        </>
    );
}
