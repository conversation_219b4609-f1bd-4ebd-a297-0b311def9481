'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { z } from 'zod';
import { Input, LoaderBtn } from '@/components/shared';
import useUserStore from '@/store/userInfoStore';
import { cn, setAccessToken } from '@/utils';
import { setFormErrors } from '@/utils/errors';
import { useSignIn } from '../../api';
import { Lock, Mail, RightUpArrow } from '../../icons';
import { useRecruiterRegisterDetails } from '../../store';

export type FormData = {
  email: string;
  password: string;
  first_name: string;
};

const registerForm = z.object({
  email: z
    .string({ required_error: 'Enter email.' })
    .email('Enter a valid email'),
  password: z
    .string({ required_error: 'Enter password.' })
    .min(8, 'Password must be at least 8 characters'),
});

const RecruiterSigninForm = () => {
  const router = useRouter();
  const params = useSearchParams();
  const redirectError = params.get('session');
  const redirect = params.get('redirect');

  const { setStep, userPersonalData, setUserPersonalData, resetStore } =
    useRecruiterRegisterDetails();
  const { setUser: setSavedUser, getUser: getSavedUser } = useUserStore();

  const [isChecked, setIsChecked] = useState(false);

  const {
    handleSubmit,
    register,
    formState: { errors, isDirty, isValid },
    watch,
    setError,
    setValue,
  } = useForm({ resolver: zodResolver(registerForm) });
  const { mutateAsync: signIn, isLoading: isSIgningIn } = useSignIn();

  useEffect(() => {
    if (redirectError === 'session') {
      toast.error('Session Expired! Log in to continue', { id: '234' });
    } else if (redirectError === 'logout') {
      toast.error('Log in again to continue', { id: '234' });
    }
  }, []);

  const onSubmit = async (data: any) => {
    signIn(data, {
      onSuccess: async response => {
        if (response?.data?.data?.message === 'USER PROFILE is not verified.') {
          setUserPersonalData({ ...userPersonalData, ...data });
          setStep(3);
          router.push('/register/complete-registration');
          return;
        }
        await setAccessToken(response?.data?.data?.access);
        setSavedUser(null);

        if (redirect) {
          router.push(redirect);
        } else {
          // router.push('/e/assessments-and-interviews/my-assessments');
          router.push('/e/dashboard');
        }
      },


      onError: (error: any) => {
        console.log(error, 'ERROR');
        toast.error(error?.response?.data?.message, {
          id: '234',
        });
        if (error?.response?.data.error == "Invalid credentials.") {
          setError('email', {
            message: "Invalid Credentials",
            type: 'custom'
          })
          setError('password', {
            message: "Invalid Credentials",
            type: 'custom'
          })
        }


        if (error?.response?.status === 412) {
          if (
            error?.response?.data?.errors.message ==
            'USER PROFILE is not verified.'
          ) {
          }
        } else {
          return;
        }
      },
    });
  };

  return (
    <div
      className={`my-auto flex h-full min-h-[58vh] w-full flex-col  justify-center md:h-full lg:min-h-[72vh] lg:px-8 3xl:min-h-[750px]`}
    >
      <header>
        <h1 className="flex items-center gap-4 font-clash text-3xl font-semibold text-white 2xl:text-[2.35rem]">
          <span>Welcome back 👋</span>
        </h1>
        <p className="mb-8 mt-1.5 text-sm font-normal text-white 2xl:text-base">
          Enter details to login
        </p>
      </header>

      <form onSubmit={handleSubmit(onSubmit)} className=" flex flex-col gap-4">
        <Input
          placeholder="Enter email"
          // label='Email'
          leftIcon={<Mail stroke="white" />}
          {...register('email')}
          variant="transparent"
          hasError={!!errors.email}
          errorMessage={errors.email?.message as string}
        />
        <Input
          type="password"
          placeholder="Enter password"
          // label='Password'
          leftIcon={<Lock stroke="white" />}
          {...register('password')}
          variant="transparent"
          hasError={!!errors.password}
          errorMessage={errors.password?.message as string}
        />

        <div>
          <div className="mb-8 mt-2 flex flex-wrap justify-between gap-2 text-xs text-white/80 xs:flex-nowrap">
            <span className="flex items-center justify-center gap-1">
              <input
                type="checkbox"
                checked={isChecked}
                onChange={() => setIsChecked(!isChecked)}
                className="h-4 w-4 rounded-sm border-2"
              />
              Keep me logged in
            </span>

            <Link
              className=" hover:underline"
              href={'./forgot-password?user=recruiter'}
            >
              Forgot password?
            </Link>
          </div>

          <button
            className="mt-8 flex w-full items-center justify-center rounded-lg bg-white px-3 py-1.5 text-center text-primary disabled:bg-grey/70 lg:mt-16 "
            type="submit"
            disabled={!isDirty || isSIgningIn}
          >
            <span className="text-secondary-dark mx-auto flex flex-1 items-center justify-center gap-2 text-[0.825rem] font-medium md:text-base">
              Proceed {isSIgningIn && <LoaderBtn />}
            </span>
            <span className="ml-auto flex h-8 w-8 items-center justify-center rounded-full bg-primary">
              <RightUpArrow height={14} width={14} className="translate-y-0" />
            </span>
          </button>
          <Link
            href={'/register/recruiter'}
            className="mt-4 flex w-full flex-wrap items-center justify-center gap-1 rounded-lg border-[1px] border-white px-2 py-3.5 text-xs text-white/75 "
          >
            <span className="block ">Don&apos;t have an account?</span>{' '}
            <span className="text-white">Sign up</span>
          </Link>
        </div>
      </form>
    </div>
  );
};

export default RecruiterSigninForm;
