"use client"
import React, { useState } from 'react';
import { RecruiterSigninFormData, RecruiterSignupFormData } from '../../types/SigupTypes';
import RecruiterSignInStep0 from './RecruiterSignInStep0';


const RecruiterSignInFlow = () => {
  const [step, setStep] = useState(1);
  const [userData, setUserData] = useState<RecruiterSigninFormData>({ email: '', password: '', recruiter_type: "HR_PERSONNEL" });

  const moveToNextStep = () => {
    setStep(step + 1);
  };
  const handleSignIn = (data: RecruiterSigninFormData) => {
    setUserData(data);
    moveToNextStep();
  }


  return (
    <>
      {step === 1 && <RecruiterSignInStep0 />}
    </>
  );
};


export default RecruiterSignInFlow;

