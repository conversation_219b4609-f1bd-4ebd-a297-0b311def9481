"use client"

import React, { Dispatch, SetStateAction } from 'react';
import { Control, FieldErrors, UseFieldArrayAppend, UseFieldArrayRemove, UseFormRegister, UseFormSetValue, UseFormWatch } from 'react-hook-form';
import ReactTextareaAutosize from 'react-textarea-autosize';

import { XIcon } from '@/app/(website-main)/e/jobs/misc/icons';
import { Button, FileUploader, Select, ToolTip } from '@/components/shared';
import { Plus, } from '@/components/shared/icons';
import { cn } from '@/utils';

import { useRecruiterRegisterDetails } from '../../store';
import { createorRegisterUnderCompanyData } from '../../types/SigupTypes';
// import { INDUSTRIES_OPTIONS } from '../../constants';
import { industries } from '@/app/(website-main)/e/jobs/misc/constants';
import { debounce } from 'lodash';


interface RecruiterSignUpStep4_HRandAgencyProps {
    setIsExistingCompany: Dispatch<SetStateAction<boolean | null>>
    register: UseFormRegister<createorRegisterUnderCompanyData>
    setValue: UseFormSetValue<createorRegisterUnderCompanyData>
    teamEmails: Record<"id", string>[]
    removeTeamMemberEmail: UseFieldArrayRemove
    addTeamMemberEmail: UseFieldArrayAppend<createorRegisterUnderCompanyData, never>
    goBack: () => void
    reset: () => void
    watch: UseFormWatch<createorRegisterUnderCompanyData>
    errors: FieldErrors<createorRegisterUnderCompanyData>
    control: Control<createorRegisterUnderCompanyData, any>

};




export const COMPANY_SIZES = [
    { value: '1-50', name: '1-50' },
    { value: '50-500', name: '50-500' },
    { value: '500-1000', name: '500-1000' },
    { value: '1000-5000', name: '1000-5000' },
    { value: '5000+', name: '5000+' },
];








const RecruiterSignUpStep4NewCompany: React.FC<RecruiterSignUpStep4_HRandAgencyProps> = ({ goBack, watch, register, errors, reset, setValue, control, teamEmails, addTeamMemberEmail, removeTeamMemberEmail, setIsExistingCompany }) => {
    const { setUserCompanyData, clearStorage } = useRecruiterRegisterDetails();

    React.useEffect(() => {
        const debouncedFn = debounce(setUserCompanyData, 1000);
        const subscription = watch(debouncedFn);
        return () => subscription.unsubscribe();
    }, [watch]);

   



    return (
        <>
            <Button onClick={goBack} size='capsule' variant='extralight' className='max-w-max'>
                Back
            </Button>

            <div className='inputdiv transparent  my-2 grow'>
                <label className='sr-only' htmlFor="name">Enter company name</label>
                <div className='relative'>
                    <input type="text" placeholder="Enter company name" className={cn("!bg-white/20 !text-white placeholder:opacity-100", errors.name && "error", "")} {...register('name')} id="name" />
                </div>
                {errors.name && <p className='formerror'>{errors.name.message}</p>}
            </div>

            <Select
                {...register('industry')}
                options={industries}
                hasError={!!errors.industry}
                errorMessage={errors.industry?.message}
                placeholder="Select industry"
                valueKey='value'
                labelKey="name"
                variant="transparent"
                itemClass='text-[0.825rem]'
                triggerColor='white'
                onChange={(value) => setValue('industry', value)}
            />

            <div className='inputdiv transparent  my-2 grow'>
                <label className='sr-only' htmlFor="website">Enter company email domain</label>
                <div className='relative'>
                    <input type="text" placeholder="Company email domain e.g(getlinked.com)" className={cn("!bg-white/20 !text-white placeholder:opacity-100", errors.company_domain && "error", "")} {...register('company_domain')} id="company_domain" />
                </div>
                {errors.company_domain && <p className='formerror'>{errors.company_domain.message}</p>}
            </div>

            <div className='inputdiv transparent  my-2 '>
                <label className='sr-only' htmlFor="email">Enter your company email</label>
                <ToolTip content="Enter a company email tied to the company's email domain, e.g if the company email domain is 'student.oauife.edu.ng', your email should be '<EMAIL>'">
                    <input type="email" placeholder="Official email e.g(<EMAIL>)" className={cn("!bg-white/20 !text-white placeholder:opacity-100", errors.company_email && "error", "")} {...register('company_email')} id="email" />
                </ToolTip>
                {errors.company_email && <p className='formerror'>{errors.company_email.message}</p>}
            </div>


            <div className='inputdiv transparent  my-2 grow'>
                <label className='sr-only' htmlFor="website">Enter company website</label>
                <div className='relative'>
                    <input type="text" placeholder="Enter company website" className={cn("!bg-white/20 !text-white placeholder:opacity-100", errors.website && "error", "")} {...register('website')} id="website" />
                </div>
                {errors.website && <p className='formerror'>{errors.website.message}</p>}
            </div>

            <Select
                {...register('size')}
                onChange={(value) => setValue('size', value)}
                options={COMPANY_SIZES}
                placeholder="Select company size"
                triggerColor='white'
                itemClass='text-[0.825rem]'
                valueKey='value'
                labelKey="name"
                variant="transparent"
            />

            <div className='inputdiv transparent  my-2 grow'>
                <label className='sr-only' htmlFor="address">Enter company location</label>
                <div className='relative'>
                    <input type="text" placeholder="Enter company address" className={cn("!bg-white/20 !text-white placeholder:opacity-70", errors.address && "error", "")} {...register('address')} id="address" />
                </div>
                {errors.address && <p className='formerror'>{errors.address.message}</p>}
            </div>

            <FileUploader
                control={control}
                name="logo"
                label="company logo"
                acceptedFormats={`image/*`}
                acceptedFileExtensions={"png, jpg, gif"}
                maxSize={10}
                color="white"
                className='my-2'
                errors={errors}
            />
            <div className='inputdiv transparent  my-2'>
                <ReactTextareaAutosize
                    className={cn('!bg-white/20 backdrop-blur-lg resize-none text-white placeholder:opacity-50', errors.description && 'error')}
                    minRows={4}
                                    style={{color:"white"}}

                    maxRows={20}
                    {...register('description', { required: 'Enter company description' })}
                    placeholder='Kindly describe the product or service your company offers'
                />
                {errors.description && <p className='formerror'>{errors.description.message}</p>}

            </div>


            <div className='my-6'>
                <header className='mb-2'>
                    <h2 className='text-lg text-white'>Invite team members <span className='text-sm text-white/70'> (optional)</span></h2>
                    <p className='text-[#BCBCBC] text-sm tracking-tight'>Enter your team member’s email, we will send them an invite to join Getlinked.</p>
                </header>
                {
                    teamEmails.map((item, index) => (
                        <div className='inputdiv transparent  relative my-1.5 grow' key={index}>
                            <label className='sr-only' htmlFor={`team_member_emails.${index}`}>Enter team member email</label>
                            <div>
                                <input type="email" placeholder="Enter email" className={cn("!bg-white/20 !text-white placeholder:opacity-100", errors.team_member_emails && errors.team_member_emails[index] && "error", "")} {...register(`team_member_emails.${index}`)} id={`team_member_emails.${index}`} />
                            </div>
                            <span className='bg-red-300 rounded-full'>
                                <XIcon width={40} height={40} stroke='red' className='absolute top-[10.25%] right-[0%] cursor-pointer' onClick={() => removeTeamMemberEmail(index)} />
                            </span>
                            {errors.team_member_emails && <p className='formerror'>{errors.team_member_emails[index]?.message}</p>}
                        </div>
                    ))
                }
                {
                    teamEmails.length < 1 &&
                    <div className='inputdiv transparent  my-1.5 grow'>
                        <label className='sr-only' htmlFor={`dummy_team_email`}>Enter role in company</label>
                        <div className='relative'>
                            <input type="email" placeholder="E.g. <EMAIL>"
                                className={cn("!bg-white/20 !text-white placeholder:opacity-100")} id={`dummy_team_email`}
                                onFocus={() => addTeamMemberEmail('')}
                            />
                        </div>
                    </div>

                }

                <Button
                    type="button"
                    variant='unstyled'
                    size='capsule'
                    className="flex items-center gap-2 text-sm text-white/60 hover:text-white/80"
                    onClick={() => addTeamMemberEmail('')}
                    icon={<Plus fill='white' />}
                >
                    Add more members
                </Button>
            </div>

          
        </>

    );
};

export default RecruiterSignUpStep4NewCompany;