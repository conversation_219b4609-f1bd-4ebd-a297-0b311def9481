"use client"
import React, { useState } from 'react';
import { RecruiterSignupFormData } from '../../types/SigupTypes';
import RecruiterSignUpStep2 from './RecruiterSignUpStep2';
import RecruiterSignUpStep3 from './RecruiterSignUpStep3';
import RecruiterSignUpStep1 from './RecruiterSignUpStep1';
import RecruiterSignUpStep4_Personal from './RecruiterSignUpStep4_Personal';
import RecruiterSignUpStep4_HRandAgency from './RecruiterSignUpStep4_HRandAgency';
import { useRecruiterRegisterDetails } from '../../store';
import { Rotate } from '@/components/shared/icons';
import { cn } from '@/utils';
import { Button, Checkbox2, ConfirmActionModal, ErrorModal, Input, LinkButton, LoaderBtn, Modal, Select } from '@/components/shared';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { AnimatePresence, motion } from 'framer-motion';
import { useSearchParams } from 'next/navigation';
import { z, ZodError, } from 'zod';
import { useForm } from 'react-hook-form';
import { AxiosError } from 'axios';
import { debounce } from 'lodash';
import { useAcceptTeamInvite, useSignUp } from '../../api';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { Mail, User, Lock, RightUpArrow } from '../../icons';
import { SuitcaseWhite } from '@/app/(website-main)/e/jobs/misc/icons';
import Link from 'next/link';
import { zodResolver } from '@hookform/resolvers/zod';
import useDecodeInvitationToken from '../../api/decodeInvitationToken';


const genders = [
  { name: 'Male', value: 'M' },
  { name: 'Female', value: 'F' },
]
const countries = [
  { value: '+1', flag: '🇺🇸', name: 'US' },
  { value: '+44', flag: '🇬🇧', name: 'UK' },
  { value: '+234', flag: '🇳🇬', name: 'NG' },
  { value: '+27', flag: '🇿🇦', name: 'ZA' },
  { value: '+233', flag: '🇬🇭', name: 'GH' },
  { value: '+254', flag: '🇰🇪', name: 'KE' },
  { value: '+33', flag: '🇫🇷', name: 'FR' }
];




const RecruiterInviteSignUpFlow = () => {

  const { step, setStep, userPersonalData, moveToNextStep, setUserPersonalData, setRecruiterType, resetStore } = useRecruiterRegisterDetails();


  const handleSignUp = (data: RecruiterSignupFormData) => {
    setUserPersonalData(data);
    moveToNextStep();
  };



  return (
    <AnimatePresence>


      <RecruiterSignUpStep2 onNext={handleSignUp} recruiter_type={userPersonalData.recruiter_type!!} />

    </AnimatePresence>
  )
};

export default RecruiterInviteSignUpFlow;