"use client"

import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, HR<PERSON><PERSON><PERSON>er, PersonalRecruiter, RightUpArrow } from '../../icons';
import { cn } from '@/utils';
import Link from 'next/link';
import { AnimatePresence, motion } from 'framer-motion'





type RecruiterSignUpStep1Props = {
    onNext: (data: string) => void;
};





const RecruiterSignUpStep1: React.FC<RecruiterSignUpStep1Props> = ({ onNext }) => {
    const [currentType, setcurrentType] = useState("PERSONAL_RECRUITER")
    const handleChooseRecruiterType = async () => {
        const timer = setTimeout(() => {
            onNext(currentType)
        }, 30);
        return () => clearTimeout(timer)
    };

    const types = [
        {
            value: "PERSONAL_RECRUITER",
            title: "Personal talent sourcing",
            desc: "Here you can onboard as a company without an HR team and perform HR related action but would not be able to add or invite team members.",
            icon: <PersonalRecruiter fill="white" width={24} height={24} />
        },
        {
            value: "HR_PERSONNEL",
            title: "HR personnel",
            desc: "Here you can onboard as an HR personnel and have other team members within your organisation join you in making recruitment decisions.",
            icon: <HRRecruiter fill="white" width={40} height={40} />
        },
        {
            value: "RECRUITMENT_AGENCY",
            title: "Recruitment agency",
            desc: "Here you can onboard as a recruitment agency and manage recruitments for your clients with easy",
            icon: <AgencyRecruiter fill="white" width={24} height={24} />
        },
    ]





    return (
        <AnimatePresence>
            <motion.div className={cn("flex flex-col items-center justify-center w-full  min-h-[58vh] lg:min-h-[72vh] 3xl:min-h-[750px] h-full my-auto text-white",)}
                initial={{ x: '110%', }}
                animate={{ x: "0%", }}
                exit={{ x: '-120%', }}
                transition={{ duration: 0.5, ease: 'linear' }}
            >
                <header className='flex flex-col self-start mb-8'>
                    <h1 className='text-3xl lg:text-[2.35rem] text-left font-clash font-medium'>Select option</h1>
                    <p className="text-[#BCBCBC] text-sm md:font-base">Select preferred option to have services tailored to your need  </p>
                </header>

                <section className='space-y-4'>
                    {
                        types.map((type, index) => {
                            const { title, desc, icon, value } = type
                            return (
                                <article className={
                                    cn("relative flex max-sm:flex-col items-stretch gap-4 bg-white/20 backdrop-blur-lg hover:bg-primary/30  p-4 rounded-lg max-w-full md:min-h-[120px] cursor-pointer transition-colors duration-300",
                                        currentType == value && "bg-primary/50"
                                    )}
                                    key={index}
                                    onClick={() => setcurrentType(value)}
                                >
                                    {icon}
                                    <div className='text-white '>
                                        <h2 className='text-lg font-bold text-uppercase '>{title}</h2>
                                        <p className='text-sm !font-normal leading-4'>{desc}</p>
                                    </div>
                                    <div>
                                        <div className={cn('max-sm:absolute flex items-center justify-center right-6 top-6 w-5 h-5 border-[2.5px] border-white rounded-full')}>
                                            {
                                                currentType == value &&
                                                <span className='block w-[80%] h-[80%] bg-white rounded-full'></span>
                                            }
                                        </div>
                                    </div>
                                </article>
                            )
                        })
                    }
                </section>

                <section className='flex flex-col gap-2 w-full mt-[10vh]'>
                    <button onClick={handleChooseRecruiterType} className="bg-white flex items-center justify-center w-[full] disabled:bg-primary-light py-1.5 px-3 rounded-lg text-primary text-center " type="submit">
                        <span className=' text-sm md:text-base text-secondary-dark font-medium mx-auto flex-1'>Proceed</span>
                        <span className='flex items-center justify-center ml-auto bg-primary rounded-full w-8 h-8'><RightUpArrow height={14} width={14} className='translate-y-0.5' /></span>
                    </button>

                    <Link href={"/login/recruiter"} className='flex flex-wrap items-center justify-center gap-1 mt-1.5 mb-8 px-6 py-3 border-[1px] border-white rounded-lg w-full text-white/75 text-xs'>
                        Already have an account?
                        <span className='text-white text-[0.95rem] ml-1'>
                            Sign in
                        </span>
                    </Link>
                </section>
            </motion.div>
        </AnimatePresence>

    );
};

export default RecruiterSignUpStep1;
