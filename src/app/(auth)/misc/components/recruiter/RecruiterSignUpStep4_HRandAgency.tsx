'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { AxiosError } from 'axios';
import { motion } from 'framer-motion';
import { debounce } from 'lodash';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { FormProvider, useFieldArray, useForm } from 'react-hook-form';
import {
  Button,
  ErrorModal,
  LoaderBtn,
  LoadingOverlay,
  Modal,
  ToolTip,
} from '@/components/shared';
import { Check } from '@/components/shared/icons';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { Axios } from '@/lib/api/axios';
import { cn, setAccessToken } from '@/utils';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { convertKebabAndSnakeToTitleCase } from '@/utils/strings';
import {
  useCreateCompany,
  useCreateRecruiter,
  useRequestPasswordResetOTP,
  useSignIn,
} from '../../api';
import { Company } from '../../api/getAllCompanies';
import { MessageIcon, RightUpArrow } from '../../icons';
import { useRecruiterRegisterDetails } from '../../store';
import {
  createCompanyData,
  createorRegisterUnderCompanyData,
  RecruiterSigninFormData,
} from '../../types/SigupTypes';
import { existingCompanyForm, newCompanyForm } from '../../types/zodResolvers';
import { CompanyList } from './CompanyList';
import RecruiterSignUpStep4ExistingCompany from './RecruiterSignUpStep4ExistingCompany';
import RecruiterSignUpStep4NewCompany from './RecruiterSignUpStep4NewCompany';

interface RecruiterSignUpStep4_HRandAgencyProps {
  recruiter_type: string;
  userData: RecruiterSigninFormData;
}

const RecruiterSignUpStep4_HRandAgency: React.FC<
  RecruiterSignUpStep4_HRandAgencyProps
> = ({ userData, recruiter_type }) => {
  const router = useRouter();
  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();
  const { state: isSuccessModalOpen, setTrue: openSuccessModal } =
    useBooleanStateControl();
  const { mutateAsync: signIn, isLoading: isSigningIn } = useSignIn();
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const [isExistingCompany, setIsExistingCompany] = useState<boolean | null>(
    null
  );

  const {
    userCompanyData,
    userPersonalData,
    setUserCompanyData,
    clearStorage,
    resetStore,
  } = useRecruiterRegisterDetails();
  const methods = useForm<createorRegisterUnderCompanyData>({
    defaultValues: {
      name: userCompanyData.name,
      company_email: userCompanyData.company_email,
      // role: userCompanyData.role,
      industry:
        userCompanyData.industry ||
        'Technology and Information Technology (IT)',
      website: userCompanyData.website,
      description: userCompanyData.description,
      size: userCompanyData.size || '1-50',
      company_domain: userCompanyData.company_domain,
      address: userCompanyData.address,
      team_member_emails: undefined,
    },
    resolver: zodResolver(
      isExistingCompany ? existingCompanyForm : newCompanyForm
    ),
  });
  const {
    handleSubmit,
    register,
    control,
    formState: { errors, isDirty, isValid },
    setError,
    watch,
    getValues,
    setValue,
    reset,
  } = methods;
  const {
    fields: teamEmails,
    append: addTeamMemberEmail,
    remove: removeTeamMemberEmail,
  } = useFieldArray<createorRegisterUnderCompanyData>({
    name: 'team_member_emails' as never,
    control,
  });
  React.useEffect(() => {
    const debouncedFn = debounce(setUserCompanyData, 1000);
    const subscription = watch(debouncedFn);
    return () => subscription.unsubscribe();
  }, [watch]);

  useEffect(() => {
    signIn(
      {
        email: userData.email || userPersonalData.email,
        password: userData.password || userPersonalData.password,
      },
      {
        onSuccess: async response => {
          await setAccessToken(response?.data?.data?.access);
        },
      }
    );
  }, []);

  //////////////////////////////////////////////////////////////
  //////////       EMAIL VERIFICATION
  //////////////////////////////////////////////////////////////
  const [showVerifyForm, setShowVerifyForm] = useState<boolean>(false);
  const [timeLeft, setTimeLeft] = useState(300);
  const [isTimerActive, setIsTimerActive] = useState(true);
  const [tokenError, setTokenError] = useState<null | 'expired' | 'incomplete'>(
    null
  );
  const [verificationCode, setVerificationCode] = useState<string[]>([
    '',
    '',
    '',
    '',
    '',
    '',
  ]);
  const inputRefs = Array(6)
    .fill(0)
    .map(() => React.createRef<HTMLInputElement>());
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (isTimerActive && timeLeft > 0) {
      timer = setInterval(() => {
        setTimeLeft(prevTime => prevTime - 1);
      }, 1000);
    } else if (timeLeft === 0) {
      setIsTimerActive(false);
    }

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [timeLeft, isTimerActive]);
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    const newValue = e.target.value;

    if (/^\d*$/.test(newValue)) {
      const newVerificationCode = [...verificationCode];
      if (newValue.length <= 1) {
        newVerificationCode[index] = newValue;
        setVerificationCode(newVerificationCode);

        if (newValue && index < 5) {
          inputRefs[index + 1].current?.focus();
        }
      } else if (newValue.length > 1) {
        const newValuesArrays = newValue.split('');

        newValuesArrays.forEach((value, i) => {
          if (newVerificationCode[index + i] !== undefined) {
            newVerificationCode[index + i] = value;
          }
        });

        setVerificationCode(newVerificationCode);

        if (index + newValuesArrays.length < 5) {
          inputRefs[index + newValuesArrays.length].current?.focus();
        } else {
          inputRefs[5].current?.focus();
        }
      }
    }
  };
  const handleInputBackspace = (
    e: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    if (e.key === 'Backspace' && !verificationCode[index] && index > 0) {
      const newVerificationCode = [...verificationCode];
      newVerificationCode[index - 1] = '';
      setVerificationCode(newVerificationCode);
      inputRefs[index - 1].current?.focus();
    }
  };

  const verifyEmail = async () => {
    const code = verificationCode.join('');
    if (code.trim().length < 6) {
      setTokenError('incomplete');
      return;
    }
    try {
      const response = await Axios.post(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/recruiter/verify_company_email/`,
        { code: code, email: watch('company_email') }
      );
      if (response.status === 200) {
        signIn(
          { email: userData.email, password: userData.password },
          {
            onSuccess: async response => {
              await setAccessToken(response?.data?.data?.access);
            },
          }
        );
        openSuccessModal();
      }
    } catch (error: any) {
      const errorMessage = formatAxiosErrorMessage(error as AxiosError);
      openErrorModalWithMessage(errorMessage);
      if (error.response.status === 400) {
        if (error.response.data.errors.message == 'invalid or expired OTP.') {
          setTokenError('expired');
        }
      }
    }
  };
  const goBack = () => {
    reset();
    setIsExistingCompany(null);
  };
  const goBackWithoutReset = () => {
    reset();
    setIsExistingCompany(null);
  };

  const { mutate: requestOTP, isLoading: isRequestingOTP } =
    useRequestPasswordResetOTP();
  const resendOTP = async () => {
    try {
      await requestOTP({ email: watch('company_email') });
      setTimeLeft(300);
      setIsTimerActive(true);
    } catch (error: any) {
      const errorMessage = formatAxiosErrorMessage(error as AxiosError);
      openErrorModalWithMessage(errorMessage);
    }
  };

  ////////////////////////////////////////////////////////////////////////////////////////
  ////////////////////////////////////////////////////////////////////////////////////////
  ////////////////////////////////////////////////////////////////////////////////////////
  //////////////////////////////       SUBMIT FORM     ///////////////////////////////////
  ////////////////////////////////////////////////////////////////////////////////////////
  ////////////////////////////////////////////////////////////////////////////////////////
  ////////////////////////////////////////////////////////////////////////////////////////
  const { mutate: createCompany, isLoading: isCompanyCreating } =
    useCreateCompany();
  const { mutate: createRecruiter, isLoading: isRecruiterCreating } =
    useCreateRecruiter();
  const onSubmit = async (formData: createorRegisterUnderCompanyData) => {
    function endsWithDomain(email: string, domain: string) {
      const domainPattern = new RegExp(`@${domain}$`, 'i');
      return domainPattern.test(email);
    }
    signIn(
      { email: userData.email, password: userData.password },
      {
        onSuccess: async response => {
          await setAccessToken(response?.data?.data?.access);
        },
        onError(error, variables, context) {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage);
        },
      }
    );

    if (!isExistingCompany) {
      const checkIfDomainCorrect = endsWithDomain(
        formData.company_email,
        formData.company_domain
      );
      if (!checkIfDomainCorrect) {
        setError('company_email', {
          type: 'manual',
          message: 'Email domain does not match company domain',
        });
        return;
      }
      const {
        name,
        address,
        website,
        industry,
        size,
        description,
        logo,
        company_domain,
        team_member_emails,
      } = formData as unknown as createCompanyData;
      const dto = {
        name,
        address,
        website,
        industry,
        size,
        description,
        logo,
        company_domain,
        team_member_emails,
        type_of_company: 'COOPERATE',
        invite_link: 'https://app.getlinked.ai/register/recruiter',
      };
      createCompany(dto, {
        onSuccess: data => {
          const {
            company: { id },
          } = data;
          const dto = {
            company: id,
            company_email: formData.company_email,
            // role: formData.role,
            type_of_recruiter: recruiter_type,
          };
          createRecruiter(dto, {
            onSuccess: data => {
              setShowVerifyForm(true);
              setIsTimerActive(true);
            },
            onError(error, variables, context) {
              const errorMessage = formatAxiosErrorMessage(error as AxiosError);
              openErrorModalWithMessage(errorMessage);
            },
          });
        },
        onError(error, variables, context) {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage);
        },
      });
    } else {
      const checkIfDomainCorrect = endsWithDomain(
        formData.company_email,
        selectedCompany?.company_domain!!
      );
      if (!checkIfDomainCorrect) {
        setError('company_email', {
          type: 'manual',
          message: 'Email domain does not match company domain',
        });
        return;
      }
      const dto = {
        company: String(selectedCompany?.id!!),
        company_email: formData.company_email,
        // role: formData.role,
        type_of_recruiter: recruiter_type,
      };
      createRecruiter(dto, {
        onSuccess: data => {
          setShowVerifyForm(true);
        },
        onError(error, variables, context) {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage);
        },
      });
    }
  };

  return (
    <FormProvider {...methods}>
      <motion.div
        className={cn(
          'my-auto flex h-full min-h-[58vh] w-full  flex-col items-center justify-center py-6 transition-transform duration-300 md:py-[3.5vh] lg:min-h-[72vh] 3xl:min-h-[750px]'
        )}
        initial={{ opacity: 1, x: '110%' }}
        animate={{
          opacity: 1,
          x: showVerifyForm ? '-120%' : '0%',
          transition: { duration: 0.5, type: 'tween', ease: 'linear' },
        }}
        exit={{
          opacity: 1,
          x: '-120%',
          transition: { duration: 0.3, type: 'tween', ease: 'linear' },
        }}
      >
        <header className="flex flex-col self-start ">
          <h1 className=" font-clash text-3xl font-medium text-white md:text-[2.35rem]">
            Let&apos;s help set up your{' '}
            <br className="max-xs:hidden lg:max-xl:hidden" /> company profile
            <span className="ml-2 inline-block translate-y-[-1.75px] items-center justify-center rounded-lg bg-white/20 px-4 py-0.5 text-lg">
              2/2
            </span>
          </h1>
          <p className="mb-8 mt-2 text-sm text-[#b5b5c8] lg:text-base">
            Kindly complete your profile setup.
          </p>
        </header>

        <form
          onSubmit={handleSubmit(onSubmit)}
          className="flex w-full flex-col"
        >
          {isExistingCompany === null && (
            <div className="my-2">
              <CompanyList
                setSelectedCompany={setSelectedCompany}
                setIsExistingCompany={setIsExistingCompany}
              />
            </div>
          )}

          {/* ///////////////////////////////////////////////////////////////////////////// */}
          {/* //////////////////////        EXISTSING COMPANY      //////////////////////// */}
          {/* ///////////////////////////////////////////////////////////////////////////// */}
          {isExistingCompany && (
            <RecruiterSignUpStep4ExistingCompany
              selectedCompany={selectedCompany}
              errors={errors}
              goBack={goBack}
              register={register}
              reset={reset}
              setIsExistingCompany={setIsExistingCompany}
              watch={watch}
            />
          )}

          {/* ///////////////////////////////////////////////////////////////////////////// */}
          {/* ///////////////////////          NEW COMPANY        ///////////////////////// */}
          {/* ///////////////////////////////////////////////////////////////////////////// */}
          {isExistingCompany === false && (
            <RecruiterSignUpStep4NewCompany
              control={control}
              setValue={setValue}
              teamEmails={teamEmails}
              addTeamMemberEmail={addTeamMemberEmail}
              removeTeamMemberEmail={removeTeamMemberEmail}
              watch={watch}
              register={register}
              errors={errors}
              reset={reset}
              goBack={goBack}
              setIsExistingCompany={setIsExistingCompany}
            />
          )}

          {isExistingCompany !== null && (
            <button
              className="mt-12 flex w-full items-center justify-center rounded-lg bg-white px-3 py-1.5 text-center text-primary disabled:bg-white/40"
              type="submit"
            >
              <span className="text-secondary-dark mx-auto flex flex-1 items-center justify-center gap-2 text-sm font-medium md:text-base">
                Submit Details {isCompanyCreating && <LoaderBtn />}
              </span>
              <span className="ml-auto flex h-8 w-8 items-center justify-center rounded-full bg-primary">
                <RightUpArrow
                  height={14}
                  width={14}
                  className="translate-y-0.5"
                />
              </span>
            </button>
          )}
        </form>
      </motion.div>

      {/* ////////////////////////////////////////////////////////////////////// */}
      {/* ////////////////////////////////////////////////////////////////////// */}
      {/* ///////////////             VERIFY EMAIL               /////////////// */}
      {/* ////////////////////////////////////////////////////////////////////// */}
      {/* ////////////////////////////////////////////////////////////////////// */}

      <motion.div
        className={cn(
          'absolute bottom-0 left-0 right-0 top-0 my-auto flex w-full flex-col items-center justify-center text-white transition-transform duration-300 lg:px-8 xl:px-16',
          !showVerifyForm && '!hidden'
        )}
        initial={{ opacity: 0.75, x: '110%' }}
        animate={{
          opacity: 1,
          x: showVerifyForm ? '0%' : '110%',
          transition: { duration: 0.5, type: 'spring', ease: 'linear' },
        }}
        exit={{
          opacity: 0.75,
          x: '-120%',
          transition: { duration: 0.3, type: 'spring', ease: 'linear' },
        }}
      >
        <ToolTip
          content="Change company"
          className="mb-8 self-start rounded-full bg-primary-light text-primary"
          align="end"
        >
          <section
            className="ml-auto cursor-pointer !rounded-full !py-[0.3rem] px-[1.35rem] text-[0.8rem] "
            onClick={goBackWithoutReset}
          >
            <span>Back</span>
          </section>
        </ToolTip>
        <header className="flex flex-col items-start">
          <h1 className="text-left font-clash text-3xl font-medium lg:text-[2.35rem]">
            Verify your company email
          </h1>
          <p className="md:font-base mb-6 mt-4 text-sm text-[#BCBCBC]">
            An OTP code has been sent to{' '}
            <span className="font-medium text-white">
              {' '}
              {getValues().company_email}.{' '}
            </span>
            Enter the 6-digit OTP below to verify your account{' '}
          </p>
        </header>

        <div className="flex w-full flex-col gap-6 ">
          <div className="confirm-title text-center xs:mb-20">
            <div className="color-black flex max-w-[325.5px] justify-between gap-2 ">
              {verificationCode.map((digit, index) => (
                <input
                  key={index}
                  className="max-w[45px] text-bold aspect-[1/1] w-[calc(25%_-_5px)]  border-[1.5px] border-white/50 bg-white/60 text-center font-clash text-xl text-white outline-none transition-all focus:outline-[1.75px] focus:outline-offset-2 focus:outline-white md:rounded-xl"
                  type="text"
                  maxLength={6}
                  value={digit}
                  onChange={e => handleInputChange(e, index)}
                  onKeyDown={e => handleInputBackspace(e, index)}
                  ref={inputRefs[index]}
                />
              ))}
            </div>
            {tokenError && (
              <span className="mt-1 font-[0.9rem] text-red-500">
                {tokenError == 'expired'
                  ? 'Invalid or expired token'
                  : 'Incomplete token'}
              </span>
            )}
            <aside className="my-4 flex w-full justify-between text-xs text-white">
              <h2>Didn't receive the OTP?</h2>
              {isTimerActive ? (
                <div className="flex items-center gap-2.5">
                  <span className="flex h-7 w-7 items-center justify-center rounded-full bg-[#d9d9d926]">
                    <MessageIcon width={18} height={18} />
                  </span>
                  {formatTime(timeLeft)}
                </div>
              ) : (
                <button
                  type="button"
                  id="resendOTP"
                  className="flex items-center gap-2.5"
                  onClick={resendOTP}
                >
                  <span className="flex h-7 w-7 items-center justify-center rounded-full bg-[#d9d9d926]">
                    <MessageIcon width={18} height={18} />
                  </span>
                  Resend OTP
                  {isRequestingOTP && (
                    <LoaderBtn width={12} height={12} color="white" />
                  )}
                </button>
              )}
            </aside>
          </div>

          <section className="w-fulll flex flex-col gap-4">
            <span className="btn-secondary w-full bg-white/20 px-1.5 py-2.5 text-center text-[0.7rem] text-white">
              If you did not see the email in your inbox, kindly check your spam
              folder
            </span>
            <button
              className="flex w-[full] items-center justify-center rounded-lg bg-white px-3 py-2 text-center text-primary disabled:bg-primary-light "
              type="button"
              onClick={verifyEmail}
            >
              <span className=" text-secondary-dark mx-auto flex-1 text-sm font-medium md:text-[1.125rem]">
                Proceed
              </span>
              <span className="ml-auto flex h-9 w-9 items-center justify-center rounded-full bg-primary">
                <RightUpArrow
                  height={14}
                  width={14}
                  className="translate-y-0.5"
                />
              </span>
            </button>
          </section>
        </div>
      </motion.div>

      {/* ////////////////////////////////////////////////////////////////////// */}
      {/* ////////////////////////////////////////////////////////////////////// */}
      {/* ///////////////                MODALS                  /////////////// */}
      {/* ////////////////////////////////////////////////////////////////////// */}
      {/* ////////////////////////////////////////////////////////////////////// */}
      <LoadingOverlay isOpen={isCompanyCreating || isRecruiterCreating} />
      <Modal
        isModalOpen={isSuccessModalOpen}
        heading="Success"
        closeModal={() => {
          clearStorage();
          router.push('/login/recruiter');
        }}
        allowDismiss={false}
        color="purple"
        body={
          <div className="flex flex-col items-center justify-center gap-4 text-center ">
            <div className="self-center rounded-full bg-primary-light-active p-4">
              <Check width={50} height={50} />
            </div>
            <div>
              <h3 className="text-2xl font-semibold text-primary">
                Successful
              </h3>
              <p className="text-[0.875rem] text-[#8C8CA1]">
                Your{' '}
                <span className="text-header-text">
                  {convertKebabAndSnakeToTitleCase(recruiter_type)}{' '}
                </span>{' '}
                account has been created. Proceed to login
              </p>
            </div>
          </div>
        }
        footer={
          <div className="ml-auto flex items-end gap-4 self-end rounded-[1rem] bg-white px-5 py-3">
            <Button
              onClick={() => {
                clearStorage();
                router.push('/e/dashboard');
              }}
              size="thin"
              className="!px-6"
            >
              Go to Dashboard
            </Button>
          </div>
        }
      />
      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 hover:border-red-950 hover:text-red-950 sm:text-sm md:px-6"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </FormProvider>
  );
};

export default RecruiterSignUpStep4_HRandAgency;
