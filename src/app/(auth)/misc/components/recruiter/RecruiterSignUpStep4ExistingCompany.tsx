'use client';

import { debounce } from 'lodash';
import Image from 'next/image';
import React, { Dispatch, SetStateAction } from 'react';
import { FieldErrors, UseFormRegister, UseFormWatch } from 'react-hook-form';
import { Button, Input, ToolTip } from '@/components/shared';
import { Trash } from '@/components/shared/icons';
import { cn } from '@/utils';
import { Company } from '../../api/getAllCompanies';
import { useRecruiterRegisterDetails } from '../../store';
import { createorRegisterUnderCompanyData } from '../../types/SigupTypes';

interface RecruiterSignUpStep4ExistingCompanyProps {
  selectedCompany: Company | null;
  setIsExistingCompany: Dispatch<SetStateAction<boolean | null>>;
  register: UseFormRegister<createorRegisterUnderCompanyData>;
  goBack: () => void;
  reset: () => void;
  watch: UseFormWatch<createorRegisterUnderCompanyData>;
  errors: FieldErrors<createorRegisterUnderCompanyData>;
}

const RecruiterSignUpStep4ExistingCompany: React.FC<
  RecruiterSignUpStep4ExistingCompanyProps
> = ({
  goBack,
  selectedCompany,
  watch,
  register,
  errors,
  reset,
  setIsExistingCompany,
}) => {
  const { setUserCompanyData } = useRecruiterRegisterDetails();
  React.useEffect(() => {
    const debouncedFn = debounce(setUserCompanyData, 1000);
    const subscription = watch(debouncedFn);
    return () => subscription.unsubscribe();
  }, [watch]);

  return (
    <>
      <Button
        onClick={goBack}
        size="capsule"
        variant="extralight"
        className="max-w-max"
      >
        Back
      </Button>
      {selectedCompany && (
        <article className="relative mb-2  mt-4 flex cursor-pointer select-none items-center gap-3.5 rounded-md bg-white p-2 text-sm outline-none hover:bg-primary-light aria-selected:bg-blue-100/70 aria-selected:text-primary">
          {selectedCompany.logo ? (
            <Image
              src={selectedCompany.logo}
              alt={`${selectedCompany.name} company logo`}
              height={50}
              width={50}
              className="rounded-full"
            />
          ) : (
            <span className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-xs text-white">
              ({`${selectedCompany.name[0]}${selectedCompany.name[1]}`}
              ).toUpperCase()
            </span>
          )}

          <section className="flex flex-col ">
            <span className="text-sm font-medium lg:text-base">
              {selectedCompany.name}
            </span>
            <span className={cn('text-xxs text-[#646464] text-opacity-80')}>
              {selectedCompany.company_domain}
            </span>
          </section>

          <ToolTip
            content="Change company"
            className="ml-auto mr-1 shrink rounded-full bg-red-500"
            align="end"
          >
            <Trash
              fill="red"
              onClick={e => {
                setIsExistingCompany(null);
                reset();
              }}
            />
          </ToolTip>
        </article>
      )}

      <div className="flex flex-col gap-3.5">
        <Input
          {...register('name')}
          placeholder="Enter company name"
          hasError={!!errors.name}
          errorMessage={errors.name?.message}
          variant="transparent"
          label="Enter company name"
          hideLabel
        />
        {/* <Input
            {...register("role")}
            placeholder="Enter role in company"
            hasError={!!errors.role}
            errorMessage={errors.role?.message}
            variant="transparent"
            label="Enter company name"
            hideLabel
        /> */}
        <Input
          {...register('company_email')}
          placeholder="Enter your official company email e.g <EMAIL>"
          hasError={!!errors.company_email}
          errorMessage={errors.company_email?.message}
          variant="transparent"
          label="Enter your official company email e.g <EMAIL>"
          hideLabel
        />
      </div>
    </>
  );
};

export default RecruiterSignUpStep4ExistingCompany;
