"use client"
import React, { useState } from 'react';
import { AnimatePresence } from 'framer-motion';
import { Back } from 'iconsax-react';

import { cn } from '@/utils';
import { useBooleanStateControl } from '@/hooks';
import { Rotate } from '@/components/shared/icons';
import { ConfirmActionModal } from '@/components/shared';

import { RecruiterSignupFormData } from '../../types/SigupTypes';
import RecruiterSignUpStep2 from './RecruiterSignUpStep2';
import RecruiterSignUpStep3 from './RecruiterSignUpStep3';
import RecruiterSignUpStep1 from './RecruiterSignUpStep1';
import RecruiterSignUpStep4_Personal from './RecruiterSignUpStep4_Personal';
import RecruiterSignUpStep4_HRandAgency from './RecruiterSignUpStep4_HRandAgency';
import { useRecruiterRegisterDetails } from '../../store';

 


const RecruiterSignUpFlow = () => {
  const { step, setStep, userPersonalData, moveToNextStep, setUserPersonalData, setRecruiterType, resetStore } = useRecruiterRegisterDetails();

  const chooseRecruiterType = (data: string) => {
    setRecruiterType(data);
    moveToNextStep();
  };
  const handleSignUp = (data: RecruiterSignupFormData) => {
    setUserPersonalData(data);
    moveToNextStep();
  };
  const handleVerification = () => {
    moveToNextStep();
  };
  const {
    state: isConfirmRestartModalOpen,
    setTrue: openConfirmRestartModal,
    setFalse: closeConfirmRestartModal,
  } = useBooleanStateControl()

  const confirmRestartRegistartion = () => {
    resetStore()
    closeConfirmRestartModal()
  }


  return (
    // <AnimatePresence>
    //   <div className='relative z-[3] gap-6 overflow-y-scroll  my-auto  max-lg:h-full w-full no-scrollbar '>
    //     <div className='flex items-center justify-center relative overflow-y-scroll rounded-2xl mt-[10vh] mb-[5vh] lg:max-xl:my-[6vh] xl:my-[6.5vh] w-full '>
    //       <article className='relative flex flex-col items-center justify-center px-6 md:max-lg:px-10 py-6 lg:px-8 rounded-2xl max-h-full overflow-y-scroll overflow-x-hidden backdrop-blur-lg w-[95%] md:max-lg:!w-[75%] lg:max-xl:!w-[85%]   2xl:w-[600px] max-w-[610px] lg:min-h-[85vh]'>

    //         <div className='absolute inset-0 z-[-1] rounded-[1.1875rem] from-[#EDF4FF] from-[-31.2%] to-white/20 to-[24.74%] opacity-40 bg-gradient-358'></div>
    //       </article>
    //     </div>



    //     <ConfirmActionModal
    //       isModalOpen={isConfirmRestartModalOpen}
    //       closeModal={closeConfirmRestartModal}
    //       title="Restart Registration"
    //       confirmFunction={confirmRestartRegistartion}
    //       icon={<Rotate width={44} height={44} fill='white' />}
    //       hideCancelButton
    //     >
    //       <p className='text-[#8C8CA1] text-sm font-normal'>
    //         You are about to <span className='!text-header-text font-bold mr-1'>restart your registration</span>.
    //         {
    //           step !== 2 &&
    //           <>
    //             Please be aware that you will no longer be able to use this email "<span className='text-header-text font-medium mr-1'>{userPersonalData?.email}</span>" for another registration if you confirm this action.
    //           </>
    //         }
    //       </p>
    //     </ConfirmActionModal>
    //   </div>
    // </AnimatePresence>

    <div className="flex flex-col">
      {step === 1 && <RecruiterSignUpStep1 onNext={chooseRecruiterType} />}
      {step === 2 && <RecruiterSignUpStep2 onNext={handleSignUp} recruiter_type={userPersonalData.recruiter_type!!} />}
      {step === 3 && <RecruiterSignUpStep3 user={userPersonalData} onVerified={handleVerification} />}
      {
        step === 4 && userPersonalData.recruiter_type === "PERSONAL_RECRUITER" && <RecruiterSignUpStep4_Personal recruiter_type="PERSONAL_RECRUITER" userData={userPersonalData} />
      }
      {
        step === 4 && userPersonalData.recruiter_type === "HR_PERSONNEL" && <RecruiterSignUpStep4_HRandAgency recruiter_type="HR_PERSONNEL" userData={userPersonalData} />
      }
      {
        step === 4 && userPersonalData.recruiter_type === "RECRUITMENT_AGENCY" && <RecruiterSignUpStep4_HRandAgency recruiter_type="RECRUITMENT_AGENCY" userData={userPersonalData} />
      }
      <button className={cn("sticky top-0 border-white border-[1.7px] text-white flex items-center justify-center gap-2 w-full disabled:bg-primary-light py-2 px-3 rounded-lg text-center disabled:opacity-50", step == 1 && "hidden")} onClick={openConfirmRestartModal}>
        <span className=' text-sm md:text-[0.9rem] '>Start new registration</span>
        <Back fill="white" width={20} height={20} />
      </button>
      <ConfirmActionModal
        isModalOpen={isConfirmRestartModalOpen}
        closeModal={closeConfirmRestartModal}
        title="Restart Registration"
        confirmFunction={confirmRestartRegistartion}
        icon={<Rotate width={44} height={44} fill='white' />}
        hideCancelButton
      >
        <p className='text-[#8C8CA1] text-sm font-normal'>
          You are about to <span className='!text-header-text font-bold mr-1'>restart your registration</span>.
          {
            step !== 2 &&
            <>
              Please be aware that you will no longer be able to use this email "<span className='text-header-text font-medium mr-1'>{userPersonalData?.email}</span>" for another registration if you confirm this action.
            </>
          }
        </p>
      </ConfirmActionModal>
    </div>

  );
};

export default RecruiterSignUpFlow;