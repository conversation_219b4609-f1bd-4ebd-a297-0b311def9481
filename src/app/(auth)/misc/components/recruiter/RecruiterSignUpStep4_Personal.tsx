'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { AxiosError } from 'axios';
import { motion } from 'framer-motion';
import { debounce } from 'lodash';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import ReactTextareaAutosize from 'react-textarea-autosize';
import { z, ZodError } from 'zod';
import {
  Button,
  Checkbox2,
  ErrorModal,
  FileUploader,
  Input,
  LoaderBtn,
  LoadingOverlay,
  Modal,
  Select,
} from '@/components/shared';
import { Check } from '@/components/shared/icons';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { cn, setAccessToken } from '@/utils';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { convertKebabAndSnakeToTitleCase } from '@/utils/strings';
import {
  useCreatePersonalCompany,
  useCreatePersonalRecruiter,
  useSignIn,
} from '../../api';
import { RightUpArrow } from '../../icons';
import { useRecruiterRegisterDetails } from '../../store';
import {
  createCompanyData,
  createorRegisterUnderCompanyData,
  RecruiterSigninFormData,
} from '../../types/SigupTypes';

interface RecruiterSignUpStep4_PersonalProps {
  recruiter_type: string;
  userData: RecruiterSigninFormData;
}

const MAX_FILE_SIZE = 1000000;

const industries = [
  {
    name: 'Technology and Information Technology (IT)',
    value: 'Technology and Information Technology (IT)',
  },
  {
    name: 'Healthcare and Pharmaceuticals',
    value: 'Healthcare and Pharmaceuticals',
  },
  { name: 'Finance and Banking', value: 'Finance and Banking' },
  { name: 'Energy', value: 'Energy' },
  { name: 'Manufacturing', value: 'Manufacturing' },
  { name: 'Retail', value: 'Retail' },
  { name: 'Food and Beverage', value: 'Food and Beverage' },
  {
    name: 'Real Estate and Construction',
    value: 'Real Estate and Construction',
  },
  { name: 'Entertainment and Media', value: 'Entertainment and Media' },
  { name: 'Travel and Tourism', value: 'Travel and Tourism' },
];

const companyForm = z.object({
  name: z
    .string({ required_error: 'Enter company name.' })
    .min(1, { message: 'Name is required' }),
  website: z
    .string({ required_error: 'Enter company website.' })
    .min(1, { message: 'Enter company website.' })
    .optional(),
  industry: z.string(),
  description: z
    .string({ required_error: 'Enter company overview.' })
    .min(1, { message: 'Enter company overview' }),
  logo: z
    .any()
    .nullable()
    .refine(
      file => {
        if (!file) {
          throw ZodError.create([
            {
              path: ['logo'],
              message: 'Please select a file.',
              code: 'custom',
            },
          ]);
        }
        if (!file.type.startsWith('image/')) {
          throw ZodError.create([
            {
              path: ['logo'],
              message: 'Please select an image file.',
              code: 'custom',
            },
          ]);
        }
        return file.size <= MAX_FILE_SIZE;
      },

      {
        message: 'Max image size is 10MB.',
      }
    ),
  address: z.string().optional(),
});

const RecruiterSignUpStep4_Personal: React.FC<
  RecruiterSignUpStep4_PersonalProps
> = ({ userData, recruiter_type }) => {
  const router = useRouter();
  const { mutateAsync: signIn, isLoading: isSigningIn } = useSignIn();
  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();
  const { state: isSuccessModalOpen, setTrue: openSuccessModal } =
    useBooleanStateControl();
  const [isTandCChecked, setTandCChecked] = useState(false);

  const {
    userCompanyData,
    setUserCompanyData,
    moveToNextStep,
    clearStorage,
    resetStore,
  } = useRecruiterRegisterDetails();

  const methods = useForm<createorRegisterUnderCompanyData>({
    defaultValues: {
      name: userCompanyData.name,
      website: userCompanyData.website,
      industry: userCompanyData.industry,
      description: userCompanyData.description,
      logo: userCompanyData.logo,
      team_member_emails: undefined,
    },
    resolver: zodResolver(companyForm),
  });
  const {
    handleSubmit,
    register,
    control,
    formState: { errors, isDirty, isValid },
    setError,
    watch,
    getValues,
    setValue,
    reset,
  } = methods;
  React.useEffect(() => {
    const debouncedFn = debounce(setUserCompanyData, 1000);
    const subscription = watch(debouncedFn);
    return () => subscription.unsubscribe();
  }, [watch]);

  useEffect(() => {
    signIn(
      { email: userData.email, password: userData.password },
      {
        onSuccess: async response => {
          await setAccessToken(response?.data?.data?.access);
        },
      }
    );
  }, []);

  //////////////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////////////
  //////////                          SUBMIT FORM                          /////////////
  //////////////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////////////
  ////submit form and register user
  const { mutate: createPersonalCompany, isLoading: isCompanyCreating } =
    useCreatePersonalCompany();
  const { mutate: createPersonalRecruiter, isLoading: isRecruiterCreating } =
    useCreatePersonalRecruiter();
  const onSubmit = async (formData: createorRegisterUnderCompanyData) => {
    const {
      name,
      address,
      website,
      industry,
      size,
      description,
      logo,
      company_domain,
      team_member_emails,
    } = formData as unknown as createCompanyData;
    const dto = {
      name: name || '',
      address,
      website,
      industry,
      size,
      description,
      logo,
      type_of_company: 'PERSONAL',
    };

    createPersonalCompany(dto, {
      onSuccess: data => {
        const {
          company: { id },
        } = data;
        const dto = {
          company: id,
          role: 'OWNER',
          type_of_recruiter: recruiter_type,
        };

        createPersonalRecruiter(dto, {
          onSuccess: () => {
            signIn(
              { email: userData.email, password: userData.password },
              {
                onSuccess: async response => {
                  await setAccessToken(response?.data?.data?.access);
                },
              }
            );
            // setslide("out");
            openSuccessModal();
          },
          onError(error) {
            const errorMessage = formatAxiosErrorMessage(error as AxiosError);
            openErrorModalWithMessage(errorMessage);
          },
        });
      },
      onError(error) {
        const errorMessage = formatAxiosErrorMessage(error as AxiosError);
        openErrorModalWithMessage(errorMessage);
      },
    });
  };

  return (
    <FormProvider {...methods}>
      <motion.div
        className={cn(
          'my-auto flex h-full min-h-[58vh] w-full  flex-col items-center justify-center py-6 transition-transform duration-300 md:py-[3.5vh] lg:min-h-[72vh] 3xl:min-h-[750px]'
        )}
        initial={{ opacity: 0, x: '110%' }}
        animate={{
          opacity: 1,
          x: '0%',
          transition: { duration: 0.4, type: 'spring', ease: 'linear' },
        }}
        exit={{
          opacity: 0,
          x: '-120%',
          transition: { duration: 0.6, type: 'spring', ease: 'linear' },
        }}
      >
        <header className="flex flex-col self-start ">
          <h1 className=" font-clash text-3xl font-medium text-white md:text-[2.35rem]">
            Lets help set up your{' '}
            <br className="max-xs:hidden lg:max-xl:hidden" /> company profile
            <span className="ml-1 inline-block items-center justify-center rounded-lg bg-white/20 px-4 py-0.5 text-lg">
              2/2
            </span>
          </h1>
          <p className="mb-8 mt-2 text-sm text-[#b5b5c8] lg:text-base">
            Kindly complete your profile setup as this would use showcase you
            better to your potential employees
          </p>
        </header>

        <form
          onSubmit={handleSubmit(onSubmit)}
          className="flex w-full flex-col gap-3.5"
        >
          {/* ///////////////////////////////////////////////////////////////////////////// */}
          {/* ///////////////////////          NEW COMPANY        ///////////////////////// */}
          {/* ///////////////////////////////////////////////////////////////////////////// */}
          {
            <>
              <div className="inputdiv transparent  my-2 grow">
                <label className="sr-only" htmlFor="name">
                  Enter company name
                </label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Enter company name"
                    className={cn(
                      '!bg-white/20 !text-white placeholder:opacity-100',
                      errors.name && 'error',
                      ''
                    )}
                    {...register('name')}
                    id="name"
                  />
                </div>
                {errors.name && (
                  <p className="formerror">{errors.name.message}</p>
                )}
              </div>

              <Select
                {...register('industry')}
                onChange={value => setValue('industry', value)}
                options={industries}
                labelKey="name"
                valueKey="value"
                hasError={!!errors.industry}
                errorMessage={errors.industry?.message}
                placeholder="Select industry"
                className=" w-full"
                triggerColor="white"
              />
              <Input
                {...register('website')}
                placeholder="Enter company website"
                hasError={!!errors.website}
                errorMessage={errors.website?.message}
                variant="transparent"
                label="Enter company website"
                hideLabel
              />
              <Input
                {...register('address')}
                placeholder="Enter company address"
                hasError={!!errors.address}
                errorMessage={errors.address?.message}
                variant="transparent"
                label="Enter company address"
                hideLabel
              />

              <FileUploader
                control={control}
                name="logo"
                label="company logo"
                acceptedFormats={`image/*`}
                acceptedFileExtensions={'png, jpg, gif'}
                maxSize={10}
                color="white"
                className="my-2"
                errors={errors}
              />
              <div className="inputdiv transparent  my-2">
                <ReactTextareaAutosize
                  className={cn(
                    '! resize-none !bg-white/20 backdrop-blur-lg',
                    errors.description && 'error'
                  )}
                  minRows={4}
                  style={{ color: 'white' }}
                  maxRows={20}
                  {...register('description', {
                    required: 'Enter company description',
                  })}
                  placeholder="Kindly describe the product or service your company offers"
                />
                {errors.description && (
                  <p className="formerror">{errors.description.message}</p>
                )}
              </div>
            </>
          }

          <footer className="mt-10">
            <label
              htmlFor="terms-conditions"
              className="flex items-center gap-2 py-5"
            >
              <Checkbox2
                id="terms-conditions"
                checked={isTandCChecked}
                onCheckedChange={() => setTandCChecked(!isTandCChecked)}
                className="border-white"
                checkClassName="!text-white"
              />
              <span className="text-sm text-white/60">
                By registering and signing in, you agree to our{' '}
                <Link
                  className="ml-0.5 text-[0.78rem] font-semibold text-white hover:underline"
                  href={'/terms'}
                >
                  Terms & Conditions
                </Link>
              </span>
            </label>

            <button
              className="mt-1.5 flex w-full items-center justify-center rounded-lg bg-white px-3 py-1.5 text-center text-primary disabled:bg-white/40 "
              type="submit"
            >
              <span className="text-secondary-dark mx-auto flex flex-1 items-center justify-center gap-2 text-sm font-medium md:text-base">
                Submit Details {isCompanyCreating && <LoaderBtn />}
              </span>
              <span className="ml-auto flex h-8 w-8 items-center justify-center rounded-full bg-primary">
                <RightUpArrow
                  height={14}
                  width={14}
                  className="translate-y-0.5"
                />
              </span>
            </button>
          </footer>
        </form>
      </motion.div>

      {/* ////////////////////////////////////////////////////////////////////// */}
      {/* ////////////////////////////////////////////////////////////////////// */}
      {/* ///////////////                MODALS                  /////////////// */}
      {/* ////////////////////////////////////////////////////////////////////// */}
      {/* ////////////////////////////////////////////////////////////////////// */}
      <LoadingOverlay isOpen={isCompanyCreating || isRecruiterCreating} />
      <Modal
        isModalOpen={isSuccessModalOpen}
        heading="Success"
        closeModal={() => {
          resetStore();
          router.push('/login/recruiter');
        }}
        allowDismiss={false}
        color="purple"
        body={
          <div className="flex flex-col items-center justify-center gap-4 ">
            <div className="self-center rounded-full bg-primary-light-active p-4">
              <Check width={50} height={50} />
            </div>
            <div>
              <h3 className="text-xl font-medium text-primary">Successful</h3>
              <p className="text-[0.875rem] text-[#8C8CA1]">
                Your {convertKebabAndSnakeToTitleCase(recruiter_type)} account
                has been created. Proceed to login
              </p>
            </div>
          </div>
        }
        footer={
          <div className="ml-auto flex items-end gap-4 self-end rounded-[1rem] bg-white p-5">
            <Button
              onClick={() => {
                resetStore();
                router.push('/e/dashboard');
              }}
              size="thin"
              className="!px-6"
            >
              Go to Dashboard
            </Button>
          </div>
        }
      />

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 hover:border-red-950 hover:text-red-950 sm:text-sm md:px-6"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </FormProvider>
  );
};

export default RecruiterSignUpStep4_Personal;
