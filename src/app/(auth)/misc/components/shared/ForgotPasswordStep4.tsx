"use client"

import React, { useEffect, useState } from 'react';
import { LinkButton } from '@/components/shared';
import { Lock } from '../../icons';



const ForgotPasswordStep4= () => {
    const [slide, setslide] = useState<string>("stagnant")
    useEffect(() => {
        const timer = setTimeout(() => {
            setslide("in");
        }, 15);
        return () => clearTimeout(timer);
    }, []);


    return (
        <div className={`flex flex-col justify-center py-[10.5vh] md:px-[1.5vw] transition-transform  ${slide == "stagnant" ? "translate-x-[110%]" : slide == "in" ? "translate-x-0 duration-300" : slide == "out" ? "translate-x-[-120%] duration-500" : ""} `}>


            <header className='flex flex-col gap-2 justify-center items-center text-center text-white '>
                <section className='bg-white rounded-full p-6 md:p-8 mb-4'>
                    <Lock height={60} width={60} stroke='#755AE2' />
                </section>

                <section className='space-y-2'>
                    <h1 className=' text-3xl md:text-[2.35rem] font-clash font-semibold leading-[41.82px]'>
                        Password  Reset <br/> Successful 👍
                    </h1>
                    <p >
                        Your have successfully reset your password.<br/> Kindly use your new password to login.
                    </p>
                </section>
            </header>

            <LinkButton className='text-center text-sm lg:text-base justify-center mt-16 xs:[@media(min-height:850px)]:mt-[10vh] md:w-[80%] self-center' href='/login' variant='white'>
                Login
            </LinkButton>
        </div>
    );
};

export default ForgotPasswordStep4;