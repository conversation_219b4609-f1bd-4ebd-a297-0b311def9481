
import { AnimatePresence, motion } from 'framer-motion';

import { cn } from '@/utils';

import { Co<PERSON>, GetLinkLogo, Home, MultipleUsers, Plane, Sheet } from '../../icons';
import Link from 'next/link';
import { AuthSliderContent } from '../../types/SigupTypes';
import AuthSlideShow from './AuthSlideShow';
const AuthLeftSide = ({ slideShowContent }: {
    slideShowContent: AuthSliderContent[]
}) => {
    return (
        <div className="flex flex-col relative mx-auto w-full max-lg:h-[16vh] lg:shrink-0 overflow-hidden text-white lg:h-screen-small lg:max-w-none lg:basis-1/2 lg:pb-0 lg:[@media(max-height:520px)]:overflow-y-auto relative">
            <header className="flex top-0 left-0 z-10 fixed lg:relative w-full items-center justify-between py-4 px-6 md:py-6 md:px-10">
                <div className='flex items-center gap-3'>
                    <GetLinkLogo stroke='white' />
                    <h3 className='font-grotesk text-xl font-medium'>Getlinked</h3>
                </div>

                <Link
                    className="flex items-center gap-2 px-4 py-2 text-base rounded-full bg-white/80 hover:bg-white text-secondary-darker transition-all"
                    href="/"
                >
                    <Home />
                    <span className='max-md:hidden text-[#3C1356]'>
                        Home
                    </span>
                </Link>
            </header>

            <AuthSlideShow content={slideShowContent} />

            <img src="/images/squiggle.svg" alt="" className='absolute bottom-0 left-0 w-[70%]' />
            <img src="/images/3d-shapes2.png" alt="" className='absolute top-[30%] -left-4 w-40 ' />

        </div>
    );
}


export default AuthLeftSide;
