'use client';

import * as React from 'react';
import Link from 'next/link';
import { AnimatePresence, motion } from 'framer-motion';

import { cn } from '@/utils';

import { Copy, GetLinkLogo, Home, MultipleUsers, Plane, Sheet } from '../../icons';





function SharedSlideshow() {
    const [currentSharedSlideshowIndex, setCurrentSharedSlideshowIndex] = React.useState(0);

    const SharedSlideshowContent = [
        {
            heading: 'Parse Bulk CVs ',
            text: 'Sift through bulk CVs with ease and effortlessly identify and shortlist candidates for your job openings.',
            icon: <Copy />
        },
        {
            heading: 'Create assessments',
            text: 'Access well curated questions and send out assessment invite to candidates to get the best talent for every position',
            icon: <Sheet />
        },
        {
            heading: 'Prepare for interviews',
            text: 'Get tools that help you adequately prepare and ace your next job interview!.',
            icon: <MultipleUsers />
        },
        {
            heading: 'Manage job applications',
            text: 'Efficiently track and manage your job applications, ensuring you stay organized throughout the hiring process.',
            icon: <Copy />
        },

    ];

    React.useEffect(() => {
        const interval = setInterval(() => {
            setCurrentSharedSlideshowIndex((prev) => (prev + 1) % SharedSlideshowContent.length);
        }, 3500);
        return () => clearInterval(interval);
    }, []);





    return (
        <div className="flex flex-col relative mx-auto w-full max-lg:h-[16vh] lg:shrink-0 overflow-hidden text-white lg:h-screen-small lg:max-w-none lg:basis-1/2 lg:pb-0 lg:[@media(max-height:520px)]:overflow-y-auto">
            <header className="flex top-0 left-0 z-[5] fixed lg:relative w-full items-center justify-between  py-4 px-6 md:py-6 md:px-10">
                <div className='flex items-center gap-3'>
                    <GetLinkLogo stroke='white' />
                    <h3 className='font-grotesk text-xl font-medium'>Getlinked</h3>
                </div>

                <Link
                    className="flex items-center gap-2 px-4 py-2 text-base rounded-full bg-white/80 hover:bg-white text-secondary-darker transition-all"
                    href="/"
                >
                    <Home />
                    <span className='max-md:hidden'>
                        Home
                    </span>
                </Link>
            </header>

            <div className="h-full lg:h-[calc(100vh_-_3rem)] overflow-hidden">
                <AnimatePresence>
                    {SharedSlideshowContent.map(({ heading, text, icon }, index) => (
                        <motion.div
                            key={heading}
                            initial={{
                                opacity: 0, y: 10,
                                zIndex: index === 0 ? 1
                                    : index === 1 ? 2
                                        : index === 2 ? 3
                                            : index === 3 ? 4 : ""
                            }}
                            animate={{
                                opacity: index === currentSharedSlideshowIndex ? 1 : 0.15,
                                y: index === currentSharedSlideshowIndex ? 0 : 10,
                                backgroundColor: index === currentSharedSlideshowIndex ? '#0D0114'
                                    : index === 0 ? '#241131'
                                        : index === 1 ? '#2c163b'
                                            : index === 2 ? '#331d42'
                                                : index === 3 ? '#1c0c26' : "",
                                zIndex: index === currentSharedSlideshowIndex ? 5 : ""
                            }}
                            exit={{ opacity: 0.5, y: -10 }}
                            transition={{ duration: 1, delay: 0.5 }}
                            className={cn(
                                'flex w-full h-full px-4 py-6 lg:flex lg:flex-col lg:justify-center lg:p-8 xl:max-2xl:px-[4vw] 2xl:px-[6vw] lg:[@media(min-height:520px)]:h-1/4',
                                index !== currentSharedSlideshowIndex && 'hidden lg:flex',
                            )}
                        >
                            <div className={cn('w-full flex justify-between max-lg:self-center my-auto')}>
                                <div>
                                    <h2
                                        className={cn(
                                            'mb-3 2xl:mb-4 text-xl font-bold leading-[78.5%] lg:text-[1.7rem] 2xl:text-4xl',
                                        )}
                                    >
                                        {heading}
                                    </h2>
                                    <p className="leading-[123.5%] max-w-[15rem] md:max-lg:max-w-[18rem] lg:max-xl:max-w-[20rem] xl:max-w-[26.25rem] font-clash max-md:text-xs md:max-2xl:text-sm">{text}</p>
                                </div>

                                <div className={cn('flex items-center justify-center w-[5rem] h-[5rem] md:w-24 md:h-24 max-md:scale-75 rounded-full ml-auto bg-white/[0.07]', index !== currentSharedSlideshowIndex ? "" : "")}>
                                    {icon}
                                </div>
                            </div>
                        </motion.div>
                    ))}
                </AnimatePresence>
            </div>
        </div>
    );
}

export default SharedSlideshow