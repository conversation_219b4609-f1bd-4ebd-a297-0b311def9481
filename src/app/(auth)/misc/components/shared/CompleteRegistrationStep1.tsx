"use client"

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';

import { cn } from '@/utils';
import { useUser } from '@/lib/contexts/UserContext';

import { useRecruiterRegisterDetails, useTalentRegisterDetails } from '../../store';
import { HRRecruiter, PersonalRecruiter, RightUpArrow } from '../../icons';
import { useBooleanStateControl } from '@/hooks';
import { ConfirmActionModal, LinkButton } from '@/components/shared';
import { Back } from 'iconsax-react';



const CompleteRegistrationStep1 = () => {
    const router = useRouter()
    const [currentType, setcurrentType] = useState("PERSONAL_RECRUITER")
    const [currentRecruiterType, setcurrentRecruiterType] = useState("PERSONAL_RECRUITER")
    const types = [
        {
            value: "TALENT",
            title: "Talent",
            desc: "Gain access to exciting career openings tailored to your unique skills and aspirations, with tools to showcase your talents, review your resume and prepare for interviews.",
            icon: <PersonalRecruiter />
        },
        {
            value: "RECRUITER",
            title: "Recruiter",
            desc: "Gain access to a vast talent pool of top professionals, coupled with world-class tools designed to streamline your entire recruitment process.",
            icon: <HRRecruiter />
        },
    ]

    const recruiterTypes = [
        {
            value: "PERSONAL_RECRUITER",
            title: "Personal Recruiter",
        },
        {
            value: "HR_PERSONNEL",
            title: "HR Personnel",
        },
        {
            value: "RECRUITMENT_AGENCY",
            title: "Recruitment Agency",
        },

    ]


    const { user } = useUser()
    const { setStep: setTalentStep, setUserData: setTalentData, userData: talentData, resetStore: resetTalentStore } = useTalentRegisterDetails()
    const { setStep: setRecruiterStep, userPersonalData: recruiterData, setUserPersonalData: setRecruiterData, resetStore: resetRecruiterStore } = useRecruiterRegisterDetails()

    const handleProceed = async () => {
        if (user?.user_type && (user?.user_type === "TALENT" || user?.user_type === "RECRUITER")) {
            toast.error("You are not authorized to perform this action")
            router.push('/')
            return
        }
        if (currentType === "PERSONAL_RECRUITER") {
            toast.error("Please select an account type")
            return
        } else if (currentType === "RECRUITER") {
            await continueRecruiterRegistration()
        } else if (currentType === "TALENT") {
            await continueTalentRegistration()
        }
    };
    const continueRecruiterRegistration = async () => {
        setRecruiterData({ ...recruiterData, user })
        if (user?.email_verified) {
            setRecruiterStep(4)
        } else {
            setRecruiterStep(3)
        }
        router.push('/register/recruiter')
    }
    const continueTalentRegistration = async () => {
        setTalentData({ ...talentData, user })
        if (user?.email_verified) {
            setTalentStep(3)
        } else {
            setTalentStep(2)
        }
        router.push('/register/talent')
    }

    const startNewRegistration = () => {
        resetTalentStore()
        resetRecruiterStore()
        router.push('/register')
    }



    return (
        <motion.div className={cn("flex flex-col items-center justify-center w-full  min-h-[58vh] lg:min-h-[72vh] 3xl:min-h-[750px] h-full my-auto transition-transform duration-300 text-white",)}

        >
            <header className='flex flex-col gap-1.5 self-start mb-8'>
                <h1 className='text-3xl lg:text-[2.35rem] text-left font-clash font-semibold'>Continue your registration</h1>
                <p className="text-[#BCBCBC] text-sm md:font-base">You didn&apos;t complete your registration process.<br className='md:hidden' /> Continue by clicking on the type of account you want to create.</p>
            </header>

            <section className='space-y-4'>
                {
                    types.map((type, index) => {
                        const { title, desc, icon, value } = type
                        return (
                            <article className={
                                cn("relative flex max-sm:flex-col items-stretch gap-4 bg-white/20 backdrop-blur-lg hover:bg-primary/30  p-4 rounded-lg max-w-full md:min-h-[120px] cursor-pointer transition-colors duration-300",
                                    currentType == value && "bg-primary/50"
                                )}
                                onClick={() => setcurrentType(value)}
                                key={index}
                            >
                                <div className='bg-white rounded-lg flex items-center justify-center w-12 max-sm:h-12 sm:w-24 px-2 sm:px-4 md:min-w-[75px]'>
                                    {icon}
                                </div>
                                <div className='text-white '>
                                    <h2 className='text-lg font-bold'>{title}</h2>
                                    <p className='text-sm !font-normal leading-4'>{desc}</p>
                                </div>
                                <div>
                                    <div className={cn('max-sm:absolute flex items-center justify-center right-6 top-6 w-5 h-5 border-[2.5px] border-white rounded-full')}>
                                        {
                                            currentType == value &&
                                            <span className='block w-[80%] h-[80%] bg-white rounded-full'></span>
                                        }
                                    </div>
                                </div>
                            </article>
                        )
                    })
                }
                {
                    currentType === "RECRUITER" &&
                    <div className='flex flex-col gap-2.5'>
                        {
                            recruiterTypes.map((type, index) => {
                                const { title, value } = type
                                return (
                                    <article className={
                                        cn("relative flex items-center justify-between gap-4 bg-white/20 backdrop-blur-lg hover:bg-primary/30  p-4 rounded-lg max-w-full  cursor-pointer transition-colors duration-300",
                                            currentType == value && "bg-primary/50"
                                        )}
                                        onClick={() => setcurrentRecruiterType(value)}
                                        key={index}
                                    >

                                        <div className='text-white '>
                                            <h2 className='text-base font-medium'>{title}</h2>
                                        </div>
                                        <div className={cn('max-sm:absolute flex items-center justify-center right-6 top-6 w-5 h-5 border-[2.5px] border-white rounded-full')}>
                                            {
                                                currentRecruiterType == value &&
                                                <span className='block w-[80%] h-[80%] bg-white rounded-full'></span>
                                            }
                                        </div>
                                    </article>
                                )
                            })
                        }
                    </div>
                }
            </section>

            <section className='flex flex-col gap-2 w-full mt-[10vh]'>
                <button onClick={handleProceed} className="bg-white flex items-center justify-center w-[full] disabled:bg-primary-light py-1.5 px-3 rounded-lg text-primary text-center " type="submit">
                    <span className=' text-sm md:text-base text-secondary-dark font-medium mx-auto flex-1'>Proceed</span>
                    <span className='flex items-center justify-center ml-auto bg-primary rounded-full w-8 h-8'><RightUpArrow height={14} width={14} className='translate-y-0.5' /></span>
                </button>

                <LinkButton variant="unstyled" href="/register"
                    className={cn("sticky top-0 border-white border-[1.7px] text-white flex items-center justify-center gap-2 w-full disabled:bg-primary-light py-2 px-3 rounded-lg text-center disabled:opacity-50")}
                    onClick={startNewRegistration}>
                    <span className=' text-sm md:text-[0.9rem] '>Start new registration</span>
                    <Back fill="white" width={20} height={20} />
                </LinkButton>
            </section>



        </motion.div>
    );
};

export default CompleteRegistrationStep1
    ;
