"use client"

import React, { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { z } from 'zod';
import { <PERSON><PERSON><PERSON><PERSON>, SubmitHandler, useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';

import { cn } from '@/utils';
import { Select, LoaderBtn, Input } from '@/components/shared';
import { zodResolver } from '@hookform/resolvers/zod';
;
import { Lock, RightUpArrow, ViewIcon } from '../../icons';
import { ForgotPasswordFormData } from '../../types/SigupTypes';
import { useChangePassword } from '../../api';


interface ForgotPasswordStep3Props {
    onDetailsSubmit: (details: ForgotPasswordFormData) => void;
    user: ForgotPasswordFormData;
    back: () => void;
    setTokenError: Dispatch<SetStateAction<boolean>>;
}


const ForgotPasswordStep3Resolver = z.object({
    password: z.string({ required_error: 'Enter password.' }).min(8, "Password must be at least 8 characters").regex(/(?=.*\d)/, "Password must contain a number").regex(/(?=.*[A-Z])/, "Password must contain an uppercase letter")
        .regex(/(?=.*[a-z])/, "Password must contain a lowercase letter").regex(/(?=.*[@#$%^&+=])/, "Password must contain a special character (@#$%^&+=)"),
    confirm_password: z.string({ required_error: 'Enter first name.' }).min(8, { message: 'Password mudt be at least 8 characters.' }),
});




type forgotPasswordFormData = {
    password: string
    confirm_password: string
}

const ForgotPasswordStep3: React.FC<ForgotPasswordStep3Props> = ({ user, onDetailsSubmit, back, setTokenError }) => {
    const { handleSubmit, register, formState: { errors, isDirty, isValid }, control, watch, setError } = useForm<forgotPasswordFormData>({ resolver: zodResolver(ForgotPasswordStep3Resolver) });;
    const [slide, setslide] = useState<string>("stagnant")
    const [show, setShow] = useState(false)
    const [show2, setShow2] = useState(false)
    const { mutate: changePassword, isLoading } = useChangePassword()

    useEffect(() => {
        const timer = setTimeout(() => {
            setslide("in");
        }, 15);
        return () => clearTimeout(timer);
    }, []);





    const onSubmit: SubmitHandler<FieldValues> = async (data) => {
        if (data.password !== data.confirm_password) {
            setError('confirm_password', { type: 'validate', message: 'Passwords do not match' });
            return;
        }

        changePassword({ ...user, new_password: data.password, confirm_password: data.confirm_password }, {
            onSuccess: () => {
                setslide("out")
                onDetailsSubmit({ ...user, new_password: data.password, confirm_password: data.confirm_password })
            },
            onError: (error: any) => {
                toast.error(error?.response?.data.error, { duration: 10000 })
                if (error.response?.data?.data?.message == "Invalid or expired OTP.") {
                    setTokenError(true)
                    back()
                    toast.error("Invalid or expired OTP.")
                }
            }
        })

    };





    return (
        <form onSubmit={handleSubmit(onSubmit)} className={`flex flex-col py-[10.5vh] md:px-[1.5vw] w-full transition-transform  ${slide == "stagnant" ? "translate-x-[110%]" : slide == "in" ? "translate-x-0 duration-300" : slide == "out" ? "translate-x-[-120%] duration-500" : ""} `}>

            <header className='flex flex-col self-start '>
                <h1 className=' text-3xl md:text-[2.35rem] text-white font-clash font-semibold'>
                    Create new password
                </h1>
                <p className="text-white text-sm md:font-base mt-1.5 mb-6 max-w-[35ch] font-normal">
                    Your new password must be different from the one  previously used.
                </p>
            </header>


            <div className=' !my-2 withicon'>
                <Input
                    label="New Password"
                    variant="transparent"
                    type="password"
                    placeholder="New password"
                    {...register('password')} id="password"
                    hasError={!!errors.password}
                    errorMessage={errors.password?.message}
                />
            </div>

            <div className='!y-2 withicon'>
                <Input
                    label="Confirm Password"
                    variant="transparent"
                    type="password"
                    placeholder="Confirm password"
                    {...register('confirm_password')} id="confirm_password"
                    hasError={!!errors.confirm_password}
                    errorMessage={errors.confirm_password?.message}
                />
            </div>



            <button className="flex items-center justify-center w-full mt-16 xs:[@media(min-height:850px)]:mt-[8.5vh] bg-white disabled:bg-primary-light py-1.5 px-3 rounded-lg text-primary text-center" type="submit">
                <span className='flex items-center justify-center flex-1 grow gap-x-2 text-sm md:text-base text-secondary-dark font-medium mx-auto'>Change Password {isLoading && <LoaderBtn />}</span>
                <span className='flex items-center shrink justify-center ml-auto bg-primary rounded-full w-8 h-8'><RightUpArrow height={14} width={14} className='translate-y-0.5' /></span>
            </button>

        </form>
    );
};

export default ForgotPasswordStep3;
