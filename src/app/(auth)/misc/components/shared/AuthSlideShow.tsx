import React, { useEffect } from "react";
import { AuthSliderContent } from "../../types/SigupTypes";
import { ArrowLef<PERSON>, ArrowRight } from "lucide-react";




const AuthSlideShow = ({ content }: { content: AuthSliderContent[] }) => {
    const [activeIndex, setActiveIndex] = React.useState(0);

    const prev = () => {
        if (activeIndex == 0) return;
        setActiveIndex(activeIndex - 1)
    }

    const next = () => {
        if (activeIndex == (content.length - 1)) {
            setActiveIndex(0)
            return;
        };
        setActiveIndex(activeIndex + 1)
    }

    useEffect(() => {
        const interval = setInterval(() => {
            next()
        }, 3000);
        return () => clearInterval(interval);
    }, [activeIndex])

    return (
        <div className="h-full hidden md:block lg:h-[calc(100vh_-_3rem)] overflow-hidden">
            <div className="flex items-center justify-center h-full">

                <div className="gap-5 flex flex-col items-end max-w-[500px] w-[70%]">
                    {/* SLIDER COVER */}
                    <div className="px-6 py-10 rounded-2xl relative gap-8 flex flex-col">
                        <div className='absolute inset-0  rounded-[1.1875rem] from-[#EDF4FF] from-[-35.2%] to-white/20  opacity-30 bg-gradient-358 backdrop-blur-lg'></div>
                        {/* SLIDER IMAGES */}
                        <div className="flex items-center justify-center relative mt-8">
                            {content.map((c, i) => {
                                const isActive = i === activeIndex;
                                const indexFromTop = i - activeIndex;

                                // Skip rendering if too far behind
                                if (indexFromTop < -3) return null;

                                const scale = 1 - Math.abs(indexFromTop) * 0.03; // 1, 0.98, 0.96...
                                const bottomOffset = Math.abs(indexFromTop) * 15; // 0px, 8px, 16px...
                                const zIndex = 20 - Math.abs(indexFromTop);
                                return (
                                    <div
                                        key={i}
                                        className={`
                                        image-cover border border-[#502B673D] shadow-sm w-full h-[250px] overflow-hidden rounded-2xl absolute transition-all duration-1000 ease-in-out
                                        ${isActive ? "z-20 opacity-100 scale-100  relative" : ""}
                                       
                                    `}
                                        style={{
                                            transform: `scale(${scale})`,
                                            bottom: `${bottomOffset}px`,
                                            zIndex: zIndex,
                                        }}

                                    >
                                        <img
                                            src={c.imageURL}
                                            alt={c.heading}
                                            className="w-full h-full object-cover object-top"
                                        />
                                    </div>
                                );
                            })}
                        </div>

                        {/* SLIDER CONTENT */}

                        <div className="relative">
                            {content.map(({ icon: Icon, heading, text }, i) => {
                                const isActive = i === activeIndex;
                                return (
                                    <div key={i} className={`flex items-start gap-5  transition-all duration-1000 ease-in-out  ${isActive ? " opacity-100  relative" : "opacity-0 absolute top-0"}`}>

                                        <div className="h-15 w-15 aspect-square p-3  rounded-full flex items-center justify-center bg-[#6B577D]">
                                            <Icon />
                                        </div>
                                        <div>
                                            <h3 className=" text-xl font-medium">{heading}</h3>
                                            <p className="text-white/70 text-sm">{text}</p>
                                        </div>


                                    </div>

                                );
                            })}
                        </div>

                    </div>
                    <div className="flex gap-2">
                        <button onClick={prev} className="bg-[#22112D] flex items-center justify-center  text-secondary-darker transition-all h-8 w-8 aspect-square rounded-full">
                            <ArrowLeft size={20} />
                        </button>
                        <button onClick={next} className="bg-[#22112D] flex items-center justify-center  text-secondary-darker transition-all h-8 w-8 aspect-square rounded-full">
                            <ArrowRight size={20} />
                        </button>
                    </div>

                </div>
            </div >
        </div >
    );
}

export default AuthSlideShow;