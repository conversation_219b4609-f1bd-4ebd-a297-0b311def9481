'use client';

import React, { useState } from 'react';
import { ForgotPasswordFormData } from '../../types/SigupTypes';
import ForgotPasswordStep1 from './ForgotPasswordStep1';
import ForgotPasswordStep2 from './ForgotPasswordStep2';
import ForgotPasswordStep3 from './ForgotPasswordStep3';
import ForgotPasswordStep4 from './ForgotPasswordStep4';

const ForgotPasswordFlow = () => {
  const [step, setStep] = useState(1);
  const [userData, setUserData] = useState<ForgotPasswordFormData>({
    otp: '',
    email: '',
    new_password: '',
    confirm_password: '',
  });
  const [tokenError, setTokenError] = useState(false);
 
  const moveToNextStep = () => {
    setStep(step + 1);
  };
  const moveToPreviousStep = () => {
    setStep(step - 1);
  };

  const completeStep = (data: ForgotPasswordFormData) => {
    setUserData(data);
    moveToNextStep();
  };

  return (
    <>
      {step == 1 && (
        <ForgotPasswordStep1
          user={userData}
          onDetailsSubmit={completeStep}
        />
      )}
      {step == 2 && (
        <ForgotPasswordStep2
          user={userData}
          onDetailsSubmit={completeStep}
          back={moveToPreviousStep}
          tokenError={tokenError}
        />
      )}
      {step == 3 && (
        <ForgotPasswordStep3
          user={userData}
          onDetailsSubmit={completeStep}
          back={moveToPreviousStep}
          setTokenError={setTokenError}
        />
      )}
      {step == 4 && <ForgotPasswordStep4 />}
    </>
  );
};

export default ForgotPasswordFlow;
