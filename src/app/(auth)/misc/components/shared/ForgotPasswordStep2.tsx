'use client';

import axios from 'axios';
import React, { FormEvent, useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { LoaderBtn } from '@/components/shared';
import { cn } from '@/utils';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { useRequestPasswordResetOTP } from '../../api';
import { useVerifyTokn } from '../../api/postPasswordChange';
import { InfoStar, MessageIcon, RightUpArrow } from '../../icons';
import { ForgotPasswordFormData } from '../../types/SigupTypes';

type ForgotPasswordStep2Props = {
  onDetailsSubmit: (details: ForgotPasswordFormData) => void;
  user: ForgotPasswordFormData;
  tokenError: boolean;
  back: () => void;
};

const ForgotPasswordStep2: React.FC<ForgotPasswordStep2Props> = ({
  user,
  onDetailsSubmit,
  tokenError: InvalidToken,
  back,
}) => {
  const [tokenError, setTokenError] = useState(InvalidToken);
  const [verificationCode, setVerificationCode] = useState<string[]>([
    '',
    '',
    '',
    '',
    '',
    '',
  ]);
  const inputRefs = Array(6)
    .fill(0)
    .map(() => React.createRef<HTMLInputElement>());
  const [slide, setslide] = useState<string>(InvalidToken ? 'out' : 'stagnant');
  const [isTimerActive, setIsTimerActive] = useState(true);
  const [timeLeft, setTimeLeft] = useState(600);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (isTimerActive && timeLeft > 0) {
      timer = setInterval(() => {
        setTimeLeft(prevTime => prevTime - 1);
      }, 1000);
    } else if (timeLeft === 0) {
      setIsTimerActive(false);
    }

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [timeLeft, isTimerActive]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };
  const { mutate: requestOTP, isLoading: isRequestingOTP } =
    useRequestPasswordResetOTP();
  const getOTP = async () => {
    requestOTP(
      { email: user?.email || '' },
      {
        onSuccess() {
          setTimeLeft(600);
          setIsTimerActive(true);
        },
        onError(error) {
          const errorMessage = formatAxiosErrorMessage(error as any);
          toast.error(errorMessage);
        },
      }
    );
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setslide('in');
    }, 30);
    return () => clearTimeout(timer);
  }, []);
  useEffect(() => {
    const token = verificationCode.join('');
    if (token.length === 6) {
      // onDetailsSubmit({ ...user, otp: token });
      handleTokenVerification();
    }
  }, [verificationCode]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    const newValue = e.target.value;

    if (/^\d*$/.test(newValue)) {
      const newVerificationCode = [...verificationCode];

      if (newValue.length <= 1) {
        newVerificationCode[index] = newValue;
        setVerificationCode(newVerificationCode);

        if (newValue && index < 5) {
          inputRefs[index + 1].current?.focus();
        }
      } else if (newValue.length > 1) {
        const newValuesArrays = newValue.split('');

        newValuesArrays.forEach((value, i) => {
          if (newVerificationCode[index + i] !== undefined) {
            newVerificationCode[index + i] = value;
          }
        });

        setVerificationCode(newVerificationCode);

        if (index + newValuesArrays.length < 5) {
          inputRefs[index + newValuesArrays.length].current?.focus();
        } else {
          inputRefs[5].current?.focus();
        }
      }
    }
  };

  const handleInputBackspace = (
    e: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    if (e.key === 'Backspace' && !verificationCode[index] && index > 0) {
      const newVerificationCode = [...verificationCode];
      newVerificationCode[index - 1] = '';
      setVerificationCode(newVerificationCode);
      inputRefs[index - 1].current?.focus();
    }
  };

  const { mutate: verifytoken, isLoading: isTokenVerifying } = useVerifyTokn();

  const handleFormSubmit = async (e: FormEvent<HTMLFormElement>) => {
    if (e) {
      e.preventDefault();
    }
    const token = verificationCode.join('');
    if (token.length < 6) {
      // console.log('TOKENERROR');

      setTokenError(true);
    } else {
      //   console.log('NEXT');
      handleTokenVerification();
    }
  };

  const handleTokenVerification = () => {
    const token = verificationCode.join('');

    verifytoken(
      { recipient: user.email, otp: token },
      {
        onSuccess: response => {
          if (response.success) {
            toast.success(response.message);

            onDetailsSubmit({ ...user, otp: token });
          }
        },
        onError: (error: any) => {
          if (
            error.response?.data?.error
              .toLowerCase()
              .match('invalid or expired OTP'.toLowerCase())
          ) {
            setTokenError(true);
            toast.error('Invalid or expired OTP.');
          }
        },
      }
    );
  };

  return (
    <div
      className={cn(
        'my-auto flex h-full min-h-[58vh] w-full  flex-col items-center justify-center text-white transition-transform duration-300 sm:px-5 lg:min-h-[72vh] 3xl:min-h-[750px]',
        slide == 'stagnant'
          ? 'translate-x-[110%]'
          : slide == 'in'
            ? 'translate-x-0'
            : slide == 'out'
              ? 'translate-x-[-120%]'
              : ''
      )}
    >
      <header className="flex flex-col self-start">
        <h1 className="text-left font-clash text-3xl font-semibold lg:text-[2.35rem]">
          Verify your email
        </h1>
        <p className="md:font-base mb-6 text-sm text-[#BCBCBC]">
          An OTP code has been sent to{' '}
          <span className="font-medium text-white"> {user.email}.</span>
          Enter the 6-digit OTP below to verify your accounts
        </p>
      </header>

      <form onSubmit={handleFormSubmit} className="flex w-full flex-col gap-6 ">
        <div className="confirm-title text-center xs:mb-20">
          <div className="color-black flex max-w-[325.5px] justify-between gap-2 ">
            {verificationCode.map((digit, index) => (
              <input
                key={index}
                className="text-bold aspect-[1/1] w-[calc(25%_-_5px)] max-w-[45px] rounded-lg  border-[1.5px] border-white/50 bg-white/60 text-center font-clash text-xl text-white outline-none transition-all focus:outline-[1.75px] focus:outline-offset-2 focus:outline-white md:rounded-xl"
                type="text"
                maxLength={6}
                value={digit}
                onChange={e => handleInputChange(e, index)}
                onKeyDown={e => handleInputBackspace(e, index)}
                ref={inputRefs[index]}
              />
            ))}
          </div>
          {tokenError && (
            <span className="mt-1 block text-left font-[0.9rem] text-red-500">
              Invalid or expired token
            </span>
          )}
          <aside className="my-4 flex w-full justify-between text-xs text-white">
            <h2>Didn't receive the OTP?</h2>
            {isTimerActive ? (
              <div className="flex items-center gap-2.5">
                <span className="flex h-7 w-7 items-center justify-center rounded-full bg-[#d9d9d926]">
                  <MessageIcon width={18} height={18} />
                </span>
                {formatTime(timeLeft)}
              </div>
            ) : (
              <button
                type="button"
                id="resendOTP"
                className="flex items-center gap-2.5"
                onClick={getOTP}
              >
                <span className="flex h-7 w-7 items-center justify-center rounded-full bg-[#d9d9d926]">
                  <MessageIcon width={18} height={18} />
                </span>
                Resend OTP
                {isRequestingOTP && (
                  <LoaderBtn width={12} height={12} color="white" />
                )}
              </button>
            )}
          </aside>
        </div>

        <section className="w-fulll flex flex-col gap-4">
          <span className="btn-secondary w-full bg-white/50 px-1.5 py-3 text-center text-[0.7rem] text-white">
            If you did not see the email in your inbox, kindly check your spam
            folder
          </span>
          <button
            className="flex w-full items-center justify-center rounded-lg bg-white px-3 py-2 text-center text-primary disabled:bg-primary-light "
            type="submit"
          >
            <span className=" text-secondary-dark mx-auto flex-1 text-sm font-medium md:text-[1.125rem]">
              Proceed
            </span>
            <span className="ml-auto flex h-9 w-9 items-center justify-center rounded-full bg-primary">
              {isTokenVerifying ? (
                <LoaderBtn width={14} height={14} color="white" />
              ) : (
                <RightUpArrow
                  height={15}
                  width={15}
                  className="translate-y-0.5"
                />
              )}{' '}
            </span>
          </button>
        </section>
      </form>
    </div>
  );
};

export default ForgotPasswordStep2;
