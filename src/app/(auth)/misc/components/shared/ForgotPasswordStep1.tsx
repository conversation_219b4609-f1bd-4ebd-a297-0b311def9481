"use client"

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { Controller, FieldValues, SubmitHandler, useForm } from 'react-hook-form';

import { cn, setAccessToken } from '@/utils';
import { Select, LoaderBtn, RadioGroup, Input } from '@/components/shared';
import { zodResolver } from '@hookform/resolvers/zod';
;
import { useSignIn } from '../../api/useSignIn';
import { useTalentOnboardMutation } from '../../api/useOnboardTalent';
import { RightUpArrow } from '../../icons';
import { ForgotPasswordFormData } from '../../types/SigupTypes';
import Link from 'next/link';
import { useRequestPasswordResetOTP } from '../../api';


interface ForgotPasswordStep1Props {
    onDetailsSubmit: (details: ForgotPasswordFormData) => void;
    user: ForgotPasswordFormData;
}


const ForgotPasswordStep1Resolver = z.object({
    email: z.string({ required_error: 'Enter email name.' }).email({ message: "Enter a valid email" }).min(1, { message: 'Email is required' }),
});




type forgotPasswordFormData = {
    email: string
}

const ForgotPasswordStep1: React.FC<ForgotPasswordStep1Props> = ({ user, onDetailsSubmit }) => {
    const { handleSubmit, register, formState: { errors, isDirty, isValid }, control, watch, setError } = useForm<forgotPasswordFormData>({ resolver: zodResolver(ForgotPasswordStep1Resolver) });;
    const [slide, setslide] = useState<string>("stagnant")

    const { mutate: requestOTP, isLoading } = useRequestPasswordResetOTP()


    useEffect(() => {
        const timer = setTimeout(() => {
            setslide("in");
        }, 15);
        return () => clearTimeout(timer);
    }, []);





    const onSubmit: SubmitHandler<FieldValues> = async (data) => {
        try {
            requestOTP({ email: data.email }, {
                onSuccess() {
                    setslide("out")
                    onDetailsSubmit({ ...user, email: data.email })
                },
                onError: (error: any) => {
                    if (error?.response?.data?.errors?.message == "USER PROFILE does not exist.") {
                        setError('email', { message: "We couldn't find a profile with this email address." });
                    }
                }
            })

        } catch (error: any) {
        }
    };




    return (
        <form onSubmit={handleSubmit(onSubmit)} className={`flex flex-col w-full py-[5vh] md:px-[1.5vw] transition-transform  ${slide == "stagnant" ? "translate-x-[110%]" : slide == "in" ? "translate-x-0 duration-300" : slide == "out" ? "translate-x-[-120%] duration-500" : ""} `}>
            <header className='flex flex-col self-start '>
                <h1 className=' text-3xl md:text-[2.35rem] text-white font-clash font-semibold'>
                    Forgot password?
                </h1>
                <p className="text-white text-sm md:font-base mt-1 mb-6  font-normal">
                    Enter your email address and we will send an email with a OTP code to reset your password.
                </p>
            </header>


            <div className="  !my-2 text-white">
                <Input
                    label="Email"
                    type="text" placeholder="Enter email" {...register('email')}
                    id="email"
                    hasError={!!errors.email}
                    errorMessage={errors.email?.message}
                    variant="transparent"
                />
            </div>

            <button className="flex items-center justify-center w-full mt-16 bg-white disabled:bg-primary-light py-1.5 px-3 rounded-lg text-primary text-center" type="submit">
                <span className='flex items-center justify-center flex-1 grow gap-x-2 text-sm md:text-base text-secondary-dark font-medium mx-auto'>
                    Done
                    {
                        isLoading && <LoaderBtn />
                    }
                </span>
                <span className='flex items-center shrink justify-center ml-auto bg-primary rounded-full w-8 h-8'><RightUpArrow height={14} width={14} className='translate-y-0.5' /></span>
            </button>
            <Link href='/login/talent' className="mt-2 flex w-full flex-wrap items-center justify-center gap-1 rounded-lg border-[1px] border-white px-2 py-3 text-sm text-white">
                Back to login
            </Link>
        </form>
    );
};

export default ForgotPasswordStep1;
