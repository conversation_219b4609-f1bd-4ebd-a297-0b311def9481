const AuthContentPage = ({ children }: { children: React.ReactNode }) => {
    return <section className="grow flex items-center justify-center relative px-4 pb-0 lg:m-0 lg:py-0 lg:flex lg:basis-1/2 lg:flex-col lg:justify-center  lg:[@media(min-height:520px)]:items-center max-lg:max-h-[84vh]">

        {/* LEFT PANEL COVER */}
        <div className='flex items-center justify-center relative  rounded-2xl z-[3] h-full  w-full py-6'>
            {/* LEFT PANEL COVER INNER */}
            <div className="relative w-[90%] md:max-lg:!w-[90%] lg:max-xl:!w-[90%] 2xl:w-[700px] max-w-[710px] lg:min-h-full lg:h-full">
                <img
                    src="/images/3d-shapes.png"
                    alt=""
                    className="absolute top-[20%] -left-20 w-20 h-30 z-[999] rotate-180"
                />
                <article className='relative flex items-center justify-center px-10 md:px-20 py-5 rounded-2xl max-h-full h-full overflow-y-scroll '>

                    {children}
                    <div className='absolute inset-0 z-[-1] rounded-[1.1875rem] from-[#EDF4FF] from-[-35.2%] to-white/20  opacity-30 bg-gradient-358 backdrop-blur-lg'></div>
                </article>
            </div>
        </div>

        {/* <div className='absolute inset-0 -scale-x-100 from-[#030004] from-[-3.89%] to-[#502B67] to-[108.49%] bg-gradient-[262deg] opacity-75' /></div> */}
    </section>;
};

export default AuthContentPage; 