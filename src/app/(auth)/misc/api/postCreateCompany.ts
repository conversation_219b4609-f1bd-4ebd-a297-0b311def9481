import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { createCompanyData } from '../types/SigupTypes';



const createCompany = async (createCompanyDto: createCompanyData) => {
    const response = await Axios.post(
        '/recruiter/company/',
        createCompanyDto,
        {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        }
    );
    return response.data;
};

export const useCreateCompany = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: createCompany,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['fetch-all-companies-in-database'],
            });
        },
    });
};