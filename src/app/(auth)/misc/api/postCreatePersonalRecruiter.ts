import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { createPersonalRecruiterData } from '../types/SigupTypes';



const createPersonalRecruiter = async (createPersonalRecruiterDto: createPersonalRecruiterData) => {
    const response = await Axios.post(
        '/recruiter/',
        createPersonalRecruiterDto,
        {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        }
    );
    return response.data;
};

export const useCreatePersonalRecruiter = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: createPersonalRecruiter,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['fetch-all-companies-in-database'],
            });
        },
    });
};