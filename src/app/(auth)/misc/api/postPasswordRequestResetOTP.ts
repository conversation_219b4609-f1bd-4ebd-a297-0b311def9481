import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios, AxiosWithNoAuth } from '@/lib/api/axios';



const requestPasswordResetOTP = async (requestPasswordResetOTPDto: { email: string }) => {
    const response = await AxiosWithNoAuth.post(
        '/user/auth/forgot-password/',
        requestPasswordResetOTPDto,
    );
    return response.data;
};

export const useRequestPasswordResetOTP = () => {
    return useMutation({
        mutationFn: requestPasswordResetOTP,
    });
};



const resendOTP= async (requestPasswordResetOTPDto: { email: string }) => {
    const response = await AxiosWithNoAuth.post(
        '/user/auth/resend-otp/',
        requestPasswordResetOTPDto,
    );
    return response.data;
};

export const useResendOTP = () => {
    return useMutation({
        mutationFn: resendOTP,
    });
};