import { useMutation } from '@tanstack/react-query';
import axios from 'axios';
import { AxiosWithNoAuth } from '@/lib/api/axios';

type loginFormData = {
  email: string;
  password: string;
};

async function loginUser(userData: loginFormData) {
  return await AxiosWithNoAuth.post('/user/auth/login/', userData);
}

export function useSignIn() {
  const mutationOptions = {
    retry: false,
  };

  return useMutation((userData: loginFormData) => loginUser(userData), mutationOptions);
}
