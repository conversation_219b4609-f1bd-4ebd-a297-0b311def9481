import { Axios } from "@/lib/api/axios";
import { useMutation, useQuery } from "@tanstack/react-query";
import axios from "axios";

export type InvitationDecodedData = {
    role: string;
    company_name: string;
    recipient_email: string;
    type_of_recruiter: string;
};
async function decodeInvite(invitation_token: string) {
    let response = await Axios.get(`teams/invite/decode`, { params: { invitation_token } });

    return response.data.data as InvitationDecodedData;
}



const useDecodeInvitationToken = (invitation_token: string) => {
    return useQuery({
        queryKey: ['decode-invitation-token', invitation_token], // include token in key
        queryFn: () => decodeInvite(invitation_token),
        enabled: false, // don't auto-run
    });
};

export default useDecodeInvitationToken