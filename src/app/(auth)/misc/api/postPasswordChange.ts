import { useMutation } from '@tanstack/react-query';
import { Axios, AxiosWithNoAuth } from '@/lib/api/axios';
import {
  ForgotPasswordFormData,
  VerifyTokemFormData,
} from '../types/SigupTypes';

const passwordChange = async (passwordChangeDto: ForgotPasswordFormData) => {
  const response = await AxiosWithNoAuth.post(
    '/user/auth/reset-password/',
    passwordChangeDto
  );
  return response.data;
};

export const useChangePassword = () => {
  return useMutation({
    mutationFn: passwordChange,
  });
};

const tokenVerification = async (passwordChangeDto: VerifyTokemFormData) => {
  const response = await AxiosWithNoAuth.post(
    '/user/auth/verify-otp/',
    passwordChangeDto
  );
  return response.data;
};

export const useVerifyTokn = () => {
  return useMutation({
    mutationFn: tokenVerification,
  });
};
