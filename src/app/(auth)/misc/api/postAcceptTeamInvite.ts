import { Axios } from "@/lib/api/axios";
import { useMutation } from "@tanstack/react-query";
import axios from "axios";

type registerFormData = {
    invitation_token: string,
    first_name: string,
    last_name: string,
    phone_number: string,
    gender: string,
    password: string,
    confirm_password: string,
}
async function createNewUser(userData: registerFormData) {
    return await Axios.post(`/teams/invite/accept/`, userData);
}



export default function useAcceptTeamInvite() {
    return useMutation(
        (formData: registerFormData) => createNewUser(formData),
    );
}
