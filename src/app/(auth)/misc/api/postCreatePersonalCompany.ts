import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { createPersonalCompanyData } from '../types/SigupTypes';



const createPersonalCompany = async (createCompanyDto: createPersonalCompanyData) => {
    const response = await Axios.post(
        '/recruiter/company/',
        createCompanyDto,
        {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        }
    );
    return response.data;
};

export const useCreatePersonalCompany = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: createPersonalCompany,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['fetch-all-companies-in-database'],
            });
        },
    });
};