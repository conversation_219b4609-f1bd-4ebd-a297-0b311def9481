import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { createRecruiterData } from '../types/SigupTypes';



const createRecruiter = async (createRecruiterDto: createRecruiterData) => {
    const response = await Axios.post(
        '/recruiter/',
        createRecruiterDto,
        {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        }
    );
    return response.data;
};

export const useCreateRecruiter = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: createRecruiter,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['fetch-all-companies-in-database'],
            });
        },
    });
};