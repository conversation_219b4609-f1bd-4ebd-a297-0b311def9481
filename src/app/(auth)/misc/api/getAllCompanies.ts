import { useQuery } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';


export interface Company {
    id: number;
    name: string;
    address: string;
    website: string;
    industry: string;
    size: string;
    description: string;
    logo: string | null;
    type_of_company: string;
    company_domain: string;
    created_at: string;
}




export const fetchCompanies = async () => {
    const response = await Axios.get(`recruiter/fetch_all_companies/`);
    return response.data as Company[];
};

const useGetCompanies = () => {
    return useQuery(['fetch-all-companies-in-database'], () => fetchCompanies(), {
        keepPreviousData: true,
    });
};

export default useGetCompanies