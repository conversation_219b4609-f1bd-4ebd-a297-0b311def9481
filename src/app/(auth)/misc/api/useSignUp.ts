import { AxiosWithNoAuth } from "@/lib/api/axios";
import { useMutation } from "@tanstack/react-query";

type registerFormData = {
    first_name: string,
    last_name: string,
    email: string,
    phone_number: string,
    gender: string,
    password: string,
    confirm_password: string,
}
async function createNewUser(userData: registerFormData) {
    const res = await AxiosWithNoAuth.post(`/user/auth/register/`, userData);
    return res.data;
}



export function useSignUp() {
    return useMutation({
        mutationFn: createNewUser,
    });
}
