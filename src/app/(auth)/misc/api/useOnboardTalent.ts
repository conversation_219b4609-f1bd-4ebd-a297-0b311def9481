import { getAccessToken } from "@/utils";
import { useMutation } from "@tanstack/react-query";
import axios from "axios";


type userData = {
    address: string,
    desired_role: string,
    years_of_experience: string,
    stack: Array<string>,
    job_type: string,
    currently_employed: string,
    willing_to_relocate: string,
};

async function onBoardTalent(userData: userData) {
    const token = getAccessToken()
    const headers = {
        'Authorization': `Bearer ${token}`
    };

    return await axios.post(`${process.env.NEXT_PUBLIC_BACKEND_URL}/talent/onboarding/`, userData, { headers });
}

export function useTalentOnboardMutation() {
    return useMutation(
        (formData: userData) => onBoardTalent(formData),
    );
}
