'use client';

import React, { useEffect, useState } from 'react';

import { Loader, LoadingOverlay } from '@/components/shared';
import GetLinkedLogoLight from '@/app/(assessments)/company-test/misc/icons/GetLinkedLogoLight';
import { StrokeCircleCheck, StrokeClose } from '@/components/shared/icons';

import { useRescheduleInterview } from '../../misc/api';


const RescheduleInterviewPage = ({ params, }: { params: { encoded: string }; }) => {
    const [status, setStatus] = useState('loading');
    const { encoded } = params;
    const { mutate: rescheduleInterview, isLoading } = useRescheduleInterview();


    useEffect(() => {
        rescheduleInterview(encoded, {
            onSuccess(data, variables, context) {
                setStatus('success');
            },
            onError(error, variables, context) {
                setStatus('failure');
            },
        })
    }, [encoded]);





    return (
        <div className="relative flex flex-col h-screen w-screen  bg-[#1F0231]">
            <header className="flex items-center  left-3 top-3 scale-90 sm:left-10 sm:top-10">
                <GetLinkedLogoLight />
            </header>

            <section className='grow flex lg:grid lg:grid-cols-[0.5fr,1fr,0.5fr] gap-8 items-center lg:items-end justify-center'>
                <aside className='p-[15%] max-lg:hidden'>
                    <img alt="" src={'/assets/char11.svg'} />
                </aside>

                {
                    status === "loading" ?
                        <Loader />
                        :
                        status === "success" ?
                            <section className="max-lg:w-[90%] lg:w-full justify-self-center max-w-[500px] min-h-[86%] flex flex-col items-center text-center rounded-2xl border-[0.01px] border-[#FFFFFF]/10 text-white bg-[#F8F6FF05] p-3 sm:p-10 sm:pt-16">
                                <div className='p-4 rounded-full bg-[#FFFFFF1A] mb-6'>
                                    <StrokeCircleCheck height={75} width={75} />
                                </div>
                                <h3 className='text-3xl font-bold '>Interview reschedule requested</h3>
                                <p className='text-helper-text max-w-[28ch] text-sm'>
                                    Your response has been noted,
                                    Rest assured that you will be contacted for any further communication.
                                </p>
                            </section>
                            :
                            status === "failure" ?
                                <section className="max-lg:w-[90%] lg:w-full justify-self-center max-w-[500px] min-h-[86%] flex flex-col items-center text-center rounded-2xl border-[0.01px] border-[#FFFFFF]/10 text-white bg-[#F8F6FF05] p-3 sm:p-10 sm:pt-16">
                                    <div className='p-4 rounded-full bg-[#FFFFFF1A] mb-6'>
                                        <StrokeClose height={75} width={75} />
                                    </div>
                                    <h3 className='text-3xl font-bold'>Something went wrong</h3>
                                    <p className='text-helper-text max-w-[28ch] text-sm'>
                                        Something went wrong while we tried to process your request, refresh the page to try again.
                                    </p>
                                </section>
                                :
                                null

                }
                <aside className='p-[15%] max-lg:hidden'>
                    <img alt="" src={'/assets/cha12.svg'} />
                </aside>
            </section>

            <footer className='min-h-[10vh]'>

            </footer>



            <LoadingOverlay isOpen={isLoading} />
        </div>
    );
};

export default RescheduleInterviewPage;