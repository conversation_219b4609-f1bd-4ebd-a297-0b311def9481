import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';


const acceptInterview = async (token:string) => {
    const response = await Axios.post(
        `/recruiter/confirm-attendance/${token}/`,
        
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
    return response.data;
};

const useAcceptInterview = () => {
    return useMutation({
        mutationFn: acceptInterview,
    });
};

export default useAcceptInterview