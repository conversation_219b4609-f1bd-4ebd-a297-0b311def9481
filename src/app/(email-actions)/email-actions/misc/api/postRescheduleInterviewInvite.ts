import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';


const rescheduleInterview = async (token:string) => {
    const response = await Axios.post(
        `/recruiter/reschedule-interview/${token}/`,
        
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
    return response.data;
};

const useRescheduleInterview = () => {
    return useMutation({
        mutationFn: rescheduleInterview,
    });
};

export default useRescheduleInterview