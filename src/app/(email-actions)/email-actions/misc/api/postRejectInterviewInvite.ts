import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';


const rejectInterview = async (token:string) => {
    const response = await Axios.post(
        `/recruiter/cancel-interview/${token}/`,
        
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
    return response.data;
};

const useRejectInterview = () => {
    return useMutation({
        mutationFn: rejectInterview,
    });
};

export default useRejectInterview