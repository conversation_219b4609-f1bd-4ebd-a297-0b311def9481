export const dummyTest = {
  name: '<PERSON>',
  role: 'Backend Developer',
  time_allocated: 1800,
  no_questions: 20,
  questions: [
    {
      id: 1,
      question:
        'What is the Document Object Model (DOM) in web development, and how does it relate to JavaScript?',
      type: 'essay',
      answer:
        'The Document Object Model (DOM) is a programming interface for web documents. It represents the structure of HTML and XML documents as a tree of objects, where each object corresponds to a part of the document. In web development, JavaScript is commonly used to interact with the DOM. It allows developers to manipulate and modify the content and structure of web pages dynamically, enabling user interactions and content updates without requiring a full page reload.',
    },
    {
      id: 2,
      question:
        "Explain the concept of 'Progressive Enhancement' in frontend development and why it is important for building accessible web applications.",
      type: 'essay',
      answer:
        'Progressive Enhancement is a design and development strategy that starts with creating a basic, accessible, and functional version of a web application or website and then progressively enhances it with more advanced features for users with modern browsers and devices. It is essential for accessibility because it ensures that the core content and functionality are available to all users, including those with older browsers or assistive technologies. By starting with a solid foundation, you can provide a better user experience for everyone and accommodate a broader audience.',
    },
    {
      id: 3,
      question: "What is the 'box model' in CSS, and how does it work?",
      type: 'essay',
      answer:
        "The 'box model' in CSS describes how elements are rendered as rectangular boxes with content, padding, borders, and margins. It works by defining the total space an element occupies on the page. The content area contains the actual content, the padding provides space between the content and the border, the border defines the element's boundary, and the margin creates space outside the element, separating it from other elements. Understanding the box model is crucial for layout and spacing in web design.",
    },
    {
      id: 4,
      question:
        "Explain the purpose of the 'localStorage' and 'sessionStorage' Web Storage APIs in JavaScript, and describe the differences between them.",
      type: 'essay',
      answer:
        "The 'localStorage' and 'sessionStorage' Web Storage APIs allow web applications to store key-value pairs on the client-side. 'localStorage' stores data with no expiration time, while 'sessionStorage' stores data only for the duration of a page session. They are useful for persisting data across page reloads or sharing data within a single session. However, 'localStorage' data remains available even after browser restarts, while 'sessionStorage' data is cleared when the session ends.",
    },
    {
      id: 5,
      question:
        'What are the advantages of using a CSS preprocessor like SASS or LESS, and how do they simplify CSS development?',
      type: 'essay',
      answer:
        'CSS preprocessors like SASS and LESS offer advantages such as variables, nesting, mixins, and functions, which simplify and enhance CSS development. Variables allow you to define and reuse values, making style changes easier. Nesting simplifies the structure of styles by grouping related rules. Mixins enable code reuse, reducing redundancy. Functions add dynamic behavior to styles. Collectively, these features improve code maintainability and make CSS development more efficient.',
    },
    {
      id: 6,
      question: 'What does HTML stand for?',
      type: 'MCQ',
      options: [
        'Hypertext Markup Language',
        'Hyperlink and Text Markup Language',
        'Highly Technical Markup Language',
      ],
      answer: 'Hypertext Markup Language',
    },
    {
      id: 7,
      question: 'What is the correct HTML element for creating a hyperlink?',
      type: 'MCQ',
      options: ['<a>', '<link>', '<hlink>', '<href>'],
      answer: '<a>',
    },
    {
      id: 8,
      question: "Explain the purpose of the 'doctype' declaration in HTML.",
      type: 'essay',
      answer:
        "The 'doctype' declaration defines the document type and version of HTML being used in the document. It helps the browser to interpret and render the content correctly.",
    },
    {
      id: 9,
      question: 'What is the primary purpose of CSS?',
      type: 'MCQ',
      options: [
        'To create structured documents',
        'To add interactivity to web pages',
        'To style and format web content',
      ],
      answer: 'To style and format web content',
    },
    {
      id: 10,
      question:
        "What is the purpose of the 'document.getElementById()' method in JavaScript?",
      type: 'MCQ',
      options: [
        'To access and modify an HTML element by its class name',
        'To access and modify an HTML element by its ID',
        'To access and modify an HTML element by its tag name',
      ],
      answer: 'To access and modify an HTML element by its ID',
    },
    {
      id: 11,
      question:
        'Explain the concept of closures in JavaScript and provide an example.',
      type: 'essay',
      answer:
        "Closures are functions that have access to their own scope and the outer (enclosing) function's scope. They retain access to the variables and parameters of the outer function even after the outer function has finished executing. Closures are commonly used to create private variables and maintain state in JavaScript. Here's an example:\n\n```javascript\nfunction outerFunction() {\n  let outerVar = 'I am from the outer function';\n  function innerFunction() {\n    console.log(outerVar);\n  }\n  return innerFunction;\n}\nconst closure = outerFunction();\nclosure(); // Prints 'I am from the outer function'\n```\nIn this example, the `innerFunction` is a closure that retains access to the `outerVar` even after `outerFunction` has completed.",
    },
    {
      id: 12,
      question:
        'What is the importance of responsive web design, and how does it differ from adaptive design?',
      type: 'essay',
      answer:
        'Responsive web design is crucial as it ensures that web content displays and functions optimally on various devices and screen sizes. It uses fluid grids and flexible layouts to adapt to different screen sizes. Unlike adaptive design, which creates different versions of a website for specific breakpoints, responsive design adjusts content dynamically based on the screen size, providing a more seamless user experience.',
    },
    {
      id: 13,
      question:
        'What are media queries in CSS, and how are they used to create responsive layouts?',
      type: 'essay',
      answer:
        'Media queries are CSS rules that apply styles based on specific conditions, such as screen width, height, or device orientation. They are used to create responsive layouts by defining different styles for different screen sizes or features. For example, you can use a media query to change the font size or layout of a website when the screen width is below a certain threshold.',
    },
    {
      id: 14,
      question:
        "Explain the purpose of a 'merge' and 'rebase' operation in Git, and when would you use each?",
      type: 'essay',
      answer:
        "In Git, 'merge' combines changes from one branch into another, creating a new commit with both branches' changes. 'Rebase' rewrites the commit history by moving or combining commits from one branch to another. You would use 'merge' for integrating features or bug fixes, maintaining a linear history. 'Rebase' is used to keep a clean history by incorporating changes from another branch as if they occurred on top of the current branch.",
    },
    {
      id: 15,
      question:
        'What are some best practices for collaborating with a team using Git?',
      type: 'essay',
      answer:
        "Best practices include using feature branches, writing descriptive commit messages, regularly pulling changes from the main branch, resolving conflicts promptly, and following a branching strategy such as Gitflow. It's also important to communicate effectively with your team and adhere to version control etiquette to avoid conflicts and streamline collaboration.",
    },
    {
      id: 16,
      question:
        'Explain the virtual DOM in React and how it helps improve performance.',
      type: 'essay',
      answer:
        'The virtual DOM is a representation of the actual DOM in memory. React uses it to efficiently update and render UI components. When changes occur, React creates a new virtual DOM tree and compares it with the previous one. It identifies the differences and updates only the necessary parts in the actual DOM. This minimizes browser repaints and reflows, resulting in better performance.',
    },
    {
      id: 17,
      question:
        'What is a React component and what are the key differences between functional and class components?',
      type: 'essay',
      answer:
        'A React component is a reusable building block for creating UI elements. Functional components are defined as JavaScript functions and are simpler and more concise. Class components are defined as ES6 classes and have additional features like lifecycle methods. With the introduction of React Hooks, functional components can now also manage state and side effects, reducing the need for class components.',
    },
    {
      id: 18,
      question: 'Explain the CSS selector specificity. How does it work?',
      type: 'essay',
      answer:
        "CSS selector specificity is a weight or value that determines which style rule should be applied to an element when there are conflicting rules. Specificity is calculated based on the combination of selectors and their component parts, such as type, class, ID, and inline styles. The selector with the highest specificity value wins and is applied to the element's styling.",
    },
    {
      id: 19,
      question:
        'Explain what the Critical Rendering Path is and how it impacts web page loading performance.',
      type: 'essay',
      answer:
        'The Critical Rendering Path is the sequence of steps that a web browser takes to convert HTML, CSS, and JavaScript into a rendered web page. It includes parsing HTML, constructing the DOM and CSSOM, rendering the page, and painting pixels on the screen. Optimizing this path is crucial for improving web page loading performance. For example, reducing render-blocking resources, minimizing the use of external scripts, and optimizing CSS delivery can help speed up the rendering process.',
    },
    {
      id: 20,
      question:
        'What is two-way data binding in Angular, and how does it differ from one-way data binding?',
      type: 'essay',
      answer:
        'Two-way data binding in Angular allows automatic synchronization of data between the model and the view. When data changes in the model, it is reflected in the view, and vice versa. One-way data binding, on the other hand, only updates the view when the model changes. Two-way data binding simplifies development but can introduce performance overhead, whereas one-way data binding offers more control and can be more efficient.',
    },
    {
      id: 21,
      question:
        'Explain the benefits of using a CSS preprocessor like SASS, and provide an example of a SASS feature.',
      type: 'essay',
      answer:
        "CSS preprocessors like SASS offer benefits such as variables, nesting, and mixins, which enhance maintainability and reusability of styles. For example, SASS variables allow you to define and reuse values throughout your styles, making it easier to update colors or other properties consistently across your project. Here's an example:\n\n```\n$primary-color: #3498db;\n$secondary-color: #e74c3c;\n\n.button {\n  background-color: $primary-color;\n}\n```\nThis code defines and uses SASS variables for primary and secondary colors.",
    },
    {
      id: 22,
      question:
        'What is the importance of web accessibility in web development, and how can you ensure your websites are accessible to all users?',
      type: 'essay',
      answer:
        'Web accessibility is crucial as it ensures that websites and web applications are usable by people with disabilities. It promotes inclusivity and compliance with accessibility standards such as WCAG. To ensure accessibility, developers should use semantic HTML elements, provide alternative text for images, create keyboard-friendly interfaces, and test with assistive technologies like screen readers. Regular accessibility audits and user testing can help identify and address accessibility issues.',
    },
    {
      id: 23,
      question:
        'Explain the concept of ARIA roles in web development and provide an example of when to use them.',
      type: 'essay',
      answer:
        "ARIA (Accessible Rich Internet Applications) roles are attributes that can be added to HTML elements to improve accessibility for assistive technologies. For example, the 'role' attribute can be used to specify the function of an element, like 'button' for a clickable button element. ARIA roles are particularly useful when creating custom widgets or interactive components, like modals or custom dropdown menus, to ensure they are properly announced to screen reader users.",
    },
    {
      id: 24,
      question:
        'What is Cross-Site Scripting (XSS) in web security, and how can it be prevented?',
      type: 'essay',
      answer:
        'Cross-Site Scripting (XSS) is a security vulnerability that allows attackers to inject malicious scripts into web pages viewed by other users. To prevent XSS, developers should sanitize and validate user inputs, use secure coding practices, and implement Content Security Policy (CSP) headers to restrict the execution of scripts from untrusted sources. Escaping user-generated content and avoiding inline scripts are also essential precautions.',
    },
    {
      id: 25,
      question:
        'Explain the same-origin policy in web security and its significance in preventing unauthorized data access.',
      type: 'essay',
      answer:
        'The same-origin policy is a fundamental security concept in web browsers that restricts web pages from making requests to domains different from the one that served the web page. This policy prevents unauthorized data access and potential security breaches. It ensures that web pages can only interact with resources (e.g., APIs) on the same domain, protecting sensitive data from being accessed by malicious scripts from other domains.',
    },
    {
      id: 26,
      question:
        'What is the purpose of a build tool like Webpack in frontend development, and what are some common optimizations it can perform?',
      type: 'essay',
      answer:
        'Webpack is a build tool that automates tasks like bundling, minification, and code splitting, improving the performance and maintainability of frontend code. Common optimizations include tree shaking (removing unused code), code splitting (creating smaller bundles for lazy loading), and minimizing assets (reducing file sizes). These optimizations enhance page load speed and user experience.',
    },
    {
      id: 27,
      question:
        "Explain the concept of 'Hot Module Replacement' (HMR) in Webpack and how it benefits developers during development.",
      type: 'essay',
      answer:
        "Hot Module Replacement (HMR) is a feature in Webpack that allows developers to update modules in a running application without a full page refresh. It speeds up development by preserving the application's state and only updating the changed modules, making the development process more efficient and interactive.",
    },
    {
      id: 28,
      question: 'Which of the following is NOT a valid HTML tag?',
      type: 'mcq',
      options: ['<div>', '<section>', '<span>', '<circle>'],
      answer: '<circle>',
    },
    {
      id: 29,
      question: 'What does CSS stand for ?',
      type: 'mcq',
      options: [
        'Cascading Style Sheet',
        'Creative Style Selector',
        'Computer Screen Styling',
        'Custom Style System',
      ],
      answer: 'Cascading Style Sheet',
    },
    {
      id: 30,
      question:
        "Which of the following CSS selectors targets all <p> elements with a class of 'special'?",
      type: 'mcq',
      options: ['p.special', '#special p', '.special p', 'p .special'],
      answer: 'p.special',
    },
  ],
};

export const dummyTest2 = {
  invite_id: '9703431c-49e6-4db2-af40-b9284ba8c474',
  candidate_name: 'Daniel Ibejih',
  candidate_email: '<EMAIL>',
  auth_method: [],
  assessment: {
    id: 'd9e9eb7e-c026-4fe3-a174-3b05cf23e785',
    sections: [
      {
        id: '9822441a-40bc-4eae-871b-d8af7f32eb4b',
        section_name: 'Skills & Competency',
        no_multiple_choices: 2,
        no_essay: 2,
        no_coding: 2,
        test: [],
        question_set: [
          {
            id: '07dcd9a0-8764-48b8-ad3b-b9ab52bf74ec',
            type: 'essay',
            question:
              'Discuss the advantages and disadvantages of static routing compared to dynamic routing.',
            answer_options: [],
            answer: null,
            role: 'Network Engineer',
            experience_level: 'entry_level',
            category: 'role_specific',
            label: 'skill',
            tags: [
              'Routing and Switching',
              'Static Routing',
              'Dynamic Routing',
            ],
            is_custom: false,
            created_at: '2023-10-16T11:52:44.387114+01:00',
            updated_at: '2023-10-16T11:52:44.387114+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
          {
            id: 'df28bae5-bc17-443e-927a-898683c1e9ee',
            type: 'essay',
            question:
              'Explain the purpose and benefits of implementing Quality of Service (QoS) in a network.',
            answer_options: [],
            answer: null,
            role: 'Network Engineer',
            experience_level: 'entry_level',
            category: 'role_specific',
            label: 'skill',
            tags: [
              'Routing and Switching',
              'Quality of Service',
              'Network Performance',
            ],
            is_custom: false,
            created_at: '2023-10-16T11:52:44.395110+01:00',
            updated_at: '2023-10-16T11:52:44.395110+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
          {
            id: '25d38f5e-663d-43b9-b31a-1a8bfaea18ac',
            type: 'coding',
            question:
              'Write a Python function that calculates the factorial of a given number.',
            answer_options: [],
            answer: null,
            role: 'Network Engineer',
            experience_level: 'entry_level',
            category: 'role_specific',
            label: 'skill',
            tags: ['Routing and Switching', 'Python', 'Factorial Calculation'],
            is_custom: false,
            created_at: '2023-10-16T11:52:44.411109+01:00',
            updated_at: '2023-10-16T11:52:44.411109+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
          {
            id: 'b59d3b6a-0a2c-4897-bd11-3db16bf6a5e1',
            type: 'coding',
            question:
              'Implement a function in C++ to reverse a string without using any built-in functions or libraries.',
            answer_options: [],
            answer: null,
            role: 'Network Engineer',
            experience_level: 'entry_level',
            category: 'role_specific',
            label: 'skill',
            tags: ['Routing and Switching', 'C++', 'String Reversal'],
            is_custom: false,
            created_at: '2023-10-16T11:52:44.427112+01:00',
            updated_at: '2023-10-16T11:52:44.427112+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
          {
            id: 'ec6b6d97-93f6-44a6-b096-50cf309ef1cd',
            type: 'multiple_choice',
            question: 'What is the capital of France?',
            answer_options: ['Paris', 'Berlin', 'London'],
            answer: 'Paris',
            role: 'Travler',
            experience_level: 'entry_level',
            category: 'role_specific',
            label: 'skill',
            tags: ['geography', 'capital cities'],
            is_custom: true,
            created_at: '2023-10-26T08:05:43.040826+01:00',
            updated_at: '2023-10-26T08:05:43.451397+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
        ],
      },
      {
        id: 'fff9273f-3911-4527-80d8-52e14910191c',
        section_name: 'Skills & Competency',
        no_multiple_choices: 2,
        no_essay: 2,
        no_coding: 2,
        test: [],
        question_set: [
          {
            id: 'a8d17a7a-0eb8-473a-946d-383d5addd9a1',
            type: 'essay',
            question:
              'Describe the process of subnetting and how it helps in efficient IP address allocation.',
            answer_options: [],
            answer: null,
            role: 'Network Engineer',
            experience_level: 'entry_level',
            category: 'role_specific',
            label: 'skill',
            tags: [
              'Routing and Switching',
              'Subnetting',
              'IP Address Allocation',
            ],
            is_custom: false,
            created_at: '2023-10-16T11:52:44.371117+01:00',
            updated_at: '2023-10-16T11:52:44.371117+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
          {
            id: 'df28bae5-bc17-443e-927a-898683c1e9ee',
            type: 'essay',
            question:
              'Explain the purpose and benefits of implementing Quality of Service (QoS) in a network.',
            answer_options: [],
            answer: null,
            role: 'Network Engineer',
            experience_level: 'entry_level',
            category: 'role_specific',
            label: 'skill',
            tags: [
              'Routing and Switching',
              'Quality of Service',
              'Network Performance',
            ],
            is_custom: false,
            created_at: '2023-10-16T11:52:44.395110+01:00',
            updated_at: '2023-10-16T11:52:44.395110+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
          {
            id: '25d38f5e-663d-43b9-b31a-1a8bfaea18ac',
            type: 'coding',
            question:
              'Write a Python function that calculates the factorial of a given number.',
            answer_options: [],
            answer: null,
            role: 'Network Engineer',
            experience_level: 'entry_level',
            category: 'role_specific',
            label: 'skill',
            tags: ['Routing and Switching', 'Python', 'Factorial Calculation'],
            is_custom: false,
            created_at: '2023-10-16T11:52:44.411109+01:00',
            updated_at: '2023-10-16T11:52:44.411109+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
          {
            id: 'ec6b6d97-93f6-44a6-b096-50cf309ef1cd',
            type: 'multiple_choice',
            question: 'What is the capital of France?',
            answer_options: ['Paris', 'Berlin', 'London'],
            answer: 'Paris',
            role: 'Travler',
            experience_level: 'entry_level',
            category: 'role_specific',
            label: 'skill',
            tags: ['geography', 'capital cities'],
            is_custom: true,
            created_at: '2023-10-26T08:05:43.040826+01:00',
            updated_at: '2023-10-26T08:05:43.451397+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
          {
            id: '6c0adeea-7baf-4180-886e-e3f0e0fd82b3',
            type: 'coding',
            question:
              'Write a Java function that calculates the factorial of a given number.',
            answer_options: [],
            answer: null,
            role: 'Network Engineer',
            experience_level: 'expert_level',
            category: 'role_specific',
            label: 'skill',
            tags: ['Routing and Switching', 'Python', 'Factorial Calculation'],
            is_custom: true,
            created_at: '2023-10-26T09:14:45.779464+01:00',
            updated_at: '2023-10-26T09:14:45.995846+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
        ],
      },
      {
        id: '354db61d-ddf7-4612-aaad-b0114779e2b9',
        section_name: '',
        no_multiple_choices: 2,
        no_essay: 2,
        no_coding: 2,
        test: [
          {
            id: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            name: 'Entry Level Network Engineer - Routing and Switching',
            description:
              "This test is designed to assess the candidate's knowledge and skills in the area of Routing and Switching for a Network Engineer role. The test will evaluate the candidate's understanding of network protocols, routing protocols, switching technologies, and troubleshooting techniques.",
            summary:
              "The purpose of this assessment is to determine the candidate's proficiency in Routing and Switching concepts and their ability to apply them in real-world scenarios. The assessment aims to identify candidates who possess the necessary skills to configure, manage, and troubleshoot network routers and switches.",
            skills_tested:
              '1. Understanding of TCP/IP and OSI models\r\n2. Knowledge of routing protocols such as OSPF and EIGRP\r\n3. Configuration and troubleshooting of VLANs and inter-VLAN routing\r\n4. Familiarity with switching technologies like STP, VLAN Trunking, and EtherChannel\r\n5. Ability to troubleshoot network connectivity issues\r\n6. Knowledge of network security principles and best practices',
            experience_level: 'entry_level',
            role: 'Network Engineer',
            tags: [],
            label: 'skills',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
          },
        ],
        question_set: [
          {
            id: 'a6506183-e720-43ae-a8c4-9ce907aa1645',
            type: 'essay',
            question:
              'Explain the concept of VLAN trunking and how it enables communication between VLANs.',
            answer_options: [],
            answer: null,
            role: 'Network Engineer',
            experience_level: 'entry_level',
            category: 'role_specific',
            label: 'skill',
            tags: [
              'Routing and Switching',
              'VLAN Trunking',
              'Inter-VLAN Communication',
            ],
            is_custom: false,
            created_at: '2023-10-16T11:52:44.379112+01:00',
            updated_at: '2023-10-16T11:52:44.379112+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
          {
            id: '07dcd9a0-8764-48b8-ad3b-b9ab52bf74ec',
            type: 'essay',
            question:
              'Discuss the advantages and disadvantages of static routing compared to dynamic routing.',
            answer_options: [],
            answer: null,
            role: 'Network Engineer',
            experience_level: 'entry_level',
            category: 'role_specific',
            label: 'skill',
            tags: [
              'Routing and Switching',
              'Static Routing',
              'Dynamic Routing',
            ],
            is_custom: false,
            created_at: '2023-10-16T11:52:44.387114+01:00',
            updated_at: '2023-10-16T11:52:44.387114+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
          {
            id: 'b59d3b6a-0a2c-4897-bd11-3db16bf6a5e1',
            type: 'coding',
            question:
              'Implement a function in C++ to reverse a string without using any built-in functions or libraries.',
            answer_options: [],
            answer: null,
            role: 'Network Engineer',
            experience_level: 'entry_level',
            category: 'role_specific',
            label: 'skill',
            tags: ['Routing and Switching', 'C++', 'String Reversal'],
            is_custom: false,
            created_at: '2023-10-16T11:52:44.427112+01:00',
            updated_at: '2023-10-16T11:52:44.427112+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
          {
            id: 'ec6b6d97-93f6-44a6-b096-50cf309ef1cd',
            type: 'multiple_choice',
            question: 'What is the capital of France?',
            answer_options: ['Paris', 'Berlin', 'London'],
            answer: 'Paris',
            role: 'Travler',
            experience_level: 'entry_level',
            category: 'role_specific',
            label: 'skill',
            tags: ['geography', 'capital cities'],
            is_custom: true,
            created_at: '2023-10-26T08:05:43.040826+01:00',
            updated_at: '2023-10-26T08:05:43.451397+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
          {
            id: '6c0adeea-7baf-4180-886e-e3f0e0fd82b3',
            type: 'coding',
            question:
              'Write a Java function that calculates the factorial of a given number.',
            answer_options: [],
            answer: null,
            role: 'Network Engineer',
            experience_level: 'expert_level',
            category: 'role_specific',
            label: 'skill',
            tags: ['Routing and Switching', 'Python', 'Factorial Calculation'],
            is_custom: true,
            created_at: '2023-10-26T09:14:45.779464+01:00',
            updated_at: '2023-10-26T09:14:45.995846+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
        ],
      },
      {
        id: '24517aae-9b58-437f-ad6d-b42497d6cfeb',
        section_name: '',
        no_multiple_choices: 2,
        no_essay: 2,
        no_coding: 2,
        test: [
          {
            id: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            name: 'Entry Level Network Engineer - Routing and Switching',
            description:
              "This test is designed to assess the candidate's knowledge and skills in the area of Routing and Switching for a Network Engineer role. The test will evaluate the candidate's understanding of network protocols, routing protocols, switching technologies, and troubleshooting techniques.",
            summary:
              "The purpose of this assessment is to determine the candidate's proficiency in Routing and Switching concepts and their ability to apply them in real-world scenarios. The assessment aims to identify candidates who possess the necessary skills to configure, manage, and troubleshoot network routers and switches.",
            skills_tested:
              '1. Understanding of TCP/IP and OSI models\r\n2. Knowledge of routing protocols such as OSPF and EIGRP\r\n3. Configuration and troubleshooting of VLANs and inter-VLAN routing\r\n4. Familiarity with switching technologies like STP, VLAN Trunking, and EtherChannel\r\n5. Ability to troubleshoot network connectivity issues\r\n6. Knowledge of network security principles and best practices',
            experience_level: 'entry_level',
            role: 'Network Engineer',
            tags: [],
            label: 'skills',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
          },
        ],
        question_set: [
          {
            id: 'a6506183-e720-43ae-a8c4-9ce907aa1645',
            type: 'essay',
            question:
              'Explain the concept of VLAN trunking and how it enables communication between VLANs.',
            answer_options: [],
            answer: null,
            role: 'Network Engineer',
            experience_level: 'entry_level',
            category: 'role_specific',
            label: 'skill',
            tags: [
              'Routing and Switching',
              'VLAN Trunking',
              'Inter-VLAN Communication',
            ],
            is_custom: false,
            created_at: '2023-10-16T11:52:44.379112+01:00',
            updated_at: '2023-10-16T11:52:44.379112+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
          {
            id: '07dcd9a0-8764-48b8-ad3b-b9ab52bf74ec',
            type: 'essay',
            question:
              'Discuss the advantages and disadvantages of static routing compared to dynamic routing.',
            answer_options: [],
            answer: null,
            role: 'Network Engineer',
            experience_level: 'entry_level',
            category: 'role_specific',
            label: 'skill',
            tags: [
              'Routing and Switching',
              'Static Routing',
              'Dynamic Routing',
            ],
            is_custom: false,
            created_at: '2023-10-16T11:52:44.387114+01:00',
            updated_at: '2023-10-16T11:52:44.387114+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
          {
            id: '25d38f5e-663d-43b9-b31a-1a8bfaea18ac',
            type: 'coding',
            question:
              'Write a Python function that calculates the factorial of a given number.',
            answer_options: [],
            answer: null,
            role: 'Network Engineer',
            experience_level: 'entry_level',
            category: 'role_specific',
            label: 'skill',
            tags: ['Routing and Switching', 'Python', 'Factorial Calculation'],
            is_custom: false,
            created_at: '2023-10-16T11:52:44.411109+01:00',
            updated_at: '2023-10-16T11:52:44.411109+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
          {
            id: 'ec6b6d97-93f6-44a6-b096-50cf309ef1cd',
            type: 'multiple_choice',
            question: 'What is the capital of France?',
            answer_options: ['Paris', 'Berlin', 'London'],
            answer: 'Paris',
            role: 'Travler',
            experience_level: 'entry_level',
            category: 'role_specific',
            label: 'skill',
            tags: ['geography', 'capital cities'],
            is_custom: true,
            created_at: '2023-10-26T08:05:43.040826+01:00',
            updated_at: '2023-10-26T08:05:43.451397+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
          {
            id: '6c0adeea-7baf-4180-886e-e3f0e0fd82b3',
            type: 'coding',
            question:
              'Write a Java function that calculates the factorial of a given number.',
            answer_options: [],
            answer: null,
            role: 'Network Engineer',
            experience_level: 'expert_level',
            category: 'role_specific',
            label: 'skill',
            tags: ['Routing and Switching', 'Python', 'Factorial Calculation'],
            is_custom: true,
            created_at: '2023-10-26T09:14:45.779464+01:00',
            updated_at: '2023-10-26T09:14:45.995846+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
        ],
      },
      {
        id: 'eb5c6da4-2681-487e-b21c-a48fcf6db9a6',
        section_name: '',
        no_multiple_choices: 2,
        no_essay: 2,
        no_coding: 2,
        test: [
          {
            id: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            name: 'Entry Level Network Engineer - Routing and Switching',
            description:
              "This test is designed to assess the candidate's knowledge and skills in the area of Routing and Switching for a Network Engineer role. The test will evaluate the candidate's understanding of network protocols, routing protocols, switching technologies, and troubleshooting techniques.",
            summary:
              "The purpose of this assessment is to determine the candidate's proficiency in Routing and Switching concepts and their ability to apply them in real-world scenarios. The assessment aims to identify candidates who possess the necessary skills to configure, manage, and troubleshoot network routers and switches.",
            skills_tested:
              '1. Understanding of TCP/IP and OSI models\r\n2. Knowledge of routing protocols such as OSPF and EIGRP\r\n3. Configuration and troubleshooting of VLANs and inter-VLAN routing\r\n4. Familiarity with switching technologies like STP, VLAN Trunking, and EtherChannel\r\n5. Ability to troubleshoot network connectivity issues\r\n6. Knowledge of network security principles and best practices',
            experience_level: 'entry_level',
            role: 'Network Engineer',
            tags: [],
            label: 'skills',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
          },
          {
            id: '7e784aa3-2c11-4946-bba5-7e74f83bb118',
            name: 'Expert Level Data Analysis Assessment',
            description:
              "This assessment is designed to evaluate the candidate's expertise in data analysis for the role of Data Analyst. It will test their ability to analyze and interpret complex data sets, identify trends and patterns, and provide actionable insights.",
            summary:
              "The purpose of this assessment is to assess the candidate's proficiency in data analysis and their ability to derive meaningful insights from large datasets. The assessment will evaluate their skills in data manipulation, statistical analysis, data visualization, and problem-solving.",
            skills_tested:
              '1. Proficiency in SQL and database querying\r\n2. Ability to clean and preprocess data\r\n3. Knowledge of statistical analysis techniques\r\n4. Experience with data visualization tools\r\n5. Problem-solving and critical thinking skills',
            experience_level: 'expert_level',
            role: 'Data Analyst',
            tags: [],
            label: 'skills',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
          },
        ],
        question_set: [
          {
            id: '07dcd9a0-8764-48b8-ad3b-b9ab52bf74ec',
            type: 'essay',
            question:
              'Discuss the advantages and disadvantages of static routing compared to dynamic routing.',
            answer_options: [],
            answer: null,
            role: 'Network Engineer',
            experience_level: 'entry_level',
            category: 'role_specific',
            label: 'skill',
            tags: [
              'Routing and Switching',
              'Static Routing',
              'Dynamic Routing',
            ],
            is_custom: false,
            created_at: '2023-10-16T11:52:44.387114+01:00',
            updated_at: '2023-10-16T11:52:44.387114+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
          {
            id: '19198efe-febb-4d39-b023-b5b74ba965db',
            type: 'essay',
            question:
              'Describe the process of troubleshooting network connectivity issues and the tools commonly used.',
            answer_options: [],
            answer: null,
            role: 'Network Engineer',
            experience_level: 'entry_level',
            category: 'role_specific',
            label: 'skill',
            tags: [
              'Routing and Switching',
              'Network Troubleshooting',
              'Connectivity Issues',
            ],
            is_custom: false,
            created_at: '2023-10-16T11:52:44.411109+01:00',
            updated_at: '2023-10-16T11:52:44.411109+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
          {
            id: '25d38f5e-663d-43b9-b31a-1a8bfaea18ac',
            type: 'coding',
            question:
              'Write a Python function that calculates the factorial of a given number.',
            answer_options: [],
            answer: null,
            role: 'Network Engineer',
            experience_level: 'entry_level',
            category: 'role_specific',
            label: 'skill',
            tags: ['Routing and Switching', 'Python', 'Factorial Calculation'],
            is_custom: false,
            created_at: '2023-10-16T11:52:44.411109+01:00',
            updated_at: '2023-10-16T11:52:44.411109+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
          {
            id: '2079e5c0-9813-40cb-96f8-9007341a8bc5',
            type: 'coding',
            question:
              'Write a Python function to calculate the average of a list of numbers.',
            answer_options: [],
            answer: null,
            role: 'Data Analyst',
            experience_level: 'expert_level',
            category: 'role_specific',
            label: 'skill',
            tags: ['data analysis', 'programming', 'python'],
            is_custom: false,
            created_at: '2023-10-16T12:10:08.255958+01:00',
            updated_at: '2023-10-16T12:10:08.255958+01:00',
            test: '7e784aa3-2c11-4946-bba5-7e74f83bb118',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
          {
            id: 'ec6b6d97-93f6-44a6-b096-50cf309ef1cd',
            type: 'multiple_choice',
            question: 'What is the capital of France?',
            answer_options: ['Paris', 'Berlin', 'London'],
            answer: 'Paris',
            role: 'Travler',
            experience_level: 'entry_level',
            category: 'role_specific',
            label: 'skill',
            tags: ['geography', 'capital cities'],
            is_custom: true,
            created_at: '2023-10-26T08:05:43.040826+01:00',
            updated_at: '2023-10-26T08:05:43.451397+01:00',
            test: '1dd8b524-9922-47df-bd2b-ae7e91ef8508',
            section: 'd77e587d-2ffe-4f9f-b8c7-02b49d921269',
            question_creator: null,
          },
        ],
      },
    ],
    name: 'Network Engineer assessment',
    role: 'Network Engineer',
    role_level: 'entry_level',
    time: '2023-11-01T11:20:10.941745+01:00',
    is_published: true,
    deadline: '2023-11-23T18:00:00+01:00',
    time_limit: null,
    created_at: '2023-11-01T11:20:10.941745+01:00',
    no_candidates: 2,
    updated_at: '2023-11-01T11:44:33.910200+01:00',
    creator: {
      id: 1,
      company_email: '<EMAIL>',
      verified: true,
      created_at: '2023-10-05T14:19:46.834863+01:00',
      updated_at: '2023-10-05T14:19:46.834863+01:00',
      user: 'db59f171-0c03-48ba-bf6b-87aeb4e1ebb9',
      company: 1,
    },
  },
};

export const questionTypeEnums = {
  multiple_choice: 'Multiplechoice Question',
  true_false: 'True or False',
  fill_in_the_blanks: 'Fill in the blank(s)',
  essay: 'Essay Question',
  multiple_response: 'Multiple Select',
  coding: 'Coding',
};
