'use client';

import React, { Children } from 'react';
import {
  <PERSON>,
  <PERSON>Angle<PERSON><PERSON><PERSON>,
  <PERSON>dialBar,
  RadialBarChart,
  RadialBarProps,
  ResponsiveContainer,
} from 'recharts';

interface Props {
  data: any[] | undefined;
  innerRadius?: string | number | undefined;
  first_text?: any;
  second_text?: any;
  outerRadius?: string | number | undefined;
  barSize?: string | number | undefined;
  progressColor?: string | undefined;
  children?: React.ReactNode;
}

const ProgressBar = ({
  data,
  first_text,
  innerRadius = '20%',
  second_text,
  outerRadius = '60%',
  barSize = 9,
  progressColor = '#755AE2',
  children,
  ...RadialBarProps
}: Props) => {
  // const data = [
  //     { name: 'L1', value: 50 }
  // ];

  const circleSize = 80;

  return (
    <div className="">
      {/* <ResponsiveContainer width="95%" height="400px"> */}
      <RadialBarChart
        width={circleSize}
        height={circleSize}
        cx={circleSize / 2}
        cy={circleSize / 2}
        innerRadius={innerRadius}
        outerRadius={outerRadius}
        barSize={barSize}
        data={data}
        startAngle={90}
        endAngle={-270}
        {...RadialBarProps}
      >
        <PolarAngleAxis
          type="number"
          domain={[0, 100]}
          angleAxisId={0}
          tick={false}
        />
        <RadialBar
          background
          dataKey="value"
          cornerRadius={circleSize / 2}
          fill={progressColor}
        />
      </RadialBarChart>
      {/* </ResponsiveContainer> */}
    </div>
  );
};

export default ProgressBar;
