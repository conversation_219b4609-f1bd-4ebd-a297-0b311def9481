import React, { useEffect } from 'react';
import { cn } from '@/utils';
import { UseCandidateStatusStore } from '../store';
import { useCurrentPageId } from '../store/currentPageId';

interface Props {
  isAssessmentActivated: boolean;
  questionsTest: any;
}
const AssessmentQuestionsLists: React.FC<Props> = ({
  isAssessmentActivated,
  questionsTest,
}) => {
  const { candidateStatus, setCandidateStatus } = UseCandidateStatusStore(
    state => ({
      candidateStatus: state.candidateStatus,
      setCandidateStatus: state.setCandidateStatus,
    })
  );
  const currentPageId = useCurrentPageId((state: any) => state.data);
  console.log(questionsTest)
  return (
    <>
      <h6 className="font-semibold">Questions by section</h6>
      <div className="flex flex-auto grid-cols-1 flex-col overflow-y-scroll bg-gradient-to-b from-transparent via-transparent to-white pt-4">
        {questionsTest?.map((item: any, index: number) => {
          return (
            <div
              className={`flex items-center gap-x-2 border-b-[0.5px] border-[#d6d6d6] py-3 ${
                item.id === questionsTest[currentPageId].id
                  ? 'text-black'
                  : 'text-helper-text'
              }`}
            >
              <div className="flex-1">
                <p
                  className={`2xl:text-lg ${
                    item.id === questionsTest[currentPageId].id
                      ? 'font-bold text-primary'
                      : 'text-helper-text'
                  }`}
                >
                  Q{index + 1}:
                </p>
              </div>

              <div
                className="tiptap "
                dangerouslySetInnerHTML={{
                  __html: `${item.question.slice(0, 50)} ${
                    item.question.length > 50 ? '...' : ''
                  }`,
                }}
              />
              <p className="flex-[5] max-2xl:text-[0.825rem]"></p>
            </div>
          );
        })}
      </div>
    </>
  );
};

export default AssessmentQuestionsLists;
