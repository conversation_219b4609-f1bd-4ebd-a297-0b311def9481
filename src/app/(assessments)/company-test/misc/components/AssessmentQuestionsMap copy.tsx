// 'use client';

// import React, { PropsWithChildren, useCallback, useEffect, useMemo, useState } from 'react';
// import { useAllAssessmentQuestions, useAllAssessmentSections, useAnsweredQuestions, useAssessmentDetails, UseCandidateStatusStore } from '../store';
// import { cn } from '@/utils';
// import { QuestionSet } from '../store/assessmentStore';
// import { useSendCandidateAnswers } from '../api/sendAnswer';
// import { useOfflineAnswersDetails } from '../store/offlneAnswers';
// import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/shared/tabs';
// import { ChevronLeft, ChevronRight } from 'lucide-react';
// import { debounce } from 'lodash';
// import { ToolTip } from '@/components/shared';



// const AssessmentQuestionsMap: React.FC<PropsWithChildren & { candidateIsInSession: boolean; }> = ({ candidateIsInSession, children }) => {
//     const assesmentDetails = useAssessmentDetails(state => state.assessmentDetails);
//     let questionNumber = 1;
//     const setCandidateStatus = UseCandidateStatusStore(state => state.setCandidateStatus);
//     const sections = useAllAssessmentSections();
//     const allQuestions = useAllAssessmentQuestions();
//     const currentQuestionId = UseCandidateStatusStore(state => state.candidateStatus.currentQuestionId);
//     const currentQuestion = allQuestions.find(question => question.id === currentQuestionId);
//     const answeredQuestions = useAnsweredQuestions();
//     const [activeTab, setActiveTabOrg] = useState(sections.find(section => section.question_set.map(({ id }) => id).includes(currentQuestionId))?.id || '');

//     const ref = React.useRef<HTMLDivElement>(null);
//     const isMobile = true; //useIsMobile();

//     useEffect(() => {
//         setActiveTab(sections.find(section => section.question_set.map(({ id }) => id).includes(currentQuestionId))?.id || '');
//     }, [currentQuestionId]);


//     const setActiveTab = useMemo(() => debounce(setActiveTabOrg, 300, { leading: true }), []);



//     const activeSectionIndex = sections.findIndex(section => section.id === activeTab);

//     return (
//         <div className='flex flex-col overflow-y-auto'>
//             <div className='sm:p-1 md:p-2 lg:p-4 bg-white overflow-x-hidden w-full flex flex-col'>
//                 <div className='lg:hidden mb-2'>
//                     {children}
//                 </div>
//                 <header>
//                     <h4 className='text-header-text text-center md:text-left text-sm sm:text-base'>
//                         Questions Map
//                     </h4>
//                     <p className='text-helper-text text-xs hidden sm:block'>
//                         Answered are {answeredQuestions.length}, unanswered are {allQuestions.length - answeredQuestions.length}, unanswered are {allQuestions.length - answeredQuestions.length}

//                     </p>
//                 </header>
//                 <h5 className='text-header-text text-center md:text-left text-xs sm:text-sm mt-4 mx-auto'>
//                     Section
//                 </h5>
//                 <Tabs value={activeTab} onValueChange={setActiveTab} className='flex flex-col h-full'>
//                     <TabsList className='flex gap-2'>
//                         {isMobile && activeSectionIndex === 0 && <TabsTrigger
//                             disabled={true}
//                             value={'prev'}
//                             className='text-xs p-1'>
//                             <ChevronLeft size={16} />
//                         </TabsTrigger>}
//                         {
//                             sections.map(({ id, section_name }, i) => {
//                                 if (isMobile && i === activeSectionIndex - 1) return (<TabsTrigger
//                                     disabled={!sections[activeSectionIndex - 1]}
//                                     value={id}
//                                     className='text-xs p-1'>
//                                     <ToolTip content={section_name}>
//                                         <ChevronLeft className='text-primary' size={16} />
//                                     </ToolTip>
//                                 </TabsTrigger>);

//                                 if (isMobile && i === activeSectionIndex + 1) return (<TabsTrigger
//                                     disabled={!sections[activeSectionIndex + 1]}
//                                     value={id}
//                                     className='text-xs p-1'>
//                                     <ToolTip content={section_name}>
//                                         <ChevronRight className='text-primary' size={16} />
//                                     </ToolTip>
//                                 </TabsTrigger>);

//                                 if (isMobile && i !== activeSectionIndex) return null;

//                                 return (
//                                     <TabsTrigger key={section_name} value={id} className='text-xs p-1 w-[50px]'>
//                                         {section_name}
//                                     </TabsTrigger>
//                                 );
//                             })
//                         }
//                         {isMobile && activeSectionIndex === sections.length - 1 && <TabsTrigger
//                             disabled={true}
//                             value='next'
//                             className='text-xs p-1'>
//                             <ChevronRight size={16} />
//                         </TabsTrigger>}
//                     </TabsList>
//                     {
//                         Object.entries(sections).map(([_, { id, question_set }], index) => (
//                             <TabsContent value={id} className={`grid grid-cols-1 gap-4 h-full ${activeTab === id ? 'grid' : 'hidden'}`}>
//                                 <div ref={ref as React.RefObject<HTMLDivElement>} className='h-full'>
//                                     <section key={index} className='py-4 grid gap-4'>
//                                         <article className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-x-4 gap-y-2'>
//                                             {
//                                                 question_set.map((question, innerIndex) => {
//                                                     const currentNumber = questionNumber++;
//                                                     return (
//                                                         <QuestionMapButton
//                                                             key={innerIndex}
//                                                             question={question}
//                                                             questionNumber={currentNumber}
//                                                         />
//                                                     );
//                                                 })
//                                             }
//                                         </article>
//                                     </section>
//                                 </div>
//                             </TabsContent>
//                         ))
//                     }
//                 </Tabs>
//             </div>
//         </div>
//     );
// };

// export default AssessmentQuestionsMap;




// const QuestionMapButton = ({ question, questionNumber }: { question: QuestionSet, questionNumber: number, }) => {

//     const setCandidateStatus = UseCandidateStatusStore(state => state.setCandidateStatus);
//     const isCandidateInSession = UseCandidateStatusStore(state => state.candidateStatus.isInSession);
//     const sections = useAllAssessmentSections();
//     const assessmentDetails = useAssessmentDetails(state => state.assessmentDetails);
//     const allQuestions = useAllAssessmentQuestions();
//     const currentQuestionId = UseCandidateStatusStore(state => state.candidateStatus.currentQuestionId);
//     const answeredQuestions = useAnsweredQuestions();
//     const isCurrentQuestion = currentQuestionId === question.id;
//     const candidateIsOnline = UseCandidateStatusStore(
//         state => state.candidateStatus.isOnline
//     );
//     const offlineAnswers = useOfflineAnswersDetails(state => state.data);
//     const setOfflineAnswers = useOfflineAnswersDetails(
//         state => state.setOfflineAnswersDetails
//     );
//     const { mutate: sendAnswer, isLoading: isAnswering } = useSendCandidateAnswers();


//     const handleButtonClick = async () => {
//         if (!isCandidateInSession) {
//             return;
//         }
//         const LocalCurrentQuestionId = currentQuestionId;
//         const currentQuestion = allQuestions.find(question => question.id === currentQuestionId);
//         setCandidateStatus({ currentQuestionId: question.id });

//         const data = {
//             candidate: assessmentDetails.candidate_email,
//             invite_id: assessmentDetails.invite_id,
//             section_id: sections.find(section => section.question_set.map(({ id }) => id).includes(LocalCurrentQuestionId))?.id || '',
//             question: LocalCurrentQuestionId,
//             answer: currentQuestion?.candidate_answer || ["-"]
//         };

//         if (LocalCurrentQuestionId) {
//             if (candidateIsOnline) {
//                 sendAnswer({
//                     candidate: assessmentDetails.candidate_email,
//                     invite_id: assessmentDetails.invite_id,
//                     section_id: sections.find(section => section.question_set.map(({ id }) => id).includes(LocalCurrentQuestionId))?.id || '',
//                     question: LocalCurrentQuestionId,
//                     answer: currentQuestion?.candidate_answer || ["-"]
//                 }, {
//                     onSuccess() {
//                         setCandidateStatus({ currentQuestionId: question.id });
//                     },
//                     onError() {
//                         setCandidateStatus({ currentQuestionId: question.id });

//                     },
//                 });
//             } else {
//                 setOfflineAnswers([...offlineAnswers, data]);
//                 setCandidateStatus({ currentQuestionId: question.id });
//             }

//         }
//     };



//     return (
//         <button
//             className={cn(
//                 "flex mx-auto h-7 w-7 cursor-pointer items-center justify-center rounded-md border border-[#D9D9D9] bg-white p-0",
//                 (answeredQuestions.map(({ id: question_id }) => question_id).includes(question.id) || isCurrentQuestion) && "border-primary",
//                 !isCandidateInSession && "opacity-50 cursor-not-allowed"
//             )}
//             onClick={handleButtonClick}
//         >
//             <div
//                 className={cn(
//                     "flex h-5 w-5 items-center justify-center rounded-md",
//                     answeredQuestions.map(({ id: question_id }) => question_id).includes(question.id) ? "bg-primary text-white" : "bg-white"
//                 )}
//             >
//                 <p className="text-xs font-medium">
//                     {questionNumber}
//                 </p>
//             </div>
//         </button>
//     );
// };