"use client";

import React from 'react';
import {
    Button,
    Input,
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/shared";
import { CalculatorIcon } from 'lucide-react';

function AssessmentCalculator() {
    return (
        <Popover>
            <PopoverTrigger asChild>
                <Button variant="outlined" size="constant" className="w-11 h-11 p-0">
                    <CalculatorIcon className="h-8 w-8" />
                    <span className="sr-only">Open calculator</span>
                </Button>
            </PopoverTrigger>
            <PopoverContent className="!w-auto min-h-[300px] max-h-[800px]">
                <Calcu />
            </PopoverContent>
        </Popover>
    );
}

export default AssessmentCalculator;

export function Calcu() {
    const [display, setDisplay] = React.useState('0');
    const [operator, setOperator] = React.useState('');
    const [prevValue, setPrevValue] = React.useState('');

    const handleNumberClick = (num: string) => {
        setDisplay(prev => prev === '0' ? num : prev + num);
    };

    const handleOperatorClick = (op: string) => {
        setOperator(op);
        setPrevValue(display);
        setDisplay('0');
    };

    const handleEqualsClick = () => {
        const current = parseFloat(display);
        const previous = parseFloat(prevValue);
        let result = 0;

        switch (operator) {
            case '+':
                result = previous + current;
                break;
            case '-':
                result = previous - current;
                break;
            case '*':
                result = previous * current;
                break;
            case '/':
                result = previous / current;
                break;
        }

        setDisplay(result.toString());
        setOperator('');
        setPrevValue('');
    };

    const handleClear = () => {
        setDisplay('0');
        setOperator('');
        setPrevValue('');
    };

    return (
        <div className="min-w-72 p-4 bg-white rounded-lg shadow-md">
            <Input
                className="mb-4 text-right text-xl font-bold"
                value={display}
                readOnly
            />
            <div className="grid grid-cols-4 gap-2">
                {['7', '8', '9', '/'].map((btn) => (
                    <Button
                        key={btn}
                        onClick={() => isNaN(parseInt(btn)) ? handleOperatorClick(btn) : handleNumberClick(btn)}
                        variant={isNaN(parseInt(btn)) ? "secondary" : "outlined"}
                    >
                        {btn}
                    </Button>
                ))}
                {['4', '5', '6', '*'].map((btn) => (
                    <Button
                        key={btn}
                        onClick={() => isNaN(parseInt(btn)) ? handleOperatorClick(btn) : handleNumberClick(btn)}
                        variant={isNaN(parseInt(btn)) ? "secondary" : "outlined"}
                    >
                        {btn}
                    </Button>
                ))}
                {['1', '2', '3', '-'].map((btn) => (
                    <Button
                        key={btn}
                        onClick={() => isNaN(parseInt(btn)) ? handleOperatorClick(btn) : handleNumberClick(btn)}
                        variant={isNaN(parseInt(btn)) ? "secondary" : "outlined"}
                    >
                        {btn}
                    </Button>
                ))}
                <Button onClick={() => handleNumberClick('0')} variant="outlined">0</Button>
                <Button onClick={() => handleNumberClick('.')} variant="outlined">.</Button>
                <Button onClick={handleEqualsClick} variant="default">=</Button>
                <Button onClick={() => handleOperatorClick('+')} variant="secondary">+</Button>
                <Button onClick={handleClear} variant="red" className="col-span-4">Clear</Button>
            </div>
        </div>
    );
}

