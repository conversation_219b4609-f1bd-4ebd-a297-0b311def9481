'use client';

import { ListI<PERSON>, XIcon } from 'lucide-react';
import React, { useCallback, useContext, useEffect, useMemo } from 'react';
import toast from 'react-hot-toast';
import { useAnsweredQuestionsDetails } from '@/app/(assessments)/company-test/misc/store/answeredQuestionsStore';
import { ProctoringOptions } from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import { Button, ToolTip } from '@/components/shared';
import { SubmitModalContext } from '../../layout';
import { useSendCandidateAnswers } from '../api/sendAnswer';
import {
  TSendProctorReportData,
  useSendProctoringReports,
} from '../api/sendProctoringReport';
import {
  useAllAssessmentQuestions,
  useAllAssessmentSections,
  useAnswerQuestion,
  useAssessmentDetails,
  useAssessmentSectionsStore,
  UseCandidateStatusStore,
  useProctoringReport,
} from '../store';
import { useOfflineAnswersDetails } from '../store/offlneAnswers';
import AssessmentCalculator from './AssessmentCalculator';
import {
  EssayAnswer,
  MultipleChoiceAnswer,
  MultipleResponseAnswer,
  TrueFalseAnswer,
} from './AssessmentQuestionAnswerTypes';
import AssessmentQuestionImages from './AssessmentQuestionImages';

interface Props {
  getProctoringData: () => TSendProctorReportData;
}

const AssessmentQuestionAnswer: React.FC<Props> = ({ getProctoringData }) => {
  const assesmentDetails = useAssessmentDetails(
    state => state.assessmentDetails
  );
  // const { removeFlags } = useProctoringReport(state => ({
  //   removeFlags: state.removeFlags,
  // }));
  const previouslyAnsweredQuestions = useAnsweredQuestionsDetails(
    state => state.data
  );

  const setCandidateStatus = UseCandidateStatusStore(
    state => state.setCandidateStatus
  );
  const candidateIsOnline = UseCandidateStatusStore(
    state => state.candidateStatus.isOnline
  );

  const panelOpen = useAssessmentSectionsStore(state => state.panelOpen);
  const togglePanel = useAssessmentSectionsStore(state => state.togglePanel);
  const sections = useAllAssessmentSections();
  const allQuestions = useAllAssessmentQuestions();
  const currentQuestionId = UseCandidateStatusStore(
    state => state.candidateStatus.currentQuestionId
  );

  // console.log(currentQuestionId, "ASSESSMENT QUESTION ANSWER CURRENT QUESTION ID")

  const currentQuestion = allQuestions.find(
    question => question.id === currentQuestionId
  );
  const currentSection = sections.find(section =>
    section.question_set.find(question => question.id === currentQuestionId)
  );
  const currentQuestionNo =
    allQuestions.findIndex(question => question.id === currentQuestionId) + 1;
  const currentQuestionIndex = useMemo(
    () => allQuestions.findIndex(question => question.id === currentQuestionId),
    [currentQuestionId]
  );

  const getValidNextQuestionId = useCallback(
    (nextQuestionIndex: number, prev = false) => {
      // console.log(nextQuestionIndex, 'NEXT QUESTION INDEX');
      let question = allQuestions[nextQuestionIndex];
      if (question) {
        // console.log(question, 'NEXT INDEX QUESTION');
        if (!previouslyAnsweredQuestions.includes(question.id)) {
          return question.id;
        } else {
          if (!prev) {
            return getValidNextQuestionId(nextQuestionIndex + 1, prev);
          } else {
            return getValidNextQuestionId(nextQuestionIndex - 1, prev);
          }
        }
      }
    },
    [allQuestions]
  );

  const nextQuestionId = useMemo(() => {
    let nextQuestionIndex = currentQuestionIndex + 1;
    // let nextQuestion = allQuestions[nextQuestionIndex];
    // return nextQuestion?.id;
    return getValidNextQuestionId(nextQuestionIndex);
  }, [currentQuestionIndex, allQuestions]);

  const previousQuestionId = useMemo(() => {
    // const previousQuestion = allQuestions[currentQuestionIndex - 1];
    // return previousQuestion?.id;
    return getValidNextQuestionId(currentQuestionIndex - 1, true);
  }, [currentQuestionIndex, allQuestions]);

  const renderAnswerSheet = () => {
    switch (currentQuestion?.type) {
      case 'essay':
        return <EssayAnswer question={currentQuestion} />;
      case 'multiple_choice':
        return <MultipleChoiceAnswer question={currentQuestion} />;
      case 'true_false':
        return <TrueFalseAnswer question={currentQuestion} />;
      case 'fill_in_the_blanks':
        return <EssayAnswer question={currentQuestion!} />;
      case 'multiple_response':
        return <MultipleResponseAnswer question={currentQuestion!} />;
      default:
        return <EssayAnswer question={currentQuestion!} />;
    }
  };

  // const [isSubmitModalOpen, setIsSubmitModalOpen] = useState(false);
  // const {
  //   state: isSubmitModalOpen,
  //   setTrue: openSubmitModal,
  //   setState: setSubmitModalState,
  // } = useBooleanStateControl();
  const { mutate: sendReport, isLoading: reportLoading } =
    useSendProctoringReports();
  const { mutate: sendAnswer, isLoading: isAnswering } =
    useSendCandidateAnswers();
  const offlineAnswers = useOfflineAnswersDetails(state => state.data);
  const setOfflineAnswers = useOfflineAnswersDetails(
    state => state.setOfflineAnswersDetails
  );

  const openSubmitModal = useContext(SubmitModalContext).setIsSubmitModalOpen;

  const handleNextPrevious = (next: boolean, submit = false) => {
    const data = {
      candidate: assesmentDetails.candidate_email,
      invite_id: assesmentDetails.invite_id,
      section_id: currentSection?.id as string,
      question: currentQuestionId,
      answer: currentQuestion?.candidate_answer || [],
    };
    if (candidateIsOnline) {
      if (submit) {
        sendAnswer(data, {
          onSuccess: () => {
            if (openSubmitModal) {
              openSubmitModal(true);
            }
          },
          onError: () => {
            // toast.error('failed to save answer');
          },
        });
      } else {
        // ===================
        // ====-=================
        // THe point of this line is to run bothe of the
        // request in parrallel since they aren't dependent on each other
        if (next) {
          if (nextQuestionId) {
            setCandidateStatus({ currentQuestionId: nextQuestionId });
          }
        } else {
          if (previousQuestionId) {
            setCandidateStatus({
              currentQuestionId: previousQuestionId,
            });
          }
        }

        Promise.all([
          // Send proctoring data
          new Promise((resolve, reject) => {
            const payload = getProctoringData();

            sendReport(payload, {
              //@ts-ignore
              onSuccess: data => {
                resolve(data);
              },
            });
          }),

          // Send answers
          new Promise((resolve, reject) => {
            sendAnswer(data, {
              onSuccess: () => {
                resolve(true);
              },
              onError: () => {
                reject();
              },
            });
          }),
        ]);
      }
    } else {
      setOfflineAnswers([...offlineAnswers, data]);
      if (submit) {
        if (openSubmitModal) {
          openSubmitModal(true);
        }
        // setIsSubmitModalOpen(true);
      } else {
        next
          ? setCandidateStatus({ currentQuestionId: nextQuestionId })
          : setCandidateStatus({ currentQuestionId: previousQuestionId });
      }
    }
  };

  useEffect(() => {
    // Reset the current question's answer when the question changes
    if (currentQuestion) {
      useAnswerQuestion(
        currentQuestion.id,
        currentQuestion.candidate_answer || []
      );
    }
  }, [currentQuestionId]);

  return (
    <div className="relative flex h-full flex-col overflow-hidden">
      <header className="flex items-center justify-between pb-4">
        <h2 className="px-4 text-lg text-helper-text">
          Section:{' '}
          <span className="text-header-text">
            {currentSection?.section_name} (
            {currentSection?.question_set.length})
          </span>
        </h2>
        <ToolTip
          className="block lg:hidden"
          asChild
          content="View all questions in this section"
        >
          <Button variant="outlined" size="capsule" onClick={togglePanel}>
            {panelOpen ? <XIcon size={24} /> : <ListIcon size={24} />}
          </Button>
        </ToolTip>
      </header>

      <section className="relative flex h-full grow flex-col overflow-y-scroll rounded-2xl bg-white">
        <main className="grow divide-y p-4 xl:px-8 ">
          <div>
            <p className="text-body-text">
              Question {currentQuestionNo} of {allQuestions.length}
            </p>
            {currentQuestion?.has_image && currentQuestion?.images && (
              <AssessmentQuestionImages />
            )}
            <div
              className="px-2 pb-5 pt-3 text-sm text-header-text md:text-base"
              dangerouslySetInnerHTML={{
                __html: currentQuestion?.question || '',
              }}
            />
          </div>
          <div className="pt-5">{renderAnswerSheet()}</div>
        </main>
        <footer className="sticky bottom-0 flex items-center justify-end gap-5 bg-white p-4 shadow-sm">
          {currentQuestionIndex > 0 && (
            <Button
              className="w"
              variant="extralight"
              onClick={() => handleNextPrevious(false)}
            >
              Previous Question
            </Button>
          )}
          {currentQuestionIndex === allQuestions.length - 1 ? (
            <Button
              className="w"
              onClick={() => handleNextPrevious(true, true)}
            >
              Submit Test
            </Button>
          ) : (
            <Button className="w" onClick={() => handleNextPrevious(true)}>
              Next Question
            </Button>
          )}
        </footer>
      </section>

      <div className="absolute bottom-16 right-0 p-8">
        <AssessmentCalculator />
      </div>
    </div>
  );
};

export default AssessmentQuestionAnswer;
