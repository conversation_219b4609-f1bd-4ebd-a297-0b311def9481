import { Dialog } from '@headlessui/react';
import { useRouter } from 'next/navigation';
import React, { SetStateAction, useEffect, useState, useContext } from 'react';
import toast from 'react-hot-toast';
import ReactLoading from 'react-loading';
import { Spinner } from '@/components/shared/icons';
import Loader from '@/components/shared/loader';
import { cn } from '@/utils';
import { getDeviceType } from '@/utils/strings';
import { useSendProctoringReports } from '../api/sendProctoringReport';
import { useSubmitAssessment } from '../api/submitAssessment';
import { UseCandidateStatusStore } from '../store';
import { useAssessmentDetails } from '../store/assessmentStore';
import { NetworkStoreProps, useNetworkDetails } from '../store/networkStore';
import { useProctoringReport } from '../store/proctoringReportStore';
import { SocketProgressEvent } from '../utils/websocketUrl';
import { useMQTTTrackingService } from '../hooks/useMQTTTrackingService';

interface SubmitTestModalProps {
  isOpen: boolean;
  setIsOpen: React.Dispatch<SetStateAction<boolean>>;
  openSuccess: () => void;

  activateAssessment: () => void;
  deactivateAssessment: () => void;
}

const SubmitTestModal: React.FunctionComponent<SubmitTestModalProps> = ({
  isOpen,
  setIsOpen,
  openSuccess,
  deactivateAssessment,
  activateAssessment,
}) => {
  const closeSubmitModal = () => {
    setIsOpen(false);
  };

  const handleOpenSuccessModal = () => {
    openSuccess();
  };
  const router = useRouter();

  const { sendMessage: sendSocketMessage } = useMQTTTrackingService();

  const { candidateStatus, setCandidateStatus } = UseCandidateStatusStore();
  const { assessmentDetails } = useAssessmentDetails();
  const { proctoringReport, resetProctoringReport } = useProctoringReport();

  const { mutate: runSubmitAssessment, isLoading: submitLoading } =
    useSubmitAssessment();
  const { mutate: sendReport, isLoading: reportLoading } =
    useSendProctoringReports();
  const [location, setLocation] = useState<any>();

  const handleSubmitAssessment = async () => {
    deactivateAssessment();
    Promise.all([
      new Promise((resolve, reject) => {
        sendReport(
          {
            candidate_email: assessmentDetails?.candidate_email,
            assessment: assessmentDetails?.assessment?.id,
            device_used: getDeviceType(),
            webcam_enabled: true,
            location: proctoringReport.location,
            mouse_out_of_window: proctoringReport.mouse_out_of_window,
            tab_switched: proctoringReport.tab_switched,
            id: proctoringReport.id as string,
            // full_screen_tolerance_used:
            // proctoringReport.full_screen_tolerance_used,
            // window_change_tolerance_used:
            // proctoringReport.window_change_tolerance_used,
            // was_forced_submission_full_screen:
            //   proctoringReport.was_forced_submission_full_screen,
            // was_forced_submission_window_change:
            //   proctoringReport.was_forced_submission_window_change,
            // was_forced_submission_bulk_proctor:
            //   proctoringReport.was_forced_submission_bulk_proctor,
            was_forced_submission: proctoringReport.was_forced_submission,

            // bulk_proctor_used: proctoringReport.bulk_proctor_used,
            // bulk_proctor_option: proctoringReport.bulk_proctor_option,
          },
          {
            onSuccess: () => {
              setCandidateStatus({ isReturning: false });
              resolve(true);
            },
            onError: (error: any) => {
              toast.error(error.message as string);
              reject(false);
            },
          }
        );
      }),

      new Promise((resolve, reject) => {
        runSubmitAssessment(
          {
            candidate_email: assessmentDetails?.candidate_email,
            invite_id: assessmentDetails?.invite_id,
            assessment_id: assessmentDetails.assessment.id,
            result_id: assessmentDetails.result_id,
            candidate_name: assessmentDetails.candidate_name,
          },
          {
            onSuccess: async (data) => {
              console.log(data, 'DATA RESPONSE');
              if (data.status === 200) {

                await sendSocketMessage(SocketProgressEvent.ASSESSMENT_SUBMITTED);

                openSuccess();
                resolve(true);
              } else {
                toast.error(data.data.error.message);
                activateAssessment();
                reject(false);
              }
            },
            onError: (error: any) => {
              console.log(error, 'ERROR FROM ASSESSMENT');
              activateAssessment();
              toast.error(error.message as string);
            },
          }
        );
      }),
    ]);
    // runSubmitAssessment(
    //   {
    //     candidate_email: assessmentDetails?.candidate_email,
    //     invite_id: assessmentDetails?.invite_id,
    //     assessment_id: assessmentDetails.assessment.id,
    //     result_id: assessmentDetails.result_id,
    //     candidate_name: assessmentDetails.candidate_name,
    //   },
    //   {
    //     onSuccess: data => {
    //       if (data.status === 200) {
    //         router.push('/company-test/submitted');
    //       } else {
    //         toast.error(data.data.error.message);
    //       }
    //       localStorage.clear();
    //     },
    //     onError: (error: any) => {
    //       toast.error(error.message as string);
    //     },
    //   }
    // );
  };

  React.useEffect(() => {
    // const getLocation = () => {
    if ('geolocation' in navigator) {
      navigator.geolocation.getCurrentPosition(
        position => {
          const { latitude, longitude } = position.coords;
          setLocation({ latitude, longitude, error: '' });
        },
        error => {
          setLocation({ latitude: 0, longitude: 0, error: error.message });
        }
      );
    } else {
      setLocation({
        latitude: 0,
        longitude: 0,
        error: 'Geolocation is not supported',
      });
    }
    // Use the cleanup function to prevent further calls after component unmounts
    return () => {
      // Optionally, you can clean up any resources here
    };
  }, [setLocation, location]);

  return (
    <Dialog open={isOpen} onClose={() => { }}>
      <div className="fixed inset-0 z-30 bg-[#000000aa]">
        <Dialog.Panel
          className={
            'absolute left-1/2 top-1/2 flex h-[19.5rem] w-[30rem] -translate-x-1/2 -translate-y-1/2 flex-col rounded-2xl bg-[#F5F3FF]'
          }
        >
          <>
            <div className="flex h-[64px] items-end justify-between rounded-t-2xl bg-primary px-10 py-[0.87rem] text-white">
              <p>Submit Test</p>
              <button
                className="flex h-8 w-[4.6rem] items-center justify-center rounded-md bg-[#F5F3FF33]"
                onClick={closeSubmitModal}
              >
                close
              </button>
            </div>
            {!reportLoading && !submitLoading ? (
              <>
                <div className="flex h-full flex-col items-center justify-center p-5">
                  <p className="text-center text-xl text-primary">
                    You're about to submit this test
                  </p>
                  <p className="text-center text-sm text-helper-text">
                    By submitting, you confirm that you have completed all the
                    required questions. Please note that this action is final
                    and cannot be undone
                  </p>
                </div>
              </>
            ) : (
              <div className="flex h-full w-full items-center justify-center">
                <Spinner color="#3C1356" height={50} width={50} />
              </div>
            )}
            <div className="flex h-[5rem] justify-end gap-x-4 rounded-b-2xl bg-white p-5 font-light">
              <button
                disabled={reportLoading || submitLoading}
                className="rounded-md bg-primary-light p-2 px-3 text-primary"
                onClick={closeSubmitModal}
              >
                Not done
              </button>

              <button
                className={cn('rounded-md bg-primary p-2 px-3 text-white', {
                  'cursor-not-allowed bg-primary-darker':
                    !candidateStatus.isOnline,
                })}
                onClick={handleSubmitAssessment}
                disabled={
                  !candidateStatus.isOnline || reportLoading || submitLoading
                }
              >
                Submit Assessment
              </button>
            </div>
          </>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default SubmitTestModal;
