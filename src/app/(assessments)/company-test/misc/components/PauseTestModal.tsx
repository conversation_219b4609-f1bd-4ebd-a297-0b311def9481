import { Dialog } from '@headlessui/react';
import { get } from 'js-cookie';
import moment from 'moment';
import { useRouter } from 'next/navigation';
import React, { SetStateAction } from 'react';
import toast from 'react-hot-toast';
import ReactLoading from 'react-loading';
import { getDeviceType } from '@/utils/strings';
import { usePauseAssessment } from '../api/pauseAssessment';
import { useSendProctoringReports } from '../api/sendProctoringReport';
import {
  useAssessmentDetails,
  UseCandidateStatusStore,
  useProctoringReport,
} from '../store';
import CountdownC from './CountdownC';

interface PauseModalProps {
  isOpen: boolean;
  closeModal: () => void;
}

const PauseModal: React.FunctionComponent<PauseModalProps> = ({
  isOpen,
  closeModal,
}) => {
  const router = useRouter();
  const { assessmentDetails } = useAssessmentDetails();
  const { candidateStatus, setCandidateStatus, clearCandidateStatus } =
    UseCandidateStatusStore();
  const { proctoringReport, resetProctoringReport } = useProctoringReport();

  const { mutate: runPauseAssessment, isLoading: isPausing } =
    usePauseAssessment();
  const { mutate: sendProctoringReport, isLoading: isSendingProctoringReport } =
    useSendProctoringReports();

  const handlePauseAssessment = () => {
    sendProctoringReport(
      {
        candidate_email: assessmentDetails?.candidate_email,
        assessment: assessmentDetails?.assessment?.id,
        device_used: getDeviceType(),
        webcam_enabled: true,
        location: proctoringReport.location,
        mouse_out_of_window: proctoringReport.mouse_out_of_window,
        tab_switched: proctoringReport.tab_switched,
        id: proctoringReport.id as string,
        // full_screen_tolerance_used: proctoringReport.full_screen_tolerance_used,
        // window_change_tolerance_used:
        //   proctoringReport.window_change_tolerance_used,
        // was_forced_submission_full_screen:
        //   proctoringReport.was_forced_submission_full_screen,
        // was_forced_submission_window_change:
        //   proctoringReport.was_forced_submission_window_change,
        // was_forced_submission_bulk_proctor:
        //   proctoringReport.was_forced_submission_bulk_proctor,

        was_forced_submission: proctoringReport.was_forced_submission,

        // bulk_proctor_option: proctoringReport.bulk_proctor_option,
        // bulk_proctor_used: proctoringReport.bulk_proctor_used,
      },
      {
        onSuccess() {
          runPauseAssessment(
            {
              candidate_email: assessmentDetails.candidate_email,
              candidate_name: assessmentDetails.candidate_name,
              assessment: assessmentDetails.assessment.id,
              result: assessmentDetails.result_id,
              invite: assessmentDetails.invite_id,
              time_left: candidateStatus.timeLeft,
              proctoring_report: proctoringReport.id as string,
            },
            {
              onSuccess: data => {
                router.replace('/login/talent');
                resetProctoringReport();
                clearCandidateStatus();
              },
              onError: (error: any) => {
                toast.error(error?.response.error.message);
              },
            }
          );
        },
      }
    );
  };

  return (
    <Dialog open={isOpen} onClose={closeModal}>
      <div className="fixed inset-0 z-30 bg-[#000000aa] backdrop-blur-lg">
        <Dialog.Panel
          className={
            'absolute left-1/2 top-1/2 flex h-max w-[30rem] -translate-x-1/2 -translate-y-1/2 flex-col rounded-2xl bg-[#F5F3FF]'
          }
        >
          <>
            <div className="flex h-[64px] items-end justify-between rounded-t-2xl bg-primary px-10 py-[0.87rem] text-white">
              <p>Pause assessment</p>
              <button
                className="flex h-8 w-[4.6rem] items-center justify-center rounded-md bg-[#F5F3FF33]"
                onClick={closeModal}
              >
                close
              </button>
            </div>
            {isPausing || isSendingProctoringReport ? (
              <div className="flex h-full w-full items-center justify-center p-20">
                <ReactLoading
                  height={50}
                  width={50}
                  type="spin"
                  color="#3C1356"
                />
              </div>
            ) : (
              <div className="flex flex-col gap-5 p-5 px-8">
                <div className="flex h-full flex-col ">
                  <p className="text-lg text-primary">
                    Sure you want to pause this assessment ?
                  </p>

                  <div>
                    <h6 className="text-sm text-helper-text">Note:</h6>
                    <ol className="list-decimal px-5 py-0 text-sm text-[#675E8B]">
                      <li>
                        By pausing this assessment all answered questions cannot
                        be changed
                      </li>
                      <li>You can only pause the assessment once</li>
                      <li>Used time cannot be gotten back</li>
                    </ol>
                  </div>
                </div>

                <section className=" mt-4">
                  <p className="text-sm">
                    Assessment Time:{' '}
                    <span className="font-semibold">
                      {moment
                        .utc(assessmentDetails?.assessment?.time_limit * 1000)
                        .format('HH [hours and ] mm [minutes]')}
                    </span>
                  </p>
                  <div className="flex items-center gap-x-2.5 text-sm">
                    Time left:{' '}
                    <span>
                      <CountdownC />
                    </span>
                  </div>
                </section>
              </div>
            )}
            <div className="flex h-[5rem] justify-end gap-x-4 rounded-b-2xl bg-white p-5 font-light">
              <button
                className="rounded-md bg-primary-light p-2 px-3 text-primary"
                onClick={closeModal}
              >
                Cancel
              </button>
              <button
                className="rounded-md bg-primary p-2 px-3 text-white"
                onClick={handlePauseAssessment}
              >
                Pause Assessment
              </button>
            </div>
          </>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default PauseModal;
