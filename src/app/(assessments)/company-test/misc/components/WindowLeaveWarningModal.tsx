import { Dialog } from '@headlessui/react';
import React, { SetStateAction, useState } from 'react';

interface WindowLeaveWarningProps {
  isOpen: boolean;
  setIsOpen: React.Dispatch<SetStateAction<boolean>>;
  limit?: number;
}

const WindowLeaveWarning: React.FunctionComponent<WindowLeaveWarningProps> = ({
  isOpen,
  setIsOpen,
  limit,
}) => {
  const closeModal = () => {
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onClose={() => {}}>
      <div className="fixed inset-0 z-30 bg-[#000000aa]">
        <Dialog.Panel
          className={
            'absolute left-1/2 top-1/2 flex h-[19.5rem] w-[30rem] -translate-x-1/2 -translate-y-1/2 flex-col rounded-2xl bg-white'
          }
        >
          <>
            <div className="flex h-[64px] items-end justify-between rounded-t-2xl bg-[#450A0A] px-10 py-[0.87rem] text-white">
              <p>Window/tab change</p>
            </div>

            <>
              <div className="flex h-full flex-col items-start justify-start p-5">
                <p className="text-left text-xl font-medium text-[#0E0E2C]">
                  Do not exit leave this window/tab!
                </p>
                <p className="mt-2 text-left text-sm font-normal text-helper-text">
                  Note:
                </p>
                <p className="text-left text-sm font-normal text-helper-text">
                  You are prohibited from exiting full-screen mode during this
                  assessment. Failure to comply will lead to your assessment
                  being submitted on the{' '}
                  <span className="font-bold">{limit}</span> occurrence of such
                  action.
                </p>
              </div>
            </>

            <div className="flex h-[5rem] justify-end gap-x-4 rounded-b-2xl bg-[#FEF2F2] p-5 font-light">
              <button
                className="rounded-md bg-white p-2 px-3 text-[#450A0A]"
                //@ts-ignore
                onClick={() => {
                  closeModal();
                }}
              >
                Continue assessment
              </button>
            </div>
          </>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default WindowLeaveWarning;
