'use client';

import React from 'react';
import { useAllAssessmentQuestions, useAllAssessmentSections, useAssessmentDetails, UseCandidateStatusStore } from '../store';
import { cn } from '@/utils';
import { stripHtml } from '@/utils/strings';
interface AssessmentQuestionsListProps {
    children: React.ReactNode;
    isAssessmentActivated: boolean;
}
const AssessmentQuestionsList = ({ children, isAssessmentActivated }: AssessmentQuestionsListProps) => {
    const assesmentDetails = useAssessmentDetails(state => state.assessmentDetails);
    const setCandidateStatus = UseCandidateStatusStore(state => state.setCandidateStatus);
    const isInSession = UseCandidateStatusStore(state => state.candidateStatus.isInSession);
    const sections = useAllAssessmentSections();
    const allQuestions = useAllAssessmentQuestions();
    const currentQuestionId = UseCandidateStatusStore(state => state.candidateStatus.currentQuestionId);
    const questionsBySection = sections.find((section) => section.question_set.find((question) => question.id === (currentQuestionId || allQuestions[0].id)));
    const currentQuestion = allQuestions.find(question => question.id === currentQuestionId);




    return (

        <div
            className={cn(
                'relative flex h-full flex-col overflow-y-scroll rounded-lg bg-white p-4 2xl:p-6',
                (!isInSession || !isAssessmentActivated) && 'blur-[5px] pointer-events-none'
            )}
        >
            <header>
                <h4 className='text-header-text text-base'>
                    Questions by section
                </h4>
            </header>

            <section className='divide-y-[0.5px] flex flex-auto grid-cols-1 flex-col overflow-y-scroll bg-gradient-to-b from-transparent via-transparent to-white pt-1'>
                {
                    questionsBySection?.question_set.map((question, index: number) => {
                        const currentQuestionNumber = sections
                            .slice(0, sections.findIndex(section => section.id === questionsBySection.id))
                            .reduce((acc, section) => acc + section.question_set.length, 0) + index + 1;

                        return (
                            <div
                                className={cn("grid grid-cols-[minmax(30px,0.15fr),1fr] items-center gap-x-2 py-3", question.id === currentQuestionId ? 'text-black' : 'text-helper-text')}
                                key={index}
                            >
                                <p
                                    className={cn(
                                        "2xl:text-lg font-bold", question.id === currentQuestionId ? 'text-primary' : 'text-helper-text'
                                    )}
                                >
                                    Q{currentQuestionNumber}:
                                </p>

                                <p className={cn("grow text-xs",
                                    question.id === currentQuestionId ? 'text-header-text' : 'text-helper-text'

                                )}>
                                    {stripHtml(question.question).substring(0, 100)} {question.question.length > 100 ? '...' : ''}
                                </p>
                            </div>
                        );
                    })
                }
            </section>

            {
                // isInSession && isAssessmentActivated &&
                <div className={cn('z-10 mt-5 h-max w-full')}>
                    {children}
                </div>
            }
        </div>

    );
};

export default AssessmentQuestionsList;