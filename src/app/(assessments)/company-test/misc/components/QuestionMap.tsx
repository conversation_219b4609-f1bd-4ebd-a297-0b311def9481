import React, { SetStateAction, useEffect, useState } from 'react';
import { cn } from '@/utils';
import { useSendCandidateAnswers } from '../api/sendAnswer';
import { useAssessmentDetails } from '../store/assessmentStore';
import { useCurrentPageId } from '../store/currentPageId';
import { NetworkStoreProps, useNetworkDetails } from '../store/networkStore';
import {
  OfflineAnswersStoreProps,
  useOfflineAnswersDetails,
} from '../store/offlneAnswers';
import { UseCandidateStatusStore } from '../store';

export interface QuestionMapProps {
  noOfSelected: number;
  questionsTest: any;
  questionAndSection: any;
  currentSection: any;

  // offlineAnswers: any;
  previousAnsweredQuestions: any;
}

const QuestionMap: React.FunctionComponent<QuestionMapProps> = ({
  noOfSelected,
  questionsTest,
  questionAndSection,

  currentSection,
  // offlineAnswers,
  previousAnsweredQuestions,
}) => {
  const { assessmentDetails } = useAssessmentDetails();
  const { candidateStatus } = UseCandidateStatusStore()

  const { mutate: sendAnswer, isLoading: sendLoading } = useSendCandidateAnswers();

  const currentPageId = useCurrentPageId((state: any) => state.data);
  const setCurrentPageId = useCurrentPageId(
    (state: any) => state.setCurrentPageId
  );

  const offlineAnswers = useOfflineAnswersDetails(
    (state: OfflineAnswersStoreProps) => state.data
  );
  const setOfflineAnswers = useOfflineAnswersDetails(
    (state: OfflineAnswersStoreProps) => state.setOfflineAnswersDetails
  );
  return (
    <div className="flex h-full min-h-[500px] flex-col overflow-y-scroll rounded-lg bg-white p-6">
      <p className="font-bold">Question Map</p>
      <p className="text-xs text-helper-text">
        ({noOfSelected}) answered and (
        {(questionsTest?.length as number) - noOfSelected}) unasnswered
      </p>

      <div className="h-full overflow-y-scroll">
        {questionAndSection?.map((section: any, index: any) => {
          return (
            <>
              <p className="text-xs font-semibold">{section.section_name || `Section (${index + 1})`}</p>
              <div className="grid grid-cols-2 gap-2 overflow-y-scroll py-2 pb-3 lg:grid-cols-4">
                {section.questionObjects.map((item: any, index: number) => {
                  const [isQuestionAnswered, setIsQuestionAnswered] = useState(
                    questionsTest.find(
                      (question: any) => question.id === item.id
                    )?.selected.length > 0
                  );
                  useEffect(() => {
                    setIsQuestionAnswered(
                      questionsTest.find(
                        (question: any) => question.id === item.id
                      )?.selected.length > 0
                    );
                  }, [questionsTest]);
                  return (
                    <div
                      className={`flex h-6 w-6 cursor-pointer items-center justify-center rounded-md border border-[#D9D9D9] bg-white p-0`}
                      onClick={() => {
                        const answer = {
                          candidate: assessmentDetails?.candidate_email,
                          invite_id: assessmentDetails.invite_id,
                          section_id: currentSection?.id as string,
                          question: questionsTest[currentPageId].id,
                          answer: questionsTest[currentPageId].selected,
                        };
                        const setPage = () => {
                          setCurrentPageId(
                            questionsTest.findIndex(
                              (question: any) => question.id === item.id
                            )
                          );
                        };

                        if (
                          !previousAnsweredQuestions.includes(item.id) &&
                          candidateStatus?.isInSession
                        ) {
                          if (candidateStatus.isOnline) {
                            sendAnswer(answer, {
                              onSuccess: () => {
                                setPage();
                              },
                            });
                          } else {
                            setOfflineAnswers([...offlineAnswers, answer]);
                            setPage();
                          }
                        }
                      }}
                    >
                      <div
                        className={cn(
                          `flex h-5 w-5 items-center justify-center rounded-md ${isQuestionAnswered
                            ? 'bg-primary text-white'
                            : 'bg-white'
                          }`,
                          {
                            'cursor-not-allowed bg-gray-300':
                              previousAnsweredQuestions.includes(item.id),
                          }
                        )}
                      >
                        <p className="text-sm font-semibold">
                          {questionsTest.findIndex(
                            (question: any) => question.id === item.id
                          ) + 1}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </>
          );
        })}
      </div>
    </div>
  );
};

export default QuestionMap;
