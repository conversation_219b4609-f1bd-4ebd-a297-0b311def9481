import { useState, useEffect, useRef } from 'react';

interface SocketOptions {
    url: string;
    onMessage: (data: any) => void;
    maxRetries?: number;
    retryDelay?: number;
    onStart?: boolean;
}

const useWebsocket = ({
    url,
    onMessage,
    maxRetries = 5,
    retryDelay = 1000,
    onStart = true
}: SocketOptions) => {
    const socketRef = useRef<WebSocket | null>(null);
    const [isConnected, setIsConnected] = useState(false);
    const [retryCount, setRetryCount] = useState(0);

    const connect = () => {
        if (isConnected) return;
        const ws = new WebSocket(url);

        ws.onopen = () => {
            console.error('WebSocket OPEN HOOK');

            setIsConnected(true);
            setRetryCount(0);
        };

        ws.onmessage = (event) => {
            onMessage(JSON.parse(event.data));
        };

        ws.onclose = () => {
            setIsConnected(false);
            //handleRetries();
        };

        ws.onerror = (error) => {
            console.error('WebSocket error HOOK:', error);
            setIsConnected(false);
            handleRetries();
        };

        socketRef.current = ws
    };

    const handleRetries = () => {
        if (isConnected) return;
        if (retryCount >= maxRetries) {
            console.error('Max retries reached. Giving up.');
            return;
        }

        const delay = retryDelay * Math.pow(2, retryCount); // Exponential backoff
        console.log(`Retrying connection in ${delay}ms (attempt ${retryCount + 1}/${maxRetries})`);

        setTimeout(() => {
            connect();
            setRetryCount(prev => prev + 1);
        }, delay);
    };

    useEffect(() => {
        if (onStart) {
            connect();
        }

        return () => {
            if (socketRef.current) {
                socketRef.current.close();
            }
        };
    }, []);

    return {
        socket: socketRef.current,
        isConnected,
        retryCount,
        connect
    };
};

export default useWebsocket;
