'use client';

import { useState } from 'react';
import { toast } from 'react-hot-toast';
import { getDeviceType } from '@/utils/strings';
import { useSendProctoringReports } from '../../api/sendProctoringReport';
import { useSubmitAssessment } from '../../api/submitAssessment';
import { TAssessmentStore } from '../../store/assessmentStore';
import { ProctoringReportState } from '../../store/proctoringReportStore';

interface UseAssessmentSubmissionProps {
  assessmentDetails: TAssessmentStore;
  proctoringReport: ProctoringReportState;
  resetProctoringReport: () => void;
  setCandidateStatus: (status: { isReturning: boolean }) => void;
  nextStep: () => void;
}

export const useAssessmentSubmission = ({
  assessmentDetails,
  proctoringReport,
  resetProctoringReport,
  setCandidateStatus,
  nextStep,
}: UseAssessmentSubmissionProps) => {
  const [location, setLocation] = useState<any>();

  const { mutate: runSubmitAssessment, isLoading: isUbmittingAssessment } =
    useSubmitAssessment();
  const { mutate: sendReport, isLoading: isSendingReport } =
    useSendProctoringReports();

  const handleSubmitAssessment = () => {
    const proctoringReportData = {
      candidate_email: assessmentDetails?.candidate_email,
      assessment: assessmentDetails?.assessment?.id,
      device_used: getDeviceType(),
      webcam_enabled: true,
      location: proctoringReport.location,
      mouse_out_of_window: proctoringReport.mouse_out_of_window,
      tab_switched: proctoringReport.tab_switched,
      id: proctoringReport.id as string,
      // full_screen_tolerance_used: proctoringReport.full_screen_tolerance_used,
      // window_change_tolerance_used:
      //   proctoringReport.window_change_tolerance_used,

      was_forced_submission: proctoringReport.was_forced_submission,
      // was_forced_submission_full_screen:
      //   proctoringReport.was_forced_submission_full_screen,
      // was_forced_submission_window_change:
      //   proctoringReport.was_forced_submission_window_change,
      //   was_forced_submission_bulk_proctor:
      //   proctoringReport.was_forced_submission_bulk_proctor,

      // bulk_proctor_used: proctoringReport.bulk_proctor_used,
      // bulk_proctor_option: proctoringReport.bulk_proctor_option,
    };

    const assessmentData = {
      candidate_email: assessmentDetails?.candidate_email,
      invite_id: assessmentDetails?.invite_id,
      assessment_id: assessmentDetails.assessment.id,
      result_id: assessmentDetails.result_id,
      candidate_name: assessmentDetails.candidate_name,
    };

    sendReport(proctoringReportData, {
      onSuccess: () => {
        setCandidateStatus({ isReturning: false });
        runSubmitAssessment(assessmentData, {
          onSuccess: (data: {
            status: number;
            data: { error: { message: string } };
          }) => {
            if (data.status === 200) {
              resetProctoringReport();
              nextStep();
              localStorage.clear();
              sessionStorage.clear();
            } else {
              toast.error(data.data.error.message);
            }
          },
          onError: (error: any) => {
            toast.error(error.message as string);
          },
        });
      },
      onError: (error: any) => {
        toast.error(error.message as string);
      },
    });
  };

  return {
    handleSubmitAssessment,
    isLoading: isUbmittingAssessment || isSendingReport,
    location,
    setLocation,
  };
};
