import { Dialog } from '@headlessui/react';
import React from 'react';
import ReactLoading from 'react-loading';

interface Props {
  is_open: boolean;
  handleRetake: () => void;
  message: string;
  nin: string;
  setNin: React.Dispatch<React.SetStateAction<string>>;
  isLoading: boolean;
  handleNINSubmit: () => void;
}

export default function VerifyIDErrorModal({
  is_open,
  handleRetake,
  message,
  nin,
  setNin,
  isLoading,
  handleNINSubmit,
}: Props) {
  return (
    <Dialog open={is_open} onClose={handleRetake}>
      <div className="fixed inset-0 z-30 bg-[#000000aa]">
        <Dialog.Panel
          className={
            'absolute left-1/2 top-1/2 flex h-[19.5rem] w-[30rem] -translate-x-1/2 -translate-y-1/2 flex-col rounded-2xl bg-white'
          }
        >
          <div className="flex h-[64px] items-end justify-between rounded-t-2xl bg-[#450A0A] px-10 py-[0.87rem] text-white">
            <p>Identity check</p>
            <button
              className={`flex h-8 w-[4.6rem] items-center justify-center rounded-md text-sm ${isLoading
                  ? 'cursor-not-allowed bg-gray-300 text-gray-500'
                  : 'bg-[#F5F3FF33]'
                }`}
              onClick={() => handleRetake()}
              disabled={isLoading}
            >
              close
            </button>
          </div>
          <div className="flex h-full flex-col justify-center gap-2 p-6 py-6">
            <span className="text-sm text-[#450A0A]">
              {message || "ID verification failed: document type is unknown."}
            </span>
            <p className="mt-3 text-xl text-[#0E0E2C]">
              {' '}
              Enter National Identity Number (NIN){' '}
            </p>

            <input
              className={`input-white mb-10 w-full border ${isLoading
                  ? 'cursor-not-allowed bg-gray-200 opacity-60'
                  : 'bg-gray-100'
                }`}
              type="number"
              required
              name="nin"
              disabled={isLoading}
              placeholder="Enter Number"
              value={nin}
              onChange={event => {
                setNin(event.target.value);
              }}
            />
          </div>

          <div className="flex h-[5rem] items-center justify-end gap-x-4 rounded-b-2xl bg-[#FEF2F2] p-5 font-light">
            <button
              className={`rounded-lg p-2 px-3 ${isLoading
                  ? 'cursor-not-allowed bg-gray-200 text-gray-500'
                  : 'bg-white text-[#450A0A]'
                }`}
              onClick={() => {
                handleRetake();
              }}
              disabled={isLoading}
            >
              Try again
            </button>

            <button
              className="ml-1 flex items-center rounded-lg border border-black bg-black p-2 px-10 text-white"
              onClick={() => {
                handleNINSubmit();
              }}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <ReactLoading
                    type="spin"
                    color="#fff"
                    width={20}
                    height={20}
                  />
                  <p className="ml-2">Verifying NIN</p>
                </>
              ) : (
                <p>Submit</p>
              )}
            </button>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
}
