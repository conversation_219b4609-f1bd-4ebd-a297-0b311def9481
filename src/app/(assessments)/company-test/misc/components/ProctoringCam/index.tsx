'use client';

import React, {
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import Webcam from 'react-webcam';
import '@tensorflow/tfjs-backend-cpu';
import '@tensorflow/tfjs-backend-webgl';
import * as cocoSsd from '@tensorflow-models/coco-ssd';
import { linearSearchLastTrue } from '@tensorflow/tfjs-backend-webgl/dist/gpgpu_context';
import moment from 'moment';
import dynamic from 'next/dynamic';
import Draggable from 'react-draggable';
import { toast, ToastContainer } from 'react-toastify';
import { useSetState } from 'react-use';
import { io, type Socket } from 'socket.io-client';
import { useDebouncedCallback } from 'use-debounce';
import { proctorOption } from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import { cn } from '@/utils';
import { useAssessmentDetails } from '../../store/assessmentStore';
import FlagDatabase from '../../store/flagManager';
import { useProfileImages } from '../../store/imageStore';
import { useProctoringReport } from '../../store/proctoringReportStore';
import {
  FaceAPIType,
  FaceData,
  Flag,
  Flags,
  InitializedModels,
  LocalReportLog,
} from '../../types';

// import * as faceAPI from 'face-api.js';
// import * as faceAPI from '@vladmandic/face-api';
// import * as faceAPI from '@vladmandic/face-api/dist/face-api.esm.js';

type WebcamComponentProps = {
  setVideoRef: (ref: HTMLVideoElement) => void;
  setWebcamRef: (ref: Webcam) => void;
};

const WebcamComponent: React.FC<WebcamComponentProps> = ({
  setVideoRef,
  setWebcamRef,
}) => {
  const videoConstraints = {
    width: 640,
    height: 480,
    facingMode: 'user',
  };

  return (
    <Webcam
      audio={false}
      height={480}
      ref={webcam => {
        if (webcam) {
          setWebcamRef(webcam);

          if (webcam.video) {
            setVideoRef(webcam.video);
          }
        }
      }}
      screenshotFormat="image/jpeg"
      width={640}
      videoConstraints={videoConstraints}
    />
  );
};

type ProctoringCamProps = {
  setCamRef: React.Dispatch<React.SetStateAction<Webcam | undefined>>;
  setCanvasRef: React.Dispatch<
    React.SetStateAction<HTMLCanvasElement | undefined>
  >;
  isAssessmentActivated: boolean;

  captureScreen: () => Promise<string>;
  flagManager: React.MutableRefObject<FlagDatabase>;
};
export default function ProctoringCam({
  setCamRef,
  setCanvasRef,
  isAssessmentActivated,


  captureScreen,
  flagManager,
}: ProctoringCamProps) {
  // LOAD FACE API DYNAMICALLY
  const assessmentDetails = useAssessmentDetails((state: any) => state.data);

  // const reportFlag = useProctoringReport(state => state.reportFlag);

  const webCamRef = useRef<Webcam>();

  const [video_ref, setVideoRef] = useState<HTMLVideoElement>();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [longInactivity, setLongInactivity] = useState(false);
  const [lipMovement, setLipMovement] = useState(false);
  const [unusualLighting, setUnusualLighting] = useState(false);
  const [is_suspected, setIsSuspected] = useState(false);
  const [flagCapturing, setFlagCapturing] = useState(false);

  const profileImages = useProfileImages(state => state.data);
  const illegal_objects = ['cell phone', 'laptop']; // "tv"]

  // console.log(import.meta, 'IMPORT META');
  // const reportFlag = (flag: string, screenshot: string, pc_capture: string) => {
  //   console.log();
  // };
  /*
 

 
=====================================
==== HANDLE MUNDANE FLAG CHECKING ===
=====================================
*/

  useEffect(() => {
    let interval: ReturnType<typeof setInterval>;

    if (isAssessmentActivated) {
      interval = setInterval(() => {
        let lastDate = sessionStorage.getItem('LAST_SUCCESSFUL_PROCTOR');
        if (lastDate) {
          let lastDateParsed = new Date(lastDate);
          //  console.log('CHECKING FOR SUCCESS');
          if (minuteDifference(lastDateParsed, new Date()) >= 5) {
            toast.warning('Proctor Server not reachable');
          }
        } else {
          let lastErrorDate = sessionStorage.getItem('LAST_ERROR_PROCTOR');
          if (lastErrorDate) {
            let lastErrorDateParsed = new Date(lastErrorDate);
            //  console.log('CHECKING FOR ERROR');

            if (minuteDifference(lastErrorDateParsed, new Date()) <= 5) {
              toast.warning('Proctor Server not reachable');
            }
          }
        }
      }, 300000);
    }
    return () => {
      clearInterval(interval);
    };
  }, [isAssessmentActivated]);

  function minuteDifference(date1: Date, date2: Date) {
    const diffMs = Math.abs(date2.getTime() - date1.getTime()); // Difference in milliseconds
    return Math.floor(diffMs / (1000 * 60)); // Convert milliseconds to minutes (integer)
  }
  /*
 ====================================
 ===== HANDLE FLAG REPORTING ========
 ====================================
 */



  const registerCheating = useCallback(
    async () => {
      // setIsSuspected(true);

      if (webCamRef.current) {

        const screenshot = webCamRef.current.getScreenshot();
        const pageScreenshot = await captureScreen();

        if (screenshot?.trim() == "data:,") {
          return;
        }
        //  console.log(screenshot, 'SCREENSHOT AS TAKEN', new Date());

        flagManager.current
          .reportFlag(String(screenshot || ''), pageScreenshot)
          .then(insertResponse => {
            //    console.log(insertResponse, 'INSERT RESPONSE');
          });

        setIsSuspected(false);
      } else {
        toast.error("Kindly check camera permissions")
      }
    },
    [webCamRef, captureScreen, setIsSuspected]
  );



  /*
   =================== ===
   HANDLE ROUTING SCREENSHOT CAPTURE
   === === === === === ===
  */
  useEffect(() => {
    if (!isAssessmentActivated) return;
    const detectFeaturesInterval = setInterval(registerCheating, 3000);
    return () => clearInterval(detectFeaturesInterval);
  }, [video_ref, isAssessmentActivated]);

  // Set PArent Canvas REf
  useEffect(() => {
    if (canvasRef.current) {
      setCanvasRef(canvasRef.current);
    }
  }, [canvasRef]);

  return (
    <>
      <div>
        <div
          className={cn(
            { 'ring ring-danger': is_suspected },
            'relative h-max overflow-hidden rounded-md'
          )}
        >
          <WebcamComponent
            setVideoRef={setVideoRef}
            setWebcamRef={ref => {
              // setWebCamRef(ref);
              webCamRef.current = ref;
              setCamRef(ref);
            }}
          />
          <canvas
            className={cn('absolute inset-0 h-full w-full hidden')}
            ref={canvasRef}
            width={250}
            height={180}
          />
        </div>
      </div>

      <ToastContainer
        autoClose={1000}
        hideProgressBar
        toastClassName="text-green p-2 rounded-md text-sm font-medium"
        position="bottom-right"
        className="absolute"
      />
    </>
  );
}
