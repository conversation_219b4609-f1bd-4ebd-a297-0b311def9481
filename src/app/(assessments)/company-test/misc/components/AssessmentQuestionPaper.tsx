import React, { useEffect, useRef } from 'react';
import { RadioGroup } from '@headlessui/react';
import { CheckIcon } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { Button } from '@/components/shared';
import { Textarea } from '@/components/shared';
import { cn } from '@/utils';
import { Question } from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import { Flag } from '../types';
import { DebouncedState, useDebouncedCallback } from 'use-debounce';

// interface Question {
//     id: string;
//     type: 'multiple_choice' | 'true_false' | 'essay' | 'multiple_response';
//     question: string;
//     answer_options?: string[];
//     images?: string[];
// }

interface AssessmentQuestionPaperProps {
    questions: Question[];
    currentQuestionId: number;
    onAnswerSubmit: (answer: string | string[]) => void;
    onNavigate: (direction: 'next' | 'previous') => void;
    isLastQuestion: boolean;
    sectionName: string;
    sectionNumber: number;
    isTrackPaste: boolean;
    onCheatingDetected: DebouncedState<(flag: Flag) => void>;
}

export default function AssessmentQuestionPaper({
    questions,
    currentQuestionId,
    onAnswerSubmit,
    onNavigate,
    isLastQuestion,
    sectionName,
    sectionNumber,
    isTrackPaste,
    onCheatingDetected,
}: AssessmentQuestionPaperProps) {
    const selectedAnswer = useRef<string | string[]>([]);
    const currentQuestion = questions[currentQuestionId];

    useEffect(() => {
        selectedAnswer.current = [];
    }, [currentQuestionId]);

    const handleAnswerSelect = (answer: string) => {
        if (currentQuestion.type === 'multiple_response') {
            if ((selectedAnswer.current as string[]).includes(answer)) {
                selectedAnswer.current = (selectedAnswer.current as string[]).filter((a) => a !== answer);
            } else {
                selectedAnswer.current = [...(selectedAnswer.current as string[]), answer];
            }
        } else {
            selectedAnswer.current = [answer];
        }
    };

    const handleSubmit = () => {
        onAnswerSubmit(selectedAnswer.current);
        if (!isLastQuestion) {
            onNavigate('next');
        }
    };

    const renderQuestionContent = () => {
        switch (currentQuestion.type) {
            case 'multiple_choice':
            case 'true_false':
                return (
                    <RadioGroup onChange={(value) => selectedAnswer.current = value as string}>
                        <div className="space-y-2">
                            {currentQuestion.answer_options?.map((option) => (
                                <RadioGroup.Option key={option} value={option} className="flex items-center space-x-2 cursor-pointer">
                                    {({ checked }) => (
                                        <>
                                            <div className={cn(
                                                "w-4 h-4 rounded-full border flex items-center justify-center",
                                                checked ? "bg-primary border-primary" : "border-gray-300"
                                            )}>
                                                {checked && <CheckIcon className="w-3 h-3 text-white" />}
                                            </div>
                                            <RadioGroup.Label>{option}</RadioGroup.Label>
                                        </>
                                    )}
                                </RadioGroup.Option>
                            ))}
                        </div>
                    </RadioGroup>
                );
            case 'multiple_response':
                return (
                    <div className="space-y-2">
                        {currentQuestion.answer_options?.map((option) => (
                            <div key={option} className="flex items-center space-x-2 cursor-pointer" onClick={() => handleAnswerSelect(option)}>
                                <div className={cn(
                                    "w-4 h-4 rounded border flex items-center justify-center",
                                    (selectedAnswer.current as string[]).includes(option) ? "bg-primary border-primary" : "border-gray-300"
                                )}>
                                    {(selectedAnswer.current as string[]).includes(option) && <CheckIcon className="w-3 h-3 text-white" />}
                                </div>
                                <span>{option}</span>
                            </div>
                        ))}
                    </div>
                );
            case 'essay':
                return (
                    <Textarea
                        value={selectedAnswer.current[0] || ''}
                        onChange={(e) => { selectedAnswer.current = e.target.value; }}
                        placeholder="Enter your answer"
                        rows={6}
                        className="w-full p-2 border rounded"
                        onPaste={(e) => {
                            if (isTrackPaste) {
                                e.preventDefault();
                                onCheatingDetected('paste');
                            }
                        }}
                    />
                );
            default:
                return null;
        }
    };

    return (
        <div className="space-y-4">
            <div>
                <h2 className="text-lg font-semibold">
                    Section {sectionNumber}: {sectionName}
                </h2>
                <p className="text-sm text-gray-500">
                    Question {currentQuestionId + 1} of {questions.length}
                </p>
            </div>
            <div className="p-4 bg-white rounded-lg shadow">
                <h3 className="text-xl font-medium mb-4">{currentQuestion.question}</h3>
                {currentQuestion.images && (
                    <div className="flex gap-2 mb-4 overflow-x-auto">
                        {currentQuestion.images.map((image, index) => (
                            <img key={index} src={image} alt={`Question image ${index + 1}`} className="max-h-40 object-cover rounded" />
                        ))}
                    </div>
                )}
                {renderQuestionContent()}
            </div>
            <div className="flex justify-between">
                <Button
                    onClick={() => onNavigate('previous')}
                    disabled={currentQuestionId === 0}
                    variant="outlined"
                >
                    Previous
                </Button>
                <Button onClick={handleSubmit}>
                    {isLastQuestion ? 'Submit Assessment' : 'Next Question'}
                </Button>
            </div>
        </div>
    );
}