import { Dialog } from '@headlessui/react';
import React from 'react';
import ReactLoading from 'react-loading';

interface Props {
  is_open: boolean;
  handleRetake: () => void;
  message: string;
  nin: string;
  setNin: React.Dispatch<React.SetStateAction<string>>;
  closeModal: () => void;
  isLoading: boolean;
}

export default function VerifyIDErrorModal({
  is_open,
  handleRetake,
  message,
  closeModal,
  nin,
  setNin,
  isLoading,
}: Props) {
  return (
    <Dialog open={is_open} onClose={() => { }}>
      <div className="fixed inset-0 z-30 bg-[#000000aa]">
        <Dialog.Panel
          className={
            'absolute left-1/2 top-1/2 flex h-[19.5rem] w-[30rem] -translate-x-1/2 -translate-y-1/2 flex-col rounded-2xl bg-white'
          }
        >
          <div className="flex h-[64px] items-end justify-between rounded-t-2xl bg-[#755AE2] px-10 py-[0.87rem] text-white">
            <p>Identity number</p>
            <button
              className="flex h-8 w-[4.6rem] items-center justify-center rounded-md bg-[#F5F3FF33] text-sm"
              onClick={closeModal}
            >
              close
            </button>
          </div>
          <div className="flex h-full flex-col justify-center gap-2 bg-[#F5F3FF] p-4">
            <p className="text-xl text-[#0E0E2C]">
              Enter National identity number (NIN)
            </p>

            <input
              className="input-white w-full border"
              type="number"
              required
              name="nin"
              disabled={isLoading}
              value={nin}
              onChange={event => {
                setNin(event.target.value);
              }}
            />
            {message && (
              <span className="text-sm text-danger">{message}</span>
            )}
          </div>

          <div className="flex h-[5rem] items-center justify-between gap-x-4 rounded-b-2xl bg-white p-5 font-light">
            <button
              className="rounded-md bg-[#F5F3FF] p-2 px-3 text-[#755AE2] "
              onClick={closeModal}
            >
              Cancel
            </button>

            <button
              className="flex rounded-md bg-[#755AE2] p-2 px-5 text-white"
              onClick={() => {
                handleRetake();
              }}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <ReactLoading
                    type="spin"
                    color="#fff"
                    width={20}
                    height={20}
                  />
                  <p className="ml-2">Verifying NIN</p>
                </>
              ) : (
                <p>Submit</p>
              )}
            </button>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
}
