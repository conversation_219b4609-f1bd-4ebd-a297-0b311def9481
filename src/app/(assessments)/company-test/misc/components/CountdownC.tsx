import { useRouter } from 'next/navigation';
import React, { useEffect, useRef } from 'react';
import Countdown from 'react-countdown';
import { LoadingOverlay } from '@/components/shared';
import { cn } from '@/utils';
import { UseTimeCountingContext } from '../../layout';
import ClockIcon from '../icons/ClockIcon';
import { UseCandidateStatusStore, useProctoringReport } from '../store';
import { useAssessmentDetails } from '../store/assessmentStore';
import { formatCountdownSeconds } from '../utils/functions';
import { useAssessmentSubmission } from './hooks';

export default function CountdownC({
  isTimerShowing = true,
}: {
  isTimerShowing?: boolean;
}) {
  const router = useRouter();
  const { isTimeCounting } = UseTimeCountingContext();
  const { assessmentDetails } = useAssessmentDetails();
  const { candidateStatus, setCandidateStatus, initializeTimer, updateTimer } =
    UseCandidateStatusStore(state => ({
      candidateStatus: state.candidateStatus,
      setCandidateStatus: state.setCandidateStatus,
      initializeTimer: state.initializeTimer,
      updateTimer: state.updateTimer,
    }));
  const { proctoringReport, resetProctoringReport } = useProctoringReport(
    state => ({
      proctoringReport: state.proctoringReport,
      resetProctoringReport: state.resetProctoringReport,
    })
  );

  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const { handleSubmitAssessment, isLoading } = useAssessmentSubmission({
    assessmentDetails,
    proctoringReport,
    resetProctoringReport,
    setCandidateStatus,
    nextStep: () => { },
  });

  //////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////
  //////////////////////////////////////////////////////////////////////////////
  useEffect(() => {
    if (
      candidateStatus.isInSession &&
      candidateStatus.startTime === 0 &&
      isTimeCounting
    ) {
      initializeTimer(assessmentDetails.assessment.time_limit * 1000);
    }
  }, [
    candidateStatus.isInSession,
    assessmentDetails?.assessment?.time_limit,
    candidateStatus.startTime,
    isTimeCounting,
  ]);

  useEffect(() => {
    // console.log(candidateStatus, "CANDIDATE STATUS FROM COUNTDOWN")
    // console.log(isTimeCounting, "IS TIME COUNTING FROM COUNTDOWN");
    if (
      candidateStatus.isInSession &&
      candidateStatus.timeLeft > 0 &&
      isTimeCounting
    ) {
      if (timerRef.current === null) {
        timerRef.current = setInterval(() => {
          updateTimer();
        }, 1000);
      }
    } else if (isTimeCounting && candidateStatus.timeLeft <= 0 && candidateStatus.isInSession) {
      // console.log("TIME LEFT IS 0")
      // console.log(candidateStatus, "CANDIDATE STATUS FROM COUNTDOWN")
      // console.log(isTimeCounting, "IS TIME COUNTING FROM COUNTDOWN");

      handleSubmitAssessment();
      router.replace('/');
      timerRef.current = null;
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [
    candidateStatus.isInSession,
    candidateStatus.timeLeft,
    updateTimer,
    isTimeCounting,
  ]);

  const renderer = ({
    formatted: { hours, minutes, seconds },
    completed,
  }: any) => {
    if (completed) {
      return (
        <p className="w-[64px] text-base font-semibold text-primary lg:w-[76px] 2xl:text-lg ">
          00:00:00
        </p>
      );
    } else {
      return (
        <span className="w-[64px] text-base font-semibold text-primary lg:w-[76px] 2xl:text-lg">
          {hours}:{minutes}:{seconds}
        </span>
      );
    }
  };

  return (
    <div
      className={cn(
        'relative flex w-max min-w-[180px] items-center gap-x-2 rounded-md bg-[#ECE8FF] px-2 py-2.5 lg:px-4',
        !isTimerShowing && 'min-w-[180px] max-w-max blur-[2.5px]'
      )}
    >
      <ClockIcon />
      {!candidateStatus.isInSession ? (
        <p className="w-[64px] !text-base font-semibold text-primary lg:w-[76px] 2xl:text-lg">
          {formatCountdownSeconds(
            assessmentDetails?.assessment?.time_limit || 0
          )}
        </p>
      ) : (
        <Countdown
          date={Date.now() + candidateStatus.timeLeft}
          renderer={renderer}
          zeroPadTime={2}
          autoStart
          className="font-semibold text-primary 2xl:text-lg"
        />
      )}
      <p className="text-primary 2xl:text-sm">time left</p>

      <LoadingOverlay isOpen={isLoading} />
    </div>
  );
}
