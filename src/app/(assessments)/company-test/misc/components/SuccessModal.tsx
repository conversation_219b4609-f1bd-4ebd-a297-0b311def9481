import { Dialog } from '@headlessui/react';
import { useRouter } from 'next/navigation';
import React, { SetStateAction, useState } from 'react';
import toast from 'react-hot-toast';
import ReactLoading from 'react-loading';
import { Spinner } from '@/components/shared/icons';
import Loader from '@/components/shared/loader';
import { cn } from '@/utils';
import { useSendProctoringReports } from '../api/sendProctoringReport';
import { useSubmitAssessment } from '../api/submitAssessment';
import { UseCandidateStatusStore } from '../store';
import { useAssessmentDetails } from '../store/assessmentStore';
import { NetworkStoreProps, useNetworkDetails } from '../store/networkStore';
import { useProctoringReport } from '../store/proctoringReportStore';

interface SuccessModalProps {
  isOpen: boolean;
  closeModal: () => void;
  openFeedback: () => void;
}

const SuccessModal: React.FunctionComponent<SuccessModalProps> = ({
  isOpen,
  closeModal,
  openFeedback,
}) => {
  return (
    <Dialog open={isOpen} onClose={() => {}}>
      <div className="fixed inset-0 z-30 bg-[#000000aa]">
        <Dialog.Panel
          className={
            'absolute left-1/2 top-1/2 flex h-[19.5rem] w-[30rem] -translate-x-1/2 -translate-y-1/2 flex-col rounded-2xl bg-[#F5F3FF]'
          }
        >
          <>
            <div className="flex h-[64px] items-end justify-between rounded-t-2xl bg-primary px-10 py-[0.87rem] text-white">
              <p>Thank you</p>
              <button
                className="flex h-8 w-[4.6rem] items-center justify-center rounded-md bg-[#F5F3FF33]"
                onClick={() => {}}
              >
                close
              </button>
            </div>

            <>
              <div className="flex h-full flex-col items-center justify-center p-5">
                <p className="text-center text-xl text-primary">
                  Thank you for taking this assessment
                </p>
                <p className="text-center text-sm text-helper-text">
                  Your assessment will be scored and you will be contacted
                  afterwards Good luck!
                </p>
              </div>
            </>

            <div className="flex h-[5rem] justify-end gap-x-4 rounded-b-2xl bg-white p-5 font-light">
              <button
                className="rounded-md bg-primary-light p-2 px-3 text-primary"
                onClick={openFeedback}
              >
                Give feedback
              </button>
              <button
                className="rounded-md bg-primary p-2 px-3 text-white"
                onClick={closeModal}
              >
                Okay
              </button>
            </div>
          </>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default SuccessModal;
