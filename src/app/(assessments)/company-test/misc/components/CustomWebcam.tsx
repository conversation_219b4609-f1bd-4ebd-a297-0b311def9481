import { useCallback, useRef, useState } from 'react';
import Webcam from 'react-webcam';

const CustomWebcam = () => {
  const webcamRef = useRef<any>(null);
  const [imgSrc, setImgSrc] = useState(null); // initialize it
  const capture = useCallback(() => {
    const imageSrc = webcamRef.current.getScreenshot();
    setImgSrc(imageSrc);
  }, [webcamRef]);

  return (
    <div className="container">
      <Webcam
        className={`w-full rounded-xl border border-primary`}
        audio
        ref={webcamRef}
        mirrored={true}
      />
    </div>
  );
};

export default CustomWebcam;
