'use client';
import { StatusCard } from './StatusCard';

export function StatusPanel({status}: any){
    return <div className='flex justify-around w-full'>
        <StatusCard status={status.moving} img={"/assets/scan.gif"} text="FakeCam" />
        <StatusCard status={status.face} img={"/assets/face.gif"} text="TakingTest" />
        {
            status.num>1?
                <StatusCard status={false} img={"/assets/chatting.gif"} text="More than One" /> :
                <StatusCard status={true} img={"/assets/chatting.gif"} text="More than One" />
        }
    </div>
}