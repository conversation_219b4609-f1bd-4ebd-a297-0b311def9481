import Image from "next/image"
import { ToolTip } from "@/components/shared";

export function StatusCard({status, img, text}: any){
    let classStyle = ""
    if (status){
        classStyle = "flex flex-col rounded-full w-[30px] bg-green-200"
    }
    else {
        classStyle = "flex flex-col rounded-full w-[30px] bg-red-400"
    }
    return <div className={classStyle}>
        <ToolTip content={text}>
            <Image src={img} width={10} height={10} alt='face-scan'/>
        </ToolTip>
    </div>
}