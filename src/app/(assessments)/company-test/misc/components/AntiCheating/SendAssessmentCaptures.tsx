'use client';

import React, { useRef, useEffect, useState } from 'react';

import Image from 'next/image';

import io from 'socket.io-client';

import useSessionStorage from '../../../../../misc/hooks/useSessionStorage';
import { useAssessmentDetails } from '../../store/assessmentStore'

export default function Home() {
  const socket = useRef();
  const videoRef = useRef();
  const canvasRef = useRef();

  const assessmentDetails = useAssessmentDetails((state: any) => state.data);

  // console.log(assessmentDetails.assessment.id, "assessment")

  const [userID, setUserID] = useSessionStorage(
    'get-linked-user-assessment-snaps-id',
    new Date()
  );

  useEffect(() => {
    //@ts-ignore
    socket.current = io('https://socket.getlinked.ai');

    const constraints = {
      video: true,
    };

    const captureInterval = setInterval(() => {
      takeScreenshot(120, 120);
    }, 800); // Take a screenshot every second (1000 milliseconds)
    const startWebcam = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia(constraints);
        if (videoRef.current) {
          //@ts-ignore
          videoRef.current.srcObject = stream;
        }
      } catch (err) {
        console.error('Error accessing the webcam:', err);
      }
    };

    startWebcam();

    return () => {
      clearInterval(captureInterval);
    };
  }, []);

  const takeScreenshot = (desiredWidth: number, desiredHeight: number) => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;

      //@ts-ignore
      canvas.width = desiredWidth;
      //@ts-ignore
      canvas.height = desiredHeight;

      //@ts-ignore
      const ctx = canvas.getContext('2d');
      ctx.drawImage(video, 0, 0, desiredWidth, desiredHeight);

      // Create a temporary canvas to resize the image
      const tempCanvas = document.createElement('canvas');
      const tempCtx = tempCanvas.getContext('2d');
      tempCanvas.width = desiredWidth;
      tempCanvas.height = desiredHeight;
      tempCtx?.drawImage(canvas, 0, 0, desiredWidth, desiredHeight);

      // Get the resized image data as base64
      const resizedImageData = tempCanvas.toDataURL('image/jpeg');

      // Send the resized image to the server
      //@ts-ignore
      socket.current.emit('upload', { imageData: resizedImageData, userID: assessmentDetails.assessment.id });
    }
  };
  return (
    <div>

      <video
        //@ts-ignore
        ref={videoRef} autoPlay />
      <canvas
        //@ts-ignore
        ref={canvasRef} style={{ display: 'none' }} />
    </div>
  );
}
