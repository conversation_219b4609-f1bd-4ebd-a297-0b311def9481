'use client';

import { Camera } from '@mediapipe/camera_utils';
import FaceDetection from '@mediapipe/face_detection';
import Image from 'next/image';
import MinimizeSVG from 'public/assets/minimize.svg';
import { useCallback, useEffect, useRef, useState } from 'react';
import Draggable from 'react-draggable';
import { toast, ToastContainer } from 'react-toastify';
import { CameraOptions, useFaceDetection } from 'react-use-face-detection';
import Webcam from 'react-webcam';
import { StatusPanel } from './StatusPanel';
import 'react-toastify/dist/ReactToastify.min.css';
import _ from 'lodash';
import moment from 'moment';
import { io, type Socket } from 'socket.io-client';
import useSessionStorage from '../../../../../misc/hooks/useSessionStorage';
import { useAssessmentDetails } from '../../store/assessmentStore';
import { useProctoringReport } from '../../store/proctoringReportStore';
import { PeerConnection, socket } from './communication';

const width = 250;
const height = 150;

interface ICheatingState {
  multiFace: boolean;
  fakeCam: boolean;
  noFace: number;
  noFaceDuration: number;
  noEnv: number;
  noEnvDuration: number;
  isLookingAway: boolean;
}

interface ITempCheatingState {
  multiFace: boolean;
  multiFaceTimeStamp: Date | null;
  fakeCam: boolean;
  noFace: boolean;
  isLookingAway: boolean;
  noFaceTimeStamp: Date | null;
  noFaceTemp: Date | null;
  noEnv: boolean;
  noEnvTimeStamp: Date | null;
  noEnvTemp: Date | null;
}

const AntiCheating = (): JSX.Element => {
  const socket2 = useRef<Socket>();

  // const proctoringReport = useProctoringReport(state => state.data);
  // const setProctoringReport = useProctoringReport(
  //   state => state.setProctoringReport
  // );

  const { proctoringReport, setProctoringReport } = useProctoringReport();

  const [isDetail, setDetail] = useState<boolean>(true);
  const assessmentDetails = useAssessmentDetails((state: any) => state.data);
  const [cheatingState, setCheatingState] = useState<ICheatingState>({
    multiFace: false,
    fakeCam: false,
    noFace: 0,
    noFaceDuration: 0,
    noEnv: 0,
    noEnvDuration: 0,
    isLookingAway: false,
  });
  const [tempCheating, setTempCheating] = useState<ITempCheatingState>({
    multiFace: false,
    multiFaceTimeStamp: null,
    fakeCam: false,
    noFace: false,
    noFaceTimeStamp: null,
    noFaceTemp: null,
    noEnv: false,
    noEnvTimeStamp: null,
    noEnvTemp: null,
    isLookingAway: false,
  });
  const { webcamRef, boundingBox, isLoading, detected, facesDetected } =
    useFaceDetection({
      faceDetectionOptions: {
        model: 'short',
        minDetectionConfidence: 0.9,
      },
      faceDetection: new FaceDetection.FaceDetection({
        locateFile: file =>
          `https://cdn.jsdelivr.net/npm/@mediapipe/face_detection/${file}`,
      }),
      camera: ({ mediaSrc, onFrame }: CameraOptions) =>
        new Camera(mediaSrc, {
          onFrame,
          width,
          height,
        }),
    });

  // Socket Part
  const [isActive, setActive] = useState<boolean>(false);
  const [clientID, setClientID] = useState<string>('');
  const [fromID, setFromID] = useState<string>('');
  const [localSrc, setLocalSrc] = useState<any>(null);
  const [peerSrc, setPeerSrc] = useState<any>(null);
  const [pc, setPC] = useState<PeerConnection | null>(null);

  // Temp Data Update
  useEffect(() => {
    // Multi Face Set
    if (facesDetected > 1) {
      if (tempCheating.multiFace == false) {
        let temp = tempCheating;
        temp.multiFace = true;
        temp.multiFaceTimeStamp = new Date();
        setTempCheating(temp);
        registerCheating('Multi Face');
        socket2.current?.emit('event', {
          eventType: 'MultiFace',
          inviteId: assessmentDetails.assessment.id,
        });
        // Object.values(proctoringReport.flags).length < 10 &&
        //   toast.warning('Multi Face Detected');
      }
    } else {
      let temp = tempCheating;
      temp.multiFace = false;
      setTempCheating(temp);
    }

    // No Face Set
    if (facesDetected == 0 && tempCheating.noFace == false) {
      let temp = tempCheating;
      temp.noFace = true;
      temp.noFaceTimeStamp = new Date();
      setTempCheating(temp);
    } else if (facesDetected != 0) {
      let temp = tempCheating;
      temp.noFace = false;
      setTempCheating(temp);
    }

    // Too close from Camera
    if (facesDetected == 1) {
      boundingBox.map((box, index) => {
        if (box.width > 0.25 && tempCheating.noEnv == false) {
          let temp = tempCheating;
          temp.noEnv = true;
          temp.noEnvTimeStamp = new Date();
          setTempCheating(temp);
        } else if (box.width < 0.25) {
          setTempCheating({ ...tempCheating, noEnv: false });
        }
      });
    } else {
      let temp = tempCheating;
      temp.noEnv = false;
      setTempCheating(temp);
    }
  }, [facesDetected, boundingBox]);

  useEffect(() => {
    // Multi Face Register
    if (
      tempCheating.multiFaceTimeStamp != null &&
      cheatingState.multiFace == false
    ) {
      let timeDelta =
        (new Date().getTime() - tempCheating.multiFaceTimeStamp.getTime()) /
        1000;
      if (facesDetected > 1 && timeDelta > 2) {
        setCheatingState({ ...cheatingState, multiFace: true });
        toast.error('Multi Face Cheating Registered');
      }
    }

    // No Face Register
    if (
      facesDetected == 0 &&
      tempCheating.noFace == true &&
      tempCheating.noFaceTimeStamp != null
    ) {
      let timeDelta =
        (new Date().getTime() - tempCheating.noFaceTimeStamp.getTime()) / 1000;
      if (timeDelta > 2) {
        if (tempCheating.noFaceTemp != tempCheating.noFaceTimeStamp) {
          let temp = tempCheating;
          temp.noFaceTemp = temp.noFaceTimeStamp;
          // Object.values(proctoringReport.flags).length < 10 &&
          //   toast.warning('No Face Detected');
          socket2.current?.emit('event', {
            eventType: 'NoFace',
            inviteId: assessmentDetails.assessment.id,
          });
          registerCheating('No Face');
          setTempCheating(temp);
          setCheatingState({
            ...cheatingState,
            noFace: cheatingState.noFace + 1,
            noFaceDuration: cheatingState.noFaceDuration + timeDelta,
          });
        }
      }
    }

    // No Env Register
    if (
      facesDetected == 1 &&
      tempCheating.noEnv == true &&
      tempCheating.noEnvTimeStamp != null
    ) {
      let timeDelta =
        new Date().getTime() - tempCheating.noEnvTimeStamp.getTime();
      if (timeDelta > 2) {
        if (tempCheating.noEnvTemp != tempCheating.noEnvTimeStamp) {
          let temp = tempCheating;
          temp.noEnvTemp = temp.noEnvTimeStamp;
          // Object.values(proctoringReport.flags).length < 10 &&
          //   toast.warning('Too close to Camera');
          socket2.current?.emit('event', {
            eventType: 'Camera close',
            inviteId: assessmentDetails.assessment.id,
          });
          registerCheating('Too close from camera');
          setTempCheating(temp);
          setCheatingState({
            ...cheatingState,
            noEnv: cheatingState.noEnv + 1,
            noEnvDuration: cheatingState.noEnvDuration + timeDelta,
          });
        }
      }
    }
  }, [facesDetected, boundingBox]);

  // Start Socket
  useEffect(() => {
    socket
      .on('init', ({ id }: { id: any }) => {
        setClientID(id);
        // console.log(id);
      })
      .emit('init');
  }, []);
  useEffect(() => {
    socket
      // .on("connect", ()=>{console.log("Server connected")})
      .on('request', ({ from: callFrom }: { from: any }) => {
        if (fromID != '') {
          // console.log('this is request', fromID);
          socket.emit('end', { to: callFrom });
        } else {
          setFromID(callFrom);
        }
      })
      .on('call', (data: any) => {
        if (data.sdp != undefined) {
          if (data.sdp.type === 'answer') {
            pc?.setRemoteDescription(data.sdp);
          }
          if (data.sdp.type === 'offer') pc?.createAnswer();
        } else pc?.addIceCandidate(data.candidate);
      })
      .on('end', () => {
        setPC(null);
        setFromID('');
      });
  });
  useEffect(() => {
    endCall();
    if (fromID != '') startCall();
  }, [fromID]);

  useEffect(() => {
    //@ts-ignore
    socket2.current = io('https://socket.getlinked.ai');
  }, []);

  // Start live Proctoring Handler
  function startCall() {
    setPC(
      new PeerConnection(fromID)
        .on('localStream', (src: any) => {
          setLocalSrc(src);
        })
        .on('peerStream', (src: any) => {
          setPeerSrc(src);
        })
        .start(false)
    );
  }

  // End Proctoring Handler
  function endCall() {
    if (_.isFunction(pc?.stop)) {
      pc?.stop(false);
    }
    setPC(null);
    setLocalSrc(null);
    setPeerSrc(null);
  }

  const registerCheating = (event: string) => {
    const date = new Date();
    const newFlag = {
      time: moment(date.getTime()).format('DD-MM-yyyy hh:mm:ss'),
      event,
    };
    const key = `${event}:${newFlag.time}`
      .toLowerCase()
      .slice(0, -1)
      .split(' ')
      .join('-');

    // let updatedFlags = {
    //   ...proctoringReport.flags,
    // };
    // updatedFlags[key] = newFlag;
    // setProctoringReport({
    //   ...proctoringReport,
    //   flags: { ...updatedFlags },
    // });
  };

  return (
    <div>
      <Draggable defaultClassName="absolute">
        <div className="bg-purple flex w-[300px] flex-col rounded-2xl px-4">
          <div className="flex w-full items-center justify-between rounded-2xl py-2 text-xl font-bold">
            {/* <span>
              <Image
                src={MinimizeSVG}
                alt="minimize"
                height={25}
                onClick={() => setDetail(!isDetail)}
              />
            </span> */}
          </div>
          <div className="flex flex-col items-center gap-2 py-2">
            <StatusPanel
              status={{
                moving: !tempCheating.noEnv,
                face: detected,
                num: facesDetected,
              }}
            />
            <div
              style={{
                width,
                height,
                position: 'relative',
                display: isDetail ? 'block' : 'none',
              }}
            >
              {/* isDetail? 'block':'none' */}
              {boundingBox.map((box, index) => (
                <div
                  key={`${index + 1}`}
                  style={{
                    border: '4px solid red',
                    position: 'absolute',
                    top: `${box.yCenter * 100}%`,
                    left: `${box.xCenter * 100}%`,
                    width: `${box.width * 100}%`,
                    height: `${box.height * 100}%`,
                    zIndex: 1,
                  }}
                />
              ))}
              <Webcam
                ref={webcamRef}
                forceScreenshotSourceSize
                style={{
                  height,
                  width,
                  //   position: 'absolute',
                }}
                mirrored
                className="rounded-xl"
              />
            </div>
          </div>
        </div>
      </Draggable>

      <ToastContainer
        autoClose={1000}
        hideProgressBar
        toastClassName="text-green p-2 rounded-md text-sm font-medium"
        position="bottom-right"
        className="absolute"
      />
    </div>
  );
};

export default AntiCheating;
