import { Dialog } from '@headlessui/react';
import { Rating } from '@smastrom/react-rating';
import React, { useState } from 'react';
import ReactLoading from 'react-loading';
import '@smastrom/react-rating/style.css';
import toast from 'react-hot-toast';
import { Textarea } from '@/components/shared';
import app_fetch from '@/lib/api/appFetch';
import { TAssessmentStore } from '../store/assessmentStore';

interface Props {
  isOpen: boolean;
  assessmentDetail: TAssessmentStore;
  closeModal: () => void;
}

export default function VerifyIDErrorModal({
  isOpen,
  assessmentDetail,
  closeModal,
}: Props) {
  const [isLoading, setLoading] = useState<boolean>(false);
  const [feedback, setFeedback] = useState<{
    rating: number;
    message: string;
  }>({
    rating: 0,
    message: '',
  });
  const updateFeedback = (data: any) => {
    setFeedback({ ...feedback, ...data });
  };

  const submitFeedback = () => {
    if (feedback.rating <= 0 && feedback.message == '') {
      return;
    }
    setLoading(true);
    // console.log(feedback);

    let payload = {
      rating: feedback.rating,
      feedback: feedback.message,
      candidate_email: assessmentDetail.candidate_email,
    };
    console.log(payload);

    const options = {
      method: 'POST',
      body: JSON.stringify(payload),
    };
    // console.log(options);
    app_fetch(
      `assessments/submit-feedback/${assessmentDetail.assessment.id}/`,
      options
    )
      .then(res => res.json())
      .then(result => {
        //console.log(result);
        if (!result.success) {
          toast.error(result.error);
        } else {
          toast.success(result.message);
          closeModal();
        }
      })
      .catch(e => {
        //   console.log(e);
      });
  };
  return (
    <Dialog open={isOpen} onClose={() => { }}>
      <div className="fixed inset-0 z-30 bg-[#000000aa]">
        <Dialog.Panel
          className={
            'absolute left-1/2 top-1/2 flex h-[19.5rem] w-[30rem] -translate-x-1/2 -translate-y-1/2 flex-col rounded-2xl bg-white'
          }
        >
          <div className="flex h-[64px] items-end justify-between rounded-t-2xl bg-[#755AE2] px-10 py-[0.87rem] text-white">
            <p>Feedback</p>
            <button
              disabled={isLoading}
              className="flex h-8 w-[4.6rem] items-center justify-center rounded-md bg-[#F5F3FF33] text-sm"
              onClick={closeModal}
            >
              close
            </button>
          </div>
          <div className="flex h-full flex-col justify-center gap-2 bg-[#F5F3FF] p-4">
            <div className="flex h-full flex-col items-center justify-center p-5">
              <p className="text-center text-xl text-primary">
                How was your assessment experience?
              </p>
              <p className="text-center text-sm text-helper-text">
                Your thoughts and feedback are valuable to us
              </p>
            </div>
            <div className="mb-3 flex w-full items-center justify-center rounded-lg bg-white p-2">
              <div className="w-1/3">
                <Rating
                  className=" max-w-full"
                  value={feedback.rating}
                  //@ts-ignore
                  onChange={val => updateFeedback({ rating: val })}
                  isDisabled={isLoading}
                />
              </div>
            </div>

            <textarea
              className="input-white w-full border"
              required
              name="message"
              rows={4}
              placeholder="Tell us more about your experience"
              value={feedback.message}
              //@ts-ignore
              onChange={e => updateFeedback({ message: e.target.value })}
              disabled={isLoading}
            ></textarea>
          </div>

          <div className="flex h-[5rem] items-center justify-between gap-x-4 rounded-b-2xl bg-white p-5 font-light">
            <button
              className="flex rounded-md bg-[#755AE2] p-2 px-5 text-white"
              onClick={submitFeedback}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <ReactLoading
                    type="spin"
                    color="#fff"
                    width={20}
                    height={20}
                  />
                </>
              ) : (
                <p>Submit</p>
              )}
            </button>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
}
