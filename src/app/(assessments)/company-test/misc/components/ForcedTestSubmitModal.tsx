import { log } from 'console';
import { Dialog } from '@headlessui/react';
import moment from 'moment';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React, { SetStateAction } from 'react';
import { LinkButton } from '@/components/shared';

interface ForceSubmitTestModalProps {
  isForcedSubmitTestOpen: boolean;
  setIsForcedSubmitTestOpen: () => void;
}

const ForceSubmitTestModal: React.FunctionComponent<
  ForceSubmitTestModalProps
> = ({ isForcedSubmitTestOpen, setIsForcedSubmitTestOpen }) => {
  const router = useRouter();
  const closeSubmittedModal = () => {
    setIsForcedSubmitTestOpen();
    router.push('/login/talent');
  };

  return (
    <Dialog open={isForcedSubmitTestOpen} onClose={() => {}}>
      <div className="fixed inset-0 z-30 bg-[#000000aa]">
        <Dialog.Panel
          className={
            'absolute left-1/2 top-1/2 flex h-[19.5rem] w-[30rem] -translate-x-1/2 -translate-y-1/2 flex-col rounded-2xl bg-white '
          }
        >
          <div className="flex h-[64px] items-end justify-between rounded-t-2xl bg-[#450A0A] px-10 py-[0.87rem] text-white">
            <p>End assessment</p>
            <button
              className="flex h-8 w-[4.6rem] items-center justify-center rounded-md bg-[#F5F3FF33]"
              onClick={closeSubmittedModal}
            >
              close
            </button>
          </div>
          <div className="flex h-full flex-col items-center justify-center p-5">
            <p className="text-center text-xl text-[#0E0E2C]">
              Assessment misconduct
            </p>
            <p className="text-center text-sm text-helper-text">
              We regret to inform you that your assessment has been terminated
              due to repeated violations of the assessment rules, despite
              several warnings. As a result of this misconduct, we have
              discontinued your assessment and submitted the necessary report.
            </p>
          </div>

          <div className="flex h-[5rem] items-center justify-between gap-x-4 rounded-b-2xl bg-[#FEF2F2] p-5 font-light">
            <p className="text-center text-sm text-[#450A0A]">
              Need help? send an email to
            </p>
            <LinkButton
              href={'/'}
              className="rounded-md bg-white p-2 px-3 text-[#450A0A]"
              onClick={closeSubmittedModal}
            >
              <EMAIL>
            </LinkButton>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default ForceSubmitTestModal;
