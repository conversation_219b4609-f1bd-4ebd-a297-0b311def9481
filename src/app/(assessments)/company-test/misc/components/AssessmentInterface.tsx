'use client';

import { <PERSON><PERSON>, Loader } from '@/components/shared';
import { cn } from '@/utils';
import { Dialog } from '@headlessui/react';
import { format } from 'date-fns';
import { XIcon } from 'lucide-react';
import React, { useContext } from 'react';
import toast from 'react-hot-toast';
import { useNetworkState } from 'react-use';
import { UseTimeCountingContext } from '../../layout';
import { useStartAssessment } from '../api';
import { useResumeAssessments } from '../api/resumeAssessment';
import { useSendCandidateAnswers } from '../api/sendAnswer';
import { TSendProctorReportData } from '../api/sendProctoringReport';
import {
  useAssessmentDetails,
  useAssessmentSectionsStore,
  UseCandidateStatusStore,
  useProctoringReport,
} from '../store';
import { useOfflineAnswersDetails } from '../store/offlneAnswers';
import AssessmentQuestionAnswer from './AssessmentQuestionAnswer';
import AssessmentQuestionsList from './AssessmentQuestionsList';
import AssessmentQuestionsMap from './AssessmentQuestionsMap';
import CountdownC from './CountdownC';
import { SocketProgressEvent } from '../utils/websocketUrl';
import { useMQTTTrackingService } from '../hooks/useMQTTTrackingService';

interface AssessmentInterfaceProps {
  isAssessmentActivated: boolean;
  activateAssessment: () => void;
  enterFullscreen: () => void;
  getProctoringData: () => TSendProctorReportData;
  children: React.ReactNode;
  screenCapturePermission: () => Promise<boolean>;
  openSubmitModal: () => void;
}
const AssessmentInterface: React.FC<AssessmentInterfaceProps> = ({
  isAssessmentActivated,
  children,
  activateAssessment,
  enterFullscreen,
  getProctoringData,
  screenCapturePermission,
  openSubmitModal,
}) => {
  var networkState = useNetworkState();
  const { setIsTimeCounting } = UseTimeCountingContext();


  const { proctoringReport } = useProctoringReport();
  const assessmentDetails = useAssessmentDetails(
    state => state.assessmentDetails
  );
  const candidateIsInSession = UseCandidateStatusStore(
    state => state.candidateStatus.isInSession
  );
  const candidateIsOnline = UseCandidateStatusStore(
    state => state.candidateStatus.isOnline
  );
  const candidateIsAReturnCandidate = UseCandidateStatusStore(
    state => state.candidateStatus.isReturning
  );
  const initializeTimer = UseCandidateStatusStore(
    state => state.initializeTimer
  );

  const setCandidateStatus = UseCandidateStatusStore(
    state => state.setCandidateStatus
  );

  const offlineAnswers = useOfflineAnswersDetails(state => state.data);

  React.useEffect(() => {
    if (networkState.online) {
      setCandidateStatus({ isOnline: true });
    } else {
      setCandidateStatus({ isOnline: false });
    }
  }, [networkState]);

  const { mutate: sendAnswer, isLoading: isAnswering } =
    useSendCandidateAnswers();
  const setOfflineAnswers = useOfflineAnswersDetails(
    state => state.setOfflineAnswersDetails
  );
  React.useEffect(() => {
    if (offlineAnswers.length > 0 && candidateIsOnline) {
      toast.success('Submitting offline answers...');
      sendAnswer(offlineAnswers, {
        onSuccess: () => {
          setOfflineAnswers([]);
        },
      });
    }
  }, [candidateIsOnline]);

  const initiatedStartAssessment = React.useRef<boolean>(false);
  const { mutate: startAssessment, isLoading: isStartingAssessment } =
    useStartAssessment();
  const { mutate: resumeAssessment, isLoading: isResumingAssessment } =
    useResumeAssessments();
  const { sendMessage: sendSocketMessage } = useMQTTTrackingService();

  const handleStartOrResumeAssessment = async () => {
    console.log(initiatedStartAssessment.current);
    if (initiatedStartAssessment.current) {
      return;
    }
    initiatedStartAssessment.current = true;
    // let screenCapture = await screenCapturePermission();
    // console.log('SCREEN CAPTURE', screenCapture);
    // if (!screenCapture) {
    // return;
    // }
    await enterFullscreen();

    console.log(candidateIsAReturnCandidate, "RETURN CANDIDATE")
    if (!candidateIsAReturnCandidate) {
      startAssessment(
        {
          email: assessmentDetails?.candidate_email as string,
          invite_id: assessmentDetails?.invite_id as string,
          assessment_id: assessmentDetails?.assessment?.id as string,
          result_id: assessmentDetails.result_id,
          proctoring_id: proctoringReport.id,
        },
        {
          onSuccess: (data: any) => {
            setIsTimeCounting(true);
            // resetProctoringFlags();
            setCandidateStatus({ isReturning: true, isInSession: true });
            activateAssessment();
            const totalTime =
              assessmentDetails.assessment.type === 'flexible'
                ? assessmentDetails?.assessment.time_limit * 1000
                : data?.data?.remaining_time * 1000;

            initializeTimer(totalTime);
            sendSocketMessage(SocketProgressEvent.ASSESSMENT_STARTED);
          },
          onError: (err: any) => {
            if (
              err?.response?.data?.detail ===
              'Invite has already been accepted.'
            ) {
              //  console.log('START ASS DIDNT WORK THE FIRST TIME');
            }
          },
        }
      );
    } else {
      console.log("RESUMPTION");
      if (!candidateIsInSession) {

        sendSocketMessage(SocketProgressEvent.ASSESSMENT_STARTED);

        console.log("RESUMPTION NOT IN SESSION");
        setCandidateStatus({ isReturning: true, isInSession: true });

        setIsTimeCounting(true);

        activateAssessment();

        enterFullscreen();
        return;
      }
      //   resumeAssessment(
      //     {
      //       email: assessmentDetails?.candidate_email as string,
      //       invite_id: assessmentDetails?.invite_id as string,
      //       assessment_id: assessmentDetails?.assessment?.id as string,
      //         proctoring_id: proctoringReport.id,
      //
      //     },
      //     {
      //       onSuccess: (data: any) => {
      //         setIsTimeCounting(true);
      //         console.log(data, 'FROM RESUME');
      //         //resetProctoringFlags();
      //         activateAssessment();
      //         enterFullscreen();
      //         setCandidateStatus({ isInSession: true });
      //
      //         const totalTime =
      //           assessmentDetails.assessment.type === 'flexible'
      //             ? assessmentDetails?.assessment.time_limit * 1000
      //             : data?.data?.remaining_time * 1000;
      //
      //         initializeTimer(totalTime);
      //       },
      //       onError: (err: any) => {
      //         // Handle error if needed
      //       },
      //     }
      //   );
    }
    initiatedStartAssessment.current = false;

    activateAssessment();
    setCandidateStatus({ isReturning: false });
  };

  const panelOpen = useAssessmentSectionsStore(state => state.panelOpen);
  const togglePanel = useAssessmentSectionsStore(state => state.togglePanel);
  return (
    <article
      className={cn(
        'relative grid h-full max-h-full min-h-[500px] max-w-full',
        'grid-cols-[100px,1fr]',
        'md:grid-cols-[250px,1fr]',
        'lg:grid-cols-[minmax(250px,0.35fr),1fr,minmax(250px,0.5fr)]',
        'gap-1 overflow-y-hidden lg:gap-5'
      )}
    >
      <AssessmentQuestionsMap candidateIsInSession={candidateIsInSession}>
        {children}
      </AssessmentQuestionsMap>

      {(!candidateIsInSession || !isAssessmentActivated) && (
        <div
          className={cn(
            'flex h-full items-center justify-center rounded-2xl bg-white transition-all'
          )}
        >
          {isStartingAssessment || isResumingAssessment ? (
            <div className="flex h-full w-full items-center justify-center p-10">
              <Loader className="z-20" width={50} height={50} />
            </div>
          ) : (
            <div className="w-[90%] max-w-[400px] rounded-xl bg-primary-light-hover p-5 lg:p-10">
              {!candidateIsAReturnCandidate ? (
                <p className="py-2 text-center text-primary">
                  Ready to start? Click the ‘Start assessment’ button below
                </p>
              ) : (
                <div className="text-primary">
                  <p className="flex items-center gap-x-4">
                    Assessment Time:
                    <span className="font-semibold 2xl:text-lg">
                      {format(
                        new Date(
                          assessmentDetails.assessment.time_limit * 1000
                        ),
                        'HH:mm:ss'
                      )}
                    </span>
                  </p>
                  <div className="flex items-center gap-x-4">
                    Time Left:
                    <span>
                      <CountdownC />
                    </span>
                  </div>
                </div>
              )}

              <button
                className="w-full rounded-md bg-primary p-3 text-white"
                onClick={handleStartOrResumeAssessment}
              >
                {candidateIsAReturnCandidate
                  ? 'Resume Assessment'
                  : 'Start Assessment '}
              </button>
            </div>
          )}
        </div>
      )}
      {candidateIsInSession && isAssessmentActivated && (
        <AssessmentQuestionAnswer getProctoringData={getProctoringData} />
      )}
      <div className="hidden overflow-hidden lg:flex">
        <AssessmentQuestionsList isAssessmentActivated={isAssessmentActivated}>
          {children}
        </AssessmentQuestionsList>
      </div>
      <div className={cn({ hidden: !panelOpen }, 'overflow-hidden lg:hidden')}>
        <Dialog open={panelOpen} onClose={togglePanel}>
          <div
            className="fixed inset-0 z-30 bg-[#000000aa]"
            onClick={togglePanel}
          >
            <Dialog.Panel
              className={
                'absolute left-1/2 top-1/2 flex h-screen w-full -translate-x-1/2 -translate-y-1/2 flex-col rounded-2xl bg-white'
              }
            >
              <div className="z-10 -mb-[48px] flex w-full justify-end gap-4 p-1 pb-3">
                <Button variant="outlined" size="capsule" onClick={togglePanel}>
                  <XIcon size={24} />
                </Button>
              </div>
              <AssessmentQuestionsList
                isAssessmentActivated={isAssessmentActivated}
              >
                {children}
              </AssessmentQuestionsList>
            </Dialog.Panel>
          </div>
        </Dialog>
      </div>
    </article>
  );
};

export default AssessmentInterface;
