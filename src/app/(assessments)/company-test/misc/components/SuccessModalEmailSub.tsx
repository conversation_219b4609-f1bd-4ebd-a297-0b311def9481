import { Dialog } from '@headlessui/react';
import { useRouter } from 'next/navigation';
import React, { SetStateAction, useState } from 'react';
import { LinkButton } from '@/components/shared';

interface SuccessModalProps {
  isOpen: boolean;
  closeModal: () => void;
}

const SuccessModalEmailSub: React.FunctionComponent<SuccessModalProps> = ({
  isOpen,
  closeModal,
}) => {
  const router = useRouter();
  return (
    <Dialog open={isOpen} onClose={() => {}}>
      <div className="fixed inset-0 z-30 bg-[#000000aa]">
        <Dialog.Panel
          className={
            'absolute left-1/2 top-1/2 flex h-[19.5rem] w-[90%] -translate-x-1/2 -translate-y-1/2 flex-col rounded-2xl bg-[#F5F3FF] md:w-[30rem]'
          }
        >
          <>
            <div className="flex h-[64px] items-end justify-between rounded-t-2xl bg-primary px-10 py-[0.87rem] text-white">
              <p>Thank you</p>
            </div>

            <>
              <div className="flex h-full flex-col items-center justify-center p-5">
                <p className="text-center text-xl text-primary">
                  Thank you for your interest.
                </p>
                <p className="text-center text-sm text-helper-text">
                  To stay up to date with further actions and information
                  regarding the assessment and payments, tap button below to
                  join our Telegram channel.
                </p>

                <div className="mt-6 flex w-full flex-col justify-center gap-3 md:flex-row md:gap-5">
                  <LinkButton
                    href={'https://chat.whatsapp.com/KQt5eYUCwWlBRxnYgRKaK0'}
                    className=" flex w-full max-w-[300px] items-center  justify-center rounded-md bg-[#25d366] p-2 px-3 text-white hover:bg-[#25d366]/80"
                    // onClick={() => { router.push('/') }}
                  >
                    Join our whatsapp
                  </LinkButton>
                  <LinkButton
                    href={'https://t.me/+EkB94thMy6Q0ZTU0'}
                    className="flex w-full max-w-[300px] items-center  justify-center rounded-md bg-primary p-2 px-3 text-white"
                    // onClick={() => { router.push('/') }}
                  >
                    Join our telegram
                  </LinkButton>
                </div>
              </div>
            </>
          </>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default SuccessModalEmailSub;
