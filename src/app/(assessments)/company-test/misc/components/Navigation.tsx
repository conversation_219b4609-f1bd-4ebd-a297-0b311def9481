'use client';

import { usePathname } from 'next/navigation';
import React, { useContext, useEffect, useState } from 'react';
import Countdown from 'react-countdown';
import { But<PERSON>, ToolTip } from '@/components/shared';
import { EyeSlash, Upload } from '@/components/shared/icons';
import { useBooleanStateControl } from '@/hooks';
import { cn } from '@/utils';
import { SubmitModalContext } from '../../layout';
import ClockIcon from '../icons/ClockIcon';
import EyeIcon from '../icons/EyeIcon';
import GetLinkedLogoDark from '../icons/GetLinkedLogoDark';
import PauseIcon from '../icons/PauseIcon';
import { UseCandidateStatusStore } from '../store';
import { useAssessmentDetails } from '../store/assessmentStore';
import CountdownC from './CountdownC';
import PauseModal from './PauseTestModal';
import { useIsMobile } from '@/hooks/useMobile';
import { ArrowRight3 } from 'iconsax-react';

const Navigation = () => {
  const [isTimerShowing, setIsTimerShowing] = useState(true);
  const pathname = usePathname();
  const { assessmentDetails } = useAssessmentDetails();
  console.log(assessmentDetails, "assessment details")


  const timeLeft = UseCandidateStatusStore(
    state => state.candidateStatus.timeLeft
  );
  const isInSession = UseCandidateStatusStore(
    state => state.candidateStatus.isInSession
  );
  const setCandidateStatus = UseCandidateStatusStore(
    state => state.setCandidateStatus
  );

  const openSubmitModal = useContext(SubmitModalContext).setIsSubmitModalOpen;

  const {
    state: isPauseModalOpen,
    setTrue: openPauseModal,
    setFalse: closePauseModal,
  } = useBooleanStateControl();

  const handleModalOpening = () => {
    if (openSubmitModal) {
      openSubmitModal(true);
    }
  };


  const onClick = React.useCallback(
    () => setIsTimerShowing(isTimerShowing => !isTimerShowing),
    [],
  );


  const isMobile = useIsMobile();

  return pathname !== '/company-test' ? (
    <div className="sticky top-0 z-20 flex h-[60px] w-full shrink-0 items-center justify-between bg-white px-1 md:px-10 2xl:h-[80px]">
      <div className="flex items-center gap-x-2">
        <GetLinkedLogoDark className="max-2xl:w-[40px]" />
        <div className="flex flex-col">
          { }
          <p className="font-semibold text-black 2xl:text-xl">
            {assessmentDetails?.assessment?.role?.name}
          </p>
          <p className="text-xs font-light text-[#8C8CA1] hidden lg:block">
            Skill Assessment Test
          </p>
        </div>
      </div>
      <div className="flex items-center gap-x-4">
        <div>
          <span className='text-[#755AE2] font-semibold hidden sm:block' data-hj-whitelist>{assessmentDetails.candidate_email}</span>
        </div>

        {isInSession && (
          <Button
            className="rounded-md bg-primary text-white"
            onClick={handleModalOpening}
          >
            {isMobile ? <ArrowRight3 color="white" width={20} height={20} /> : "Submit Assessment"}
          </Button>
        )}
        {
          isMobile ? <CountdownC isTimerShowing={isTimerShowing} /> : <>
            <CountdownC isTimerShowing={isTimerShowing} />
            <ToolTip asChild content={isTimerShowing ? 'Hide timer' : 'Show timer'}>
              <button
                className="flex h-10 w-10 items-center justify-center rounded-md bg-primary-light"
                onClick={onClick}
              >
                {isTimerShowing ? (
                  <EyeSlash fill="#755AE2" width={20} height={20} />
                ) : (
                  <EyeIcon width={20} height={20} />
                )}
              </button>
            </ToolTip>
          </>

        }
        {/* {
          candidateStatus?.isInSession &&
          assessmentDetails?.assessment?.type === 'flexible' &&
          candidateStatus.isReturning && (
            <button
              className="flex h-9 w-9 items-center justify-center rounded-md bg-primary-light"
              onClick={openPauseModal}
            >
              <PauseIcon />
            </button>
          )} */}
      </div>

      {/* <PauseModal
        isOpen={isPauseModalOpen}
        closeModal={closePauseModal}
      /> */}
    </div>
  ) : null;
};

export default Navigation;
