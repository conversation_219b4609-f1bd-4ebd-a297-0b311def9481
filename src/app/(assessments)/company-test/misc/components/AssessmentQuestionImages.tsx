'use client';

import { CloseCircle } from 'iconsax-react';
import { <PERSON><PERSON><PERSON><PERSON>, ArrowRight, ChevronLeft, ChevronRight } from 'lucide-react';
import Image from 'next/image';
import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog, DialogContent } from '@/components/shared';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/shared/tooltip';
import { useAllAssessmentQuestions, UseCandidateStatusStore } from '../store';

export default function AssessmentQuestionImages() {
  const allQuestions = useAllAssessmentQuestions();
  const currentQuestionId = UseCandidateStatusStore(
    state => state.candidateStatus.currentQuestionId
  );
  const currentQuestion = allQuestions.find(
    question => question.id === currentQuestionId
  );
  const images = currentQuestion?.images || [];

  const [isOpen, setIsOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const openModal = (index: number) => {
    setCurrentImageIndex(index);
    setIsOpen(true);
  };

  const closeModal = () => setIsOpen(false);
  const nextImage = () =>
    setCurrentImageIndex(prevIndex => (prevIndex + 1) % images.length);
  const prevImage = () =>
    setCurrentImageIndex(
      prevIndex => (prevIndex - 1 + images.length) % images.length
    );

  return (
    <div>
      <div className="flex flex-wrap items-center gap-4">
        {images.map((image_url, index) => (
          <TooltipProvider key={index}>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="relative h-[200px] w-[200px] cursor-pointer overflow-hidden rounded-xl transition-transform hover:scale-105"
                  onClick={() => openModal(index)}                
                >
                  <Image
                    src={image_url}
                    alt={`Image ${index + 1}`}
                    fill
                    sizes="200px"
                    style={{ objectFit: 'cover' }}
                  />
                  <div className="absolute bottom-3 left-3 z-50 rounded-md bg-[#0000007a] p-2 py-1 text-white">
                    Tap to view
                  </div>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>Click to view full size image</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ))}
      </div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="h-full max-h-[90vh] w-full max-w-[90vw] p-0">
          <div className="relative flex h-[95%] w-[95%] items-center justify-center">
            <div className="max-w-[80vw]] relative h-full w-full">
              <Image
                src={images[currentImageIndex]}
                alt={`Full size image ${currentImageIndex + 1}`}
                fill
                style={{ objectFit: 'contain' }}
                priority
              />
            </div>
            <Button
              variant="outlined"
              size="tiny"
              className="absolute right-4 top-4 bg-black/50 text-white hover:bg-black/70"
              onClick={closeModal}
            >
              <CloseCircle className="h-7 w-7" />
              <span className="">Close</span>
            </Button>
            <Button
              variant="outlined"
              size="icon"
              className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/50 text-white hover:bg-black/70"
              onClick={prevImage}
            >
              <ChevronLeft className="h-6 w-6" />
              <span className="sr-only">Previous image</span>
            </Button>
            <Button
              variant="outlined"
              size="icon"
              className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/50 text-white hover:bg-black/70"
              onClick={nextImage}
            >
              <ChevronRight className="h-6 w-6" />
              <span className="sr-only">Next image</span>
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
