'use client';

import React, { PropsWithChildren, useMemo } from 'react';
import { cn } from '@/utils';
import { useSendCandidateAnswers } from '../api/sendAnswer';
import {
  useAllAssessmentQuestions,
  useAllAssessmentSections,
  useAnsweredQuestions,
  useAssessmentDetails,
  UseCandidateStatusStore,
} from '../store';
import { useAnsweredQuestionsDetails } from '../store/answeredQuestionsStore';
import { QuestionSet } from '../store/assessmentStore';
import { useOfflineAnswersDetails } from '../store/offlneAnswers';

const AssessmentQuestionsMap: React.FC<
  PropsWithChildren & { candidateIsInSession: boolean }
> = ({ children }) => {
  const assesmentDetails = useAssessmentDetails(
    state => state.assessmentDetails
  );

  let questionNumber = 1;

  const setCandidateStatus = UseCandidateStatusStore(
    state => state.setCandidateStatus
  );

  const sections = useAllAssessmentSections();

  const allQuestions = useAllAssessmentQuestions();

  const currentQuestionId = UseCandidateStatusStore(
    state => state.candidateStatus.currentQuestionId
  );

  const currentQuestion = allQuestions.find(
    question => question.id === currentQuestionId
  );

  const answeredQuestions = useAnsweredQuestions();

  return (
    <div className="flex flex-col overflow-y-auto">
      <div className="flex flex-col rounded-xl bg-white sm:p-1 md:p-2 lg:p-4">
        <div className="mb-2 lg:hidden">{children}</div>

        <header>
          <h4 className="text-center text-sm text-header-text sm:text-base md:text-left ">
            Questions Map
          </h4>

          <p className="text-xs text-helper-text">
            Answered are {answeredQuestions.length}, unanswered are{' '}
            {allQuestions.length - answeredQuestions.length}
          </p>
        </header>

        {Object.entries(sections).map(
          ([_, { section_name, question_set }], index) => (
            <section key={index} className="mb-5 flex flex-col gap-4 py-4">
              <h2 className="text-xs text-body-text">
                {' '}
                ({index + 1}) {section_name}{' '}
              </h2>

              <article className="grid gap-x-4 gap-y-2 md:grid-cols-4">
                {question_set.map((question, innerIndex) => {
                  const currentNumber = questionNumber++;

                  return (
                    <QuestionMapButton
                      key={innerIndex}
                      question={question}
                      questionNumber={currentNumber}
                    />
                  );
                })}
              </article>
            </section>
          )
        )}
      </div>
    </div>
  );
};

export default AssessmentQuestionsMap;

const QuestionMapButton = ({
  question,
  questionNumber,
}: {
  question: QuestionSet;
  questionNumber: number;
}) => {
  const setCandidateStatus = UseCandidateStatusStore(
    state => state.setCandidateStatus
  );

  const sections = useAllAssessmentSections();

  const assessmentDetails = useAssessmentDetails(
    state => state.assessmentDetails
  );

  const allQuestions = useAllAssessmentQuestions();

  const currentQuestionId = UseCandidateStatusStore(
    state => state.candidateStatus.currentQuestionId
  );

  const answeredQuestions = useAnsweredQuestions();

  const isCurrentQuestion = currentQuestionId === question.id;

  const candidateIsOnline = UseCandidateStatusStore(
    state => state.candidateStatus.isOnline
  );

  const previouslyAnsweredQuestions = useAnsweredQuestionsDetails(
    state => state.data
  );

  const isFromPreviousQuestion = previouslyAnsweredQuestions.includes(
    question.id
  );

  const offlineAnswers = useOfflineAnswersDetails(state => state.data);

  const setOfflineAnswers = useOfflineAnswersDetails(
    state => state.setOfflineAnswersDetails
  );

  const { mutate: sendAnswer, isLoading: isAnswering } =
    useSendCandidateAnswers();

  const handleButtonClick = async () => {
    if (isFromPreviousQuestion) {
      return;
    }

    const LocalCurrentQuestionId = currentQuestionId;

    const currentQuestion = allQuestions.find(
      question => question.id === currentQuestionId
    );

    setCandidateStatus({ currentQuestionId: question.id });

    const data = {
      candidate: assessmentDetails.candidate_email,

      invite_id: assessmentDetails.invite_id,

      section_id:
        sections.find(section =>
          section.question_set
            .map(({ id }) => id)
            .includes(LocalCurrentQuestionId)
        )?.id || '',

      question: LocalCurrentQuestionId,

      answer: currentQuestion?.candidate_answer || ['-'],
    };

    if (LocalCurrentQuestionId) {
      if (candidateIsOnline) {
        sendAnswer(
          {
            candidate: assessmentDetails.candidate_email,

            invite_id: assessmentDetails.invite_id,

            section_id:
              sections.find(section =>
                section.question_set
                  .map(({ id }) => id)
                  .includes(LocalCurrentQuestionId)
              )?.id || '',

            question: LocalCurrentQuestionId,

            answer: currentQuestion?.candidate_answer || ['-'],
          },
          {
            onSuccess() {
              setCandidateStatus({ currentQuestionId: question.id });
            },

            onError() {
              setCandidateStatus({ currentQuestionId: question.id });
            },
          }
        );
      } else {
        setOfflineAnswers([...offlineAnswers, data]);

        setCandidateStatus({ currentQuestionId: question.id });
      }
    }
  };

  return (
    <button
      className={cn(
        ' flex h-7 w-7 cursor-pointer items-center justify-center rounded-md border border-[#D9D9D9] bg-white p-0',

        (answeredQuestions
          .map(({ id: question_id }) => question_id)
          .includes(question.id) ||
          isCurrentQuestion ||
          isFromPreviousQuestion) &&
          'border-primary',

        isFromPreviousQuestion && 'cursor-not-allowed border-danger'
      )}
      onClick={handleButtonClick}
    >
      <div
        className={cn(
          'flex h-5 w-5 items-center justify-center rounded-md',

          answeredQuestions

            .map(({ id: question_id }) => question_id)

            .includes(question.id)
            ? 'bg-primary text-white'
            : 'bg-white'
        )}
      >
        <p className="text-xs font-medium">{questionNumber}</p>
      </div>
    </button>
  );
};
