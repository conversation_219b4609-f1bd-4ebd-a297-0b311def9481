'use client';

import React, { useCallback, useMemo, useState } from 'react';
import { TickCircle } from 'iconsax-react';

import { cn } from '@/utils';
import { Label, RichTextEditor, ToggleGroup, ToggleGroupItem } from '@/components/shared';

import { QuestionSet } from '../store/assessmentStore';
import { useAnswerQuestion, useAssessmentSectionsStore } from '../store';
import { debounce } from 'lodash';
import { useDebouncedCallback } from 'use-debounce';
// import { TRUE_OR_FALSE } from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants'

interface QuestionTypeProps {
  question: QuestionSet;
}
export const QuestionTypeEnums = {
  multiple_choice: 'Multiplechoice Question',
  true_false: 'True or False',
  fill_in_the_blanks: 'Fill in the blank(s)',
  essay: 'Essay Question',
  multiple_response: 'Multiple Select',
  coding: 'Coding',
};


const TRUE_OR_FALSE = [
  { value: "true", readable_string: "True" },
  { value: "false", readable_string: "False" },
];

// export const EssayAnswer = ({ question }: QuestionTypeProps) => {
//   const getQuestion = useAssessmentSectionsStore(state => state.getQuestion);
//   const [localAnswer, setLocalAnswer] = useState("");
//   const answerQuestion = useAnswerQuestion;

//   const debouncedAnswerQuestion = useDebouncedCallback(
//     (answer: string) => {
//       answerQuestion(question.id, [answer]);
//     },
//     20
//   );

//   React.useEffect(() => {
//     const currentQuestion = getQuestion(question.id);
//     setLocalAnswer(currentQuestion?.candidate_answer?.[0] || "");
//   }, [question.id, getQuestion])

//   const handleChange = (value: string) => {
//     setLocalAnswer(value);
//     debouncedAnswerQuestion(value);
//   };

//   return (
//     <div>
//       <RichTextEditor
//         initialContent={localAnswer}
//         value={localAnswer}
//         className="min-h-[200px]"
//         onChange={handleChange}
//       />
//     </div>
//   );
// };

export const EssayAnswer = ({ question }: QuestionTypeProps) => {
  const getQuestion = useAssessmentSectionsStore(state => state.getQuestion);
  const answerQuestion = useAnswerQuestion;

  const [localAnswer, setLocalAnswer] = useState("");
  const debouncedAnswerQuestion = useDebouncedCallback(
    (answer: string) => {
      answerQuestion(question.id, [answer]);
    },
    20
  );

  React.useEffect(() => {
    const currentQuestion = getQuestion(question.id);
    setLocalAnswer(currentQuestion?.candidate_answer?.[0] || "");
  }, [question.id, getQuestion])

  const handleChange = (value: string) => {
    setLocalAnswer(value);
    debouncedAnswerQuestion(value);
  };

  return (
    <div>
      <RichTextEditor
        initialContent={localAnswer}
        value={localAnswer}
        className="min-h-[200px]"
        onChange={handleChange}
        disableCopyPaste={true}
      />
    </div>
  );
};

export const MultipleChoiceAnswer = ({ question }: QuestionTypeProps) => {
  const getQuestion = useAssessmentSectionsStore(state => state.getQuestion);
  const [localAnswer, setLocalAnswer] = useState<string | null>(null);
  const answerQuestion = useAnswerQuestion;

  const debouncedAnswerQuestion = useMemo(() => debounce((answer: string) => {
    answerQuestion(question.id, answer ? [answer] : []);
  }), [question.id, answerQuestion]);

  React.useEffect(() => {
    const currentQuestion = getQuestion(question.id);
    setLocalAnswer(currentQuestion?.candidate_answer?.[0] || null);
  }, [question.id, getQuestion]);

  const handleValueChange = useCallback((value: string) => {
    setLocalAnswer(value);
    debouncedAnswerQuestion(value);
  }, [debouncedAnswerQuestion]);

  return (
    <>
      <h6 className="font-bold text-body-text mb-4">
        Select only one
      </h6>
      <ToggleGroup type="single" className="flex flex-col items-start space-y-2" value={localAnswer || undefined}>
        {
          question.answer_options?.map((option, index) => (
            <div key={index} className="flex items-center w-full">
              <ToggleGroupItem
                value={option}
                variant="unstyled"
                data-state={localAnswer === option ? "on" : "off"}
                onClick={() => handleValueChange(option)}
                className={cn(
                  "flex items-center justify-start w-full p-3 rounded-md border transition-colors",
                )}
              >
                <div
                  className={cn(
                    "flex items-center justify-center h-5 w-5 rounded-full border mr-3",
                    localAnswer === option ? 'border-primary bg-transparent' : 'border-[#D6D6D6]'
                  )}
                >
                  {
                    localAnswer === option && (
                      <TickCircle className="h-4 w-4 text-primary" variant="Bold" />
                    )
                  }
                </div>
                <Label>{option}</Label>
              </ToggleGroupItem>
            </div>
          ))
        }
      </ToggleGroup>
    </>
  );
};


// export const MultipleResponseAnswer = ({ question }: QuestionTypeProps) => {
//   const getQuestion = useAssessmentSectionsStore(state => state.getQuestion);
//   const [selectedAnswers, setSelectedAnswers] = React.useState<string[]>([])
//   const answerQuestion = useAnswerQuestion

//   React.useEffect(() => {
//     const currentQuestion = getQuestion(question.id);
//     setSelectedAnswers(currentQuestion?.candidate_answer || []);
//   }, [question.id, getQuestion])

//   const handleValueChange = (value: string) => {
//     const updatedAnswers = selectedAnswers.includes(value)
//       ? selectedAnswers.filter(answer => answer !== value)
//       : [...selectedAnswers, value]

//     setSelectedAnswers(updatedAnswers)
//     answerQuestion(question.id, updatedAnswers)
//   }

//   return (
//     <>
//       <h6 className="font-bold text-body-text mb-4">
//         Select all that apply
//       </h6>
//       <ToggleGroup type="multiple" className="flex flex-col items-start space-y-2">
//         {question.answer_options?.map((option, index) => (
//           <div key={index} className="flex items-center w-full">
//             <ToggleGroupItem
//               value={option}
//               variant="unstyled"
//               data-state={selectedAnswers.includes(option) ? "on" : "off"}
//               onClick={() => handleValueChange(option)}
//               className={cn(
//                 "flex items-center justify-start w-full p-3 rounded-md border transition-colors",
//               )}
//             >
//               <div className={cn(
//                 "flex items-center justify-center h-5 w-5 rounded-full border mr-3",
//                 selectedAnswers.includes(option) ? 'border-primary bg-transparent' : 'border-[#D6D6D6]'
//               )}>
//                 {selectedAnswers.includes(option) && (
//                   <TickCircle className="h-4 w-4 text-primary" variant="Bold" />
//                 )}
//               </div>
//               <Label>{option}</Label>
//             </ToggleGroupItem>
//           </div>
//         ))}
//       </ToggleGroup>
//     </>
//   )
// }


export const MultipleResponseAnswer = ({ question }: QuestionTypeProps) => {
  const getQuestion = useAssessmentSectionsStore(state => state.getQuestion);
  const [selectedAnswers, setSelectedAnswers] = React.useState<string[]>([]);
  const answerQuestion = useAnswerQuestion;

  React.useEffect(() => {
    const currentQuestion = getQuestion(question.id);
    setSelectedAnswers(currentQuestion?.candidate_answer || []);
  }, [question.id, getQuestion]);

  // const handleValueChange = useCallback((value: string) => {
  //   const updatedAnswers = selectedAnswers.includes(value)
  //     ? selectedAnswers.filter(answer => answer !== value)
  //     : [...selectedAnswers, value];

  //   setSelectedAnswers(updatedAnswers);
  //   answerQuestion(question.id, updatedAnswers);
  // }, []);
  const handleValueChange = (value: string) => {
    const updatedAnswers = selectedAnswers.includes(value)
      ? selectedAnswers.filter(answer => answer !== value)
      : [...selectedAnswers, value]

    setSelectedAnswers(updatedAnswers)
    answerQuestion(question.id, updatedAnswers)
  }

  return (
    <>
      <h6 className="font-bold text-body-text mb-4">
        Select all that apply
      </h6>
      <ToggleGroup type="multiple" className="flex flex-col items-start space-y-2">
        {question.answer_options?.map((option, index) => (
          <div key={index} className="flex items-center w-full">
            <ToggleGroupItem
              value={option}
              variant="unstyled"
              data-state={selectedAnswers.includes(option) ? "on" : "off"}
              onClick={() => handleValueChange(option)}
              className={cn(
                "flex items-center justify-start w-full p-3 rounded-md border transition-colors",
              )}
            >
              <div className={cn(
                "flex items-center justify-center h-5 w-5 rounded-full border mr-3",
                selectedAnswers.includes(option) ? 'border-primary bg-transparent' : 'border-[#D6D6D6]'
              )}>
                {selectedAnswers.includes(option) && (
                  <TickCircle className="h-4 w-4 text-primary" variant="Bold" />
                )}
              </div>
              <Label>{option}</Label>
            </ToggleGroupItem>
          </div>
        ))}
      </ToggleGroup>
    </>
  );
};

export const TrueFalseAnswer = ({ question }: QuestionTypeProps) => {
  const getQuestion = useAssessmentSectionsStore(state => state.getQuestion);
  const [currentAnswer, setCurrentAnswer] = useState<string | null>(null);
  const answerQuestion = useAnswerQuestion;

  React.useEffect(() => {
    const currentQuestion = getQuestion(question.id);
    setCurrentAnswer(currentQuestion?.candidate_answer?.[0] || null);
  }, [question.id, getQuestion]);

  const handleAnswerChange = (value: string) => {
    setCurrentAnswer(value);
    answerQuestion(question.id, [value]);
  };

  return (
    <>
      <h6 className="font-bold text-body-text mb-4">
        Answer true or false
      </h6>
      <ToggleGroup type="single" className="flex flex-col items-start space-y-2" value={currentAnswer || undefined}>
        {
          TRUE_OR_FALSE?.map((option, index) => (
            <div key={index} className="flex items-center w-full">
              <ToggleGroupItem
                value={option.value}
                variant="unstyled"
                data-state={currentAnswer === option.value ? "on" : "off"}
                onClick={() => handleAnswerChange(option.value)}
                className={cn(
                  "flex items-center justify-start w-full p-3 rounded-md border transition-colors",
                )}
              >
                <div
                  className={cn(
                    "flex items-center justify-center h-5 w-5 rounded-full border mr-3",
                    currentAnswer === option.value ? 'border-primary bg-transparent' : 'border-[#D6D6D6]'
                  )}
                >
                  {
                    currentAnswer === option.value && (
                      <TickCircle className="h-4 w-4 text-primary" variant="Bold" />
                    )
                  }
                </div>
                <Label>{option.readable_string}</Label>
              </ToggleGroupItem>
            </div>
          ))
        }
      </ToggleGroup>
    </>
  );
}

