import { Dialog } from '@headlessui/react';
import moment from 'moment';
import { useRouter } from 'next/navigation';
import React, { useEffect } from 'react';
import Countdown from 'react-countdown';

interface AssessmentNotAvailableProps {
  isOpen: boolean;
  isInterview?: boolean;
  onCountdownComplete?: () => void;
  error?: {
    detail: string;
    start_time?: string;
    deadline?: string;
    error?: string;
  };
}

const AssessmentNotAvailable: React.FunctionComponent<
  AssessmentNotAvailableProps
> = ({ isOpen, error, isInterview = false, onCountdownComplete }) => {
  const router = useRouter();
  const closeSubmittedModal = () => {};

  const renderer = ({
    formatted: { minutes, seconds, hours },
    completed,
  }: {
    formatted: {
      minutes: any;
      seconds: any;
      hours: any;
    };
    completed: boolean;
  }) => {
    if (completed) {
      // Render a completed state
      return <p className="text-sm text-primary">00:00</p>;
    } else {
      // Render a countdown
      return (
        <span className="text-xl font-semibold text-primary">
          {hours && hours} <span className="text-sm font-normal">hours</span>{' '}
          {minutes} <span className="text-sm font-normal">minutes</span>{' '}
          {seconds} <span className="text-sm font-normal">seconds</span>
        </span>
      );
    }
  };

  const handleCountdownComplete = () => {
    if (onCountdownComplete) {
      onCountdownComplete();
    }
  };

  const date = new Date();
  const startTime = error?.start_time;
  const deadline = error?.deadline;

  // Calculate countdown properly - if start time has passed, show 0
  const countdown = startTime
    ? Math.max(0, moment(startTime).valueOf() - date.getTime())
    : 0;

  // Only show countdown if there's actually time remaining
  const shouldShowCountdown = startTime && countdown > 0;

  // Auto-trigger countdown completion if start time has already passed
  useEffect(() => {
    if (startTime && countdown <= 0 && onCountdownComplete) {
      // Small delay to ensure component is fully mounted
      const timer = setTimeout(() => {
        onCountdownComplete();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [startTime, countdown, onCountdownComplete]);

  return (
    <Dialog open={isOpen} onClose={closeSubmittedModal}>
      <div className="fixed inset-0 z-30 bg-[#000000aa]">
        <Dialog.Panel
          className={
            'absolute left-1/2 top-1/2 flex h-[19.5rem] w-[30rem] -translate-x-1/2 -translate-y-1/2 flex-col rounded-2xl bg-white'
          }
        >
          <div className="flex h-[64px] items-end justify-between rounded-t-2xl bg-[#450A0A] px-10 py-[0.87rem] text-white">
            <p>Error Message</p>
          </div>
          <div className="flex h-full flex-col  justify-center p-5">
            <p className="text-2xl font-semibold text-[#450A0A]">
              {isInterview ? 'Interview' : 'Assessment'} Not Available
            </p>
            <p className="py-3 text-sm text-helper-text">
              {error?.detail || error?.error}
            </p>
            {error?.start_time && (
              <div>
                <p>
                  The {isInterview ? 'interview' : 'assessment'} will begin in:{' '}
                </p>
                {shouldShowCountdown && (
                  <Countdown
                    date={Date.now() + countdown}
                    renderer={renderer}
                    zeroPadTime={2}
                    daysInHours={true}
                    autoStart
                    onComplete={handleCountdownComplete}
                  />
                )}
                {!shouldShowCountdown && (
                  <p className="text-sm text-primary">Now</p>
                )}
              </div>
            )}
            {error?.deadline && (
              <p>
                The {isInterview ? 'interview' : 'assessment'} ended on{' '}
                {moment(deadline).format('MMMM Do YYYY, h:mm:ss a')}
              </p>
            )}
            <p className="text-sm text-helper-text">
              Contact:{' '}
              <span className="text-[#755AE2] underline">
                <EMAIL>
              </span>
            </p>
          </div>
          <div className="flex h-[5rem] justify-end gap-x-4 rounded-b-2xl bg-[#FEF2F2] p-5 font-light"></div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default AssessmentNotAvailable;
