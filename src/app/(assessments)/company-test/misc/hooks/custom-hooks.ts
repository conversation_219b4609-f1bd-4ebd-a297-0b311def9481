import { useEffect, useRef, useCallback, useState } from "react";

export function useWindowChange(onChangeConfirm: () => void) {
    const isBlurred = useRef(false);

    useEffect(() => {
        const handleBlur = () => {
            isBlurred.current = true;
        };

        const handleFocus = () => {
            if (isBlurred.current) {
                onChangeConfirm?.(); // only call if user actually left
                isBlurred.current = false;
            }
        };

        window.addEventListener("blur", handleBlur);
        window.addEventListener("focus", handleFocus);

        return () => {
            window.removeEventListener("blur", handleBlur);
            window.removeEventListener("focus", handleFocus);
        };
    }, [onChangeConfirm]);
}

