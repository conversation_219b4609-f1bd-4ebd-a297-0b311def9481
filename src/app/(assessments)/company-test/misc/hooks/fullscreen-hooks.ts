import { useState, useCallback, useEffect, useRef } from "react";


export const useFullScreen = (options: UseFullScreenOptions = {}): FullScreenHook => {
    const {
        autoEnter = false,
        exitOnUnmount = true,
        onBeforeEnter,
        onAfterEnter,
        onBeforeExit,
        onAfterExit,
        onDenied,
        retryDelay = 1000, // Default delay between retries
        maxRetries = 3     // Default max retries
    } = options;

    const [retryCount, setRetryCount] = useState(0);
    const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
    const timeoutCheck = useRef<NodeJS.Timeout | null>(null);
    const [fullscreenElement, setFullscreenElement] = useState<Element | null>(null);
    const [isDenied, setIsDenied] = useState<boolean>(false);
    const [previousElement, setPreviousElement] = useState<Element | null>(null);
    const [isAttemptingFullscreen, setIsAttemptingFullscreen] = useState<boolean>(false);

    const markDenied = useCallback(() => {
        console.log("Marking fullscreen as denied");
        if (timeoutCheck.current) {
            clearTimeout(timeoutCheck.current)
        }
        setIsDenied(true);
        onDenied?.(new Error("Fullscreen denied"), document.documentElement);
    }, [onDenied]);

    const getCurrentFullscreenElement = useCallback(() =>
        document.fullscreenElement ||
        (document as any).webkitFullscreenElement ||
        (document as any).mozFullScreenElement ||
        (document as any).msFullscreenElement ||
        null,
        []);

    const checkFullscreen = useCallback(() =>
        !!getCurrentFullscreenElement(),
        [getCurrentFullscreenElement]);

    const handleFullscreenChange = useCallback((e: any) => {
        const newElement = getCurrentFullscreenElement();
        const newState = !!newElement;
        // console.group("DEBUG FULLSCREEN EVENT CHANGE")
        console.log(newState,);
        // console.log(newElement)
        // console.log(e, "EVENT")
        // console.groupEnd();

        //setIsFullscreen(newState);
        setFullscreenElement(newElement);
        setIsAttemptingFullscreen(false);

        if (newState) {
            setIsDenied(false);
            setRetryCount(0);
            onAfterEnter?.(newElement);
        } else {
            onAfterExit?.(previousElement);
        }
    }, [onAfterEnter, onAfterExit, previousElement, getCurrentFullscreenElement]);

    const enterFullscreen = useCallback(async (
        element: Element = document.documentElement
    ): Promise<EnterFullscreenResult> => {
        try {
            setIsAttemptingFullscreen(true);
            setPreviousElement(element);
            if (onBeforeEnter) await Promise.resolve(onBeforeEnter(element));

            if (element.requestFullscreen) {
                await element.requestFullscreen();
            } else if ((element as any).webkitRequestFullscreen) {
                await (element as any).webkitRequestFullscreen();
            } else if ((element as any).mozRequestFullScreen) {
                await (element as any).mozRequestFullScreen();
            } else if ((element as any).msRequestFullscreen) {
                await (element as any).msRequestFullscreen();
            }
            console.log("Fullscreen entered successfully");
            return { success: true };
        } catch (error: any) {
            setIsAttemptingFullscreen(false);
            console.error('Fullscreen denied:', error);
            if (error?.name === 'NotAllowedError') {
                setIsDenied(true);
                onDenied?.(error, element);
                return { success: false, denied: true };
            }
            return { success: false };
        }
    }, [onBeforeEnter, onDenied]);

    const exitFullscreen = useCallback(async (): Promise<boolean> => {
        try {
            if (onBeforeExit) await Promise.resolve(onBeforeExit(previousElement));

            if (document.exitFullscreen) {
                await document.exitFullscreen();
            } else if ((document as any).webkitExitFullscreen) {
                await (document as any).webkitExitFullscreen();
            } else if ((document as any).mozCancelFullScreen) {
                await (document as any).mozCancelFullScreen();
            } else if ((document as any).msExitFullscreen) {
                await (document as any).msExitFullscreen();
            }

            return true;
        } catch (error) {
            console.error('Failed to exit fullscreen:', error);
            return false;
        }
    }, [onBeforeExit, previousElement]);

    const toggleFullscreen = useCallback(async (
        element: Element = document.documentElement
    ): Promise<boolean | EnterFullscreenResult> => {
        return isFullscreen ? await exitFullscreen() : await enterFullscreen(element);
    }, [isFullscreen, exitFullscreen, enterFullscreen]);

    // This is the key function combining your retry logic
    const attemptEnterFullscreen = useCallback((
        element: Element = document.documentElement,
        retriesLeft: number = maxRetries
    ) => {
        console.log(`ATTEMPT ENTER FULLSCREEN ${retriesLeft}`);
        console.log(isFullscreen, "IS FULLSCREEN ");
        console.log(isDenied, "IS DENIED CHECK");

        if (
            isFullscreen ||
            isDenied ||
            isAttemptingFullscreen
        ) {
            console.log("Skipping fullscreen attempt due to state conditions");
            if (timeoutCheck.current) {
                console.log("CLEARING TIMEOUT");
                clearTimeout(timeoutCheck.current)
            }
            return;
        }

        if (retriesLeft <= 0) {
            console.log("MARKED FULLSCREEN AS DENIED - max retries reached");
            markDenied();
            return;
        }

        // Reset retry count for UI tracking if needed
        setRetryCount(maxRetries - retriesLeft + 1);

        enterFullscreen(element).catch((e) => {
            console.warn('Enter fullscreen attempt failed', e);
        });

        if (timeoutCheck.current) {
            clearTimeout(timeoutCheck.current)
        }
        // Check again after delay
        timeoutCheck.current = setTimeout(() => {
            // We need to recheck the state here
            const currentIsFullscreen = checkFullscreen();
            console.log(currentIsFullscreen, "IS CURENTLY FUKKLSCREEN")
            if (!currentIsFullscreen && !isDenied && !isAttemptingFullscreen) {
                attemptEnterFullscreen(element, retriesLeft - 1);
            }
        }, retryDelay);
    }, [
        isFullscreen,
        isDenied,
        isAttemptingFullscreen,

        enterFullscreen,
        markDenied,
        maxRetries,
        retryDelay,
        checkFullscreen
    ]);

    useEffect(() => {
        const events = [
            'fullscreenchange',
            'webkitfullscreenchange',
            'mozfullscreenchange',
            'MSFullscreenChange',
        ];

        events.forEach((evt) =>
            document.addEventListener(evt, handleFullscreenChange)
        );

        // Initial state
        setIsFullscreen(checkFullscreen());
        setFullscreenElement(getCurrentFullscreenElement());

        if (autoEnter) attemptEnterFullscreen();

        return () => {
            events.forEach((evt) =>
                document.removeEventListener(evt, handleFullscreenChange)
            );
            if (exitOnUnmount && checkFullscreen()) exitFullscreen();
        };
    }, [
        autoEnter,
        exitOnUnmount,
        handleFullscreenChange,
        checkFullscreen,
        getCurrentFullscreenElement,
        attemptEnterFullscreen,
        exitFullscreen
    ]);

    return {
        isFullscreen,
        isDenied,
        fullscreenElement,
        enterFullscreen,
        exitFullscreen,
        markDenied,
        toggleFullscreen,
        attemptEnterFullscreen,
        retryCount
    };
};

// Type definitions
interface UseFullScreenOptions {
    autoEnter?: boolean;
    exitOnUnmount?: boolean;
    onBeforeEnter?: (element: Element | null) => void | Promise<void>;
    onAfterEnter?: (element: Element | null) => void;
    onBeforeExit?: (element: Element | null) => void | Promise<void>;
    onAfterExit?: (element: Element | null) => void;
    onDenied?: (error: any, element: Element) => void;
    retryDelay?: number;
    maxRetries?: number;
}

interface EnterFullscreenResult {
    success: boolean;
    denied?: boolean;
}

interface FullScreenHook {
    isFullscreen: boolean;
    isDenied: boolean;
    fullscreenElement: Element | null;
    enterFullscreen: (element?: Element) => Promise<EnterFullscreenResult>;
    exitFullscreen: () => Promise<boolean>;
    markDenied: () => void;
    toggleFullscreen: (element?: Element) => Promise<boolean | EnterFullscreenResult>;
    attemptEnterFullscreen: (element?: Element, retriesLeft?: number) => void;
    retryCount: number;
}