import { useState, useEffect, useCallback, useRef } from 'react'
import mqtt, { MqttClient } from 'mqtt'
import { useAssessmentDetails } from '../store';
import { SocketProgressEvent } from '../utils/websocketUrl';

// interface UseMQTTTrackingServiceProps {
//     autoConnect?: boolean;
// }
// {
//     autoConnect = true
// }: UseMQTTTrackingServiceProps

export function useMQTTTrackingService() {

    const { candidate_email, assessment: { name }
    } = useAssessmentDetails(state => state.assessmentDetails);

    const connectUrl = `${process.env.NEXT_PUBLIC_ASSESSMENT_MQTT_SERVICE}`
    console.log('MQTT TRACKING connect URL:', connectUrl)
    const [isConnected, setIsConnected] = useState(false)
    const clientRef = useRef<MqttClient | null>(null)
    const STORAGE_KEY = 'unsentEvents';

    // Keep track of connection attempts
    const connectionAttemptsRef = useRef(0)
    const touchAttemptsRef = useRef(0)
    const maxConnectionAttempts = 3


    const connect = () => {
        let mqttClient: MqttClient | null = null
        console.log("ATTEMPTING TO CONNECT TO MQTT")

        mqttClient = mqtt.connect(connectUrl, {
            clientId: `mqtt_${Math.random().toString(16).slice(3)}`,
            clean: true,
            connectTimeout: 30000,
            reconnectPeriod: 5000,
            username: `${process.env.NEXT_PUBLIC_ASSESSMENT_MQTT_USERNAME}`,
            password: `${process.env.NEXT_PUBLIC_ASSESSMENT_MQTT_PASSWORD}`,
            keepalive: 60,
            protocol: 'wss',
            rejectUnauthorized: false
        })

        mqttClient.on('connect', () => {
            console.log('Connected to MQTT TRACKING broker')
            setIsConnected(true)
            connectionAttemptsRef.current = 0

            setTimeout(() => {
                console.log("CHECKING FOR UNSENT MESSAGES")
                const existing = sessionStorage.getItem(STORAGE_KEY);
                const messageQueue = existing ? JSON.parse(existing) : [];

                console.log("UNSENT MESSAGES", messageQueue)
                console.log(clientRef.current)
                console.log(isConnected)
                console.log("SENT MESSAGES")
                if (messageQueue.length > 0) {
                    console.log(`Found ${messageQueue.length} unsent messages. Sending now...`);

                    messageQueue.forEach((msg: any, index: number) => {
                        clientRef.current!.publish(
                            'events',
                            JSON.stringify(msg),
                            { qos: 1 },
                            (err) => {
                                if (err) {
                                    console.error(`Publish error on message ${index}:`, err);
                                } else {
                                    console.log(`Message ${index} sent successfully`);
                                    // Optionally, you can remove messages one by one or clear all at once after loop
                                }
                            }
                        );
                    });

                    // After sending all, clear sessionStorage
                    sessionStorage.removeItem(STORAGE_KEY);
                    console.log('Cleared unsent messages from sessionStorage');
                }

            }, 1000)
        })

        mqttClient.on('error', (err) => {
            console.error('MQTT TRACKING Connection error:', err)
            setIsConnected(false)
            connectionAttemptsRef.current++

            if (connectionAttemptsRef.current >= maxConnectionAttempts) {
                console.error('Max connection attempts reached')
                mqttClient?.end()
            }
        })

        mqttClient.on('disconnect', () => {
            console.log('Disconnected from MQTT broker')
            setIsConnected(false)
        })

        mqttClient.on('offline', () => {
            console.log('MQTT client is offline')
            setIsConnected(false)
        })

        mqttClient.on('reconnect', () => {
            console.log('Reconnecting to MQTT broker...')
        })

        clientRef.current = mqttClient
    }


    useEffect(() => {

        connect();


        return () => {
            if (clientRef.current) {
                console.log('Cleaning up MQTT connection')
                clientRef.current.end(true)
                clientRef.current = null
            }
        }
    }, [])

    const sendMessage = useCallback((event: SocketProgressEvent) => {
        console.log('Sending message to MQTT broker -- ', event)
        const payload = {
            candidate_email: candidate_email,
            event,
            assessment_name: name
        }
        if (!clientRef.current || !isConnected) {
            console.warn('MQTT client not connected — saving message to sessionStorage');

            const existing = sessionStorage.getItem(STORAGE_KEY);
            const messageQueue = existing ? JSON.parse(existing) : [];
            messageQueue.push(payload);
            sessionStorage.setItem(STORAGE_KEY, JSON.stringify(messageQueue));

            return Promise.resolve('MQTT client not connected — message saved to sessionStorage');
        }


        return new Promise((resolve, reject) => {

            clientRef.current!.publish(
                "events",
                JSON.stringify(payload),
                { qos: 1 },
                (err) => {
                    if (err) {
                        console.error('Publish error:', err)
                        reject(err)
                    } else {
                        console.log('Message sent successfully -- ', event)
                        resolve(true)
                    }
                }
            )
        })
    }, [isConnected])



    return {
        sendMessage,
        isConnected,
    }
}