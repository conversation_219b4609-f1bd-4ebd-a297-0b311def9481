import * as React from 'react';
import { SVGProps } from 'react';


const WebcamIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={35}
    height={35}
    viewBox="0 0 35 35"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx={17.5} cy={17.5} r={17.5} fill="" />
    <path
      d="M25.5 17.9175V18.585C25.5 21.255 24.8325 21.915 22.17 21.915H13.83C11.1675 21.915 10.5 21.2475 10.5 18.585V13.83C10.5 11.1675 11.1675 10.5 13.83 10.5H15"
      stroke="#E6E0FF"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M18 21.915V25.5"
      stroke="#E6E0FF"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.5 18.75H25.5"
      stroke="#E6E0FF"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.625 25.5H21.375"
      stroke="#E6E0FF"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M22.3951 16.0275H18.8251C17.7901 16.0275 17.4451 15.3375 17.4451 14.6475V12.0075C17.4451 11.1825 18.1201 10.5075 18.9451 10.5075H22.3951C23.1601 10.5075 23.7751 11.1225 23.7751 11.8875V14.6475C23.7751 15.4125 23.1601 16.0275 22.3951 16.0275Z"
      stroke="#E6E0FF"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M24.6824 14.94L23.7749 14.3025V12.2325L24.6824 11.595C25.1324 11.2875 25.4999 11.475 25.4999 12.0225V14.52C25.4999 15.0675 25.1324 15.255 24.6824 14.94Z"
      stroke="#E6E0FF"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default WebcamIcon;