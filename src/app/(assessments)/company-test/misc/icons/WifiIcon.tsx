import * as React from 'react';
import { SVGProps } from 'react';


const WifiIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={35}
    height={35}
    viewBox="0 0 35 35"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx={17.5} cy={17.5} r={17.5} fill="" />
    <path
      d="M12.6826 17.88C15.9076 15.39 20.1001 15.39 23.3251 17.88"
      stroke="#E6E0FF"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.5 15.27C15.045 11.76 20.955 11.76 25.5 15.27"
      stroke="#E6E0FF"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.0925 20.6175C16.455 18.7875 19.5375 18.7875 21.9 20.6175"
      stroke="#E6E0FF"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16.05 23.3625C17.235 22.4475 18.7726 22.4475 19.9576 23.3625"
      stroke="#E6E0FF"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default WifiIcon;