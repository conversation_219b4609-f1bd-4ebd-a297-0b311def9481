import * as React from 'react';
import { SVGProps } from 'react';


const MicIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <rect width={16} height={16} rx={8} fill="" />
    <path
      d="M8 11.2291C6.45 11.2291 5.1875 9.96665 5.1875 8.41665V6.33331C5.1875 4.78331 6.45 3.52081 8 3.52081C9.55 3.52081 10.8125 4.78331 10.8125 6.33331V8.41665C10.8125 9.96665 9.55 11.2291 8 11.2291ZM8 4.14581C6.79583 4.14581 5.8125 5.12498 5.8125 6.33331V8.41665C5.8125 9.62498 6.79583 10.6041 8 10.6041C9.20417 10.6041 10.1875 9.62498 10.1875 8.41665V6.33331C10.1875 5.12498 9.20417 4.14581 8 4.14581Z"
      fill="white"
    />
    <path
      d="M8 12.4791C5.75833 12.4791 3.9375 10.6583 3.9375 8.41665V7.58331C3.9375 7.41248 4.07917 7.27081 4.25 7.27081C4.42083 7.27081 4.5625 7.41248 4.5625 7.58331V8.41665C4.5625 10.3125 6.10417 11.8541 8 11.8541C9.89583 11.8541 11.4375 10.3125 11.4375 8.41665V7.58331C11.4375 7.41248 11.5792 7.27081 11.75 7.27081C11.9208 7.27081 12.0625 7.41248 12.0625 7.58331V8.41665C12.0625 10.6583 10.2417 12.4791 8 12.4791Z"
      fill="white"
    />
    <path
      d="M9.08753 6.42918C9.0542 6.42918 9.0167 6.42501 8.9792 6.41251C8.30836 6.17085 7.57086 6.17085 6.90003 6.41251C6.7417 6.47085 6.56253 6.38751 6.5042 6.22501C6.44586 6.06251 6.5292 5.88335 6.6917 5.82501C7.50003 5.53335 8.38753 5.53335 9.19586 5.82501C9.35836 5.88335 9.4417 6.06251 9.38336 6.22501C9.33753 6.35418 9.2167 6.42918 9.08753 6.42918Z"
      fill="white"
    />
    <path
      d="M8.70822 7.67918C8.68322 7.67918 8.65405 7.67501 8.62489 7.66668C8.17905 7.54585 7.70822 7.54585 7.26239 7.66668C7.09155 7.71251 6.92489 7.61251 6.87905 7.44585C6.83322 7.27918 6.93322 7.10835 7.09989 7.06251C7.65405 6.91251 8.23739 6.91251 8.79155 7.06251C8.95822 7.10835 9.05822 7.27918 9.01239 7.44585C8.97072 7.59168 8.84572 7.67918 8.70822 7.67918Z"
      fill="white"
    />
  </svg>
);
export default MicIcon;