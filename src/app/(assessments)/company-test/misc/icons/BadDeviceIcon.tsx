import * as React from 'react';
import { SVGProps } from 'react';

const BadDeviceIcon = (props: SVGProps<SVGSVGElement>) => (
 <svg
    width={63}
    height={63}
    viewBox="0 0 63 63"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle opacity={0.1} cx={31.5} cy={31.5} r={31.5} fill="white" />
    <path
      d="M31.3463 49.0382C30.5394 49.0382 29.8828 48.3817 29.8828 47.5748C29.8828 46.7678 30.5394 46.1113 31.3463 46.1113C32.1533 46.1113 32.8098 46.7678 32.8098 47.5748C32.8098 48.3817 32.1533 49.0382 31.3463 49.0382ZM31.3463 46.5991C30.8083 46.5991 30.3706 47.0368 30.3706 47.5748C30.3706 48.1128 30.8083 48.5504 31.3463 48.5504C31.8843 48.5504 32.322 48.1128 32.322 47.5748C32.322 47.0368 31.8843 46.5991 31.3463 46.5991Z"
      fill="white"
    />
    <path
      d="M32.5666 15.7032H30.1004C29.9656 15.7032 29.8564 15.594 29.8564 15.4593C29.8564 15.3246 29.9656 15.2154 30.1004 15.2154H32.5666C32.7013 15.2154 32.8105 15.3246 32.8105 15.4593C32.8105 15.594 32.7013 15.7032 32.5666 15.7032Z"
      fill="white"
    />
    <path
      d="M35.283 44.973C35.2205 44.973 35.1582 44.9492 35.1105 44.9016C35.0152 44.8063 35.0152 44.6519 35.1105 44.5567L41.7445 37.9227C41.8398 37.8274 41.9941 37.8274 42.0894 37.9227C42.1847 38.0179 42.1847 38.1724 42.0894 38.2676L35.4554 44.9016C35.4078 44.9492 35.3454 44.973 35.283 44.973Z"
      fill="white"
    />
    <path
      d="M20.7791 24.3698C20.7166 24.3698 20.6542 24.346 20.6066 24.2983C20.5113 24.2031 20.5113 24.0487 20.6066 23.9534L27.2406 17.3195C27.3358 17.2242 27.4903 17.2242 27.5855 17.3195C27.6808 17.4147 27.6808 17.5691 27.5855 17.6643L20.9515 24.2983C20.904 24.346 20.8415 24.3698 20.7791 24.3698Z"
      fill="white"
    />
    <path
      d="M41.9184 17.7358H20.7791C20.6443 17.7358 20.5352 17.6266 20.5352 17.4919C20.5352 17.3572 20.6443 17.248 20.7791 17.248H41.9184C42.0531 17.248 42.1623 17.3572 42.1623 17.4919C42.1623 17.6266 42.0531 17.7358 41.9184 17.7358Z"
      fill="white"
    />
    <path
      d="M41.9184 44.973H20.7791C20.6443 44.973 20.5352 44.8638 20.5352 44.7291C20.5352 44.5944 20.6443 44.4852 20.7791 44.4852H41.9184C42.0531 44.4852 42.1623 44.5944 42.1623 44.7291C42.1623 44.8638 42.0531 44.973 41.9184 44.973Z"
      fill="white"
    />
    <path
      d="M14.8813 18.305C15.218 18.305 15.4911 18.032 15.4911 17.6952C15.4911 17.3584 15.218 17.0854 14.8813 17.0854C14.5445 17.0854 14.2715 17.3584 14.2715 17.6952C14.2715 18.032 14.5445 18.305 14.8813 18.305Z"
      fill="white"
    />
    <path
      d="M17.3217 25.2159C17.6585 25.2159 17.9315 24.9429 17.9315 24.6061C17.9315 24.2693 17.6585 23.9963 17.3217 23.9963C16.9849 23.9963 16.7119 24.2693 16.7119 24.6061C16.7119 24.9429 16.9849 25.2159 17.3217 25.2159Z"
      fill="white"
    />
    <path
      d="M16.9164 46.3552C17.2532 46.3552 17.5262 46.0822 17.5262 45.7454C17.5262 45.4086 17.2532 45.1356 16.9164 45.1356C16.5797 45.1356 16.3066 45.4086 16.3066 45.7454C16.3066 46.0822 16.5797 46.3552 16.9164 46.3552Z"
      fill="white"
    />
    <path
      d="M45.7768 46.3552C46.1136 46.3552 46.3866 46.0822 46.3866 45.7454C46.3866 45.4086 46.1136 45.1356 45.7768 45.1356C45.44 45.1356 45.167 45.4086 45.167 45.7454C45.167 46.0822 45.44 46.3552 45.7768 46.3552Z"
      fill="white"
    />
    <path
      d="M44.559 33.3464C44.8958 33.3464 45.1688 33.0734 45.1688 32.7366C45.1688 32.3999 44.8958 32.1269 44.559 32.1269C44.2222 32.1269 43.9492 32.3999 43.9492 32.7366C43.9492 33.0734 44.2222 33.3464 44.559 33.3464Z"
      fill="white"
    />
    <path
      d="M38.6391 50.6644H24.0583C22.1156 50.6644 20.5352 49.0839 20.5352 47.1412V16.2994C20.5352 14.3567 22.1156 12.7762 24.0583 12.7762H38.6391C40.5818 12.7762 42.1623 14.3567 42.1623 16.2994V17.9088L42.1894 31.3133L42.1623 47.1415C42.1623 49.0839 40.5818 50.6644 38.6391 50.6644ZM24.0583 13.2641C22.3847 13.2641 21.023 14.6258 21.023 16.2994V47.1412C21.023 48.8149 22.3847 50.1765 24.0583 50.1765H38.6391C40.3128 50.1765 41.6744 48.8148 41.6744 47.1411L41.7015 31.3134L41.6744 17.9093V16.2994C41.6744 14.6258 40.3128 13.2641 38.6391 13.2641H24.0583Z"
      fill="white"
    />
    <path
      d="M31.3904 30.2952C30.6071 30.2952 29.9697 29.6579 29.9697 28.8746V24.7564C29.9697 23.9731 30.607 23.3358 31.3904 23.3358C32.1737 23.3358 32.8109 23.9731 32.8109 24.7564V28.8746C32.8109 29.6579 32.1737 30.2952 31.3904 30.2952ZM31.3904 23.8236C30.876 23.8236 30.4576 24.242 30.4576 24.7563V28.8746C30.4576 29.389 30.876 29.8074 31.3904 29.8074C31.9046 29.8074 32.3231 29.389 32.3231 28.8746V24.7564C32.3231 24.242 31.9047 23.8236 31.3904 23.8236Z"
      fill="white"
    />
    <path
      d="M31.3904 33.8252C30.6071 33.8252 29.9697 33.188 29.9697 32.4046C29.9697 31.6213 30.607 30.984 31.3904 30.984C32.1737 30.984 32.8109 31.6213 32.8109 32.4046C32.8109 33.188 32.1737 33.8252 31.3904 33.8252ZM31.3904 31.4719C30.876 31.4719 30.4576 31.8903 30.4576 32.4046C30.4576 32.9189 30.876 33.3374 31.3904 33.3374C31.9046 33.3374 32.3231 32.919 32.3231 32.4046C32.3231 31.8903 31.9047 31.4719 31.3904 31.4719Z"
      fill="white"
    />
    <path
      d="M31.6199 36.0514C31.5574 36.0514 31.4951 36.0275 31.4474 35.9799C31.3522 35.8847 31.3522 35.7302 31.4474 35.635L37.2394 29.8431C37.3347 29.7478 37.489 29.7478 37.5843 29.8431C37.6795 29.9383 37.6795 30.0928 37.5843 30.188L31.7923 35.9799C31.7447 36.0275 31.6823 36.0514 31.6199 36.0514Z"
      fill="white"
    />
    <path
      d="M34.948 36.0515C34.8856 36.0515 34.8232 36.0277 34.7756 35.98C34.6803 35.8848 34.6803 35.7303 34.7756 35.6351L38.4575 31.9532C38.5528 31.8579 38.7072 31.8579 38.8024 31.9532C38.8977 32.0484 38.8977 32.2028 38.8024 32.298L35.1205 35.98C35.0728 36.0277 35.0104 36.0515 34.948 36.0515Z"
      fill="white"
    />
    <path
      d="M27.273 34.4034H24.9197C24.785 34.4034 24.6758 34.2942 24.6758 34.1594C24.6758 34.0247 24.785 33.9155 24.9197 33.9155H27.273C27.4077 33.9155 27.5169 34.0247 27.5169 34.1594C27.5169 34.2942 27.4077 34.4034 27.273 34.4034Z"
      fill="white"
    />
    <path
      d="M38.4683 36.0514H24.3095C23.7445 36.0514 23.2385 35.7593 22.956 35.27C22.6736 34.7807 22.6735 34.1964 22.956 33.707L30.0354 21.4452C30.3179 20.9558 30.8239 20.6637 31.389 20.6637C31.954 20.6637 32.4599 20.9558 32.7424 21.4452L39.8218 33.707C40.1043 34.1964 40.1043 34.7806 39.8218 35.27C39.5392 35.7593 39.0333 36.0514 38.4683 36.0514ZM31.3889 21.1515C31.0002 21.1515 30.6521 21.3525 30.4578 21.6891L23.3784 33.951C23.1841 34.2876 23.1841 34.6895 23.3784 35.0261C23.5727 35.3627 23.9208 35.5636 24.3094 35.5636H38.4683C38.8569 35.5636 39.2051 35.3626 39.3994 35.0261C39.5937 34.6895 39.5937 34.2876 39.3994 33.951L32.32 21.6891C32.1256 21.3524 31.7775 21.1515 31.3889 21.1515Z"
      fill="white"
    />
    <path
      d="M46.9975 17.8985C47.3343 17.8985 47.6073 17.6255 47.6073 17.2887C47.6073 16.9519 47.3343 16.6789 46.9975 16.6789C46.6607 16.6789 46.3877 16.9519 46.3877 17.2887C46.3877 17.6255 46.6607 17.8985 46.9975 17.8985Z"
      fill="white"
    />
    <path
      d="M15.2901 42.534C14.1468 42.534 13.2168 41.604 13.2168 40.4607C13.2168 39.3175 14.1468 38.3875 15.2901 38.3875C16.4333 38.3875 17.3633 39.3175 17.3633 40.4607C17.3633 41.604 16.4333 42.534 15.2901 42.534ZM15.2901 38.8753C14.4159 38.8753 13.7046 39.5865 13.7046 40.4607C13.7046 41.3349 14.4159 42.0462 15.2901 42.0462C16.1643 42.0462 16.8755 41.3349 16.8755 40.4607C16.8755 39.5865 16.1643 38.8753 15.2901 38.8753Z"
      fill="white"
    />
    <path
      d="M46.7947 29.5251C45.0911 29.5251 43.7051 28.1391 43.7051 26.4355C43.7051 24.7319 45.0911 23.3459 46.7947 23.3459C48.4983 23.3459 49.8843 24.7319 49.8843 26.4355C49.8843 28.1391 48.4983 29.5251 46.7947 29.5251ZM46.7947 23.8337C45.36 23.8337 44.1929 25.0008 44.1929 26.4355C44.1929 27.8701 45.36 29.0372 46.7947 29.0372C48.2293 29.0372 49.3964 27.8701 49.3964 26.4355C49.3964 25.0008 48.2293 23.8337 46.7947 23.8337Z"
      fill="white"
    />
    <path
      d="M26.0639 41.7208H20.7791C20.6443 41.7208 20.5352 41.6116 20.5352 41.4769C20.5352 41.3421 20.6443 41.2329 20.7791 41.2329H26.0639C26.1986 41.2329 26.3078 41.3421 26.3078 41.4769C26.3078 41.6116 26.1986 41.7208 26.0639 41.7208Z"
      fill="white"
    />
    <path
      d="M24.0313 43.3469H20.7791C20.6443 43.3469 20.5352 43.2377 20.5352 43.103C20.5352 42.9683 20.6443 42.8591 20.7791 42.8591H24.0313C24.166 42.8591 24.2752 42.9683 24.2752 43.103C24.2752 43.2377 24.166 43.3469 24.0313 43.3469Z"
      fill="white"
    />
  </svg>
);
export default BadDeviceIcon;
