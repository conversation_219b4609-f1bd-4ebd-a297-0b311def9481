import * as React from 'react';
import { SVGProps } from 'react';

const TickIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={10}
    height={10}
    viewBox="0 0 10 10"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M4.99992 9.16665C7.29159 9.16665 9.16659 7.29165 9.16659 4.99998C9.16659 2.70831 7.29159 0.833313 4.99992 0.833313C2.70825 0.833313 0.833252 2.70831 0.833252 4.99998C0.833252 7.29165 2.70825 9.16665 4.99992 9.16665Z"
      stroke="white"
      strokeWidth={0.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M3.22925 5.00003L4.40841 6.1792L6.77091 3.82086"
      stroke="white"
      strokeWidth={0.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default TickIcon;
