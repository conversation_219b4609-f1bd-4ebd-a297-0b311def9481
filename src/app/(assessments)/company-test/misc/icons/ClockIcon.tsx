import * as React from 'react';
import { SVGProps } from 'react';

const ClockIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx={12} cy={12} r={12} fill="#E6E0FF" />
    <path
      d="M12 9.33334V12.6667"
      stroke="#755AE2"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.0001 18.6667C8.78008 18.6667 6.16675 16.0533 6.16675 12.8333C6.16675 9.61333 8.78008 7 12.0001 7C15.2201 7 17.8334 9.61333 17.8334 12.8333"
      stroke="#755AE2"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 5.33334H14"
      stroke="#755AE2"
      strokeWidth={1.5}
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.9333 16.3333V15.56C13.9333 14.6067 14.6133 14.2133 15.44 14.6933L16.1067 15.08L16.7733 15.4667C17.6 15.9467 17.6 16.7267 16.7733 17.2067L16.1067 17.5933L15.44 17.98C14.6133 18.46 13.9333 18.0667 13.9333 17.1133V16.3333Z"
      stroke="#755AE2"
      strokeWidth={1.5}
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default ClockIcon;
