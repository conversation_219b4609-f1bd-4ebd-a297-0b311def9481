import * as React from 'react';
import { SVGProps } from 'react';

const GetLinkedLogoDark = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={63}
    height={62}
    viewBox="0 0 63 62"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <rect width={63} height={62} rx={12} fill="#3C1356" />
    <path
      d="M32 31.5C32 37.299 27.299 42 21.5 42C15.701 42 11 37.299 11 31.5C11 25.701 15.701 21 21.5 21C27.299 21 32 25.701 32 31.5Z"
      stroke="white"
      strokeWidth={6}
    />
    <path
      d="M54.687 31.5C54.687 38.6797 48.8667 44.5 41.687 44.5C34.5073 44.5 28.687 38.6797 28.687 31.5C28.687 24.3203 34.5073 18.5 41.687 18.5C48.8667 18.5 54.687 24.3203 54.687 31.5Z"
      stroke="white"
    />
    <path
      d="M32.9862 31.6261C32.9862 33.481 32.8004 35.3946 32.4506 36.83C32.2747 37.5517 32.0649 38.1201 31.8368 38.4962C31.5975 38.891 31.4199 38.9486 31.3413 38.9486C31.2361 38.9486 31.0101 38.8593 30.6966 38.4509C30.4007 38.0656 30.1025 37.4909 29.8365 36.7744C29.306 35.345 28.9395 33.4483 28.9395 31.6261C28.9395 29.7986 29.2763 27.899 29.7766 26.469C30.0276 25.7518 30.3109 25.1783 30.5953 24.7945C30.8963 24.3882 31.114 24.3037 31.2152 24.3037C31.2986 24.3037 31.484 24.3672 31.7355 24.7643C31.9746 25.1419 32.199 25.7113 32.39 26.432C32.7699 27.8659 32.9862 29.7768 32.9862 31.6261Z"
      fill="white"
      stroke="white"
    />
  </svg>
);
export default GetLinkedLogoDark;
