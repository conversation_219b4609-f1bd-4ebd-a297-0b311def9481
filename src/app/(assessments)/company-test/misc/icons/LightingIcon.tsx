import * as React from 'react';
import { SVGProps } from 'react';


const LightingIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={35}
    height={35}
    viewBox="0 0 35 35"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx={17.5} cy={17.5} r={17.5} fill="transparent" />
    <path
      d="M18 13.9175L17.1975 15.3125C17.0175 15.62 17.1675 15.875 17.52 15.875H18.4725C18.8325 15.875 18.975 16.13 18.795 16.4375L18 17.8325"
      stroke="#E6E0FF"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.225 21.53V20.66C13.5 19.6175 12.0825 17.585 12.0825 15.425C12.0825 11.7125 15.495 8.80251 19.35 9.64251C21.045 10.0175 22.53 11.1425 23.3025 12.695C24.87 15.845 23.22 19.19 20.7975 20.6525V21.5225C20.7975 21.74 20.88 22.2425 20.0775 22.2425H15.945C15.12 22.25 15.225 21.9275 15.225 21.53Z"
      stroke="#E6E0FF"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.375 24.5C17.0925 24.0125 18.9075 24.0125 20.625 24.5"
      stroke="#E6E0FF"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default LightingIcon;