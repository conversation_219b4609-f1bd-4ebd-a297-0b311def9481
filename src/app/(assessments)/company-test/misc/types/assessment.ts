export type AcceptAssessmentInviteAPIResponse = {
    invite_id: string;
    candidate_name: string;
    candidate_email: string;
    auth_method: any[];
    token: string;
    result_id: string;
    assessment: TAssessmentInformation;
}

export type TAssessmentInformation = {
    id: string;
    sections: AssessmentSection[];
    interview_questions: any[];
    name: string;
    description: string;
    type: string;
    intro_video_url: string | null;
    role_level: string;
    time: string;
    commencement_settings: boolean;
    deadline: string;
    start_time: string;
    time_limit: number;
    reminder_time: number;
    total_questions: number;
    no_candidates: number;
    paused: any[];
    invigilate_assessment: boolean;
    is_stop_screen_sharing: boolean;
    full_screen_tolerance_level: number;
    window_change_tolerance_level: number;
    is_shuffle: boolean;
    is_shuffle_sections: boolean;
    is_webcam_snapshot: boolean;
    is_restrict_copying: boolean;
    is_restrict_tab_change: boolean;
    is_track_paste: boolean;
    is_custom: boolean;
    last_page_url: string;
    reminder_sent: boolean;
    created_at: string;
    updated_at: string;
    role: Role;
    creator: AssessmentCreator;
    assessment_team_members: any[];
}

type AssessmentSection = {
    id: string;
    section_name: string;
    no_multiple_choice: number;
    no_essay: number;
    no_coding: number;
    no_fill_in_the_blanks: number;
    no_multiple_response: number;
    no_true_false: number;
    total_points: number;
    order: number;
    test: any[];
    question_pack: any[];
    question_set: Question[];
}

type Question = {
    id: string;
    type: 'multiple_choice' | 'multiple_response';
    question: string;
    answer_options: string[];
    experience_level: string;
    category: string | null;
    points: number;
    label: string | null;
    tags: string | null;
    is_custom: boolean;
    time: number;
    created_at: string;
    updated_at: string;
    instructions: string | null;
    images: string | null;
    has_image: boolean;
    role: string | null;
    test: string;
    section: string;
    creator: string | null;
}

type Role = {
    id: number;
    name: string;
    use_ai: boolean;
}

type AssessmentCreator = {
    id: number;
    company_email: string;
    verified: boolean;
    type_of_recruiter: string;
    profile_picture: string | null;
    role: string | null;
    created_at: string;
    updated_at: string;
    user: string;
    company: number;
}