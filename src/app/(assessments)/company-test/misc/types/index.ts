import * as cocoSsd from '@tensorflow-models/coco-ssd';
import * as faceDetection from '@tensorflow-models/face-detection';
import {
  FaceDetection,
  FaceLandmarks68,
  WithFaceDescriptor,
  WithFaceLandmarks,
} from '@vladmandic/face-api';
import { string } from 'zod';

export * from './assessment';
export type Flag =
  | 'MultiFace'
  | 'NoFace'
  | 'illegalObject'
  | 'paste'
  | 'lookingAway'
  | 'faceMismatch';

export enum Flags {
  MultiFace = 'MultiFace',
  NoFace = 'NoFace',
  illegalObject = 'illegalObject',
  paste = 'paste',
  faceMismatch = 'faceMismatch',
  lookingAway = 'lookingAway',
}

export type FaceAPIType = typeof import('@vladmandic/face-api');
export type FaceData = WithFaceDescriptor<
  WithFaceLandmarks<{ detection: FaceDetection }, FaceLandmarks68>
>;

export interface LocalReportLog {
  flag: Flags;
  date: String;
}

export type DisplaySurface =
  | 'window'
  | 'monitor'
  | 'application'
  | 'unknown'
  | 'unsupported';

export type InitializedModels = {
  objectModel: cocoSsd.ObjectDetection;
  faceModel: faceDetection.FaceDetector;
};
