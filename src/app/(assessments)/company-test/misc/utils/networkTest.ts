/**
 * Represents the result of a network speed test
 */
interface SpeedTestResult {
  value: number | null;
  unit: string;
  error: string | null;
}

/**
 * Test size options for network tests
 */
type TestSize = 'small' | 'medium' | 'large';

/**
 * Complete network test results
 */
export interface NetworkTestResults {
  ping: SpeedTestResult;
  download: SpeedTestResult;
  upload: SpeedTestResult;
}
 
function getTestSizeInBytes(size: TestSize, forUpload: boolean = false): number {
  const sizes: Record<TestSize, { download: number, upload: number }> = {
    small: {
      download: 100 * 1024, // 100KB
      upload: 50 * 1024     // 50KB (smaller due to upload limitations)
    },
    medium: {
      download: 1 * 1024 * 1024, // 1MB
      upload: 500 * 1024         // 500KB
    },
    large: {
      download: 5 * 1024 * 1024, // 5MB
      upload: 2 * 1024 * 1024    // 2MB
    }
  };
  
  return forUpload ? sizes[size].upload : sizes[size].download;
}

 
function formatSpeed(bitsPerSecond: number): { value: number, unit: string } {
  if (bitsPerSecond < 1000 * 1000) {
    // Less than 1 Mbps, return in Kbps
    return {
      value: parseFloat((bitsPerSecond / 1000).toFixed(2)),
      unit: 'Kbps'
    };
  } else {
    // Return in Mbps
    return {
      value: parseFloat((bitsPerSecond / (1000 * 1000)).toFixed(2)),
      unit: 'Mbps'
    };
  }
}
 
export async function measurePing(): Promise<SpeedTestResult> {
  try {
    const url = 'https://speed.cloudflare.com/__ping';
    
    const startTime = new Date().getTime();
    
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Network response was not ok: ${response.status}`);
    }
    
    const endTime = new Date().getTime();
    const pingTime = endTime - startTime;
    
    return {
      value: pingTime,
      unit: 'ms',
      error: null
    };
  } catch (err) {
    console.error('Ping test failed:', err);
    return {
      value: null,
      unit: 'ms',
      error: `Ping test failed: ${err instanceof Error ? err.message : String(err)}`
    };
  }
}
 
export async function measureDownloadSpeed(size: TestSize = 'medium'): Promise<SpeedTestResult> {
  try {
    const fileSize = getTestSizeInBytes(size, false);
    
    // Create a unique URL to prevent caching
    const url = `https://speed.cloudflare.com/__down?bytes=${fileSize}&cachebust=${new Date().getTime()}`;
    
    const startTime = new Date().getTime();
    
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Network response was not ok: ${response.status}`);
    }
    
    const data = await response.arrayBuffer();
    const endTime = new Date().getTime();
    const duration = (endTime - startTime) / 1000; // in seconds
    
    // Calculate speed in bits per second
    const bitsLoaded = fileSize * 8;
    const bitsPerSecond = bitsLoaded / duration;
    
    // Format the speed
    const { value, unit } = formatSpeed(bitsPerSecond);
    
    return {
      value,
      unit,
      error: null
    };
  } catch (err) {
    console.error('Download test failed:', err);
    return {
      value: null,
      unit: 'Mbps',
      error: `Download test failed: ${err instanceof Error ? err.message : String(err)}`
    };
  }
}

 
export async function measureUploadSpeed(size: TestSize = 'small'): Promise<SpeedTestResult> {
  try {
    const fileSize = getTestSizeInBytes(size, true);
    
    // Generate random data to upload
    const data = new ArrayBuffer(fileSize);
    const blob = new Blob([data]);
    
    const startTime = new Date().getTime();
    
    // Using Cloudflare's speed test endpoint for upload test
    const response = await fetch('https://speed.cloudflare.com/__up', {
      method: 'POST',
      body: blob
    });
    
    if (!response.ok) {
      throw new Error(`Network response was not ok: ${response.status}`);
    }
    
    const endTime = new Date().getTime();
    const duration = (endTime - startTime) / 1000; // in seconds
    
    // Calculate speed in bits per second
    const bitsLoaded = fileSize * 8;
    const bitsPerSecond = bitsLoaded / duration;
    
    // Format the speed
    const { value, unit } = formatSpeed(bitsPerSecond);
    
    return {
      value,
      unit,
      error: null
    };
  } catch (err) {
    console.error('Upload test failed:', err);
    return {
      value: null,
      unit: 'Mbps',
      error: `Upload test failed: ${err instanceof Error ? err.message : String(err)}`
    };
  }
}

 
export async function runNetworkTests(
  downloadSize: TestSize = 'medium',
  uploadSize: TestSize = 'small'
): Promise<NetworkTestResults> {
  const pingResult = await measurePing();
  const downloadResult = await measureDownloadSpeed(downloadSize);
  const uploadResult = await measureUploadSpeed(uploadSize);
  
  return {
    ping: pingResult,
    download: downloadResult,
    upload: uploadResult
  };
}
 
function formatSpeedResult(result: SpeedTestResult): string {
  if (result.error) {
    return `Error: ${result.error}`;
  }
  return `${result.value} ${result.unit}`;
}
 