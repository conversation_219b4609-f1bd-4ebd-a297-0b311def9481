import shallow from 'zustand/shallow';
import { UseGetAssessmentSections } from '../api';
import {
  useAllAssessmentSections,
  useAssessmentDetails,
  useAssessmentSectionsStore,
  UseCandidateStatusStore,
  useProctoringReport,
} from '../store';
import { Section } from '../store/assessmentStore';
import { DisplaySurface } from '../types';

export const PrepTest = (
  assessmentSections: Section[],
  isAssessmentShuffleQuestions: boolean
): Section[] => {
  let sections = assessmentSections;
  if (isAssessmentShuffleQuestions) {
    sections = sections.map((section, index) => {
      const shuffledQuestions = section.question_set.sort(
        () => Math.random() - 0.5
      );

      return {
        ...section,
        question_set: shuffledQuestions,
      };
    });
  }
  return sections;
};

export const detectDisplaySurface = (stream: MediaStream): DisplaySurface => {
  // Check if running in Firefox
  const isFirefox = navigator.userAgent.toLowerCase().includes('firefox');

  // Check if running in Chrome/Chromium-based browsers
  const isChrome = navigator.userAgent.toLowerCase().includes('chrome');

  const videoTrack = stream.getVideoTracks()[0];

  if (!videoTrack) {
    console.log('No video track available');
    return 'unknown';
  }

  if (isFirefox) {
    // Firefox detection via track label
    const label = videoTrack.label.toLowerCase();

    if (label.includes('window')) return 'window';
    if (label.includes('screen')) return 'monitor';
    if (label.includes('monitor')) return 'monitor';
    if (label.includes('application')) return 'application';

    return 'unknown';
  }

  // if (isChrome) {
  // Chrome uses displaySurface property
  const settings = videoTrack.getSettings();
  if (settings.displaySurface) return settings.displaySurface as DisplaySurface;

  return 'unknown';
  // }
};
// Helper function to get the appropriate suffix
export function getSuffix(num: number): string {
  const lastDigit = num % 10;
  const lastTwoDigits = num % 100;

  if (lastTwoDigits >= 11 && lastTwoDigits <= 13) {
    return 'th';
  }

  switch (lastDigit) {
    case 1:
      return 'st';
    case 2:
      return 'nd';
    case 3:
      return 'rd';
    default:
      return 'th';
  }
}
