interface CleanUrl {
  protocol: string;
  baseUrl: string;
}

const cleanBaseURL = (): CleanUrl => {
  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
  if (!backendUrl) {
    console.error('Backend URL not configured');
    return { protocol: '', baseUrl: '' };
  }

  const protocol = backendUrl.startsWith('https://') ? 'wss://' : 'ws://';
  const baseUrl = backendUrl.replace(/^(https?:\/\/)/, '');

  return {
    protocol,
    baseUrl,
  };
};

export const getWebSocketUrl = (
  assessmentId: string,
  proctoringReportId: string
): string => {
  const { protocol, baseUrl } = cleanBaseURL();
  return `${protocol}${baseUrl}/ws/proctoring/${assessmentId}/${proctoringReportId}/`;
};

export const getProgressWebSocketUrl = (
  assessmentId: string,
  candidateEmail: string
): string => {
  const { protocol, baseUrl } = cleanBaseURL();
  return `${protocol}${baseUrl}/ws/progress/${assessmentId}/${candidateEmail}/`;
};



export enum SocketProgressEvent {
  ACCEPT_INVITE = 'accept_invite',
  LOGIN_VERIFIED = 'login_verified',
  INSTRUCTIONS_READ = 'instructions_read',
  SYSTEM_CHECK_PASSED = 'system_check_passed',
  ID_VERIFIED = 'id_verified',
  ASSESSMENT_STARTED = 'assessment_started',
  ASSESSMENT_SUBMITTED = 'assessment_submitted',
}
