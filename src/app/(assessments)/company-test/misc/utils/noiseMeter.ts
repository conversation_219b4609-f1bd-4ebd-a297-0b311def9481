export interface NoiseReading {
  rms: number;
  peak: number;
  db: number;
  dataArray: number[];
  description: string;
  rawMax: number;
  rawMin: number;
}

export class NoiseMeter {
  private audioContext: AudioContext | null;
  private microphone: MediaStreamAudioSourceNode | null;
  private analyzer: AnalyserNode | null;
  private isInitialized: boolean;

  constructor() {
    this.audioContext = null;
    this.microphone = null;
    this.analyzer = null;
    this.isInitialized = false;
  }

  async initialize(): Promise<boolean> {
    try {
      // Ensure audio context is supported
      if (!window.AudioContext && !(window as any).webkitAudioContext) {
        console.error('Web Audio API is not supported in this browser');
        return false;
      }

      // Create audio context
      this.audioContext = new (window.AudioContext ||
        (window as any).webkitAudioContext)();

      // Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: false,
          autoGainControl: false,
          noiseSuppression: false,
        },
      });

      if (!this.audioContext) {
        throw new Error('AudioContext not initialized');
      }

      // Create analyzer node
      this.analyzer = this.audioContext.createAnalyser();
      // Increase FFT size for more detailed analysis
      this.analyzer.fftSize = 4096; // More data points

      // Adjust analyzer settings
      this.analyzer.minDecibels = -90;
      this.analyzer.maxDecibels = -10;
      this.analyzer.smoothingTimeConstant = 0.2;

      // Connect microphone to analyzer
      this.microphone = this.audioContext.createMediaStreamSource(stream);
      this.microphone.connect(this.analyzer);

      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('Detailed initialization error:', error);
      return false;
    }
  }

  measure(): NoiseReading {
    if (!this.isInitialized || !this.analyzer) {
      throw new Error('NoiseMeter not initialized. Call initialize() first.');
    }

    // Create data array for analyzer (use larger buffer)
    const dataArray = new Uint8Array(this.analyzer.frequencyBinCount);
    this.analyzer.getByteTimeDomainData(dataArray);

    // More sophisticated noise calculation
    let sum = 0;
    let sumSquared = 0;
    let rawMax = -Infinity;
    let rawMin = Infinity;

    for (let i = 0; i < dataArray.length; i++) {
      // Raw value processing
      const value = dataArray[i];
      rawMax = Math.max(rawMax, value);
      rawMin = Math.min(rawMin, value);

      // Convert to float between -1 and 1
      const float = (value - 128) / 128;

      // Calculate sum and sum of squares
      sum += Math.abs(float);
      sumSquared += float * float;
    }

    // Different RMS calculations
    const rms = Math.sqrt(sumSquared / dataArray.length);
    const avgMagnitude = sum / dataArray.length;

    // Peak calculation
    const peak = (rawMax - rawMin) / 255;

    // More nuanced decibel calculation
    const db =
      peak > 0
        ? 20 * Math.log10(peak) // Peak-based calculation
        : -Infinity;

    return {
      rms,
      peak,
      db: isFinite(db) ? db : -Infinity,
      dataArray: Array.from(dataArray),
      rawMax,
      rawMin,
      description: this.interpretNoiseLevel(db),
    };
  }

  private interpretNoiseLevel(db: number): string {
    if (db <= -60) return 'Extremely quiet';
    if (db <= -50) return 'Very quiet';
    if (db <= -40) return 'Quiet';
    if (db <= -30) return 'Moderate noise';
    if (db <= -20) return 'Loud';
    if (db <= -10) return 'Very loud';
    return 'Extremely loud';
  }

  stop(): void {
    if (this.microphone) {
      const tracks = this.microphone.mediaStream.getTracks();
      tracks.forEach(track => track.stop());
    }

    if (this.audioContext) {
      this.audioContext.close();
    }

    this.microphone = null;
    this.analyzer = null;
    this.isInitialized = false;
  }
}

// Start monitoring when script loads
// startNoiseMonitoring();
