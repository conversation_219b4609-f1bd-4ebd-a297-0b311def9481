import { useMutation } from '@tanstack/react-query';
import axios from 'axios';

export const startAssessment = async ({
  email,
  invite_id,
  assessment_id,
  result_id,
  proctoring_id,
}: {
  email: string;
  invite_id: string;
  assessment_id: string;
  result_id: string;
  proctoring_id: string;
}) => {
  const response = await axios.post(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/assessments/mark-invite/${invite_id}/accept/`,
    {
      candidate_email: email,
      assessment_id: assessment_id,
      result_id,
      proctoring_id,
    }
  );
  return response.data;
};

export const useStartAssessment = () => {
  return useMutation({
    mutationFn: startAssessment,
    mutationKey: ['startAssessment'],
  });
};
