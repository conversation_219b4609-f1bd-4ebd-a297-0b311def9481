import { useMutation } from '@tanstack/react-query';
import { AxiosWithNoAuth } from '@/lib/api/axios';

export interface CandidateAnswerProps {
  candidate: string;
  invite_id: string;
  section_id: string;
  question: string;
  answer: string | string[];
}
export const sendCandidateAnswer = async (
  data: CandidateAnswerProps | CandidateAnswerProps[]
) => {
  const response = await AxiosWithNoAuth.post(
    `/assessments/candidate-answers/`,
    data
  );
  // const response = await AxiosWithNoAuth.post(`/assessments/v2/candidate-answers/`, data);
  return response.data;
};

export const useSendCandidateAnswers = () => {
  return useMutation({
    mutationFn: sendCandidateAnswer,
    mutationKey: ['sendCandidateAnswer'],
  });
};
