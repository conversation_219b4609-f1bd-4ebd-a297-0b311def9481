export const MINIMUM_BRIGHTNESS_LEVEL = 70;
export const checkBrightness = (
  canvas_ref: HTMLCanvasElement,
  imageSrc: CanvasImageSource
): number => {
  if (!canvas_ref || !imageSrc) return 0;
  const ctx = canvas_ref.getContext('2d');
  if (!ctx) return 0;
  ctx.drawImage(imageSrc, 0, 0, canvas_ref.width, canvas_ref.height);
  const imageData = ctx.getImageData(0, 0, canvas_ref.width, canvas_ref.height);
  const data = imageData.data;
  let r, g, b, avg;
  let colorSum = 0;
  for (let x = 0, len = data.length; x < len; x += 4) {
    r = data[x];
    g = data[x + 1];
    b = data[x + 2];

    avg = Math.floor((r + g + b) / 3);
    colorSum += avg;
  }

  return Math.floor(colorSum / (canvas_ref.width * canvas_ref.height));
};
