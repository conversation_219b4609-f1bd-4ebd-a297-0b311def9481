import { useMutation } from '@tanstack/react-query';
import axios from 'axios';


export const submitAssessment = async ({
  candidate_email,
  invite_id,
  assessment_id,
  result_id,
  candidate_name,
}: {
  candidate_email: string;
  invite_id: string;
  assessment_id: string;
  result_id: string;
    candidate_name: string;
  
}): Promise<any> => {
  const response = await axios.post(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/assessments/submit/`,
    {
      candidate_email,
      invite_id,
      assessment_id,
      result_id,
      candidate_name,
    }
  );
  return response;
};

export const useSubmitAssessment = () => {
  return useMutation({
    mutationFn: submitAssessment,
  });
};
