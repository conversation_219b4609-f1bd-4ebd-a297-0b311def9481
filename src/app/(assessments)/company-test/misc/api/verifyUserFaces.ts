import * as faceAPI from 'face-api.js';

export const loadFaceModels = async () => {
  console.log('FACE API MODEL STARTED LOADING');

  try {
    await faceAPI.loadFaceDetectionModel('/faceapi-models');
    await faceAPI.loadFaceRecognitionModel('/faceapi-models');
    await faceAPI.loadFaceLandmarkModel('/faceapi-models');
    await faceAPI.loadSsdMobilenetv1Model('/faceapi-models');
    console.log('FACE API MODEL LOADED');

    return true;
  } catch (e) {
    console.error(e, 'Face api load err');
  }
  return false;
};

export const validateFaceMatch = async (
  referenceImage: string,
  compImage: string
) => {
  let imageFile = await faceAPI.fetchImage(referenceImage);
  // console.log(imageFile);
  const referenceDescriptor = await faceAPI
    .detectAllFaces(imageFile)
    .withFaceLandmarks()
    .withFaceDescriptors();
  console.log(referenceDescriptor, 'REFERENCE DESCRIPTOR');
  if (referenceDescriptor) {
    const faceMatcher = new faceAPI.FaceMatcher(referenceDescriptor, 0.5);

    const comparismDescriptor = await faceAPI
      .detectAllFaces(compImage)
      .withFaceLandmarks()
      .withFaceDescriptors();
    console.log(comparismDescriptor);
    if (comparismDescriptor.length === 1) {
      let results = comparismDescriptor.map(cd =>
        faceMatcher.findBestMatch(cd.descriptor)
      );
      return results;
    } else {
      console.log('Cannot find another face in comparism Image');
      //  setFaceMismatch(true);
    }
  }
};
