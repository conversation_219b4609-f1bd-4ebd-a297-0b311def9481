import { useMutation } from '@tanstack/react-query';
import axios from 'axios';

export const resumeAssessment = async (data: {
  invite_id: string;
  assessment_id: string;
  email: string;
  proctoring_id: string;
}) => {
  const response = await axios.post(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/assessments/resume-assessment/`,
    data
  );
  return response.data;
};

export const useResumeAssessments = () => {
  return useMutation({
    mutationFn: resumeAssessment,
    mutationKey: ['resumeAssessment'],
  });
};
