import { useMutation, useQuery } from '@tanstack/react-query';
import axios from 'axios';

export const fetchLocation = async ({
  long,
  lat,
  key,
}: {
  long: number;
  lat: number;
  key: string;
}): Promise<any> => {
  const response = await axios.get(
    `https://api.opencagedata.com/geocode/v1/json?q=${lat}+${long}&key=${key}`
  );
  return response.data;
};

export const useGetGeoLocation = () => {
  return useMutation({
    mutationFn: fetchLocation,
    mutationKey: ['fetch'],
  });
};
