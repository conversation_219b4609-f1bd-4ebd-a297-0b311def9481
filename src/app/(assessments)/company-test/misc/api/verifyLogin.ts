import { useMutation } from '@tanstack/react-query';
import axios from 'axios';


export const getVerifyCandidateCustom = async ({
  invite_id,
  method1,
  method2,
  is_custom,
  candidate_email,
  assessment_id,
  candidate_name,
  result_id,
  candidate_phone_number
}: {
  invite_id: string;
  method1: string;
  method2: string;
  is_custom: boolean;
  candidate_email: string;
  assessment_id: string;
  candidate_name: string;
  result_id: string;
  candidate_phone_number: string;
}): Promise<any> => {
  const response = await axios.post(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/assessments/verify-candidate/`,
    {
      invite_id,
      method1,
      method2,
      is_custom,
      candidate_email,
      assessment_id,
      candidate_name,
      result_id,
      candidate_phone_number
    }
  );
  return response;
};

export const useGetVerifyCandidateCustom = () => {
  return useMutation({
    mutationFn: getVerifyCandidateCustom,
  });
};
export const getVerifyCandidateDefault = async ({
  assessment_id,
  email,
  token,
  invite_id,
  candidate_name,
  result_id,
  candidate_phone_number
}: {
  assessment_id: string;
  email: string;
  token: string;
  invite_id: string;
  candidate_name: string;
    result_id: string;
  candidate_phone_number: string;
}): Promise<any> => {
  const response = await axios.post(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/assessments/verify-candidate/`,
    {
      assessment_id,
      email,
      token,
      invite_id,
      candidate_name,
      candidate_phone_number,
      result_id,
      
    }
  );
  return response;
};

export const useGetVerifyCandidateDefault = () => {
  return useMutation({
    mutationFn: getVerifyCandidateDefault,
  });
};
