import { useMutation } from '@tanstack/react-query';
import axios from 'axios';
import { assessment } from '../../../../misc/mock/fake_assessments_library';
import { useProctoringReport } from '../store/proctoringReportStore';

export type TSendProctorReportData = {
  candidate_email: string;
  assessment: string;
  device_used: string;
  webcam_enabled: boolean;
  location: string;
  mouse_out_of_window: boolean;
  tab_switched: boolean;
  id: string;
  // full_screen_tolerance_used: number;
  // window_change_tolerance_used: number;
  // was_forced_submission_full_screen: boolean;
  // was_forced_submission_window_change: boolean;
  // was_forced_submission_bulk_proctor: boolean;

  was_forced_submission: boolean;

  // bulk_proctor_used: number;
  // bulk_proctor_option: {
  //   key: string;
  //   value: number;
  // }[];
};
export const sendProctoringReport = async (data: TSendProctorReportData) => {
  let payload = { ...data };
  // console.log(payload, 'DATA FOR PROCTORING SENDING');

  const response = await axios.patch(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/assessments/proctoring-reports/${payload.id}/`,
    payload
  );
  return response.data;
};

export const uploadProfileImages = async (data: {
  candidate_email: string;
  assessment: string;
  photo_id: string;
  profile_photo: string;
  is_upload: boolean;
  is_identity_verification?: boolean;
}) => {
  const response = await axios.post(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/assessments/v2/proctoring-reports/`,
    data
  );
  return response.data;
};

export const useSendProctoringReports = () => {
  return useMutation({
    mutationFn: sendProctoringReport,
    mutationKey: ['sendProctoringReport'],
  });
};

export const useUploadProfileImages = () => {
  return useMutation({
    mutationFn: uploadProfileImages,
    mutationKey: ['uploadProfileImages'],
  });
};

export const checkProctorStatus = async (id: string) => {
  const response = await axios.get(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/assessments/proctoring-status/${id}/`
  );
  return response.data;
};
export const useCheckProcotorStatus = () => {
  return useMutation({
    mutationFn: checkProctorStatus,
    mutationKey: ['checkProctorStatus'],
  });
};

export const verifyWithNIN = async (data: {
  candidate_email: string;
  assessment: string;
  profile_photo: string;
  nin: string;
}) => {
  const response = await axios.post(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/assessments/nin-verification/`,
    data
  );
  return response.data;
};
export const useVerifyWithNIN = () => {
  return useMutation({
    mutationFn: verifyWithNIN,
    mutationKey: ['verifyWithNIN'],
  });
};

export const verifyInterviewWithNIN = async (data: {
  candidate_email: string;
  interview: string;
  result_id: string;
  profile_photo: string;
  nin: string;
}) => {
  const response = await axios.post(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/assessments/interview-nin-verification/`,
    data
  );
  return response.data;
};
export const useVerifyInterviewWithNIN = () => {
  return useMutation({
    mutationFn: verifyInterviewWithNIN,
    mutationKey: ['verifyInterviewWithNIN'],
  });
};
