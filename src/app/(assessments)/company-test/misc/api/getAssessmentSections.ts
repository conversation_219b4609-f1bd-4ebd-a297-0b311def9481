
import { useQuery } from '@tanstack/react-query';
import { AxiosWithNoAuth } from '@/lib/api/axios';
import { Section } from '../store/assessmentStore';


export const getSections = async (id: string) => {
  const response = await AxiosWithNoAuth.get(`/assessments/load-assessment/${id}`);
  return response.data?.assessment?.sections as Section[];
};

export const UseGetAssessmentSections = (id: string) => {
  return useQuery({
    queryFn: () => getSections(id),
    queryKey: ['get-assessment-sections', id],
    enabled: !!id,
  });
};
