import axios from 'axios';

import { useMutation } from '@tanstack/react-query';

import { AcceptAssessmentInviteAPIResponse } from '../types';
import { AxiosWithNoAuth } from '@/lib/api/axios';


export const acceptInvite = async (data: string): Promise<any> => {
  const response = await AxiosWithNoAuth.post(`/assessments/accept-invite/`,
    {
      invitation_token: data,
    }
  );
  return response.data as AcceptAssessmentInviteAPIResponse;
};

export const useAcceptAssessmentInvite = () => {
  return useMutation({
    mutationFn: acceptInvite,
  });
};
