import { useMutation } from '@tanstack/react-query';
import axios from 'axios';


type Props = {
  candidate_email: string;
  invite: string;
  assessment: string;
  result: string;
  candidate_name: string;
  time_left: number;
  proctoring_report: string;
}

export const pauseAssessment = async (data: Props): Promise<any> => {
  const response = await axios.post(`${process.env.NEXT_PUBLIC_BACKEND_URL}/assessments/assessment-sessions/`, data);
  return response;
};

export const usePauseAssessment = () => {
  return useMutation({
    mutationFn: pauseAssessment,
  });
};