import { InterviewAssessment } from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

// import { data, dataProps } from './assessmentStore';

export interface InterviewDataProps {
  invite_id: string;
  candidate_name: string;
  candidate_email: string;
  auth_method: any[];
  interview: InterviewAssessment;
  started?: boolean;
  result_id: string;
  token: string;
}

export interface setFunctionProps {
  data: InterviewDataProps | null;
  setInterviewDetails: (payload: InterviewDataProps) => void;
}

export const useInterviewDetails = create<setFunctionProps>()(
  persist(
    set => ({
      data: null,
      setInterviewDetails(payload) {
        set(state => ({ data: payload }));
      },
    }),
    {
      name: 'interview-details',
      storage: createJSONStorage(() => sessionStorage),
    }
  )
);
