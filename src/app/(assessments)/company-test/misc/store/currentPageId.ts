import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

interface currentPageIdProps {
  data: number;
  setCurrentPageId: (payload: any) => void;
}
export const useCurrentPageId = create<currentPageIdProps>()(
  persist(
    set => ({
      data: 0,
      setCurrentPageId(payload) {
        set(state => ({ data: payload }));
      },
    }),
    {
      name: 'currentPageId',
      storage: createJSONStorage(() => sessionStorage),
    }
  )
);
