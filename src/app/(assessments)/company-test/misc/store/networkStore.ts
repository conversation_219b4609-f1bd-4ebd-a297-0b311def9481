import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

export interface NetworkStoreProps {
  onlineStatus: number
  setOnlineStatus: (payload: any) => void;
}

export const useNetworkDetails = create<NetworkStoreProps>()(

  persist(
    set => ({
      onlineStatus: 1,

      setOnlineStatus: (payload: number) => {
        set(state => ({ onlineStatus: payload }));
      }
    }),
    {
      name: 'assessment-network-details',
      storage: createJSONStorage(() => sessionStorage),
    }
  )
);
