import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

type TCandidateStatus = {
    isInSession: boolean;
    isReturning: boolean;
    isOnline: boolean;
    timeLeft: number;
    startTime: number;
    totalTime: number;
    currentQuestionId: string;
}

export interface statusStoreProps {
    candidateStatus: TCandidateStatus;
    setCandidateStatus: (payload: Partial<TCandidateStatus>) => void;
    initializeTimer: (totalTime: number) => void;
    updateTimer: () => void;
    clearCandidateStatus: () => void;
}

const defaultCandidateStatus: TCandidateStatus = {
    isInSession: false,
    isReturning: false,
    isOnline: true,
    timeLeft: 0,
    startTime: 0,
    totalTime: 0,
    currentQuestionId: '',
};

export const UseCandidateStatusStore = create<statusStoreProps>()(
    persist(
        (set, get) => ({
            candidateStatus: defaultCandidateStatus,
            setCandidateStatus(payload) {
                set(state => ({
                    candidateStatus: {
                        ...state.candidateStatus,
                        ...payload,
                    },
                }));
            },
            initializeTimer(totalTime: number) {
                const now = Date.now();
                set(state => ({
                    candidateStatus: {
                        ...state.candidateStatus,
                        startTime: now,
                        timeLeft: totalTime,
                        totalTime: totalTime,
                    },
                }));
            },
            updateTimer() {
                const { startTime, totalTime } = get().candidateStatus;
                const now = Date.now();
                const elapsed = now - startTime;
                const newTimeLeft = Math.max(0, totalTime - elapsed);
                set(state => ({
                    candidateStatus: {
                        ...state.candidateStatus,
                        timeLeft: newTimeLeft,
                    },
                }));
            },
            clearCandidateStatus() {
                set({
                    candidateStatus: defaultCandidateStatus,
                });
            },
        }),
        {
            name: 'candidate-assessment-status-details',
            storage: createJSONStorage(() => sessionStorage),
        }
    )
);