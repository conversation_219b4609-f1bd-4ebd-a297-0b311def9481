import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';


export interface answeredQuestionsStoreProps {
  data: string[];
  setAnsweredQuestionsDetails: (payload: any) => void;
  resetPreviousAnswers: () => void;
}

export const useAnsweredQuestionsDetails =
  create<answeredQuestionsStoreProps>()(
    persist(
      set => ({
        data: [],
        setAnsweredQuestionsDetails(payload) {
          set(state => ({ data: payload }));
        },
        resetPreviousAnswers() {
          set(state => ({ data: [] }));
        },
      }),
      {
        name: 'assessment-answered-questionss-details',
        storage: createJSONStorage(() => sessionStorage),
      }
    )
  );