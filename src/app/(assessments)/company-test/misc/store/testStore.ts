import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

interface testStoreProps {
  data: any;
  setTestDetails: (payload: any) => void;
}

export const useTestDetails: any = create<testStoreProps>()(
  persist(
    set => ({
      data: null,
      setTestDetails(payload) {
        set(state => ({ data: payload }));
      },
    }),
    {
      name: 'test-details',
      storage: createJSONStorage(() => sessionStorage),
    }
  )
);
