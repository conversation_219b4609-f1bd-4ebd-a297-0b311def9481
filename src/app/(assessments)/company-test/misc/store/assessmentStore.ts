import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

export interface TAssessmentStore {
  invite_id: string;
  candidate_name: string;
  candidate_email: string;
  auth_method: any[];
  assessment: Assessment;
  started?: boolean;
  result_id: string;
  token: string;
}

export interface Assessment {
  id: string;
  sections: Section[];
  name: string;
  role: { name: string };
  role_level: string;
  time: Date;
  is_published: boolean;
  deadline: Date;
  time_limit: number;
  created_at: Date;
  no_candidates: number;
  updated_at: Date;
  creator: Creator;
  description: string;
  intro_video_url: string;
  is_shuffle?: boolean;
  total_questions?: number;
  window_change_tolerance_level: number;
  full_screen_tolerance_level: number;
  is_restrict_tab_change: boolean;
  is_stop_screen_sharing: boolean;
  allow_strict_id_verification: boolean;
  is_restrict_copying: boolean;
  is_identity_verification: boolean;
  is_track_paste: boolean;
  type: string;
  bulk_tolerance_setup: {
    combined_tolerance: number;
    options: { option: string }[];
  } | null;
}

export interface Creator {
  id: number;
  company_email: string;
  verified: boolean;
  created_at: Date;
  updated_at: Date;
  user: string;
  company: number;
}

export enum ELevel {
  EntryLevel = 'entry_level',
  ExpertLevel = 'expert_level',
}

export interface Section {
  id: string;
  section_name: string;
  no_multiple_choices: number;
  no_essay: number;
  no_coding: number;
  test: Test[];
  question_set: QuestionSet[];
}

export interface QuestionSet {
  id: string;
  type: Type;
  question: string;
  answer_options: string[];
  answer_options_type: string;
  experience_level: string;
  category: string;
  points: number;
  label: string;
  tags: string[];
  is_custom: boolean;
  time: number;
  instructions: string | null;
  images: string[];
  has_image: boolean;
  candidate_answer?: string[];
  role: string;
  created_at: Date;
  updated_at: Date;
  test: string;
  section: string;
  question_creator: null;
}

export enum Category {
  RoleSpecific = 'role_specific',
}

export enum Label {
  Skill = 'skill',
}

export enum Type {
  Coding = 'coding',
  Essay = 'essay',
  MultipleChoice = 'multiple_choice',
  TrueOrFalse = 'true_false',
  MultipleResponse = 'multiple_response',
  FillInTheBlanks = 'fill_in_the_blanks',
}

export interface Test {
  id: string;
  name: string;
  description: string;
  summary: string;
  skills_tested: string;
  experience_level: string;
  role: string;
  tags: any[];
  label: string;
  section: string;
}

export interface setFunctionProps {
  assessmentDetails: TAssessmentStore;
  setAssessmentDetails: (payload: Partial<TAssessmentStore>) => void;
}

const defaultValue: TAssessmentStore = {
  invite_id: '',
  candidate_name: '',
  candidate_email: '',
  auth_method: [],
  assessment: {
    id: '',
    sections: [],
    name: '',
    role: { name: '' },
    role_level: '',
    time: new Date(),
    is_published: false,
    deadline: new Date(),
    time_limit: 0,
    created_at: new Date(),
    no_candidates: 0,
    updated_at: new Date(),
    creator: {
      id: 0,
      company_email: '',
      verified: false,
      created_at: new Date(),
      updated_at: new Date(),
      user: '',
      company: 0,
    },
    description: '',
    intro_video_url: '',
    is_shuffle: false,
    total_questions: 0,
    window_change_tolerance_level: 0,
    full_screen_tolerance_level: 0,
    is_restrict_tab_change: false,
    is_stop_screen_sharing: false,
    is_restrict_copying: false,
    is_identity_verification: false,
    allow_strict_id_verification: true,
    is_track_paste: false,
    type: '',
    bulk_tolerance_setup: null,
  },
  started: false,
  result_id: '',
  token: '',
};

export const useAssessmentDetails = create<setFunctionProps>()(
  persist(
    set => ({
      assessmentDetails: defaultValue,

      setAssessmentDetails: payload => {
        set(state => ({
          assessmentDetails: { ...state.assessmentDetails, ...payload },
        }));
      },
    }),
    {
      name: 'assessment-details',
      storage: createJSONStorage(() => sessionStorage),
    }
  )
);
