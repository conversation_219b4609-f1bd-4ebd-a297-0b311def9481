import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

export interface OfflineAnswersStoreProps {
  data: AnswerProps[];
  setOfflineAnswersDetails: (payload: any) => void;
}

export interface AnswerProps {
  candidate: string;
  invite_id: string;
  section_id: string;
  question: string;
  answer: string[];
}

export const useOfflineAnswersDetails = create<OfflineAnswersStoreProps>()(
  persist(
    set => ({
      data: [],
      setOfflineAnswersDetails(payload) {
        set(state => ({ data: payload }));
      },
    }),
    {
      name: 'OfflineAnswers-details',
      storage: createJSONStorage(() => sessionStorage),
    }
  )
);
