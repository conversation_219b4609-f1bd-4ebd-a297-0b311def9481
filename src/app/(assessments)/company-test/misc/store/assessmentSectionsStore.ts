import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { Question } from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import { QuestionSet, Section } from './assessmentStore';

// Define the shape of a question
type TQuestion = QuestionSet & {
  selected: string[];
};

interface sectionsStore {
  sections: Section[];
  panelOpen: boolean;
  togglePanel: () => void;
  setSection: (sections: Section[]) => void;
  answerQuestion: (questionId: string, answer: string | string[]) => void;
  clearSection: () => void;
  getQuestion: (questionId: string) => QuestionSet | null;
}

// Create the store
export const useAssessmentSectionsStore = create<sectionsStore>()(
  persist(
    (set, get) => ({
      sections: [],
      panelOpen: false,
      togglePanel: () => set(state => ({ panelOpen: !state.panelOpen })),
      setSection: sections => set({ sections }),
      answerQuestion: (questionId, answer) =>
        set(state => {
          const updatedSections = state.sections.map(section => {
            const updatedQuestionSet = section.question_set.map(question => {
              if (question.id === questionId) {
                return {
                  ...question,
                  candidate_answer: Array.isArray(answer) ? answer : [answer],
                };
              }
              return question;
            });
            return { ...section, question_set: updatedQuestionSet };
          });
          return { ...state, sections: updatedSections };
        }),
      clearSection: () => set({ sections: [] }),
      getQuestion: questionId => {
        const state = get();
        for (const section of state.sections) {
          const question = section.question_set.find(q => q.id === questionId);
          if (question) return question;
        }
        return null;
      },
    }),
    {
      name: 'assessment-sections-store',
      storage: createJSONStorage(() => sessionStorage),
    }
  )
);

export const useAllAssessmentSections = () =>
  useAssessmentSectionsStore(state => state.sections);

export const useAllAssessmentQuestions = () =>
  useAssessmentSectionsStore(state =>
    state.sections
      .flat()
      .map(item => [...item.question_set])
      .flat()
  );

export const currentSectionFromCurrentQuestion = (questionId: string) =>
  useAssessmentSectionsStore(state =>
    state.sections.find(section =>
      section.question_set.find(question => question.id === questionId)
    )
  );

export const QuestionsInCurrentSection = (sectionId: string) =>
  useAssessmentSectionsStore(
    state =>
      state.sections.find(section => section.id === sectionId)?.question_set
  );

export const useGetSectionFromQuestionId = (questionId: string) =>
  useAssessmentSectionsStore(state =>
    state.sections.find(section =>
      section.question_set.find(question => question.id === questionId)
    )
  );

export const useAnswerQuestion = (
  questionId: string,
  answer: string | string[]
) => {
  useAssessmentSectionsStore.getState().answerQuestion(questionId, answer);
};

export const useAnsweredQuestions = () =>
  useAssessmentSectionsStore(state =>
    state.sections
      .flat()
      .map(item => [...item.question_set])
      .flat()
          .filter(question => question.candidate_answer?.length)
  );

export const useGetQuestion = (questionId: string) =>
  useAssessmentSectionsStore(state => state.getQuestion(questionId));
