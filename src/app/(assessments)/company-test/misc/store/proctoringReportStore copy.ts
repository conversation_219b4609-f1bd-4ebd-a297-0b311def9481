import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { Flag } from '../types';
import moment from 'moment';

interface proctoringReportProps {
  data: {
    mouse_out_of_window: boolean;
    location: string;
    tab_switched: boolean;
    flags: any[];
    id: string;
    full_screen_tolerance_used: number;
    window_change_tolerance_used: number;
    was_forced_submission_full_screen: boolean;
    was_forced_submission_window_change: boolean;
  };
  setProctoringReport: (payload: any) => void;
  reportFlag: (flag: Flag, screenshot:any) => void;
  resetProctoringReport: () => void;
  resetProctoringFlags: () => void;
}

const MAX_FLAGS = 100; // Set maximum number of flags based on browser quota limit i guess
const MAX_INDIVIDUAL_FLAG = 10; // Set maximum number of flag for each one.

const default_state = {
  mouse_out_of_window: false,
  location: '',
  tab_switched: false,
  flags: [],
  id: 'default_id',
  full_screen_tolerance_used: 0,
  window_change_tolerance_used: 0,
  was_forced_submission_full_screen: false,
  was_forced_submission_window_change: false,
}

export const useProctoringReport = create<proctoringReportProps>()(
  persist(
    set => ({
      data: {...default_state},
      // FIRST TWEAK
      // setProctoringReport(payload) {
      //   set(state => ({ data: payload }));
      // },
      //Dont delete abeg. might fall back to this later
      // setProctoringReport(payload) {
      //   set(state => ({
      //     data: {
      //       ...state.data,
      //       ...payload,
      //       flags: [...state.data.flags, ...(payload.flags || [])], // Basically Ensure flags are merged in place
      //     },
      //   }));
      // },
      //

      setProctoringReport: payload => {
        set(state => {
          // Basically merging the existing flags in the state with any new flags provided in the payload
          // Sharp sharp means no time
          let newFlags = [...state.data.flags, ...(payload.flags || [])];

          // Ensuring flags array does not exceed the maximum number
          if (newFlags.length > MAX_FLAGS) {
            newFlags = newFlags.slice(newFlags.length - MAX_FLAGS);
          }

          return {
            data: {
              ...state.data,
              ...payload,
              // Retun my flags here
              flags: newFlags,
            },
          };
        });
      },

      reportFlag:(flag, screenshot)=>{
        set(state =>{
          const recorded_count =  Object.values(state.data.flags).filter(_flag=> { return _flag.flag === flag }).length
          if (recorded_count >= MAX_INDIVIDUAL_FLAG){
            return state
          }
          return {
            data: {
              ...state.data,
              flags:[
                ...state.data.flags,
                { time: moment(new Date().getTime()).format('DD-MM-yyyy hh:mm:ss'), flag, screenshot:screenshot.split("data:")[1] },
              ]
            },
          };
        })
      },

 resetProctoringFlags:() => {
        set(state => {
          return {
            data: {
              ...default_state,
              flags:[]
            },
          };
        });
      },

      resetProctoringReport:() => {
        set(state => {
          return {
            data: {
              ...state.data,
              flags:[],
            },
          };
        });
      },

    }),
    {
      name: 'proctoringReport',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
