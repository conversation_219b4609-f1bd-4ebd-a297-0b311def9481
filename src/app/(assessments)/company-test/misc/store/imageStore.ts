import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

interface ProfileImagesProps {
  data: null | { profilePicture: string; idPicture: string; isUploaded?: boolean };
  setProfileImages: (payload: {
    profilePicture: string;
    idPicture: string;
    isUploaded?: boolean;
  }) => void;
}
export const useProfileImages = create<ProfileImagesProps>()(
  persist(
    set => ({
      data: null,
      setProfileImages(payload) {
        set(state => ({ data: payload }));
      },
    }),
    {
      name: 'profileImages',
      storage: createJSONStorage(() => sessionStorage),
    }
  )
);
