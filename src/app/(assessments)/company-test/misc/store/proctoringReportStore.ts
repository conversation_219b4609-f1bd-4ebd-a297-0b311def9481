// import { } from 'zustand/middleware'
import { del, get, set } from 'idb-keyval'; // can use anything: IndexedDB, Ionic Storage, etc.
import moment from 'moment';
import { create } from 'zustand';
import { createJSONStorage, persist, StateStorage } from 'zustand/middleware';
import { Flag } from '../types';

export interface ProctoringReportState {
  mouse_out_of_window: boolean;
  location: string;
  tab_switched: boolean;
  id: string;
  full_screen_tolerance_used: number;
  window_change_tolerance_used: number;

  bulk_proctor_used: number;
  bulk_proctor_option: {
    key: string;
    value: number;
  }[];

  // was_forced_submission_full_screen: boolean;
  // was_forced_submission_window_change: boolean;
  // was_forced_submission_bulk_proctor: boolean;
  was_forced_submission: boolean;
  queue_position: number;
  estimated_wait_time: number;

}

interface ProctoringReportActions {
  proctoringReport: ProctoringReportState;
  setProctoringReport: (payload: Partial<ProctoringReportState>) => void;
  setProctoringId: (id: string) => void;
  // reportFlag: (flag: Flag, screenshot: string, pageScreenshot: string) => void;
  resetProctoringReport: () => void;
  // resetProctoringFlags: () => void;
  // removeFlags: (flagKey: string[]) => void;
}

const MAX_FLAGS = 100;
const MAX_INDIVIDUAL_FLAG = 10;

const defaultState: ProctoringReportState = {
  mouse_out_of_window: false,
  location: '',
  tab_switched: false,
  id: '',
  full_screen_tolerance_used: 0,
  window_change_tolerance_used: 0,
  bulk_proctor_used: 0,
  bulk_proctor_option: [],
  // was_forced_submission_full_screen: false,
  // was_forced_submission_window_change: false,
  // was_forced_submission_bulk_proctor: false,
  was_forced_submission: false,
  queue_position: 0,
  estimated_wait_time: 0,
};

// Custom storage object
// const storage: StateStorage = {
//   getItem: async (name: string): Promise<string | null> => {
//     return (await get(name)) || null;
//   },
//   setItem: async (name: string, value: string): Promise<void> => {
//     await set(name, value);
//   },
//   removeItem: async (name: string): Promise<void> => {
//     await del(name);
//   },
// };

export const useProctoringReport = create<ProctoringReportActions>()(
  persist(
    set => ({
      proctoringReport: defaultState,
      setProctoringReport: payload =>
        set(state => {
          return {
            proctoringReport: {
              ...state.proctoringReport,
              ...payload,
            },
          };
        }),
      setProctoringId: id =>
        set(state => ({
          proctoringReport: { ...state.proctoringReport, id },
        })),
      // reportFlag: (flag, screenshot, pageScreenshot) =>
      //   set(state => {
      //     const newFlag = {
      //       time: moment().format('DD-MM-YYYY HH:mm:ss'),
      //       flag,
      //       screenshot: screenshot.split('data:')[1],
      //       pc_capture: pageScreenshot,
      //     };
      //     const key = `${flag}:${newFlag.time.substring(-1)}`
      //       .toLowerCase()
      //       .slice(0, -1)
      //       .split(' ')
      //       .join('-');

      //     let updatedFlags = {
      //       ...state.proctoringReport.flags,
      //     };
      //     updatedFlags[key] = newFlag;
      //     //  console.log(updatedFlags, 'UPDATED FLAGS');
      //     return {
      //       proctoringReport: {
      //         ...state.proctoringReport,
      //         flags: { ...updatedFlags },
      //       },
      //     };
      //   }),
      // resetProctoringFlags: () =>
      //   set(state => {
      //     let newProctoring = Object.assign({}, state.proctoringReport);
      //     newProctoring.flags = {};
      //     return {
      //       proctoringReport: newProctoring,
      //     };
      //   }),
      // removeFlags: (flagKeys: string[]) =>
      //   set(state => {
      //     let proctorFlags = { ...state.proctoringReport.flags };

      //     for (let index = 0; index < flagKeys.length; index++) {
      //       const key = flagKeys[index];
      //       delete proctorFlags[key];
      //     }
      //     const newProctoring = {
      //       proctoringReport: {
      //         ...state.proctoringReport,
      //         flags: { ...proctorFlags },
      //       },
      //     };
      //     console.log(newProctoring);
      //     return newProctoring;
      //   }),
      resetProctoringReport: () => set({ proctoringReport: defaultState }),
    }),
    {
      name: 'proctoringReport',
      // storage: createJSONStorage(() => storage),
    }
  )
);
