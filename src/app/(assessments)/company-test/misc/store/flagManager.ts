import <PERSON><PERSON>, { <PERSON>ieError, Table } from 'dexie';
import moment from 'moment';

export interface proctorFlags {
  flags: { [key: string]: {} };
}

interface FlagRecord {
  key: string;
  time: string;
   screenshot: string;
  pc_capture: string;
}

interface DatabaseResponse {
  success: boolean;
  message?: string;
  error?: string;
}

const DATABASE_NAME = 'ProctorFlagDatabase';
class FlagDatabase extends Dexie {
  flags!: Table<FlagRecord>;

  constructor() {
    super(DATABASE_NAME);

    this.version(1).stores({
      flags: '&key, time, screenshot, pc_capture',
    });
    this.open();
    this.getAllFlags();
  }

  async reportFlag(
     screenshot: string,
    pc_capture: string,
   ): Promise<string> {
    try {
      const id = await this.flags.add({
        time: moment().format('DD-MM-YYYY HH:mm:ss'),
         screenshot,
        pc_capture,
        key: this.genKey(),
      });
      return id;
    } catch (e: any) {
     // console.log(e, 'INDEXDB ERROR');
      if (e.name == 'ConstraintError') {
        return 'key_exists';
      } else {
        return 'error';
      }
    }
  }
  genKey() {
    let date = moment().format('DD-MM-YYYY HH:mm:ss');
    return `${date}`.toLowerCase().slice(0, -1).split(' ').join('-');
  }

  async getAllFlags(): Promise<FlagRecord[]> {
    return await this.flags.toArray();
  }

  async cleanDatabase(): Promise<DatabaseResponse> {
    try {
      await this.flags.clear();
      return {
        success: true,
        message: 'Database cleared successfully',
      };
    } catch (error) {
      console.error('Error clearing database:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}
export default FlagDatabase;
