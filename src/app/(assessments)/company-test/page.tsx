'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import toast from 'react-hot-toast';
import ReactLoading from 'react-loading';
import { useGeolocation } from 'react-use';
import { Checkbox2, ToolTip } from '@/components/shared';
import Loader from '@/components/shared/loader';
import { useAcceptAssessmentInvite } from './misc/api';
import { useGetGeoLocation } from './misc/api/getGeolocation';
import {
  useGetVerifyCandidateCustom,
  useGetVerifyCandidateDefault,
} from './misc/api/verifyLogin';
import AssessmentNotAvailable from './misc/components/AssessmentNotAvailable';
import { useMQTTTrackingService } from './misc/hooks/useMQTTTrackingService';
import BadDeviceIcon from './misc/icons/BadDeviceIcon';
import GetLinkedLogoLight from './misc/icons/GetLinkedLogoLight';
import RoundCirclePurple from './misc/icons/RoundCirclePurple';
import RoundCircleWhite from './misc/icons/RoundCircleWhite';
import { UseCandidateStatusStore } from './misc/store';
import {
  answeredQuestionsStoreProps,
  useAnsweredQuestionsDetails,
} from './misc/store/answeredQuestionsStore';
import { useAssessmentDetails } from './misc/store/assessmentStore';
import FlagDatabase from './misc/store/flagManager';
import { useProctoringReport } from './misc/store/proctoringReportStore';
import { SocketProgressEvent } from './misc/utils/websocketUrl';

const page = () => {
  const router = useRouter();
  const search = useSearchParams();
  const token = search.get('invitation_token');
  const flagManager = useRef<FlagDatabase>(new FlagDatabase());

  const { mutate: acceptAssessmentInvite } = useAcceptAssessmentInvite();
  const { mutate: fetchGeoLocation } = useGetGeoLocation();
  const {
    mutate: verifyCandidateCustom,
    isLoading: isVerifyingCustomCandidate,
  } = useGetVerifyCandidateCustom();
  const { mutate: verifyCandidateDefault, isLoading: isVeridyingDefault } =
    useGetVerifyCandidateDefault();

  const { assessmentDetails, setAssessmentDetails } = useAssessmentDetails(
    state => ({
      assessmentDetails: state.assessmentDetails,
      setAssessmentDetails: state.setAssessmentDetails,
    })
  );
  const { setCandidateStatus, clearCandidateStatus } = UseCandidateStatusStore(
    state => ({
      setCandidateStatus: state.setCandidateStatus,
      clearCandidateStatus: state.clearCandidateStatus,
    })
  );
  const { proctoringReport, setProctoringReport, resetProctorReport } =
    useProctoringReport(state => ({
      proctoringReport: state.proctoringReport,
      setProctoringReport: state.setProctoringReport,
      resetProctorReport: state.resetProctoringReport,
    }));

  const location = useGeolocation();
  const [email, setEmail] = useState<string>('');
  const [loginToken, setLoginToken] = useState<string>('');
  const [candidateName, setCandidateName] = useState<string>('');
  const [candidatePhoneNumber, setCandidatePhoneNumber] = useState<string>('');

  const [method1, setMethod1] = useState<string>('');
  const [has_accepted_policy, setHasAcceptedPolicy] = useState(false);
  const [method2, setMethod2] = useState<string>('');

  const [showAssessmentNotAvalaible, setShowAssessmentNotAvailable] =
    useState(false);
  const [errObj, setErrObj] = useState<any>();

  const setAnsweredQuestions = useAnsweredQuestionsDetails(
    (state: answeredQuestionsStoreProps) => state.setAnsweredQuestionsDetails
  );
  const resetPreviousAnswers = useAnsweredQuestionsDetails(
    (state: answeredQuestionsStoreProps) => state.resetPreviousAnswers
  );

  // SOCKET
  const { sendMessage: sendSocketMessage, isConnected } =
    useMQTTTrackingService();

  useEffect(() => {
    if (token) {
      acceptAssessmentInvite(token, {
        onSuccess: data => {
          setAssessmentDetails(data);
          setCandidateName(data?.candidate_name as string);
          setLoginToken(data?.token as string);
          setEmail(data?.candidate_email as string);
          setCandidatePhoneNumber(data?.phone_number as string);
          resetProctorReport();

          sendSocketMessage(SocketProgressEvent.ACCEPT_INVITE);
          // let sections = data.assessment.sections as Section[]
          // if (!data.assessment.is_shuffle_sections) {
          //   sections = sections.sort((a: Section, b: Section) => a.order! - b.order!)
          // }
          // data.assessment.sections = sections
        },
        onError: (err: any) => {
          setErrObj(err?.response.data);
          setShowAssessmentNotAvailable(true);
        },
      });
    }
  }, []);

  useEffect(() => {
    console.log(location, "USER LOCATION")
    const setGeoLocation = async () => {
      fetchGeoLocation(
        {
          long: location?.longitude as number,
          lat: location?.latitude as number,
          key: 'be5cdeb5c4c843f5aafa31ce711734f0',
        },
        {
          onSuccess: data => {
            setProctoringReport({
              ...proctoringReport,
              location: data?.results[0]?.components?.county,
            });
          },
          onError: () => {
            setProctoringReport({
              ...proctoringReport,
              location: 'Unknown',
            });
          },
        }
      );
    };
    if (location?.longitude && location?.latitude) {
      setGeoLocation();
    }
  }, [location]);

  const handleLogin = (e: any) => {
    e.preventDefault();
    resetProctorReport();
    flagManager.current.cleanDatabase();

    setAssessmentDetails({
      ...assessmentDetails,
      candidate_name: candidateName,
    });

    if (assessmentDetails?.auth_method[0]) {
      verifyCandidateCustom(
        {
          invite_id: assessmentDetails.invite_id,
          method1: method1 as string,
          method2: method2 as string,
          is_custom: true,
          candidate_email: email.toLowerCase(),
          assessment_id: assessmentDetails?.assessment.id as string,
          candidate_name: candidateName,
          candidate_phone_number: candidatePhoneNumber,
          result_id: assessmentDetails?.result_id as string,
        },
        {
          onSuccess: data => {
            if (data.status === 200) {
              sendSocketMessage(SocketProgressEvent.LOGIN_VERIFIED);

              if (data.data.candidate_session?.id !== undefined) {
                setCandidateStatus({ isReturning: true });
                setAssessmentDetails({
                  ...assessmentDetails,
                  assessment: {
                    ...assessmentDetails?.assessment,
                    time_limit: data?.data?.candidate_session?.time_left,
                  },
                });
                setProctoringReport({
                  ...proctoringReport,
                  id: data?.data?.candidate_session?.proctoring_report,
                });
                setAnsweredQuestions(
                  data?.data?.candidate_session.answered_questions
                );
                router.push('/company-test/test?page=start-test');
              } else {
                clearCandidateStatus();
                setAssessmentDetails({
                  ...assessmentDetails,
                  assessment: {
                    ...assessmentDetails?.assessment,
                    time_limit: data?.data?.assessment?.time_limit,
                  },
                });
                router.push('/company-test/assessment-criteria');
              }
            }
          },
          onError: (error: any) => {
            toast.error(error.response.data.message as string);
          },
        }
      );
    } else {
      verifyCandidateDefault(
        {
          assessment_id: assessmentDetails?.assessment.id as string,
          email: email.toLowerCase() as string,
          token: loginToken as string,
          invite_id: assessmentDetails?.invite_id as string,
          candidate_name: candidateName,
          result_id: assessmentDetails?.result_id as string,
          candidate_phone_number: candidatePhoneNumber,
        },
        {
          onSuccess: data => {
            if (data.status === 200) {
              sendSocketMessage(SocketProgressEvent.LOGIN_VERIFIED);

              console.log(data.data, 'DATA');

              if (data?.data?.data !== undefined) {
                setCandidateStatus({
                  isReturning: true,
                  timeLeft: data?.data?.data?.remaining_time,
                });
                setAssessmentDetails({
                  ...assessmentDetails,
                  assessment: {
                    ...assessmentDetails?.assessment,
                    time_limit: data?.data?.data?.remaining_time,
                  },
                });
                setProctoringReport({
                  ...proctoringReport,
                  id: data?.data?.data?.proctoring_report,
                });
                setAnsweredQuestions(data?.data?.data?.answered_questions);

                router.push('/company-test/test?page=start-test');
              } else {
                resetPreviousAnswers();
                router.push('/company-test/test?page=assessment-criteria');
              }
            }
          },
          onError: (error: any) => {
            console.log(error.response, 'ERROR OF STATE');
            toast.error(
              error.response.data.message || error.response.data.error
            );
          },
        }
      );
    }
    // router.push('/company-test/assessment-criteria');.
  };

  return (
    <div className="relative flex h-full w-screen items-center justify-center overflow-auto  bg-[#1F0231] md:h-screen ">
      {assessmentDetails ? (
        <>
          <div className="z-20 flex h-[70%] w-[90%] flex-col justify-center rounded-2xl border border-[#FFFFFF38] bg-[#F8F6FF05] p-3 sm:w-[500px] sm:p-10">
            <div className="hidden lg:block">
              <div className="relative h-[40px] w-[200px]">
                <p className="text-3xl text-white"> Welcome 👋</p>
              </div>
              <form
                onSubmit={e => handleLogin(e)}
                className="flex w-full flex-col gap-y-3 py-5"
                action=""
              >
                <input
                  className="rounded-lg bg-[#FFFFFF16] p-3 capitalize text-white outline-none placeholder:text-white"
                  type="text"
                  placeholder="Name"
                  required
                  value={candidateName}
                  onChange={e => setCandidateName(e.target.value)}
                />
                {assessmentDetails?.auth_method?.length === 0 ? (
                  <>
                    <input
                      className="rounded-lg bg-[#FFFFFF16] p-3 lowercase text-white outline-none placeholder:text-white"
                      type="email"
                      placeholder="Email"
                      required
                      value={email}
                      onChange={e => setEmail(e.target.value)}
                    />
                    <input
                      className="rounded-lg bg-[#FFFFFF16] p-3  text-white outline-none placeholder:text-white"
                      type="text"
                      placeholder="Token"
                      required
                      value={loginToken}
                      onChange={e => setLoginToken(e.target.value)}
                    />
                  </>
                ) : (
                  <>
                    <input
                      className="rounded-lg bg-[#FFFFFF16] p-3  text-white outline-none placeholder:text-white"
                      type="text"
                      placeholder={
                        assessmentDetails?.auth_method[0] &&
                        assessmentDetails.auth_method[0]
                      }
                      required
                      value={method1}
                      onChange={e => setMethod1(e?.target?.value)}
                    />
                    <input
                      className="rounded-lg bg-[#FFFFFF16] p-3  text-white outline-none placeholder:text-white"
                      type="text"
                      placeholder={
                        assessmentDetails?.auth_method[1] &&
                        assessmentDetails?.auth_method[1]
                      }
                      required
                      value={method2}
                      onChange={e => setMethod2(e?.target?.value)}
                    />
                  </>
                )}
                <input
                  className="rounded-lg bg-[#FFFFFF16] p-3 capitalize text-white outline-none placeholder:text-white"
                  type="tel"
                  placeholder="Phone number"
                  required
                  value={candidatePhoneNumber}
                  onChange={e => {
                    const value = e.target.value;
                    // Only allow numbers, spaces, dashes, parentheses, and plus sign
                    if (/^[\d\s\-\(\)\+]*$/.test(value)) {
                      setCandidatePhoneNumber(value);
                    }
                  }}
                  onKeyDown={e => {
                    const allowedPattern = /[\d\s\-\(\)\+]/;
                    const allowedKeys = [
                      'Backspace',
                      'Delete',
                      'ArrowLeft',
                      'ArrowRight',
                      'Tab',
                      'Home',
                      'End',
                      'Enter',
                    ];
                    if (
                      !allowedKeys.includes(e.key) &&
                      !allowedPattern.test(e.key)
                    ) {
                      e.preventDefault();
                    }
                  }}
                />
                <div className="mt-6 flex items-center gap-3 rounded-xl bg-[#8b71f31f] px-2 py-2 text-white ">
                  <Checkbox2
                    checked={has_accepted_policy}
                    onCheckedChange={e => setHasAcceptedPolicy(e as boolean)}
                    name="accept privacy policy"
                    required
                    className="!border-white"
                    checkClassName="!text-white"
                    id="accept-privacy-policy"
                  />
                  <label className="text-xs" htmlFor="accept-privacy-policy">
                    I have read and accept the
                    <Link
                      href="/privacy"
                      className="ml-1.5 text-[0.8rem] font-medium underline"
                    >
                      privacy policy
                    </Link>
                  </label>
                </div>
                <button
                  type="submit"
                  className="relative flex w-full items-center justify-center rounded-lg bg-white p-3 text-[#1f0231] disabled:bg-white/70"
                  disabled={!has_accepted_policy}
                  // onClick={() => router.push('/company-test/start-test')}
                >
                  {!has_accepted_policy && (
                    <ToolTip content="Please accept the privacy policy">
                      <div className="absolute bottom-0 left-0 right-0  top-0 z-[10] h-full w-full" />
                    </ToolTip>
                  )}
                  {isVerifyingCustomCandidate || isVeridyingDefault ? (
                    <div className="flex h-8 w-8 items-center justify-center">
                      <ReactLoading
                        height={20}
                        width={20}
                        type="spin"
                        color="#3C1356"
                      />
                    </div>
                  ) : (
                    <p>Login</p>
                  )}
                </button>
              </form>
            </div>
            <div className="flex-col items-center gap-y-4 lg:hidden">
              <BadDeviceIcon />
              <div className="text-white">
                <p className="text-left text-lg font-bold">
                  Sorry, Incompatible device
                </p>
                <p className="max-w-[299px] py-2 text-left text-xs font-extralight leading-[15.62px]">
                  This assessment cannot be taken on a mobile device due to the
                  proctoring feature. It is only accessible on a laptop,
                  desktop, or system with a compatible operating system.
                </p>
              </div>

              <div className="mt-4 text-white">
                <p className="max-w-[211px] text-left text-lg font-bold">
                  Other possible reasons for Incompatibility:
                </p>
                <p className="max-w-[299px] py-2 text-left text-xs font-extralight leading-[15.62px]">
                  <span className="font-bold">Zoomed-in Screen:</span> If you
                  are using a laptop and encounter this issue, follow these
                  steps below to resolve it:
                </p>
                <p className="max-w-[299px] py-2 text-left text-xs font-normal leading-[15.62px]">
                  Press Command (⌘) + "-" (on Mac) continuously to zoom out and
                  adjust to until error disappears.
                </p>
                <p className="flex items-center gap-[11px] text-xs">
                  <svg
                    width="67"
                    height="35"
                    viewBox="0 0 67 35"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <rect width="67" height="35" rx="6" fill="white" />
                    <path
                      d="M11.8 28.12C11.1133 28.12 10.52 27.97 10.02 27.67C9.52667 27.3633 9.14333 26.94 8.87 26.4C8.60333 25.8533 8.47 25.22 8.47 24.5C8.47 23.7867 8.60333 23.1567 8.87 22.61C9.14333 22.0633 9.52667 21.64 10.02 21.34C10.52 21.0333 11.1133 20.88 11.8 20.88C12.62 20.88 13.2833 21.0767 13.79 21.47C14.3033 21.8633 14.63 22.4167 14.77 23.13H13.67C13.57 22.7167 13.3633 22.3867 13.05 22.14C12.7433 21.8933 12.3267 21.77 11.8 21.77C11.3267 21.77 10.9167 21.88 10.57 22.1C10.2233 22.32 9.95667 22.6333 9.77 23.04C9.58333 23.4467 9.49 23.9333 9.49 24.5C9.49 25.0667 9.58333 25.5567 9.77 25.97C9.95667 26.3767 10.2233 26.69 10.57 26.91C10.9167 27.1233 11.3267 27.23 11.8 27.23C12.3267 27.23 12.7433 27.1133 13.05 26.88C13.3633 26.64 13.57 26.3167 13.67 25.91H14.77C14.63 26.6033 14.3033 27.1467 13.79 27.54C13.2833 27.9267 12.62 28.12 11.8 28.12ZM18.2163 28.12C17.743 28.12 17.3163 28.01 16.9363 27.79C16.563 27.57 16.2663 27.2633 16.0463 26.87C15.833 26.47 15.7263 26.01 15.7263 25.49C15.7263 24.9567 15.8363 24.4933 16.0563 24.1C16.2763 23.7 16.5763 23.39 16.9563 23.17C17.3363 22.95 17.763 22.84 18.2363 22.84C18.7163 22.84 19.143 22.95 19.5163 23.17C19.8897 23.39 20.183 23.6967 20.3963 24.09C20.6163 24.4833 20.7263 24.9467 20.7263 25.48C20.7263 26.0133 20.6163 26.4767 20.3963 26.87C20.183 27.2633 19.8863 27.57 19.5063 27.79C19.1263 28.01 18.6963 28.12 18.2163 28.12ZM18.2163 27.26C18.4897 27.26 18.7363 27.1933 18.9563 27.06C19.183 26.9267 19.363 26.73 19.4963 26.47C19.6363 26.2033 19.7063 25.8733 19.7063 25.48C19.7063 25.0867 19.6397 24.76 19.5063 24.5C19.373 24.2333 19.193 24.0333 18.9663 23.9C18.7463 23.7667 18.503 23.7 18.2363 23.7C17.9697 23.7 17.723 23.7667 17.4963 23.9C17.2697 24.0333 17.0863 24.2333 16.9463 24.5C16.813 24.76 16.7463 25.0867 16.7463 25.48C16.7463 25.8733 16.813 26.2033 16.9463 26.47C17.0863 26.73 17.2663 26.9267 17.4863 27.06C17.713 27.1933 17.9563 27.26 18.2163 27.26ZM21.9027 28V22.96H22.8027L22.8727 23.67C23.0327 23.41 23.246 23.2067 23.5127 23.06C23.7793 22.9133 24.0793 22.84 24.4127 22.84C24.666 22.84 24.896 22.8767 25.1027 22.95C25.3093 23.0167 25.4927 23.12 25.6527 23.26C25.8127 23.4 25.9427 23.5733 26.0427 23.78C26.2227 23.4867 26.4627 23.2567 26.7627 23.09C27.0693 22.9233 27.3927 22.84 27.7327 22.84C28.1393 22.84 28.4893 22.9233 28.7827 23.09C29.076 23.25 29.2993 23.4933 29.4527 23.82C29.606 24.14 29.6827 24.54 29.6827 25.02V28H28.6927V25.12C28.6927 24.6533 28.596 24.3 28.4027 24.06C28.216 23.82 27.9393 23.7 27.5727 23.7C27.326 23.7 27.106 23.7633 26.9127 23.89C26.7193 24.0167 26.566 24.2 26.4527 24.44C26.346 24.68 26.2927 24.9733 26.2927 25.32V28H25.2927V25.12C25.2927 24.6533 25.196 24.3 25.0027 24.06C24.816 23.82 24.5393 23.7 24.1727 23.7C23.9393 23.7 23.726 23.7633 23.5327 23.89C23.3393 24.0167 23.186 24.2 23.0727 24.44C22.9593 24.68 22.9027 24.9733 22.9027 25.32V28H21.9027ZM30.9749 28V22.96H31.8749L31.9449 23.67C32.1049 23.41 32.3183 23.2067 32.5849 23.06C32.8516 22.9133 33.1516 22.84 33.4849 22.84C33.7383 22.84 33.9683 22.8767 34.1749 22.95C34.3816 23.0167 34.5649 23.12 34.7249 23.26C34.8849 23.4 35.0149 23.5733 35.1149 23.78C35.2949 23.4867 35.5349 23.2567 35.8349 23.09C36.1416 22.9233 36.4649 22.84 36.8049 22.84C37.2116 22.84 37.5616 22.9233 37.8549 23.09C38.1483 23.25 38.3716 23.4933 38.5249 23.82C38.6783 24.14 38.7549 24.54 38.7549 25.02V28H37.7649V25.12C37.7649 24.6533 37.6683 24.3 37.4749 24.06C37.2883 23.82 37.0116 23.7 36.6449 23.7C36.3983 23.7 36.1783 23.7633 35.9849 23.89C35.7916 24.0167 35.6383 24.2 35.5249 24.44C35.4183 24.68 35.3649 24.9733 35.3649 25.32V28H34.3649V25.12C34.3649 24.6533 34.2683 24.3 34.0749 24.06C33.8883 23.82 33.6116 23.7 33.2449 23.7C33.0116 23.7 32.7983 23.7633 32.6049 23.89C32.4116 24.0167 32.2583 24.2 32.1449 24.44C32.0316 24.68 31.9749 24.9733 31.9749 25.32V28H30.9749ZM41.7172 28.12C41.3039 28.12 40.9605 28.05 40.6872 27.91C40.4139 27.77 40.2105 27.5833 40.0772 27.35C39.9439 27.11 39.8772 26.8533 39.8772 26.58C39.8772 26.2467 39.9639 25.9633 40.1372 25.73C40.3105 25.49 40.5572 25.3067 40.8772 25.18C41.1972 25.0533 41.5805 24.99 42.0272 24.99H43.3372C43.3372 24.6967 43.2939 24.4533 43.2072 24.26C43.1205 24.0667 42.9905 23.9233 42.8172 23.83C42.6505 23.73 42.4372 23.68 42.1772 23.68C41.8772 23.68 41.6205 23.7533 41.4072 23.9C41.1939 24.04 41.0605 24.25 41.0072 24.53H40.0072C40.0472 24.1767 40.1672 23.8767 40.3672 23.63C40.5739 23.3767 40.8372 23.1833 41.1572 23.05C41.4772 22.91 41.8172 22.84 42.1772 22.84C42.6505 22.84 43.0472 22.9233 43.3672 23.09C43.6872 23.2567 43.9272 23.4933 44.0872 23.8C44.2539 24.1 44.3372 24.46 44.3372 24.88V28H43.4672L43.3872 27.15C43.3139 27.2833 43.2272 27.41 43.1272 27.53C43.0272 27.65 42.9072 27.7533 42.7672 27.84C42.6339 27.9267 42.4772 27.9933 42.2972 28.04C42.1239 28.0933 41.9305 28.12 41.7172 28.12ZM41.9072 27.31C42.1205 27.31 42.3139 27.2667 42.4872 27.18C42.6605 27.0933 42.8072 26.9767 42.9272 26.83C43.0539 26.6767 43.1472 26.5067 43.2072 26.32C43.2739 26.1267 43.3105 25.93 43.3172 25.73V25.7H42.1272C41.8405 25.7 41.6072 25.7367 41.4272 25.81C41.2539 25.8767 41.1272 25.97 41.0472 26.09C40.9672 26.21 40.9272 26.35 40.9272 26.51C40.9272 26.6767 40.9639 26.82 41.0372 26.94C41.1172 27.0533 41.2305 27.1433 41.3772 27.21C41.5239 27.2767 41.7005 27.31 41.9072 27.31ZM45.6136 28V22.96H46.5136L46.5736 23.82C46.7336 23.52 46.9603 23.2833 47.2536 23.11C47.5469 22.93 47.8836 22.84 48.2636 22.84C48.6636 22.84 49.0069 22.92 49.2936 23.08C49.5803 23.24 49.8036 23.4833 49.9636 23.81C50.1236 24.13 50.2036 24.5333 50.2036 25.02V28H49.2036V25.12C49.2036 24.6533 49.1003 24.3 48.8936 24.06C48.6869 23.82 48.3869 23.7 47.9936 23.7C47.7336 23.7 47.5003 23.7633 47.2936 23.89C47.0869 24.01 46.9203 24.19 46.7936 24.43C46.6736 24.67 46.6136 24.9633 46.6136 25.31V28H45.6136ZM53.7227 28.12C53.2427 28.12 52.8194 28.0067 52.4527 27.78C52.0861 27.5467 51.7994 27.2333 51.5927 26.84C51.3927 26.44 51.2927 25.9867 51.2927 25.48C51.2927 24.9667 51.3927 24.5133 51.5927 24.12C51.7994 23.7267 52.0861 23.4167 52.4527 23.19C52.8261 22.9567 53.2527 22.84 53.7327 22.84C54.1261 22.84 54.4727 22.92 54.7727 23.08C55.0727 23.2333 55.3061 23.4533 55.4727 23.74V20.8H56.4727V28H55.5727L55.4727 27.22C55.3727 27.3733 55.2427 27.52 55.0827 27.66C54.9227 27.7933 54.7294 27.9033 54.5027 27.99C54.2761 28.0767 54.0161 28.12 53.7227 28.12ZM53.8827 27.25C54.1961 27.25 54.4727 27.1767 54.7127 27.03C54.9527 26.8833 55.1361 26.6767 55.2627 26.41C55.3961 26.1433 55.4627 25.8333 55.4627 25.48C55.4627 25.1267 55.3961 24.82 55.2627 24.56C55.1361 24.2933 54.9527 24.0867 54.7127 23.94C54.4727 23.7867 54.1961 23.71 53.8827 23.71C53.5827 23.71 53.3127 23.7867 53.0727 23.94C52.8327 24.0867 52.6461 24.2933 52.5127 24.56C52.3794 24.82 52.3127 25.1267 52.3127 25.48C52.3127 25.8333 52.3794 26.1433 52.5127 26.41C52.6461 26.6767 52.8327 26.8833 53.0727 27.03C53.3127 27.1767 53.5827 27.25 53.8827 27.25Z"
                      fill="black"
                    />
                    <path
                      d="M51.04 13C50.5733 13 50.2 12.8633 49.92 12.59C49.6467 12.3167 49.51 11.95 49.51 11.49C49.51 11.0033 49.6533 10.6267 49.94 10.36C50.2333 10.0867 50.6633 9.95 51.23 9.95H51.82V8.85H51.23C50.6633 8.85 50.2333 8.71667 49.94 8.45C49.6533 8.17667 49.51 7.79667 49.51 7.31C49.51 6.85 49.6467 6.48333 49.92 6.21C50.2 5.93667 50.5733 5.8 51.04 5.8C51.4133 5.8 51.7067 5.87 51.92 6.01C52.14 6.15 52.2933 6.33667 52.38 6.57C52.4733 6.80333 52.52 7.06 52.52 7.34V8.17H53.62V7.34C53.62 7.06 53.6633 6.80333 53.75 6.57C53.8433 6.33667 53.9967 6.15 54.21 6.01C54.43 5.87 54.7267 5.8 55.1 5.8C55.5667 5.8 55.9367 5.93667 56.21 6.21C56.49 6.48333 56.63 6.85 56.63 7.31C56.63 7.79667 56.4867 8.17667 56.2 8.45C55.9133 8.71667 55.4833 8.85 54.91 8.85H54.32V9.95H54.91C55.4833 9.95 55.9133 10.0867 56.2 10.36C56.4867 10.6267 56.63 11.0033 56.63 11.49C56.63 11.95 56.49 12.3167 56.21 12.59C55.9367 12.8633 55.5667 13 55.1 13C54.7267 13 54.43 12.93 54.21 12.79C53.9967 12.65 53.8433 12.4633 53.75 12.23C53.6633 11.9967 53.62 11.74 53.62 11.46V10.63H52.52V11.46C52.52 11.74 52.4733 11.9967 52.38 12.23C52.2933 12.4633 52.14 12.65 51.92 12.79C51.7067 12.93 51.4133 13 51.04 13ZM54.32 7.32V8.17H54.91C55.2633 8.17 55.52 8.1 55.68 7.96C55.84 7.81333 55.92 7.59667 55.92 7.31C55.92 7.01667 55.84 6.80667 55.68 6.68C55.5267 6.55333 55.3333 6.49 55.1 6.49C54.84 6.49 54.6433 6.56667 54.51 6.72C54.3833 6.86667 54.32 7.06667 54.32 7.32ZM51.23 8.17H51.82V7.32C51.82 7.06667 51.7533 6.86667 51.62 6.72C51.4933 6.56667 51.3 6.49 51.04 6.49C50.8067 6.49 50.61 6.55333 50.45 6.68C50.2967 6.80667 50.22 7.01667 50.22 7.31C50.22 7.59667 50.3 7.81333 50.46 7.96C50.62 8.1 50.8767 8.17 51.23 8.17ZM52.52 9.95H53.62V8.85H52.52V9.95ZM51.04 12.31C51.3 12.31 51.4933 12.2367 51.62 12.09C51.7533 11.9367 51.82 11.7333 51.82 11.48V10.63H51.23C50.8767 10.63 50.62 10.7033 50.46 10.85C50.3 10.99 50.22 11.2033 50.22 11.49C50.22 11.7833 50.2967 11.9933 50.45 12.12C50.61 12.2467 50.8067 12.31 51.04 12.31ZM54.32 11.48C54.32 11.7333 54.3833 11.9367 54.51 12.09C54.6433 12.2367 54.84 12.31 55.1 12.31C55.3333 12.31 55.5267 12.2467 55.68 12.12C55.84 11.9933 55.92 11.7833 55.92 11.49C55.92 11.2033 55.84 10.99 55.68 10.85C55.52 10.7033 55.2633 10.63 54.91 10.63H54.32V11.48Z"
                      fill="black"
                    />
                  </svg>
                  <span>and</span>
                  <svg
                    width="67"
                    height="35"
                    viewBox="0 0 67 35"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <rect width="67" height="35" rx="6" fill="white" />
                    <path
                      d="M11.8 28.12C11.1133 28.12 10.52 27.97 10.02 27.67C9.52667 27.3633 9.14333 26.94 8.87 26.4C8.60333 25.8533 8.47 25.22 8.47 24.5C8.47 23.7867 8.60333 23.1567 8.87 22.61C9.14333 22.0633 9.52667 21.64 10.02 21.34C10.52 21.0333 11.1133 20.88 11.8 20.88C12.62 20.88 13.2833 21.0767 13.79 21.47C14.3033 21.8633 14.63 22.4167 14.77 23.13H13.67C13.57 22.7167 13.3633 22.3867 13.05 22.14C12.7433 21.8933 12.3267 21.77 11.8 21.77C11.3267 21.77 10.9167 21.88 10.57 22.1C10.2233 22.32 9.95667 22.6333 9.77 23.04C9.58333 23.4467 9.49 23.9333 9.49 24.5C9.49 25.0667 9.58333 25.5567 9.77 25.97C9.95667 26.3767 10.2233 26.69 10.57 26.91C10.9167 27.1233 11.3267 27.23 11.8 27.23C12.3267 27.23 12.7433 27.1133 13.05 26.88C13.3633 26.64 13.57 26.3167 13.67 25.91H14.77C14.63 26.6033 14.3033 27.1467 13.79 27.54C13.2833 27.9267 12.62 28.12 11.8 28.12ZM18.2163 28.12C17.743 28.12 17.3163 28.01 16.9363 27.79C16.563 27.57 16.2663 27.2633 16.0463 26.87C15.833 26.47 15.7263 26.01 15.7263 25.49C15.7263 24.9567 15.8363 24.4933 16.0563 24.1C16.2763 23.7 16.5763 23.39 16.9563 23.17C17.3363 22.95 17.763 22.84 18.2363 22.84C18.7163 22.84 19.143 22.95 19.5163 23.17C19.8897 23.39 20.183 23.6967 20.3963 24.09C20.6163 24.4833 20.7263 24.9467 20.7263 25.48C20.7263 26.0133 20.6163 26.4767 20.3963 26.87C20.183 27.2633 19.8863 27.57 19.5063 27.79C19.1263 28.01 18.6963 28.12 18.2163 28.12ZM18.2163 27.26C18.4897 27.26 18.7363 27.1933 18.9563 27.06C19.183 26.9267 19.363 26.73 19.4963 26.47C19.6363 26.2033 19.7063 25.8733 19.7063 25.48C19.7063 25.0867 19.6397 24.76 19.5063 24.5C19.373 24.2333 19.193 24.0333 18.9663 23.9C18.7463 23.7667 18.503 23.7 18.2363 23.7C17.9697 23.7 17.723 23.7667 17.4963 23.9C17.2697 24.0333 17.0863 24.2333 16.9463 24.5C16.813 24.76 16.7463 25.0867 16.7463 25.48C16.7463 25.8733 16.813 26.2033 16.9463 26.47C17.0863 26.73 17.2663 26.9267 17.4863 27.06C17.713 27.1933 17.9563 27.26 18.2163 27.26ZM21.9027 28V22.96H22.8027L22.8727 23.67C23.0327 23.41 23.246 23.2067 23.5127 23.06C23.7793 22.9133 24.0793 22.84 24.4127 22.84C24.666 22.84 24.896 22.8767 25.1027 22.95C25.3093 23.0167 25.4927 23.12 25.6527 23.26C25.8127 23.4 25.9427 23.5733 26.0427 23.78C26.2227 23.4867 26.4627 23.2567 26.7627 23.09C27.0693 22.9233 27.3927 22.84 27.7327 22.84C28.1393 22.84 28.4893 22.9233 28.7827 23.09C29.076 23.25 29.2993 23.4933 29.4527 23.82C29.606 24.14 29.6827 24.54 29.6827 25.02V28H28.6927V25.12C28.6927 24.6533 28.596 24.3 28.4027 24.06C28.216 23.82 27.9393 23.7 27.5727 23.7C27.326 23.7 27.106 23.7633 26.9127 23.89C26.7193 24.0167 26.566 24.2 26.4527 24.44C26.346 24.68 26.2927 24.9733 26.2927 25.32V28H25.2927V25.12C25.2927 24.6533 25.196 24.3 25.0027 24.06C24.816 23.82 24.5393 23.7 24.1727 23.7C23.9393 23.7 23.726 23.7633 23.5327 23.89C23.3393 24.0167 23.186 24.2 23.0727 24.44C22.9593 24.68 22.9027 24.9733 22.9027 25.32V28H21.9027ZM30.9749 28V22.96H31.8749L31.9449 23.67C32.1049 23.41 32.3183 23.2067 32.5849 23.06C32.8516 22.9133 33.1516 22.84 33.4849 22.84C33.7383 22.84 33.9683 22.8767 34.1749 22.95C34.3816 23.0167 34.5649 23.12 34.7249 23.26C34.8849 23.4 35.0149 23.5733 35.1149 23.78C35.2949 23.4867 35.5349 23.2567 35.8349 23.09C36.1416 22.9233 36.4649 22.84 36.8049 22.84C37.2116 22.84 37.5616 22.9233 37.8549 23.09C38.1483 23.25 38.3716 23.4933 38.5249 23.82C38.6783 24.14 38.7549 24.54 38.7549 25.02V28H37.7649V25.12C37.7649 24.6533 37.6683 24.3 37.4749 24.06C37.2883 23.82 37.0116 23.7 36.6449 23.7C36.3983 23.7 36.1783 23.7633 35.9849 23.89C35.7916 24.0167 35.6383 24.2 35.5249 24.44C35.4183 24.68 35.3649 24.9733 35.3649 25.32V28H34.3649V25.12C34.3649 24.6533 34.2683 24.3 34.0749 24.06C33.8883 23.82 33.6116 23.7 33.2449 23.7C33.0116 23.7 32.7983 23.7633 32.6049 23.89C32.4116 24.0167 32.2583 24.2 32.1449 24.44C32.0316 24.68 31.9749 24.9733 31.9749 25.32V28H30.9749ZM41.7172 28.12C41.3039 28.12 40.9605 28.05 40.6872 27.91C40.4139 27.77 40.2105 27.5833 40.0772 27.35C39.9439 27.11 39.8772 26.8533 39.8772 26.58C39.8772 26.2467 39.9639 25.9633 40.1372 25.73C40.3105 25.49 40.5572 25.3067 40.8772 25.18C41.1972 25.0533 41.5805 24.99 42.0272 24.99H43.3372C43.3372 24.6967 43.2939 24.4533 43.2072 24.26C43.1205 24.0667 42.9905 23.9233 42.8172 23.83C42.6505 23.73 42.4372 23.68 42.1772 23.68C41.8772 23.68 41.6205 23.7533 41.4072 23.9C41.1939 24.04 41.0605 24.25 41.0072 24.53H40.0072C40.0472 24.1767 40.1672 23.8767 40.3672 23.63C40.5739 23.3767 40.8372 23.1833 41.1572 23.05C41.4772 22.91 41.8172 22.84 42.1772 22.84C42.6505 22.84 43.0472 22.9233 43.3672 23.09C43.6872 23.2567 43.9272 23.4933 44.0872 23.8C44.2539 24.1 44.3372 24.46 44.3372 24.88V28H43.4672L43.3872 27.15C43.3139 27.2833 43.2272 27.41 43.1272 27.53C43.0272 27.65 42.9072 27.7533 42.7672 27.84C42.6339 27.9267 42.4772 27.9933 42.2972 28.04C42.1239 28.0933 41.9305 28.12 41.7172 28.12ZM41.9072 27.31C42.1205 27.31 42.3139 27.2667 42.4872 27.18C42.6605 27.0933 42.8072 26.9767 42.9272 26.83C43.0539 26.6767 43.1472 26.5067 43.2072 26.32C43.2739 26.1267 43.3105 25.93 43.3172 25.73V25.7H42.1272C41.8405 25.7 41.6072 25.7367 41.4272 25.81C41.2539 25.8767 41.1272 25.97 41.0472 26.09C40.9672 26.21 40.9272 26.35 40.9272 26.51C40.9272 26.6767 40.9639 26.82 41.0372 26.94C41.1172 27.0533 41.2305 27.1433 41.3772 27.21C41.5239 27.2767 41.7005 27.31 41.9072 27.31ZM45.6136 28V22.96H46.5136L46.5736 23.82C46.7336 23.52 46.9603 23.2833 47.2536 23.11C47.5469 22.93 47.8836 22.84 48.2636 22.84C48.6636 22.84 49.0069 22.92 49.2936 23.08C49.5803 23.24 49.8036 23.4833 49.9636 23.81C50.1236 24.13 50.2036 24.5333 50.2036 25.02V28H49.2036V25.12C49.2036 24.6533 49.1003 24.3 48.8936 24.06C48.6869 23.82 48.3869 23.7 47.9936 23.7C47.7336 23.7 47.5003 23.7633 47.2936 23.89C47.0869 24.01 46.9203 24.19 46.7936 24.43C46.6736 24.67 46.6136 24.9633 46.6136 25.31V28H45.6136ZM53.7227 28.12C53.2427 28.12 52.8194 28.0067 52.4527 27.78C52.0861 27.5467 51.7994 27.2333 51.5927 26.84C51.3927 26.44 51.2927 25.9867 51.2927 25.48C51.2927 24.9667 51.3927 24.5133 51.5927 24.12C51.7994 23.7267 52.0861 23.4167 52.4527 23.19C52.8261 22.9567 53.2527 22.84 53.7327 22.84C54.1261 22.84 54.4727 22.92 54.7727 23.08C55.0727 23.2333 55.3061 23.4533 55.4727 23.74V20.8H56.4727V28H55.5727L55.4727 27.22C55.3727 27.3733 55.2427 27.52 55.0827 27.66C54.9227 27.7933 54.7294 27.9033 54.5027 27.99C54.2761 28.0767 54.0161 28.12 53.7227 28.12ZM53.8827 27.25C54.1961 27.25 54.4727 27.1767 54.7127 27.03C54.9527 26.8833 55.1361 26.6767 55.2627 26.41C55.3961 26.1433 55.4627 25.8333 55.4627 25.48C55.4627 25.1267 55.3961 24.82 55.2627 24.56C55.1361 24.2933 54.9527 24.0867 54.7127 23.94C54.4727 23.7867 54.1961 23.71 53.8827 23.71C53.5827 23.71 53.3127 23.7867 53.0727 23.94C52.8327 24.0867 52.6461 24.2933 52.5127 24.56C52.3794 24.82 52.3127 25.1267 52.3127 25.48C52.3127 25.8333 52.3794 26.1433 52.5127 26.41C52.6461 26.6767 52.8327 26.8833 53.0727 27.03C53.3127 27.1767 53.5827 27.25 53.8827 27.25Z"
                      fill="black"
                    />
                    <path
                      d="M51.04 13C50.5733 13 50.2 12.8633 49.92 12.59C49.6467 12.3167 49.51 11.95 49.51 11.49C49.51 11.0033 49.6533 10.6267 49.94 10.36C50.2333 10.0867 50.6633 9.95 51.23 9.95H51.82V8.85H51.23C50.6633 8.85 50.2333 8.71667 49.94 8.45C49.6533 8.17667 49.51 7.79667 49.51 7.31C49.51 6.85 49.6467 6.48333 49.92 6.21C50.2 5.93667 50.5733 5.8 51.04 5.8C51.4133 5.8 51.7067 5.87 51.92 6.01C52.14 6.15 52.2933 6.33667 52.38 6.57C52.4733 6.80333 52.52 7.06 52.52 7.34V8.17H53.62V7.34C53.62 7.06 53.6633 6.80333 53.75 6.57C53.8433 6.33667 53.9967 6.15 54.21 6.01C54.43 5.87 54.7267 5.8 55.1 5.8C55.5667 5.8 55.9367 5.93667 56.21 6.21C56.49 6.48333 56.63 6.85 56.63 7.31C56.63 7.79667 56.4867 8.17667 56.2 8.45C55.9133 8.71667 55.4833 8.85 54.91 8.85H54.32V9.95H54.91C55.4833 9.95 55.9133 10.0867 56.2 10.36C56.4867 10.6267 56.63 11.0033 56.63 11.49C56.63 11.95 56.49 12.3167 56.21 12.59C55.9367 12.8633 55.5667 13 55.1 13C54.7267 13 54.43 12.93 54.21 12.79C53.9967 12.65 53.8433 12.4633 53.75 12.23C53.6633 11.9967 53.62 11.74 53.62 11.46V10.63H52.52V11.46C52.52 11.74 52.4733 11.9967 52.38 12.23C52.2933 12.4633 52.14 12.65 51.92 12.79C51.7067 12.93 51.4133 13 51.04 13ZM54.32 7.32V8.17H54.91C55.2633 8.17 55.52 8.1 55.68 7.96C55.84 7.81333 55.92 7.59667 55.92 7.31C55.92 7.01667 55.84 6.80667 55.68 6.68C55.5267 6.55333 55.3333 6.49 55.1 6.49C54.84 6.49 54.6433 6.56667 54.51 6.72C54.3833 6.86667 54.32 7.06667 54.32 7.32ZM51.23 8.17H51.82V7.32C51.82 7.06667 51.7533 6.86667 51.62 6.72C51.4933 6.56667 51.3 6.49 51.04 6.49C50.8067 6.49 50.61 6.55333 50.45 6.68C50.2967 6.80667 50.22 7.01667 50.22 7.31C50.22 7.59667 50.3 7.81333 50.46 7.96C50.62 8.1 50.8767 8.17 51.23 8.17ZM52.52 9.95H53.62V8.85H52.52V9.95ZM51.04 12.31C51.3 12.31 51.4933 12.2367 51.62 12.09C51.7533 11.9367 51.82 11.7333 51.82 11.48V10.63H51.23C50.8767 10.63 50.62 10.7033 50.46 10.85C50.3 10.99 50.22 11.2033 50.22 11.49C50.22 11.7833 50.2967 11.9933 50.45 12.12C50.61 12.2467 50.8067 12.31 51.04 12.31ZM54.32 11.48C54.32 11.7333 54.3833 11.9367 54.51 12.09C54.6433 12.2367 54.84 12.31 55.1 12.31C55.3333 12.31 55.5267 12.2467 55.68 12.12C55.84 11.9933 55.92 11.7833 55.92 11.49C55.92 11.2033 55.84 10.99 55.68 10.85C55.52 10.7033 55.2633 10.63 54.91 10.63H54.32V11.48Z"
                      fill="black"
                    />
                  </svg>
                </p>

                <p className="py-[21px] text-xs">OR</p>

                <p className="max-w-[299px] py-2 text-left text-xs font-normal leading-[15.62px]">
                  Ctrl + "-" (on Windows) <br /> continuously to zoom out and
                  adjust to until error disappears.
                </p>
                <p className="flex items-center gap-[11px] text-xs">
                  <svg
                    width="67"
                    height="35"
                    viewBox="0 0 67 35"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <rect width="67" height="35" rx="6" fill="white" />
                    <path
                      d="M26.214 23.168C25.5327 23.168 24.926 23.014 24.394 22.706C23.862 22.3887 23.442 21.9547 23.134 21.404C22.8353 20.8533 22.686 20.214 22.686 19.486C22.686 18.7487 22.8353 18.1047 23.134 17.554C23.442 16.994 23.862 16.56 24.394 16.252C24.926 15.9347 25.5327 15.776 26.214 15.776C27.0727 15.776 27.7913 16 28.37 16.448C28.9487 16.896 29.3173 17.5027 29.476 18.268H28.02C27.9267 17.8573 27.712 17.54 27.376 17.316C27.0493 17.092 26.6573 16.98 26.2 16.98C25.8267 16.98 25.4813 17.078 25.164 17.274C24.8467 17.4607 24.59 17.7407 24.394 18.114C24.2073 18.478 24.114 18.9307 24.114 19.472C24.114 19.8733 24.17 20.2327 24.282 20.55C24.394 20.858 24.5433 21.1193 24.73 21.334C24.926 21.5487 25.15 21.712 25.402 21.824C25.654 21.9267 25.92 21.978 26.2 21.978C26.508 21.978 26.7833 21.9313 27.026 21.838C27.278 21.7353 27.488 21.586 27.656 21.39C27.8333 21.194 27.9547 20.9607 28.02 20.69H29.476C29.3173 21.4367 28.9487 22.0387 28.37 22.496C27.7913 22.944 27.0727 23.168 26.214 23.168ZM33.8757 23C33.4277 23 33.0404 22.93 32.7137 22.79C32.3871 22.65 32.1351 22.4167 31.9577 22.09C31.7804 21.7633 31.6917 21.32 31.6917 20.76V17.134H30.4737V15.944H31.6917L31.8597 14.18H33.0917V15.944H35.0937V17.134H33.0917V20.774C33.0917 21.1753 33.1757 21.4507 33.3437 21.6C33.5117 21.74 33.8011 21.81 34.2117 21.81H35.0237V23H33.8757ZM36.6375 23V15.944H37.8975L38.0235 17.274C38.1822 16.9567 38.3875 16.6907 38.6395 16.476C38.8915 16.252 39.1855 16.0793 39.5215 15.958C39.8669 15.8367 40.2589 15.776 40.6975 15.776V17.26H40.1935C39.9042 17.26 39.6289 17.2973 39.3675 17.372C39.1062 17.4373 38.8729 17.554 38.6675 17.722C38.4715 17.89 38.3175 18.1187 38.2055 18.408C38.0935 18.6973 38.0375 19.0567 38.0375 19.486V23H36.6375ZM41.9833 23V12.92H43.3833V23H41.9833Z"
                      fill="black"
                    />
                  </svg>

                  <span>and</span>
                  <svg
                    width="53"
                    height="35"
                    viewBox="0 0 53 35"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <rect width="53" height="35" rx="6" fill="white" />
                    <rect
                      x="10"
                      y="12"
                      width="12"
                      height="1"
                      rx="0.5"
                      fill="black"
                    />
                    <rect
                      x="22"
                      y="23"
                      width="9"
                      height="1"
                      rx="0.5"
                      fill="black"
                    />
                  </svg>
                </p>
              </div>

              <button className="mb-12 mt-[22px] min-w-[200px] rounded-md bg-white p-3 text-[#3C1356] sm:min-w-[250px]">
                Go Home
              </button>
            </div>
          </div>
          <div className="absolute left-3 top-3 scale-90 sm:left-10 sm:top-10">
            <GetLinkedLogoLight />
          </div>
          <div className="absolute inset-0 hidden h-full w-full bg-transparent lg:block">
            <div className="absolute right-20 top-1/2 z-10 h-[270px] w-[270px]">
              <Image alt="" src={'/assets/cha12.svg'} fill />
            </div>
            <div className="absolute left-20 top-1/2 z-10 h-[300px] w-[270px]">
              <Image alt="" src={'/assets/char11.svg'} fill />
            </div>
            <div className="absolute right-[200px] top-1/3 z-10">
              <RoundCirclePurple />
            </div>
            <div className="absolute right-[300px] top-[40%] z-10">
              <RoundCircleWhite />
            </div>
          </div>
        </>
      ) : (
        <Loader />
      )}
      <AssessmentNotAvailable
        error={errObj}
        isOpen={showAssessmentNotAvalaible}
      />
    </div>
  );
};

export default page;

// QUESTION
// id, question,time
