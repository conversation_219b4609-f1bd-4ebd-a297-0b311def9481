"use client"

import { formatDuration, intervalToDuration } from "date-fns"
import { useAssessmentDetails } from "../../misc/store/assessmentStore"
import CustomVideoPlayer from "@/components/shared/video-player"
import { useEffect, useState, useContext } from "react"
import { cn } from "@/utils"
import { send } from "process"
import { SocketProgressEvent } from "../../misc/utils/websocketUrl"
import { useMQTTTrackingService } from "../../misc/hooks/useMQTTTrackingService"

// Updated assessment criteria
const ASSESSMENT_CRITERIA = [
  "We recommend completing the assessment in a quiet environment for the best experience.",
  "You must use a device with a working webcam",
  "You need a well-lit room",
  "Please have a government-issued ID ready (V<PERSON>'s card, NIN, Driver's license, or International passport)",
  'If your device shuts down or your browser tab closes, you can resume by clicking the "Take Assessment" button in your email',
  "Note: Previously viewed or answered questions will become inaccessible when you resume to maintain assessment integrity",
  "If you encounter technical issues, you can refresh your browser at any time - your answers will be saved",
  "Your responses will be automatically saved when the time expires",
]

// Updated proctoring information
const PROCTORING_INFORMATION = [
  "This is a proctored assessment monitoring for the following violations:",
  "Multiple faces detected",
  "No face detection",
  "Looking away from the screen",
  "Prohibited objects",
  "Tab changes",
  "Window changes",
  "Exiting full-screen mode",
  "Depending on the assessment settings, you may be automatically removed from the assessment after a certain number of violations.",
]

// Prohibited items
const PROHIBITED_ITEMS = ["Phone", "Tablet", "Book", "Paper", "Second screen", "Smartwatch", "Headphones/earphones"]

const AssessmentCriteria = ({ handleNext }: { handleNext: () => void }) => {
  const { assessmentDetails } = useAssessmentDetails()
  const duration = intervalToDuration({
    start: 0,
    end: (assessmentDetails?.assessment.time_limit || 0) * 1000,
  })
  const formatted = formatDuration(duration, {
    format: ["hours", "minutes", "seconds"],
  })

  const { sendMessage: sendSocketMessage } = useMQTTTrackingService();


  const [floatProceed, setFloatProceed] = useState<boolean>(true);
  const handleProceed = async () => {
    await sendSocketMessage(SocketProgressEvent.INSTRUCTIONS_READ)
    handleNext()
  }
  useEffect(() => {
    const mainContent = document.querySelector("#main-content");

    const handleScroll = () => {
      if (!mainContent) return;

      const scrollTop = mainContent.scrollTop;
      const scrollHeight = mainContent.scrollHeight;
      const clientHeight = mainContent.clientHeight;

      const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100;
      console.log(isNearBottom, "TO THE BOTTOM");
      setFloatProceed(!isNearBottom);
    };

    mainContent?.addEventListener('scroll', handleScroll);

    // Clean up
    return () => {
      mainContent?.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <div className="relative flex items-center justify-center px-4 py-10 pt-2 lg:px-16 lg:py-20 lg:pt-10 2xl:px-24 criteria-container" >
      <div className="grid h-full w-full grid-cols-1 lg:grid-cols-2 rounded-2xl bg-white p-5 2xl:w-[80%]">
        <div className="flex h-[100%] flex-col overflow-y-scroll p-5">
          <div className="h-[80px]">
            <p className="py-1 text-2xl font-semibold">Hello {assessmentDetails?.candidate_name} 👋</p>
          </div>
          <div className="h-max overflow-y-scroll">
            <p className="py-3 max-2xl:text-sm">{assessmentDetails?.assessment?.description}</p>
            {assessmentDetails?.assessment?.intro_video_url && (
              <CustomVideoPlayer src={assessmentDetails?.assessment?.intro_video_url} />
            )}
            <CustomVideoPlayer src="https://res.cloudinary.com/dhgp0fmlh/video/upload/v1746692377/Getlinked_demo_fuu3u0.mp4" />
          </div>
          <div className="h-[100px]">
            <p className="py-2 font-medium 2xl:text-lg">
              You have <span className="text-primary">{formatted}</span> to answer{" "}
              <span className="text-primary">{assessmentDetails?.assessment?.total_questions}</span> Questions
            </p>
          </div>
        </div>

        <div className="flex h-[100%] flex-col overflow-hidden p-5">
          <div className="h-full overflow-y-scroll 2xl:p-5">
            <section>
              <h4 className="py-1 font-semibold 2xl:text-xl">Assessment Information</h4>
              <div className="rounded-xl p-2 px-4 text-[#FF5F56] bg-[#FF5F561A] text-xs my-4">
                Close all browser tabs to aid better system performance
              </div>
              <ul className="list-disc space-y-2 p-4 text-sm font-light">
                {ASSESSMENT_CRITERIA.map((criteria, index) => (
                  <li key={index} className="max-2xl:text-sm">
                    {criteria}
                  </li>
                ))}
              </ul>
            </section>

            <section>
              <h4 className="py-1 font-semibold 2xl:text-xl">Proctoring Information</h4>
              <ul className="list-disc space-y-2 p-4 text-sm font-light">
                {PROCTORING_INFORMATION.map((info, index) => (
                  <li key={index} className="max-2xl:text-sm">
                    {info}
                  </li>
                ))}
              </ul>
            </section>

            <section>
              <h4 className="py-1 font-semibold 2xl:text-xl">Prohibited Items</h4>
              <p className="px-4 pt-2 text-sm font-light">
                The following objects are not allowed during the assessment:
              </p>
              <ul className="list-disc space-y-2 p-4 text-sm font-light">
                {PROHIBITED_ITEMS.map((item, index) => (
                  <li key={index} className="max-2xl:text-sm">
                    {item}
                  </li>
                ))}
              </ul>
            </section>
          </div>
          <p className="text-[#019F23] bg-[#30A3651A] text-xs rounded-xl py-2 px-4">
            If you encounter any issues during this assessment, please reach out to us by clicking the WhatsApp chat
            icon or use the support chat option.
          </p>
          <div className={cn("h-[80px] p-5 ", floatProceed ? "fixed bottom-0 right-30 bg-white" : "")}>
            <button className="min-w-[150px] rounded-md bg-primary p-3 text-white" onClick={handleProceed}>
              Proceed
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AssessmentCriteria