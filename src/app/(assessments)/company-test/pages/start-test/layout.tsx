'use client';

import { useRouter } from 'next/navigation';
import React, { useEffect } from 'react';
import { shallow } from 'zustand/shallow';
import { LoadingOverlay } from '@/components/shared';
import { UseGetAssessmentSections } from '../../misc/api';
import {
  useAllAssessmentSections,
  useAssessmentDetails,
  useAssessmentSectionsStore,
  UseCandidateStatusStore,
  useProctoringReport,
} from '../../misc/store';

const layout = ({ children }: { children: React.ReactNode; }) => {
  const router = useRouter();

  const allSections = useAllAssessmentSections();
  const setSection = useAssessmentSectionsStore(state => state.setSection);
  const setCandidateStatus = UseCandidateStatusStore(
    state => state.setCandidateStatus
  );

  const assessmentId = useAssessmentDetails(
    state => state.assessmentDetails.assessment?.id
  );
  const isAssessmentShuffleQuestions = useAssessmentDetails(
    state => state.assessmentDetails.assessment?.is_shuffle
  );

  const { proctoringReportId, resetProctoringReport } = useProctoringReport(
    state => ({
      proctoringReportId: state.proctoringReport.id,
      resetProctoringReport: state.resetProctoringReport,
    }),
    shallow
  );

  const { data: assessmentSections, isLoading: isLoadingAssessment } =
    UseGetAssessmentSections(assessmentId);

  useEffect(() => {
    if (!isLoadingAssessment && !allSections.length && assessmentSections) {
      let sections = assessmentSections;
      if (isAssessmentShuffleQuestions) {
        sections = sections.map((section, index) => {
          const shuffledQuestions = section.question_set.sort(
            () => Math.random() - 0.5
          );
          if (index === 0) {
            setCandidateStatus({
              currentQuestionId: shuffledQuestions[0].id,
            });
          }
          return {
            ...section,
            question_set: shuffledQuestions,
          };
        });
      } else {
        setCandidateStatus({
          currentQuestionId: sections[0].question_set[0].id,
        });
      }

      setSection(sections);
    }
  }, [assessmentSections, isLoadingAssessment, allSections]);
  useEffect(() => {
    if (!assessmentId) {
      // if (proctoringReport.id == "" || !assessmentDetails?.assessment?.id) {
      // resetProctoringReport()
      router.push('/');
    }
  }, [proctoringReportId]);

  return (
    <>
      {isLoadingAssessment && !assessmentSections ? (
        <LoadingOverlay
          isOpen={isLoadingAssessment}
          errorMsg="Assessment Loading, Please wait"
        />
      ) : !!allSections ? (
        <>{children}</>
      ) : (
        <></>
      )}
    </>
  );
};

export default layout;
