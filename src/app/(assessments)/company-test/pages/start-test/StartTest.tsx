'use client';

import React, {
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import { toast } from 'react-toastify';
// import { TestContext } from '../layout';
import DeficientLightWarning from '../../misc/components/DeficientLightWarning';
import FullScreenEscWarning from '../../misc/components/FullScreenEscWarning';
import SubmitTestModal from '../../misc/components/SubmitTestModal';
import 'react-toastify/dist/ReactToastify.min.css';
import { CloseCircle } from 'iconsax-react';
import moment from 'moment';
import { useRouter } from 'next/navigation';
import { useNetworkState, usePageLeave } from 'react-use';
import Webcam from 'react-webcam';
import { useDebouncedCallback } from 'use-debounce';
import { shallow } from 'zustand/shallow';
import { PROCTORING_OPTIONS } from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants';
import { proctorOption } from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import { LoadingOverlay } from '@/components/shared';
import { useBooleanStateControl } from '@/hooks';
import { getDeviceType } from '@/utils/strings';
import { SubmitModalContext } from '../../layout';
import {
  checkBrightness,
  MINIMUM_BRIGHTNESS_LEVEL,
} from '../../misc/api/checkBrightness';
import { useSendCandidateAnswers } from '../../misc/api/sendAnswer';
import {
  TSendProctorReportData,
  useSendProctoringReports,
} from '../../misc/api/sendProctoringReport';
import { useSubmitAssessment } from '../../misc/api/submitAssessment';
import AssessmentInterface from '../../misc/components/AssessmentInterface';
import ExtendedScreenWarning from '../../misc/components/ExtendedScreenWarning';
import ForceSubmitTestModal from '../../misc/components/ForcedTestSubmitModal'; // Ensure this path is correct
import GenericAssessmentWarning from '../../misc/components/GenericAssessmentWarning';
import ProctoringCam from '../../misc/components/ProctoringCam';
import ScreenCaptureError from '../../misc/components/ScreenCaptureError';
import SubmitFeedback from '../../misc/components/SubmitFeedback';
import SuccessModal from '../../misc/components/SuccessModal';
import WindowLeaveWarning from '../../misc/components/WindowLeaveWarningModal';
import { UseCandidateStatusStore } from '../../misc/store';
import { useAssessmentDetails } from '../../misc/store/assessmentStore';
import { useCurrentPageId } from '../../misc/store/currentPageId';
import FlagDatabase from '../../misc/store/flagManager';
import {
  ProctoringReportState,
  useProctoringReport,
} from '../../misc/store/proctoringReportStore';
import { Flag, Flags, InitializedModels } from '../../misc/types';
import { useWindowChange } from '../../misc/hooks/custom-hooks';
import { useFullScreen, } from '../../misc/hooks/fullscreen-hooks';
import { NoiseMeter } from '../../misc/utils/noiseMeter';
import { detectDisplaySurface, getSuffix } from '../../misc/utils/prepTest';
import { Button } from '@/components/shared';
import useWebsocket from '../../misc/components/hooks/useWebsocket';
import { getWebSocketUrl } from '../../misc/utils/websocketUrl';

interface StartTestProps {
  isModelsLoaded: boolean;
  faceApiReady: boolean;
  models: InitializedModels | undefined;
  // referenceDescriptor: any[];
}

const StartTest = () => {
  const router = useRouter();
  const [didNextHappen, setDidNextHappen] = useState(true); //to determine whether to skip forward or backwards when skipping an answered question

  const {
    isInSession,
    isReturning,
    isOnline,
    setCandidateStatus,
    initializeTimer,
  } = UseCandidateStatusStore(
    state => ({
      isInSession: state.candidateStatus.isInSession,
      isReturning: state.candidateStatus.isReturning,
      isOnline: state.candidateStatus.isOnline,
      setCandidateStatus: state.setCandidateStatus,
      initializeTimer: state.initializeTimer,
    }),
    shallow
  );
  const [forcedSubmission, setForcedSubmission] = useState(false);
  // const [isSubmitModalOpen, setIsSubmitModalOpen] = useState(false);.
  const { isSubmitModalOpen, setIsSubmitModalOpen } = useContext(SubmitModalContext);

  //   UseCandidateStatusStore();,
  const {
    // reportFlag,
    proctoringReport,
    setProctoringReport,
    // resetProctoringFlags,
    resetProctoringReport,
    // removeFlags,
  } = useProctoringReport(
    state => ({
      // reportFlag: state.reportFlag,
      proctoringReport: state.proctoringReport,
      setProctoringReport: state.setProctoringReport,
      // resetProctoringFlags: state.resetProctoringFlags,
      resetProctoringReport: state.resetProctoringReport,
      // removeFlags: state.removeFlags,
    }),
    shallow
  );
  const { assessmentDetails } = useAssessmentDetails();

  const flagManager = useRef<FlagDatabase>(new FlagDatabase());

  useEffect(() => {
    console.log(proctoringReport.id, 'Proctoring report ide before pushing');
    if (proctoringReport.id == 'default_id' || proctoringReport.id == '') {
      router.push('/');
    }
  }, [proctoringReport.id]);

  const fullRef = useRef<any>(null);
  const {
    state: isAssessmentActivated,
    setTrue: activateAssessment,
    setFalse: deactivateAssessment,
  } = useBooleanStateControl();

  // const [is_cam_ready, setIsCamReady] = useState(false);
  const [manualLoading, setManualLoading] = useState(false);
  const [webcamRef, setWebcamRef] = useState<Webcam>();
  const [canvas_ref, setCanvasRef] = useState<HTMLCanvasElement>();
  const screenCaptureStream = useRef<MediaStream | null>(null);

  /* 
  ========================================
  ============ NOISE CHECKING ============
  ======================================== 
  */
  const [noiseCheckInitialized, setNoiseCheckInitialized] =
    useState<boolean>(false);
  const [noiseMeter, setNoiseMeter] = useState<NoiseMeter | null>(null);
  const [noiseHistory, setNoiseHistory] = useState<number[]>([]);

  const handleNoiseCheck = useCallback(() => {
    if (!noiseCheckInitialized) return;
    if (!noiseMeter) return;
    const noiseResult = noiseMeter.measure();
    // console.log(noiseResult.db.toFixed(2), 'NOISE DECIBAL');

    if (noiseResult.db != -Infinity) {
      //console.log(noiseResult);
      setNoiseHistory(prevState => {
        let presentNoiseHistory = [...prevState];
        presentNoiseHistory.push(parseFloat(noiseResult.db.toFixed(2)));
        // console.log(presentNoiseHistory, 'NOISE HISTORY');

        if (presentNoiseHistory.length > 10) {
          let sum = 0;
          presentNoiseHistory.forEach(n => {
            sum += n;
          });
          // console.log(sum, 'SUM');
          const avg = sum / presentNoiseHistory.length;
          // console.log(avg, 'NOISE AVERAGE');

          if (avg > -30 && avg < 0) {
            openNoisyEnvironmentDialog();
          }
          return [];
        }
        return presentNoiseHistory;
      });
    }
  }, [noiseCheckInitialized, noiseMeter]);

  const initNoiseMeter = useCallback(async () => {
    if (!isAssessmentActivated) return;
    const preNoiceMeter = new NoiseMeter();
    if (await preNoiceMeter.initialize()) {
      setNoiseCheckInitialized(true);
      setNoiseMeter(preNoiceMeter);
    }
  }, [isAssessmentActivated]);

  // Initialize noise checker
  useEffect(() => {
    let interval = setInterval(handleNoiseCheck, 400);
    return () => {
      noiseMeter?.stop();
      clearInterval(interval);
    };
  }, [noiseMeter, noiseCheckInitialized]);

  // useEffect(() => {
  //   let Sinterval = setInterval(handleNoiseAveraging, 800);
  //   return () => clearInterval(Sinterval);
  // });

  // initialize Noise Meter
  useEffect(() => {
    // initNoiseMeter();
  }, [noiseCheckInitialized, isAssessmentActivated]);

  const registerCheating = useDebouncedCallback(async (flag: Flag) => {
    let flag_value = '';
    switch (flag) {
      case 'paste': {
        flag_value = 'Paste';
        toast.warning('Paste Detected');
        break;
      }
    }
    const screenshot = '';
    // const screenshot = webcamRef?.getScreenshot() ?? '';
    const pageScreenshot = await captureScreen();

    flagManager.current.reportFlag(screenshot, pageScreenshot);
  }, 1000);

  const [escFullScreenAttemptsCount, setEscFullScreenAttemptsCount] = useState(
    proctoringReport?.full_screen_tolerance_used || 0
  );
  const [windowLeaveCount, setWindowLeaveCount] = useState(
    proctoringReport?.window_change_tolerance_used || 0
  );
  const [bulkToleranceLevel, setBulkToleranceLevel] = useState<number>(
    proctoringReport?.bulk_proctor_used || 0
  );
  const [isWindowLeaveModalOpen, setIsWindowLeaveModalOpen] = useState(false);

  const {
    state: isEscFullscreenDialogOpen,
    setTrue: openEscFullscreenDialog,
    setFalse: closeEscFullscreenOpenDialog,
  } = useBooleanStateControl();

  const {
    state: isDeficientLightDialogOpen,
    setTrue: openIsDeficientLightDialog,
    setFalse: closeIsDeficientLightDialog,
  } = useBooleanStateControl();

  const {
    state: isForcedSumbitionDialogOpen,
    setTrue: openForcedSumbittionDialog,
    setFalse: closeForcedSumbittionDialog,
  } = useBooleanStateControl();

  const {
    state: isScreenExtendedDialogOpen,
    setTrue: openScreenExtendedDialog,
    setFalse: closeScreenExtendedDialog,
  } = useBooleanStateControl();

  const {
    state: isNoisyEnvironmentDialogOpen,
    setTrue: openNoisyEnvironmentDialog,
    setFalse: closeNoisyEnvironmentDialog,
  } = useBooleanStateControl();

  const {
    state: isGenericWarningOpen,
    setTrue: openGenericWarning,
    setFalse: closeGenericWarning,
  } = useBooleanStateControl();

  const {
    state: isScreenCaptureErrorOpen,
    setTrue: openScreenCaptureError,
    setFalse: closeScreenCaptureError,
  } = useBooleanStateControl();

  const {
    state: isSuccessModalOpen,
    setTrue: openSuccessModal,
    setFalse: closeSuccessModal,
  } = useBooleanStateControl();

  // window.openSuccessModal = openSuccessModal;

  const {
    state: isFeedbackModalOpen,
    setTrue: openFeedbackModal,
    setFalse: closeFeedbackModal,
  } = useBooleanStateControl();

  const toleranceForWindowLeave =
    assessmentDetails?.assessment?.window_change_tolerance_level;
  const toleranceForFullscreenLeave =
    assessmentDetails?.assessment?.full_screen_tolerance_level;

  const currentPageId = useCurrentPageId((state: any) => state.data);
  const setCurrentPageId = useCurrentPageId(
    (state: any) => state.setCurrentPageId
  );

  var networkState = useNetworkState();

  const { mutate: sendAnswer, isLoading: isAnswering } =
    useSendCandidateAnswers();

  const { mutate: runSubmitAssessment, isLoading: isSubmitting } =
    useSubmitAssessment();
  const { mutate: sendReport, isLoading: reportLoading } =
    useSendProctoringReports();

  const getData = (): TSendProctorReportData => ({
    candidate_email: assessmentDetails?.candidate_email,
    assessment: assessmentDetails?.assessment?.id,
    device_used: getDeviceType(),
    webcam_enabled: true,
    location: proctoringReport.location,
    mouse_out_of_window: proctoringReport.mouse_out_of_window,
    tab_switched: proctoringReport.tab_switched,
    id: proctoringReport.id,
    // full_screen_tolerance_used: escFullScreenAttemptsCount,
    // window_change_tolerance_used: windowLeaveCount,
    // was_forced_submission_full_screen:
    //   escFullScreenAttemptsCount >= toleranceForFullscreenLeave,
    // was_forced_submission_window_change:
    //   windowLeaveCount >= toleranceForWindowLeave,
    // was_forced_submission_bulk_proctor:
    //   proctoringReport.was_forced_submission_bulk_proctor,

    was_forced_submission: proctoringReport.was_forced_submission || forcedSubmission,

    // bulk_proctor_used: proctoringReport.bulk_proctor_used,
    // bulk_proctor_option: proctoringReport.bulk_proctor_option,
  });
  const submitAssessment = () => {
    if (
      !assessmentDetails?.candidate_email ||
      !assessmentDetails?.assessment?.id
    ) {
      toast.error('Candidate email or assessment ID is missing');
      return;
    }
    const payload = getData();

    deactivateAssessment();
    console.log(proctoringReport, "PROCTORING REPORT");
    console.log(payload, "PAYLOAD")
    console.log("SUBMITTTING")
    Promise.all([
      new Promise((resolve, reject) => {
        sendReport(payload, {
          onSuccess: () => {
            resolve(true);
          },
          onError: (error: any) => {
            reject(false);
            toast.error(error.message as string);
          },
        });
      }),
      new Promise((resolve, reject) => {
        runSubmitAssessment(
          {
            candidate_email: assessmentDetails?.candidate_email,
            invite_id: assessmentDetails?.invite_id,
            assessment_id: assessmentDetails.assessment.id,
            result_id: assessmentDetails.result_id,
            candidate_name: assessmentDetails.candidate_name,
          },
          {
            onSuccess: data => {
              // console.log(data, 'RESOPOND DATA');
              // resetProctoringFlags();
              resetProctoringReport();
              localStorage.clear();
              sessionStorage.clear();
              closeScreenShareCapture();
              deactivateAssessment();

              if (data.status === 200) {
                openForcedSumbittionDialog();
                // router.push('/company-test/submitted');
              } else {
                toast.error(data.data.error.message);
              }
              resolve(true);
            },
            onError: (error: any) => {
              //  console.log(error, 'RESOPOND DATA');
              activateAssessment();

              toast.error(error.message as string);
              reject(false);
            },
          }
        );
      }),
    ]);
  };

  // UPLOAD WORKER
  const uploadWorker = useRef<Worker>();
  useEffect(() => {
    const url = new URL('./worker.js', import.meta.url);

    uploadWorker.current = new Worker(url, {
      type: 'module',
    });
    // console.log(proctoringReport);

    uploadWorker.current.postMessage({ type: 'id', data: proctoringReport.id });
    uploadWorker.current.postMessage({
      type: 'proctorURL',
      data: process.env.NEXT_PUBLIC_PROCTOR_URL,
    });
    uploadWorker.current.postMessage({
      type: 'env',
      data: process.env.NEXT_PUBLIC_ENV,
    });

    uploadWorker.current.onerror = e => {
      // console.log(e, 'WORKER ERROR');
    };

    uploadWorker.current.onmessageerror = e => {
      // console.log(e, 'WORKER MESSAGE ERROR');
    };

    uploadWorker.current.onmessage = e => {
      // console.log(e, 'MESSAGE FROM WORKER');
      let payload = e.data;
      if (payload.type === 'LOG') {
        sessionStorage.setItem('LAST_SUCCESSFUL_PROCTOR', payload.data);
        sessionStorage.removeItem('LAST_ERROR_PROCTOR');
      } else if (payload.type === 'LOG_ERROR') {
        sessionStorage.setItem('LAST_ERROR_PROCTOR', payload.data);
      } else if (payload.type === 'RESPONSE_PAYLOAD') {
        let response = payload.data;
        for (const [key, value] of Object.entries(response)) {
          // console.log(`Key: ${key}, Value: ${value}`);
          if (key != 'time' && value) {
            reportCheating(key as Flag, response.time);
          }
        }
      }
    };

    return () => {
      uploadWorker.current?.terminate();
    };
  }, [proctoringReport]);



  /* === === === === === ===
    SOCKET CONNECTION
  === === === === === === */


  interface SocketProctorResponse {
    kick: boolean;
    is_bulk?: boolean;
    message: string;
    violation_count: number;
    remaining: number
    violation_type: proctorOption | null
  }


  const handleSocketMessage = useCallback((data: SocketProctorResponse) => {

    // console.log('WebSocket message received:', data);
    // let data = JSON.parse(event.data) as SocketProctorResponse;
    console.log(data, "WEBSOCKET DATA")
    if (data.violation_type == null) return
    if (data.kick) {
      setForcedSubmission(true);
      submitAssessment();
      return;
    }

    if (data.is_bulk) {
      openGenericWarning();
      return;
    }

    if (data.violation_type == proctorOption.fullScreenExit) {
      openEscFullscreenDialog();
      return
    }

    if (data.violation_type == proctorOption.windowChange) {
      setIsWindowLeaveModalOpen(true);
    }
  }, [openEscFullscreenDialog, openGenericWarning, setIsWindowLeaveModalOpen, submitAssessment]);

  const websocketUrl = getWebSocketUrl(
    assessmentDetails?.assessment.id || '',
    proctoringReport.id
  );

  const { socket, connect: socketConnect } = useWebsocket({
    url: websocketUrl,
    onMessage: handleSocketMessage,
    maxRetries: 5,
    retryDelay: 1000,
    // onStart: true
  })
  // useEffect(() => {
  //   // if (!isAssessmentActivated) return;

  //   const ws = new WebSocket(websocketUrl);
  //   ws.onopen = () => {
  //     console.log('WebSocket connection opened OPEN EFFECT');
  //   };
  //   ws.onclose = () => {
  //     console.log('WebSocket connection closed EFFECT');
  //   };
  //   ws.onerror = (error) => {
  //     console.error('WebSocket error EFFECT:', error);
  //   };

  //   // socketConnect()
  //   // return () => {
  //   //   socket?.close();
  //   // };
  // }, [isAssessmentActivated]);


  useEffect(() => {
    if (!isAssessmentActivated) return;

    socketConnect()
    return () => {
      socket?.close();
    };
  }, [isAssessmentActivated]);

  // @ts-ignore
  // window.handleProctorCheck = handleProctorCheck;
  // let proctorSendInterval: ReturnType<typeof setInterval>;
  // const runInterval = async () => {
  //   if (proctorSendInterval) {
  //     clearInterval(proctorSendInterval);
  //   }
  //   await handleSendProctorInterval();
  //   proctorSendInterval = setInterval(runInterval, 10000);
  // };

  // useEffect(() => {
  //   runInterval();
  //   return () => {
  //     clearInterval(proctorSendInterval);
  //   };
  // }, [isAssessmentActivated]);

  // Check for tab switching

  usePageLeave(() => {
    if (proctoringReport.mouse_out_of_window === false) {
      setProctoringReport({ ...proctoringReport, mouse_out_of_window: true });
    }
    // setIsWindowLeaveModalOpen(true);å
  });

  /*
=============================== 
=== CHECKING FOR BRIGHTNESS ===
=============================== 
*/
  const handleBrightnessCheck = useCallback(() => {
    if (isDeficientLightDialogOpen || !isAssessmentActivated) return;
    if (!canvas_ref || !webcamRef?.video) return;
    let brightnessLevel = checkBrightness(canvas_ref, webcamRef.video);
    // console.log(brightnessLevel, 'BRIGHTNESS LEVEL');
    if (brightnessLevel < MINIMUM_BRIGHTNESS_LEVEL) {
      openIsDeficientLightDialog();
    }
  }, [
    isDeficientLightDialogOpen,
    isAssessmentActivated,
    canvas_ref,
    webcamRef,
  ]);

  useEffect(() => {
    if (isDeficientLightDialogOpen) return;

    let interval = setInterval(handleBrightnessCheck, 600);
    return () => clearInterval(interval);
  }, [isDeficientLightDialogOpen, canvas_ref, webcamRef]);

  const [noOfSelected, setNoOfSelected] = useState(0);

  // useEffect(() => {
  //   if (
  //     questionsTest &&
  //     previousAnsweredQuestions.includes(questionsTest[currentPageId]?.id)
  //   ) {
  //     didNextHappen
  //       ? setCurrentPageId(currentPageId + 1)
  //       : setCurrentPageId(currentPageId - 1);
  //   }
  // }, [currentPageId]);

  const [is_widow_visible, setIsWindowVisible] = useState(true);
  const [is_screen_extended, setIsScreenExtended] = useState(false);
  useEffect(() => {
    if (assessmentDetails?.assessment?.is_restrict_tab_change) {
      document.onvisibilitychange = () => {
        setIsWindowVisible(document.visibilityState === 'visible');
      };
    }


    function resizeHandler() {
      if (assessmentDetails?.assessment?.is_stop_screen_sharing) {
        setIsScreenExtended(getIsExtendedDisplay());
      }
    }
    document.addEventListener('resize', resizeHandler);
    return () => {
      document.removeEventListener('resize', resizeHandler);
    };
  }, []);

  const getIsExtendedDisplay = (): boolean => {
    const primaryWidth = window.screen.availWidth;
    const primaryHeight = window.screen.availHeight;
    const totalWidth = window.screen.width;
    const totalHeight = window.screen.height;

    // console.log(primaryWidth, primaryHeight);
    // console.log(totalWidth, totalHeight);

    // If the total width or height is significantly greater than the primary width or height,
    // it's likely an extended display
    return totalWidth > primaryWidth * 1.2 || totalHeight > primaryHeight * 1.2;
  };

  // === === === WINDOW CHANGE === === ===
  useEffect(() => {
    // console.log("REGISTERING EVENTS");

    let wasFocused = document.hasFocus();
    //  console.log('Initial focus state:', wasFocused);

    const handleFocus = () => {
      console.log("FOCUS EVENT FIRED");
      wasFocused = true;
      // Your focus logic here

      // using focus now because it is more easily trusted
      // i can trust a focus event and i also know focus
      //events would always fire after a blur
    };

    const handleBlur = () => {
      console.log('BLUR EVENT FIRED');
      wasFocused = false;
      handleWindowChangeViolation();

      // Check for Tab/window switching in the proctoring
      if (proctoringReport.tab_switched === false) {
        setProctoringReport({
          ...proctoringReport,
          tab_switched: true,
        });
      }
    };

    window.onfocus = handleFocus;
    window.onblur = handleBlur;

    return () => {
      window.onfocus = null;
      window.onblur = null;
    };
  }, [isAssessmentActivated, toleranceForWindowLeave, windowLeaveCount, assessmentDetails, proctoringReport]);


  const handleBulkProctorCheck = useCallback(
    (localProctorOption: proctorOption) => {


      socket?.send(JSON.stringify({
        violation_type: localProctorOption
      }));


      //   if (!assessmentDetails.assessment.bulk_tolerance_setup?.options.length) {
      //     return;
      //   }

      //   // Find this proctor option in selected bulk proctor
      //   let checkProctorOption =
      //     assessmentDetails.assessment.bulk_tolerance_setup?.options.find(
      //       o => o.option === localProctorOption.toString()
      //     );

      //   if (!checkProctorOption) {
      //     return;
      //   }

      //   // Use functional updates to ensure we're working with the latest state
      //   setBulkToleranceLevel(prevLevel => {
      //     const newLevel = prevLevel + 1;
      //     const currentBulkProctorOption = [
      //       ...proctoringReport.bulk_proctor_option,
      //     ];
      //     const proctorOptionIndex = currentBulkProctorOption.findIndex(
      //       bP => bP.key === localProctorOption
      //     );

      //     if (proctorOptionIndex < 0) {
      //       return prevLevel;
      //     }

      //     currentBulkProctorOption[proctorOptionIndex].value =
      //       currentBulkProctorOption[proctorOptionIndex].value + 1;

      //     console.log(
      //       "BULK TOLERANCE CHECK:",
      //       newLevel,
      //       assessmentDetails.assessment.bulk_tolerance_setup?.combined_tolerance
      //     );

      //     // Check if we need to force submission
      //     const combinedTolerance = assessmentDetails.assessment.bulk_tolerance_setup?.combined_tolerance || 0;

      //     if (combinedTolerance > 0 && newLevel >= combinedTolerance) {
      //       console.log("PROCTORING FORCED SUBMISSION - Bulk tolerance exceeded");

      //       // Set both the proctoring report and our separate state
      //       //@ts-ignore
      //       setProctoringReport(prevReport => ({
      //         ...prevReport,
      //         bulk_proctor_used: newLevel,
      //         bulk_proctor_option: currentBulkProctorOption,
      //         was_forced_submission: true
      //       }));

      //       // Set our separate state to trigger the useEffect
      //       setForcedSubmission(true);
      //     } else {
      //       if (
      //         localProctorOption !== proctorOption.multiFace &&
      //         localProctorOption !== proctorOption.illegalObject
      //       ) {
      //         openGenericWarning();
      //       }
      //       //@ts-ignore
      //       setProctoringReport(prevReport => ({
      //         ...prevReport,
      //         bulk_proctor_used: newLevel,
      //         bulk_proctor_option: currentBulkProctorOption
      //       }));
      //     }

      //     return newLevel;
      //   });
    },
    [assessmentDetails, proctoringReport, openGenericWarning, socket]
  );


  const handleWindowChangeViolation = useCallback(() => {
    console.log("WINDOW CHANGE EVENT CALLED");
    if (!isAssessmentActivated) return;
    socket?.send(JSON.stringify({
      violation_type: proctorOption.windowChange
    }));
    // // For individual proctor check
    // if (toleranceForWindowLeave !== 0) {
    //   // Check if we've reached the limit
    //   if (windowLeaveCount + 1 >= toleranceForWindowLeave) {
    //     // Set both the proctoring report and our separate state
    //     //@ts-ignore
    //     setProctoringReport(prevReport => ({
    //       ...prevReport,
    //       was_forced_submission: true,
    //       window_change_tolerance_used: windowLeaveCount + 1
    //     }));

    //     // Set our separate state to trigger the useEffect
    //     setForcedSubmission(true);
    //   } else {
    //     // Increment the counter
    //     setWindowLeaveCount(prevCount => {
    //       const newCount = prevCount + 1;
    //       // Show warning if not using bulk tolerance
    //       if (
    //         !assessmentDetails.assessment.bulk_tolerance_setup ||
    //         assessmentDetails.assessment.bulk_tolerance_setup?.combined_tolerance == 0
    //       ) {
    //         setIsWindowLeaveModalOpen(true);
    //       }
    //       return newCount;
    //     });
    //   }
    // }

    // // For bulk tolerance recording
    // if (
    //   assessmentDetails.assessment.bulk_tolerance_setup &&
    //   assessmentDetails.assessment.bulk_tolerance_setup.combined_tolerance != 0
    // ) {
    //   // This will update bulkToleranceLevel and potentially set was_forced_submission
    //   handleBulkProctorCheck(proctorOption.windowChange);
    // }
  }, [isAssessmentActivated, toleranceForWindowLeave, windowLeaveCount, assessmentDetails, handleBulkProctorCheck]);

  // === === === WINDOW CHANGE END === === ===

  // === === === === === === === === === ===
  // === === === FULLSCREEN CHANGE === === ===
  // === === === === === === === === === === 
  const [fullscreenIsDenied, setFullscreenIsDenied] = useState<boolean>(false);
  const attemtFullscreeRetryTimeout = useRef<NodeJS.Timeout | null>(null);
  const fullscreenRetryCount = useRef<number>(0);
  const enterFullscreen = useCallback(async (
    element: Element = document.documentElement
  ): Promise<boolean> => {
    // console.log("ENTERING FULLSCREEN")
    try {
      if (element.requestFullscreen) {
        await element.requestFullscreen();
      } else if ((element as any).webkitRequestFullscreen) {
        await (element as any).webkitRequestFullscreen();
      } else if ((element as any).mozRequestFullScreen) {
        await (element as any).mozRequestFullScreen();
      } else if ((element as any).msRequestFullscreen) {
        await (element as any).msRequestFullscreen();
      }
      setFullscreenIsDenied(false);
      console.log("Fullscreen entered successfully");
      // attemptEnterFullscreen();
      return true;
    } catch (error: any) {

      // if (fullscreenRetryCount.current >= 3) {
      //   setFullscreenIsDenied(true);
      //   fullscreenRetryCount.current = 0;
      //   return false;
      // }
      if (fullscreenRetryCount.current == 0) {
        attemptEnterFullscreen();
      }

      // alert("I TRIED FULL SCREEN BUT IT WAS REJECTED")
      console.error('Fullscreen denied:', error);
      if (error?.name === 'NotAllowedError') {
        return false;
      }
      return false;
    }
  }, []);

  //@ts-ignore
  // window.enterFullScreen = enterFullScreen;
  useEffect(() => {
    document.addEventListener('fullscreenchange', handleFullScreenEventChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullScreenEventChange);
    };
  }, [isAssessmentActivated]);
  // Fullscreen Event Change Handler
  const handleFullScreenEventChange = (e: any) => {
    // console.log(e, "EVENT")
    // console.log(getCurrentFullscreenElement())
    // console.log(isAssessmentActivated)

    if (getCurrentFullscreenElement() == null && isAssessmentActivated) {
      attemptEnterFullscreen();
      handleFullScreenAction();
    }
  }

  // Get Current Fullscreen Element
  const getCurrentFullscreenElement = () =>
    document.fullscreenElement ||
    (document as any).webkitFullscreenElement ||
    (document as any).mozFullScreenElement ||
    (document as any).msFullscreenElement ||
    null;

  // A Method and Count to handle fullscreen retry
  const attemptEnterFullscreen = useCallback(() => {
    if (fullscreenIsDenied) return;
    if (fullscreenRetryCount.current >= 3) {
      setFullscreenIsDenied(true);
      fullscreenRetryCount.current = 0;
      return
    }
    // console.log("FULLSCREEN ATTEMPTTT");

    enterFullscreen();
    fullscreenRetryCount.current = fullscreenRetryCount.current + 1;
    attemtFullscreeRetryTimeout.current = setTimeout(() => {
      const currentIsFullscreen = getCurrentFullscreenElement();
      //    console.log(currentIsFullscreen, "IS CURENTLY FUKKLSCREEN")
      if (!currentIsFullscreen && !fullscreenIsDenied) {
        attemptEnterFullscreen();
      }
    }, 500);
  }, [attemtFullscreeRetryTimeout, fullscreenIsDenied]);


  const handleFullScreenAction = useCallback(() => {
    if (!isAssessmentActivated) return;
    socket?.send(JSON.stringify({
      violation_type: proctorOption.fullScreenExit
    }));
    // if (toleranceForFullscreenLeave != 0) {
    //   if (escFullScreenAttemptsCount >= toleranceForFullscreenLeave) {
    //     setProctoringReport({
    //       ...proctoringReport,
    //       was_forced_submission: true,
    //     });
    //     // forced subnission based on proctor report
    //     // has been moved to a useEffect for better handling
    //   } else {
    //     setEscFullScreenAttemptsCount(prevCount => prevCount + 1);
    //   }
    // }

    // if (
    //   assessmentDetails.assessment.bulk_tolerance_setup &&
    //   assessmentDetails.assessment.bulk_tolerance_setup.combined_tolerance !=
    //   0
    // ) {
    //   let findFullScreen =
    //     assessmentDetails.assessment.bulk_tolerance_setup.options.find(
    //       o => o.option == proctorOption.fullScreenExit.toString()
    //     );

    //   if (!findFullScreen) {
    //     openEscFullscreenDialog();
    //   }

    //   handleBulkProctorCheck(proctorOption.fullScreenExit);
    // } else {
    //   openEscFullscreenDialog();
    // }

  }, [isAssessmentActivated, toleranceForFullscreenLeave, escFullScreenAttemptsCount, assessmentDetails]);

  // === === === FULLSCREEN CHANGE END === === ===

  //  extended display
  useEffect(() => {
    if (isAssessmentActivated) {
      if (getIsExtendedDisplay()) {
        openScreenExtendedDialog();

      } else {
        // console.log('No extended display detected other');
        closeScreenExtendedDialog();
      }
    }
    // }, [isAssessmentActivated, is_screen_extended]);
  });


  // === === === === === ===
  // HANDLE BULK PROCTORING
  // === === === === === ===
  useEffect(() => {
    setProctoringReport({
      ...proctoringReport,
      full_screen_tolerance_used: escFullScreenAttemptsCount,
      window_change_tolerance_used: windowLeaveCount,
      bulk_proctor_used: bulkToleranceLevel,
    });
    // console.log(escFullScreenAttemptsCount, 'ESC PROTECTED');
  }, [escFullScreenAttemptsCount, windowLeaveCount, bulkToleranceLevel]);

  // a check to force submission of of assessment when proctoring report is forced
  useEffect(() => {
    console.log("FORCED SUBMISSION STATE CHANGED:", forcedSubmission);
    if (forcedSubmission || proctoringReport.was_forced_submission) {
      console.log("SUBMITTING ASSESSMENT DUE TO FORCED SUBMISSION");
      submitAssessment();
    }
  }, [proctoringReport, forcedSubmission]);


  const {
    state: isFlagOpen,
    setTrue: openFlagWarning,
    setFalse: closeFlagWarning,
  } = useBooleanStateControl();
  const [flagMessage, setFlagMessage] = useState<string>('');

  const reportCheating = useCallback(
    (flag: Flag, time: string) => {
      let toastMessage = '';
      let localProctorOption = '';
      switch (flag) {
        case 'MultiFace': {
          localProctorOption = proctorOption.multiFace;
          toastMessage =
            'You violated the guideline of not having more than 1 person in view at [time] ';
          break;
        }
        case 'NoFace': {
          toastMessage =
            'Your face was not detected or went out of view at [time], which violates the proctoring guidelines.';
          break;
        }
        case 'illegalObject': {
          localProctorOption = proctorOption.illegalObject;

          toastMessage =
            'An illegal object was detected in view at [time], which violates the proctoring guidelines. ';
          break;
        }
        case 'lookingAway': {
          toastMessage = 'You Were Looking Away from the Assessment';
          break;
        }
      }
      // console.log(time, 'TIME');
      // console.log(moment(time).format('LT'), 'TIME');
      toastMessage = toastMessage.replace(
        '[time]',
        moment(time, 'DD-MM-YYYY HH:mm:ss').format('hh:mmA')
      );

      if (assessmentDetails.assessment.bulk_tolerance_setup?.options.length) {
        let checkProctorOption =
          assessmentDetails.assessment.bulk_tolerance_setup?.options.find(
            o => o.option === localProctorOption.toString()
          );
        if (checkProctorOption) {
          toastMessage += `Please note, upon the ${assessmentDetails.assessment.bulk_tolerance_setup.combined_tolerance
            }${getSuffix(
              assessmentDetails.assessment.bulk_tolerance_setup.combined_tolerance
            )} violation of any proctoring guideline, your assessment will be automatically submitted.`;
        }
      }
      // Find this proctor option in selected bulk proctor

      setFlagMessage(toastMessage);
      openFlagWarning();
      setTimeout(() => {
        closeFlagWarning();
      }, 10000);
      // Deal with HAndlw bulk proctor after recording flag
      if (flag == 'MultiFace' || flag == 'illegalObject') {
        socket?.send(JSON.stringify({
          violation_type: localProctorOption
        }));
      }

      // switch (flag) {
      //   case 'MultiFace': {
      //     handleBulkProctorCheck(proctorOption.multiFace);
      //   }
      //   case 'illegalObject': {
      //     handleBulkProctorCheck(proctorOption.illegalObject);
      //   }
      // }
    },
    [socket]
  );

  /* 
  ======================================
    SCREEN CAPTURE
  =======================================
  */

  const closeScreenCapture = () => {
    closeScreenCaptureError();
    screenCapturePermission();
  };

  const closeScreenShareCapture = useCallback(() => {
    // console.log(screenCaptureStream, 'SCREEN CAPTURE BEFORE OUT');
    if (screenCaptureStream && screenCaptureStream.current) {
      screenCaptureStream.current.getTracks().forEach(track => track.stop());
    }
  }, [screenCaptureStream]);

  const screenCapturePermission = async () => {
    try {
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: { displaySurface: 'monitor' },
      });
      //window.openStream = stream;
      const videoTrack = stream.getVideoTracks()[0];
      if (!videoTrack) {
        return false;
      }

      const displaySurface = detectDisplaySurface(stream);
      if (displaySurface !== 'monitor') {
        openScreenCaptureError();
        stream.getTracks().forEach(s => s.stop());
        return false;
      }

      screenCaptureStream.current = stream;
      videoTrack.addEventListener('ended', () => {
        openScreenCaptureError();
      });
      return true;
    } catch (error) {
      console.error(error);
      openScreenCaptureError();
      return false;
    }
  };

  const captureScreen = useCallback(async () => {
    if (screenCaptureStream && screenCaptureStream.current) {
      let video = document.createElement('video');
      video.srcObject = screenCaptureStream.current;

      await new Promise(resolve => {
        video.onloadedmetadata = () => {
          video.play();
          resolve(true);
        };
      });
      const canvas = document.createElement('canvas');
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      let ctx = canvas.getContext('2d');
      ctx?.drawImage(video, 0, 0, canvas.width, canvas.height);

      const imageDataUrl = canvas.toDataURL('image/png');

      return imageDataUrl;
    }
    return '';
  }, [screenCaptureStream]);

  const openSuccess = useCallback(() => {
    setIsSubmitModalOpen(false);
    openSuccessModal();
  }, []);

  const closeSuccess = useCallback(() => {
    closeSuccessModal();
    setManualLoading(true);
    cleanUp();
  }, []);
  const openFeedback = useCallback(() => {
    closeSuccessModal();
    openFeedbackModal();
  }, []);
  const closeFeedback = useCallback(() => {
    closeFeedbackModal();
    setManualLoading(true);
    cleanUp();
  }, []);

  // On the 3rd violation of any proctoring guideline, your assessment will be automatically submitted.

  const cleanUp = () => {
    resetProctoringReport();
    localStorage.clear();
    sessionStorage.clear();
    flagManager.current.cleanDatabase();
    sessionStorage.removeItem('LAST_SUCCESSFUL_PROCTOR');
    sessionStorage.removeItem('LAST_ERROR_PROCTOR');
    closeScreenShareCapture();
    router.replace('/company-test/submitted');
  };

  // ================================
  // ===  PREVENT COPY AND PASTE  ===
  // ================================

  // Updated useEffect for copy/paste prevention
  useEffect(() => {
    // Prevent copy-paste
    const handleCopyPaste = (e: ClipboardEvent) => {
      e.preventDefault();
      e.stopPropagation(); // Added stopPropagation to fully block the event
      alert('Copy-paste is disabled on this page!');
    };

    document.addEventListener('copy', handleCopyPaste);
    document.addEventListener('paste', handleCopyPaste);
    document.addEventListener('cut', handleCopyPaste);

    // Prevent opening developer tools via common shortcuts
    const handleDevToolsShortcut = (e: KeyboardEvent) => {
      // Common shortcuts for opening dev tools
      const isF12 = e.key === 'F12';
      const isCtrlShiftI = e.ctrlKey && e.shiftKey && e.key === 'I';
      const isCtrlShiftJ = e.ctrlKey && e.shiftKey && e.key === 'J';
      const isCtrlShiftC = e.ctrlKey && e.shiftKey && e.key === 'C';
      const isCtrlU = e.ctrlKey && e.key === 'u';

      if (isF12 || isCtrlShiftI || isCtrlShiftJ || isCtrlShiftC || isCtrlU) {
        e.preventDefault();
        e.stopPropagation(); // Added for consistency
        alert('Opening developer tools is disabled!');
      }
    };

    document.addEventListener('keydown', handleDevToolsShortcut);

    // Additional protection: disable contextmenu (right-click menu)
    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault();
      return false;
    };

    document.addEventListener('contextmenu', handleContextMenu);

    // Cleanup event listeners on component unmount
    return () => {
      document.removeEventListener('copy', handleCopyPaste);
      document.removeEventListener('paste', handleCopyPaste);
      document.removeEventListener('cut', handleCopyPaste);
      document.removeEventListener('keydown', handleDevToolsShortcut);
      document.removeEventListener('contextmenu', handleContextMenu);
    };
  }, []);
  return (
    <div
      ref={fullRef}
      className="h-full bg-[#F8F9FB] px-1 py-5 md:px-2 lg:p-5"
      // onCopy={e =>
      //   assessmentDetails.assessment.is_restrict_copying && e.preventDefault()
      // }
      // onPaste={e => {
      //   if (assessmentDetails.assessment.is_track_paste) {
      //     e.preventDefault();
      //   }
      // }}
      onCopyCapture={e => {
        if (assessmentDetails.assessment.is_restrict_copying) {
          e.preventDefault();
          e.stopPropagation();
          // Optionally show alert or call registerCheating
          alert('Copying is disabled on this page!');
        }
      }}
      onPasteCapture={e => {
        if (assessmentDetails.assessment.is_track_paste) {
          e.preventDefault();
          e.stopPropagation();
          alert('Paste is disabled on this page!');
          registerCheating('paste');
        }
      }}
      onCutCapture={e => {
        e.preventDefault();
        e.stopPropagation();
        alert('Cut operation is disabled on this page!');
      }}
    >
      <AssessmentInterface
        isAssessmentActivated={isAssessmentActivated}
        activateAssessment={activateAssessment}
        enterFullscreen={enterFullscreen}
        getProctoringData={getData}
        screenCapturePermission={screenCapturePermission}
        openSubmitModal={() => {
          setIsSubmitModalOpen(true);
        }}
      >
        <ProctoringCam
          setCamRef={setWebcamRef}
          setCanvasRef={setCanvasRef}
          isAssessmentActivated={isAssessmentActivated}
          captureScreen={captureScreen}
          flagManager={flagManager}
        />
      </AssessmentInterface>

      {fullscreenIsDenied && isAssessmentActivated && !isGenericWarningOpen && !isEscFullscreenDialogOpen && !isScreenCaptureErrorOpen && (
        <div className="w-screen h-screen bg-[#0000008c] items-center fixed left-0 top-0 flex flex-col justify-center z-[999]">
          <div className="flex flex-col justify-center z-[9999]">
            <p className="p-2 mb-2 text-lg text-white">We could not return you back to fullscreen automatically</p>
            <Button
              className="rounded-md bg-white p-2 px-3 text-[#450A0A]"
              variant="light"
              type="button"
              onClick={() => { console.log("BUTTON IS CLICKED"); enterFullscreen() }}
            >
              Back to full screen
            </Button>
          </div>
        </div>
      )}

      {isFlagOpen && (
        <div
          style={{ transform: 'translateX(-50%)' }}
          className="fixed left-1/2 top-20 z-50 w-[450px] rounded-md bg-[#FFE2E0] p-4"
        >
          <button
            className="absolute right-2 top-2 flex h-10 w-10 cursor-pointer items-center justify-center rounded-full"
            onClick={closeFlagWarning}
          >
            <CloseCircle size="32" color="#FF5F56" variant="TwoTone" />
          </button>
          <p className="text-[#FF5F56]">Misconduct warning!</p>
          <p className="mt-3 text-[#732D2D]">{flagMessage}</p>
        </div>
      )}
      <SubmitTestModal
        isOpen={isSubmitModalOpen}
        setIsOpen={setIsSubmitModalOpen}
        openSuccess={openSuccess}
        activateAssessment={activateAssessment}
        deactivateAssessment={deactivateAssessment}
      />

      <SuccessModal
        isOpen={isSuccessModalOpen}
        closeModal={closeSuccess}
        openFeedback={openFeedback}
      />

      <SubmitFeedback
        isOpen={isFeedbackModalOpen}
        assessmentDetail={assessmentDetails}
        closeModal={closeFeedback}
      />

      <FullScreenEscWarning
        isEscFullscreenOpen={isEscFullscreenDialogOpen}
        setIsEscFullscreenOpen={closeEscFullscreenOpenDialog}
        limit={toleranceForFullscreenLeave - escFullScreenAttemptsCount}
        tolerance={toleranceForFullscreenLeave}
        enterFullscreen={enterFullscreen}
      // BABA GO USE WRONG FULL SCREEN EXIT COUNT----- FISH
      // limit={toleranceForFullscreenLeave - fullscreenLeaveCount}
      // limit={toleranceForFullscreenLeave - escFullScreenAttemptsCount}
      />

      <GenericAssessmentWarning
        isWarningOpen={isGenericWarningOpen}
        setIsWarningOpen={closeGenericWarning}
        bulkCount={
          assessmentDetails.assessment.bulk_tolerance_setup
            ?.combined_tolerance as number
        }
        enterFullscreen={enterFullscreen}

      />

      <ScreenCaptureError
        isWarningOpen={isScreenCaptureErrorOpen}
        closeScreenCaptureError={closeScreenCapture}
      />

      <DeficientLightWarning
        isDeficientLightDialogOpen={isDeficientLightDialogOpen}
        setIsLightDeficientOpen={closeIsDeficientLightDialog}
      />
      {/* <NoisyEnvironmentWarning
        isNoisyEnvironmentDialogOpen={isNoisyEnvironmentDialogOpen}
        setIsNoisyEnvironmentOpen={closeNoisyEnvironmentDialog}
      /> */}
      {/* <div
        className="fixed  top-0 z-50
 flex w-96 rounded-lg	border-2 bg-white p-4 text-black	shadow-lg	"
        style={" left:50%;  translate:transformX(-50%)"}
      >
        <p>
          {' '}
          You are in a very noisy environment, Kindly move to a quiter area
          before continuing the assessment
        </p>
      </div> */}

      {/* FORCE SUBMIT MODAL */}
      <ForceSubmitTestModal
        isForcedSubmitTestOpen={isForcedSumbitionDialogOpen}
        setIsForcedSubmitTestOpen={() => {
          setManualLoading(true);
          closeForcedSumbittionDialog();
        }}
      />

      <ExtendedScreenWarning
        isScreenExtendedOpen={isScreenExtendedDialogOpen}
        setIsScreenExtendedOpen={closeScreenExtendedDialog}
      />

      <WindowLeaveWarning
        isOpen={isWindowLeaveModalOpen}
        setIsOpen={setIsWindowLeaveModalOpen}
        limit={toleranceForWindowLeave - windowLeaveCount}
      />

      {/* CHECK IF  SUBMIT ACCESSMENTs IS LOADING*/}
      <LoadingOverlay
        isOpen={reportLoading || manualLoading}
        message={
          manualLoading ? 'Redirecting you to login page' : 'please wait...'
        }
      />
    </div>
  );
};

export default StartTest;
