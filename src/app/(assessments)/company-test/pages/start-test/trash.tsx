// import { questionTypeEnums } from "../dummydata";

// <div
//     className={cn(
//         'flex h-full flex-col rounded-lg p-0 pb-0 xl:pb-0 xl:pt-5',
//         (!candidateStatus.isReturning || !isAssessmentActivated) && 'hidden'
//     )}
// >
//     <p className="pb-2">
//         Section {currentSection?.number}:{' '}
//         <span className="font-semibold">{currentSection?.section}</span>
//     </p>
//     <div className="h-max w-full overflow-x-hidden rounded-lg font-light">
//         {questionsTest?.map((item: any, index: number) => {
//             const [selectedOption, setSelectedOption] = useState(
//                 item.selected
//             );
//             const handleOptionSelect = (val: string) => {
//                 setSelectedOption([val]);
//             };
//             useEffect(() => {
//                 let questionAtIndex = questionsTest[index];
//                 questionAtIndex = {
//                     ...questionAtIndex,
//                     selected: selectedOption,
//                 };
//                 const questionsTestCopy = [...questionsTest];
//                 questionsTestCopy[index] = questionAtIndex;
//                 setQuestionsTest(questionsTestCopy);
//             }, [selectedOption]);
//             return item.id === questionsTest[currentPageId].id ? (
//                 <div
//                     key={index}
//                     className="relative h-full rounded-lg bg-white p-5 pb-20"
//                 >
//                     <div className="question-block">
//                         <div className="border-b-[0.5px] border-[#d6d6d6] pb-6">
//                             <p className="w-[75%] py-2">Question {index + 1}</p>
//                             <div
//                                 className={cn(
//                                     'my-1 w-max rounded-full bg-primary-light px-2 py-1 text-sm font-medium text-primary'
//                                 )}
//                             >
//                                 {
//                                     questionTypeEnums[
//                                     item?.type as keyof typeof questionTypeEnums
//                                     ]
//                                 }
//                             </div>
//                             <div
//                                 className="tiptap ProwseMirror w-[75%] py-2"
//                                 dangerouslySetInnerHTML={{ __html: item.question }}
//                             />

//                             <div className="flex flex-wrap gap-2">
//                                 <div className="flex gap-2 overflow-x-auto">
//                                     {item.images?.map(
//                                         (image_url: string, index: number) => (
//                                             <div
//                                                 className="flex w-full flex-1 space-y-4"
//                                                 key={index}
//                                             >
//                                                 {/* <div className="relative flex w-full items-center justify-center"> */}
//                                                 <img
//                                                     className="relative h-auto w-full rounded-xl"
//                                                     src={image_url}
//                                                 />
//                                                 {/* </div> */}
//                                             </div>
//                                         )
//                                     )}
//                                 </div>
//                             </div>
//                         </div>
//                         <div className="p-2 py-5">
//                             {item.type === 'true_false' && (
//                                 <RadioGroup
//                                     value={selectedOption[0] || null}
//                                     onChange={val => handleOptionSelect(val)}
//                                 >
//                                     <div className="grid grid-cols-1 gap-y-2">
//                                         {['true', 'false'].map((option: any) => {
//                                             return (
//                                                 <RadioGroup.Option value={option}>
//                                                     {({ checked }) => (
//                                                         <div className="flex items-center gap-x-4">
//                                                             <div className="h-6 w-6 rounded-full border border-[#d6d6d6] p-[2px]">
//                                                                 <div
//                                                                     className={`flex h-full w-full items-center justify-center rounded-full p-1 ${checked ? 'bg-primary' : 'bg-white'
//                                                                         }`}
//                                                                 >
//                                                                     {checked && (
//                                                                         <CheckedIcon color="white" />
//                                                                     )}
//                                                                 </div>
//                                                             </div>
//                                                             <p>{option}</p>
//                                                         </div>
//                                                     )}
//                                                 </RadioGroup.Option>
//                                             );
//                                         })}
//                                     </div>
//                                 </RadioGroup>
//                             )}
//                             {(item.type === 'multiple_choice' ||
//                                 item.type === 'fill_in_the_blanks') && (
//                                     <RadioGroup
//                                         value={selectedOption[0] || null}
//                                         onChange={val => handleOptionSelect(val)}
//                                     >
//                                         <div className="grid grid-cols-1 gap-y-2">
//                                             {item.answer_options?.map((option: any) => {
//                                                 return (
//                                                     <RadioGroup.Option value={option}>
//                                                         {({ checked }) => (
//                                                             <div className="flex items-center gap-x-4">
//                                                                 <div className="h-6 w-6 rounded-full border border-[#d6d6d6] p-[2px]">
//                                                                     <div
//                                                                         className={`flex h-full w-full items-center justify-center rounded-full p-1 ${checked ? 'bg-primary' : 'bg-white'
//                                                                             }`}
//                                                                     >
//                                                                         {checked && (
//                                                                             <CheckedIcon color="white" />
//                                                                         )}
//                                                                     </div>
//                                                                 </div>
//                                                                 <p>{option}</p>
//                                                             </div>
//                                                         )}
//                                                     </RadioGroup.Option>
//                                                 );
//                                             })}
//                                         </div>
//                                     </RadioGroup>
//                                 )}
//                             {item.type === 'essay' && (
//                                 <textarea
//                                     id="textarea"
//                                     cols={30}
//                                     rows={6}
//                                     className="h-[50%] w-full rounded-lg bg-[#F8F9FB] p-3 outline-primary"
//                                     placeholder="Enter your answer"
//                                     onChange={e => {
//                                         setSelectedOption([e.target.value]);
//                                     }}
//                                     value={selectedOption[0] || ''}
//                                     onPaste={e => {
//                                         if (assessmentDetails.assessment.is_track_paste) {
//                                             e.preventDefault();
//                                             registerCheating('paste');
//                                         }
//                                     }}
//                                 ></textarea>
//                             )}
//                             {item.type === 'multiple_response' && (
//                                 <div className="grid grid-cols-1 gap-y-2">
//                                     {item.answer_options.map((option: any) => {
//                                         var checked = selectedOption.includes(option);
//                                         return (
//                                             <div>
//                                                 <div
//                                                     className="flex items-center gap-x-4"
//                                                     onClick={() => {
//                                                         if (checked) {
//                                                             setSelectedOption(
//                                                                 selectedOption.filter(
//                                                                     (optional: any) => {
//                                                                         return optional !== option;
//                                                                     }
//                                                                 )
//                                                             );
//                                                         } else {
//                                                             setSelectedOption([
//                                                                 ...selectedOption,
//                                                                 option,
//                                                             ]);
//                                                         }
//                                                     }}
//                                                 >
//                                                     <div className="h-6 w-6 rounded-full border border-[#d6d6d6] p-[2px]">
//                                                         <div
//                                                             className={`flex h-full w-full items-center justify-center rounded-full p-1 ${checked ? 'bg-primary' : 'bg-white'
//                                                                 }`}
//                                                         >
//                                                             {checked && <CheckedIcon color="white" />}
//                                                         </div>
//                                                     </div>
//                                                     <p>{option}</p>
//                                                 </div>
//                                             </div>
//                                         );
//                                     })}
//                                 </div>
//                             )}
//                         </div>

import React from 'react'

const trash = () => {
  return (
    <div>trash</div>
  )
}

export default trash