//   };

let url;
let db;
let proctorId;
let env;
let DB_CONNECTION_STATUS;

const dbName = 'ProctorFlagDatabase';
const storeName = 'flags';
// setInterval(() => {
// postMessage('adfsadf');
// }, 3000);

function connectDB() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(dbName);

    request.onerror = () => reject(request.error);
    request.onsuccess = () => {
      db = request.result;
      DB_CONNECTION_STATUS = 'ready';
      resolve();
    };
  });
}

function getAllData() {
  return new Promise(async (resolve, reject) => {
    try {
      if (DB_CONNECTION_STATUS !== 'ready') {
        await connectDB();
      }
      if (!db.objectStoreNames.contains(storeName)) {
        resolve([]);
      }
      const transaction = db.transaction(storeName, 'readonly');

      const store = transaction.objectStore(storeName);
      const request = store.getAll();

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
    } catch (error) {
      reject(error);
    }
  });
}

function clearStore() {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.clear();

    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve();
  });
}

// const run = async () => {
//   await connectDB();
// };

// run();

// Routine Uploader

// const sendProctoringReport = async () => {
//   let data = await getAllData();

//   let payload = { flags: data };

//   const response = await fetch(
//     `${url}/assessments/proctoring-reports/${proctorId}/`,
//     {
//       method: 'PATCH',
//       headers: {
//         'Content-Type': 'application/json',
//       },
//       body: JSON.stringify(payload),
//     }
//   );

//   if (!response.ok) {
//     throw new Error(`HTTP error! status: ${response.status}`);
//   }
//   // // console.log(response.json(), 'PROCTOR RESPONSE');
//   return response.json();
// };

function capitalize(str) {
  if (typeof str !== 'string') return '';
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

const cloudProctor = async data => {
  return new Promise(async (resolve, reject) => {
    const payload = {
      // proctoring_id: '19bfa36f-6675-4854-9450-979ddef5331d',
      proctoring_id: proctorId,
      images: data, // Array of base64 image strings under the "image" property
      //model: 'gpt-4o',
    };
    // console.log(env);
    // // console.log(payload, 'PAYLOAD');
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Proctor-Env': capitalize(env),
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        // throw new Error(`Server responded with ${response.status}`);
        reject(response.status);
      }
      let responseData = response.json();
      // // console.log('Response from server:', responseData);
      resolve(responseData);
    } catch (error) {
      // console.error('Error sending images:', error);
      reject(error);
    }
  });
};

let interval;
let proctorIsSending = false;
async function sendProctoringReport() {
  // console.log('cloud proctor running');
  if (interval) {
    clearInterval(interval);
  }
  // // // console.log('getting data');

  try {
    // console.log(new Date(), 'GETTING DATA FROM TABLE');
    let data = await getAllData();
    // console.log(data, 'WORKER DATA');
    if (data.length > 0) {
      let timeForSent = data[data.length - 1].time;
      clearStore(); // Clear the entire table
      // // // console.log(new Date(), 'GOT DATA FROM TABLE');
      // // // console.log(data);

      let managedData = [];

      for (let i = 0; i < data.length; i++) {
        // console.log('loop is running');
        let item = data[i];

        if (item.screenshot.trim() === ",") {
          continue
        }
        // // console.log(item);
        // const compressedScreenshot = await compressImage(
        //   item.screenshot,
        //   0.7,
        //   700
        // );
        const compressedPCCapture = await compressImage(
          item.pc_capture,
          0.7,
          700
        );

        managedData.push({
          // screenshot: compressedScreenshot,
          screenshot: item.screenshot,
          pc_capture: compressedPCCapture,
        });
      }
      if (managedData.length > 3) {
        managedData = managedData.slice(0, 3);
        // console.log('DATA sliced');
      }
      // console.log(managedData, 'MANAGE');
      // // console.log(new Date(), 'STARTING API CALL');
      if (managedData.length > 0) {
        let response = await cloudProctor(managedData);
        // // console.log(new Date(), 'RESPONSE FROM API CALL');

        // // console.log(response, 'RESPONSE FROM CLOUD PROCTOR');
        postMessage({ type: 'LOG', data: new Date() });

        parseProctorResponse(response, timeForSent);
      } else {
        // console.log('ignoring empty data');
      }
    } else {
      // console.log('ignoring not available');
      postMessage({ type: 'LOG_ERROR', data: new Date() });
    }
  } catch (e) {
    // console.log('ERROR', e);
  }

  interval = setInterval(() => {
    sendProctoringReport();
  }, 5000);
}

function parseProctorResponse(responses, timeForSent) {
  let responseToFront = {};
  // // console.log('RESPONSE PARSING');
  // console.log(responses, 'RESPONSES');
  responses.forEach(response => {
    // console.log(response, 'ANALYSIS');
    if (response.analysis.looking_away) {
      responseToFront.lookingAway = true;
    }
    if (response.analysis.multiple_faces) {
      responseToFront.MultiFace = true;
    }
    if (response.analysis.no_face) {
      responseToFront.NoFace = true;
    }
    if (response.analysis.prohibited_objects.detected) {
      responseToFront.illegalObject = true;
    }
  });
  // // console.log('POSTING MESSAGE TO CLIENT');
  responseToFront.time = timeForSent;
  // console.log(responseToFront, 'MESSAGE TO CLIENT');
  postMessage({ type: 'RESPONSE_PAYLOAD', data: responseToFront });
}

// EVENT LISTENERS
addEventListener('message', async e => {
  let data = e.data;
  // // console.log(data);
  if (data.type == 'id') {
    proctorId = data.data;
  }
  if (data.type == 'proctorURL') {
    url = data.data;
  }
  if (data.type == 'env') {
    env = data.data;
  }
  // // console.log(url, 'URL');
  // // console.log(proctorId, 'PROCTOR ID');
  if (url && proctorId && env) {
    // console.log('ALL COMPLETE');
    sendProctoringReport();
    // postMessage({
    //   MultiFace: true,
    // });
  }
});

// sendProctoringReport();
// Worker-compatible image compression function

async function compressImage(base64String, quality, maxWidth) {
  if (!base64String) {
    return '';
  }
  // Ensure the base64 string has the correct prefix
  const base64 = normalizeBase64String(base64String);

  // Create an image from the base64 string
  const image = await createImageBitmap(dataURItoBlob(base64));

  // Calculate new dimensions while maintaining aspect ratio
  let newWidth = image.width;
  let newHeight = image.height;

  if (newWidth > maxWidth) {
    const ratio = maxWidth / newWidth;
    newWidth = maxWidth;
    newHeight = Math.round(image.height * ratio);
  }

  // Create a canvas to draw the resized image
  const canvas = new OffscreenCanvas(newWidth, newHeight);
  const ctx = canvas.getContext('2d');

  // Draw the image with smooth scaling
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';
  ctx.drawImage(image, 0, 0, newWidth, newHeight);

  // Convert to blob with compression
  const blob = await canvas.convertToBlob({
    type: 'image/jpeg',
    quality: quality,
  });

  // Convert blob back to base64
  return await blobToBase64(blob);
}

function normalizeBase64String(base64String) {
  // Handle case where string already starts with "data:"
  if (base64String.startsWith('data:')) {
    return base64String;
  }

  // Handle case where string starts with "image/[type];base64,"
  if (base64String.startsWith('image/')) {
    return `data:${base64String}`;
  }

  // Handle raw base64 string
  if (!base64String.includes(';base64,')) {
    return `data:image/jpeg;base64,${base64String}`;
  }

  return base64String;
}

function dataURItoBlob(dataURI) {
  // Extract the base64 data after the comma
  const splitDataURI = dataURI.split(',');
  const base64 = splitDataURI[1];

  // Get the mime type from the header
  const mimeString = splitDataURI[0].split(':')[1].split(';')[0];

  const byteString = atob(base64);
  const ab = new ArrayBuffer(byteString.length);
  const ia = new Uint8Array(ab);

  for (let i = 0; i < byteString.length; i++) {
    ia[i] = byteString.charCodeAt(i);
  }

  return new Blob([ab], { type: mimeString });
}

function blobToBase64(blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onloadend = () => resolve(reader.result);
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}
