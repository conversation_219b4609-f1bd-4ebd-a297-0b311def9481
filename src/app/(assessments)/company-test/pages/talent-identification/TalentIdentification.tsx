'use client';

// import CustomWebcam from '../components/CustomWebcam';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import toast from 'react-hot-toast';
import ReactLoading from 'react-loading';
import { usePageLeave } from 'react-use';
import Webcam from 'react-webcam';
import UploadIcon from '@/app/(public-links)/jobs/misc/icons/UploadIcon';
import { LoadingOverlay } from '@/components/shared';
import { cn } from '@/utils';
import {
  TSendProctorReportData,
  useCheckProcotorStatus,
  useUploadProfileImages,
  useVerifyWithNIN,
} from '../../misc/api/sendProctoringReport';
import RetryWithNIN from '../../misc/components/RetryWithNIN';
import VerifyIDErrorModal from '../../misc/components/VerifyIDErrorModal';
import { useAssessmentDetails } from '../../misc/store/assessmentStore';
import { useProfileImages } from '../../misc/store/imageStore';
import { useProctoringReport } from '../../misc/store/proctoringReportStore';

const TalentIdentification = ({
  handleNext,
}: {
  handleNext: (page?: number) => void;
}) => {
  const router = useRouter();
  const { assessmentDetails } = useAssessmentDetails();
  const profileImages = useProfileImages(state => state.data);
  const setProfileImages = useProfileImages(state => state.setProfileImages);
  const webcamRef = useRef<any>(null);
  const [imgSrc, setImgSrc] = useState(null); // initialize it
  const [upload_error, setUploadError] = useState('');
  const { proctoringReport, setProctoringReport, setProctoringId } =
    useProctoringReport();

  const capture = useCallback(() => {
    const imageSrc = webcamRef.current.getScreenshot();
    setImgSrc(imageSrc);
    setProfileImages({
      profilePicture: profileImages?.profilePicture as string,
      idPicture: imageSrc,
      isUploaded: false,
    });
  }, [webcamRef]);

  const retake = () => {
    setImgSrc(null);
    setUploadError('');
  };
  const [cameraError, setCameraError] = useState<string | null>();

  const [is_changing_pages, setIsChangingPages] = useState(true);

  useEffect(() => {
    setIsChangingPages(false);
  }, []);

  const { mutate: uploadProfileImages, isLoading: uploadLoading } =
    useUploadProfileImages();

  const reformatProctoringData = (data: any) => {
    let payload = { ...data };

    if (typeof payload.bulk_proctor_option == 'object') {
      payload.bulk_proctor_option = Object.entries(
        payload.bulk_proctor_option
      ).map(i => ({
        key: i[0],
        value: i[1],
      }));
    } else {
      payload.bulk_proctor_option = [...payload.bulk_proctor_option];
    }
    return payload;
  };
  const handleUpload = () => {
    const payload = {
      candidate_email: assessmentDetails?.candidate_email,
      assessment: assessmentDetails?.assessment.id,
      photo_id: profileImages?.idPicture as string,
      profile_photo: profileImages?.profilePicture as string,
      is_upload: profileImages?.isUploaded as boolean,
    };
    uploadProfileImages(payload, {
      onSuccess: data => {
        console.log(data, 'PROCTOR CHECK RESULT');
        // console.log(reformatProctoringData(data), 'PURE PROCTORING DATA');
        let payload = reformatProctoringData(data);
        setProctoringReport(payload);
        console.log(payload);
        setIsChangingPages(true);

        if (assessmentDetails.assessment.allow_strict_id_verification) {
          handleNext();
        } else {
          handleNext(5);
        }
      },
      onError: (err: any) => {
        let error =
          err?.response?.data?.detail || err.message || 'Something went wrong';
        /* let error_status = err?.response?.status */
        /* if (error_status === 400){ */
        /*   error = "The Identity document you submitted cannot be verified. Kindly provide a valid and verifiable document e.g NIN, voters card" */
        /* } */
        toast.error(error);
      },
    });
    // router.push('/company-test/start-test');
  };
  let interval: ReturnType<typeof setInterval>;
  const [checkingProctorStatus, setCheckingProctorStatus] = useState(false);
  const { mutate: checkProctorStatus } = useCheckProcotorStatus();

  const [current_tab, setCurrentTab] = useState('upload');
  const tabs = ['upload', 'camera'];

  /* intro video */
  const imageRef = useRef<HTMLImageElement | null>(null);

  function clearImageSrc() {
    setImageURL('');
    setProfileImages({
      profilePicture: profileImages?.profilePicture as string,
      idPicture: '',
      isUploaded: false,
    });
  }

  const [image_url, setImageURL] = useState('');

  function blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, _) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.readAsDataURL(blob);
    });
  }

  async function convertUploadedImage(acceptedFiles: any) {
    const blob = new Blob([acceptedFiles[0]], {
      type: acceptedFiles[0].type,
    });
    const image_base64 = await blobToBase64(blob);
    setImageURL(image_base64);
    setProfileImages({
      profilePicture: profileImages?.profilePicture as string,
      idPicture: image_base64,
      isUploaded: true,
    });
  }

  const onDrop = useCallback(async (acceptedFiles: any) => {
    convertUploadedImage(acceptedFiles);
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      'image/*': [],
    },
    maxSize: 10 * 1024 * 1024, //10mb.
  });

  // PREP NEXT PAGE

  const prepNextPage = () => { };

  return (
    <div className=" relative flex items-center justify-center gap-8 px-20 py-10 pb-20">
      <div className=" grid h-[90%] w-[90%] grid-cols-2 gap-x-8 overflow-y-scroll rounded-2xl bg-white p-5 2xl:w-[70%]">
        <div>
          <p className="text-xl font-medium">
            Upload ID Photo or Take a picture
          </p>
          <p className="py-4 text-body-text">
            {/* Place your government issued ID e.g (e.g Voters card, NIN, Drivers
            licence, Intl passport) in front of your Webcam/camera and tap the
            Take photo Button below. */}
            Upload a clear image of your government-issued ID (e.g., Voter’s
            Card, NIN, Driver’s License, or International Passport) or switch to
            the Camera tab, place your ID in front of your webcam/camera and tap
            the 'Take picture' button.
          </p>
          <div className="space-y-2">
            <div className="w-full rounded-lg bg-primary-light p-3 text-sm text-primary">
              <p>
                Verify that the photo of your ID is clear, eligible and centred
                then click Continue to continue. If the photo is blurred or
                dark, Click Retake Photo to try again before
              </p>
            </div>
            <ul className="grid grid-cols-4 gap-4">
              <li>
                <img
                  src="/images/take-assessment/blurry.png"
                  className="w-full"
                  alt="dont use a blurry id"
                />
              </li>
              <li>
                <img
                  src="/images/take-assessment/cropped.png"
                  className="w-full"
                  alt="dont use a cropped id"
                />
              </li>
              <li>
                <img
                  src="/images/take-assessment/slanted.png"
                  className="w-full"
                  alt="use an upright image"
                />
              </li>
              <li>
                <img
                  src="/images/take-assessment/good.png"
                  className="w-full"
                  alt="use a perfectly framed image"
                />
              </li>
            </ul>
          </div>
        </div>
        <div className="space-y-4">
          <nav className="">
            <ul className="flex items-center gap-4">
              {tabs.map((tab, index) => (
                <li
                  key={index}
                  className={cn(
                    'border-b-[3px] border-transparent p-4 pb-2 text-sm transition-all duration-300 hover:border-primary-light',
                    tab == current_tab && ' !border-primary text-primary'
                  )}
                >
                  <label className="cursor-pointer capitalize">
                    <input
                      type="radio"
                      name="active tab"
                      className="hidden"
                      value={tab}
                      onChange={e => {
                        setCurrentTab(e.target.value);
                      }}
                    />
                    {tab}
                  </label>
                </li>
              ))}
            </ul>
          </nav>
          {current_tab == 'camera' && (
            <div className="rounded-md bg-grey p-5">
              <p className=" text-xl font-medium">Preview Photo</p>
              <div className="my-4">
                {imgSrc ? (
                  <img
                    src={imgSrc}
                    alt="image"
                    className="border-primary` w-full rounded-xl border"
                  />
                ) : (
                  <Webcam
                    className={`w-full rounded-xl border border-primary`}
                    audio
                    ref={webcamRef}
                    mirrored={false}
                    muted
                    screenshotFormat="image/png"
                  />
                )}
              </div>
              <div className=" flex gap-x-4 py-4">
                {imgSrc ? (
                  <button
                    className="flex min-w-[150px] items-center justify-center rounded-md bg-primary p-3 text-white"
                    onClick={retake}
                  >
                    Retake
                  </button>
                ) : (
                  <button
                    className="min-w-[150px] rounded-md bg-primary p-3 text-white"
                    onClick={capture}
                  >
                    Take picture
                  </button>
                )}
                {imgSrc && (
                  <button
                    className="btn-primary flex min-w-[150px] items-center justify-center gap-2"
                    onClick={handleUpload}
                    disabled={uploadLoading || checkingProctorStatus}
                  >
                    {uploadLoading || checkingProctorStatus ? (
                      <>
                        <ReactLoading
                          type="spin"
                          color="#fff"
                          width={20}
                          height={20}
                        />
                        <p>Verifying ID</p>
                      </>
                    ) : (
                      <p>Continue</p>
                    )}
                  </button>
                )}
              </div>
            </div>
          )}
          {current_tab == 'upload' && (
            <div className="rounded-md bg-grey p-5">
              <div className="space-y-2">
                {!image_url && (
                  <div
                    className={`mt-3 flex h-[5.9375rem] cursor-pointer items-center gap-4 rounded-[0.875rem] border-[0.3px] border-dashed border-[rgb(117,90,226)] bg-white p-6`}
                    {...getRootProps()}
                  >
                    <div className="">
                      <UploadIcon />
                    </div>
                    <div className="">
                      <p className="font-sans text-xs font-semibold text-[#755AE2] sm:text-sm">
                        Tap to upload an image of your ID
                      </p>
                      <span className="font-sans text-xs font-semibold text-[#755AE2] opacity-75">
                        Files types: PNG, JPG, Max size: 10MB
                      </span>
                    </div>
                    <input hidden {...getInputProps()} />
                  </div>
                )}
                {image_url && (
                  <div className="space-y-4">
                    <div className="relative flex items-center justify-center">
                      <img
                        className="relative w-full rounded-xl object-cover"
                        src={image_url}
                        ref={imageRef}
                      />
                      <button
                        className="size-8 absolute right-0 top-0 m-4 grid place-items-center rounded-full p-2 text-2xl hover:bg-black/20"
                        onClick={clearImageSrc}
                      >
                        &times;
                      </button>
                    </div>
                    <div className="flex items-center justify-end gap-4">
                      <button
                        className="btn-primary-light flex min-w-[150px] items-center justify-center"
                        onClick={clearImageSrc}
                      >
                        Clear image
                      </button>

                      <button
                        className="btn-primary flex min-w-[150px] items-center justify-center gap-2"
                        onClick={() => handleUpload()}
                        disabled={uploadLoading || checkingProctorStatus}
                      >
                        {uploadLoading || checkingProctorStatus ? (
                          <>
                            <ReactLoading
                              type="spin"
                              color="#fff"
                              width={20}
                              height={20}
                            />
                            <p>Verifying ID</p>
                          </>
                        ) : (
                          <p>Continue</p>
                        )}
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      <LoadingOverlay isOpen={is_changing_pages} />
    </div>
  );
};

export default TalentIdentification;
