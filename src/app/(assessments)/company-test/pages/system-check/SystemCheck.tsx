'use client';

import * as faceDetection from '@tensorflow-models/face-detection';
import * as tf from '@tensorflow/tfjs'; // Required for TF models

import { ArrowCircleDown2, ArrowCircleUp2 } from 'iconsax-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React, {
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import toast from 'react-hot-toast';
import ReactLoading from 'react-loading';
import { useGeolocation, useNetworkState } from 'react-use';
import Webcam from 'react-webcam';
import { LoadingOverlay } from '@/components/shared';
import { cn } from '@/utils';
import {
  checkBrightness,
  MINIMUM_BRIGHTNESS_LEVEL,
} from '../../misc/api/checkBrightness';
import { useUploadProfileImages } from '../../misc/api/sendProctoringReport';
import AntiCheating from '../../misc/components/AntiCheating/AntiCheating';
import ProgressBar from '../../misc/components/ProgressBar';
import { useMQTTTrackingService } from '../../misc/hooks/useMQTTTrackingService';
import DangerIcon from '../../misc/icons/DangerIcon';
import LightingIcon from '../../misc/icons/LightingIcon';
import MicIcon from '../../misc/icons/MicIcon';
import TickIcon from '../../misc/icons/TickIcon';
import TickIcon2 from '../../misc/icons/TickIcon2';
import WebcamIcon from '../../misc/icons/WebcamIcon';
import WifiIcon from '../../misc/icons/WifiIcon';
import {
  useAssessmentDetails,
  UseCandidateStatusStore,
  useProctoringReport,
} from '../../misc/store';
import { useProfileImages } from '../../misc/store/imageStore';
import {
  NetworkStoreProps,
  useNetworkDetails,
} from '../../misc/store/networkStore';
import {
  NetworkTestResults,
  runNetworkTests,
} from '../../misc/utils/networkTest';
import { SocketProgressEvent } from '../../misc/utils/websocketUrl';

 import { useLocation } from '../../misc/hooks/use-location';
import { useGetGeoLocation } from '../../misc/api/getGeolocation';


interface systemCheckProps {
  name: SystemCheckTypes;
  controller: boolean;
  icon: React.ReactNode;
  errMsg: string;
}

enum SystemCheckTypes {
  Webcam = 'Webcam',
  Network = 'Network',
  Microphone = 'Microphone',
  Lighting = 'Lighting',
  Location = 'Location',
}

const defaultSystemChecks: systemCheckProps[] = [
  {
    name: SystemCheckTypes.Webcam,
    controller: false,
    icon: <WebcamIcon />,
    errMsg: 'Your webcam is not working',
  },
  {
    name: SystemCheckTypes.Network,
    controller: false,
    icon: <WifiIcon />,
    errMsg: 'Your network is unstable',
  },
  {
    name: SystemCheckTypes.Microphone,
    controller: false,
    icon: <MicIcon />,
    errMsg: 'Your microphone is not working',
  },


  {
    name: SystemCheckTypes.Lighting,
    controller: false,
    icon: <LightingIcon />,
    errMsg: 'Make sure you are in a well lit area',
  },
  {
    name: SystemCheckTypes.Location,
    controller: false,
    icon: <MicIcon />,
    errMsg: 'Your location is not working',
  },
];

const SystemCheck = ({
  handleNext,
}: {
  handleNext: (page?: number) => void;
}) => {
  const profileImages = useProfileImages(state => state.data);
  const { assessmentDetails } = useAssessmentDetails();
  const { mutate: uploadProfileImages, isLoading: uploadLoading } =
    useUploadProfileImages();
  const router = useRouter();
  const setProfileImages = useProfileImages(state => state.setProfileImages);
  const webcamRef = useRef<any>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [imgSrc, setImgSrc] = useState<null | string>(); // initialize it
  const [cameraError, setCameraError] = useState<string>('');
  const [lightSufficient, setLightSuffuciency] = useState<boolean>(false);
  const [webcamCheck, setWebcamCheck] = useState<boolean>(
    defaultSystemChecks[0].controller
  );
  const [networkCheck, setNetworkCheck] = useState<boolean>(
    defaultSystemChecks[1].controller
  );
    const { mutate: fetchGeoLocation } = useGetGeoLocation();
  
  const [microphoneCheck, setMicrophoneCheck] = useState<boolean>(
    defaultSystemChecks[2].controller
  );
  const [lightingCheck, setLightingCheck] = useState<boolean>(
    defaultSystemChecks[3].controller
  );
  const { candidateStatus, setCandidateStatus } = UseCandidateStatusStore();
  const { proctoringReport, setProctoringReport, setProctoringId } =
    useProctoringReport();

  const { sendMessage: sendSocketMessage } = useMQTTTrackingService();


  const [locationCheck, setLocationCheck] = useState<boolean>(false);
  const { location, getCurrentPosition } = useLocation();

  useEffect(() => {
    console.log(location, "LOCATION")
    if (!location) {
      getCurrentPosition();
      return
    }
   const setGeoLocation = async () => {
    console.log("SETTING GEO LOCATION")
      fetchGeoLocation(
        {
          long: location?.longitude as number,
          lat: location?.latitude as number,
          key: 'be5cdeb5c4c843f5aafa31ce711734f0',
        },
        {
          onSuccess: (data:any) => {
            console.log(data, "GEOLOCATION DATA")
            setProctoringReport({
              ...proctoringReport,
              location: data?.results[0]?.components?.county,
            });
            setLocationCheck(true)

          },
          onError: () => {
            setProctoringReport({
              ...proctoringReport,
              location: 'Unknown',
            });
          },
        }
      );
    };
    if (location?.longitude && location?.latitude) {
      setGeoLocation();
    }


  }, [location])

  const [is_changing_pages, setIsChangingPages] = useState(false);

  const capture = useCallback(() => {
    const imageSrc = webcamRef.current.getScreenshot();

    setImgSrc(imageSrc);
    setProfileImages({
      profilePicture: imageSrc as string,
      idPicture: '',
    });
  }, [webcamRef]);

  const retake = () => {
    setImgSrc(null);
    setProfileImages({
      profilePicture: '',
      idPicture: '',
    });
  };

  const handleWebcamSuccess = (stream: MediaStream) => {
    setWebcamCheck(true);
    setMicrophoneCheck(true);
  };

  const handleWebcamError = (error: any) => {
    setWebcamCheck(false);
    setMicrophoneCheck(false);

    if (error.name === 'NotAllowedError') {
      setCameraError(
        "Camera Permission Denied. If you didn't do that, refresh the page and try again"
      );
    } else if (
      error.name === 'NotFoundError' ||
      error.name === 'DevicesNotFoundError'
    ) {
      setCameraError(
        'Check your device. No camera found or no camera devices available.'
      );
    } else if (
      error.name === 'NotReadableError' ||
      error.name === 'TrackStartError'
    ) {
      setCameraError(
        'Camera is being used by another application or a hardware issue.'
      );
    } else if (
      error.name === 'InactiveStreamError' ||
      error.code === 'InactiveStreamError'
    ) {
      setCameraError('Your camera or microphone device is inactive.');
    } else {
      console.log('getUserMedia error:', error);
    }
  };

  const checkCamera = async () => {
    try {
      await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
      setWebcamCheck(true);
      setMicrophoneCheck(true);
      return true;
    } catch (error: any) {
      setWebcamCheck(false);
      setMicrophoneCheck(false);

      if (error.name === 'NotAllowedError') {
        setCameraError(
          "Camera Permission Denied. If you didn't do that, refresh the page and try again"
        );
      } else if (
        error.name === 'NotFoundError' ||
        error.name === 'DevicesNotFoundError'
      ) {
        setCameraError(
          'Check your device. No camera found or no camera devices available.'
        );
      } else if (
        error.name === 'NotReadableError' ||
        error.name === 'TrackStartError'
      ) {
        setCameraError(
          'Camera is being used by another application or a hardware issue.'
        );
      } else if (
        error.name === 'InactiveStreamError' ||
        error.code === 'InactiveStreamError'
      ) {
        setCameraError('Your camera or microphone device is inactive.');
      } else {
        console.log('getUserMedia error:', error);
      }
      return false;
    }
  };
  var network = useNetworkState();

  useEffect(() => {
    if (network.online) {
      setCandidateStatus({ isOnline: true });
      setNetworkCheck(true);
    } else {
      setCandidateStatus({ isOnline: false });
      setNetworkCheck(false);
    }
  }, [network]);


  const [lightLoading, setLightLoading] = useState<boolean>(true);
  useEffect(() => {
    let interval: NodeJS.Timeout;
    const init = async () => {
      await tf.setBackend('webgl');
      await tf.ready();

      const faceModel = await faceDetection.createDetector(
        faceDetection.SupportedModels.MediaPipeFaceDetector,
        {
          runtime: 'tfjs',
          maxFaces: 1,
          modelType: 'short',
        }
      );
      const detectFace = async () => {
        if (!webcamRef.current) return;
        const faces = await faceModel.estimateFaces(webcamRef?.current.video);
        // console.log(faces.length, "FACESc")
        if (faces.length == 1) {
          setLightingCheck(true);
          setLightSuffuciency(true);
        } else {
          setLightingCheck(false);
          setLightSuffuciency(false);
        }
      };
      setLightLoading(false);
      detectFace();
      interval = setInterval(detectFace, 300);
      console.timeEnd('init');
    };

    init().catch(console.error);
    return () => {
      clearInterval(interval);
    };
  }, [webcamRef]);

  const reformatProctoringData = (data: any) => {
    let payload = { ...data };
    payload.bulk_proctor_option = Object.entries(
      payload.bulk_proctor_option
    ).map(i => ({
      key: i[0],
      value: i[1],
    }));
    return payload;
  };

  const handleUpload = async () => {
    //console.log(assessmentDetails, 'ASSESSMENT DETAIL');
    // return;
    if (!assessmentDetails.assessment.is_identity_verification) {
      setIsChangingPages(true);
      const payload = {
        candidate_email: assessmentDetails?.candidate_email,
        assessment: assessmentDetails?.assessment.id,
        profile_photo: profileImages?.profilePicture as string,
        is_upload: profileImages?.isUploaded as boolean,
        photo_id: '',
        is_identity_verification:
          assessmentDetails.assessment.is_identity_verification,
      };
      uploadProfileImages(payload, {
        onSuccess: async (data: any) => {
          setProctoringReport(reformatProctoringData(data));
          handleNext(5);
          await sendSocketMessage(SocketProgressEvent.SYSTEM_CHECK_PASSED);
        },
        onError: (err: any) => {
          setIsChangingPages(false);

          let error =
            err?.response?.data?.detail ||
            err.message ||
            'Something went wrong';

          toast.error(error);
        },
      });
    } else {
      // router.push(`/company-test/talent-identification/`);
      await sendSocketMessage(SocketProgressEvent.SYSTEM_CHECK_PASSED);

      handleNext();
    }
  };


  const [internetSpeed, setInternetSpeed] = useState<null | NetworkTestResults>(
    null
  );
  const [internetCheckOngoing, setInternetCheckOngoing] = useState(false);
  // NETwork nCheck
  const handleNetworkSpeedCheck = async () => {
    setInternetCheckOngoing(true);

    try {
      let response = await runNetworkTests();
      // console.log(response, "NETWORK RESPONSE")
      setInternetSpeed(response);
    } catch (e) {
      console.log(e);
    }
    setInternetCheckOngoing(false);
  };

  useEffect(() => {
    handleNetworkSpeedCheck();
  }, []);

  return (
    <div className="relative flex h-max justify-center px-5 pb-20 pt-10 lg:px-10 xl:px-20">
      <div className="flex h-max w-full flex-col rounded-2xl bg-white p-10 2xl:w-[70%]">
        <div>
          <p className="text-xl font-medium">System Check</p>
          <p className="py-4 text-body-text">
            We utilize your camera image to ensure fairness for all
            participants, and we also employ both your camera and microphone for
            a video questions where you will be prompted to record a response
            using your camera or webcam, so it's essential to verify that your
            camera and microphone are functioning correctly and that you have a
            stable internet connection.
            <span className="font-medium">
              To do this, please position yourself in front of your camera,
              ensuring that your entire face is clearly visible on the screen.
              This includes your forehead, eyes, ears, nose, and lips. You can
              initiate a 5-second recording of yourself by clicking the button
              below.
            </span>
          </p>
        </div>
        <div className="mt-4 flex h-full w-full flex-col gap-x-8 lg:flex-row">
          <div className="w-full pb-4 lg:w-[50%] lg:pb-0">
            {imgSrc ? (
              <img
                src={imgSrc}
                alt="image"
                className="border-primary` w-full rounded-xl border"
              />
            ) : (
              <>
                <Webcam
                  className={`w-full rounded-xl border border-primary`}
                  audio
                  ref={webcamRef}
                  mirrored={true}
                  muted
                  screenshotFormat="image/png"
                  onUserMedia={handleWebcamSuccess}
                  onUserMediaError={handleWebcamError}
                />
                <canvas
                  ref={canvasRef}
                  width={'100%'}
                  style={{ display: 'none' }}
                />
              </>
            )}
          </div>
          <div className=" flex h-[80%] w-full flex-col gap-4 lg:w-[50%]">
            <div className="grid grid-cols-4 gap-4">
              <SystemCheckItem
                controller={webcamCheck}
                icon={defaultSystemChecks[0].icon}
                name={defaultSystemChecks[0].name}
              />

              <SystemCheckItem
                controller={microphoneCheck}
                icon={defaultSystemChecks[2].icon}
                name={defaultSystemChecks[2].name}
              />
              <SystemCheckItem
                controller={lightingCheck}
                icon={defaultSystemChecks[3].icon}
                name={defaultSystemChecks[3].name}
                loading={lightLoading}
              />
              <SystemCheckItem
                controller={locationCheck}
                icon={defaultSystemChecks[4].icon}
                name={defaultSystemChecks[4].name}
              />
            </div>
            {/* NETWORK TEST */}
            <div className="relative flex h-full w-full items-center justify-center rounded-md bg-primary-light p-3 text-primary">
              {(internetCheckOngoing || !internetSpeed) && (
                <div className="flex h-8 w-8 items-center justify-center">
                  <ReactLoading
                    height={30}
                    width={30}
                    type="spin"
                    color="#755AE2"
                  />
                </div>
              )}

              {!internetCheckOngoing && internetSpeed && (
                <div className="flex h-full w-full   items-center justify-center">
                  {/* Download */}
                  <div className="flex items-center p-4">
                    <ArrowCircleDown2 size="32" className="mr-2" />
                    <div>
                      <p className="fs-10">Download</p>
                      <h5 className="text-[20px] font-bold">
                        {internetSpeed?.download?.value
                          ? Math.floor(internetSpeed.download.value)
                          : 0}{' '}
                        {internetSpeed?.download?.unit}
                      </h5>
                    </div>
                  </div>
                  <div className="h-[10px] w-[10px] rounded-full bg-primary-light"></div>

                  {/* Upload */}
                  <div className="flex items-center p-4">
                    <ArrowCircleUp2 size="32" className="mr-2" />
                    <div>
                      <p className="fs-10">Upload</p>
                      <h5 className="text-[20px] font-bold">
                        {internetSpeed?.upload?.value
                          ? Math.floor(internetSpeed.upload.value)
                          : 0}{' '}
                        {internetSpeed?.upload?.unit}
                      </h5>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-4 py-4 md:flex-row">
          {imgSrc ? (
            <button
              className="mx-auto flex min-w-[200px] items-center justify-center rounded-md bg-primary p-3 text-white lg:mx-0"
              onClick={retake}
            >
              Retake
            </button>
          ) : (
            <button
              className="mx-auto min-w-[200px] rounded-md bg-primary p-3 text-white lg:mx-0"
              onClick={capture}
              disabled={
                cameraError != '' || !lightSufficient || internetCheckOngoing || !locationCheck
              }
            >
              Take picture
            </button>
          )}
          {imgSrc && (
            <button
              className="mx-auto flex min-w-[200px] items-center justify-center rounded-md bg-primary p-3 text-white lg:mx-0"
              onClick={handleUpload}
            >
              Continue
            </button>
          )}
        </div>
      </div>
      <LoadingOverlay isOpen={is_changing_pages} />
    </div>
  );
};

const SystemCheckItem: React.FC<{
  controller: boolean;
  name: string;
  icon: React.ReactNode;
  loading?: boolean;
}> = ({ controller, name, icon, loading }) => {
  return (
    <div className="relative flex h-full w-full items-center justify-center rounded-md bg-primary-light p-3 text-primary">
      <div className="flex h-full w-full flex-col items-center justify-center">
        <div className="relative">
          <ProgressBar
            data={[{ name: 'L1', value: controller ? 100 : 45 }]}
            outerRadius={'60%'}
            innerRadius={'55%'}
            progressColor={controller ? '#755AE2' : '#bf2626'}
          />
          <div className="absolute left-1/2 top-1/2 h-full w-full">
            {controller ? (
              <TickIcon2 className="h-[50%] w-[50%] -translate-x-1/2 -translate-y-1/2" />
            ) : (
              <DangerIcon className="h-[35%] w-[35%] -translate-x-1/2 -translate-y-1/2" />
            )}
          </div>
        </div>
        <p className={cn({ 'text-danger-dark': !controller })}>{name}</p>
      </div>
      <div
        className={cn(
          'absolute right-1 top-1 flex h-6 w-6 items-center justify-center rounded-full bg-danger-dark',
          { 'bg-primary': controller }
        )}
      >
        {icon}
      </div>

      {loading && (
        <div
          className={cn(
            'absolute left-1 top-1 flex h-6 w-6 items-center justify-center rounded-full bg-danger-dark',
            { 'bg-primary': controller }
          )}
        >
          <ReactLoading height={18} width={18} type="spin" color="#fff" />
        </div>
      )}
    </div>
  );
};

export default SystemCheck;
