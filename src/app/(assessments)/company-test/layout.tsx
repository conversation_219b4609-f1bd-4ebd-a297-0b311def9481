'use client';

import React, {
  Children,
  createContext,
  useContext,
  useEffect,
  useState,
} from 'react';
// import { SkeletonTheme } from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import { usePathname, useSearchParams } from 'next/navigation';
import { cn } from '@/utils';
import { dummyTest } from './dummydata';
import useWebsocket from './misc/components/hooks/useWebsocket';
import Navigation from './misc/components/Navigation';
import { useAssessmentDetails } from './misc/store';
import {
  SubmitModalContextType,
  TestContextType,
} from './misc/types/contextType';
import { getProgressWebSocketUrl, SocketProgressEvent } from './misc/utils/websocketUrl';
import { useMQTTTrackingService } from './misc/hooks/useMQTTTrackingService';

// export const metadata = {
//   title: 'Test',
//   description: 'Generated by Next.js',
// };

const TestContext = createContext<TestContextType | null>(null);
export const SubmitModalContext = createContext<SubmitModalContextType>({
  isSubmitModalOpen: false,
  setIsSubmitModalOpen: () => { },
  isTimeCounting: false,
  setIsTimeCounting: () => { },
});

export default function TalentCompanyTestLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [data, setData] = useState({ ...dummyTest, started: false });
  const [isSubmitModalOpen, setSubmitModal] = useState<boolean>(false);
  const [isTimeCounting, setIsTimeCounting] = useState<boolean>(false);
  const [progressWebSocketUrl, setProgressURL] = useState<string>("");
  const { assessmentDetails } = useAssessmentDetails(state => ({
    assessmentDetails: state.assessmentDetails,
  }));

  // onStart: true
  // onStart: true
  const handleSocketMessage = (data: any) => {
    console.log('Received data from WebSocket LAYOUT:', data);
    // Handle the incoming data from the WebSocket
  };


  // const {
  //   socket,
  //   connect: socketConnect,
  //   isConnected,
  // } = useWebsocket({
  //   url: progressWebSocketUrl ?? '',
  //   onMessage: handleSocketMessage,
  //   maxRetries: 5,
  //   retryDelay: 1000,
  //   onStart: false, // do not auto-connect
  // });
  // const {
  //   socket,
  //   connect: socketConnect,
  //   isConnected,
  // } = useWebsocket({
  //   url: progressWebSocketUrl ?? '',
  //   onMessage: handleSocketMessage,
  //   maxRetries: 5,
  //   retryDelay: 1000,
  //   onStart: false, // do not auto-connect
  // });

  // useEffect(() => {
  //   const hasData =
  //     !!assessmentDetails?.assessment?.id &&
  //     !!assessmentDetails?.candidate_email;
  // useEffect(() => {
  //   const hasData =
  //     !!assessmentDetails?.assessment?.id &&
  //     !!assessmentDetails?.candidate_email;

  //   if (!hasData || isConnected) return;
  //   if (!hasData || isConnected) return;

  //   const websocketUrl = getProgressWebSocketUrl(
  //     assessmentDetails.assessment.id,
  //     assessmentDetails.candidate_email
  //   );
  //   const websocketUrl = getProgressWebSocketUrl(
  //     assessmentDetails.assessment.id,
  //     assessmentDetails.candidate_email
  //   );

  //   setProgressURL(websocketUrl);
  // }, [assessmentDetails, isConnected]);
  //   setProgressURL(websocketUrl);
  // }, [assessmentDetails, isConnected]);

  // useEffect(() => {
  //   if (progressWebSocketUrl && !isConnected) {
  //     socketConnect();
  //   }
  // }, [progressWebSocketUrl]);
  // useEffect(() => {
  //   if (progressWebSocketUrl && !isConnected) {
  //     socketConnect();
  //   }
  // }, [progressWebSocketUrl]);

  // useEffect(() => {
  //   if (isConnected) {
  //     socket?.send(JSON.stringify({
  //       event: SocketProgressEvent.ACCEPT_INVITE,
  //     }));
  //   }
  //   if (!isConnected && progressWebSocketUrl) {
  //     // Try to reconnect after disconnection
  //     socketConnect();
  //   }
  // }, [isConnected])

  const { sendMessage: sendSocketMessage, isConnected } = useMQTTTrackingService();



  return (
    <div className="relative flex h-screen w-screen flex-col overflow-hidden bg-[#F8F9FB]">
      <SubmitModalContext.Provider
        value={{
          isSubmitModalOpen: isSubmitModalOpen,
          setIsSubmitModalOpen: setSubmitModal,
          isTimeCounting,
          setIsTimeCounting,
        }}
      >
        <Navigation />
        <main className="max-h-full grow overflow-y-scroll" id="main-content">
          {children}
          <div
            className={
              pathname === '/company-test/test' &&
                searchParams.get('page') === 'start-test'
                ? 'hidden'
                : 'absolute bottom-10 left-10'
            }
          >
            <p
              className={
                pathname === '/company-test'
                  ? `text-white`
                  : 'text-black opacity-25 mix-blend-multiply'
              }
            >
              <span className="text-helper-text">POWERED BY </span>Getlinked.AI
            </p>
          </div>
        </main>
      </SubmitModalContext.Provider>
    </div>
  );
}

// @ts-ignore
export const UseTimeCountingContext = () => {
  return useContext(SubmitModalContext);
};
// UseTimeCountingContext.isSubmitModalOpen
// export {TestContext}
