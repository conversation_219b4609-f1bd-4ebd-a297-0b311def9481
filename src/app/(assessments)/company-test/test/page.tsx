'use client';

// MODELS
import { Dialog } from '@headlessui/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useState, useMemo } from 'react';
import shallow from 'zustand/shallow';
import { useAnsweredQuestionsDetails } from '@/app/(assessments)/company-test/misc/store/answeredQuestionsStore';
import { useBooleanStateControl } from '@/hooks';
import { UseGetAssessmentSections } from '../misc/api';
import {
  useAllAssessmentSections,
  useAssessmentDetails,
  useAssessmentSectionsStore,
  UseCandidateStatusStore,
  useProctoringReport,
} from '../misc/store';
import { useProfileImages } from '../misc/store/imageStore';
import { FaceAPIType, InitializedModels } from '../misc/types';
import { PrepTest } from '../misc/utils/prepTest';
// COMPONENTS
import AssessmentCriteria from '../pages/assessment-criteria/AssessmentCriteria';
import IDCheck from '../pages/id-check/IDCheck';
import StartTest from '../pages/start-test/StartTest';
import SystemCheck from '../pages/system-check/SystemCheck';
import TalentIdentification from '../pages/talent-identification/TalentIdentification';
import { useHotjarIdentify } from '@/hooks/useHotjarIdentify';

type PageKey = '1' | '2' | '3' | '4' | '5';

const PAGES: Record<PageKey, string> = {
  '1': 'assessment-criteria',
  '2': 'system-check',
  '3': 'talent-identification',
  '4': 'id-check',
  '5': 'start-test',
};

const Page = () => {
  const user = useMemo(() => {
    const sessionData = sessionStorage.getItem("assessment-details"); // replace with your actual key
    const parsed = sessionData ? JSON.parse(sessionData) : null;

    const assessmentDetails = parsed?.state?.assessmentDetails;
    return assessmentDetails
      ? {
        id: assessmentDetails.invite_id,
        name: assessmentDetails.candidate_name,
        email: assessmentDetails.candidate_email,
        role: assessmentDetails.assessment?.role?.name || "unknown",
      }
      : null;
  }, []);

  useHotjarIdentify(user);

  const searchParams = useSearchParams();
  const [assessmentError, setAssessmentError] = useState<boolean>(false);

  const [currentPage, setCurrentPage] = useState<number>(() => {
    const page = searchParams.get('page');
    let pageKey = Object.entries(PAGES).find(
      ([_, value]) => value == page
    )?.[0];
    return pageKey ? parseInt(pageKey) : 1;
  });
  const router = useRouter();

  /* ===== ==== ==== === ===== ====
      ***** HANDLE ROUTE CHANGES ******
      ===== ===== ===== ===== ==== ==== */
  useEffect(() => {
    const params = new URLSearchParams(searchParams.toString());
    const pageValue = PAGES[currentPage.toString() as PageKey];
    if (pageValue) {
      params.set('page', pageValue);
      router.push(`?${params.toString()}`);
    }
  }, [currentPage, router]);

  const handleNextPage = (page?: number) => {
    if (currentPage == 5) return;
    // console.log(page, 'PAGE');
    // console.log(currentPage, 'CURRENT PAGE');
    if (page && PAGES.hasOwnProperty(page)) {
      setCurrentPage(page);
      return;
    }
    setCurrentPage(prevPage => prevPage + 1);
  };
  const handlePreviousPage = (page?: number) => {
    if (currentPage == 1) return;
    // console.log(page, 'PAGE');
    // console.log(currentPage, 'CURRENT PAGE');
    if (page && PAGES.hasOwnProperty(page)) {
      setCurrentPage(page);
      return;
    }
    setCurrentPage(prevPage => prevPage - 1);
  };

  /* ===== ==== ==== === ===== ==== ====== =
      ***** PREFETCH ASSESSMENT SECTIONS ******
      ===== ===== ===== ===== ==== ==== === === */
  const setCandidateStatus = UseCandidateStatusStore(
    state => state.setCandidateStatus
  );
  const { assessmentId, isAssessmentShuffleQuestions } = useAssessmentDetails(
    state => ({
      assessmentId: state.assessmentDetails.assessment?.id,
      isAssessmentShuffleQuestions:
        state.assessmentDetails.assessment?.is_shuffle,
    })
  );

  const allSections = useAllAssessmentSections();
  const setSection = useAssessmentSectionsStore(state => state.setSection);
  const { data: assessmentSections, isLoading: isLoadingAssessment } =
    UseGetAssessmentSections(assessmentId);
  const { proctoringReportId, resetProctoringReport } = useProctoringReport(
    state => ({
      proctoringReportId: state.proctoringReport.id,
      resetProctoringReport: state.resetProctoringReport,
    }),
    shallow
  );
  useEffect(() => {
    if (assessmentId == '') {
      // alert('Kinldy restart the assessment');
      setAssessmentError(true);
    }
  }, []);

  const previouslyAnsweredQuestions = useAnsweredQuestionsDetails(
    state => state.data
  );

  // GET AND PREP Question SECTIons
  useEffect(() => {
    if (!isLoadingAssessment && allSections.length <= 0 && assessmentSections) {
      // if (!isLoadingAssessment && !allSections.length && assessmentSections) {
      let sections = PrepTest(
        assessmentSections,
        isAssessmentShuffleQuestions as boolean
      );
      //   console.log(sections, ' SECTIONS ');

      if (previouslyAnsweredQuestions.length) {
        const allQuestions = sections.flatMap(section => section.question_set);

        // Find the first question whose ID is not in the previouslyAnsweredQuestions
        const nextClosestQuestionID =
          allQuestions.find(
            question => !previouslyAnsweredQuestions.includes(question.id)
          )?.id ?? null;
        console.log(nextClosestQuestionID, 'NEXT CLOSEST QUESTION IDÏ');

        if (nextClosestQuestionID) {
          console.log('SETTING CURRENT QUESTION ID TO NEXT');
          setCandidateStatus({
            currentQuestionId: String(nextClosestQuestionID),
          });
        } else {
          setCandidateStatus({
            currentQuestionId: sections[0].question_set[0].id,
          });
        }
      } else {
        setCandidateStatus({
          currentQuestionId: sections[0].question_set[0].id,
        });
      }
      setSection(sections);
    }
  }, [
    assessmentSections,
    isLoadingAssessment,
    allSections,
    previouslyAnsweredQuestions,
  ]);

  // useEffect(() => {
  //   console.log(assessmentId, 'ASSESSMENT ID  ');
  //   console.log(proctoringReportId, 'PROPCTORING ID ');
  //   if (!assessmentId) {
  //     router.push('/');
  //   }
  // }, [proctoringReportId]);

  /* ===== ==== ====== =
      ***** LOAD MODELS ******
      ===== ==== ==== === === */

  // ===== FACE API ====
  const profileImages = useProfileImages(state => state.data);
  return (
    <>
      {currentPage == 1 && <AssessmentCriteria handleNext={handleNextPage} />}
      {currentPage == 2 && <SystemCheck handleNext={handleNextPage} />}
      {currentPage == 3 && <TalentIdentification handleNext={handleNextPage} />}
      {currentPage == 4 && (
        <IDCheck
          handleNext={handleNextPage}
          handlePreviousPage={handlePreviousPage}
        />
      )}

      {currentPage == 5 && <StartTest />}

      <Dialog
        open={assessmentError}
        onClose={() => {
          setAssessmentError(false);

          router.push('/');
        }}
      >
        <div className="fixed inset-0 z-30 bg-[#000000aa]">
          <Dialog.Panel
            className={
              'absolute left-1/2 top-1/2 flex h-[19.5rem] w-[30rem] -translate-x-1/2 -translate-y-1/2 flex-col rounded-2xl bg-white'
            }
          >
            <>
              <div className="flex h-[64px] items-end justify-between rounded-t-2xl bg-[#450A0A] px-10 py-[0.87rem] text-white">
                <p>Assessment error</p>
              </div>

              <>
                <div className="flex h-full flex-col items-start justify-start p-5">
                  <p className="text-left text-xl font-medium text-[#0E0E2C]">
                    Kindly restart your assessment
                  </p>
                </div>
              </>

              <div className="flex h-[5rem] justify-end gap-x-4 rounded-b-2xl bg-[#FEF2F2] p-5 font-light">
                <button
                  className="rounded-md bg-white p-2 px-3 text-[#450A0A]"
                  onClick={() => {
                    setAssessmentError(false);
                    router.push('/');
                  }}
                >
                  Close
                </button>
              </div>
            </>
          </Dialog.Panel>
        </div>
      </Dialog>
    </>
  );
};

export default Page;
