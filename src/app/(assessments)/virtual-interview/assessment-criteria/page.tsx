'use client';

import moment from 'moment';
import Link from 'next/link';
import React, { useEffect, useState } from 'react';
import { LoadingOverlay } from '@/components/shared';
import { InterviewDataProps, useInterviewDetails } from '../../company-test/misc/store/interviewStore';

const page = () => {
  const assessmentDetails = useInterviewDetails((state) => state.data);
  const [pageData, setPageData] = useState<InterviewDataProps>();
  const [time_limit,setTimeLimit] =  useState(0)

  useEffect(() => {
    setPageData(assessmentDetails!);
    setTimeLimit(assessmentDetails?.interview?.questions?.reduce((acc,question)=>{acc += question.time ;return acc}, 0) || 0)
  }, []);


  return (
    <div className="relative flex items-center justify-center px-24 py-20 pt-10">
      <div className="grid h-full w-[80%] grid-cols-2 rounded-2xl bg-white p-5">
        <div className="flex h-[100%] flex-col overflow-y-scroll p-5">
          <div className="h-[80px]">
            <p className="py-1 text-2xl font-semibold">
              Hello {pageData?.candidate_name} 👋
            </p>
          </div>
          <div className="h-[100px]">
            <p className="py-2 text-lg font-medium">
              You have{' '}
                <span className="text-primary">
                  {moment
                    .utc(time_limit * 1000)
                    .format(`${(time_limit || 0) >= 3600000 ? 'HH [hours and ] ' : '' } mm [minutes]`)}
                </span>
              {' '}
              to answer{' '}
              <span className="text-primary">
                {pageData?.interview?.total_questions}
              </span>{' '}
              Questions
            </p>
          </div>
        </div>

        <div className="flex h-[100%] flex-col overflow-hidden p-5">
          <div className="h-full overflow-y-scroll p-5">
            <p className="py-1 text-xl font-semibold">Interview Criteria</p>
            <ul className="list-disc p-4 text-sm font-light">
              <li className="py-1">
                <p>
                  Please note that you cannot pause or retake the test. We
                  strongly advise you to proceed with the assessment in a quiet
                  environment.
                </p>
              </li>
              <li className="py-1">
                <p>
                  You are required to utilize a gadget equipped with a webcam
                  for this interview.
                </p>
              </li>
              <li className="py-1">
                <p>
                  You will be required to provide a Government issued ID e.g
                  Voters card, NIN, Drivers licence, Intl passport.
                </p>
              </li>
              <li className="py-1">
                <p>
                  You are required to take this interview in a well lighted
                  room
                </p>
              </li>
              <li className="py-1">
                <p>
                  If you encounter a technical issue, you have the option to
                  refresh your browser at any point during the test. Rest
                  assured, your answers will be saved.
                </p>
              </li>
              <li className="py-1">
                <p>
                  Your responses will be automatically saved when the allotted
                  time for the task expires.
                </p>
              </li>
            </ul>

            <p className="py-1 text-xl font-semibold">
              Proctoring Instructions
            </p>
            <ul className="list-disc p-4 text-sm font-light">
              <li className="py-1">
                <p>
                  Do not attempt to open the test in multiple tabs
                  simultaneously.
                </p>
              </li>
              <li className="py-1">
                <p>
                  Stay on the interview page during the test, and refrain from
                  switching to other tabs or windows unless instructed.
                  Tab/window switches are reported to the hiring team.
                </p>
              </li>
              <li className="py-1">
                <p>
                  Do not copy and paste content from external sources. Any such
                  incidents will be detected and reported to the hiring team.
                </p>
              </li>
              <li className="py-1">
                <p>
                  Ensure your webcam is on through out the interview session
                </p>
              </li>
            </ul>
          </div>
          <div className="h-[80px] p-5">
            <Link href={'/virtual-interview/system-check'}>
              <button className="min-w-[150px] rounded-md bg-primary p-3 text-white">
                Proceed
              </button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default page;
