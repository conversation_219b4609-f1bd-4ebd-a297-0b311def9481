'use client';

import Link from 'next/link';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useNetworkState } from 'react-use';
import Webcam from 'react-webcam';
import { cn } from '@/utils';
import ProgressBar from '../../company-test/misc/components/ProgressBar';
import DangerIcon from '../../company-test/misc/icons/DangerIcon';
import LightingIcon from '../../company-test/misc/icons/LightingIcon';
import MicIcon from '../../company-test/misc/icons/MicIcon';
import TickIcon2 from '../../company-test/misc/icons/TickIcon2';
import WebcamIcon from '../../company-test/misc/icons/WebcamIcon';
import WifiIcon from '../../company-test/misc/icons/WifiIcon';
import { useProfileImages } from '../../company-test/misc/store/imageStore';
import {
  NetworkStoreProps,
  useNetworkDetails,
} from '../misc/store/networkStore';

import * as faceDetection from '@tensorflow-models/face-detection';
import * as tf from '@tensorflow/tfjs'; // Required for TF models
import ReactLoading from 'react-loading';

interface systemCheckProps {
  name: string;
  controller: boolean;
  icon: React.ReactNode;
  errMsg: string;
}

const page = () => {
  const profileImages = useProfileImages(state => state.data);
  const setProfileImages = useProfileImages(state => state.setProfileImages);
  const webcamRef = useRef<any>(null);
  const [imgSrc, setImgSrc] = useState<null | string>(); // initialize it
  const [cameraError, setCameraError] = useState<string | null>();

  const online = useNetworkDetails(
    (state: NetworkStoreProps) => state.data.online
  );
  const setOnline = useNetworkDetails(
    (state: NetworkStoreProps) => state.setNetworkDetails
  );

  const capture = useCallback(() => {
    const imageSrc = webcamRef.current.getScreenshot();

    setImgSrc(imageSrc);
    setProfileImages({
      profilePicture: imageSrc as string,
      idPicture: '',
    });
  }, [webcamRef]);

  const retake = () => {
    setImgSrc(null);
    setProfileImages({
      profilePicture: '',
      idPicture: '',
    });
  };


  const checkCamera = async () => {
    try {
      await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
      return true;
    } catch (error: any) {
      if (error.name === 'NotAllowedError') {
        setCameraError(
          "Camera Permission Denied. If you didn't do that, refresh the page and try again"
        );
      } else if (
        error.name === 'NotFoundError' ||
        error.name === 'DevicesNotFoundError'
      ) {
        setCameraError(
          'Check your device. No camera found or no camera devices available.'
        );
      } else if (
        error.name === 'NotReadableError' ||
        error.name === 'TrackStartError'
      ) {
        setCameraError(
          'Camera is being used by another application or a hardware issue.'
        );
      } else if (
        error.name === 'InactiveStreamError' ||
        error.code === 'InactiveStreamError'
      ) {
        setCameraError('Your camera or microphone device is inactive.');
      } else {
        console.log('getUserMedia error:', error);
      }
      return false;
    }
  };
  var network = useNetworkState();

  useEffect(() => {
    if (network.online) {
      setOnline(1); // 1 = online
    } else {
      setOnline(0); // 0 = offline
    }

    checkCamera();
  }, [network]);

  // const networkState = useNetworkState();

  // const checkNetworkState = () => {
  //   if (
  //     // networkState.effectiveType === '3g' ||
  //     networkState.effectiveType === '4g'
  //   ) {
  //     setIsInternetGood(true);
  //   }
  // };

  // useEffect(() => {
  //   const intervalId = setTimeout(() => {
  //     const networkState = useNetworkState();
  //     // For example, increment the value
  //     console.log(networkState);
  //   }, 2000); // Set the interval in milliseconds (e.g., every 2 seconds)

  //   // Cleanup function to clear the timeout when the component unmounts
  //   return () => clearTimeout(intervalId);
  // }, []);

  const systemChecks: systemCheckProps[] = [
    {
      name: 'Webcam',
      controller: cameraError ? true : false,
      icon: <WebcamIcon />,
      errMsg: 'Your webcam is not working',
    },
    {
      name: 'Network',
      controller: !online,
      icon: <WifiIcon />,
      errMsg: 'Your network is unstable',
    },
    {
      name: 'Microphone',
      controller: false,
      icon: <MicIcon />,
      errMsg: 'Your microphone is not working',
    },
    {
      name: 'Lighting',
      controller: true,
      icon: <LightingIcon />,
      errMsg: 'Make sure you are in a well lit area',
    },
  ];
  const [lightSufficient, setLightSuffuciency] = useState<boolean>(false);

  const [lightLoading, setLightLoading] = useState<boolean>(true);
  useEffect(() => {

    let interval: NodeJS.Timeout;
    const init = async () => {
      await tf.setBackend('webgl');
      await tf.ready();

      const faceModel = await faceDetection.createDetector(
        faceDetection.SupportedModels.MediaPipeFaceDetector,
        {
          runtime: 'tfjs',
          maxFaces: 1,
          modelType: 'short'
        }
      );

      const detectFace = async () => {
        if (!webcamRef.current) return;
        const faces = await faceModel.estimateFaces(webcamRef?.current.video);
        // console.log(faces.length, "FACESc")
        if (faces.length == 1) {
          setLightSuffuciency(true);
        } else {
          setLightSuffuciency(false);
        }
      }
      setLightLoading(false);
      detectFace()
      interval = setInterval(detectFace, 300)
      console.timeEnd('init');

    }

    init().catch(console.error);
    return () => { clearInterval(interval) }



  }, [webcamRef]);


  return (
    <div className="relative flex h-max justify-center px-5 pb-20 pt-10 lg:px-10 xl:px-20">
      <div className="flex h-max w-full flex-col rounded-2xl bg-white p-10 md:w-[95%] lg:w-[90%] xl:w-[70%]">
        <div>
          <p className="text-xl font-medium">System Check</p>
          <p className="py-4 text-body-text">
            We utilize your camera image to ensure fairness for all
            participants, and we also employ both your camera and microphone for
            a video questions where you will be prompted to record a response
            using your camera or webcam, so it's essential to verify that your
            camera and microphone are functioning correctly and that you have a
            stable internet connection. To do this, please position yourself in
            front of your camera, ensuring that your entire face is clearly
            visible on the screen. This includes your forehead, eyes, ears,
            nose, and lips. You can initiate a 5-second recording of yourself by
            clicking the button below.
          </p>
        </div>
        <div className="mt-4 flex h-full w-full gap-x-8">
          <div className="w-[50%]">
            {imgSrc ? (
              <img
                src={imgSrc}
                alt="image"
                className="border-primary` w-full rounded-xl border"
              />
            ) : (
              <Webcam
                className={`w-full rounded-xl border border-primary`}
                audio
                ref={webcamRef}
                mirrored={true}
                muted
                screenshotFormat="image/png"
              />
            )}
          </div>
          <div className="grid h-[80%] w-[50%] grid-cols-2 gap-4">
            {systemChecks.map((item: systemCheckProps, index: number) => {
              return (
                <div
                  key={index}
                  className="relative flex h-full w-full items-center justify-center rounded-md bg-primary-light p-3 text-primary"
                >
                  <div className="flex h-full w-full flex-col items-center justify-center relative">
                    <div className="relative">
                      <ProgressBar
                        data={[
                          { name: 'L1', value: item.controller ? 45 : 100 },
                        ]}
                        outerRadius={'60%'}
                        innerRadius={'55%'}
                        progressColor={
                          (item.name.toLowerCase() == "lighting" && lightSufficient) ?
                            '#755AE2' :
                            item.controller ?  '#bf2626':'#755AE2' 
                        }
                      />
                      <div className="absolute left-1/2 top-1/2 h-full w-full">
                        {(item.name.toLowerCase() == "lighting" ? lightSufficient : !item.controller) ? (
                          <TickIcon2 className="h-[50%] w-[50%] -translate-x-1/2 -translate-y-1/2" />
                        ) : (
                          <DangerIcon className="h-[35%] w-[35%] -translate-x-1/2 -translate-y-1/2" />
                        )}
                      </div>
                      {(item.name.toLowerCase() == "lighting" && lightLoading) && <div
                        className={cn(
                          'absolute left-1 top-1 flex h-6 w-6 items-center justify-center rounded-full bg-danger-dark',
                          { 'bg-primary': lightSufficient }
                        )}
                      >
                        <ReactLoading
                          height={18}
                          width={18}
                          type="spin"
                          color="#fff"
                        />
                      </div>
                      }

                    </div>
                    <p className={cn({ 'text-danger-dark': item.name.toLowerCase() == "lighting" ? !lightSufficient : item.controller })}>
                      {item.name}
                    </p>
                  </div>
                  <div className={cn(
                      'absolute right-1 top-1 flex h-6 w-6 items-center justify-center rounded-full bg-primary',
                      { 'bg-danger-dark': item.name.toLowerCase() == "lighting" ? !lightSufficient : item.controller }
                    )}
                  >
                    {item.icon}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        <div className="flex gap-x-4 py-4">
          {imgSrc ? (
            <button
              className="flex min-w-[200px] items-center justify-center rounded-md bg-primary p-3 text-white"
              onClick={retake}
            >
              Retake
            </button>
          ) : (
            <button
              className="min-w-[200px] rounded-md bg-primary p-3 text-white"
              onClick={capture}
              disabled={cameraError || !lightSufficient ? true : false}
            >
              Take picture
            </button>
          )}
          {imgSrc && (
            <Link href={'/virtual-interview/talent-identification'}>
              <button className="flex min-w-[200px] items-center justify-center rounded-md bg-primary-light p-3 text-primary">
                Continue
              </button>
            </Link>
          )}
        </div>
      </div>
    </div>
  );
};

export default page;
