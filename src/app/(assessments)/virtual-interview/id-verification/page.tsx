'use client';

// import CustomWebcam from '../components/CustomWebcam';
import { useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import toast from 'react-hot-toast';
import { BarLoader } from 'react-spinners';
import { LoadingOverlay } from '@/components/shared';
import { useVerifyInterviewWithNIN } from '../../company-test/misc/api/sendProctoringReport';
import Retry<PERSON>ithNIN from '../../company-test/misc/components/RetryWithNIN';
import VerifyIDErrorModal from '../../company-test/misc/components/VerifyIDErrorModal';
import { useProfileImages } from '../../company-test/misc/store/imageStore';
import { useInterviewDetails } from '../../company-test/misc/store/interviewStore';
import { useProctoringReport } from '../../company-test/misc/store/proctoringReportStore';
import { useCheckIDVerificationStatus } from '../misc/api/uploadImages';

const page = () => {
  const router = useRouter();
  const interview_details = useInterviewDetails(state => state.data);
  const profileImages = useProfileImages(state => state.data);
  const [upload_error, setUploadError] = useState('');
  const { proctoringReport, setProctoringReport, setProctoringId } =
    useProctoringReport();
  const [pendingVerificationError, setPendingVerificationError] = useState('');

  const [is_changing_pages, setIsChangingPages] = useState(true);

  useEffect(() => {
    setIsChangingPages(false);
  }, []);

  const reformatProctoringData = (data: any) => {
    let payload = { ...data };

    if (typeof payload.bulk_proctor_option == 'object') {
      payload.bulk_proctor_option = Object.entries(
        payload.bulk_proctor_option
      ).map(i => ({
        key: i[0],
        value: i[1],
      }));
    } else {
      payload.bulk_proctor_option = [...payload.bulk_proctor_option];
    }
    return payload;
  };

  const intervalRef = useRef<ReturnType<typeof setInterval> | null>(null);

  const [checkingProctorStatus, setCheckingProctorStatus] = useState(false);
  const {
    mutate: verifyInterviewWithNIN,
    isLoading: verifyInterviewWithNINLoading,
  } = useVerifyInterviewWithNIN();
  const {
    mutate: checkIDVerificationStatus,
    isLoading: checkIDVerificationStatusLoading,
  } = useCheckIDVerificationStatus();
  const beginProctorStatusCheck = async (proctorID: string) => {
    if (!proctorID) {
      return;
    }
    const runProctor = () => {
      setCheckingProctorStatus(true);

      checkIDVerificationStatus(proctorID, {
        onSuccess: (responsePayload: any) => {
          const { data } = responsePayload;

          if (
            data.verification_status === 'pending' &&
            data.message ===
              'Verification failed, Probably your face do not match with the ID provided!'
          ) {
            setPendingVerificationError(data.message);
          }

          if (data.verification_status === 'success') {
            let proctorData = { ...proctoringReport, ...data.id };
            setProctoringReport(reformatProctoringData(proctorData));

            setProctoringId(data.id);
            setIsChangingPages(true);
            setIsNINVerificationInProgress(false);

            if (intervalRef.current) {
              clearInterval(intervalRef.current);
            }
            router.push('/virtual-interview/start-test');
          } else if (
            data.verification_status === 'failed' ||
            data.verification_status === 'error'
          ) {
            toast.error('ID verification failed');
            setIsNINVerificationInProgress(false);
            if (is_verify_id_error_modal_open) {
              router.push('/virtual-interview/talent-identification');
            } else {
              setIsVerifyIDErrorModalOpen(true);
            }
          }
        },
        onError: (err: any) => {
          setCheckingProctorStatus(false);
          setIsNINVerificationInProgress(false);

          console.log(err);

          let error =
            err?.response?.data?.error || err.message || 'Something went wrong';
          console.log(error);
          setIsVerifyIDErrorModalOpen(true);
          setUploadError(error);

          if (intervalRef.current) {
            clearInterval(intervalRef.current);
          }
        },
      });
    };

    intervalRef.current = setInterval(runProctor, 10000);
  };

  useEffect(() => {
    if (proctoringReport.id) {
      beginProctorStatusCheck(proctoringReport?.id);
    }
    return () => {
      clearInterval(intervalRef.current!);
    };
  }, [proctoringReport]);

  // NIN Retry
  const [is_verify_id_error_modal_open, setIsVerifyIDErrorModalOpen] =
    useState(false);
  const [retry_with_nin_modal, setRetryWithNINModal] = useState(false);
  // const { mutate: verifyWithNIN, isLoading: verifyLoading } =
  //   useVerifyWithNIN();
  const [niNumber, setNINumber] = useState<string>('');
  const [isNINVerificationInProgress, setIsNINVerificationInProgress] =
    useState(false);

  const handleVerificationWithNIN = () => {
    if (niNumber.trim() === '') {
      toast.error('Invalid NIN');

      return;
    }
    if (niNumber.trim().length !== 11) {
      toast.error('Invalid NIN');
      return;
    }

    setIsNINVerificationInProgress(true);

    const payload = {
      candidate_email: interview_details?.candidate_email || '',
      interview: interview_details?.interview.id || '',
      result_id: interview_details?.result_id || '',
      profile_photo: profileImages?.profilePicture as string,
      nin: niNumber,
    };

    verifyInterviewWithNIN(payload, {
      onSuccess: (responsePayload: any) => {
        const { data } = responsePayload;
        setProctoringId(data.verification_id);

        if (data.status === 'success') {
          let proctorData = { ...proctoringReport, ...data.id };

          setProctoringId(data.id);
          setIsChangingPages(true);

          if (intervalRef.current) {
            clearInterval(intervalRef.current);
          }
          router.push('/virtual-interview/start-test');
        } else if (data.status === 'failed') {
          toast.error('ID verification failed');
          setIsNINVerificationInProgress(false);
        }
      },
      onError: (err: any) => {
        console.log(err, 'ERROR');
        setCheckingProctorStatus(false);
        setIsNINVerificationInProgress(false);

        let error =
          err?.response?.data?.error || err.message || 'Something went wrong';
        console.log(error);
        setIsVerifyIDErrorModalOpen(true);

        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      },
    });
  };

  const retryWithUpload = () => {
    setIsVerifyIDErrorModalOpen(false);
    router.push('/virtual-interview/talent-identification');
  };

  const closeModal = () => {
    setRetryWithNINModal(false);
  };

  return (
    <div className=" relative flex items-center justify-center gap-8 px-20 py-10 pb-20">
      <div className=" flex h-[90%] w-[60%]  items-center justify-center  overflow-y-scroll rounded-2xl bg-white p-5 2xl:w-[70%]">
        <div className="flex-column flex w-1/2 flex-col items-center justify-center gap-4">
          <div className="m-4 flex h-[100px] w-[100px] items-center justify-center rounded-full bg-primary-light p-4">
            <svg
              width="36"
              height="32"
              viewBox="0 0 36 32"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M31.2219 9.4779H22.3041V0.40863H13.3636V9.4779H4.44583C2.33825 9.4779 0.623535 11.193 0.623535 13.3006V27.9838C0.623535 30.0918 2.33825 31.8065 4.44583 31.8065H31.2223C33.3299 31.8065 35.0446 30.0918 35.0446 27.9838V13.3006C35.0446 11.193 33.3295 9.4779 31.2219 9.4779ZM14.0983 1.14334H21.569V9.4779V10.7738H14.0983V9.4779V1.14334ZM34.3095 27.9838C34.3095 29.6867 32.9245 31.0718 31.2219 31.0718H4.44583C2.74328 31.0718 1.35824 29.6867 1.35824 27.9838V13.3006C1.35824 11.598 2.74328 10.2126 4.44583 10.2126H13.3636V11.5086H22.3041V10.2126H31.2223C32.9249 10.2126 34.3099 11.5976 34.3099 13.3006V27.9838H34.3095Z"
                fill="#755AE2"
              />
              <path
                d="M20.7131 2.83197H14.9543V3.56668H20.7131V2.83197Z"
                fill="#755AE2"
              />
              <path
                d="M20.7131 5.35138H14.9543V6.08609H20.7131V5.35138Z"
                fill="#755AE2"
              />
              <path
                d="M20.7131 7.87115H14.9543V8.60586H20.7131V7.87115Z"
                fill="#755AE2"
              />
              <path
                d="M13.228 23.4585C13.1546 21.2713 12.0651 19.327 10.4611 18.4184C11.0215 17.885 11.3728 17.1346 11.3728 16.3014C11.3728 14.6879 10.06 13.3755 8.44651 13.3755C6.83344 13.3755 5.52062 14.6879 5.52062 16.3014C5.52062 17.1342 5.87228 17.8846 6.43234 18.4184C4.8283 19.3266 3.73879 21.2713 3.6654 23.4585L3.65088 23.9051H13.2421L13.228 23.4585ZM8.44651 14.2397C9.5835 14.2397 10.509 15.1648 10.509 16.3018C10.509 17.4388 9.58389 18.3642 8.44651 18.3642C7.30991 18.3642 6.38445 17.4392 6.38445 16.3018C6.38445 15.1648 7.30991 14.2397 8.44651 14.2397ZM4.56024 23.0417C4.75216 21.2273 5.73177 19.6759 7.1015 19.0404C7.93394 19.4737 8.96104 19.4737 9.7923 19.0404C11.1616 19.6759 12.1413 21.2273 12.3336 23.0417H4.56024Z"
                fill="#755AE2"
              />
              <path
                d="M28.5045 15.7245H18.7146V16.588H28.5045V15.7245Z"
                fill="#755AE2"
              />
              <path
                d="M28.5045 19.4677H18.7146V20.3311H28.5045V19.4677Z"
                fill="#755AE2"
              />
              <path
                d="M26.2014 26.378H11.3557V27.2414H26.2014V26.378Z"
                fill="#755AE2"
              />
            </svg>
          </div>

          <div className="w-full rounded-lg bg-[#F5F3FF] p-3  ">
            <BarLoader
              color="#755AE2"
              className="rounded-lg"
              height="15"
              width="100%"
            />
          </div>

          <h2 className="text-center text-xl font-bold text-primary">
            Identity verification ongoing...
          </h2>
          <p className="py-4 text-center text-body-text">
            We're currently verifying your ID to ensure a secure and fair
            process. Please stay on this page and you’ll be automatically moved
            forward once verification is complete. Thank you for your patience!
          </p>
          {/* THIS WAS REMOVED BECAUSE THE VERIFICATION IS STILL PENDING BUT THE MESSAGE BEING DISPLAYED SAYS IT FAILED */}
          {/* {pendingVerificationError && (
            <p className="text-center text-yellow-500">
              {pendingVerificationError}
            </p>
          )} */}
        </div>
      </div>
      <VerifyIDErrorModal
        is_open={is_verify_id_error_modal_open}
        handleRetake={() => retryWithUpload()}
        message={upload_error}
        nin={niNumber}
        setNin={setNINumber}
        isLoading={
          checkIDVerificationStatusLoading ||
          verifyInterviewWithNINLoading ||
          isNINVerificationInProgress
        }
        handleNINSubmit={handleVerificationWithNIN}
      />{' '}
      <RetryWithNIN
        closeModal={closeModal}
        is_open={retry_with_nin_modal}
        handleRetake={handleVerificationWithNIN}
        message={upload_error}
        nin={niNumber}
        setNin={setNINumber}
        isLoading={
          checkIDVerificationStatusLoading || verifyInterviewWithNINLoading
        }
      />
      <LoadingOverlay isOpen={is_changing_pages} />
      {/* ERROR DIALOG */}
    </div>
  );
};

export default page;
