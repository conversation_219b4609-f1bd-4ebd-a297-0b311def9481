'use client'

import { getPersonalityTestQuestions } from "@/app/(website-main)/t/showcase/misc/api"
import { AssesmentPageHeader, VideoInterview } from "@/app/(website-main)/t/showcase/misc/components/assessment";
import { Loader } from '@/components/shared';
import { toast } from 'react-toastify';
import { useStartTime } from "../misc/store/start-time";
import { statusStoreProps, useStatusDetails } from "../misc/store/candidate-status";
import { useStartInterview } from "../misc/api/startAssessment";
import { useEffect, useState } from "react";
import { InterviewDataProps, useInterviewDetails } from "../../company-test/misc/store/interviewStore";
import { NetworkStoreProps, useNetworkDetails } from "../misc/store/networkStore";
import { useNetworkState } from "react-use";
import { useSendCandidateAnswers } from "../misc/api/sendAnswers";
import { useSubmitInterview } from "../misc/api/submit-interview";
import { OfflineAnswersStoreProps, useOfflineAnswersDetails } from "../misc/store/offlineAnswers";
import ReactLoading from "react-loading";
import moment from "moment";
import Countdown from "../misc/components/CountDownTimer";
import { cn } from "@/utils";


function PageLayout({ children, title = "Interview" }: { children: React.ReactNode, title?: string }) {
  return (

    <div className='maincontent h-full bg-primary-light'>
      <AssesmentPageHeader title={title} />
      {children}
    </div>
  )
}

export default function StartInterviewPage() {

  const isLoading = false
  const isError = false

  const [isAssessmentActivated, setIsAssessmentActivated] = useState(false);

  const [pageData, setPageData] = useState<InterviewDataProps>();
  const assessmentDetails = useInterviewDetails((state) => state.data);
  const [has_loaded_details, setHasLoadedDetails] = useState(false)
  const setInterviewDetails = useInterviewDetails((state) => state.setInterviewDetails);

  useEffect(() => {
    setPageData(assessmentDetails!);
    if (assessmentDetails) {
      setHasLoadedDetails(true)
    }
  }, [assessmentDetails]);

  const setCandidateStatus = useStatusDetails(
    (state: statusStoreProps) => state.setStatusDetails
  );



  const setStartTime = useStartTime(
    (state: any) => state.setStartTime
  );

  const candidateStatus = useStatusDetails(
    (state: statusStoreProps) => state.data.candidateStatus
  );

  const online = useNetworkDetails(
    (state: NetworkStoreProps) => state.data.online
  );
  const setOnline = useNetworkDetails(
    (state: NetworkStoreProps) => state.setNetworkDetails
  );

  const offlineAnswers = useOfflineAnswersDetails(
    (state: OfflineAnswersStoreProps) => state.data
  );
  const setofflineAnswers = useOfflineAnswersDetails(
    (state: OfflineAnswersStoreProps) => state.setOfflineAnswersDetails
  );

  useEffect(() => {
    if (offlineAnswers.length > 0 && online) {
      sendAnswer(offlineAnswers, {
        onSuccess: () => {
          setofflineAnswers([]);
        },
      });
    }
  }, [online]);

  var networkState = useNetworkState();

  useEffect(() => {
    if (networkState.online) {
      setOnline(1);
    } else {
      setOnline(0);
    }
  }, [networkState]);


  const { mutate: startInterview, isLoading: startLoading } = useStartInterview()

  const { mutate: sendAnswer, isLoading: sendLoading } =
    useSendCandidateAnswers();

  const { mutate: runSubmitAssessment, isLoading: submitLoading } =
    useSubmitInterview();

  function submitInterview() {
    return new Promise((resolve, reject) => {
      runSubmitAssessment(
        {
          candidate_email: assessmentDetails!.candidate_email,
          invite_id: assessmentDetails!.invite_id,
          interview_id: assessmentDetails!.interview.id,
          result_id: assessmentDetails!.result_id,
          candidate_name: assessmentDetails!.candidate_name,
        },
        {
          onSuccess: data => {

            localStorage.clear();
            sessionStorage.clear();

            if (data.status === 200) {
              setIsSubmitModalOpen(true)
            } else {
              toast.error(data.data.error.message);
            }
            resolve(data)
          },
          onError: (error: any) => {
            toast.error(error.message as string);
            reject(error)
          },
        }
      );
    })
  }

  function handleTimeOutFromCounter() {
    if (!isAssessmentActivated) {
      submitInterview()
    }
  }

  const startTest = () => {
    startInterview(
      {
        email: pageData?.candidate_email as string,
        invite: pageData?.invite_id as string,
        interview_id: pageData?.interview?.id as string,
      },
      {
        onSuccess: (data: any) => {
          //reset Proctoring flag for when the user just starts the assessment so that old screenshots and flags are cleared if they exist
          /* resetProctoringFlags().. */

          setCandidateStatus(2)
          setInterviewDetails({ ...assessmentDetails!, started: true });
          setStartTime(Date.now())
          setIsAssessmentActivated(true);
        },
        onError: (err: any) => { },
      }
    );
  };

  const [isSubmitModalOpen, setIsSubmitModalOpen] = useState(false);

  const handleNavigation = (
    data: { question: string; video: string; },
    { is_last }: { is_last: boolean, } = { is_last: false }
  ) => {
    if (online) {
      if (is_last) {
        sendAnswer({
          result_id: assessmentDetails!.result_id,
          candidate: assessmentDetails!.candidate_email,
          question: data.question,
          video: data.video.split("base64,")[1],
        }, {
          onSuccess: () => {
            setIsSubmitModalOpen(true);
          },
          onError: () => {
            // toast.error('failed to save answer');
          },
        });
      } else {
        sendAnswer({
          result_id: assessmentDetails!.result_id,
          candidate: assessmentDetails!.candidate_email,
          question: data.question,
          video: data.video.split("base64,")[1],
        }, {
          onSuccess: () => {
          },
          onError: () => {
            // toast.error('failed to save answer');
          },
        });
      }
    } else {
      setofflineAnswers([...offlineAnswers, data]);
      if (is_last) {
        setIsSubmitModalOpen(true);
      } else {
      }
    }
  };

  if (!has_loaded_details) {
    return (
      <PageLayout>
        <Loader />
      </PageLayout>
    )
  }

  if (!assessmentDetails) {
    return (
      <PageLayout>
        An error occured while loading the interview
      </PageLayout>
    )
  }

  if (!assessmentDetails.interview.questions.length) {
    return (
      <PageLayout>
        This interview currently have no question
      </PageLayout>
    )
  }

  const questions = assessmentDetails?.interview.questions



  return (
    <PageLayout title={assessmentDetails?.interview.name}>
      <div
        className={`col-span-6 flex h-full items-center justify-center rounded-2xl bg-white transition-all ${pageData?.started && isAssessmentActivated ? 'hidden' : 'block'
          }`}
      >
        {startLoading ? (
          <div className="flex h-full w-full items-center justify-center p-10">
            <ReactLoading
              className="z-20"
              type="spin"
              color="#3C1356"
              width={50}
              height={50}
            />
          </div>
        ) : (
          <div className="w-[70%] bg-primary-light p-8">
            {candidateStatus === 1 ? (
              <p className="py-2 text-center text-primary">
                Ready to start? Click the ‘Start Interview’ button below
              </p>
            ) : (
              <div>
                <p>
                  Assessment Time:{' '}
                  <span className="font-bold">
                    {moment
                      .utc(pageData?.interview?.questions?.reduce((acc, que) => { acc += que.time; return acc }, 0) || 1 * 1000)
                      .format('HH [hours and ] mm [minutes]')}
                  </span>
                </p>
                <div className="flex items-center gap-x-4">
                  Time Left:{' '}
                  <span>
                    <Countdown handleSubmit={handleTimeOutFromCounter} />
                  </span>
                </div>
              </div>
            )}
            <button
              className="w-full rounded-md bg-primary p-3 text-white"
              onClick={startTest}
            >
              {candidateStatus === 1
                ? 'Start Interview'
                : 'Resume Interview '}
            </button>
          </div>
        )}
      </div>

      <VideoInterview
        type="interview"
        loading={!Boolean(pageData?.started && isAssessmentActivated)}
        questions={questions}
        onNavigate={handleNavigation}
        onFinish={submitInterview}
      />

    </PageLayout>
  )
}
