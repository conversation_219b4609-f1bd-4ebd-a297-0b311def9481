import { useMutation } from '@tanstack/react-query';
import axios from 'axios';


export const uploadProfileImages = async (data: {
    candidate_email: string;
    result_id: string;
    photo_id: string;
    interview: string;

    profile_photo: string;
}) => {
    const response = await axios.post(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/assessments/interview-verification/`,
        data
    );
    return response.data;
};

export const useUploadProfileImages = () => {
    return useMutation({
        mutationFn: uploadProfileImages,
        mutationKey: ['uploadProfileImages'],
    });
};



const checkIDVerificationStatus = async (id: string) => {
    const response = await axios.get(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/assessments/interview-verification/status/${id}/`
    );
    return response.data;
};
export const useCheckIDVerificationStatus = () => {
    return useMutation({
        mutationFn: checkIDVerificationStatus,
        mutationKey: ['checkIDVerificationStatus'],
    });
};