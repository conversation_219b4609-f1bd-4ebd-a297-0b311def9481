import { useMutation } from '@tanstack/react-query';
import axios from 'axios';


export interface CandidateAnswerProps {
  candidate: string;
  question: string;
  result_id: string;
  video: string;
  /* invite_id: string; */
  /* section_id: string; */
}
export const sendCandidateAnswer = async (
  data: CandidateAnswerProps | CandidateAnswerProps[]
) => {
  const response = await axios.post(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/assessments/interview-answers/`,
    data
  );
  return response.data;
};

export const useSendCandidateAnswers = () => {
  return useMutation({
    mutationFn: sendCandidateAnswer,
    mutationKey: ['sendCandidateInterviewAnswer'],
  });
};
