import { useMutation } from '@tanstack/react-query';
import axios from 'axios';


export const startInterview = async ({
    email,
    invite,
    interview_id,
}: {
        email: string;
        invite: string;
        interview_id: string;
    }) => {
    const response = await axios.post(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}/assessments/mark-invite/${invite}/accept/`,
        {
            candidate_email: email,
            interview_id: interview_id,
            is_interview:true,
        }
    );
};

export const useStartInterview = () => {
    return useMutation({
        mutationFn: startInterview,
        mutationKey: ['startInterview'],
    });
};
