import { useMutation } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { VideoInterviewCandidateData } from '../types';

interface acceptInviteDTO {
    invitation_token: string
}

const acceptInvite = async (token: string) => {
    const response = await Axios.post(
        '/assessments/accept-interview/',
        {invitation_token:token},

    );
    return response.data as VideoInterviewCandidateData;
};

const useAcceptInterviewInvite = () => {

    return useMutation({
        mutationFn: acceptInvite
    });
};

export default useAcceptInterviewInvite
