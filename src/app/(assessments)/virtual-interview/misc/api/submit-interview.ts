import { useMutation } from '@tanstack/react-query';
import axios from 'axios';


export const submitInterview = async ({
  candidate_email,
  interview_id,
  result_id,
  candidate_name,
  invite_id,
}: {
  candidate_email: string;
  interview_id: string;
  result_id: string;

  candidate_name: string;
  invite_id: string;
}): Promise<any> => {
  const response = await axios.post(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/assessments/interview-submit/`,
    {
      candidate_email,
      interview_id,
      result_id,

      candidate_name,
      invite_id,
    }
  );
  return response;
};

export const useSubmitInterview = () => {
  return useMutation({
    mutationFn: submitInterview,
  });
};
