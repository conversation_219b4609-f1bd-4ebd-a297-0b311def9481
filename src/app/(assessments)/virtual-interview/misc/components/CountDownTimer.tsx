import { useRouter } from 'next/navigation';
import React, { useContext, useEffect, useState } from 'react';
import _Countdown from 'react-countdown';
import toast from 'react-hot-toast';
import { cn } from '@/utils';
import { useSubmitInterview } from '../api/submit-interview';
import ClockIcon from '@/app/(assessments)/company-test/misc/icons/ClockIcon';
import { InterviewDataProps, useInterviewDetails } from '@/app/(assessments)/company-test/misc/store/interviewStore';
import { useStartTime } from '../store/start-time';

type Props = {
  handleSubmit?: ()=>void
}
const Countdown = ({handleSubmit}:Props) => {
  const router = useRouter();
  const renderer = ({
    formatted: { minutes, seconds, hours },
    completed,
  }: {
    formatted: {
      minutes: any;
      seconds: any;
      hours: any;
    };
    completed: boolean;
  }) => {
    if (completed) {
      // Render a completed state
      return <p className="text-sm text-primary">00:00</p>;
    } else {
      // Render a countdown
      return (
        <span
          className={cn('text-sm text-primary', { 'text-red-500': crunchTime })}
        >
          {hours}:{minutes}:{seconds}
        </span>
      );
    }
  };
  const renderer2 = ({
    formatted: { minutes, seconds, hours },
    completed,
  }: {
    formatted: {
      minutes: any;
      seconds: any;
      hours: any;
    };
    completed: boolean;
  }) => {
    if (completed) {
      // Render a completed state
      return <></>;
    } else {
      // Render a countdown
      return <></>;
    }
  };

  // const contextData = useContext(TestContext);
  // const data = contextData?.data;
  const assessmentDetails = useInterviewDetails((state: any) => state.data);
  const time_started = useStartTime(state => state.data);


  const [crunchTime, setCrucnchTime] = useState(false);
  const [pageData, setPageData] = useState<InterviewDataProps>(assessmentDetails);

  const [time_left, setTimeLeft] = useState(pageData?.interview?.questions?.reduce((acc,que)=>{acc += que.time;return acc},0) * 1000)
  const time = React.useMemo(() => {
    return Date.now() + pageData?.interview?.questions?.reduce((acc,que)=>{acc += que.time;return acc},0) * 1000;
  }, [pageData?.started]);

  useEffect(() => {
    setPageData(assessmentDetails);
  }, [assessmentDetails]);

  useEffect(()=>{
    if (pageData?.started){
      const time_limit = pageData?.interview?.questions?.reduce((acc,que)=>{acc += que.time;return acc},0) * 1000
      const now = Date.now()
      let time_left = (time_started + time_limit) - now 
      setTimeLeft(time_left)
    }

  }, [pageData?.started])

  return (
    <div
      className={`relative flex w-[180px] items-center gap-x-2 rounded-md bg-[#F5F3FF] p-2`}
    >
      <ClockIcon />
      {!pageData?.started && (
        <_Countdown
          date={time}
          renderer={renderer}
          zeroPadTime={10}
          autoStart={false}
        />
      )}
      {pageData?.started && (
        <_Countdown
          date={Date.now() + time_left}
          renderer={renderer}
          zeroPadTime={10}
          autoStart
          onComplete={handleSubmit}
        />
      )}
      {pageData?.started && (
        <_Countdown
          date={Date.now() + time_left + 20000}
          renderer={renderer2}
          zeroPadTime={10}
          autoStart
          onComplete={()=>setCrucnchTime(true)}
        />
      )}
      <p className="text-sm text-primary">time left</p>
    </div>
  );
};

export default Countdown;
