export type VideoInterviewCandidateData = {
    candidate_name: string;
    candidate_email: string;
    invite_id: string;
    result_id: string | null;
    interview: Interview;
}

type Interview = {
    id: string;
    name: string;
    questions: VideoInterviewQuestion[];
    role: Role;
    role_level: string;
    total_questions: number;
}

type VideoInterviewQuestion = {
    id: string;
    type: string | null;
    question: string;
    answer_options: any[] | null;
    answer: any | null;
    experience_level: string | null;
    category: string | null;
    points: number;
    label: string | null;
    tags: string[] | null;
    is_custom: boolean;
    time: number;
    created_at: string;
    updated_at: string;
    instructions: string | null;
    role: string | null;
    test: string | null;
    section: string | null;
    creator: number;
}

type Role = {
    id: number;
    name: string;
    use_ai: boolean;
}
