import { create } from 'zustand';
import { StateStorage, createJSONStorage, persist } from 'zustand/middleware';

export interface storeProps {
  step: number;
  setStep: (payload: number) => void;
  userPersonalData: any;
  userCompanyData: any;
  setRecruiterType: (payload: string) => void;
  setUserPersonalData: (payload: any) => void;
  setUserCompanyData: (payload: any) => void;
  moveToNextStep: () => void;
  getStorage: (() => StateStorage) | undefined;
  resetStore: () => void;
  clearStorage: () => void;
}


const useRecruiterRegisterDetails = create<storeProps>()(
  persist(
    (set, get) => ({
      step: 1,
      setStep: (data) => set(() => ({ step: data })),
      moveToNextStep: () => set((state) => ({ step: state.step + 1 })),
      userPersonalData: { email: '', first_name: '', password: '', recruiter_type: "HR_PERSONNEL" },
      userCompanyData: { name: '', company_email: '', website: '', industry: '', size: '', description: '' },
      setRecruiterType: (data) => set({ userPersonalData: { ...get().userPersonalData, recruiter_type: data } }),
      setUserPersonalData: (data) => set({ userPersonalData: { ...get().userPersonalData, ...data } }),
      setUserCompanyData: (data) => set({ userCompanyData: { ...get().userCompanyData, ...data } }),
      resetStore: () => {
        set({
          step: 1,
          userPersonalData: {
            email: '',
            first_name: '',
            password: '',
            recruiter_type: "HR_PERSONNEL",
          },
        });
      },
      clearStorage: () => {
        const storageName = 'recruiter-signup-storage';
        const storage = get().getStorage?.();
        if (storage) {
          storage.removeItem(storageName);
        }
      },
      getStorage: () => createJSONStorage(() => localStorage) as unknown as StateStorage,
    }),
    {
      name: 'recruiter-signup-storage',
      getStorage: () => createJSONStorage(() => localStorage) as unknown as StateStorage,
    }
  )
);

export default useRecruiterRegisterDetails;
