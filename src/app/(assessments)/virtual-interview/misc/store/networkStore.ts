import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

export interface NetworkStoreProps {
  data: { online: number };
  setNetworkDetails: (payload: any) => void;
}

export const useNetworkDetails: any = create<NetworkStoreProps>()(
  persist(
    set => ({
      data: { online: 1 }, // 1 for online 0 for offline would use boolean but it throws error TODO
      setNetworkDetails(payload) {
        set(state => ({ data: { online: payload } }));
      },
    }),
    {
      name: 'interview-assessment-network-details',
      storage: createJSONStorage(() => sessionStorage),
    }
  )
);
