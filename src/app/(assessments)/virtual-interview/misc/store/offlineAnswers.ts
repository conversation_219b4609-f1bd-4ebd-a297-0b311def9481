import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

export interface OfflineAnswersStoreProps {
  data: any;
  setOfflineAnswersDetails: (payload: any) => void;
}

export interface AnswerProps {
  candidate: string;
  invite_id: string;
  section_id: string;
  question: string;
  answer: any[];
}

export const useOfflineAnswersDetails: any = create<OfflineAnswersStoreProps>()(
  persist(
    set => ({
      data: [],
      setOfflineAnswersDetails(payload) {
        set(state => ({ data: payload }));
      },
    }),
    {
      name: 'OfflineAnswers-details',
      storage: createJSONStorage(() => sessionStorage),
    }
  )
);
