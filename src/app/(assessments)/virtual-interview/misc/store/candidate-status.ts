import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

export interface statusStoreProps {
  data: StatusProps;
  setStatusDetails: (payload: any) => void;
}

export interface StatusProps {
  candidateStatus: number;
}

// 1 for new candidates and 2 for returning/resuming candidates

export const useStatusDetails: any = create<statusStoreProps>()(
  persist(
    set => ({
      data: { candidateStatus: 1 },
      setStatusDetails(payload) {
        set(state => ({ data: { candidateStatus: payload } }));
      },
    }),
    {
      name: 'status-details',
      storage: createJSONStorage(() => sessionStorage),
    }
  )
);
