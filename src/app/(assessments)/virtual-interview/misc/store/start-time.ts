import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";

interface StartTime {
  data: any;
  setStartTime: (payload: any) => void;
}
export const useStartTime = create<StartTime>()(
  persist(
    set => ({
      data: null,
      setStartTime(payload) {
        set(state => ({ data: payload }));
      },
    }),
    {
      name: 'start_time',
      storage: createJSONStorage(() => sessionStorage),
    }
  )
);
