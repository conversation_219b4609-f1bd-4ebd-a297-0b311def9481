import { create, StateCreator } from 'zustand';
import { persist, PersistOptions } from 'zustand/middleware';
import { VideoInterviewCandidateData } from '../types';

interface StoreState {
    storedVideoAssessmentData: VideoInterviewCandidateData | null;
    setStoredVideoAssessmentData: (data: VideoInterviewCandidateData) => void;
}

type MyPersist = (
    config: StateCreator<StoreState>,
    options: PersistOptions<StoreState>
) => StateCreator<StoreState>;

const useVideoInterviewStore = create<StoreState>(
    (persist as MyPersist)(
        (set) => ({
            storedVideoAssessmentData: null,
            setStoredVideoAssessmentData: (data: VideoInterviewCandidateData) => 
                set({ storedVideoAssessmentData: data }),
        }),
        {
            name: 'stored-video-assessment-data',
            getStorage: () => localStorage,
        }
    )
);

export default useVideoInterviewStore;
