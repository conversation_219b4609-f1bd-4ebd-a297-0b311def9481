'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import ReactLoading from 'react-loading';
import { useGeolocation } from 'react-use';
import { Checkbox2, ToolTip } from '@/components/shared';
import Loader from '@/components/shared/loader';
import { useGetGeoLocation } from '../company-test/misc/api/getGeolocation';
import AssessmentNotAvailable from '../company-test/misc/components/AssessmentNotAvailable';
import BadDeviceIcon from '../company-test/misc/icons/BadDeviceIcon';
import GetLinkedLogoLight from '../company-test/misc/icons/GetLinkedLogoLight';
import RoundCirclePurple from '../company-test/misc/icons/RoundCirclePurple';
import RoundCircleWhite from '../company-test/misc/icons/RoundCircleWhite';
import {
  answeredQuestionsStoreProps,
  useAnsweredQuestionsDetails,
} from '../company-test/misc/store/answeredQuestionsStore';
import {
  InterviewDataProps,
  useInterviewDetails,
} from '../company-test/misc/store/interviewStore';
import { useProctoringReport } from '../company-test/misc/store/proctoringReportStore';
import {
  useGetVerifyCandidateCustom,
  useGetVerifyCandidateDefault,
} from '../virtual-interview/misc/api/verifyLogin';
import { useAcceptInterviewInvite } from './misc/api';
import {
  statusStoreProps,
  useStatusDetails,
} from './misc/store/candidate-status';

const page = () => {
  const router = useRouter();
  const search = useSearchParams();
  const token = search.get('invitation_token');
  const { mutate: getAssessmentDetails } = useAcceptInterviewInvite();
  const { mutate: acceptInterviewInvite } = useAcceptInterviewInvite();
  const setInterviewData = useInterviewDetails(
    (state: any) => state.setInterviewDetails
  );
  const assessmentDetails = useInterviewDetails(state => state.data);
  const [pageData, setPageData] = useState<InterviewDataProps>();
  const [email, setEmail] = useState<string>('');
  const [loginToken, setLoginToken] = useState<string>('');
  const [method1, setMethod1] = useState<string>('');
  const [has_accepted_policy, setHasAcceptedPolicy] = useState(false);
  const [method2, setMethod2] = useState<string>('');
  const [candidateName, setCandidateName] = useState('');
  const [showAssessmentNotAvalaible, setShowAssessmentNotAvailable] =
    useState(false);
  const [errObj, setErrObj] = useState<any>();
  const { proctoringReport, setProctoringReport } = useProctoringReport();

  const setAnsweredQuestions = useAnsweredQuestionsDetails(
    (state: answeredQuestionsStoreProps) => state.setAnsweredQuestionsDetails
  );
  const { mutate: fetchGeoLocation } = useGetGeoLocation();
  const setCandidateStatus = useStatusDetails(
    (state: statusStoreProps) => state.setStatusDetails
  );

  const location = useGeolocation();

  // Function to handle countdown completion
  const handleCountdownComplete = () => {
    // Close the error modal
    setShowAssessmentNotAvailable(false);
    setErrObj(undefined);

    // Refresh the interview data
    if (token) {
      acceptInterviewInvite(token, {
        onSuccess: data => {
          setInterviewData({ ...data, started: false });
        },
        onError: (err: any) => {
          setErrObj(err?.response.data);
          setShowAssessmentNotAvailable(true);
        },
      });
    }
  };

  useEffect(() => {
    if (token) {
      acceptInterviewInvite(token, {
        onSuccess: data => {
          setInterviewData({ ...data, started: false });
        },
        onError: (err: any) => {
          setErrObj(err?.response.data);
          setShowAssessmentNotAvailable(true);
        },
      });
      // setAssessmentData({ ...dummyTest2, started: false });
    }
  }, [token]);

  useEffect(() => {
    if (location?.longitude && location?.latitude) {
      setGeoLocation();
    }
  }, [location]);

  const setGeoLocation = async () => {
    fetchGeoLocation(
      {
        long: location?.longitude as number,
        lat: location?.latitude as number,
        key: 'be5cdeb5c4c843f5aafa31ce711734f0',
      },
      {
        onSuccess: data => {
          setProctoringReport({
            ...proctoringReport,
            location: data?.results[0]?.components?.county,
          });
        },
        onError: () => {
          setProctoringReport({
            ...proctoringReport,
            location: 'Unknown',
          });
        },
      }
    );
  };

  const { mutate: verifyCandidateCustom, isLoading: customLoading } =
    useGetVerifyCandidateCustom();
  const { mutate: verifyCandidateDefault, isLoading: defaultLoading } =
    useGetVerifyCandidateDefault();

  useEffect(() => {
    setPageData(assessmentDetails!);
  }, [assessmentDetails]);

  useEffect(() => {
    setEmail(pageData?.candidate_email as string);
    setLoginToken(pageData?.token || '');
    setCandidateName(pageData?.candidate_name || '');
  }, [pageData]);

  const handleLogin = (e: any) => {
    e.preventDefault();
    setInterviewData({
      ...assessmentDetails,
      candidate_name: candidateName,
    });
    if (pageData?.auth_method?.[0]) {
      verifyCandidateCustom(
        {
          invite_id: pageData.invite_id,
          method1: method1 as string,
          method2: method2 as string,
          is_custom: true,
          candidate_email: email,
          assessment_id: pageData?.interview.id,
          candidate_name: candidateName,
          result_id: pageData?.result_id as string,
        },
        {
          onSuccess: data => {
            if (data.status === 200) {
              if (data.data.candidate_session?.id !== undefined) {
                setCandidateStatus(2); // set to 2 for returning candidates
                setInterviewData({
                  ...assessmentDetails,
                  interview: {
                    ...assessmentDetails?.interview,
                    time_limit: data?.data?.candidate_session?.time_left,
                  },
                });
                setProctoringReport({
                  ...proctoringReport,
                  id: data?.data?.candidate_session?.proctoring_report,
                });
                setAnsweredQuestions(
                  data?.data?.candidate_session.answered_questions
                );
                router.push('/virtual-interview/start-test');
              } else {
                router.push('/virtual-interview/assessment-criteria');
              }
            }
          },
          onError: (error: any) => {
            toast.error(error.response.data.message as string);
          },
        }
      );
    } else {
      verifyCandidateDefault(
        {
          assessment_id: pageData?.interview.id!,
          email: email as string,
          token: loginToken as string,
          invite_id: pageData?.invite_id as string,
          candidate_name: candidateName,
          result_id: pageData?.result_id as string,
        },
        {
          onSuccess: data => {
            if (data.status === 200) {
              if (data?.data?.candidate_session?.id !== undefined) {
                setCandidateStatus(2); // set to 2 for returning candidates
                setInterviewData({
                  ...assessmentDetails,
                  interview: {
                    ...assessmentDetails?.interview,
                    time_limit: data?.data?.candidate_session?.time_left,
                  },
                });
                /* setProctoringReport({ */
                /*   ...proctoringReport, */
                /*   id: data?.data?.candidate_session?.proctoring_report, */
                /* }); */
                setAnsweredQuestions(
                  data?.data?.candidate_session?.answered_questions
                );
                router.push('/virtual-interview/start-test');
              } else {
                router.push('/virtual-interview/assessment-criteria');
              }
            }
          },
          onError: (error: any) => {
            toast.error(error.response.data.message as string);
          },
        }
      );
    }
    // router.push('/company-test/assessment-criteria');
  };

  return (
    <div className="relative flex h-screen w-screen items-center justify-center bg-[#1F0231]">
      {pageData ? (
        <>
          <div className="z-20 flex h-[70%] w-[90%] flex-col justify-center rounded-2xl border border-[#FFFFFF38] bg-[#F8F6FF05] p-3 sm:w-[500px] sm:p-10">
            <div className="hidden lg:block">
              <div className="relative h-[40px] w-[200px]">
                <p className="text-3xl text-white"> Welcome 👋</p>
              </div>
              <form
                onSubmit={e => handleLogin(e)}
                className="flex w-full flex-col gap-y-3 py-5"
                action=""
              >
                <input
                  className="rounded-lg bg-[#FFFFFF16] p-3 capitalize text-white outline-none placeholder:text-white"
                  type="text"
                  placeholder="Name"
                  required
                  value={candidateName}
                  onChange={e => setCandidateName(e.target.value)}
                />

                <input
                  className="rounded-lg bg-[#FFFFFF16] p-3 lowercase text-white outline-none placeholder:text-white"
                  type="email"
                  placeholder="Email"
                  required
                  value={email}
                  onChange={e => setEmail(e.target.value)}
                />
                <input
                  className="rounded-lg bg-[#FFFFFF16] p-3  text-white outline-none placeholder:text-white"
                  type="text"
                  placeholder="Token"
                  required
                  value={loginToken}
                  onChange={e => setLoginToken(e.target.value)}
                />
                <div className="mt-6 flex items-center gap-3 rounded-xl bg-[#8b71f31f] px-2 py-2 text-white ">
                  <Checkbox2
                    checked={has_accepted_policy}
                    onCheckedChange={e => setHasAcceptedPolicy(e as boolean)}
                    name="accept privacy policy"
                    required
                    className="!border-white"
                    checkClassName="!text-white"
                    id="accept-privacy-policy"
                  />
                  <label className="text-xs" htmlFor="accept-privacy-policy">
                    I have read and accept the
                    <Link
                      href="/privacy"
                      className="ml-1.5 text-[0.8rem] font-medium underline"
                    >
                      privacy policy
                    </Link>
                  </label>
                </div>

                <button
                  type="submit"
                  className="relative flex w-full items-center justify-center rounded-lg bg-white p-3 text-[#1f0231] disabled:bg-white/70"
                  disabled={!has_accepted_policy}
                  // onClick={() => router.push('/company-test/start-test')}
                >
                  {!has_accepted_policy && (
                    <ToolTip content="Please accept the privacy policy">
                      <div className="absolute bottom-0 left-0 right-0  top-0 z-[10] h-full w-full" />
                    </ToolTip>
                  )}
                  {customLoading || defaultLoading ? (
                    <div className="flex h-8 w-8 items-center justify-center">
                      <ReactLoading
                        height={20}
                        width={20}
                        type="spin"
                        color="#3C1356"
                      />
                    </div>
                  ) : (
                    <p>Login</p>
                  )}
                </button>

                {/* 
                    {!has_accepted_policy && (
                      <TooltipContent className="bg-white">
                        You need to accept the privacy policy to proceed
                      </TooltipContent>
                    )} */}
              </form>
            </div>
            <div className="flex flex-col items-center gap-y-4 lg:hidden">
              <BadDeviceIcon />
              <div className="text-white">
                <p className="text-center text-lg font-semibold sm:text-2xl">
                  Incompatible device
                </p>
                <p className="py-3 text-center text-xs font-extralight sm:text-base">
                  We apologize for any inconvenience, but please note that due
                  to the enabled proctoring feature, this assessment is only
                  accessible on a laptop, desktop, or a system equipped with a
                  compatible operating system
                </p>
              </div>
              <button className="min-w-[200px] rounded-md bg-white p-3 text-[#3C1356] sm:min-w-[250px]">
                Go Home
              </button>
            </div>
          </div>
          <div className="absolute left-3 top-3 scale-90 sm:left-10 sm:top-10">
            <GetLinkedLogoLight />
          </div>
          <div className="absolute inset-0 hidden h-full w-full bg-transparent lg:block">
            <div className="absolute right-20 top-1/2 z-10 h-[270px] w-[270px]">
              <Image alt="" src={'/assets/cha12.svg'} fill />
            </div>
            <div className="absolute left-20 top-1/2 z-10 h-[300px] w-[270px]">
              <Image alt="" src={'/assets/char11.svg'} fill />
            </div>
            <div className="absolute right-[200px] top-1/3 z-10">
              <RoundCirclePurple />
            </div>
            <div className="absolute right-[300px] top-[40%] z-10">
              <RoundCircleWhite />
            </div>
          </div>
        </>
      ) : (
        <Loader />
      )}
      <AssessmentNotAvailable
        error={errObj}
        isOpen={showAssessmentNotAvalaible}
        isInterview={true}
        onCountdownComplete={handleCountdownComplete}
      />
    </div>
  );
};

export default page;
