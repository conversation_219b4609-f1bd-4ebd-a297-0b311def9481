"use client"

import React, { useEffect } from 'react'
import { AxiosError } from 'axios';
import { useSearchParams } from 'next/navigation';

import { Button, ErrorModal } from '@/components/shared';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { useErrorModalState } from '@/hooks';

import { useAcceptInterviewInvite } from './misc/api';
import useVideoInterviewStore from './misc/store';

interface TalentVirtualInterviewLayoutProps {
  children: React.ReactNode;
}

const TalentVirtualInterviewLayout: React.FC<TalentVirtualInterviewLayoutProps> = ({ children }) => {
  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState()

  return (
    <div className='w-screen h-screen overflow-hidden overflow-y-auto bg-secondary-darker'>
      {children}
      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        // heading={"No invite token found"}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 hover:border-red-950 hover:text-red-950 px-1.5 sm:text-sm md:px-6"
            type="button"
            onClick={closeErrorModal}
          >
            Close
          </Button>
        </div>
      </ErrorModal>
    </div>
  );
}

export default TalentVirtualInterviewLayout;
