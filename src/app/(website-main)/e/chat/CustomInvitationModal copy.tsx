import React, { FormEvent, useCallback, useRef, useEffect, useState } from "react";
import Modal from "@/components/Modal";
import { TOKEN_CONSTANTS, CUSTOM_INVITATION_TOKENS } from "@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants";
import "@/styles/quill.css"
import UploadIcon from "@/app/(website-main)/t/showcase/misc/components/cv-upload/icons/UploadIcon";
import { useDropzone } from "react-dropzone";
import { blobToBase64 } from "@/lib/utils/functions";

type FormDataType = {
  subject: string,
  message: string,
  cover_image: Blob | undefined,
}

interface EditorProps {
  form_data: FormDataType,
  setMessage: (data: string) => void,
  setSubject: (data: string) => void,
  setCoverImage: (data: Blob | undefined) => void,
}

const Editor: React.FC<EditorProps> = ({ form_data, setMessage, setSubject, setCoverImage }) => {
  let quill = {} as any;

  const [is_tokens_list_expanded, setIsTokensListExpanded] = useState(false)
  function toggleIsTokensListExpanded() {
    setIsTokensListExpanded(!is_tokens_list_expanded)
  }

  const token_possibilities: string[] = [];
  const tokens_by_id: Record<string, { title: string; value: string }> = {};

  const objects = Object.entries(CUSTOM_INVITATION_TOKENS);
  objects.map((object, _) => {
    const { title, value } = object[1];
    token_possibilities.push(title);
    token_possibilities.push(value);
    tokens_by_id[value] = object[1];
  });

  function addToken(token: string) {
    if (Object.keys(quill).length !== 0) {
      if (!token_possibilities.includes(token)) {
        return;
      }
      const range = quill.getSelection(true);
      quill.insertEmbed(range?.index, "token", token, "user");
      quill.insertText(range?.index + 1, " ", "user");
      quill.setSelection(range?.index + 2, 0, "user");
    }
  }

  let removeToken = (e: any, TokenBlot: any, Parchment: any, is_value = false) => { };

  async function setupQuill() {
    const { default: Quill } = await import("quill");

    const Parchment = Quill.import("parchment");

    class TokenBlot extends Parchment.Embed {
      static blotName = "token";
      static className = TOKEN_CONSTANTS.class_name;
      static tagName = TOKEN_CONSTANTS.tag_name;

      static create(value: string) {
        const close_btn = document.createElement("button")
        close_btn.setAttribute("type", "button")
        close_btn.innerHTML = "<span class='ml-2 font-bold'>&times;</span>"
        this.listener = close_btn.addEventListener("click", (e) => {
          removeToken(node, TokenBlot, Parchment, true);
        });

        const token_body = document.createElement("span")
        token_body.innerText = tokens_by_id[value].title;
        token_body.appendChild(close_btn)

        const node = super.create();
        node.appendChild(token_body)

        node.setAttribute("contenteditable", false);

        node.classList.add("btn-primary-light");
        node.classList.add("py-1");
        node.classList.add("inline-block");

        node.dataset.value = value;
        node.setAttribute("draggable", "true");
        node.addEventListener("dragstart", (e: DragEvent) => {
          e.dataTransfer?.setData(
            "Token",
            Parchment.find(node).offset(quill.scroll),
          );

          // @ts-ignore
          e.dataTransfer.dropEffect = "move";
        });
        node.addEventListener("dragend", (e: DragEvent) => {
          const editor = document.querySelector("#editor-container");
          const editor_bounds = editor?.getBoundingClientRect();

          // @ts-ignore
          if (e.x < editor_bounds.right && e.x > editor_bounds.left && e.y > editor_bounds.top && e.y < editor_bounds.bottom) {
            addToken(node.dataset.value);
            removeToken(node, TokenBlot, Parchment, true);
          } else {
            removeToken(node, TokenBlot, Parchment, true);
          }
        });
        return node;
      }

      static value(node: HTMLElement) {
        return node.dataset.value;
      }

      length() {
        return 1;
      }
    }

    Quill.register(TokenBlot);

    const quill_editor = document.querySelector("#editor-container");
    if (!quill_editor) {
      return;
    }


    quill_editor.innerHTML = form_data.message;

    const _quill = new Quill(quill_editor, {
      modules: {
        toolbar: [
          [{ header: [1, 2, false] }],
          ["bold", "italic", "underline"],
        ],
      },
      placeholder: "Compose an epic...",
      theme: "snow", // or 'bubble'
    });

    _quill.on("text-change", () => {
      setMessage(_quill.root.innerHTML)
    });

    quill = _quill;

    document
      .querySelectorAll(`.${TOKEN_CONSTANTS.class_name}`)
      .forEach((token_element) => {
        token_element.setAttribute("draggable", "true");

        // @ts-ignore
        token_element.addEventListener("dragstart", (e: DragEvent) => {

          // @ts-ignore
          e.dataTransfer.setData("text/plain", token_element.dataset.value);
          // @ts-ignore
          e.dataTransfer.dropEffect = "move";
        });
      });

    const ql_editor = document.querySelector("#editor-container");
    ql_editor?.addEventListener("dragover", (e) => {
      e.preventDefault();
    });
    ql_editor?.addEventListener("drop", (e) => {

      // @ts-ignore
      const value = e.dataTransfer.getData("text/plain");

      if (document.caretRangeFromPoint) {
        const selection = document.getSelection();

        // @ts-ignore
        const range = document.caretRangeFromPoint(event.clientX, event.clientY,);
        if (selection && range) {
          selection.setBaseAndExtent(
            range.startContainer,
            range.startOffset,
            range.startContainer,
            range.startOffset,
          );
        }
      }
      addToken(value);
    });


    removeToken = (e: any, TokenBlot: any, Parchment: any, is_value = false) => {
      let token;
      let pos;
      const is_pos = false;
      if (is_pos) {
        pos = e;
      } else {
        if (is_value) {
          token = Parchment.find(e);
        } else {
          token = Parchment.find(e.target);
        }
        if (token instanceof TokenBlot) {
          pos = token.offset(quill.scroll);
        }
      }
      if (pos >= 0) {
        /* quill.setSelection(pos, 1, "user"); */
        quill.deleteText(pos, 2, "api");

        //remove event listener
        //window.removeEventListener("click", token.listener);
      }
    }
  }


  useEffect(() => {
    if (Object.keys(quill).length == 0) {
      setupQuill();
    }
  }, [])

  /* cover image */
  function clearCoverImage() {
    setCoverImage(undefined)
  }

  function updateCoverImage(acceptedFiles: any) {
    const formdata = new FormData();
    formdata.append("file", acceptedFiles[0]);
    const blob = new Blob([acceptedFiles[0]], {
      type: "image/*", // or whatever your Content-Type is
    });
    setCoverImage(blob)
  }

  const onDrop = useCallback((acceptedFiles: any) => {
    updateCoverImage(acceptedFiles);
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      "image/*": [],
    },
    maxSize: 10 * 1024 * 1024, //10mb
  });

  const updateSubject = (e: any) => {    
    setSubject(e.target.value)
  }

  return (
    <>
      <div>
        <input className="border rounded-xl p-4 floating-placeholder-input w-full" id="email-subject" type="text" name="email subject" placeholder="Enter email subject here" value={form_data.subject} onChange={updateSubject} required />
      </div>

      <div className="space-y-2">
        <label>
          <p>Cover Image</p>
        </label>
        {
          form_data.cover_image ? (
            <div className="relative flex justify-center items-center w-[40%]">
              <img src={URL.createObjectURL(form_data.cover_image)} alt="" />
              <div>
                <div className="absolute inset-0">
                  <button
                    onClick={clearCoverImage}
                    className="z-10 absolute m-4 top-0 right-0"
                    type="button"
                  >
                    <span
                      title="clear video"
                      className="bg-white rounded-full w-7 aspect-square flex items-center justify-center text-lg"
                    >
                      &times;
                    </span>
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div
              className={`${false ? "border border-red-600" : ""
                } mt-3 flex h-[5.9375rem] cursor-pointer items-center gap-4 rounded-[0.875rem] border-[0.3px] border-dashed border-[rgb(117,90,226)] p-6`}
              {...getRootProps()}
            >
              <div className="">
                <UploadIcon />
              </div>
              <div className="">
                <p className="font-sans text-xs font-semibold text-[#755AE2] sm:text-sm">
                  Tap to upload cover image
                </p>
                <span className="font-sans text-xs font-semibold text-[#755AE2] opacity-75">
                  Files types: PNG, JPG, Max size: 10MB
                </span>
              </div>
              <input hidden {...getInputProps()} />
            </div>
          )}
      </div>

      <p className="text-sm helper-text">To customize this email, drag the placeholder options displayed below and place them where you will like to have them.</p>
      <div>
        <ul className="flex flex-wrap gap-2 items-center">
          <div className={`${is_tokens_list_expanded ? "max-h-max" : "max-h-[40px]"} overflow-y-clip contents`}>
            {CUSTOM_INVITATION_TOKENS.slice(0, is_tokens_list_expanded ? CUSTOM_INVITATION_TOKENS.length : 4).map((token, index) => (
              <li key={index}>
                <button
                  onClick={() => {
                    addToken(token.value);
                  }}
                  data-value={token.value}
                  type="button"
                  title={`click or drag to add the '${token.title}' placeholder`}
                  className={`btn-primary-light text-xs ${TOKEN_CONSTANTS.class_name}`}
                >
                  <span>{token.title}</span>
                </button>
              </li>
            ))}
          </div>

          <li>
            <button onClick={toggleIsTokensListExpanded} type="button" className="btn-primary-bordered text-xs">
              show {is_tokens_list_expanded ? "less" : "more"}
            </button>
          </li>

        </ul>
      </div>
      <div className="">
        <div id="editor-container" className="rounded-b-xl min-h-[100px] !border !border-t-none"></div>
      </div>
    </>
  )
}

interface Props {
  is_open: boolean;
  is_busy?: boolean;
  initial_data?: FormDataType,
  close: () => void;
  handleSubmit: (form_data: FormDataType) => void;
}

const CustomInvitationModal: React.FC<Props> = ({
  is_open,
  close,
  initial_data,
  handleSubmit,
}) => {

  const [subject, setSubject] = useState(initial_data?.subject || "")
  const [message, setMessage] = useState(initial_data?.message || "")
  const [cover_image, setCoverImage] = useState(initial_data?.cover_image || undefined)

  function submitForm(event: FormEvent) {
    event.preventDefault();
    if (message) {
      handleSubmit({ subject, message, cover_image });
    }
    else {
      alert("Error, You cannot save an empty email template. However, you could cancel and the default would be used.")
    }
  }

  function htmlToTokens(html_string: string,) {
    const parser = new DOMParser();
    const dom = parser.parseFromString(html_string, "text/html");

    dom
      .querySelectorAll(`.${TOKEN_CONSTANTS.class_name}`)
      .forEach((token: any) => {
        const {
          dataset: { value },
        } = token;
        const span = document.createElement("span")
        span.innerText = TOKEN_CONSTANTS.regex.replace("token", value);
        span.classList.add("font-semibold")

        token.replaceWith(span);
      });

    return dom.body.innerHTML;
  }

  return (
    <Modal title="Customize invitation email" close={close} is_open={true}>
      <form className="md:w-[1177px] p-4 min-h-[80vh] bg-[#F8F9FB] flex flex-col" onSubmit={submitForm}>
        <div className="grid md:grid-cols-2 gap-4 flex-1">

          <div className="p-4 rounded-xl bg-white space-y-2">
            <div className="space-y-2">
              <h2 className="font-medium text-2xl">Edit email</h2>
              <p className="text-sm helper-text">
                You can change these fields to create a custom invitation email template.
              </p>
              <Editor form_data={{ message, subject, cover_image }} setSubject={setSubject} setMessage={setMessage} setCoverImage={setCoverImage} />
            </div>
          </div>

          <div className="p-4 rounded-xl bg-white space-y-6">
            <header>
              <h2 className="font-medium text-2xl">Preview</h2>
              <p className="underline underline-offset-4">Subject: <span className="font-semibold text-[#1C1C1C]">{subject || "Assessment Invitation"}</span></p>
              <p className="text-sm helper-text">
                To customize this email, select the placeholder options delayed
                and place them where you will like to have them.
              </p>
            </header>


            {
              message ? (
                <p>
                  <div className="text-sm" dangerouslySetInnerHTML={{ __html: htmlToTokens(message) }}></div>
                </p>
              ) : (
                <p className="text-sm">"content goes here"</p>
              )}

            <section>
              <h3 className="font-semibold mt-6 mb-2">Assessment Details:</h3>
              <ul className="space-y-1 text-sm ml-2.5 mt-0">
                <li className="text-black/80">
                  Assessment Name: <span className="font-semibold text-[#1C1C1C]">{"{ assessment_name }"}</span> </li>
                <li className="text-black/80">
                  Assessment Start Date: <span className="font-semibold text-[#1C1C1C]">{"{ start_date }"}</span> </li>
                <li className="text-black/80">
                  Duration: <span className="font-semibold text-[#1C1C1C]">{"{ time_limit }"}</span> </li>
                <li className="text-black/80">
                  Assessment deadline: This assessment is valid until <span className="font-semibold text-[#1C1C1C]">{"{ deadline }"}</span>. </li>
                <li className="text-black/80">
                  The ‘Take assessment’ button provided below will only be active from <span className="font-semibold text-[#1C1C1C]">{"{ start_date }"}</span > until <span className="font-semibold text-[#1C1C1C]">{"{ deadline }"}</span>. </li>
                <li className="text-black/80">
                  The ‘Take assessment’ button provided below will only be active during the mentioned time period. </li>
              </ul>
            </section>

            <button className="btn-primary text-sm" type="button">Take Assessment</button>

            <footer>
              <p className="text-sm text-black/80" >
                Use the following details to log in to the assessment: <span className="font-semibold text-[#1C1C1C]">"{"{ auth_method1 }"}" - {"{ auth_method1_value }"} "{"{ auth_method2 }"}" - {"{ auth_method2_value }"}</span></p>
              <p className="text-sm text-black/80">Note that you can only take this assessment once. Good luck !</p>
            </footer>
          </div>
        </div>
      </form>
      <div className="flex gap-4 items-center justify-end p-4 bg-white">
        <button type="button" onClick={close} className="btn-primary-light"> Cancel </button>
        <button onClick={submitForm} type="submit" className="btn-primary"> Save </button>
      </div>
    </Modal>
  );
};

export default CustomInvitationModal;
