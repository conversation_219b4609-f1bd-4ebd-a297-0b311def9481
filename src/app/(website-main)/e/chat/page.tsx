'use client'
import { AppHeaderR<PERSON>ruiter, EmptyCard } from '@/components/shared';
import Image from 'next/image';
import React from 'react';
import { useForm, FieldValues, Control } from 'react-hook-form';






const RecruiterChatPage = () => {


  return (
    <main className="main">
      <div className='maincontentwithheader grid grid-rows-[max-content_1fr] flex-col bg-[#F8F9FB] h-full overflow-y-hidden '>
        <AppHeaderRecruiter title='Chats' className='bg-white md:!pt-2' />
     
        <EmptyCard
          noIcon
          content={
            <div className='py-8'>
              <Image
                src="/images/coming-soon-tag.svg"
                alt="Coming soon"
                width={1000}
                height={300}
                className='max-w-full mt-7'
              />
              <p className='text-sm text-[#7D8590] mt-8'>While we finalize the details of this feature rich module, we encourage you to explore other modules available to you.</p>
            </div>
          }
        />
      </div>
    </main>
  );
};

export default RecruiterChatPage;