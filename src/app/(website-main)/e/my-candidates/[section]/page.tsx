'use client'

import React from "react";
import { useRouter } from "next/navigation";

import { <PERSON><PERSON>, Tab, ErrorModal } from '@/components/shared'
import { useBooleanStateControl, useErrorModalState } from "@/hooks";

import { CandidateGroups, FilteredGroups } from "../misc/section";
import ImportModal from "../misc/components/ImportBulkCVModal";
import ImportIcon from "../misc/icons/ImportIcon";
import PurpleBox from "../misc/icons/PurpleBox";
import CreateCVGroupModal from "../misc/components/CreateCVGroupModal";





const RecruiterAssessmentandInterviewView = ({ params }: { params: { section: string }; }) => {
    const { section } = params;

    const router = useRouter();
    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();
    const {
        state: isImportModalOpen,
        setTrue: openImportModal,
        setFalse: closeImportModal,
    } = useBooleanStateControl()
    const {
        state: isCreateGroupModalOpen,
        setTrue: openCreateGroupModal,
        setFalse: closCreateGrouptModal,
    } = useBooleanStateControl()

    const tabsArray = [
        {
            id: 1,
            title: "Candidates Groups",
            link: './candidate-groups',
            component: <CandidateGroups
                openImportModal={openImportModal}
            />,
        },
        {
            id: 2,
            title: "Filtered Groups",
            link: './filtered-groups',
            component: <FilteredGroups />
        },


    ]


    return (
        <>
            <Tab
                fallback='my-candidates'
                listClass="md:px-6"
                className='md:py-2'
                currentTab={section}
                catgoryArray={tabsArray}
                sideButton={

                    <div className="flex gap-2 items-center">
                        <Button
                            onClick={openCreateGroupModal}
                            type="button"
                            variant="outlined"
                            className="px-5"
                            icon={<PurpleBox width={20} height={20} />}

                        >
                            Create group
                        </Button>
                        <Button
                            onClick={openImportModal}
                            variant="default"
                            className="px-5"
                            icon={<ImportIcon />}
                        >
                            Import Bulk CVs
                        </Button>
                    </div>
                }
            />


            <ImportModal
                isOpen={isImportModalOpen}
                closeModal={closeImportModal}
            />
            <CreateCVGroupModal
                isOpen={isCreateGroupModalOpen}
                closeModal={closCreateGrouptModal}
            />

            {/* ERROR MODAL */}
            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={
                    errorModalMessage || 'Please check your inputs and try again.'
                }
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button
                        className="grow bg-red-950 hover:border-red-950 hover:text-red-950 px-1.5 sm:text-sm md:px-6"
                        type="button"
                        onClick={closeErrorModal}
                    >
                        Okay
                    </Button>
                </div>
            </ErrorModal>
        </>
    )
}


export default RecruiterAssessmentandInterviewView