import * as React from 'react';
import { SVGProps } from 'react';

const CandidateGroup = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={131}
    height={131}
    viewBox="0 0 131 131"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx={65.5} cy={65.5} r={65.5} fill="white" />
    <g opacity={0.5}>
      <path
        d="M80.5525 107H37.5489C33.9498 107 31 104.069 31 100.451V45.3701C31 41.771 33.9312 38.8212 37.5489 38.8212H80.5339C84.1516 38.8212 87.0828 41.7524 87.0828 45.3701V100.451C87.0828 104.069 84.1516 107 80.5525 107ZM37.5489 40.695C34.9701 40.695 32.8552 42.7914 32.8552 45.3886V100.47C32.8552 103.048 34.9516 105.163 37.5489 105.163H80.5339C83.1127 105.163 85.2276 103.067 85.2276 100.47V45.3701C85.2276 42.7914 83.1312 40.6764 80.5339 40.6764L37.5489 40.695Z"
        fill="#7D8590"
      />
      <path
        d="M87.4553 100.099H86.1567C85.6372 100.099 85.2291 99.6904 85.2291 99.171C85.2291 98.6515 85.6372 98.2434 86.1567 98.2434H87.4553C90.0341 98.2434 92.149 96.147 92.149 93.5497V38.4687C92.149 35.89 90.0526 33.7751 87.4553 33.7751H44.4703C41.8915 33.7751 39.7766 35.8714 39.7766 38.4687V39.7674C39.7766 40.2868 39.3684 40.695 38.849 40.695C38.3295 40.695 37.9214 40.2868 37.9214 39.7674V38.4687C37.9214 34.8696 40.8526 31.9199 44.4703 31.9199H87.4553C91.073 31.9199 94.0042 34.8511 94.0042 38.4687V93.5497C94.0042 97.1488 91.073 100.099 87.4553 100.099Z"
        fill="#7D8590"
      />
      <path
        d="M94.3748 93.1787H93.0761C92.5567 93.1787 92.1485 92.7706 92.1485 92.2511C92.1485 91.7317 92.5567 91.3235 93.0761 91.3235H94.3748C96.9535 91.3235 99.0684 89.2271 99.0684 86.6299V31.5489C99.0684 28.9701 96.972 26.8552 94.3748 26.8552H51.3897C48.811 26.8552 46.696 28.9516 46.696 31.5489V32.8475C46.696 33.367 46.2879 33.7751 45.7684 33.7751C45.249 33.7751 44.8408 33.367 44.8408 32.8475V31.5489C44.8408 27.9312 47.772 25 51.3897 25H94.3748C97.9924 25 100.924 27.9312 100.924 31.5489V86.6299C100.924 90.2475 97.9739 93.1787 94.3748 93.1787Z"
        fill="#7D8590"
      />
      <path
        d="M68.4432 74.9423H49.6686C46.3106 74.9423 43.6021 72.2151 43.6021 68.8757V51.1771C43.6021 47.8192 46.3292 45.1106 49.6686 45.1106H68.4247C71.764 45.1106 74.4912 47.8377 74.4912 51.1771V68.8757C74.5098 72.2337 71.7826 74.9423 68.4432 74.9423ZM49.6686 46.9658C47.3496 46.9658 45.4573 48.8581 45.4573 51.1771V68.8757C45.4573 71.1948 47.3496 73.0871 49.6686 73.0871H68.4247C70.7437 73.0871 72.636 71.1948 72.636 68.8757V51.1771C72.636 48.8581 70.7437 46.9658 68.4247 46.9658H49.6686Z"
        fill="#7D8590"
      />
      <path
        d="M67.3242 74.9422C66.8047 74.9422 66.3966 74.5341 66.3966 74.0146V68.3006C66.3966 64.4046 63.2242 61.2508 59.3468 61.2508H58.7531C54.8572 61.2508 51.7033 64.4232 51.7033 68.3006V74.0146C51.7033 74.5341 51.2952 74.9422 50.7757 74.9422C50.2563 74.9422 49.8481 74.5341 49.8481 74.0146V68.3006C49.8481 63.3843 53.8554 59.3956 58.7531 59.3956H59.3468C64.2631 59.3956 68.2518 63.4028 68.2518 68.3006V74.0146C68.2518 74.5341 67.8436 74.9422 67.3242 74.9422Z"
        fill="#7D8590"
      />
      <path
        d="M59.0499 61.3063C55.4508 61.3063 52.5381 58.3751 52.5381 54.7945C52.5381 51.214 55.4693 48.2827 59.0499 48.2827C62.6304 48.2827 65.5616 51.214 65.5616 54.7945C65.5616 58.3751 62.6489 61.3063 59.0499 61.3063ZM59.0499 50.1379C56.4711 50.1379 54.3933 52.2343 54.3933 54.7945C54.3933 57.3547 56.4897 59.4511 59.0499 59.4511C61.61 59.4511 63.7064 57.3547 63.7064 54.7945C63.7064 52.2343 61.6286 50.1379 59.0499 50.1379Z"
        fill="#7D8590"
      />
      <path
        d="M55.1688 74.9421C54.6494 74.9421 54.2412 74.534 54.2412 74.0145V67.1688C54.2412 66.6494 54.6494 66.2412 55.1688 66.2412C55.6883 66.2412 56.0964 66.6494 56.0964 67.1688V74.0145C56.0964 74.534 55.6883 74.9421 55.1688 74.9421Z"
        fill="#7D8590"
      />
      <path
        d="M62.9305 74.9421C62.4111 74.9421 62.0029 74.534 62.0029 74.0145V67.1688C62.0029 66.6494 62.4111 66.2412 62.9305 66.2412C63.45 66.2412 63.8581 66.6494 63.8581 67.1688V74.0145C63.8581 74.534 63.4314 74.9421 62.9305 74.9421Z"
        fill="#7D8590"
      />
      <path
        d="M78.0862 82.029H40.0174C39.498 82.029 39.0898 81.6209 39.0898 81.1014C39.0898 80.5819 39.498 80.1738 40.0174 80.1738H78.0862C78.6057 80.1738 79.0138 80.5819 79.0138 81.1014C79.0138 81.6209 78.6057 82.029 78.0862 82.029Z"
        fill="#7D8590"
      />
      <path
        d="M78.0862 88.2811H40.0174C39.498 88.2811 39.0898 87.8729 39.0898 87.3535C39.0898 86.834 39.498 86.4259 40.0174 86.4259H78.0862C78.6057 86.4259 79.0138 86.834 79.0138 87.3535C79.0138 87.8729 78.6057 88.2811 78.0862 88.2811Z"
        fill="#7D8590"
      />
      <path
        d="M78.0862 94.5146H40.0174C39.498 94.5146 39.0898 94.1065 39.0898 93.587C39.0898 93.0676 39.498 92.6594 40.0174 92.6594H78.0862C78.6057 92.6594 79.0138 93.0676 79.0138 93.587C79.0138 94.1065 78.6057 94.5146 78.0862 94.5146Z"
        fill="#7D8590"
      />
      <path
        d="M78.0862 100.748H40.0174C39.498 100.748 39.0898 100.34 39.0898 99.8203C39.0898 99.3009 39.498 98.8927 40.0174 98.8927H78.0862C78.6057 98.8927 79.0138 99.3009 79.0138 99.8203C79.0138 100.34 78.6057 100.748 78.0862 100.748Z"
        fill="#7D8590"
      />
    </g>
  </svg>
);
export default CandidateGroup;
