import * as React from 'react';
import { SVGProps } from 'react';


interface DropDownIconProps {
  color?: string;
  props?: SVGProps<SVGSVGElement>;
}

const DropDownIcon = ({ props, color = '#755AE2' }: DropDownIconProps) => (
  <svg
    width={30}
    height={30}
    viewBox="0 0 37 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M21.357 17.5221C20.0986 18.7805 18.0845 18.8224 16.7758 17.6479L16.643 17.5221L13.4107 14.0892C13.0853 13.7638 13.0853 13.2362 13.4107 12.9107C13.7111 12.6103 14.1838 12.5872 14.5107 12.8414L14.5892 12.9107L17.8215 16.3436C18.4381 16.9602 19.4177 16.9927 20.0725 16.441L20.1785 16.3436L23.4107 12.9107C23.7362 12.5853 24.2638 12.5853 24.5892 12.9107C24.8896 13.2111 24.9127 13.6838 24.6586 14.0107L24.5892 14.0892L21.357 17.5221Z"
      fill={color}
    />
  </svg>
);
export default DropDownIcon;