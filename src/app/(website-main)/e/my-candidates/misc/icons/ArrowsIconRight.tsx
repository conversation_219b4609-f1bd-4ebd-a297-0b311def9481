import * as React from 'react';
import { SVGProps } from 'react';

const ArrowsIconRight = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={25}
    height={20}
    viewBox="0 0 25 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16.5222 7.64297C17.7806 8.90133 17.8225 10.9155 16.6481 12.2242L16.5222 12.357L13.0894 15.5893C12.7639 15.9147 12.2363 15.9147 11.9108 15.5893C11.6104 15.2888 11.5873 14.8162 11.8415 14.4892L11.9108 14.4107L15.3437 11.1785C15.9603 10.5619 15.9928 9.58232 15.4411 8.9275L15.3437 8.82149L11.9108 5.58925C11.5854 5.26382 11.5854 4.73618 11.9108 4.41074C12.2112 4.11034 12.6839 4.08723 13.0109 4.34142L13.0894 4.41074L16.5222 7.64297Z"
      fill="#111C38"
    />
    <g opacity={0.3}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.5222 7.64297C12.7806 8.90133 12.8225 10.9155 11.6481 12.2242L11.5222 12.357L8.08936 15.5893C7.76392 15.9147 7.23628 15.9147 6.91085 15.5893C6.61044 15.2888 6.58733 14.8162 6.84152 14.4892L6.91085 14.4107L10.3437 11.1785C10.9603 10.5619 10.9928 9.58232 10.4411 8.9275L10.3437 8.82149L6.91085 5.58925C6.58541 5.26382 6.58541 4.73618 6.91085 4.41074C7.21125 4.11034 7.68394 4.08723 8.01085 4.34142L8.08936 4.41074L11.5222 7.64297Z"
        fill="#111C38"
      />
    </g>
  </svg>
);
export default ArrowsIconRight;
