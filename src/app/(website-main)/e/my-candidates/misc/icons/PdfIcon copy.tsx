import * as React from "react";
import { SVGProps } from "react";
const PdfIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M18.0751 1.5542L22.2481 5.9042V22.4462H6.6593V22.5002H22.3013V5.95895L18.0751 1.5542Z"
      fill="#909090"
    />
    <path
      d="M18.0232 1.5H6.60596V22.446H22.248V5.90475L18.0232 1.5Z"
      fill="#F4F4F4"
    />
    <path
      d="M6.49123 2.625H1.69873V7.74525H16.7737V2.625H6.49123Z"
      fill="#7A7B7C"
    />
    <path d="M16.854 7.65818H1.79626V2.53418H16.854V7.65818Z" fill="#DD2025" />
    <path
      d="M6.78897 3.40052H5.80872V7.00052H6.57972V5.78627L6.74997 5.79602C6.91539 5.79317 7.07927 5.76354 7.23522 5.70827C7.37194 5.66124 7.49772 5.587 7.60497 5.49002C7.71409 5.39763 7.80013 5.28103 7.85622 5.14952C7.93144 4.93089 7.95831 4.69854 7.93497 4.46852C7.93028 4.3042 7.90147 4.14146 7.84947 3.98552C7.80212 3.87295 7.73186 3.77147 7.64315 3.68754C7.55444 3.60361 7.44923 3.53906 7.33422 3.49802C7.23477 3.46202 7.13203 3.43589 7.02747 3.42002C6.94829 3.4078 6.86833 3.40128 6.78822 3.40052M6.64647 5.12102H6.57972V4.01102H6.72447C6.78835 4.00641 6.85245 4.01622 6.91204 4.03971C6.97163 4.06321 7.02517 4.0998 7.06872 4.14677C7.15895 4.26753 7.20716 4.41452 7.20597 4.56527C7.20597 4.74977 7.20597 4.91702 7.03947 5.03477C6.91952 5.10074 6.78298 5.13119 6.64647 5.12102ZM9.39972 3.39077C9.31647 3.39077 9.23547 3.39677 9.17847 3.39902L8.99997 3.40352H8.41497V7.00352H9.10347C9.36658 7.01073 9.62855 6.96613 9.87447 6.87227C10.0724 6.79376 10.2477 6.6672 10.3845 6.50402C10.5175 6.33936 10.613 6.14764 10.6642 5.94227C10.7231 5.70967 10.7519 5.47045 10.7497 5.23052C10.7643 4.94713 10.7423 4.66306 10.6845 4.38527C10.6296 4.18079 10.5267 3.99234 10.3845 3.83552C10.2729 3.70889 10.1362 3.60674 9.98322 3.53552C9.8518 3.4747 9.71356 3.42988 9.57147 3.40202C9.51496 3.39268 9.45773 3.38842 9.40047 3.38927M9.26397 6.34202H9.18897V4.04402H9.19872C9.35334 4.02623 9.50978 4.05413 9.64872 4.12427C9.75046 4.20551 9.83337 4.30787 9.89172 4.42427C9.95468 4.54677 9.99098 4.68122 9.99822 4.81877C10.005 4.98377 9.99822 5.11877 9.99822 5.23052C10.0013 5.35924 9.99299 5.48799 9.97347 5.61527C9.95035 5.74594 9.90762 5.87237 9.84672 5.99027C9.77778 6.09988 9.68465 6.19225 9.57447 6.26027C9.48193 6.32011 9.37236 6.34804 9.26247 6.33977M13.0725 3.40352H11.25V7.00352H12.021V5.57552H12.996V4.90652H12.021V4.07252H13.071V3.40352"
      fill="#464648"
    />
    <path
      d="M16.3358 15.1911C16.3358 15.1911 18.7268 14.7576 18.7268 15.5744C18.7268 16.3911 17.2455 16.0589 16.3358 15.1911ZM14.568 15.2534C14.1881 15.3373 13.8179 15.4602 13.4633 15.6201L13.7633 14.9451C14.0633 14.2701 14.3745 13.3499 14.3745 13.3499C14.7325 13.9524 15.149 14.5181 15.618 15.0389C15.2643 15.0916 14.9138 15.1637 14.568 15.2549V15.2534ZM13.6215 10.3784C13.6215 9.66664 13.8518 9.47239 14.031 9.47239C14.2103 9.47239 14.412 9.55864 14.4188 10.1766C14.3604 10.7981 14.2302 11.4106 14.031 12.0021C13.7581 11.5055 13.6169 10.9473 13.6208 10.3806L13.6215 10.3784ZM10.1348 18.2654C9.40126 17.8266 11.673 16.4759 12.0848 16.4324C12.0825 16.4331 10.9028 18.7244 10.1348 18.2654ZM19.425 15.6711C19.4175 15.5961 19.35 14.7659 17.8725 14.8011C17.2567 14.7912 16.6411 14.8346 16.0328 14.9309C15.4435 14.3372 14.936 13.6675 14.5238 12.9396C14.7835 12.1892 14.9406 11.4071 14.991 10.6146C14.9693 9.71464 14.754 9.19864 14.064 9.20614C13.374 9.21364 13.2735 9.81739 13.3643 10.7159C13.4532 11.3197 13.6208 11.9092 13.863 12.4694C13.863 12.4694 13.5443 13.4616 13.1228 14.4486C12.7013 15.4356 12.4133 15.9531 12.4133 15.9531C11.6803 16.1917 10.9903 16.5462 10.3695 17.0031C9.75151 17.5784 9.50026 18.0201 9.82576 18.4619C10.1063 18.8429 11.088 18.9291 11.9655 17.7794C12.4318 17.1855 12.8577 16.5611 13.2405 15.9104C13.2405 15.9104 14.5785 15.5436 14.9948 15.4431C15.411 15.3426 15.9143 15.2631 15.9143 15.2631C15.9143 15.2631 17.136 16.4924 18.3143 16.4489C19.4925 16.4054 19.4355 15.7446 19.428 15.6726"
      fill="#DD2025"
    />
    <path
      d="M17.9655 1.55762V5.96237H22.1902L17.9655 1.55762Z"
      fill="#909090"
    />
    <path d="M18.0232 1.5V5.90475H22.2479L18.0232 1.5Z" fill="#F4F4F4" />
    <path
      d="M6.73123 3.3429H5.75098V6.9429H6.52498V5.7294L6.69598 5.73915C6.8614 5.7363 7.02528 5.70667 7.18123 5.6514C7.31794 5.60435 7.44372 5.53011 7.55098 5.43315C7.65928 5.34052 7.74453 5.22394 7.79998 5.09265C7.8752 4.87402 7.90207 4.64167 7.87873 4.41165C7.87404 4.24733 7.84523 4.08459 7.79323 3.92865C7.74588 3.81608 7.67562 3.7146 7.58691 3.63067C7.4982 3.54674 7.39299 3.4822 7.27798 3.44115C7.17808 3.4048 7.07482 3.37842 6.96973 3.3624C6.89055 3.35018 6.81059 3.34367 6.73048 3.3429M6.58873 5.0634H6.52198V3.9534H6.66748C6.73136 3.94879 6.79547 3.9586 6.85505 3.9821C6.91464 4.00559 6.96818 4.04218 7.01173 4.08915C7.10197 4.20991 7.15017 4.3569 7.14898 4.50765C7.14898 4.69215 7.14898 4.8594 6.98248 4.97715C6.86253 5.04312 6.72599 5.07283 6.58948 5.06265M9.34198 3.33315C9.25873 3.33315 9.17773 3.33915 9.12073 3.3414L8.94448 3.3459H8.35948V6.9459H9.04798C9.3111 6.95311 9.57306 6.90852 9.81898 6.81465C10.0169 6.73614 10.1922 6.60958 10.329 6.4464C10.462 6.28174 10.5575 6.09003 10.6087 5.88465C10.6676 5.65205 10.6964 5.41283 10.6942 5.1729C10.7088 4.88952 10.6868 4.60544 10.629 4.32765C10.5741 4.12317 10.4712 3.93472 10.329 3.7779C10.2174 3.65127 10.0808 3.54913 9.92773 3.4779C9.79631 3.41709 9.65807 3.37227 9.51598 3.3444C9.45947 3.33506 9.40224 3.3308 9.34498 3.33165M9.20848 6.2844H9.13348V3.9864H9.14323C9.29785 3.96861 9.45429 3.99651 9.59323 4.06665C9.69497 4.14789 9.77788 4.25025 9.83623 4.36665C9.89919 4.48915 9.93549 4.6236 9.94273 4.76115C9.94948 4.92615 9.94273 5.06115 9.94273 5.1729C9.94578 5.30163 9.9375 5.43037 9.91798 5.55765C9.89486 5.68832 9.85213 5.81475 9.79123 5.93265C9.72229 6.04227 9.62916 6.13463 9.51898 6.20265C9.42644 6.2625 9.31687 6.29042 9.20698 6.28215M13.0147 3.3459H11.1922V6.9459H11.9632V5.5179H12.9382V4.8489H11.9632V4.0149H13.0132V3.3459"
      fill="white"
    />
  </svg>
);
export default PdfIcon;
