import * as React from 'react';
import { SVGProps } from 'react';

const ArrowsIconLeft = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={25}
    height={20}
    viewBox="0 0 25 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.47778 7.64297C7.21942 8.90133 7.17747 10.9155 8.35194 12.2242L8.47778 12.357L11.9106 15.5893C12.2361 15.9147 12.7637 15.9147 13.0892 15.5893C13.3896 15.2888 13.4127 14.8162 13.1585 14.4892L13.0892 14.4107L9.65629 11.1785C9.03967 10.5619 9.00722 9.58232 9.55893 8.9275L9.65629 8.82149L13.0892 5.58925C13.4146 5.26382 13.4146 4.73618 13.0892 4.41074C12.7888 4.11034 12.3161 4.08723 11.9891 4.34142L11.9106 4.41074L8.47778 7.64297Z"
      fill="#111C38"
    />
    <g opacity={0.3}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.4778 7.64297C12.2194 8.90133 12.1775 10.9155 13.3519 12.2242L13.4778 12.357L16.9106 15.5893C17.2361 15.9147 17.7637 15.9147 18.0892 15.5893C18.3896 15.2888 18.4127 14.8162 18.1585 14.4892L18.0892 14.4107L14.6563 11.1785C14.0397 10.5619 14.0072 9.58232 14.5589 8.9275L14.6563 8.82149L18.0892 5.58925C18.4146 5.26382 18.4146 4.73618 18.0892 4.41074C17.7888 4.11034 17.3161 4.08723 16.9891 4.34142L16.9106 4.41074L13.4778 7.64297Z"
        fill="#111C38"
      />
    </g>
  </svg>
);
export default ArrowsIconLeft;
