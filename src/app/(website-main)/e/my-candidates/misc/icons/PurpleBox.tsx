import * as React from 'react';
import { SVGProps } from 'react';

const PurpleBox = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M3.16992 7.44L11.9999 12.55L20.7699 7.47"
      stroke="#755AE2"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 21.61V12.54"
      stroke="#755AE2"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.93014 2.47998L4.59014 5.43998C3.38014 6.10998 2.39014 7.78998 2.39014 9.16998V14.82C2.39014 16.2 3.38014 17.88 4.59014 18.55L9.93014 21.52C11.0701 22.15 12.9401 22.15 14.0801 21.52L19.4201 18.55C20.6301 17.88 21.6201 16.2 21.6201 14.82V9.16998C21.6201 7.78998 20.6301 6.10998 19.4201 5.43998L14.0801 2.46998C12.9301 1.83998 11.0701 1.83998 9.93014 2.47998Z"
      stroke="#755AE2"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default PurpleBox;
