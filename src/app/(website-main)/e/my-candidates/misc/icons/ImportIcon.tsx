import * as React from 'react';
import { SVGProps } from 'react';

const ImportIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={21}
    height={20}
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M7.83907 18.3334H12.8946C17.1076 18.3334 18.7928 16.6667 18.7928 12.5V7.50002C18.7928 3.33335 17.1076 1.66669 12.8946 1.66669H7.83907C3.6261 1.66669 1.94092 3.33335 1.94092 7.50002V12.5C1.94092 16.6667 3.6261 18.3334 7.83907 18.3334Z"
      stroke="white"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.83936 9.59167L10.3671 12.0917L12.8949 9.59167"
      stroke="white"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.3672 12.0917V5.42499"
      stroke="white"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.31152 13.7583C8.58921 14.8416 12.1449 14.8416 15.4226 13.7583"
      stroke="white"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default ImportIcon;
