import * as React from 'react';
import { SVGProps } from 'react';

const RoleFilterIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={18}
    height={18}
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M9 13.65C10.6569 13.65 12 12.3069 12 10.65C12 8.99317 10.6569 7.65002 9 7.65002C7.34315 7.65002 6 8.99317 6 10.65C6 12.3069 7.34315 13.65 9 13.65Z"
      stroke="#755AE2"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.83002 10.725L8.31752 11.2125C8.46002 11.355 8.69252 11.355 8.83502 11.22L10.17 9.98999"
      stroke="#755AE2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.99999 16.5H12C15.015 16.5 15.555 15.2925 15.7125 13.8225L16.275 7.8225C16.4775 5.9925 15.9525 4.5 12.75 4.5H5.24999C2.04749 4.5 1.52249 5.9925 1.72499 7.8225L2.28749 13.8225C2.44499 15.2925 2.98499 16.5 5.99999 16.5Z"
      stroke="#755AE2"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6 4.5V3.9C6 2.5725 6 1.5 8.4 1.5H9.6C12 1.5 12 2.5725 12 3.9V4.5"
      stroke="#755AE2"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16.2375 8.25C14.94 9.195 13.5 9.855 12.0075 10.23"
      stroke="#755AE2"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1.96503 8.45251C3.21753 9.30751 4.58253 9.91501 6.00003 10.26"
      stroke="#755AE2"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default RoleFilterIcon;
