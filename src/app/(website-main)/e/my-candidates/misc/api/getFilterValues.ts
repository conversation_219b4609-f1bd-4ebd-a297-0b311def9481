import { useMutation, useQuery } from '@tanstack/react-query';
import axios from 'axios';

export interface getFilterValuesResponseProps {
  role?: string[];
  group?: string[];
  keyword_matching_percentage?: string[];
  industry?: string[];
  skills?: string[];
  years_of_experience?: string[];
  location?: string[];
  education?: string[];
  language?: string[];
}
export const getFilterValues = async (
  data: string
): Promise<getFilterValuesResponseProps> => {
  const response = await axios.get(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/services/filter_value_params/${data}`
  );

  return response.data;
};

export const useGetFilterValues = (data: string) => {
  return useQuery({
    queryKey: ['FilterValues', data],
    queryFn: () => getFilterValues(data),
  });
};
