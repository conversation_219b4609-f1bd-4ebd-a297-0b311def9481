import { useMutation } from '@tanstack/react-query';
import axios from 'axios';


export const createGroup = async (data: { email: string; name: string }) => {
  const response = await axios.post(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/services/cv_parser_group/`,
    data
  );
  return response.data;
};
export const useCreateGroup = () => {
  return useMutation({
    mutationKey: ['createGroup'],
    mutationFn: createGroup,
  });
};
