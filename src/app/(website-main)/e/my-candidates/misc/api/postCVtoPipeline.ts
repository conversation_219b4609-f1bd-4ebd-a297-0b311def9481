import { Axios } from "@/lib/api/axios";
import { useMutation } from "@tanstack/react-query";


interface MoveToPipelineRequest {
  cvparser_ids: number[];
  job_id: number;
}

const moveToPipeline = async (data: MoveToPipelineRequest) => {
  const res = await Axios.post(`/services/move_candidate_to_job_pipeline/`, data);
  return res.data;
};


export const useMoveCVToPipeline = () => {
  return useMutation({
    mutationFn: moveToPipeline,
    mutationKey: ["moveCVToPipeline"],
  });
};