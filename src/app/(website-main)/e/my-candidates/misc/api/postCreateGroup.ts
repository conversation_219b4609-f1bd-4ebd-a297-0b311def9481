import { Axios } from "@/lib/api/axios"
import { useMutation, useQueryClient } from "@tanstack/react-query"

type createCVGroupProps ={
    name:string;
    role:string;
    keywords?:string[];
    filter_group?: boolean;
}
const createCVGroup = async (data: createCVGroupProps) => {
    const res = await Axios.post("services/cv_parser_group/", data)
    return res.data as APIResponse
}


export const useCreateCVGroup = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: createCVGroup,
        mutationKey: ["createCVGroup"],
        onSuccess: () => {
            queryClient.invalidateQueries(["all-cv-groups"]);
        }
    })
}


interface APIResponse {
  success: boolean;
  status_code: number;
  message: string;
  data: CreateCVGroupResponseData;
}

export interface CreateCVGroupResponseData {
  id: number;
  name: string;
  role: string;
  keywords: any[];
  email: null;
  filter_group: boolean;
  created_at: string;
  updated_at: string;
  company: number;
  cv_count: number;
  unique_id: string | null;
}