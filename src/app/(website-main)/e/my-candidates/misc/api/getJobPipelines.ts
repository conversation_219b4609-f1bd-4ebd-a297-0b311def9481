import { Axios } from "@/lib/api/axios";
import { useQuery } from "@tanstack/react-query";

const getPipelines = async (): Promise<JobPipelinesResponse> => {
  const response = await Axios.get(`/recruiter/fetch_jobs_brief`);
  return response.data;

};


export const useGetJobPipelines = () => {
  return useQuery({
    queryKey: ["job-pipelines-for-my-candidates"],
    queryFn: getPipelines,
    refetchOnWindowFocus: true,
    retry: 2,
  });
}


interface JobPipelinesResponse {
  success: boolean;
  status_code: number;
  message: string;
  data: TJobBrief[];
}

interface TJobBrief {
  id: number;
  job_title: string;
}