import { Axios } from "@/lib/api/axios"
import { useMutation } from "@tanstack/react-query"

interface UploadMultipleCVProps {
  cv_file: File
  group_id: number
}

const uploadMultipleCV = async (data: UploadMultipleCVProps) => {
  const formData = new FormData()
  formData.append("cv_file", data.cv_file)
  formData.append("group_id", data.group_id.toString())

  const res = await Axios.post("services/v2/multiple_cv_upload/", formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  })
  return res.data
}

export const useUploadMultipleCV = () => {
  return useMutation({
    mutationFn: uploadMultipleCV,
    mutationKey: ["uploadMultipleCV"],
  })
}
