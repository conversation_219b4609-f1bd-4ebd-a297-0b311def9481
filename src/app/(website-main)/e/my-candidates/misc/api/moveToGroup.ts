import { Axios } from '@/lib/api/axios';
import { useMutation } from '@tanstack/react-query';


export const moveToGroup = async (data: {
  cv_parser_ids: number[];
    group_id: string;
  group_name?: string;
  email?: string;
}) => {
  const response = await Axios.post(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/services/move_cv_parser_to_group/${data.group_id}/`,
    data
  );
  return response.data;
};
export const useMoveToGroup = () => {
  return useMutation({
    mutationKey: ['moveToGroup'],
    mutationFn: moveToGroup,
  });
};
