import { Axios } from "@/lib/api/axios";
import { useQuery } from "@tanstack/react-query";
import { ICVData } from "./getCVGroupData";

const getDetails = async (id: string | number) => {
    const res = await Axios.get(`/services/view-cv-cvgroup/${id}/`);
    return res.data?.data as CVData;
}

export const useGetCVDetails = (id: string | number, enabled: boolean) => {
    return useQuery({
        queryFn: () => getDetails(id),
        queryKey: ["cv-details", id],
        enabled 
    })
}


interface APIResponse {
  success: boolean;
  status_code: number;
  message: string;
  data: CVData;
}

interface CVData {
  id: number;
  cv_file: string;
  cv_data: null;
  ocr_cv_data: null;
  cv_text_data: string;
  old_cv_data_dict: string | null;
  candidate_fullname: string | null;
  parser_data: string;
  role: string;
  keywords: string[];
  industry: string;
  skills: string[];
  location: string;
  work_experience: string |null;
  years_of_experience: string;
  education: string[];
  language: string;
  keyword_matching_percentage: number;
  tools: string[];
  unique_id: string;
  still_in_job: boolean;
  type_of_file_uploaded: string;
  completed: boolean;
  job_application_id: string | null;
  cv_file_url: string;
  created_at: string;
  updated_at: string;
  group: number;
}