import { useMutation } from '@tanstack/react-query';
import axios from 'axios';
import { useUser } from '@/lib/contexts/UserContext';


const uploadBulkCv = async (data: any) => {
  const formData = new FormData();
  formData.append('role', data.role);
  formData.append('group_name', data?.groupName);
  formData.append('email', data?.email);
  data.cvs.forEach((cv: any) => {
    formData.append('cv_files', cv);
  });
  formData.append(
    'keywords',
    data.keywords.map((keyword: string) => keyword)
  );

  const response = await axios.post(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/services/multiple_cv_upload/`,
    formData,
    {
      headers: { 'Content-Type': 'multipart/form-data' },
    }
  );

  return response.data;
};

export const useUploadBulkCv = () => {
  return useMutation({
    mutationFn: uploadBulkCv,
  });
};
