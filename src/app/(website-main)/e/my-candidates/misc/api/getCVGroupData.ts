import { useQuery } from "@tanstack/react-query"
import { Axios } from "@/lib/api/axios"

interface FilterState {
  roles: string[]
  industries: string[]
  locations: string[]
  languages: string[]
  yearsOfExperience: string
  minKeywordPercentage: number
  maxKeywordPercentage: number
}

interface GetGroupCVsParams {
  page?: number
  search?: string
  filters?: FilterState
}
interface APIResponse {
  success: boolean;
  status_code: number;
  message: string;
  data: Data;
}

interface Data {
  count: number;
  total_pages: number;
  current_page: number;
  next: string;
  previous: null;
  results: ICVData[];
}

export interface ICVData {
  id: number;
  cv_file: string;
  cv_data: any | null;
  ocr_cv_data: null;
  cv_text_data: string;
  old_cv_data_dict: null;
  candidate_fullname: string | null;
  parser_data: string;
  role: string;
  keywords: string[];
  industry: string;
  skills: string[];
  location: string;
  application_count: number;
  work_experience: string | null;
  years_of_experience: string;
  education: string[];
  language: string;
  keyword_matching_percentage: number;
  tools: string[];
  unique_id: string;
  still_in_job: boolean;
  type_of_file_uploaded: string;
  completed: boolean;
  job_application_id: string | null;
  cv_file_url: string;
  created_at: string;
  updated_at: string;
  group: number;
}




const getGroupCVs = async (groupId: string, params: GetGroupCVsParams = {}): Promise<APIResponse> => {
  const { page = 1, search = "", filters } = params

  let url = `services/fetch_cv_group_cvs/${groupId}?page=${page}`

  if (search) {
    url += `&search=${encodeURIComponent(search)}`
  }

  if (filters) {
    if (filters.roles.length > 0) {
      url += `&role=${filters.roles.join(",")}`
    }
    if (filters.industries.length > 0) {
      url += `&industry=${filters.industries.join(",")}`
    }
    if (filters.locations.length > 0) {
      url += `&location=${filters.locations.join(",")}`
    }
    if (filters.languages.length > 0) {
      url += `&language=${filters.languages.join(",")}`
    }
    if (filters.yearsOfExperience) {
      url += `&years_of_experience=${filters.yearsOfExperience}`
    }
    if (filters.minKeywordPercentage > 0 || filters.maxKeywordPercentage < 100) {
      url += `&keywords_percentage=${filters.minKeywordPercentage},${filters.maxKeywordPercentage}`
    }
  }

  const res = await Axios.get(url)
  return res.data
}

export const useGetGroupCVs = (groupId: string, params: GetGroupCVsParams = {}) => {
  return useQuery({
    queryKey: ["cvs-in-a-group", groupId, params],
    queryFn: () => getGroupCVs(groupId, params),
    enabled: !!groupId,
    refetchInterval: 30 * 1000,
  })
}
