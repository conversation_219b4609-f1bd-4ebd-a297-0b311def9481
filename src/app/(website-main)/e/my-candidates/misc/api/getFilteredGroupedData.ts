import { useMutation, useQuery } from '@tanstack/react-query';
import axios from 'axios';


export const getFilteredGroupedData = async (data: any): Promise<any> => {
  const response = await axios.get(`${data}`);

  return response.data;
};

export const useGetFilteredGroupedData = () => {
  return useMutation({
    mutationKey: ['filteredgroupedData'],
    mutationFn: getFilteredGroupedData,
  });
};