import { Axios } from "@/lib/api/axios"
import { useQuery } from "@tanstack/react-query"

export interface CVGroup {
    id: number
    name: string
    role: string
    keywords: string[] | null
    email: string | null
    filter_group: boolean
    created_at: string
    updated_at: string
    company: number
    cv_count: number
    unique_id: string | null
}

interface GroupedDataResponse {
    success: boolean
    status_code: number
    message: string
    data: {
        count: number
        total_pages: number
        current_page: number
        next: string | null
        previous: string | null
        results: CVGroup[]
    }
}




const getGroupedData = async (page = 1): Promise<GroupedDataResponse> => {
    const res = await Axios.get(`services/cv_parser_group/?page=${page}`)
    return res.data
}

export const useGetCVGroups = (page = 1) => {
    return useQuery({
        queryKey: ["all-cv-groups", page],
        queryFn: () => getGroupedData(page),
        keepPreviousData: true,
    })
}
