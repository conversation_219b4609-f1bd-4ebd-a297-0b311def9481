import { Axios } from "@/lib/api/axios"
import { useQuery } from "@tanstack/react-query"
import { CVGroup } from "./getCVGroups";

interface GroupedDataResponse {
  success: boolean;
  status_code: number;
  message: string;
  data: CVGroup[];
}

const getGroupedData = async (page = 1): Promise<GroupedDataResponse> => {
    const res = await Axios.get(`/services/cv_parser_group/filter?page=${page}`)
    return res.data
}

export const useGetFilteredCVGroups = (page = 1) => {
    return useQuery({
        queryKey: ["filtered-cv-groups", page],
        queryFn: () => getGroupedData(page),
    })
}
