import { useMutation, useQuery } from '@tanstack/react-query';
import axios from 'axios';

interface APIResponse {
  success: boolean;
  status_code: number;
  message: string;
  data: Data;
}

interface Data {
  download_link: string;
}

export const exportCvFiles = async (data: {
  cv_ids: number[];
  group_id: string;
}): Promise<APIResponse> => {
  const response = await axios.post(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/services/download_filtered_cvs/`,
    data,
  );

  return response.data as APIResponse;
};

export const useExportCvFiles = () => {
  return useMutation({
    mutationKey: ['exportCvFiles'],
    mutationFn: exportCvFiles,
  });
};
