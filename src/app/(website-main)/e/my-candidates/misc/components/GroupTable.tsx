import { createColumnHelper, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import moment from 'moment';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React, { useEffect } from 'react';
import EllipsesVerticalIcon from '@/components/icons/jsx/EllipsesVerticalIcon';
import ThreeDots from '../icons/ThreeDots';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/shared';
import { DoubleForward } from '@/components/shared/icons';
import { CVGroup } from '../api/getCVGroups';


const columnHelper = createColumnHelper<CVGroup>();

interface GroupTableProps {
  tableData: Array<CVGroup>;
  currentPage: number;
  showRole: boolean;
}
const GroupTable: React.FunctionComponent<GroupTableProps> = ({
  tableData,
  currentPage,
  showRole,
}) => {
  const columns = [
    // columnHelper.accessor('index', {
    {
      id: 'select',
      header: 'ID',
      cell: (info: any) => info.row.index + 1 + (currentPage - 1) * 10,
    },
    // }),
    columnHelper.accessor(row => row.name, {
      id: 'name',
      cell: info => (
        <div className="flex items-center gap-x-2">
          <div className="flex h-7 w-7 items-center justify-center rounded-full bg-primary">
            <p className="text-center text-[10px] uppercase text-white">
              {info.getValue().slice(0, 2)}
            </p>
          </div>
          <p className='text-sm'>{info.getValue()}</p>
        </div>
      ),
      header: () => <span>Group Name</span>,
    }),
    columnHelper.accessor('cv_count', {
      header: () => 'Candidates',
      cell: info => (
        <p className="max-w-[300px] truncate text-sm">{info.renderValue()}</p>
      ),
    }),
    columnHelper.accessor('role', {
      header: () => 'Role At Upload',
      cell: info => (
        <p className="max-w-[300px] truncate text-sm">{info.renderValue()}</p>
      ),
    }),
    columnHelper.accessor('created_at', {
      header: 'Created at',
      cell: info => (
        <p className="whitespace-nowrap text-sm">
          {moment(info.renderValue()).format('DD-MM-yyyy')}
        </p>
      ),
    }),
    columnHelper.accessor('updated_at', {
      header: 'Updated at',
      cell: info => (
        <p className="whitespace-nowrap text-sm">
          {moment(info.renderValue()).format('DD-MM-yyyy')}
        </p>
      ),
    }),
    {
      id: 'action',
      header: 'Action',
      cell: ({ row }: { row: any }) => (
        <Link
          href={`/e/my-candidates/cv-group?group_id=${row.original.id}&group_name=${row.original.name}&unique_id=${row.original.unique_id}`}
          className="flex items-center gap-5 bg-[#F8F9FB] rounded-lg my-3 py-2 px-4 outline-1 outline-[#EAE6FB] max-w-max"
        >
          <p className="text-[13px]">VIew Details</p>
          <span className='opacity-50'>
            <DoubleForward />
          </span>
        </Link>
        // <Popover>
        //   <PopoverTrigger className='flex items-center gap-5 bg-[#F8F9FB] rounded-lg my-3 py-2 px-4'>
        //     <p className="text-[13px]">Action</p>
        //     <span className='opacity-70'>
        //       <EllipsesVerticalIcon />
        //     </span>
        //   </PopoverTrigger>
        //   <PopoverContent align='end'>
        //     FRE
        //   </PopoverContent>
        // </Popover>
      ),
    },
  ];

  const router = useRouter();
  const [data, setData] = React.useState(() => [...tableData]);
  const rerender = React.useReducer(() => ({}), {})[1];
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onStateChange() {
      rerender();
    },
  });

  useEffect(() => {
    setData([...tableData]);
  }, [tableData]);
  return (
    <table className="w-full">
      <thead className="w-full">
        {table.getHeaderGroups().map((headerGroup, index) => (
          <tr key={index}>
            {headerGroup.headers.map(header => (
              <th
                className="mb-2 border-b px-4 py-2 text-left font-medium whitespace-nowrap"
                key={header.id}
              >
                {header.isPlaceholder
                  ? null
                  : flexRender(
                    header.column.columnDef.header,
                    header.getContext()
                  )}
              </th>
            ))}
          </tr>
        ))}
      </thead>
      <tbody className="w-full">
        {table.getRowModel().rows.map(row => (
          <tr
            className="w-full cursor-pointer border-b transition-all hover:bg-[#F3F1FF] whitespace-nowrap rounded-md"
            key={row.id}
          // onClick={() =>
          //   router.push(
          //     `/e/my-candidates/group?group_id=${row.original.id}&group_name=${row.original.name}&unique_id=${row.original.unique_id}`
          //   )
          // }
          >
            {row.getVisibleCells().map(cell => (
              <td className="px-4" key={cell.id}>
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </td>
            ))}
          </tr>
        ))}
      </tbody>
      <tfoot>
        {table.getFooterGroups().map(footerGroup => (
          <tr key={footerGroup.id}>
            {footerGroup.headers.map(header => (
              <th key={header.id}>
                {header.isPlaceholder
                  ? null
                  : flexRender(
                    header.column.columnDef.footer,
                    header.getContext()
                  )}
              </th>
            ))}
          </tr>
        ))}
      </tfoot>
    </table>
  );
};

export default GroupTable;