"use client"

import type React from "react"

import { useState, useMemo } from "react"
import { FixedSizeList as List } from "react-window"
import { useUser } from "@/lib/contexts/UserContext"
import { Search } from "@/components/shared/icons"
import { Button } from "@/components/shared"
import { useGetCVGroups } from "../api/getCVGroups"
import { cn } from "@/utils"

interface CVGroup {
  id: number
  name: string
  role: string
  keywords: string[] | null
  cv_count: number
}

interface ImportBulkCVGroupSelectorProps {
  selectedGroupId: number | null
  onGroupSelect: (group: CVGroup) => void
  onCreateNew: () => void
  disabled: boolean
}

const GroupItem = ({ index, style, data, disabled }: any) => {
  const { groups, selectedGroupId, onGroupSelect } = data
  const group = groups[index]
  const isSelected = selectedGroupId === group.id

  return (
    <div
      style={style}
  
      className={cn(
        "flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50",
        isSelected ? "bg-primary-light border-l-4 border-primary-normal-hover" : "",
        disabled && "opacity-50 cursor-not-allowed"
      )}
      onClick={() => {
        if (disabled) return
        onGroupSelect(group)
      }}
    > 
      <div className="flex-1">
        <p className="font-medium text-sm">{group.name}</p>
        <p className="text-xs text-gray-500">{group.role}</p>
      </div>
      <div className="text-xs text-gray-400">{group.cv_count} CVs</div>
    </div>
  )
}

export const ImportBulkCVGroupSelector: React.FC<ImportBulkCVGroupSelectorProps> = ({ selectedGroupId, onGroupSelect, onCreateNew, disabled=false }) => {
  const { user } = useUser()
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)

  const { data: groupsData, isLoading } = useGetCVGroups(currentPage)

  const filteredGroups = useMemo(() => {
    if (!groupsData?.data.results) return []

    return groupsData.data.results.filter(
      (group: CVGroup) =>
        group.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        group.role.toLowerCase().includes(searchQuery.toLowerCase()),
    )
  }, [groupsData?.data.results, searchQuery])

  if (isLoading) {
    return <div className="p-4 text-center">Loading groups...</div>
  }

  return (
    <div className="border rounded-lg bg-white">
      <div className="p-3 border-b">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search groups..."
            value={searchQuery}
            disabled={disabled}
            readOnly={disabled}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      <div className="max-h-60 overflow-hidden">
        {filteredGroups.length > 0 ? (
          <List
            height={Math.min(240, filteredGroups.length * 60)}
            width={"100%"}
            itemCount={filteredGroups.length}
            itemSize={60}            
            itemData={{
              groups: filteredGroups,
              selectedGroupId,
              onGroupSelect,
            }}
          >
            {GroupItem}
          </List>
        ) : (
          <div className="p-4 text-center text-gray-500">
            {searchQuery ? "No groups found matching your search" : "No groups available"}
          </div>
        )}
      </div>

      <div className="p-3 border-t">
        <Button onClick={onCreateNew} variant="outlined" size="small" className="w-full">
          + Create New Group
        </Button>
      </div>
    </div>
  )
}
