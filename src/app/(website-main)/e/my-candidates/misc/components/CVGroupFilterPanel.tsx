"use client"

import { useState } from "react"
import { ChevronDown, ChevronUp } from "lucide-react"
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  Checkbox2,
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Label,
} from "@/components/shared"
import { Menu } from "@headlessui/react"

// import Slider from "react-slick"
import { Slider } from "@/components/shared/slider"
import { SelectTrigger, SelectContent, SelectItem, SelectValue, Select } from "@/components/shared/select"

interface FilterState {
  roles: string[]
  industries: string[]
  locations: string[]
  languages: string[]
  yearsOfExperience: string
  minKeywordPercentage: number
  maxKeywordPercentage: number
}

interface FilterOptions {
  role?: string[]
  industry?: string[]
  location?: string[]
  language?: string[]
  years_of_experience?: string[]
}

interface FilterPanelProps {
  filters: FilterState
  filterOptions?: FilterOptions
  onFilterChange: (filters: Partial<FilterState>) => void
  onClearFilters: () => void
}

export default function CVGroupFilterPanel({
  filters,
  filterOptions,
  onFilterChange,
  onClearFilters,
}: FilterPanelProps) {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    roles: true,
    experience: false,
    location: false,
    industry: false,
    language: false,
    keywords: false,
  })

  const toggleSection = (section: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }))
  }

  const handleRoleChange = (role: string, checked: boolean) => {
    const newRoles = checked ? [...filters.roles, role] : filters.roles.filter((r) => r !== role)
    onFilterChange({ roles: newRoles })
  }

  const handleIndustryChange = (industry: string, checked: boolean) => {
    const newIndustries = checked ? [...filters.industries, industry] : filters.industries.filter((i) => i !== industry)
    onFilterChange({ industries: newIndustries })
  }

  const handleLocationChange = (location: string, checked: boolean) => {
    const newLocations = checked ? [...filters.locations, location] : filters.locations.filter((l) => l !== location)
    onFilterChange({ locations: newLocations })
  }

  const handleLanguageChange = (language: string, checked: boolean) => {
    const newLanguages = checked ? [...filters.languages, language] : filters.languages.filter((l) => l !== language)
    onFilterChange({ languages: newLanguages })
  }

  const handleKeywordPercentageChange = (values: number[]) => {
    onFilterChange({
      minKeywordPercentage: values[0],
      maxKeywordPercentage: values[1],
    })
  }

  return (
    <div className="space-y-4">
      <section className="my-1 flex items-stretch w-full gap-x-2 rounded bg-white p-2">
        <Button className="rounded-lg bg-primary p-2 px-3 text-sm text-white min-w-max" onClick={onClearFilters}>
          Clear filters
        </Button>
        <div className="flex w-full gap-x-2 whitespace-nowrap">
          {/* Role Filter Menu */}
          {filterOptions?.role && filterOptions.role.length > 0 && (
            <div className={`${filters.roles.length > 0 ? "order-first" : ""}`}>
              <Menu>
                {({ open }) => (
                  <div className="relative  h-full">
                    <Menu.Button
                      className={`flex min-w-[120px] h-full items-center justify-center gap-x-2 rounded-lg p-1 px-2 text-sm ${
                        filters.roles.length > 0 ? "bg-primary text-white" : "bg-primary-light text-primary"
                      }`}
                    >
                      <span>Role</span>
                      <ChevronDown className={`h-4 w-4 transition-transform ${open ? "rotate-180" : ""}`} />
                    </Menu.Button>
                    <Menu.Items className="absolute -bottom-2 left-0 z-50 flex min-h-max max-h-[350px] min-w-max max-w-[400px] translate-y-full flex-col rounded-lg border bg-white px-2 py-2">
                      <div className="h-full overflow-y-scroll">
                        {filterOptions.role?.map((role) => (
                          <div className="flex gap-x-2 rounded-md p-2 text-sm hover:bg-primary-light" key={role}>
                            <label className="flex gap-x-2 whitespace-nowrap">
                              <input
                                type="checkbox"
                                className="checkbox-purple pr-2"
                                checked={filters.roles.includes(role)}
                                onChange={(e) => handleRoleChange(role, e.target.checked)}
                              />
                              <p>{role}</p>
                            </label>
                          </div>
                        ))}
                      </div>
                      <div className="flex w-max items-center justify-end gap-x-2 p-2">
                        <button
                          className="rounded-lg bg-primary-light p-2 px-3 text-primary text-sm"
                          onClick={() => onFilterChange({ roles: [] })}
                        >
                          reset filters
                        </button>
                      </div>
                    </Menu.Items>
                  </div>
                )}
              </Menu>
            </div>
          )}

          {/* Years of Experience Filter Menu */}
          {filterOptions?.years_of_experience && filterOptions.years_of_experience.length > 0 && (
            <div className={`${filters.yearsOfExperience ? "order-first" : ""}`}>
              <Menu>
                {({ open }) => (
                  <div className="relative  h-full">
                    <Menu.Button
                      className={`flex min-w-[120px] h-full items-center justify-center gap-x-2 rounded-lg p-1 px-2 ${
                        filters.yearsOfExperience ? "bg-primary text-white" : "bg-primary-light text-primary"
                      }`}
                    >
                      <span className="text-sm">Years of Exp.</span>
                      <ChevronDown className={`h-4 w-4 transition-transform ${open ? "rotate-180" : ""}`} />
                    </Menu.Button>
                    <Menu.Items className="absolute -bottom-2 left-0 z-50 flex min-h-max max-h-[350px] text-sm min-w-[150%] translate-y-full flex-col rounded-lg border bg-white px-2 py-2">
                      <div className="h-full overflow-y-scroll">
                        {filterOptions.years_of_experience?.map((exp) => (
                          <div className="flex gap-x-2 rounded-md p-2 hover:bg-primary-light" key={exp}>
                            <label className="flex gap-x-2 whitespace-nowrap">
                              <input
                                type="radio"
                                name="years_of_experience"
                                checked={filters.yearsOfExperience === exp}
                                onChange={() => onFilterChange({ yearsOfExperience: exp })}
                              />
                              {exp}
                            </label>
                          </div>
                        ))}
                      </div>
                      <div className="flex w-max items-center justify-end gap-x-2 p-2">
                        <button
                          className="rounded-lg bg-primary-light p-2 px-3 text-primary"
                          onClick={() => onFilterChange({ yearsOfExperience: "" })}
                        >
                          reset filters
                        </button>
                      </div>
                    </Menu.Items>
                  </div>
                )}
              </Menu>
            </div>
          )}

          {/* Industry Filter Menu */}
          {filterOptions?.industry && filterOptions.industry.length > 0 && (
            <div className={`${filters.industries.length > 0 ? "order-first" : ""}`}>
              <Menu>
                {({ open }) => (
                  <div className="relative h-full">
                    <Menu.Button
                      className={`flex min-w-[120px] h-full items-center justify-center gap-x-2 rounded-lg p-1 px-2 ${
                        filters.industries.length > 0 ? "bg-primary text-white" : "bg-primary-light text-primary"
                      }`}
                    >
                      <span className="text-sm">Industry</span>
                      <ChevronDown className={`h-4 w-4 transition-transform ${open ? "rotate-180" : ""}`} />
                    </Menu.Button>
                    <Menu.Items className="absolute -bottom-2 left-0 z-50 flex min-h-max max-h-[350px] min-w-[150%] translate-y-full flex-col rounded-lg border bg-white px-2 py-2">
                      <div className="h-full overflow-y-scroll">
                        {filterOptions.industry?.map((industry) => (
                          <div className="flex gap-x-2 rounded-md p-2 hover:bg-primary-light text-sm" key={industry}>
                            <label className="flex gap-x-2 whitespace-nowrap">
                              <input
                                type="checkbox"
                                className="checkbox-purple"
                                checked={filters.industries.includes(industry)}
                                onChange={(e) => handleIndustryChange(industry, e.target.checked)}
                              />
                              {industry}
                            </label>
                          </div>
                        ))}
                      </div>
                      <div className="flex w-max items-center justify-end gap-x-2 p-2">
                        <button
                          className="whitespace-nowrap rounded-lg text-sm bg-primary-light p-2 px-3 text-primary"
                          onClick={() => onFilterChange({ industries: [] })}
                        >
                          reset filters
                        </button>
                      </div>
                    </Menu.Items>
                  </div>
                )}
              </Menu>
            </div>
          )}

          {/* Location Filter Menu */}
          {filterOptions?.location && filterOptions.location.length > 0 && (
            <div className={`${filters.locations.length > 0 ? "order-first" : ""}`}>
              <Menu>
                {({ open }) => (
                  <div className="relative  h-full">
                    <Menu.Button
                      className={`flex min-w-[120px] h-full items-center justify-center gap-x-2 rounded-lg p-1 px-2 ${
                        filters.locations.length > 0 ? "bg-primary text-white" : "bg-primary-light text-primary"
                      }`}
                    >
                      <span className="text-sm">Location</span>
                      <ChevronDown className={`h-4 w-4 transition-transform ${open ? "rotate-180" : ""}`} />
                    </Menu.Button>
                    <Menu.Items className="absolute -bottom-2 left-0 z-50 flex min-h-max max-h-[350px] text-sm min-w-[150%] translate-y-full flex-col rounded-lg border bg-white px-2 py-2">
                      <div className="h-full overflow-y-scroll">
                        {filterOptions.location?.map((location) => (
                          <div className="flex gap-x-2 rounded-md p-2 hover:bg-primary-light text-sm" key={location}>
                            <label className="flex gap-x-2 whitespace-nowrap">
                              <input
                                type="checkbox"
                                className="checkbox-purple"
                                checked={filters.locations.includes(location)}
                                onChange={(e) => handleLocationChange(location, e.target.checked)}
                              />
                              {location}
                            </label>
                          </div>
                        ))}
                      </div>
                      <div className="flex w-max items-center justify-end gap-x-2 p-2">
                        <button
                          className="rounded-lg bg-primary-light p-2 px-3 text-primary"
                          onClick={() => onFilterChange({ locations: [] })}
                        >
                          reset filters
                        </button>
                      </div>
                    </Menu.Items>
                  </div>
                )}
              </Menu>
            </div>
          )}

          {/* Language Filter Menu */}
          {filterOptions?.language && filterOptions.language.length > 0 && (
            <div className={`${filters.languages.length > 0 ? "order-first" : ""}`}>
              <Menu>
                {({ open }) => (
                  <div className="relative  h-full">
                    <Menu.Button
                      className={`flex min-w-[120px] h-full items-center justify-center gap-x-2 rounded-lg p-1 px-2 ${
                        filters.languages.length > 0 ? "bg-primary text-white" : "bg-primary-light text-primary"
                      }`}
                    >
                      <span className="text-sm">Language</span>
                      <ChevronDown className={`h-4 w-4 transition-transform ${open ? "rotate-180" : ""}`} />
                    </Menu.Button>
                    <Menu.Items className="absolute -bottom-2 left-0 z-50 flex min-h-max max-h-[400px] text-sm min-w-[150%] translate-y-full flex-col rounded-lg border bg-white px-2 py-2">
                      <div className="h-full overflow-y-scroll py-2">
                        {filterOptions.language?.map((language) => (
                          <div className="flex gap-x-2 rounded-md p-2 hover:bg-primary-light text-sm" key={language}>
                            <label className="flex gap-x-2 whitespace-nowrap">
                              <input
                                type="checkbox"
                                className="checkbox-purple"
                                checked={filters.languages.includes(language)}
                                onChange={(e) => handleLanguageChange(language, e.target.checked)}
                              />
                              {language}
                            </label>
                          </div>
                        ))}
                      </div>
                      <div className="flex w-max items-center justify-end gap-x-2 p-2">
                        <button
                          className="rounded-lg bg-primary-light p-2 px-3 text-primary"
                          onClick={() => onFilterChange({ languages: [] })}
                        >
                          reset filters
                        </button>
                      </div>
                    </Menu.Items>
                  </div>
                )}
              </Menu>
            </div>
          )}

          {/* Keyword Percentage Filter Menu */}
          <div className={`${filters.minKeywordPercentage > 0 ? "order-first" : ""}`}>
            <Menu>
              {({ open }) => (
                <div className="relative  h-full">
                  <Menu.Button
                    className={`flex min-w-[120px] h-full items-center justify-center gap-x-2 rounded-lg p-1 px-2 ${
                      filters.minKeywordPercentage > 0 ? "bg-primary text-white" : "bg-primary-light text-primary"
                    }`}
                  >
                    <span className="text-sm">Keyword</span>
                    <ChevronDown className={`h-4 w-4 transition-transform ${open ? "rotate-180" : ""}`} />
                  </Menu.Button>
                  <Menu.Items className="absolute -bottom-2 right-0 z-50 flex min-h-max max-h-[400px] text-sm min-w-[200px] translate-y-full flex-col rounded-lg border bg-white px-2 py-2">
                    <div className="h-full overflow-y-scroll p-2">
                      <p className="whitespace-normal text-sm px-2 mb-4">Filter by minimum keyword match percentage</p>
                      <div className="space-y-4">
                        <Slider
                          value={[filters.minKeywordPercentage, filters.maxKeywordPercentage]}
                          onValueChange={handleKeywordPercentageChange}
                          max={100}
                          min={0}
                          step={5}
                          className="w-full"
                        />
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>{filters.minKeywordPercentage}%</span>
                          <span>{filters.maxKeywordPercentage}%</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex w-max items-center justify-end gap-x-2 p-2">
                      <button
                        className="rounded-lg bg-primary-light p-2 px-3 text-primary"
                        onClick={() => onFilterChange({ minKeywordPercentage: 0, maxKeywordPercentage: 100 })}
                      >
                        reset filters
                      </button>
                    </div>
                  </Menu.Items>
                </div>
              )}
            </Menu>
          </div>
        </div>
      </section>
   
    </div>
  )
}
