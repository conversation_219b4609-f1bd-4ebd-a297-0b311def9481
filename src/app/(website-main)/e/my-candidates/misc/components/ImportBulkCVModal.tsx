"use client"

import { Dialog } from "@headlessui/react"
import { useQueryClient } from "@tanstack/react-query"
import { filesize } from "filesize"
import type React from "react"
import { useCallback, useState } from "react"
import { useDropzone } from "react-dropzone"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import toast from "react-hot-toast"
import CloseIcon from "@/app/(website-main)/t/dashboard/misc/icons/CloseIcon"
import UploadIcon from "@/app/(website-main)/t/showcase/misc/components/cv-upload/icons/UploadIcon"
import Loader from "@/components/shared/loader"
import DocIcon from "../icons/DocxIcon"
import PdfIcon from "../icons/PdfIcon"
import { z } from "zod"
import { useUploadMultipleCV } from "../api/postUploadMultipleCV"
import { useCreateCVGroup } from "../api/postCreateGroup"
import { ImportBulkCVGroupSelector } from "./ImportBulkCVGroupSelector"



export const importSchema = z.object({
  cv_file: z
    .instanceof(File)
    .refine((file) => file.size <= 10 * 1024 * 1024, "File size must be less than 10MB")
    .refine(
      (file) => ["application/pdf", "application/zip", "application/x-zip-compressed"].includes(file.type),
      "Only PDF and ZIP files are allowed",
    ),
  group_id: z.number().min(1, "Please select a group"),
})

export type ImportFormData = z.infer<typeof importSchema>

interface ImportBulkCVModalProps {
  isOpen: boolean
  closeModal: () => void
  defaultGroup?: CVGroup
  onComplete?: () => void
}

interface CVGroup {
  id: number
  name: string
  role: string
  keywords: string[] | null
  cv_count: number
}

const ImportBulkCVModal: React.FC<ImportBulkCVModalProps> = ({ isOpen, closeModal, defaultGroup, onComplete }) => {
  const queryClient = useQueryClient()

  // Form setup with Zod validation
  const {
    control,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<ImportFormData>({
    resolver: zodResolver(importSchema),
    defaultValues: {
      group_id: defaultGroup?.id || 0,
    },
  })

  const watchedFile = watch("cv_file")
  const watchedGroupId = watch("group_id")
  if (isOpen) {

    console.log("watched group id", watchedGroupId)
    console.log(defaultGroup?.id, "default group id")
  }

  // State management
  const [showUploadProgress, setShowUploadProgress] = useState(false)
  const [progress, setProgress] = useState(0)
  const [showGroupSelector, setShowGroupSelector] = useState(!!defaultGroup)
  const [showCreateGroup, setShowCreateGroup] = useState(false)
  const [selectedGroup, setSelectedGroup] = useState<CVGroup | null>(defaultGroup || null)

  // API hooks
  const { mutate: uploadCV, isLoading: uploadLoading } = useUploadMultipleCV()
  const { mutate: createGroup, isLoading: createGroupLoading } = useCreateCVGroup()

  const closeImportBulkCVModal = () => {
    reset()
    setShowUploadProgress(false)
    setShowGroupSelector(false)
    setShowCreateGroup(false)
    setSelectedGroup(null)
    setProgress(0)
    closeModal()
  }

  // File upload handling
  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        setValue("cv_file", acceptedFiles[0])
        if (defaultGroup) {
          setValue("group_id", defaultGroup?.id || 0)
        }
        setShowUploadProgress(true)
        // Simulate upload progress
        setProgress(0)
        const interval = setInterval(() => {
          setProgress((prev) => {
            if (prev >= 100) {
              clearInterval(interval)
              return 100
            }
            return prev + 20
          })
        }, 500)
      }
    },
    [setValue],
  )

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "application/pdf": [".pdf"],
      "application/zip": [".zip"],
      "application/x-zip-compressed": [".zip"],
    },
    maxSize: 10 * 1024 * 1024,
    multiple: false,
  })

  const closeFile = () => {
    setShowUploadProgress(false)
    setValue("cv_file", undefined as any)
    setProgress(0)
  }

  const handleGroupSelect = (group: CVGroup) => {
    setSelectedGroup(group)
    setValue("group_id", group.id)
    setShowGroupSelector(false)
  }

  const handleCreateNewGroup = () => {
    setShowGroupSelector(false)
    setShowCreateGroup(true)
  }

  // Create group form data
  const [newGroupData, setNewGroupData] = useState({
    name: "",
    role: "",
    keywords: [] as string[],
  })

  const [keyword, setKeyword] = useState("")

  const addKeyword = () => {
    if (keyword.trim()) {
      setNewGroupData((prev) => ({
        ...prev,
        keywords: [...prev.keywords, keyword.trim()],
      }))
      setKeyword("")
    }
  }

  const removeKeyword = (index: number) => {
    setNewGroupData((prev) => ({
      ...prev,
      keywords: prev.keywords.filter((_, i) => i !== index),
    }))
  }

  const handleCreateGroup = () => {
    if (!newGroupData.name || !newGroupData.role) {
      toast.error("Please fill in group name and role")
      return
    }

    createGroup(
      {
        name: newGroupData.name,
        role: newGroupData.role,
        keywords: newGroupData.keywords,
      },
      {
        onSuccess: (response) => {
          const newGroup: CVGroup = {
            id: response?.data.id,
            name: newGroupData.name,
            role: newGroupData.role,
            keywords: newGroupData.keywords,
            cv_count: 0,
          }
          setSelectedGroup(newGroup)
          setValue("group_id", response?.data.id)
          setShowCreateGroup(false)
          toast.success("Group created successfully!")
          queryClient.invalidateQueries({ queryKey: ["all-cv-groups"] })
        },
        onError: (error: any) => {
          toast.error(error?.response?.data?.error || "Failed to create group")
        },
      },
    )
  }

  // Form submission
  const onSubmit = (data: ImportFormData) => {
    uploadCV(
      {
        cv_file: data.cv_file,
        group_id: data.group_id,
      },
      {
        onSuccess: () => {
          toast.success("CVs uploaded successfully!")
          onComplete?.()
          closeImportBulkCVModal()
        },
        onError: (error: any) => {
          toast.error(error?.response?.data?.error || "Failed to upload CVs")
        },
      },
    )
  }

  const isZipFile = watchedFile?.type === "application/zip" || watchedFile?.type === "application/x-zip-compressed"

  return (
    <Dialog open={isOpen} onClose={closeImportBulkCVModal}>
      <div className="fixed inset-0 z-30 bg-[#000000]/70 backdrop-blur-lg">
        <Dialog.Title></Dialog.Title>
        <Dialog.Panel className="absolute left-1/2 top-1/2 flex w-[35rem] -translate-x-1/2 -translate-y-1/2 flex-col rounded-2xl bg-white max-h-[90vh]">
          <div className="flex h-[64px] items-end justify-between rounded-t-2xl bg-primary px-10 py-[0.87rem] text-white">
            <p>Import CVs              {defaultGroup?.id}</p>
            <button
              className="flex h-8 w-[4.6rem] items-center justify-center rounded-md bg-[#F5F3FF33]"
              onClick={closeImportBulkCVModal}
            >
              close
            </button>
          </div>

          <div className="flex-1 overflow-y-auto p-5">
            <p className="py-1 text-xl text-black">How to use:</p>
            <ol className="list-decimal px-4 text-sm text-helper-text mb-4">
              <li>Upload a ZIP file containing CVs or a single PDF</li>
              <li>Select an existing group or create a new one</li>
              <li>Submit to process the CVs</li>
            </ol>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {/* File Upload Section */}
              <div>
                {showUploadProgress && watchedFile ? (
                  <div className="min-h-[5.1875rem] rounded-lg bg-[#F3F1FF] px-[1.31rem] py-[0.87rem]">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-[0.56rem]">
                        {isZipFile ? <DocIcon height={45} /> : <PdfIcon />}
                        <div>
                          <p className="text-[0.75rem] text-[#646464]">{watchedFile.name}</p>
                          <span className="text-[0.625rem] text-[#4E4E4E]">
                            {filesize(watchedFile.size, { base: 2, standard: "jedec" })}
                          </span>
                        </div>
                      </div>
                      <div className="cursor-pointer">
                        <CloseIcon onClick={closeFile} />
                      </div>
                    </div>
                    <div className="relative mt-1 h-[0.3125rem] rounded-[0.625rem] bg-white">
                      <div
                        className="absolute inset-0 rounded-[0.625rem] bg-[#755AE2] transition-all duration-300"
                        style={{ width: `${progress}%` }}
                      />
                    </div>
                  </div>
                ) : (
                  <div
                    className={`flex h-[5.9375rem] w-full cursor-pointer items-center gap-4 rounded-[0.875rem] border-[0.3px] border-dashed border-[rgb(117,90,226)] p-6 ${errors.cv_file ? "border-red-600" : ""
                      }`}
                    {...getRootProps()}
                  >
                    <UploadIcon />
                    <div>
                      <p className="font-sans text-xs font-semibold text-[#755AE2] sm:text-sm">
                        Tap to upload document
                      </p>
                      <span className="font-sans text-xs font-semibold text-[#755AE2] opacity-75">
                        File types: PDF, ZIP (max 10MB)
                      </span>
                    </div>
                    <input {...getInputProps()} />
                  </div>
                )}
                {errors.cv_file && <p className="text-xs text-red-600 mt-1">{errors.cv_file.message}</p>}
              </div>

              {/* Group Selection Section */}
              {watchedFile && (
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium mb-2">Select Group</p>
                    {selectedGroup ? (
                      <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div>
                          <p className="font-medium text-sm">{selectedGroup.name}</p>
                          <p className="text-xs text-gray-500">{selectedGroup.role}</p>
                        </div>
                        <button
                          type="button"
                          onClick={() => {
                            setSelectedGroup(null)
                            setValue("group_id", 0)
                            setShowGroupSelector(true)
                          }}
                          className="text-xs text-blue-600 hover:text-blue-800"
                        >
                          Change
                        </button>
                      </div>
                    ) : (
                      <button
                        type="button"
                        onClick={() => setShowGroupSelector(true)}
                        className="w-full p-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-500 hover:border-blue-400 hover:text-blue-600"
                      >
                        Click to select a group
                      </button>
                    )}
                    {errors.group_id && <p className="text-xs text-red-600 mt-1">{errors.group_id.message}</p>}
                  </div>

                  {/* Group Selector Modal */}
                  {showGroupSelector && (
                    <div className="border rounded-lg p-4 bg-gray-50">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-medium">Select Group</h3>
                        <button
                          type="button"
                          onClick={() => setShowGroupSelector(false)}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          ✕
                        </button>
                      </div>
                      <ImportBulkCVGroupSelector
                        disabled={!!defaultGroup}
                        selectedGroupId={watchedGroupId}
                        onGroupSelect={handleGroupSelect}
                        onCreateNew={handleCreateNewGroup}
                      />
                    </div>
                  )}

                  {/* Create New Group Form */}
                  {showCreateGroup && (
                    <div className="border rounded-lg p-4 bg-gray-50">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-medium">Create New Group</h3>
                        <button
                          type="button"
                          onClick={() => setShowCreateGroup(false)}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          ✕
                        </button>
                      </div>
                      <div className="space-y-3">
                        <div>
                          <label className="block text-sm font-medium mb-1">Group Name</label>
                          <input
                            type="text"
                            value={newGroupData.name}
                            onChange={(e) => setNewGroupData((prev) => ({ ...prev, name: e.target.value }))}
                            className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Enter group name"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1">Role</label>
                          <input
                            type="text"
                            value={newGroupData.role}
                            onChange={(e) => setNewGroupData((prev) => ({ ...prev, role: e.target.value }))}
                            className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Enter role"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1">Keywords (Optional)</label>
                          <div className="flex gap-2">
                            <input
                              type="text"
                              value={keyword}
                              onChange={(e) => setKeyword(e.target.value)}
                              onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addKeyword())}
                              className="flex-1 p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="Add keyword"
                            />
                            <button
                              type="button"
                              onClick={addKeyword}
                              className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                            >
                              Add
                            </button>
                          </div>
                          {newGroupData.keywords.length > 0 && (
                            <div className="flex flex-wrap gap-2 mt-2">
                              {newGroupData.keywords.map((kw, index) => (
                                <span
                                  key={index}
                                  className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs"
                                >
                                  {kw}
                                  <button
                                    type="button"
                                    onClick={() => removeKeyword(index)}
                                    className="text-blue-600 hover:text-blue-800"
                                  >
                                    ✕
                                  </button>
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                        <button
                          type="button"
                          onClick={handleCreateGroup}
                          disabled={createGroupLoading}
                          className="w-full py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                        >
                          {createGroupLoading ? "Creating..." : "Create Group"}
                        </button>
                      </div>
                    </div>
                  )}

                  {/* Submit Button */}
                  <button
                    type="submit"
                    disabled={uploadLoading || !watchedFile || !watchedGroupId}
                    className="w-full py-3 bg-primary text-white rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {uploadLoading ? "Uploading..." : "Upload CVs"}
                  </button>
                </div>
              )}
            </form>
          </div>
        </Dialog.Panel>
        {(uploadLoading || createGroupLoading) && <Loader />}
      </div>
    </Dialog>
  )
}

export default ImportBulkCVModal
