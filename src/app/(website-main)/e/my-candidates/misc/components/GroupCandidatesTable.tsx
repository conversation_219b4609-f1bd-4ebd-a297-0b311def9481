"use client"
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  // flexRender,
  // getCoreRowModel,
  getFilteredRowModel,
  useReactTable,
  // useReactTable,
} from "@tanstack/react-table"
import { usePathname } from "next/navigation"
import React, { type HTMLProps, type SetStateAction, useEffect, useState } from "react"
import ThreeDots from "../icons/ThreeDots"
import type { ICVData } from "../api/getCVGroupData"
import { ConfirmDeleteModalAlt, DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/shared"
import { useBooleanStateControl } from "@/hooks"
import GroupCandidateDetails from "./GroupCandidateDetails"
import { useDeleteCV } from "../api/deleteCV"
import toast from "react-hot-toast"
import { cn } from "@/utils"

const columnHelper = createColumnHelper<ICVData>()

function IndeterminateCheckbox({
  indeterminate,
  className = "",
  ...rest
}: { indeterminate?: boolean } & HTMLProps<HTMLInputElement>) {
  const ref = React.useRef<HTMLInputElement>(null!)

  React.useEffect(() => {
    if (typeof indeterminate === "boolean") {
      ref.current.indeterminate = !rest.checked && indeterminate
    }
  }, [ref, indeterminate])

  return <input type="checkbox" ref={ref} className={className + " cursor-pointer checkbox-purple"} {...rest} />
}



interface GroupTableProps {
  tableData: Array<ICVData>
  setRowSelectedArray: React.Dispatch<SetStateAction<number[]>>
  isLoading: boolean
  isFetching: boolean
  selectedCVs: number[]
  setSelectedCVs: React.Dispatch<SetStateAction<number[]>>
  onSelectCV: (cvId: number) => void
  onSelectAll: (cvIds: number[]) => void
  onClearSelection: () => void
  onPageChange: (page: number) => void
  currentPage: number
  totalPages?: number
  totalCount?: number
  pageSize?: number
  refetchData: () => void
}
const GroupCandidatesTable: React.FunctionComponent<GroupTableProps> = ({
  tableData,
  setRowSelectedArray,
  onPageChange,
  isLoading,
  isFetching,
  currentPage,
  totalPages = 1,
  totalCount = 0,
  pageSize = 10,
  refetchData
}) => {



  const pathname = usePathname()
  const [data, setData] = React.useState(() => [...tableData])
  const [rowSelection, setRowSelection] = React.useState({})
  const [currentCVID, setCurrentCVID] = useState<number | null>(null)

  const {
    state: isCVDetailsSheetOpen,
    setTrue: openCVDetailsSheet,
    setFalse: closeCVDetailsSheet,
  } = useBooleanStateControl()
  const {
    state: isConfirmDeleteCVModalOpen,
    setTrue: openConfirmDeleteCVModal,
    setFalse: closeConfirmDeleteCVModal,
  } = useBooleanStateControl()

  const { mutate: deleteCV, isLoading: isDeletingCV } = useDeleteCV()
  const handleDeleteCV = () => {
    if (!currentCVID) {
      toast.error("No CV selected for deletion")
      return
    }
    deleteCV(currentCVID, {
      onSuccess: () => {
        closeConfirmDeleteCVModal()
        refetchData()
        toast.success("CV deleted successfully")
      },
    })
  }

  const columns = [
    {
      id: "select",
      header: ({ table }: { table: any }) => (
        <IndeterminateCheckbox
          {...{
            checked: table.getIsAllRowsSelected(),
            indeterminate: table.getIsSomeRowsSelected(),
            onChange: table.getToggleAllRowsSelectedHandler(),
          }}
        />
      ),
      cell: ({ row }: { row: any }) => (
        <div className="">
          <IndeterminateCheckbox
            {...{
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              indeterminate: row.getIsSomeSelected(),
              onChange: row.getToggleSelectedHandler(),
            }}
          />
        </div>
      ),
    },
    columnHelper.accessor("id", {
      header: "S/N",
      cell: (info) => info.row.index + 1,
    }),

    columnHelper.accessor("old_cv_data_dict", {
      header: () => "Name",
      cell: ({ row }) => (

        <>
          {row.original.candidate_fullname}
        </>
      ),
    }),
    columnHelper.accessor("role", {
      header: () => "Role At Upload",
      cell: (info) => <p className="max-w-[120px] truncate text-sm font-normal text-body-text">{info.renderValue()}</p>,
    }),
    columnHelper.accessor("application_count", {
      header: () => "Application Count",
      cell: (info) => <p className="max-w-[120px] truncate text-sm font-normal text-body-text">{info.renderValue()}</p>,
    }),
    columnHelper.accessor("years_of_experience", {
      header: "Years Of Exp.",
      cell: (info) => <p className="max-w-[120px] truncate text-sm font-normal text-body-text">{info.renderValue()}</p>,
    }),
    columnHelper.accessor("industry", {
      header: () => "Industry",
      cell: (info) => <p className="max-w-[200px] truncate text-sm font-normal text-body-text">{info.renderValue()}</p>,
    }),
    columnHelper.accessor("keyword_matching_percentage", {
      header: "Keyword%",
      cell: (info) => (
        <p className="my-2 flex items-center justify-center rounded-lg bg-primary-light p-1.5 text-primary transition-all hover:bg-primary text-sm hover:text-white">
          {`${info.renderValue()}%`}
        </p>
      ),
    }),
    columnHelper.accessor("skills", {
      header: "Skills",
      cell: ({ row }) => (
        <div className="my-3 flex items-center justify-center gap-x-4 rounded-lg bg-primary-light p-2 text-primary transition-all hover:bg-primary hover:text-white">
          <p className="max-w-[120px] truncate text-sm">{row.original.skills && row.original.skills[0]}</p>
        </div>
      ),
    }),
    columnHelper.accessor("location", {
      header: "Location",
      cell: (info) => <p className="max-w-[200px] truncate text-sm font-normal text-body-text">{info.renderValue()}</p>,
    }),
    columnHelper.accessor("old_cv_data_dict", {
      header: "Degree",
      cell: ({ row }) => (
        <p className="max-w-[150px] truncate text-sm font-normal text-body-text">
          {/* {row.original.old_cv_data_dict?.Education[0]?.degree} */}
        </p>
      ),
    }),
    columnHelper.accessor("language", {
      header: "Language",
      cell: ({ row }) => (
        <p className="max-w-[100px] truncate text-sm font-normal text-body-text">{row.original.language}</p>
      ),
    }),
    columnHelper.accessor("id", {
      header: "Action",
      cell: ({ row }) => (
        <>
          <DropdownMenu>
            <DropdownMenuTrigger className="cursor-pointer flex items-center justify-center gap-5 bg-[#F8F9FB] rounded-lg my-3 py-2 px-4 outline-1 outline-[#EAE6FB] max-w-max">
              <p className="text-[13px]">Action</p>
              <span className="opacity-60">
                <ThreeDots />
              </span>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={() => {
                  openCVDetailsSheet()
                  setCurrentCVID(row.original.id)
                }}
              >
                <p className="text-sm">View Details</p>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={() => {
                  setCurrentCVID(row.original.id)
                  openConfirmDeleteCVModal()
                }}>
                <p className="text-sm">Delete</p>
              </DropdownMenuItem>

            </DropdownMenuContent>
          </DropdownMenu>


        </>
      ),
    }),
  ]
  useEffect(() => {
    setRowSelectedArray(
      Object.keys(rowSelection).map((item) => {
        return tableData[Number.parseInt(item)].id
      }),
    )
  }, [rowSelection])

  // SET TABLE DATE HERE
  //TODO ADD A LOADER
  useEffect(() => {
    setData([...tableData])
  }, [tableData, pathname])

  const rerender = React.useReducer(() => ({}), {})[1]
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onStateChange() {
      rerender()
    },
    state: {
      rowSelection,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    getFilteredRowModel: getFilteredRowModel(),
    debugTable: true,
  })

  useEffect(() => {
    rerender()
  }, [])

  const startItem = (currentPage - 1) * pageSize + 1
  const endItem = Math.min(currentPage * pageSize, totalCount)

  return (
    <>
      <div
        className={cn(
          'overflow-hidden rounded-full opacity-0 transition-opacity',
          isFetching && !isLoading && 'opacity-100'
        )}
      >
        <div className="bg-primary/20 h-1 w-full overflow-hidden">
          <div className="h-full w-full origin-[0_50%] animate-indeterminate-progress rounded-full bg-primary "></div>
        </div>
      </div>
      <div className="w-full overflow-auto">
        <table className="min-w-max w-full caption-bottom text-sm ">
          <thead className="">
            {table.getHeaderGroups().map((headerGroup, index) => (
              <tr key={index} className="sticky top-0 bg-white shadow-sm">
                {headerGroup.headers.map((header) => (
                  <th
                    className="whitespace-nowrap px-4 py-2 pb-4 text-left font-medium  border-b text-[15px] text-header-text"
                    key={header.index}
                  >
                    {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody className="">
            {table.getRowModel().rows.map((row) => (
              <tr
                className="w-full cursor-pointer border-b transition-all hover:bg-[#F3F1FF]"
                key={`${row.index}-${row.id}-${Math.random()}`}
              >
                {row.getVisibleCells().map((cell) => (
                  <td className="whitespace-nowrap px-4" key={`${cell.id}-${row.index}-${Math.random()}`}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
          <tfoot>
            {table.getFooterGroups().map((footerGroup) => (
              <tr key={footerGroup.id}>
                {footerGroup.headers.map((header) => (
                  <th key={header.index}>
                    {header.isPlaceholder ? null : flexRender(header.column.columnDef.footer, header.getContext())}
                  </th>
                ))}
              </tr>
            ))}
          </tfoot>
        </table>
      </div>

      <div className="flex items-center justify-between px-4 py-4 border-t bg-white">
        <div className="flex items-center gap-2 text-sm text-body-text">
          <span>
            Showing {startItem} to {endItem} of {totalCount} results
          </span>
        </div>

        <div className="flex items-center gap-2">
          <button
            className="px-3 py-1 text-sm border rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage <= 1}
          >
            Previous
          </button>

          <div className="flex items-center gap-1">
            {Array.from({ length: totalPages }, (_, i) => (
              <button
                key={i}
                className={`px-3 py-1 text-sm border rounded-md hover:bg-gray-50 ${currentPage === i + 1 ? "bg-primary text-white border-primary" : ""
                  }`}
                onClick={() => onPageChange(i + 1)}
              >
                {i + 1}
              </button>
            ))}
          </div>

          <button
            className="px-3 py-1 text-sm border rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage >= totalPages}
          >
            Next
          </button>
        </div>

        <div className="flex items-center gap-2 text-sm">
          <span>Rows per page:</span>
          <select
            className="border rounded-md px-2 py-1 text-sm"
            value={pageSize}
            onChange={(e) => {
              // This would typically trigger a refetch with new page size
              console.log("Page size changed to:", e.target.value)
            }}
          >
            {[10, 20, 30, 40, 50].map((size) => (
              <option key={size} value={size}>
                {size}
              </option>
            ))}
          </select>
        </div>
      </div>
      <ConfirmDeleteModalAlt
        closeModal={closeConfirmDeleteCVModal}
        isModalOpen={isConfirmDeleteCVModalOpen}
        title="Delete CV"
        isDeleting={isDeletingCV}
        deleteFunction={handleDeleteCV}
      >
        <div>
          <p>
            Are you sure you want to delete this CV?
          </p>
        </div>
      </ConfirmDeleteModalAlt>

      <GroupCandidateDetails
        cv_id={currentCVID!}
        allCVsId={tableData.map(cv => cv.id)}
        isDrawerOpen={isCVDetailsSheetOpen}
        refetchGroupData={refetchData}
        closeDrawer={closeCVDetailsSheet}
        openDrawer={openCVDetailsSheet}
      />
    </>
  )
}

export default GroupCandidatesTable
