import React, { useState, useMemo } from 'react';
import { Dialog } from '@headlessui/react';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';
import ReactLoading from 'react-loading';
import { useGetJobPipelines } from '../api/getJobPipelines';
import { useMoveCVToPipeline } from '../api/postCVtoPipeline';

interface MoveToPipelineModalProps {
  isOpen: boolean;
  closeMoveToPipelineModal: () => void;
  rowSelected: number[];
}

const MoveToPipelineModal: React.FunctionComponent<MoveToPipelineModalProps> = ({
  isOpen,
  closeMoveToPipelineModal,
  rowSelected,
}) => {
  const { data: pipelinesData } = useGetJobPipelines();
  const { mutate: moveToPipeline, isLoading: moveLoading } = useMoveCVToPipeline();

  const [search, setSearch] = useState('');

  const handleMoveToGroup = (id: number) => {
    moveToPipeline(
      {
        cvparser_ids: rowSelected,
        job_id: id,
      },
      {
        onSuccess: data => {
          toast.success('Candidates successfully moved to filtered group');
          closeMoveToPipelineModal();
        },
      }
    );
  };

  // Filter pipelines by search
  const filteredPipelines = useMemo(() => {
    if (!pipelinesData?.data) return [];
    if (!search.trim()) return pipelinesData.data;
    return pipelinesData.data.filter((item: any) =>
      item.job_title?.toLowerCase().includes(search.toLowerCase())
    );
  }, [pipelinesData, search]);

  return (
    <Dialog open={isOpen} onClose={closeMoveToPipelineModal}>
      <div className="fixed inset-0 z-30 bg-[#000000aa]">
        <Dialog.Title></Dialog.Title>
        <Dialog.Panel
          className={
            'absolute left-1/2 top-1/2 flex h-[32rem] w-[30rem] -translate-x-1/2 -translate-y-1/2 flex-col rounded-2xl bg-white'
          }
        >
          <div className="flex h-[64px] items-end justify-between rounded-t-2xl bg-primary px-10 py-[0.87rem] text-white">
            <p>Move To Group</p>
            <button
              className="flex h-8 w-[4.6rem] items-center justify-center rounded-md bg-[#F5F3FF33]"
              onClick={closeMoveToPipelineModal}
            >
              close
            </button>
          </div>
          {moveLoading ? (
            <div className="flex h-full w-full items-center justify-center p-10">
              <ReactLoading
                className="z-20"
                type="spin"
                color="#3C1356"
                width={70}
                height={70}
              />
            </div>
          ) : (
            <div className="p-5 text-sm">
              <p className="mb-2">Select the job you want to move these CVs to</p>
              <input
                type="text"
                placeholder="Search jobs..."
                value={search}
                onChange={e => setSearch(e.target.value)}
                className="mb-3 w-full rounded border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
              />
              <div className="h-[20.5rem] overflow-y-scroll py-3">
                <>
                  {(filteredPipelines?.length ?? 0) > 0 ? (
                    <div className="grid w-full grid-cols-1 gap-y-2">
                      {filteredPipelines.map(
                        (item: any, index: number) => {
                          return (
                            <div
                              key={index}
                              className="w-full cursor-pointer rounded bg-primary-light p-3 transition-all hover:bg-primary hover:text-white"
                              onClick={() =>
                                handleMoveToGroup(item.id)
                              }
                            >
                              <p>{item.job_title}</p>
                            </div>
                          );
                        }
                      )}
                    </div>
                  ) : (
                    <div>No job pipelines found</div>
                  )}
                </>
              </div>
            </div>
          )}
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default MoveToPipelineModal;