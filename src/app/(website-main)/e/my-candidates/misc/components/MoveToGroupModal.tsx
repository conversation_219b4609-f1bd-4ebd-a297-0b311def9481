import { Dialog } from '@headlessui/react';
import { useRouter } from 'next/navigation';
import React, { SetStateAction, useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import ReactLoading from 'react-loading';
import { useUser } from '@/lib/contexts/UserContext';
import { useMoveToGroup } from '../api/moveToGroup';
import { CVGroup } from '../api/getCVGroups';


interface MoveToGroupModalProps {
  isOpen: boolean;
  setIsOpen: React.Dispatch<SetStateAction<boolean>>;
  filteredGroupsData: CVGroup[] | undefined;
  rowSelected: number[];
  openCreateGroupModal?: () => void
}
const MoveToGroupModal: React.FunctionComponent<MoveToGroupModalProps> = ({
  isOpen,
  setIsOpen,
  filteredGroupsData,
  rowSelected,
  openCreateGroupModal
}) => {
  const router = useRouter();
  const { user } = useUser();
  const closeImportModal = () => {
    setIsOpen(false);
  };

  const { mutate: moveToGroup, isLoading: moveLoading } = useMoveToGroup();
  const handleMoveToGroup = (id: number, name: string) => {
    moveToGroup(
      {
        cv_parser_ids: rowSelected,
        group_id: id.toString(),
      },
      {
        onSuccess: data => {
          setIsOpen(false);
          router.push(
            `/e/my-candidates/cv-group?group_id=${data.group_id as string
            }&group_name=${name}`
          );
          toast.success('Candidates successfully moved to filtered group');
        },
      }
    );
  };
  return (
    <Dialog open={isOpen} onClose={() => closeImportModal}>
      <div className="fixed inset-0 z-30 bg-[#000000aa]">
        <Dialog.Title></Dialog.Title>
        <Dialog.Panel
          className={
            'absolute left-1/2 top-1/2 flex h-[30rem] w-[30rem] -translate-x-1/2 -translate-y-1/2 flex-col rounded-2xl bg-white'
          }
        >
          <div className="flex h-[64px] items-end justify-between rounded-t-2xl bg-primary px-10 py-[0.87rem] text-white">
            <p>Move To Group</p>
            <button
              className="flex h-8 w-[4.6rem] items-center justify-center rounded-md bg-[#F5F3FF33]"
              onClick={closeImportModal}
            >
              close
            </button>
          </div>
          {moveLoading ? (
            <div className="flex h-full w-full items-center justify-center p-10">
              <ReactLoading
                className="z-20"
                type="spin"
                color="#3C1356"
                width={70}
                height={70}
              />
            </div>
          ) : (
            <div className="p-5 text-sm">
              <p>Select group you want to move to or create new group</p>
              <div className=" h-[250px]  overflow-y-scroll p-3">
                <>
                  {(filteredGroupsData?.length ?? 0) > 0 ? (
                    <div className="grid w-full grid-cols-1 gap-y-2">
                      {filteredGroupsData?.map(
                        (item: any, index: number) => {
                          return (
                            <div
                              key={index}
                              className="w-full cursor-pointer rounded bg-primary-light p-3 transition-all hover:bg-primary hover:text-white"
                              onClick={() =>
                                handleMoveToGroup(item.id, item.name)
                              }
                            >
                              <p>{item.name}</p>
                            </div>
                          );
                        }
                      )}
                    </div>
                  ) : (
                    <div>You have no filtered groups to move to</div>
                  )}
                </>
              </div>
              <p className="py-3 text-center">Or</p>
              <button
                className="w-full rounded-md bg-primary p-3 text-white"
                onClick={() => {
                  setIsOpen(false);
                  openCreateGroupModal?.();
                }}
              >
                Create new Group
              </button>
            </div>
          )}
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default MoveToGroupModal;