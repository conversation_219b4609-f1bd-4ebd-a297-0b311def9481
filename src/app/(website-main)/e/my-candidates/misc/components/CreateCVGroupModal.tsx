"use client"

import { Dialog } from "@headlessui/react"
import <PERSON><PERSON> from '@/components/Modal'

import type React from "react"
import { type SetStateAction, useState } from "react"
import { useForm, Controller } from "react-hook-form"
import toast from "react-hot-toast"
import { useUser } from "@/lib/contexts/UserContext"
import RemoveKeywordIcon from "../icons/RemoveKeywordIcon"
import { z } from "zod"
import { CreateCVGroupResponseData, useCreateCVGroup } from "../api/postCreateGroup"
import { Loader } from "@/components/shared"

interface CreateCVGroupModalProps {
  isOpen: boolean
  closeModal: () => void
  onSuccess?: (data: CreateCVGroupResponseData) => void
  filter_group?: boolean
}

const formSchema = z.object({
  name: z.string().min(1, "Group name is required"),
  role: z.string().min(1, "Role is required"),
  keywords: z.array(z.string()).optional(),
})

type FormData = z.infer<typeof formSchema>

const CreateCVGroupModal: React.FunctionComponent<CreateCVGroupModalProps> = ({ isOpen, closeModal, onSuccess, filter_group }) => {
  // React Hook Form setup
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues: {
      name: "",
      role: "",
      keywords: [],
    },
  })

  const watchedKeywords = watch("keywords")

  const closeCreateCVGroupModal = () => {
    reset()
    setKeyword("")
    closeModal()
  }

  const [keyword, setKeyword] = useState<string>("")

  // Use the new createCVGroup hook
  const { mutate: createCVGroup, isLoading: createCVGroupLoading } = useCreateCVGroup()

  const addKeyword = () => {
    if (keyword.trim()) {
      const currentKeywords = watchedKeywords || []
      setValue("keywords", [...currentKeywords, keyword.trim()])
      setKeyword("")
    }
  }

  const removeKeyword = (indexToRemove: number) => {
    const currentKeywords = watchedKeywords || []
    setValue(
      "keywords",
      currentKeywords.filter((_, index) => index !== indexToRemove),
    )
  }

  const onSubmit = (data: FormData) => {

    createCVGroup(
      {
        name: data.name,
        role: data.role,
        keywords: data.keywords,
        filter_group
      },
      {
        onSuccess: (data) => {
          onSuccess?.(data?.data)
          toast.success("CV Group created successfully!")
          closeCreateCVGroupModal()
        },
        onError: (error: any) => {
          toast.error(error?.response?.data?.error || "Failed to create CV group")
        },
      },
    )
  }

  const handleKeywordKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault()
      addKeyword()
    }
  }

  return (
    <Modal is_open={isOpen} close={closeCreateCVGroupModal} title="CV Group">
      <div className="h-full overflow-y-scroll p-5">
        <div className="mt-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Create CV Group</h3>
        </div>
        <div className="">
          <div className="py-5">
            <form className="flex w-full flex-col gap-y-2 text-sm" onSubmit={handleSubmit(onSubmit)}>
              <div>
                <p>Name Upload Group</p>
                <Controller
                  name="name"
                  control={control}
                  rules={{ required: "Group name is required" }}
                  render={({ field }) => (
                    <input
                      {...field}
                      className="w-full rounded bg-[#F5F7F9] p-3 outline-none"
                      type="text"
                      placeholder="Enter group name"
                    />
                  )}
                />
                {errors.name && <p className="text-xs text-red-600">{errors.name.message}</p>}
              </div>
              <div>
                <p>Role</p>
                <Controller
                  name="role"
                  control={control}
                  rules={{ required: "Role is required" }}
                  render={({ field }) => (
                    <input
                      {...field}
                      className="w-full rounded bg-[#F5F7F9] p-3 outline-none"
                      type="text"
                      placeholder="Enter Role"
                    />
                  )}
                />
                {errors.role && <p className="text-xs text-red-600">{errors.role.message}</p>}
              </div>
              <div>
                <p className="pt-3">Keywords(optional)</p>
                <div className="flex h-max w-full justify-between rounded-md bg-[#F5F7F9] p-1">
                  <input
                    className="w-full rounded bg-[#F5F7F9] p-3 capitalize outline-none"
                    type="text"
                    placeholder="Type keywords tap add"
                    value={keyword}
                    onChange={(e) => setKeyword(e.target.value)}
                    onKeyPress={handleKeywordKeyPress}
                  />
                  <button
                    type="button"
                    className="min-w-[80px] rounded-md bg-white px-3 text-primary"
                    onClick={addKeyword}
                  >
                    Add
                  </button>
                </div>

                <div className="flex w-full flex-wrap gap-4 py-2">
                  {watchedKeywords?.map((keywordItem: string, index: number) => (
                    <div key={index} className="flex gap-4 rounded-full bg-[#F5F3FF] p-2 pl-4 capitalize">
                      <p className="text-primary">{keywordItem}</p>
                      <button type="button" className="outline-none" onClick={() => removeKeyword(index)}>
                        <RemoveKeywordIcon />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
              <input
                className="my-3 w-full rounded-md bg-primary p-3 text-white disabled:opacity-50"
                type="submit"
                value="Proceed"
                disabled={createCVGroupLoading}
              />
            </form>
          </div>
        </div>
      </div>
      {createCVGroupLoading && <Loader />}
    </Modal>
  )
}

export default CreateCVGroupModal
