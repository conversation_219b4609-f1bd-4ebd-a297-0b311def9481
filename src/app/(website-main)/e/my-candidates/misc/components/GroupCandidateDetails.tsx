import React from 'react'
import { useWindowWidth } from '@/hooks';

import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    LoaderBtn,
    Skeleton,
    ToolTip,
} from '@/components/shared';
import {
    <PERSON>For<PERSON>,
    Eye,
    Info,
    SmallSpinner,
    <PERSON>ner,
    StrokeCircleCheck,
} from '@/components/shared/icons';
import { getInitials } from '../../../_talent-pool/misc/util';

import type { ICVData } from "../api/getCVGroupData"
import CandidateDetailsDocumentsTab from '../../../jobs/misc/components/job-detail/5.3 JobPipelineCandidateDetailsDocumentsTab';
import { Axios } from '@/lib/api/axios';
import { useQuery } from '@tanstack/react-query';
import { useGetCVDetails } from '../api/getCVDetails';
import ViewPDF from '../../../jobs/misc/components/job-detail/ViewPDF';
import ViewDOCX from '../../../jobs/misc/components/job-detail/ViewDOCX';


interface props {
    cv_id: number;
    allCVsId: number[];
    isDrawerOpen: boolean;
    refetchGroupData: () => void;
    closeDrawer: () => void;
    openDrawer: () => void;
}
const GroupCandidateDetails: React.FC<props> = ({
    cv_id,
    allCVsId,
    isDrawerOpen,
    refetchGroupData,
    closeDrawer,
    openDrawer,
}) => {
    const windowWidth = useWindowWidth();
    const [currentCVId, setCurrentCVId] = React.useState<number>(cv_id);
    const currentCVIndex = allCVsId.findIndex(
        candidate => candidate === currentCVId
    );
    const nextCV =
        currentCVIndex + 1 < allCVsId.length
            ? allCVsId[currentCVIndex + 1]
            : allCVsId[0];
    const previousCV =
        currentCVIndex > 0
            ? allCVsId[currentCVIndex - 1]
            : allCVsId[allCVsId.length - 1];

    React.useEffect(() => {
        setCurrentCVId(cv_id);
    }, [cv_id]);

    const { data: CVDetails, isLoading: isLoadingCVDetails, isFetching: isFetchingCVDetails } = useGetCVDetails(currentCVId, isDrawerOpen)
    console.log(CVDetails, "CVDetails");

    return (
        <Drawer
            open={isDrawerOpen}
            dismissible
            direction={windowWidth < 720 ? 'bottom' : 'right'}
        >
            {/* <DrawerTrigger asChild className="max-w-max">
                <Button
                    variant="extralight"
                    size="tiny"
                    className="max-w-max text-[0.7rem]"
                    onClick={() => openDrawer()}
                >
                    View Details
                </Button>
            </DrawerTrigger> */}

            <DrawerContent className="!m-0 h-[90vh] w-full overflow-hidden rounded-l-2xl border-none bg-white !p-0 md:left-auto md:right-0 md:h-screen md:w-[60%] md:max-w-[750px]">
                <div className="relative flex max-h-full grow flex-col overflow-y-hidden">
                    <div className="flex w-full items-center justify-center bg-primary md:hidden">
                        <div className="mx-auto mb-1 mt-3 h-1 w-[100px] rounded-full bg-white/60" />
                    </div>

                    <DrawerHeader className="sticky top-0 flex w-full items-center justify-between bg-primary px-5 text-white md:px-8 max-md:pt-1">
                        <h3>Candidate Details</h3>
                        <DrawerClose className="rounded-lg bg-white/30 px-6 py-2 text-sm"
                            onClick={closeDrawer}
                        >
                            Close
                        </DrawerClose>
                    </DrawerHeader>

                    <section className="flex grow flex-col overflow-y-scroll">
                        <header className="flex items-start gap-4 p-6 pb-4">
                            {isLoadingCVDetails ? (
                                <Skeleton className="h-14 w-14 shrink-0 rounded-full" />
                            ) : (
                                <div className="flex h-14 w-14 shrink-0 items-center justify-center rounded-full bg-primary text-xl font-bold text-white">
                                    {getInitials(CVDetails?.candidate_fullname || "")}
                                </div>
                            )}
                            <div>
                                {(isLoadingCVDetails || isFetchingCVDetails) ? (
                                    <>
                                        <Skeleton className="h-4 w-28 shrink-0 rounded-md md:w-60" />
                                        <div className="mt-2.5 flex items-center gap-2">
                                            <Skeleton className="h-2.5 w-16 shrink-0 rounded-md md:w-40" />
                                            <Skeleton className="h-8 w-6 shrink-0 rounded-md md:w-32" />
                                            <Skeleton className="h-8 w-6 shrink-0 rounded-md md:w-32" />
                                        </div>
                                    </>
                                ) : (
                                    <>
                                        <h4 className="text-lg font-medium text-primary md:text-xl">
                                            {CVDetails?.candidate_fullname}
                                        </h4>
                                    </>
                                )}
                            </div>
                        </header>
                        <div className="grow mt-2 max-w-full overflow-y-scroll">
                            {
                                (isLoadingCVDetails || isFetchingCVDetails) ?
                                    <div className="flex items-center justify-center w-full h-full">
                                        <SmallSpinner color="#755AE2" />
                                    </div>
                                    :
                                    !isLoadingCVDetails && !isFetchingCVDetails && CVDetails && CVDetails?.cv_file?.toLowerCase().includes(".pdf") ?
                                        <ViewPDF cvFile={CVDetails?.cv_file_url} />
                                        :
                                        !isLoadingCVDetails && !isFetchingCVDetails && CVDetails?.cv_file && CVDetails?.cv_file?.toLowerCase().includes(".docx") ?
                                            <ViewDOCX cvFile={CVDetails?.cv_file_url} />
                                            :
                                            <p>NO CV FILE</p>
                            }
                        </div>
                    </section>

                    <DrawerFooter className="sticky bottom-0 flex w-full flex-row flex-wrap items-center justify-between rounded-t-xl bg-primary-light text-white md:px-8 ">
                        <Button
                            size="tiny"
                            variant="outlined"
                            onClick={() => setCurrentCVId(previousCV)}
                            icon={<DoubleForward className="rotate-180" />}
                        >
                            Prev
                        </Button>
                        <Button
                            size="tiny"
                            variant="outlined"
                            onClick={() => setCurrentCVId(nextCV)}
                            icon={<DoubleForward className="" />}
                            iconPosition='after'
                        >
                            Next
                        </Button>




                    </DrawerFooter>
                </div>
            </DrawerContent>
        </Drawer>
    );
}

export default GroupCandidateDetails