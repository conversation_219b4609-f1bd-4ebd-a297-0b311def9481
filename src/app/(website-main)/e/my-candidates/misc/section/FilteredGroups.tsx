"use client"

import { useEffect, useState } from "react"
import ImportIcon from "../icons/ImportIcon"
import { <PERSON><PERSON>, Loader, <PERSON><PERSON>, <PERSON>overTrigger, PopoverContent } from "@/components/shared"
import { useUser } from "@/lib/contexts/UserContext"
import GroupTable from "../components/GroupTable"
import CandidateGroup from "../icons/CandidateGroup"
import { Search } from "@/components/shared/icons"
import ArrowsIconLeft from "../icons/Arrows"
import { useGetFilteredCVGroups } from "../api/getFilteredCVGroups"
import { CVGroup } from "../api/getCVGroups"

const FilteredGroups = () => {
  const { user } = useUser()
  const [currentPage, setCurrentPage] = useState(1)
  const [searchQuery, setSearchQuery] = useState("")

  const { data: CVGroupsData, isLoading: dataLoading, refetch } = useGetFilteredCVGroups(currentPage)

  const [dataArrayForTable, setDataArrayForTable] = useState<CVGroup[] | null>(null)

  const goToNextPage = () => {
    if (CVGroupsData?.data) {
      setCurrentPage(currentPage + 1)
    }
  }

  const goToPreviousPage = () => {
    if (CVGroupsData?.data) {
      setCurrentPage(currentPage - 1)
    }
  }

  // Update table data when CVGroupsData changes
  useEffect(() => {
    if (CVGroupsData?.data) {
      setDataArrayForTable(CVGroupsData?.data)
    }
  }, [CVGroupsData])

  // Handle search filtering
  useEffect(() => {
    if (CVGroupsData?.data) {
      if (searchQuery !== "") {
        setDataArrayForTable(
          CVGroupsData?.data?.filter((item: any) => item?.name?.toLowerCase().includes(searchQuery.toLowerCase())),
        )
      } else {
        setDataArrayForTable(CVGroupsData?.data)
      }
    }
  }, [searchQuery, CVGroupsData])

  return (
    <div className="flex h-full w-full flex-col bg-[#F5F7F9] pb-2">
      {!dataLoading ? (
        <main className="flex h-full flex-col px-4">
          <section className="my-2 flex items-center justify-between rounded-md bg-white py-3 px-6">
            <p className="text-header-text font-medium">Filtered Groups</p>
            <div className="flex gap-x-4">
              <div className="relative">
                <Search className="absolute right-[5%] top-[25%]" />
                <input
                  type="search"
                  placeholder="Search"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="px-3.5 py-2 sm:px-4 border-[1.75px] border-[#D6D6D6] rounded-lg text-[0.9rem] focus:border-primary focus:outline-none transition-all"
                />
              </div>

              <Popover>
                <PopoverTrigger>
                  <span className="px-4 py-2 border-[#D6D6D6] border-[1.75px] rounded-[0.35rem] text-[#556575] text-[0.875rem]">
                    Filter
                  </span>
                </PopoverTrigger>

                <PopoverContent>Nothing here</PopoverContent>
              </Popover>
            </div>
          </section>

          <section className="relative h-full overflow-y-scroll rounded-md bg-white">
            {CVGroupsData?.data.length && CVGroupsData.data.length > 0 ? (
              <div className="absolute inset-5 flex flex-col overflow-y-scroll">
                <div className="h-full overflow-y-scroll">
                  {dataArrayForTable && dataArrayForTable.length > 0 ? (
                    <GroupTable tableData={dataArrayForTable} currentPage={currentPage} showRole={true} />
                  ) : (
                    <div className="w-full flex justify-center min-h-full align-center">
                      <article className="flex flex-col items-center gap-6 p-8 sm:p-12 w-[90%] max-w-[380px] my-[7vh] xs:max-md:my-[10vh] md:my-[12vh] bg-gradient-to-tr from-primary-light via-primary-[#D9D9D9] to-transparent rounded-xl">
                        <div>
                          <CandidateGroup />
                        </div>
                        <div className="text-center">
                          <h3 className="font-medium">No parsed CV match your search parameters</h3>
                          <p className="text-sm font-normal text-helper-text">kindly clear your search query</p>
                        </div>
                        <Button onClick={() => setSearchQuery("")} className="text-sm" size="small">
                          Clear Search Query
                        </Button>
                      </article>
                    </div>
                  )}
                </div>
                <div className="flex gap-x-3 bg-white px-5 pt-3 pb-1">
                 
                 
                </div>
              </div>
            ) : (
              <div className="w-full flex justify-center min-h-full align-center">
                <article className="flex flex-col items-center gap-6 p-8 sm:p-12 w-[90%] max-w-[380px] my-[7vh] xs:max-md:my-[10vh] md:my-[12vh] bg-gradient-to-tr from-primary-light via-primary-[#D9D9D9] to-transparent rounded-xl">
                  <div>
                    <CandidateGroup />
                  </div>
                  <div className="text-center">
                    <h3 className="font-medium">You currently do not have any candidate group</h3>
                    <p className="text-sm font-normal text-helper-text">Create your first CV group to get started</p>
                  </div>
                  <Button
                    // onClick={() => setIsImportModalOpen(true)}
                    icon={<ImportIcon />}
                    className="text-sm"
                    size="small"
                  >
                    Import Bulk CVs
                  </Button>
                </article>
              </div>
            )}
          </section>
        </main>
      ) : (
        <Loader />
      )}
    </div>
  )
}

export default FilteredGroups
