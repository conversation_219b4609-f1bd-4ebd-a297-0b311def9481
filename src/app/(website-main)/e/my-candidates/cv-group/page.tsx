"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON>, useSearch<PERSON>ara<PERSON> } from "next/navigation"
import { ArrowLeft, Download, Move, Search, X } from "lucide-react"
import { useUser } from "@/lib/contexts/UserContext"

import MoveToGroupModal from "../misc/components/MoveToGroupModal"
import GroupCandidatesTable from "../misc/components/GroupCandidatesTable"
import toast from "react-hot-toast"
import { Badge, Button, Input } from "@/components/shared"
import CVGroupFilterPanel from "../misc/components/CVGroupFilterPanel"
import { useGetGroupCVs } from "../misc/api/getCVGroupData"
import { useGetFilterValues } from "../misc/api/getFilterValues"
import { useExportCvFiles } from "../misc/api/exportCvFiles"
import { useBooleanStateControl } from "@/hooks"
import CreateCVGroupModal from "../misc/components/CreateCVGroupModal"
import ImportBulkCVModal from "../misc/components/ImportBulkCVModal"
import { useGetFilteredCVGroups } from "../misc/api/getFilteredCVGroups"
import MoveToPipelineModal from "../misc/components/MoveToPipelineModal"

interface FilterState {
  roles: string[]
  industries: string[]
  locations: string[]
  languages: string[]
  yearsOfExperience: string
  minKeywordPercentage: number
  maxKeywordPercentage: number
}

const initialFilterState: FilterState = {
  roles: [],
  industries: [],
  locations: [],
  languages: [],
  yearsOfExperience: "",
  minKeywordPercentage: 0,
  maxKeywordPercentage: 100,
}

export default function GroupDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const searchParams = useSearchParams()
  const { user } = useUser()

  const groupId = searchParams.get("group_id") || "1"
  const groupName = searchParams.get("group_name") || "CV Group"
  const defaultRole = searchParams.get("default_role")
  const defaultMinKeyword = searchParams.get("default_min_keyword")

  // State management
  const [selectedCVs, setSelectedCVs] = useState<number[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [filters, setFilters] = useState<FilterState>(() => ({
    ...initialFilterState,
    roles: defaultRole ? [defaultRole] : [],
    minKeywordPercentage: defaultMinKeyword ? Number.parseInt(defaultMinKeyword) : 0,
  }))
  const [currentPage, setCurrentPage] = useState(1)

  // Modal states
  const [showMoveModal, setShowMoveModal] = useState(false)
  const { state: isImportModalOpen, setTrue: openImportModal, setFalse: closeImportModal } = useBooleanStateControl()
  const {
    state: isCreateGroupModalOpen,
    setTrue: openCreateGroupModal,
    setFalse: closeCreateGroupModal,
  } = useBooleanStateControl()
  const {
    state: isMoveToPipelineModalOpen,
    setTrue: openMoveToPipelineModal,
    setFalse: closeMoveToPipelineModal,
    setState: setMoveToPipelineModalState,
  } = useBooleanStateControl()

  // API hooks
  const {
    data: groupCVs,
    isLoading: cvLoading,
    isFetching,
    refetch: refetchGroupCVs,
  } = useGetGroupCVs(groupId, {
    page: currentPage,
    search: searchQuery,
    filters,
  })

  const { data: filterOptions, isLoading: filterLoading } = useGetFilterValues(groupId)
  const { data: CVGroups, refetch: refetchCVGroupsList } = useGetFilteredCVGroups(1)

  // Handlers
  const handleFilterChange = (newFilters: Partial<FilterState>) => {
    setFilters((prev) => ({ ...prev, ...newFilters }))
    setCurrentPage(1)
  }

  const handleClearFilters = () => {
    setFilters(initialFilterState)
    setCurrentPage(1)
  }

  const handleSelectCV = (cvId: number) => {
    setSelectedCVs((prev) => (prev.includes(cvId) ? prev.filter((id) => id !== cvId) : [...prev, cvId]))
  }

  const handleSelectAll = (cvIds: number[]) => {
    setSelectedCVs(cvIds)
  }

  const handleClearSelection = () => {
    setSelectedCVs([])
  }

  const { mutate: exportCvs, isLoading: exportLoading } = useExportCvFiles()
  const exportCvFiles = () => {
    exportCvs(
      { cv_ids: selectedCVs, group_id: groupId },
      {
        onSuccess: (data) => {
          const link = document.createElement("a")
          link.href = data.data.download_link
          link.setAttribute("download", "cv_download.zip")
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        },
      },
    )
  }
  const handleMoveToGroup = () => {
    if (selectedCVs.length === 0) {
      toast.error("Please select CVs to move")
      return
    }
    setShowMoveModal(true)
  }

  const activeFiltersCount = Object.values(filters).filter((value) => {
    if (Array.isArray(value)) return value.length > 0
    if (typeof value === "string") return value !== ""
    if (typeof value === "number") return value !== initialFilterState.minKeywordPercentage
    return false
  }).length

  const CVGroup = CVGroups?.data?.find((group) => group.id.toString() === groupId?.toString())
  const isLoading = cvLoading || filterLoading


  return (
    <div className="max-w-full overflow-hidden ">
      <div className="2">
        <div className="flex items-center justify-between mb-4">
          <div className="flex flex-col items-start xl:flex-row xl:items-center gap-4">
            <Button variant="outlined" size="small" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>

            <div>
              <h1 className="text-2xl font-bold text-gray-900">{groupName}</h1>
              <p className="text-sm text-gray-600">
                {groupCVs?.data.count || 0} candidates • {selectedCVs.length} selected
              </p>
            </div>

            {/* {groupsData && <ImportBulkCVGroupSelector onCreateNew={} />} */}
          </div>

          <div className="flex items-center gap-3">
            <Button variant="outlined" size="small" onClick={openImportModal} disabled={isLoading}>
              <Download className="h-4 w-4 mr-2" />
              Import
            </Button>
            <Button variant="outlined" size="small" onClick={exportCvFiles} disabled={selectedCVs.length === 0}>
              <Download className="h-4 w-4 mr-2 rotate-180" />
              Export ({selectedCVs.length})
            </Button>

            <Button size="small" onClick={handleMoveToGroup} disabled={selectedCVs.length === 0}>
              <Move className="h-4 w-4 mr-2" />
              Move To group
            </Button>

            <Button size="small" onClick={openMoveToPipelineModal} disabled={selectedCVs.length === 0}>
              <Move className="h-4 w-4 mr-2" />
              Move To pipeline
            </Button>
          </div>
        </div>

        <div className="flex items-center">
          <div className="relative p-1.5 pr-0 bg-white">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search candidates..."
              value={searchQuery}
              variant="dark_showcase"
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          <CVGroupFilterPanel
            filters={filters}
            filterOptions={filterOptions}
            onFilterChange={handleFilterChange}
            onClearFilters={handleClearFilters}
          />
        </div>
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="mb-1 flex flex-wrap gap-2">
          {filters.roles.map((role) => (
            <Badge key={role} variant="secondary" className="flex items-center gap-1">
              Role: {role}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() =>
                  handleFilterChange({
                    roles: filters.roles.filter((r) => r !== role),
                  })
                }
              />
            </Badge>
          ))}
          {filters.industries.map((industry) => (
            <Badge key={industry} variant="secondary" className="flex items-center gap-1">
              Industry: {industry}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() =>
                  handleFilterChange({
                    industries: filters.industries.filter((i) => i !== industry),
                  })
                }
              />
            </Badge>
          ))}
          {filters.locations.map((location) => (
            <Badge key={location} variant="secondary" className="flex items-center gap-1">
              Location: {location}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() =>
                  handleFilterChange({
                    locations: filters.locations.filter((l) => l !== location),
                  })
                }
              />
            </Badge>
          ))}
          {filters.languages.map((language) => (
            <Badge key={language} variant="secondary" className="flex items-center gap-1">
              Language: {language}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() =>
                  handleFilterChange({
                    languages: filters.languages.filter((l) => l !== language),
                  })
                }
              />
            </Badge>
          ))}
          {filters.yearsOfExperience && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Experience: {filters.yearsOfExperience}
              <X className="h-3 w-3 cursor-pointer" onClick={() => handleFilterChange({ yearsOfExperience: "" })} />
            </Badge>
          )}
          {filters.minKeywordPercentage > 0 && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Keywords: {filters.minKeywordPercentage}%-{filters.maxKeywordPercentage}%
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() =>
                  handleFilterChange({
                    minKeywordPercentage: 0,
                    maxKeywordPercentage: 100,
                  })
                }
              />
            </Badge>
          )}
        </div>
      )}

      {/* Main Content */}
      <div className="w-full overflow-hidden">
        <GroupCandidatesTable
          tableData={groupCVs?.data.results || []}
          isLoading={isLoading}
          isFetching={isFetching}
          selectedCVs={selectedCVs}
          onSelectCV={handleSelectCV}
          onSelectAll={handleSelectAll}
          onClearSelection={handleClearSelection}
          currentPage={currentPage}
          onPageChange={setCurrentPage}
          setRowSelectedArray={setSelectedCVs}
          setSelectedCVs={setSelectedCVs}
          totalPages={groupCVs?.data.total_pages || 1}
          totalCount={groupCVs?.data.count || 0}
          pageSize={10}
          refetchData={refetchGroupCVs}
        />
      </div>

      {/* Modals */}
      <ImportBulkCVModal
        isOpen={isImportModalOpen}
        closeModal={closeImportModal}
        defaultGroup={CVGroup}
        onComplete={() => refetchGroupCVs()}
      />

      <MoveToGroupModal
        isOpen={showMoveModal}
        filteredGroupsData={CVGroups?.data || []}
        rowSelected={selectedCVs}
        openCreateGroupModal={openCreateGroupModal}
        setIsOpen={setShowMoveModal}
      />
      <MoveToPipelineModal
        isOpen={isMoveToPipelineModalOpen}
        rowSelected={selectedCVs}
        closeMoveToPipelineModal={closeMoveToPipelineModal}
      />

      <CreateCVGroupModal
        isOpen={isCreateGroupModalOpen}
        closeModal={closeCreateGroupModal}
        onSuccess={() => {
          refetchCVGroupsList()
          setShowMoveModal(true)
        }}
        filter_group={true}
      />
    </div>
  )
}
