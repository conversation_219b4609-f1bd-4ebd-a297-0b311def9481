"use client"
import { AppHeaderRecruiter } from '@/components/shared'
import React from 'react'
interface Props {
    children: React.ReactNode
}
const MyCandidatesLayout: React.FC<Props> = ({ children }) => {
    return (
        <main className="main">
            <div className='maincontentwithheader grid grid-rows-[max-content_1fr] flex-col bg-[#F8F9FB] h-full !overflow-hidden '>
                <AppHeaderRecruiter title='My Candidates' className='bg-white md:!pt-2' />
                {children}
            </div>
        </main>
    )
}

export default MyCandidatesLayout