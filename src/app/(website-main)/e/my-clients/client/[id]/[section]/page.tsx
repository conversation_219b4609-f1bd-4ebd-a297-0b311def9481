'use client';

import { useRouter } from 'next/navigation';
import React from 'react';
import { <PERSON><PERSON>, LineTabs } from '@/components/shared';
import { useGetClient } from '../../../misc/api/getSingleClient';
import ClientDetails from '../../../misc/components/ClientDetails';
import ClientDashboardHeader from '../../../misc/components/ClientHeader';
import ClientJobs from '../../../misc/components/ClientJobs';
import ClientTeamMembers from '../../../misc/components/ClientTeamMembers';

// import TalentHistory from '../../../misc/components/TalentHistory';

const ClientDetailView = ({
  params,
}: {
  params: { id: string; section: string };
}) => {
  const { id: unique_id, section } = params;

  const { data: client } = useGetClient({ clientId: unique_id });

  const router = useRouter();

  const [searchQuery, setSearchQuery] = React.useState('');

  const tabsArray = [
    {
      id: 1,
      title: 'Jobs',
      link: './all',
      component: <ClientJobs clientId={unique_id} />,
    },
    {
      id: 2,
      title: 'Team Members',
      link: './members',
      component: client && <ClientTeamMembers client={client} />,
    },
    {
      id: 3,
      title: 'Client Details',
      link: './client-details',
      component: client && <ClientDetails client={client} />,
    },
  ];
  return (
    <div className="h-screen overflow-y-hidden rounded-b-xl ">
      <ClientDashboardHeader
        title="Clients"
        className="bg-white"
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
      />
      <div className="">
        <div className="bg-white p-4">
          <Button
            variant="light"
            size="capsule"
            onClick={() => router.push('/e/my-clients')}
          >
            Back
          </Button>
          <div className="my-2 flex items-center justify-between pb-2 ">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                {client?.company_name}
              </h2>
              <div className="mt-1 flex gap-4 text-sm text-gray-500">
                <p className="">Email: {client?.contact_person_email}</p>
                <p>Open jobs: {client?.activeJobs}</p>
                <p>Hires: {client?.candidates}</p>
              </div>
            </div>
          </div>
        </div>

        <LineTabs
          className="max-w-screen grid max-h-full grow grid-rows-[max-content,1fr] overflow-y-hidden bg-gray-100  "
          tabListClassName="md:!my-0  bg-white py-3"
          fallback="all"
          currentTab={section}
          catgoryArray={tabsArray}
        />
      </div>
    </div>
  );
};

export default ClientDetailView;
