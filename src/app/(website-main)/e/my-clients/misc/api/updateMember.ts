import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { TeamMember } from '../types';

const updateTeamMember = async ({
  clientId,
  data,
  memberId,
}: {
  clientId: number;
  memberId: number;
  data: Partial<TeamMember>;
}): Promise<TeamMember> => {
  const res = await Axios.patch(
    `/clients/${clientId}/team-members/${memberId}/`,
    data
  );
  return res.data.data as TeamMember;
};

export const useEditTeamMember = () => {
  const queryClient = useQueryClient();

  return useMutation<
    TeamMember,
    Error,
    { clientId: number; memberId: number; data: Partial<TeamMember> }
  >({
    mutationFn: ({ clientId, memberId, data }) =>
      updateTeamMember({ clientId, memberId, data }),
    onSuccess: () => {
      queryClient.invalidateQueries(['fetch-team-members']);
    },
  });
};
