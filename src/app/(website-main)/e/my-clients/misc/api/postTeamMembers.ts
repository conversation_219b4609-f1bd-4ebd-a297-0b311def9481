import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { TeamMember } from '../types';

const addTeamMember = async ({
  data,
  clientId,
}: {
  data: TeamMember;
  clientId: number;
}) => {
  const response = await Axios.post(
    `/clients/${clientId}/team-members/`,
    data,
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
  return response.data.data as TeamMember;
};

const useAddTeamMember = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: addTeamMember,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['fetch-team-members'],
        refetchType: 'all',
      });
    },
  });
};

export default useAddTeamMember;
