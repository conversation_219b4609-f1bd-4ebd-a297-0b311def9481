import { useQuery } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { ClientsResponse } from '../types';

const getTalentAnalytics = async ({
  clientId,
}: {
  clientId: string;
}): Promise<ClientsResponse> => {
  const res = await Axios.get(`/clients/${clientId}/talentanalytics/`);
  return res.data;
};

export const useGetTalentAnalytics = ({ clientId }: { clientId: string }) =>
  useQuery<ClientsResponse, Error>({
    queryKey: ['fetch-talents'],
    queryFn: () => getTalentAnalytics({ clientId }),
    refetchOnWindowFocus: false,
  });
