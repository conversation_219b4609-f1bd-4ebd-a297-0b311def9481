import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { ClientsResponse } from '../types';

const updateClient = async ({
  clientId,
  data,
}: {
  clientId: string;
  data: Partial<ClientsResponse>;
}): Promise<ClientsResponse> => {
  const res = await Axios.patch(`/clients/${clientId}/`, data);
  return res.data;
};

export const useUpdateClient = () => {
  const queryClient = useQueryClient();

  return useMutation<
    ClientsResponse,
    Error,
    { clientId: string; data: Partial<ClientsResponse> }
  >({
    mutationFn: ({ clientId, data }) => updateClient({ clientId, data }),
    onSuccess: () => {
      queryClient.invalidateQueries(['fetch-clients']);
    },
  });
};
