import { useQuery } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { TeamMember } from '../types';

const getTeamMember = async (
  clientId: string,
  memberId: string
): Promise<TeamMember> => {
  const res = await Axios.get(`/clients/${clientId}/team-members/${memberId}`);
  return res.data;
};

export const useGetTeamMember = (clientId: string, memberId: string) =>
  useQuery<TeamMember, Error>({
    queryKey: ['fetch-team-member', clientId, memberId],
    queryFn: () => getTeamMember(clientId, memberId),
    refetchOnWindowFocus: false,
  });
