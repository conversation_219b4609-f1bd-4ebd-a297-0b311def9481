import { useQuery } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { ClientJobResponse } from '../types';

const getJobAnalytics = async ({
  clientId,
}: {
  clientId: number;
}): Promise<ClientJobResponse> => {
  const res = await Axios.get(`/clients/${clientId}/jobanalytics/`);
  return res.data;
};

export const useGetJobAnalytics = ({ clientId }: { clientId: number }) =>
  useQuery<ClientJobResponse, Error>({
    queryKey: ['fetch-jobs'],
    queryFn: () => getJobAnalytics({ clientId }),
    refetchOnWindowFocus: false,
  });

const getJobAnalyticsForClient = async (
  clientId: string
): Promise<ClientJobResponse> => {
  const res = await Axios.get(`/clients/${clientId}/jobanalytics/`);
  return res.data;
};

export const useGetAllJobAnalytics = (clientIds: string[]) => {
  return useQuery<ClientJobResponse[], Error>({
    queryKey: ['fetch-all-job-analytics', clientIds],
    queryFn: async () => {
      const responses = await Promise.all(
        clientIds.map(getJobAnalyticsForClient)
      );
      return responses;
    },
    enabled: clientIds.length > 0,
    refetchOnWindowFocus: false,
  });
};
