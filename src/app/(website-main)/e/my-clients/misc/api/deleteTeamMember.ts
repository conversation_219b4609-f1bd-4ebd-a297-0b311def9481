import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';

const deleteClient = async ({
  clientId,
  memberId,
}: {
  clientId: number;
  memberId: number;
}) => {
  const res = await Axios.delete(
    `/clients/${clientId}/team-members/${memberId}/`
  );
  return res.data;
};

export const useDeleteTeamMember = () => {
  const queryClient = useQueryClient();
  return useMutation(deleteClient, {
    onSuccess: () => {
      queryClient.invalidateQueries(['fetch-team-members']);
    },
  });
};
