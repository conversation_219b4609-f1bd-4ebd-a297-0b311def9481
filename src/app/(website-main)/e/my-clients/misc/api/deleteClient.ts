import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { ClientsResponse } from '../types';

const deleteClient = async ({
  clientId,
}: {
  clientId: string;
}): Promise<ClientsResponse> => {
  const res = await Axios.delete(`/clients/${clientId}/`);
  return res.data;
};

export const useDeleteClient = ({ clientId }: { clientId: string }) => {
  const queryClient = useQueryClient();

  return useMutation<ClientsResponse, Error, { clientId: string }>({
    mutationFn: ({ clientId }) => deleteClient({ clientId }),
    onSuccess: () => {
      queryClient.invalidateQueries(['fetch-clients']);
    },
  });
};
