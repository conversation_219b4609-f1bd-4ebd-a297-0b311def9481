import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { CreateClientForm } from '../types';

const CreateClient = async (CreateClientDto: CreateClientForm) => {
  const response = await Axios.post('/clients/', CreateClientDto, {
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return response.data;
};

const useCreateClient = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: CreateClient,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['fetch-clients'],
      });
    },
  });
};

export default useCreateClient;
