import { useQuery } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { ClientAnalyticstData, ClientsResponse } from '../types';

const getClientAnalytics = async ({
  clientId,
}: {
  clientId: number;
}): Promise<ClientAnalyticstData> => {
  const res = await Axios.get(`/clients/${clientId}/clientanalytics`);
  return res.data;
};

export const useGetClientAnalytics = ({ clientId }: { clientId: number }) =>
  useQuery<ClientAnalyticstData, Error>({
    queryKey: ['fetch-clients'],
    queryFn: () => getClientAnalytics({ clientId }),
    refetchOnWindowFocus: false,
  });
