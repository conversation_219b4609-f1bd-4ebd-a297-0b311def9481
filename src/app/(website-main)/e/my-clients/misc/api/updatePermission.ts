import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { Permission } from '../types';

const updateClientPermission = async ({
  token,
  data,
}: {
  token: string;
  data: Partial<Permission>[];
}): Promise<Permission[]> => {
  const res = await Axios.patch(
    `/clients-share/${token}/update_permissions/`,
    data
  );
  return res.data;
};

export const useUpdateClientPermission = () => {
  const queryClient = useQueryClient();

  return useMutation<
    Permission[],
    Error,
    { token: string; data: Partial<Permission>[] }
  >({
    mutationFn: ({ token, data }) => updateClientPermission({ token, data }),
    onSuccess: () => {
      queryClient.invalidateQueries(['fetch-clients']);
    },
  });
};
