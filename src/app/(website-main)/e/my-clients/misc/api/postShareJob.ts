import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { ShareJobRequest } from '../types';

const shareJob = async (data: ShareJobRequest): Promise<void> => {
  try {
    const response = await Axios.post('/client-shares/', data, {
      headers: { 'Content-Type': 'application/json' },
    });
    return response.data;
  } catch (error) {
    throw new Error('Failed to share job');
  }
};

type RevokeAccessParams = { token: string; clientId: number };

const revokeAccess = async ({ token }: RevokeAccessParams): Promise<void> => {
  try {
    const response = await Axios.delete(
      `/client-shares/${token}/revoke_access/`
    );
    return response.data;
  } catch (error) {
    throw new Error('Failed to revoke access');
  }
};

export const useShareJob = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: shareJob,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['team-members'] });
    },
  });
};
export const useRevokeAccess = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: revokeAccess,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['team-members'] });
    },
  });
};
