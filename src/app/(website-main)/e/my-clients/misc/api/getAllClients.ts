import { useQuery } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { ClientsResponse } from '../types';

const getAllclients = async (): Promise<ClientsResponse[]> => {
  const res = await Axios.get(`/clients/`);
  return res.data;
};

export const useGetAllClients = () =>
  useQuery<ClientsResponse[], Error>({
    queryKey: ['fetch-clients'],
    queryFn: () => getAllclients(),
    refetchOnWindowFocus: false,
  });
