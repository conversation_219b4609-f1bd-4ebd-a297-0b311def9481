import { useQuery } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { ClientsResponse } from '../types';

const getClient = async ({
  clientId,
}: {
  clientId: string;
}): Promise<ClientsResponse> => {
  const res = await Axios.get(`/clients/${clientId}`);
  return res.data;
};

export const useGetClient = ({ clientId }: { clientId: string }) =>
  useQuery<ClientsResponse, Error>({
    queryKey: ['fetch-client', clientId],
    queryFn: () => getClient({ clientId }),
    refetchOnWindowFocus: false,
  });
