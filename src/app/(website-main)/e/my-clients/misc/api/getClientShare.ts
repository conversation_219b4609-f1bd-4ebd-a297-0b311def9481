import { useQuery } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { ClientPermission } from '../types';

const getClientWithPermissions = async (): Promise<ClientPermission[]> => {
  const res = await Axios.get(`/client-shares/`);
  return res.data;
};

export const useGetClientWithPermissions = () =>
  useQuery<ClientPermission[], Error>({
    queryKey: ['fetch-client'],
    queryFn: () => getClientWithPermissions(),
    refetchOnWindowFocus: false,
  });
