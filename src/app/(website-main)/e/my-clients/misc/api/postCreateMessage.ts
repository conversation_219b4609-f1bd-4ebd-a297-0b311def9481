import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { CreateMessageForm } from '../types';

const CreateMessage = async (CreateMessageDto: CreateMessageForm) => {
  const response = await Axios.post(
    '/clients/send_bulk_emails/',
    CreateMessageDto,
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
  return response.data;
};

const useCreateMessage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: CreateMessage,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['fetch-clients'],
      });
    },
  });
};

export default useCreateMessage;
