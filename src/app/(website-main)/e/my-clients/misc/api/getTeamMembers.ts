import { useQuery } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { TeamMember } from '../types';

const getAllTeamMembers = async (clientId: number): Promise<TeamMember[]> => {
  const res = await Axios.get(`/clients/${clientId}/team-members/`);
  return res.data as TeamMember[];
};

export const useGetAllTeamMembers = (clientId: number) =>
  useQuery<TeamMember[], Error>({
    queryKey: ['fetch-team-members', clientId],
    queryFn: () => getAllTeamMembers(clientId),
    refetchOnWindowFocus: false,
  });
