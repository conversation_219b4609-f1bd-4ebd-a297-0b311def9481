import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import {
  Button,
  Dialog,
  DialogClose,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from '@/components/shared';
import { CopyButton } from '../../../jobs/misc/components/CopyButton';
import { useShareJob } from '../api/postShareJob';

const ShareLinkToTalent = ({
  trigger,
  job_id,
}: {
  trigger: React.ReactNode;
  job_id?: string;
}) => {
  const [showEmailForm, setShowEmailForm] = useState(false);

  const jobLink = `${process.env.NEXT_PUBLIC_FRONTEND_URL}/jobs/${job_id}`;

  const { mutate: shareJobMutation, isLoading: isSharing } = useShareJob();

  const [open, setOpen] = React.useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<{ emails: string }>({
    defaultValues: { emails: '' },
  });

  const onSubmit = (data: { emails: string }) => {
    // Split emails by comma and trim whitespace

    shareJobMutation({
      client_email: data.emails,
      permissions: [],
      job_id: job_id ?? '',
      team_member_emails: [],
      frontend_url: jobLink,
    });
    setShowEmailForm(false);
  };
  return (
    <div>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger size={'tiny'} variant={'light'} asChild className="">
          {trigger}
        </DialogTrigger>

        <DialogContent className="max-w-md">
          {/* Header */}
          <div className="rounded-t-xl bg-primary p-4 text-white sm:p-6">
            <div className="flex items-center justify-between">
              <DialogTitle className=" font-semibold sm:text-lg">
                Share link to talent
              </DialogTitle>
              <DialogClose asChild>
                <Button
                  size="tiny"
                  className="bg-white/30 px-3 py-2 text-xs hover:bg-white/20 sm:px-6 sm:text-sm"
                >
                  Close
                </Button>
              </DialogClose>
            </div>
          </div>

          <div className="p-4 sm:p-6">
            <p className="mb-6 text-xs text-gray-600 sm:text-sm">
              Send an invite to a talent to apply for this opening or copy the
              link of the job and share'
            </p>

            {/* Email Form */}
            {showEmailForm && (
              <form
                onSubmit={handleSubmit(onSubmit)}
                className="mb-6 space-y-4"
              >
                {/* Email Input */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Email address
                  </label>
                  <textarea
                    {...register('emails')}
                    placeholder="<EMAIL>, <EMAIL>, fav@..."
                    className="h-16 w-full resize-none rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                  {errors.emails && (
                    <p className="mt-1 text-xs text-red-500">
                      {errors.emails.message}
                    </p>
                  )}
                </div>

                {/* Submit Button */}
                <button
                  type="submit"
                  className="w-full rounded-lg bg-primary py-3 text-sm font-medium text-white transition-colors hover:bg-primary disabled:bg-purple-400 sm:text-base"
                >
                  {isSharing ? 'Sending...' : 'Send job'}
                </button>
              </form>
            )}

            {/* Divider */}
            <div className="my-6 border-t border-gray-200"></div>

            {/* Copy Job Link Section */}
            <div className="space-y-4">
              <h3 className="text-sm font-semibold text-gray-900 sm:text-base">
                Copy job link
              </h3>

              <div className="flex items-center justify-between gap-4 rounded-lg  border border-gray-200 bg-gray-50 p-2 px-3 text-sm text-gray-700">
                <span className="truncate">{jobLink}</span>
                <CopyButton text={jobLink} className="shrink-0 text-sm" />
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ShareLinkToTalent;
