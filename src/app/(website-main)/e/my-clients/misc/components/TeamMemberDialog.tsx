import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import {
  <PERSON><PERSON>,
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTrigger,
} from '@/components/shared';

// Validation schema (can import if already defined elsewhere)
const teamMemberSchema = z.object({
  memberName: z.string().min(1, 'Member name is required'),
  email: z.string().email('Invalid email address'),
  phoneNumber: z.string().min(1, 'Phone number is required'),
  title: z.string().min(1, 'Title is required'),
});

export type TeamMemberFormData = z.infer<typeof teamMemberSchema>;

type TeamMemberDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  initialValues?: Partial<TeamMemberFormData>;
  onSubmit: (data: TeamMemberFormData) => void | Promise<void>;
  isLoading?: boolean;
  mode?: 'create' | 'edit';
  trigger?: React.ReactNode;
};

export default function TeamMemberDialog({
  open,
  onOpenChange,
  initialValues = {},
  onSubmit,
  isLoading,
  mode = 'create',
  trigger,
}: TeamMemberDialogProps) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<TeamMemberFormData>({
    resolver: zodResolver(teamMemberSchema),
    defaultValues: {
      memberName: initialValues.memberName ?? '',
      email: initialValues.email ?? '',
      phoneNumber: initialValues.phoneNumber ?? '',
      title: initialValues.title ?? '',
    },
  });

  React.useEffect(() => {
    // Reset form when dialog is opened or initialValues change
    if (open) {
      reset({
        memberName: initialValues.memberName ?? '',
        email: initialValues.email ?? '',
        phoneNumber: initialValues.phoneNumber ?? '',
        title: initialValues.title ?? '',
      });
    }
  }, [open, initialValues, reset]);

  const titleText = mode === 'edit' ? 'Edit team member' : 'Add team member';
  const buttonText =
    mode === 'edit'
      ? isLoading
        ? 'Updating...'
        : 'Update member'
      : isLoading
      ? 'Adding...'
      : 'Add new member';

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <h3 className="text-lg font-medium">{titleText}</h3>
          <DialogClose asChild>
            <button className="bg-white/30 text-white hover:bg-white/20 hover:text-gray-200">
              Close
            </button>
          </DialogClose>
        </DialogHeader>

        <div className="px-6 py-6">
          <div className="mb-6">
            <h4 className="mb-2 text-xl font-semibold text-gray-900">
              {mode === 'edit' ? 'Edit Team Member' : 'Add a New Team Member'}
            </h4>
            <p className="text-sm text-gray-600">
              {mode === 'edit'
                ? 'Update the information for this team member.'
                : 'Enter the information below to add a new team member and manage their report and access level.'}
            </p>
          </div>

          <form
            onSubmit={handleSubmit(data => onSubmit(data))}
            className="space-y-4"
          >
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-900">
                Member Name
              </label>
              <input
                {...register('memberName')}
                type="text"
                placeholder="Enter member name"
                className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm placeholder-gray-500 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
                disabled={isLoading}
              />
              {errors.memberName && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.memberName.message}
                </p>
              )}
            </div>
            {mode === 'create' && (
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-900">
                  Email
                </label>
                <input
                  {...register('email')}
                  type="email"
                  placeholder="Enter email address"
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm placeholder-gray-500 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
                  disabled={isLoading}
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.email.message}
                  </p>
                )}
              </div>
            )}
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-900">
                Phone Number
              </label>
              <input
                {...register('phoneNumber')}
                type="tel"
                placeholder="Enter phone number"
                className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm placeholder-gray-500 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
                disabled={isLoading}
              />
              {errors.phoneNumber && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.phoneNumber.message}
                </p>
              )}
            </div>
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-900">
                Title
              </label>
              <input
                {...register('title')}
                type="text"
                className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
                disabled={isLoading}
              />
              {errors.title && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.title.message}
                </p>
              )}
            </div>
            <Button type="submit" disabled={isLoading} className="w-full">
              {isLoading && (
                <svg
                  className="-ml-1 mr-3 inline-block h-4 w-4 animate-spin align-middle text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              )}
              {buttonText}
            </Button>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
