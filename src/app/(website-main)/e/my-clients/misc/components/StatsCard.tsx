import React from 'react';

interface StatsCardProps {
  icon: React.ReactNode;
  value: number;
  label: string;
}

export const StatsCard: React.FC<StatsCardProps> = ({ icon, value, label }) => {
  return (
    <div className="rounded-2xl border border-gray-100 bg-white px-6 py-10 ">
      <div className="flex items-center gap-4">
        <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-primary-light text-[#502B67]">
          {icon}
        </div>
        <div>
          <div className="text-2xl font-bold text-gray-900">{value}</div>
          <div className="text-sm text-gray-500">{label}</div>
        </div>
      </div>
    </div>
  );
};
