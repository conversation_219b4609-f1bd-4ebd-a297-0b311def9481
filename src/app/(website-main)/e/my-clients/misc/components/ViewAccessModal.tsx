'use client';

import { Loader2 } from 'lucide-react';
import React from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import {
  Button,
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
} from '@/components/shared';
import { useRevokeAccess } from '../api/postShareJob';
import { useUpdateClientPermission } from '../api/updatePermission';
import { ClientPermission, ClientsResponse, Permission } from '../types';

interface ViewAccessModalProps {
  trigger: React.ReactNode;
  member: ClientsResponse;
  permissions: ClientPermission;
}

export function ViewAccessModal({
  trigger,
  member,
  permissions,
}: ViewAccessModalProps) {
  const [open, setOpen] = React.useState(false);
  const {
    mutate: updatePermissionsMutation,
    isLoading: updatePermissionsLoading,
  } = useUpdateClientPermission();
  const { mutate: revokeAccess, isLoading: revokeAccessLoading } =
    useRevokeAccess();

  const { control, watch, setValue } = useForm<{ permissions: Permission[] }>({
    defaultValues: {
      permissions: member.permissions,
    },
  });

  const selectedPermissions = watch('permissions');

  const handlePermissionChange = async (
    permission: Permission,
    checked: boolean
  ) => {
    let newPermissions: Partial<Permission>[] = [];

    if (checked) {
      newPermissions = [...selectedPermissions, permission];
    } else {
      newPermissions = selectedPermissions?.filter(p => p !== permission);
    }

    setValue('permissions', newPermissions);

    try {
      await updatePermissionsMutation({
        token: '',
        data: newPermissions,
      });
    } catch (error) {
      console.error('Failed to update permissions:', error);
    }
  };

  const handleRevokeAccess = async () => {
    revokeAccess(
      {
        token: permissions.token,
        clientId: member.id,
      },
      {
        onSuccess: () => {
          setOpen(false);
          toast.success('Access revoked successfully');
        },
        onError: error => {
          console.error('Failed to revoke access:', error);
          toast.error('Failed to revoke access');
        },
      }
    );
  };

  const isLoading = updatePermissionsLoading || revokeAccessLoading;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger variant="light" size="tiny" asChild>
        {trigger}
      </DialogTrigger>
      <DialogContent className=" max-w-md">
        {/* Header */}

        <div className="rounded-t-xl bg-primary p-4 text-white sm:p-6">
          <div className="flex items-center justify-between">
            <DialogTitle className=" font-semibold sm:text-lg">
              Access Permissions
            </DialogTitle>
            <DialogClose asChild>
              <Button
                size="tiny"
                className="bg-white/30 px-3 py-2 text-xs hover:bg-white/20 sm:px-6 sm:text-sm"
              >
                Close
              </Button>
            </DialogClose>
          </div>
        </div>

        <div className="space-y-6 p-4">
          {/* Member Info */}
          <div className="flex items-center space-x-3 border-b pb-4">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-300 text-sm font-medium text-gray-600">
              <span>{member.contact_person_name.charAt(0)}</span>
            </div>
            <div>
              <p className="font-medium text-gray-900">
                {member.contact_person_name}
              </p>
              <p className="text-sm text-gray-500">
                {member.contact_person_email}
              </p>
            </div>
          </div>

          {/* Permissions */}
          <div>
            <DialogDescription className="mb-3 text-sm font-medium text-gray-700">
              See all access permission
            </DialogDescription>
            <div className="space-y-3">
              {permissions?.permissions?.map((p, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <label className="flex-1 cursor-pointer text-sm capitalize text-gray-700">
                    {p.split('_').join(' ')}
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3 border-t pt-4">
            <Button
              size="tiny"
              variant="light"
              onClick={handleRevokeAccess}
              disabled={isLoading}
            >
              {revokeAccessLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Revoking...</span>
                </>
              ) : (
                <span>Revoke access</span>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
