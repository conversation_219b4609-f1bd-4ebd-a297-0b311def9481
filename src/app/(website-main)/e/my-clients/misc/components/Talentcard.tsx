'use client';

import * as Popover from '@radix-ui/react-popover';
import { CalendarDays, Mail, MapPin, MoreVertical } from 'lucide-react';
import Image from 'next/image';
import React, { useState } from 'react';
import { Button } from '@/components/shared';
import { UserProfile } from '../types';
import ClientTalentView from './ClientTalentView';

export default function TalentCard({ talent }: { talent: UserProfile }) {
  const { name, role, email, location, dateHired, avatar } = talent;
  const [actionOpen, setActionOpen] = useState<number | null>(null);

  const handleView = () => {
    alert("View Dexter's profile");
  };

  const handleActionClick = (talentId: number) => {
    setActionOpen(actionOpen === talentId ? null : talentId);
  };

  return (
    <div className="w-full min-w-0 flex-grow space-y-4 rounded-2xl bg-[#F9F9F9] p-4 shadow-sm">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Image
            width={300}
            height={300}
            src={avatar}
            alt={name}
            className="h-10 w-10 rounded-full object-cover"
          />
          <div>
            <h2 className="text-sm font-semibold text-gray-900">{name}</h2>
            <p className="text-xs text-gray-500">{role}</p>
          </div>
        </div>
        <div className="mt-3 flex justify-end">
          <Popover.Root
            open={actionOpen === talent.id}
            onOpenChange={open => setActionOpen(open ? talent.id : null)}
          >
            <Popover.Trigger asChild>
              <button
                onClick={() => handleActionClick(talent.id)}
                className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-light p-1 text-primary"
              >
                <MoreVertical className="h-4 w-4" />
              </button>
            </Popover.Trigger>
            <Popover.Portal>
              <Popover.Content
                sideOffset={5}
                className="w-40 rounded-md border border-gray-200 bg-white p-2 shadow-lg"
              >
                <div className="py-1">
                  <button className="block w-full px-4 py-2 text-left text-sm hover:bg-gray-100">
                    View Profile
                  </button>
                  <button className="block w-full px-4 py-2 text-left text-sm hover:bg-gray-100">
                    Edit
                  </button>
                  <button className="block w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-gray-100">
                    Remove
                  </button>
                </div>
              </Popover.Content>
            </Popover.Portal>
          </Popover.Root>
        </div>
      </div>

      {/* Info Section */}
      <div className="space-y-3 rounded-xl bg-white p-4 text-sm text-gray-700">
        <div className="flex items-center gap-2 text-primary">
          <Mail className="h-4 w-4" />
          <span className="text-gray-700">{email}</span>
        </div>
        <div className="flex items-center gap-2 text-primary">
          <MapPin className="h-4 w-4" />
          <span className="text-gray-700">{location}</span>
        </div>
        <div className="flex items-center gap-2 text-primary">
          <CalendarDays className="h-4 w-4" />
          <span className="text-gray-700">{dateHired}</span>
        </div>
      </div>

      {/* View Button */}
      <div className="flex w-full justify-center rounded-xl bg-primary-light py-2 text-sm font-medium text-primary transition hover:bg-primary-light-hover">
        <ClientTalentView talent={talent} />
      </div>
    </div>
  );
}
