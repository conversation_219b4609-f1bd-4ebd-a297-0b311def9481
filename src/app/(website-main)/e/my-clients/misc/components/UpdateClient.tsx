import { zodResolver } from '@hookform/resolvers/zod';
import { Country } from 'country-state-city';
import { ChevronDown } from 'lucide-react';
import React, { FC, useEffect, useState } from 'react';
import { FieldError, useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import * as z from 'zod';
import {
  Button,
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerHeader,
} from '@/components/shared';
import { useWindowWidth } from '@/hooks';
import useFileUpload from '@/hooks/useFileUpload';
import useGetCountryState from '@/hooks/useGetCountryState';
import { useLoading } from '@/lib/contexts/LoadingContext';
import { useUser } from '@/lib/contexts/UserContext';
import { useGetClient } from '../api/getSingleClient';
import { useUpdateClient } from '../api/updateClient';
import { companySizes, industries, roles } from '../constants';
import { clientSchema } from '../utils/clientSchema';

type ClientFormData = z.infer<typeof clientSchema>;

type UpdateClientDialogProps = {
  isOpen: boolean;
  onClose: () => void;
  clientId: string;
};

type SelectProps = {
  options: string[];
  placeholder: string;
  value?: string;
  onChange: (value: string) => void;
  error?: FieldError;
  disabled?: boolean;
};

const Select: FC<SelectProps> = ({
  options,
  placeholder,
  value,
  onChange,
  error,
  disabled = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const filteredOptions = options.filter(option =>
    option.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="relative">
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`w-full rounded-lg border border-gray-200 bg-gray-50 px-4 py-3 text-left transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary ${
          error ? 'border-red-300 bg-red-50' : ''
        } ${disabled ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-100'}`}
      >
        <span className={value ? 'text-gray-900' : 'text-gray-500'}>
          {value || placeholder}
        </span>
        <ChevronDown
          className={`absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400 transition-transform ${
            isOpen ? 'rotate-180' : ''
          }`}
        />
      </button>

      {isOpen && !disabled && (
        <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-lg border border-gray-200 bg-white shadow-lg">
          <div className="sticky top-0 bg-white p-2">
            <input
              type="text"
              placeholder="Search..."
              className="w-full rounded border px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary"
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
            />
          </div>
          {filteredOptions.length > 0 ? (
            filteredOptions.map(option => (
              <button
                key={option}
                type="button"
                onClick={() => {
                  onChange(option);
                  setSearchTerm('');
                  setIsOpen(false);
                }}
                className="w-full border-b border-gray-100 px-4 py-2 text-left hover:bg-purple-50 hover:text-primary"
              >
                {option}
              </button>
            ))
          ) : (
            <div className="p-2 text-sm text-gray-500">No options found</div>
          )}
        </div>
      )}
    </div>
  );
};

const UpdateClientDialog: FC<UpdateClientDialogProps> = ({
  isOpen,
  onClose,
  clientId,
}) => {
  const [error, setError] = useState<string | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const windowWidth = useWindowWidth();

  const { data: clientData } = useGetClient({ clientId });

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<ClientFormData>({
    resolver: zodResolver(clientSchema),
  });

  const { userInfo } = useUser();
  const { uploadToServer } = useFileUpload();
  const { isUploading } = useLoading();
  const { mutate: updateClient, isLoading } = useUpdateClient();

  // Transform API data to form data format
  useEffect(() => {
    if (isOpen && clientData) {
      let country = '';
      let state = '';
      if (clientData.location) {
        const parts = clientData.location.split(',').map(s => s.trim());
        if (parts.length === 2) {
          country = parts[0];
          state = parts[1];
        } else if (parts.length === 1) {
          country = parts[0];
        }
      }

      const transformedData = {
        companyName: clientData.company_name || '',
        industry: clientData.industry || '',
        companySize: clientData.company_size || '',
        country,
        state,
        address: clientData.description || '',
        website: clientData.company_website || '',
        companyLogo: clientData.company_logo || '',
        contactPerson: clientData.contact_person_name || '',
        role: clientData.contact_person_role || '',
        email: clientData.contact_person_email || '',
      };

      reset(transformedData);

      // Set logo preview if exists
      if (clientData.company_logo) {
        setLogoPreview(clientData.company_logo);
      }
    }
  }, [clientData, isOpen, reset]);

  const onSubmit = (data: ClientFormData) => {
    const payload = {
      user: userInfo?.user.id || '',
      company_name: data.companyName,
      industry: data.industry.toLowerCase(),
      company_size: data.companySize,
      location: data.state ? `${data.state}, ${data.country}` : data.country,
      contact_person_name: data.contactPerson,
      contact_person_role: data.role,
      contact_person_email: data.email,
      company_website: data.website || '',
      company_logo: data.companyLogo || '',
      description: data.address,
      email_domain: data.website,
    };

    updateClient(
      { data: payload, clientId: clientId },
      {
        onError: err => {
          setError('An error occurred while updating client');
        },
        onSuccess: () => {
          setError(null);
          handleClose();
        },
      }
    );
  };

  const handleClose = () => {
    if (!isLoading) {
      reset();
      setLogoPreview(null);
      setError(null);
      onClose();
    }
  };

  const watchedValues = watch();
  const { countries, states } = useGetCountryState(
    Country.getAllCountries().find(c => c.name === watchedValues.country)
      ?.isoCode
  );

  return (
    <Drawer
      open={isOpen}
      onOpenChange={open => !open && handleClose()}
      direction={windowWidth < 720 ? 'bottom' : 'right'}
    >
      <DrawerContent className="!m-0 h-[90vh] w-full overflow-hidden rounded-l-2xl border-none bg-white !p-0 md:left-auto md:right-0 md:h-screen md:w-[50%] md:max-w-[750px]">
        <div className="sticky left-0 top-0 z-10 flex w-full items-center justify-between bg-primary px-4 py-3 text-white sm:rounded-tl-lg sm:px-6 sm:py-4">
          <DrawerHeader className="rounded-tl-lg py-1 text-base sm:text-lg">
            Update Client Info
          </DrawerHeader>
          <DrawerClose asChild>
            <Button
              size="tiny"
              className="bg-white/30 px-3 py-1.5 text-sm transition-colors hover:bg-white/20 sm:px-4 sm:py-2"
            >
              Close
            </Button>
          </DrawerClose>
        </div>

        <div className="flex-1 overflow-y-auto px-6 py-6">
          {error && (
            <div className="mb-4 rounded-lg border border-red-200 bg-red-50 p-3 text-sm text-red-600">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Company Name */}
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Company Name
              </label>
              <input
                {...register('companyName')}
                placeholder="Enter the company name"
                disabled={isLoading}
                className={`w-full rounded-lg border px-4 py-3 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary ${
                  errors.companyName
                    ? 'border-red-300 bg-red-50'
                    : 'border-gray-200 bg-gray-50'
                } ${
                  isLoading
                    ? 'cursor-not-allowed opacity-50'
                    : 'hover:bg-gray-100'
                }`}
              />
              {errors.companyName && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.companyName.message}
                </p>
              )}
            </div>

            {/* Industry */}
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Industry
              </label>
              <Select
                options={industries}
                placeholder="Select clients industry"
                value={watchedValues.industry}
                onChange={value => setValue('industry', value)}
                error={errors.industry}
                disabled={isLoading}
              />
              {errors.industry && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.industry.message}
                </p>
              )}
            </div>

            {/* Company Size */}
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Company Size
              </label>
              <Select
                options={companySizes}
                placeholder="Select company size"
                value={watchedValues.companySize}
                onChange={value => setValue('companySize', value)}
                error={errors.companySize}
                disabled={isLoading}
              />
              {errors.companySize && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.companySize.message}
                </p>
              )}
            </div>

            {/* Country & State */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  Country
                </label>
                <Select
                  options={countries.map(c => c.name)}
                  placeholder="Select Country"
                  value={watchedValues.country}
                  onChange={value => {
                    setValue('country', value);
                    setValue('state', ''); // reset state
                  }}
                  error={errors.country}
                  disabled={isLoading}
                />
                {errors.country && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.country.message}
                  </p>
                )}
              </div>
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  State
                </label>
                <Select
                  options={states.map(s => s.name)}
                  placeholder="Select State"
                  value={watchedValues.state}
                  onChange={value => setValue('state', value)}
                  error={errors.state}
                  disabled={isLoading}
                />
                {errors.state && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.state.message}
                  </p>
                )}
              </div>
            </div>

            {/* Address/Description */}
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                {...register('address')}
                rows={3}
                placeholder="Enter company description"
                disabled={isLoading}
                className={`w-full resize-none rounded-lg border px-4 py-3 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary ${
                  errors.address
                    ? 'border-red-300 bg-red-50'
                    : 'border-gray-200 bg-gray-50'
                } ${
                  isLoading
                    ? 'cursor-not-allowed opacity-50'
                    : 'hover:bg-gray-100'
                }`}
              />
              {errors.address && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.address.message}
                </p>
              )}
            </div>

            {/* Website */}
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Website
              </label>
              <input
                {...register('website')}
                placeholder="Enter website URL"
                disabled={isLoading}
                className={`w-full rounded-lg border px-4 py-3 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary ${
                  errors.website
                    ? 'border-red-300 bg-red-50'
                    : 'border-gray-200 bg-gray-50'
                } ${
                  isLoading
                    ? 'cursor-not-allowed opacity-50'
                    : 'hover:bg-gray-100'
                }`}
              />
              {errors.website && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.website.message}
                </p>
              )}
            </div>

            {/* Company Logo Upload */}
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Company Logo
              </label>
              <input
                type="file"
                accept="image/*"
                disabled={isLoading || isUploading}
                onChange={async e => {
                  const file = e.target.files?.[0];
                  if (file) {
                    try {
                      const url = await uploadToServer(file);
                      setValue('companyLogo', url.url);
                      setLogoPreview(URL.createObjectURL(file));
                      setError(null);
                      toast.success('Logo uploaded successfully');
                    } catch {
                      setError('Failed to upload logo');
                      toast.error('Failed to upload logo');
                    }
                  }
                }}
                className="w-full rounded-lg border border-gray-200 bg-gray-50 px-4 py-2 text-sm file:mr-4 file:rounded file:border-0 file:bg-primary file:px-4 file:py-2 file:text-white hover:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-50"
              />
              {logoPreview && (
                <div className="mt-3">
                  <img
                    src={logoPreview}
                    alt="Logo Preview"
                    className="h-16 w-16 rounded border object-contain"
                  />
                </div>
              )}
              {isUploading && (
                <div className="mt-3 flex items-center">
                  <div className="mr-2 h-6 w-6 animate-spin rounded-full border-b-2 border-primary"></div>
                  <span className="text-sm text-gray-500">Uploading...</span>
                </div>
              )}
            </div>

            {/* Contact Person */}
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Contact Person
              </label>
              <input
                {...register('contactPerson')}
                placeholder="Enter contact person name"
                disabled={isLoading}
                className={`w-full rounded-lg border px-4 py-3 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary ${
                  errors.contactPerson
                    ? 'border-red-300 bg-red-50'
                    : 'border-gray-200 bg-gray-50'
                } ${
                  isLoading
                    ? 'cursor-not-allowed opacity-50'
                    : 'hover:bg-gray-100'
                }`}
              />
              {errors.contactPerson && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.contactPerson.message}
                </p>
              )}
            </div>

            {/* Role */}
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Role
              </label>
              <Select
                options={roles}
                placeholder="Select contact person role"
                value={watchedValues.role}
                onChange={value => setValue('role', value)}
                error={errors.role}
                disabled={isLoading}
              />
              {errors.role && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.role.message}
                </p>
              )}
            </div>

            {/* Email */}
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Email
              </label>
              <input
                {...register('email')}
                type="email"
                placeholder="Enter email address"
                disabled={isLoading}
                className={`w-full rounded-lg border px-4 py-3 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary ${
                  errors.email
                    ? 'border-red-300 bg-red-50'
                    : 'border-gray-200 bg-gray-50'
                } ${
                  isLoading
                    ? 'cursor-not-allowed opacity-50'
                    : 'hover:bg-gray-100'
                }`}
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.email.message}
                </p>
              )}
            </div>

            {/* Submit Button */}
            <div className="pt-4">
              <Button
                type="submit"
                disabled={isLoading || isUploading}
                className="w-full"
              >
                {isLoading ? 'Updating...' : 'Update Client'}
              </Button>
            </div>
          </form>
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export default UpdateClientDialog;
