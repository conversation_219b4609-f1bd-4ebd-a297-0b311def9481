'use client';

import * as Popover from '@radix-ui/react-popover';
import {
  ChevronLeft,
  ChevronRight,
  Filter,
  Grid3X3,
  List,
  MoreVertical,
  Search,
} from 'lucide-react';
import Image from 'next/image';
import React, { useMemo, useState } from 'react';
import { talents } from '../constants';
import { ClientsResponse } from '../types';
import ClientTalentView from './ClientTalentView';
import NoTalentDisplay from './NoTalentDisplay';
import TalentCard from './Talentcard';

const TalentHistory = ({ client }: { client: ClientsResponse }) => {
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [viewType, setViewType] = useState<'list' | 'grid'>('list');
  const [filterOpen, setFilterOpen] = useState<boolean>(false);
  const [actionOpen, setActionOpen] = useState<number | null>(null);
  const [roleFilter, setRoleFilter] = useState<string>('');
  const [locationFilter, setLocationFilter] = useState<string>('');

  // Filter and search functionality
  const filteredTalents = useMemo(() => {
    return talents.filter(talent => {
      const matchesSearch =
        talent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        talent.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        talent.role.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesRole =
        !roleFilter ||
        talent.role.toLowerCase().includes(roleFilter.toLowerCase());
      const matchesLocation =
        !locationFilter ||
        talent.location.toLowerCase().includes(locationFilter.toLowerCase());

      return matchesSearch && matchesRole && matchesLocation;
    });
  }, [searchQuery, roleFilter, locationFilter]);

  const handleActionClick = (talentId: number) => {
    setActionOpen(actionOpen === talentId ? null : talentId);
  };

  const clearFilters = () => {
    setRoleFilter('');
    setLocationFilter('');
  };

  return (
    <div>
      <div className="h-[550px] overflow-y-scroll bg-white px-4 py-6 sm:px-6 lg:px-8">
        {client.successfulHires === 0 ? (
          <NoTalentDisplay />
        ) : (
          <div>
            <div className="mb-6 flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
              <h2 className="text-lg  text-gray-900">
                Hired Talents:{' '}
                <span className="font-medium">{client.successfulHires}</span>
              </h2>

              <div className="flex flex-col items-stretch space-y-3 sm:flex-row sm:items-center sm:space-x-4 sm:space-y-0">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search"
                    value={searchQuery}
                    onChange={e => setSearchQuery(e.target.value)}
                    className="w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-transparent focus:ring-2 focus:ring-blue-500 sm:w-64"
                  />
                </div>

                {/* Filter */}
                <Popover.Root
                  open={filterOpen}
                  onOpenChange={open => setFilterOpen(open)}
                >
                  <Popover.Trigger asChild>
                    <button
                      onClick={() => setFilterOpen(!filterOpen)}
                      className="flex items-center space-x-2 rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50"
                    >
                      <Filter className="h-4 w-4" />
                      <span>Filter</span>
                    </button>
                  </Popover.Trigger>
                  <Popover.Portal>
                    <Popover.Content className="rounded-md bg-white p-2 shadow-md">
                      <div className="space-y-4 p-4">
                        <div>
                          <label className="mb-2 block text-sm font-medium text-gray-700">
                            Role
                          </label>
                          <input
                            type="text"
                            placeholder="Filter by role"
                            value={roleFilter}
                            onChange={e => setRoleFilter(e.target.value)}
                            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500"
                          />
                        </div>
                        <div>
                          <label className="mb-2 block text-sm font-medium text-gray-700">
                            Location
                          </label>
                          <input
                            type="text"
                            placeholder="Filter by location"
                            value={locationFilter}
                            onChange={e => setLocationFilter(e.target.value)}
                            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500"
                          />
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={clearFilters}
                            className="rounded border border-gray-300 px-3 py-1 text-sm hover:bg-gray-50"
                          >
                            Clear
                          </button>
                          <button
                            onClick={() => setFilterOpen(false)}
                            className="rounded bg-blue-600 px-3 py-1 text-sm text-white hover:bg-blue-700"
                          >
                            Apply
                          </button>
                        </div>
                      </div>
                    </Popover.Content>
                  </Popover.Portal>
                </Popover.Root>

                {/* View Toggle */}
                <div className="flex rounded-lg border border-gray-300">
                  <button
                    onClick={() => setViewType('list')}
                    className={`rounded-l-lg p-2 ${
                      viewType === 'list' ? 'bg-gray-100' : 'hover:bg-gray-50'
                    }`}
                  >
                    <List className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setViewType('grid')}
                    className={`rounded-r-lg border-l border-gray-300 p-2 ${
                      viewType === 'grid' ? 'bg-gray-100' : 'hover:bg-gray-50'
                    }`}
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>

            {/* Content */}
            {viewType === 'list' ? (
              /* Table View */
              <div className="overflow-hidden rounded-lg bg-white shadow">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          SN
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Email
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Role type
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Location
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Date hired
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                          Action
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {filteredTalents.map((talent, index) => (
                        <tr key={talent.id} className="hover:bg-gray-50">
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                            {index + 1}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4">
                            <div className="flex items-center">
                              <div className="mr-3 flex h-8 w-8 items-center justify-center rounded-full bg-green-500 text-xs font-medium text-white">
                                <Image
                                  width={300}
                                  height={300}
                                  src={talent?.avatar}
                                  alt={talent?.name}
                                  className="h-8 w-8 rounded-full object-cover"
                                />
                              </div>
                              <div className="text-sm font-medium text-gray-900">
                                {talent.name}
                              </div>
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                            {talent.email}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                            {talent.role}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                            {talent.location}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                            {talent.dateHired}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                            <Popover.Root
                              open={actionOpen === talent.id}
                              onOpenChange={open =>
                                setActionOpen(open ? talent.id : null)
                              }
                            >
                              <Popover.Trigger asChild>
                                <button
                                  onClick={() => handleActionClick(talent.id)}
                                  className="flex items-center  gap-4 rounded-full border border-gray-200 bg-[#EAE6FB] px-2 py-1 text-sm text-black hover:bg-primary-light"
                                >
                                  Action <MoreVertical size={16} />
                                </button>
                              </Popover.Trigger>
                              <Popover.Portal>
                                <Popover.Content className="rounded-md bg-white p-2 shadow-md">
                                  <div className="py-1">
                                    <div className="block w-full px-4 py-2 text-left text-sm hover:bg-gray-100">
                                      <ClientTalentView talent={talent} />
                                    </div>
                                    <button className="block w-full px-4 py-2 text-left text-sm hover:bg-gray-100">
                                      Edit
                                    </button>
                                    <button className="block w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-gray-100">
                                      Remove
                                    </button>
                                  </div>
                                </Popover.Content>
                              </Popover.Portal>
                            </Popover.Root>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            ) : (
              /* Grid View */
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {filteredTalents.map(talent => (
                  <TalentCard key={talent.id} talent={talent} />
                ))}
              </div>
            )}

            {/* Pagination */}
            <div className="mt-6 flex items-center justify-between">
              <button className="flex items-center px-4 py-2 text-sm text-gray-500 hover:text-gray-700">
                <ChevronLeft className="mr-1 h-4 w-4" />
                Previous
              </button>
              <button className="flex items-center px-4 py-2 text-sm text-gray-500 hover:text-gray-700">
                Next
                <ChevronRight className="ml-1 h-4 w-4" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TalentHistory;
