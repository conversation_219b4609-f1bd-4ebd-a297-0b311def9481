'use client';

import React from 'react';
import { <PERSON><PERSON>, Tab } from '@/components/shared';
import { useBooleanStateControl } from '@/hooks';
import { PlusIcon } from '../../jobs/misc/icons';
import { InitiateCreateAssessmentModal } from '../misc/components';
import CreateQuestionPackStep1SelectInterviewOrAssessment from '../misc/components/CreateQuestionPackStep1SelectInterviewOrAssessment';
import CreateQuestionPackModal from '../misc/components/CreateQuestionPackStep2SelectTypeModal';
import {
  Candidates,
  Interviews,
  MyAssessments,
  TestsLibrary,
} from '../misc/sections';

const RecruiterAssessmentandInterviewView = ({
  params,
}: {
  params: { section: string };
}) => {
  const { section } = params;
  const tabsArray = [
    {
      id: 1,
      title: 'My Assessments',
      link: './my-assessments',
      component: <MyAssessments />,
    },
    {
      id: 2,
      title: 'Candidates',
      link: './candidates',
      component: <Candidates />,
    },
    {
      id: 3,
      title: 'Tests Library',
      link: './tests-library',
      component: <TestsLibrary />,
    },
    {
      id: 4,
      title: 'Interviews',
      link: './interviews-library',
      component: <Interviews />,
    },
  ];

  const {
    state: isCreateAssessmentModalOpen,
    setTrue: openCreateAssessmentModal,
    setFalse: closeCreateAssessmentModal,
  } = useBooleanStateControl();

  const {
    state: isCreateQuestionPackModalOpen,
    setTrue: openCreatePackModal,
    setFalse: closeCreateQuestionPackModal,
  } = useBooleanStateControl();

  return (
    <>
      <Tab
        fallback="my-assessments"
        listClass=""
        className="p-2"
        currentTab={section}
        catgoryArray={tabsArray}
        sideButton={
          <div className="flex items-center gap-2">
            <Button
              onClick={openCreatePackModal}
              type="button"
              variant="outlined"
            >
              Create pack
            </Button>
            <Button
              onClick={openCreateAssessmentModal}
              icon={
                <PlusIcon
                  width={20}
                  height={20}
                  className="rounded-full border-[1.5px] border-dashed border-white p-[1px]"
                />
              }
              variant="default"
              className="px-5"
            >
              Create new assessment
            </Button>
          </div>
        }
      />
      <CreateQuestionPackStep1SelectInterviewOrAssessment
        closeCreateQuestionPackStep1SelectInterviewOrAssessment={
          closeCreateQuestionPackModal
        }
        isCreateQuestionPackStep1SelectInterviewOrAssessmentOpen={
          isCreateQuestionPackModalOpen
        }
      />
      <InitiateCreateAssessmentModal
        isCreateAssessmentModalOpen={isCreateAssessmentModalOpen}
        closeCreateAssessmentModal={closeCreateAssessmentModal}
      />
    </>
  );
};

export default RecruiterAssessmentandInterviewView;
