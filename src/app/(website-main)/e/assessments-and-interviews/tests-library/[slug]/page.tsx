"use client";

import Link from "next/link";
import Button from "@/components/shared/Button";
import { Button as AltButton } from "@/components/shared"
import { FormEvent, useCallback, useEffect, useState } from "react";
import QuestionTypeModal from "../../misc/components/QuestionTypeModal";
import AddCustomQuestionModal from "../../misc/components/AddCustomQuestionModal";
import app_fetch from "@/lib/api/appFetch";
import { QUESTION_OPTIONS, QUESTION_TYPES_DICTIONARY } from "../../misc/constants/constants";
import Modal from "@/components/Modal";
import QuestionPackSuccessIcon from "@/components/icons/QuestionPackSuccessIcon";
import { useParams } from "next/navigation";
import { CreateQuestion, Question, QuestionPack } from "../../misc/types/create-assessments";
import { LinkButton, Loader } from "@/components/shared";
import { formatBytes } from "@/lib/utils/functions";
import { useDropzone } from "react-dropzone";
import UploadIcon from "@/app/(website-main)/t/showcase/misc/components/cv-upload/icons/UploadIcon";
import { Dialog } from "@headlessui/react";
import EditQuestionModal from "../../misc/components/EditQuestionModal";
import { useRouter } from "next/navigation";
import { useWindowWidth } from "@/hooks";
import { EditPenToSquare, Elipsis } from "@/components/shared/icons";
import { convertToTitleCase } from "@/utils/strings";
import { cn } from "@/utils";
import DropdownMenu from "@/components/DropdownMenu";

export default function CreatePack({ searchParams }: { searchParams: { redirect?: string, } }) {
  const router = useRouter();
  const windowWidth = useWindowWidth()

  const [active_question_index, setActiveQuestionIndex] = useState(0);
  const [is_question_type_modal_open, setIsQuestionTypeModalOpen] = useState(
    false,
  );
  function openQuestionTypeModal() {
    setIsQuestionTypeModalOpen(true);
  }
  function closeQuestionTypeModal() {
    setIsQuestionTypeModalOpen(false);
  }
  const [question_type, setQuestionType] = useState<string | undefined>(undefined);
  const [is_create_custom_question_modal_open, setIsCreateCustomQuestionModalOpen,] = useState(false);
  function openCreateCustomQuestionModal() {
    setIsCreateCustomQuestionModalOpen(true);
  }
  function closeCreateCustomQuestionModal() {
    setIsCreateCustomQuestionModalOpen(false);
  }

  const [question_pack, setQuestionPack] = useState({} as QuestionPack);

  const params = useParams();

  function getPageState() {
    switch (params.slug) {
      case "create-pack":
        return "create-pack"
      case "my questions":
        return "my questions"
      default:
        return "detail"
    };
  }

  const page_state = getPageState();

  type Mode = "read" | "write"
  const [mode, setMode] = useState<Mode>("read")

  const [is_loading_question_pack, setIsLoadingQuestionPack] = useState(false);
  useEffect(() => {
    switch (page_state) {
      case "create-pack":
        setMode("write")
        openQuestionPackDescriptionModal();
        break
      case "my questions":
        break
      case "detail":
        setIsLoadingQuestionPack(true)
        const url = `assessments/question-packs/${params.slug}`;
        app_fetch(url).then(response => response.json()).then((result) => {
          setQuestionPack(result)
          setIsLoadingQuestionPack(false)
        })
    }
  }, [params.slug])


  const [file_details, setFileDetails] = useState(undefined as any);
  function clearFile() {
    setUploadError("")
    setFileDetails(undefined)
  }
  const [is_uploading_file, setIsUploadingFile] = useState(false);
  const [upload_error, setUploadError] = useState("")
  const [is_bulk_upload_modal_open, setIsBulkUploadModalOpen] = useState(false);
  function openBulkUploadModal() {
    setIsBulkUploadModalOpen(true);
  }
  function closeBulkUploadModal() {
    setIsBulkUploadModalOpen(false);
    setFileDetails(undefined)
  }

  const onDrop = useCallback((acceptedFiles: any) => {
    setFileDetails(acceptedFiles[0]);
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [],
      "application/vnd.ms-excel": [],
    },
    maxSize: 10 * 1024 * 1024, //10mb
  });

  function submitBulkUpload() {

    const formdata = new FormData();
    formdata.append("file", file_details);

    const requestOptions = {
      method: "POST",
      body: formdata,
    };

    setIsUploadingFile(true)
    app_fetch(`assessments/question-packs/${question_pack.id}/upload-questions/`, requestOptions, false)
      .then((response) => response.json())
      .then((result) => {
        if (result.error) { // the error for this endpoint seems to be here
          setUploadError(result.error)
        }
        else {
          setQuestionPack({ ...question_pack, question_set: [...question_pack.question_set, ...result.questions] })
          closeBulkUploadModal();
        }
      })
      .catch(e => {
        console.log(e)
        setUploadError(e.error)
      }).finally(() => {
        setIsUploadingFile(false)
      })
  }

  const [is_question_pack_description_modal_open, setIsQuestionPackDescriptionModalOpen] = useState(false);
  function closeQuestionPackDescriptionModal() {
    setIsQuestionPackDescriptionModalOpen(false);
  }
  function openQuestionPackDescriptionModal() {
    setIsQuestionPackDescriptionModalOpen(true);
  }
  function exitQuestionPackCreation() {
    router.push(searchParams.redirect || "/e/assessments-and-interviews/tests-library/")
  }

  const [form_data, setFormData] = useState({} as QuestionPack);
  const [is_saving_question_pack_description, setIsSavingQuestionPackDescription] = useState(false);

  function saveQuestionPackDescription(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();

    const options = {
      method: "POST",
      body: JSON.stringify(form_data),
    };

    setIsSavingQuestionPackDescription(true);
    app_fetch("assessments/question-packs/", options).then((response) =>
      response.json()
    ).then((result) => {
      setQuestionPack(result);
      closeQuestionPackDescriptionModal();
    }).finally(() => {
      setIsSavingQuestionPackDescription(false);
    });
  }

  function saveEditedQuestionPackDescription(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();

    const options = {
      method: "PATCH",
      body: JSON.stringify(form_data),
    };
    setIsSavingQuestionPackDescription(true);
    app_fetch(`assessments/question-packs/${question_pack?.id}/`, options).then((response) =>
      response.json()
    ).then((result) => {
      setQuestionPack({ ...question_pack, name: form_data.name, description: form_data.description });
      closePackDetailsModal()
    }).finally(() => {
      setIsSavingQuestionPackDescription(false);
    });
  }


  const [question_set, setQuestionSet] = useState(Array(10).fill(undefined) as Question[]);

  // function createCustomQuestion(question: CreateQuestion) {

  //   question.section = question.category

  //   const _form_data = new FormData()

  //   const data = question
  //   //
  //   // @ts-ignore
  //   delete question.category

  //   Object.entries(data).map(([key, val])=>{
  //     if (key==="images"){
  //       const _val = val as unknown as  File[]
  //       _val.map((val)=>{
  //         _form_data.append(key, val, `${val.name}.${val.type.split("/")[1]}`)
  //       })
  //       return
  //     }
  //     //@ts-ignore
  //     _form_data.set(key, val)
  //   })


  //   const options = {
  //     method: "POST",
  //     body: _form_data
  //   };

  //   app_fetch(
  //     `assessments/question-packs/${question_pack.id}/create-question/`,
  //     options, false
  //   ).then((response) => response.json()).then((result) => {
  //       const _question_set = [...question_set];
  //       _question_set[active_question_index] = result;
  //       setQuestionSet(_question_set);
  //       setQuestionPack({
  //         ...question_pack,
  //         question_set: [...question_pack.question_set, result],
  //       });
  //     });
  // }

  function createCustomQuestion(question: CreateQuestion) {
    // Ensure section is set correctly
    const data = {
      ...question,
      section: question.section || question.category, // Set `section` directly, with a fallback
    };
    const formData = new FormData();

    // Remove `category` if it's only used for assigning `section`
    //@ts-ignore
    delete data.category;

    Object.entries(data).forEach(([key, val]) => {
      if (key === "answer_options") {
        // Handle answer_options array separately
        (val as string[]).forEach((option, index) => {
          formData.append(`${key}[${index}]`, option);
        });
      } else if (key === "images") {
        const _val = val as File[];
        _val.forEach((file) => {
          formData.append(key, file, `${file.name}.${file.type.split("/")[1]}`);
        });
      } else {
        formData.append(key, val as string); // Use append for compatibility
      }
    });

    const options = {
      method: "POST",
      body: formData,
    };

    app_fetch(
      `assessments/question-packs/${question_pack.id}/create-question/`,
      options,
      false
    )
      .then((response) => response.json())
      .then((result) => {
        const questionSet = [...question_set];
        questionSet[active_question_index] = result;
        setQuestionSet(questionSet);
        setQuestionPack({
          ...question_pack,
          question_set: [...question_pack.question_set, result],
        });
      });
  }


  const [is_view_question_modal_open, setIsViewQuestionModalOpen] = useState(false)
  function openViewQuestionModal() {
    setIsViewQuestionModalOpen(true)
  }
  function closeViewQuestionModal() {
    setIsViewQuestionModalOpen(false)
  }

  const [is_edit_question_modal_open, setIsEditQuestionModalOpen] = useState(false)
  function openEditQuestionModal() {
    setIsEditQuestionModalOpen(true)
  }
  function closeEditQuestionModal() {
    setIsEditQuestionModalOpen(false)
  }

  const [is_editing_question, setIsEditingQuestion] = useState(false)
  function editQuestion(edited_question: Question) {

    setIsEditingQuestion(true)


    const _form_data = new FormData()

    const data = edited_question

    Object.entries(data).map(([key, val]) => {
      if (key === "images") {
        const _val = val as unknown as File[]
        _val.map((val) => {
          _form_data.append(key, val, `${val.name}.${val.type.split("/")[1]}`)
        })
        return
      }
      //@ts-ignore
      _form_data.set(key, val)
    })

    app_fetch(`assessments/question-packs/${question_pack.id}/edit-question/${edited_question.id}/`, {
      method: "PATCH", body: _form_data
    }, false).then(() => {
      // @ts-ignore
      question_pack.question_set[active_question_index] = edited_question
      closeEditQuestionModal();
    }).finally(() => {
      setIsEditingQuestion(false)
    })
  }



  function handleQuestionOption(_selected_option: typeof QUESTION_OPTIONS[0], question: Question) {
    switch (_selected_option.title) {
      case "delete":
        app_fetch(
          `assessments/question-packs/${question_pack.id}/delete-question/${question.id}/`,
          {
            method: "DELETE",
          },
        ).then(() => {
          const new_question_set = question_pack.question_set;
          // @ts-ignore
          new_question_set[active_question_index] = undefined;
          setQuestionPack({ ...question_pack, question_set: new_question_set })
          /* setQuestionSet(new_question_set); */
        });
        break;
      case "edit":
        openEditQuestionModal()
        break;
      case "view":
        openViewQuestionModal()
        break;
      default:
        break;
    }
  }

  const [is_success_modal_open, setIsSuccessModalOpen] = useState(false);
  function openSuccessModal() {
    setIsSuccessModalOpen(true);
  }

  function closeSuccessModal() {
    setIsSuccessModalOpen(false);
  }


  const [is_pack_details_modal_open, setIsPackDetailsModalOpen] = useState(false);
  function openPackDetailsModal() {
    // @ts-ignore
    setFormData({ name: question_pack.name, description: question_pack.description })
    setIsPackDetailsModalOpen(true);
  }

  function closePackDetailsModal() {
    setIsPackDetailsModalOpen(false);
  }


  return (
    <div className="space-y-4 pb-2">
      <section className="mt-2 flex max-md:flex-col gap-2 items-center justify-between bg-white px-2 py-4">
        <div className="flex items-center gap-4">
          <LinkButton
            href="/e/assessments-and-interviews/tests-library"
            className="btn-primary-light-pill"
            size="capsule"
            variant="extralight"
          >
            Back
          </LinkButton>
          <div>
            <div className="flex items-center gap-2">
              <h2 className="text-header-text font-semibold">{question_pack?.name}</h2>
              <button className="flex items-center gap-1 underline text-xs text-body-text" title="Edit pack name or description" onClick={openPackDetailsModal}> Edit <EditPenToSquare width={15} height={15} /></button>
            </div>
            <p className="max-w-[45ch] text-xs text-body-text truncate overflow-clip" title={question_pack?.description}> {question_pack?.description} </p>
          </div>
        </div>
        {
          mode == "write" ? (
            <div className="flex gap-2">
              <AltButton
                disabled={false}
                className=""
                onClick={openBulkUploadModal}
                variant="extralight"
                size="small"
              >
                Bulk Upload Question
              </AltButton>
              <AltButton
                disabled={false}
                className=""
                onClick={openSuccessModal}
                size="small"
              >
                Save pack
              </AltButton>
            </div>
          ) : (
            <div className="flex gap-2">
              <AltButton
                disabled={false}
                className=""
                onClick={() => {
                  setMode("write")
                }}
                size="thin"
              >
                Edit
              </AltButton>
            </div>
          )}
      </section>

      <p className="px-6 text-sm text-header-text">
        {
          mode == "write" ?
            "Tap ‘add question’ to add a new question to the pack"
            :
            "Click on the edit button to make changes"
        }
      </p>

      {is_loading_question_pack ? (<Loader />) : (
        <>
          {
            (question_pack?.question_set?.length == 0 && mode == "read") ?
              (
                <div className="grid min-h-[80vh] flex-1 place-content-center">
                  <div className="rounded-md bg-gradient-to-b from-[#755ae20a] via-transparent to-transparent p-12 border border-[#DADADA]/20 text-center">
                    <div className="container max-w-xs space-y-2">
                      <div className="container flex justify-center">
                        <img
                          src="/images/create-assessments/no-assessment.png"
                          alt="writing with pen"
                        />
                      </div>
                      <h2>This pack currently has no questions</h2>
                      <p className="text-sm text-[#7D8590]">Click on the edit button to add new questions</p>
                      <button
                        onClick={() => setMode("write")}
                        className="btn-primary inline-flex gap-2"
                        type="button"
                      >Edit Pack</button>
                    </div>
                  </div>
                </div>
              )
              :
              (

                <ul className={cn("grid gap-3 px-5", question_pack.question_set?.length < 4 ? "grid-cols-[repeat(auto-fit,minmax(250px,1fr))]" : "grid-cols-[repeat(auto-fill,minmax(250px,300px))]")}>
                  {
                    question_pack.question_set?.map((question, index) => (
                      <>
                        {
                          question &&
                          <li
                            key={index}
                            className="rounded-xl outline-primary hover:outline bg-white min-h-[150px]"
                          >
                            <article className="rounded-lg bg-white p-4">
                              <header className="item-center flex justify-between">
                                <h2 className="text-base text-header-text font-medium">Question {index + 1}</h2>
                                <DropdownMenu
                                  options={QUESTION_OPTIONS}
                                  readable_text_key="title"
                                  callback={(_selected_option) => {
                                    setActiveQuestionIndex(index);
                                    handleQuestionOption(_selected_option, question);
                                  }}
                                  button={
                                    <div
                                      className="px-2 cursor-pointer"
                                      title="view options"
                                    >
                                      <Elipsis fill="#D9D9D9" />
                                    </div>
                                  }
                                />
                              </header>

                              <p className="text-sm mt-3 mb-2 font-normal">
                                <span className="text-helper-text">
                                  Question type:
                                </span>{" "}
                                <span className="text-header-text font-medium">
                                  {convertToTitleCase(QUESTION_TYPES_DICTIONARY[question?.type]?.name || "")}
                                </span>
                              </p>

                              <p className="text-xs text-body-text leading-snug">{question.question}</p>
                            </article>
                          </li>
                        }
                      </>

                    ))
                  }

                  {
                    mode == "write" && (
                      <li
                        className="flex items-center min-h-[150px] justify-center rounded-xl outline-primary hover:outline bg-white"
                      >
                        <button
                          className="grid  w-full rounded-xl place-items-center"
                          type="button"
                          onClick={() => {
                            /* setActiveQuestionIndex(index); */
                            openQuestionTypeModal();
                          }}
                        >
                          <div className="space-y-2">
                            <p className="text-header-text font-medium">
                              Question {question_pack.question_set?.length + 1 || 1}
                            </p>
                            <p className="btn-primary-light text-xs font-normal">Add question</p>
                          </div>
                        </button>
                      </li>
                    )
                  }
                </ul>
              )
          }
        </>
      )}



      <QuestionTypeModal
        is_open={is_question_type_modal_open}
        close={closeQuestionTypeModal}
        callback={(_question_type) => {
          setQuestionType(_question_type);
          closeQuestionTypeModal();
          openCreateCustomQuestionModal();
        }}
      />
      <AddCustomQuestionModal
        is_open={is_create_custom_question_modal_open}
        close={closeCreateCustomQuestionModal}
        question_type={question_type}
        handleSubmit={createCustomQuestion}
      />
      <Modal
        is_open={is_question_pack_description_modal_open}
        title="Create question pack"
        close={exitQuestionPackCreation}
        is_close_by_button_only={true}
      >
        <div className="">
          <form
            className="md:w-[547px] py-4  bg-[#F5F3FF] text-center text-[#675E8B] bg-light-accent-bg max-w-[450px] space-y-4 p-4 text-sm"
            onSubmit={(e) => {
              saveQuestionPackDescription(e);
            }}
          >
            <div>
              <label className="space-y-1 text-left">
                <p className="heading-text font-medium">Name of pack</p>
                <input
                  value={form_data.name}
                  onChange={(e) =>
                    setFormData({ ...form_data, name: e.target.value })}
                  required
                  className="input-white"
                  type="text"
                  name="name of pack"
                  placeholder="Enter pack name"
                />
              </label>
            </div>
            <div>
              <label className="space-y-1 text-left">
                <p className="heading-text font-medium">Description</p>
                <textarea
                  value={form_data.description}
                  onChange={(e) =>
                    setFormData({ ...form_data, description: e.target.value })}
                  required
                  className="input-white min-h-[150px]"
                  name="Pack description"
                  placeholder="Enter pack description"
                />
              </label>
            </div>
            <div className="flex items-center gap-2">
              <Button type="button" onClick={exitQuestionPackCreation} className="btn-primary-bordered w-full">
                Cancel
              </Button>
              <Button is_busy={is_saving_question_pack_description} type="submit" className="btn-primary w-full">
                Proceed
              </Button>
            </div>
          </form>
        </div>
      </Modal>
      <Modal
        is_open={is_success_modal_open}
        title="Success"
        close={closeSuccessModal}
      >
        <div className="md:w-[547px] space-y-2 bg-[#F5F3FF] text-center p-4 text-sm text-[#675E8B]">
          <div className="flex flex-col gap-2 items-center p-4 md:px-12">
            <QuestionPackSuccessIcon />
            <h2 className="heading-2 text-primary">
              Question pack saved successfully
            </h2>
            <p>
              Your '{question_pack.name}' question pack has been saved
              successfully
            </p>
          </div>
        </div>
        <div className="rounded-xl bg-white p-4">
          <div className="flex items-center justify-end gap-2">
            {
              page_state == "create-pack" ? (
                <Link
                  className="btn-primary-light"
                  href={`/e/assessments-and-interviews/tests-library/${question_pack.id}`}
                  onClick={closeSuccessModal}
                >
                  View pack
                </Link>
              ) : (
                <button
                  className="btn-primary-light"
                  onClick={() => {
                    closeSuccessModal();
                    setMode("read")
                  }}
                >
                  View pack
                </button>
              )
            }

            <Link
              className="btn-primary"
              href={searchParams.redirect || `/e/assessments-and-interviews/tests-library?tab=my_question_packs`}
            >
              Done
            </Link>
          </div>
        </div>
      </Modal>

      <Modal
        is_open={is_bulk_upload_modal_open}
        title="Upload questions"
        close={closeBulkUploadModal}
      >
        <div className="w-[490px] p-4 space-y-4 text-sm">
          <h2 className="heading-2">Bulk upload custom questions</h2>
          <p className="helper-text ">
            Please download the <span className="font-bold text-black "> 'sample file' </span>
            provided below and populate the <span className="font-bold text-black ">EXCEL</span> file by entering the necessary information in the required fields. Once completed, upload the file to have your custom questions on getlinked.
          </p>
          <div className="flex gap-2">
            <a
              className="btn-primary-light"
              href="/files/assessment-and-interviews/getlinked_sample_questions_template.xlsx"
              target="_blank"
            >
              Download sample file
            </a>
            <Link href="#" className="btn-primary-transparent">
              Learn more
            </Link>
          </div>

          {!file_details && (
            <div
              className={`${false ? "border border-red-600" : ""} mt-3 flex h-[5.9375rem] max-w-[90%] cursor-pointer items-center gap-4 rounded-[0.875rem] border-[0.3px] border-dashed border-[rgb(117,90,226)] p-6`}
              {...getRootProps()}
            >
              <div className="">
                <UploadIcon />
              </div>
              <div className="">
                <p className="font-sans text-xs font-semibold text-[#755AE2] sm:text-sm">
                  Tap to upload invite document
                </p>
                <span className="font-sans text-xs font-semibold text-[#755AE2] opacity-75">
                  Files types: excel, Max size: 10MB
                </span>
              </div>
              <input hidden {...getInputProps()} />
            </div>
          )}

          {file_details && (
            <>
              <div className="p-4 max-w-[60%] rounded-md bg-light-accent-bg text-xs">
                <div className="flex items-start justify-between">
                  <div className="flex gap-2 items-center">
                    <div>
                      <img
                        src="/images/icons/excel.png"
                        alt="excel icon"
                      />
                    </div>
                    <div>
                      <p className="font-bold">
                        {file_details?.name}
                      </p>
                      <p className="text-[#4E4E4E]">
                        {formatBytes(file_details?.size)}
                      </p>
                    </div>
                  </div>
                  <button onClick={clearFile}>
                    <span className="bg-white rounded-full w-7 aspect-square flex items-center justify-center text-lg">
                      &times;
                    </span>
                  </button>
                </div>
              </div>
              {upload_error && (
                <p className="text-red-500">{upload_error}</p>
              )}
            </>
          )}
          <Button
            is_busy={is_uploading_file}
            disabled={!file_details}
            onClick={submitBulkUpload}
            className={`px-20 ${file_details ? 'btn-primary' : 'btn-primary-bordered'}`}
            type="button"
          >
            Upload file
          </Button>
        </div>
      </Modal>




      <Dialog open={is_view_question_modal_open} onClose={closeViewQuestionModal} className="relative z-50">
        {
          (active_question_index !== undefined && question_pack?.question_set?.[active_question_index]) && (
            <>
              <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

              <div className="fixed inset-0 w-screen overflow-y-auto">
                <div className="flex min-h-full items-start p-4">
                  <Dialog.Panel className="Zg:min-w-[450px] max-w-1g ml-auto overflow-hidden rounded-[1.25rem] bg-white sm:min-w-[300px]">
                    <header className="flex items-center justify-between gap-4 bg-primary p-8 pb-4 pt-6 text-white">
                      <Dialog.Title className="font-medium">View question</Dialog.Title>
                      <button
                        onClick={closeViewQuestionModal}
                        className="rounded-xl bg-white bg-opacity-20 px-5 py-2 text-xs"
                      >
                        Close
                      </button>
                    </header>

                    <div className="md:w-[526px] space-y-4 p-4 text-sm">
                      <div className="grid grid-cols-3 gap-4 items-center font-medium">
                        <div>
                          <h3 className="text-body-text">Type:</h3>
                          <p className="heading-text capitalize">{QUESTION_TYPES_DICTIONARY[question_pack.question_set[active_question_index].type].name}</p>
                        </div>
                        <div>
                          <h3 className="text-body-text">No:</h3>
                          <p className="heading-text">Question {active_question_index + 1}</p>
                        </div>
                      </div>
                      <div>
                        <h3 className="heading-text">Question</h3>
                        <p>{question_pack.question_set[active_question_index].question}</p>
                      </div>
                      {
                        (question_pack.question_set[active_question_index].answer_options?.length > 1) && (
                          <>
                            <div>
                              <h3 className="heading-text">Answer options</h3>
                              {!question_pack.question_set[active_question_index].answer_options && (
                                <p>
                                  "-"
                                </p>
                              )}
                              <ul className="list-disc pl-4">
                                {
                                  question_pack.question_set[active_question_index].answer_options?.map((option, index) => (
                                    <li key={index}>{option}</li>
                                  ))
                                }
                              </ul>
                            </div>
                          </>
                        )
                      }
                      {
                        (question_pack.question_set[active_question_index].answer?.length > 1) ? (

                          <>
                            <div>
                              <h3 className="heading-text">Answers</h3>
                              {!question_pack.question_set[active_question_index].answer && (
                                <p>
                                  "-"
                                </p>
                              )}
                              <ul className="list-disc pl-4">
                                {
                                  // @ts-ignore - answer is a list of strings, that is how it is being saved in the backend unlike what we've been using in the frontend
                                  question_pack.question_set[active_question_index].answer?.map((answer: string, index: number) => (
                                    <li key={index}>{answer}</li>
                                  ))
                                }
                              </ul>
                            </div>
                          </>
                        ) : (
                          <div>
                            <h3 className="heading-text">Answer</h3>
                            <p>{question_pack.question_set[active_question_index].answer || "-"}</p>
                          </div>
                        )
                      }
                      <div className="flex justify-end gap-2 items-center">
                        <button className="btn-primary-light" onClick={() => { openEditQuestionModal(); closeViewQuestionModal() }}>Edit question</button>
                        <button className="btn-primary" onClick={() => { setActiveQuestionIndex((active_question_index + 1) % question_pack.question_set.length) }}>
                          Next question
                        </button>
                      </div>
                    </div>
                  </Dialog.Panel>

                </div>
              </div>
            </>
          )
        }
      </Dialog>

      <EditQuestionModal is_busy={is_editing_question} is_open={is_edit_question_modal_open} close={closeEditQuestionModal} onSave={editQuestion} question={question_pack.question_set?.[active_question_index]} />

      {/* edited question pack details */}
      <Modal
        is_open={is_pack_details_modal_open}
        title="Edit question pack details"
        close={closePackDetailsModal}
      >
        <div className="">
          <form
            className="md:w-[547px] py-4  bg-[#F5F3FF] text-center text-[#675E8B] bg-light-accent-bg max-w-[450px] space-y-4 p-4 text-sm"
            onSubmit={(e) => {
              saveEditedQuestionPackDescription(e);
            }}
          >
            <div>
              <label className="space-y-1 text-left">
                <p className="heading-text font-medium">Name of pack</p>
                <input
                  value={form_data.name}
                  onChange={(e) =>
                    setFormData({ ...form_data, name: e.target.value })}
                  required
                  className="input-white"
                  type="text"
                  name="name of pack"
                  placeholder="Enter pack name"
                />
              </label>
            </div>
            <div>
              <label className="space-y-1 text-left">
                <p className="heading-text font-medium">Description</p>
                <textarea
                  value={form_data.description}
                  onChange={(e) =>
                    setFormData({ ...form_data, description: e.target.value })}
                  required
                  className="input-white min-h-[150px]"
                  name="Pack description"
                  placeholder="Enter pack description"
                />
              </label>
            </div>
            <div className="flex items-center gap-2">
              <Button type="button" onClick={closePackDetailsModal} className="btn-primary-bordered w-full">
                Cancel
              </Button>
              <Button is_busy={is_saving_question_pack_description} type="submit" className="btn-primary w-full">
                Proceed
              </Button>
            </div>
          </form>
        </div>
      </Modal>
    </div>
  );
}
