'use client';

import { usePara<PERSON>, useRouter } from 'next/navigation';
import React from 'react';
import toast from 'react-hot-toast';
import { cn } from '@/utils';
import { UseGetAssessmentDetails } from '../misc/api';
import AssessmentCreatorStepContent from '../misc/components/AssessmentCreatorContent';
import AssessmentCreatorStepHeader from '../misc/components/AssessmentCreatorHeader';
import AssessmentCreatorValidationProvider, {
  useCreateAssessmentValidation,
} from '../misc/contexts';
import useAssessmentCreatorStore from '../misc/store';

const AssessmentCreator = () => {
  const updateStore = useAssessmentCreatorStore(state => state.updateStore);
  const step = useAssessmentCreatorStore(state => state.step);

  const router = useRouter();
  const params = useParams();
  const assessmentId = params.id;

  //// Gets id from params, then fetches assessment details
  //// Sets the store so details can be used at any point
  //// Refetches and ee-sets store if details change
  const { data, isLoading } = UseGetAssessmentDetails(assessmentId as string);
  React.useEffect(() => {
    if (data && !isLoading) {
      updateStore(data);
    } else if (!isLoading && !data) {
      toast.error('Assessment not found');
      router.push('/e/assessments-and-interviews/my-assessments');
    }
  }, [data, isLoading]);

  return (
    //// Validation provider wraps the content of the creator
    //// This allows for validation to be used even outside the steps
    //// So to move from step 2 to step 3, a validation can be run
    <AssessmentCreatorValidationProvider>
      <div
        className={cn(
          'relative grid h-full grid-rows-[max-content,1fr] gap-2.5 overflow-hidden rounded-[1.25rem] px-2',
          step < 4 ? '' : ''
        )}
      >
        <AssessmentCreatorStepHeader />
        <AssessmentCreatorStepContent step={step} />
      </div>
    </AssessmentCreatorValidationProvider>
  );
};

export default AssessmentCreator;
