import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import {
  TAssessment,
  TAssessmentQuestionset,
  TAssessmentSection,
  TBulkToleranceSetup,
  TProctoringOptions,
} from '../types';

const initialState = {
  step: 1,
  id: '',
  sections: [],
  interview_questions: [],
  feedbacks: [],
  name: '',
  description: '',
  type: '',
  intro_video_url: '',
  role_level: '',
  support_email: null,
  time: '',
  is_published: false,
  commencement_settings: false,
  deadline: new Date().toISOString(),
  start_time: new Date().toISOString(),
  time_limit: 0,
  deadline_reminder: false,
  reminder_time: 0,
  total_questions: 0,
  no_candidates: 0,
  completed: [],
  paused: [],
  ongoing: [],
  not_started: [],
  invigilate_assessment: false,
  is_stop_screen_sharing: false,
  full_screen_tolerance_level: 0,
  window_change_tolerance_level: 0,
  tab_change_tolerance_level: 0,
  multi_face_tolerance_level: 0,
  different_face_tolerance_level: 0,
  is_bulk_tolerance: false,
  bulk_tolerance_setup: null,
  is_shuffle: false,
  is_shuffle_sections: false,
  is_webcam_snapshot: false,
  is_restrict_copying: false,
  is_restrict_tab_change: false,
  is_identity_verification: false,
  is_track_paste: false,
  is_custom: false,
  last_page_url: '1',
  reminder_sent: false,
  created_at: '',
  updated_at: '',
  role: { id: 0, name: '', use_ai: false },
  creator: {
    id: 0,
    company_email: '',
    verified: false,
    type_of_recruiter: '',
    profile_picture: null,
    role: '',
    created_at: '',
    updated_at: '',
    user: '',
    company: 0,
  },
  assessment_team_members: [],
};

interface AssessmentCreatorStore extends TAssessment {
  step: number;
  setStep: (step: number) => void;
  setId: (id: string) => void;
  setName: (name: string) => void;
  setDescription: (description: string) => void;
  setRoleLevel: (roleLevel: string) => void;
  setTime: (time: string) => void;
  setTimeLimit: (timeLimit: number) => void;
  setIsPublished: (isPublished: boolean) => void;
  setSections: (sections: TAssessmentSection[]) => void;
  addSection: (section: TAssessmentSection) => void;
  updateSection: (
    sectionId: string,
    updatedSection: Partial<TAssessmentSection>
  ) => void;
  removeSection: (sectionId: string) => void;
  addQuestionToSection: (
    sectionId: string,
    question: TAssessmentQuestionset
  ) => void;
  updateQuestionInSection: (
    sectionId: string,
    questionId: string,
    updatedQuestion: Partial<TAssessmentQuestionset>
  ) => void;
  removeQuestionFromSection: (sectionId: string, questionId: string) => void;
  updateStore: (updatedStore: Partial<AssessmentCreatorStore>) => void;
  clearStore: () => void;
  updateProctoringOption: (option: TProctoringOptions, value: boolean) => void;
  updateToleranceLevel: (option: string, value: number) => void;
  updateBulkToleranceSetup: (setup: TBulkToleranceSetup) => void;
}

export const useAssessmentCreatorStore = create<AssessmentCreatorStore>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,
        setStep: step => set({ step }),
        setId: id => set({ id }),
        setName: name => set({ name }),
        setDescription: description => set({ description }),
        setRoleLevel: role_level => set({ role_level }),
        setTime: time => set({ time }),
        setTimeLimit: time_limit => set({ time_limit }),
        setIsPublished: is_published => set({ is_published }),

        setSections: sections => set({ sections }),
        addSection: section =>
          set(state => ({ sections: [...state.sections, section] })),
        updateSection: (sectionId, updatedSection) =>
          set(state => ({
            sections: state.sections.map(section =>
              section.id === sectionId
                ? { ...section, ...updatedSection }
                : section
            ),
          })),
        removeSection: sectionId =>
          set(state => ({
            sections: state.sections.filter(
              section => section.id !== sectionId
            ),
          })),

        addQuestionToSection: (sectionId, question) =>
          set(state => ({
            sections: state.sections.map(section =>
              section.id === sectionId
                ? {
                    ...section,
                    question_set: [...section.question_set, question],
                  }
                : section
            ),
          })),
        updateQuestionInSection: (sectionId, questionId, updatedQuestion) =>
          set(state => ({
            sections: state.sections.map(section =>
              section.id === sectionId
                ? {
                    ...section,
                    question_set: section.question_set.map(question =>
                      question.id === questionId
                        ? { ...question, ...updatedQuestion }
                        : question
                    ),
                  }
                : section
            ),
          })),
        removeQuestionFromSection: (sectionId, questionId) =>
          set(state => ({
            sections: state.sections.map(section =>
              section.id === sectionId
                ? {
                    ...section,
                    question_set: section.question_set.filter(
                      question => question.id !== questionId
                    ),
                  }
                : section
            ),
          })),
        updateProctoringOption: (option, value) =>
          set(state => ({ [option]: value })),
        updateToleranceLevel: (option, value) =>
          set(state => ({ [option]: value })),
        updateBulkToleranceSetup: setup =>
          set({ bulk_tolerance_setup: setup, is_bulk_tolerance: true }),

        updateStore: updatedStore => set(updatedStore),
        clearStore: () => set(initialState),
      }),
      {
        name: 'getlinked_ai_assessment_creation_store',
        getStorage: () => sessionStorage,
      }
    )
  )
);
// export const useAssessmentCreatorStore = create<AssessmentCreatorStore>((set) => ({

export default useAssessmentCreatorStore;
