interface htmlToTokenSProps {
  html_string: string;
  auth_type?: "email" | "file";
  cover_image: string;
}


export async function htmlToTokens(data: htmlToTokenSProps) {
  const { html_string, auth_type, cover_image } = data;

  const parser = new DOMParser();
  const dom = parser.parseFromString(html_string, "text/html");

  const base64CoverImage = cover_image ?? "https://res.cloudinary.com/dvdnjlxa8/image/upload/v1696419285/Group_40747_thpo8x.png"
  const EMAIL_TEMPLATE_WITH_AUTH = CUSTOM_EMAIL_TEMPLATE(base64CoverImage).replace("{{#auth#}}", (auth_type || "email") == "email" ? CUSTOM_TEMPLATE_EMAIL_AUTH_PARTIAL : CUSTOM_TEMPLATE_FILE_AUTH_PARTIAL)

  const finalHtml = EMAIL_TEMPLATE_WITH_AUTH.replace("{{#content_goes_here#}}", dom.body.innerHTML);

  // Use proper Unicode-safe base64 encoding
  return btoa(encodeURIComponent(finalHtml).replace(/%([0-9A-F]{2})/g, (_, p1) => {
    return String.fromCharCode(parseInt(p1, 16));
  }));
}

/**
 * Reverses the effect of htmlToTokens by extracting the original content from the email template
 * @param encodedString - The base64 encoded string returned by htmlToTokens
 * @returns The original HTML content that was inserted into the template
 */
export function tokensToHtml(encodedString: string): string {
  try {
    // First decode the Unicode-safe base64 string
    const decodedBytes = atob(encodedString);
    const fullHtml = decodeURIComponent(decodedBytes.split('').map(c => {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));

    // The htmlToTokens function replaces {{#content_goes_here#}} with dom.body.innerHTML
    // So we need to extract the content that appears in that location

    // Look for the content between the template structure
    // The inserted content appears right after {{#content_goes_here#}} placeholder
    // which is at line 107 in the template, before the "Assessment Details" section

    // Find the pattern where content was inserted
    const parser = new DOMParser();
    const doc = parser.parseFromString(fullHtml, "text/html");

    // Look for the td element that contains the inserted content
    // It should be right before the "Assessment Details" paragraph
    const allTds = doc.querySelectorAll('td');

    for (const td of allTds) {
      const innerHTML = td.innerHTML;

      // Look for the td that contains both inserted content and "Assessment Details"
      if (innerHTML.includes('Assessment Details')) {
        // Extract everything before the "Assessment Details" paragraph
        const assessmentDetailsIndex = innerHTML.indexOf('<p data-id="react-email-text"');

        if (assessmentDetailsIndex > 0) {
          const insertedContent = innerHTML.substring(0, assessmentDetailsIndex).trim();

          // Clean up any empty content or just whitespace/newlines
          if (insertedContent && insertedContent.length > 10) {
            return insertedContent;
          }
        }
      }
    }

    // If we can't extract specific content, return the full HTML
    return fullHtml;
  } catch (error) {
    console.error('Error decoding tokens to HTML:', error);
    throw new Error('Failed to decode tokens to HTML. Make sure the input is a valid base64 string.');
  }
}



/**
 * Returns the full decoded HTML template (useful for debugging or full template access)
 * @param encodedString - The encoded string returned by htmlToTokens
 * @returns The complete decoded HTML template
 */
export function tokensToFullHtml(encodedString: string): string {
  try {
    // Unicode-safe base64 decoding to get the full template
    const decodedBytes = atob(encodedString);
    return decodeURIComponent(decodedBytes.split('').map(c => {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
  } catch (error) {
    console.error('Error decoding tokens to full HTML:', error);
    throw new Error('Failed to decode tokens to full HTML');
  }
}

/**
 * Alternative method using base64 encoding/decoding (same as tokensToFullHtml)
 * @param encodedString - The encoded string returned by htmlToTokens
 * @returns The decoded HTML string
 */
export function tokensToHtmlBase64(encodedString: string): string {
  try {
    // Unicode-safe base64 decoding
    const decodedBytes = atob(encodedString);
    return decodeURIComponent(decodedBytes.split('').map(c => {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
  } catch (error) {
    console.error('Error decoding tokens to HTML using base64:', error);
    throw new Error('Failed to decode tokens to HTML using base64');
  }
}

export const CUSTOM_EMAIL_TEMPLATE = (cover_image: string) => {

  return `
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<html lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <style>
      @font-face {
        font-family: "DM sans";
        font-style: normal;
        font-weight: 400;
        mso-font-alt: Verdana;
        src: url("https://fonts.googleapis.com/css2?family=DM+Sans:opsz,wght@9..40,100;9..40,200;9..40,300;9..40,400;9..40,500;9..40,600;9..40,700;9..40,800;9..40,900;9..40,1000&amp;family=Montserrat&amp;display=swap");
        /* format(&#x27; woff2&#x27; ); */
      }

      * {
        font-family: "DM sans", Verdana;
      }
    </style>
  </head>

  <body data-id="__react-email-body" style="
      width: max-content;
      height: max-content;
      margin: 0 auto;
      background: #f4f4f4ff;
    ">
    <table align="center" width="100%" data-id="__react-email-container" role="presentation" cellspacing="0" cellpadding="0" border="0" style="
        max-width: 37.5em;
        width: 100vw;
        height: max-content;
        overflow-x: hidden;
        padding-top: 0px;
        box-sizing: border-box;
        background-color: white;
      ">
      <tbody>
        <tr style="width: 100%">
          <td>
            <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation">
              <tbody>
                <tr>
                  <td>
                    <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation" style="
                        width: 100%;
                        height: 20%;
                        display: flex;
                        flex-direction: column;
                        color: rgb(255, 255, 255);
                        row-gap: 0.625rem;
                        justify-content: center;
                        align-items: center;
                        background-color: rgb(132, 39, 189);
                        background: url("${cover_image}");
                      ">
                      <tbody>
                        <tr>
                          <td style="position:relative">
                            <img data-id="react-email-img" src="${cover_image}"  style="
                                display: block;
                                outline: none;
                                border: none;
                                text-decoration: none;
                                width: 100%;
                                max-width: 700px;
                                max-height: 150px;
                                object-fit: cover;
                              ">
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation" style="
                        padding-left: 2.5rem;
                        padding-right: 2.5rem;
                        padding-top: 2.5rem;
                        padding-bottom: 2.5rem;
                      ">
                      <tbody>
                        <tr>
                          <td>
                            <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation">
                              <tbody>
                                <tr>
                                  <td>
                                    {{#content_goes_here#}}
                                    <p data-id="react-email-text" style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 1.5rem;
                                        padding-bottom: 0.5rem;
                                      "> <strong> Assessment Details </strong>
                                    </p>
                                    <p data-id="react-email-text" style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 0px;
                                        padding-bottom: 0px;
                                      ">
                                      Assessment Name: <strong>{{ assessment_name }}</strong>
                                    </p>
                                    <p data-id="react-email-text" style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 2px;
                                        padding-bottom: 2px;

                                      ">
                                    {% if start_time %}
                                        Assessment Start Date: <strong>{{ start_time }}</strong><br>
                                    {% endif %}
                                    Duration: <strong>{{ time_limit }}</strong><br>
                                    </p>
                                    <p data-id="react-email-text" style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 0px;
                                        padding-bottom: 0px;
                                      ">
                                    Assessment deadline: This assessment is valid until <strong>{{ deadline }}</strong>.<br>
                                    {% if start_time %}
                                        The ‘Take assessment’ button provided below will only be active from <strong>{{ start_time }}</strong> until <strong>{{ deadline }}</strong>.<br>
                                    {% else %}
                                        The ‘Take assessment’ button provided below will only be active during the mentioned time period.
                                    {% endif %}
                                    </p>
                                    <a href="{{ invitation_link }}" data-id="react-email-button" target="_blank" style="
                                        line-height: 100%;
                                        text-decoration: none;
                                        display: inline-block;
                                        max-width: 100%;
                                        padding: 0.75rem;
                                        background-color: rgb(117, 90, 226);
                                        border-radius: 0.375rem;
                                        padding-top: 0.5rem;
                                        padding-bottom: 0.5rem;
                                        margin-top: 0.5rem;
                                        margin-bottom: 0.5rem;
                                      "><span></span><span style="
                                          max-width: 100%;
                                          display: inline-block;
                                          line-height: 120%;
                                          mso-padding-alt: 0px;
                                          mso-text-raise: 0;
                                        "><p data-id="react-email-text" style="
                                            font-size: 14px;
                                            line-height: 24px;
                                            margin: 0px;
                                            color: rgb(255, 255, 255);
                                          ">
                                          Take Assessment
                                        </p>
                                        
                                        </span><span></span></a>
                                        ">
                                        <p data-id="react-email-text">
                                          <strong>or copy this link: <a href="{{ invitation_link }}" style="color: rgb(117, 90, 226); text-decoration: none;">{{ invitation_link }}</a></strong>
                                        </p>                                        
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                            <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation">
                              <tbody>
                                <tr>
                                  <td>
                                    <!-- <p
                                      data-id="react-email-text"
                                      style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 0.25rem;
                                        padding-bottom: 0.25rem;
                                        margin-top: 0.5rem;
                                      "
                                    >
                                      Use your "{{ auth_method1 }}" and "{{ auth_method2 }}"" details to login to the
                                      assessment,
                                    </p> -->
                                    <p data-id="react-email-text" style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 1rem;
                                        padding-bottom: 0.25rem;
                                      ">
                                      Use the following details to log in to the
                                      assessment:<br>
                                      {{#auth#}}
                                    </p>
                                    <p data-id="react-email-text" style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 0px;
                                        padding-bottom: 0px;
                                        margin-top: 1.3rem;
                                      ">
                                      Note that you can only take this
                                      assessment once.
                                    </p>
                                    <p data-id="react-email-text" style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 0px;
                                        padding-bottom: 0px;
                                      ">
                                      Good luck !
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>

  <style>
    @import url(&quot;https://fonts.googleapis.com/css2?family=DM+Sans:opsz,wght@9..40,100;9..40,200;9..40,300;9..40,400;9..40,500;9..40,600;9..40,700;9..40,800;9..40,900;9..40,1000&amp;family=Montserrat&amp;display=swap&quot;);

    .email-text {
      font-family: DM Sans, sans-serif;
    }
  </style>

</body></html>
`
}

export const CUSTOM_TEMPLATE_EMAIL_AUTH_PARTIAL = `<b>"email" - {{ candidate_email }}</b><br>
<b>"passcode" - {{ otp }}</b>`

export const CUSTOM_TEMPLATE_FILE_AUTH_PARTIAL = `<b>"{{ auth_method1 }}" - {{ auth_method1_value }}</b><br>
<b>"{{ auth_method2 }}" - {{ auth_method2_value }}</b> `
