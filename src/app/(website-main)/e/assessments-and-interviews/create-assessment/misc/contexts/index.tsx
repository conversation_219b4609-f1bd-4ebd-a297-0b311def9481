'use client'
import React, { createContext, useCallback, useContext, useRef } from 'react';

type ValidationFunction = () => Promise<boolean>;

const ValidationContext = createContext<{
    registerValidation: (step: number, validate: ValidationFunction, validationErrorMessage?:string) => void;
    validateStep: (step: number) => Promise<boolean>;
    validationErrorMessage: string | null;
} | null>(null);

const AssessmentCreatorValidationProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
    const validationFunctions = useRef<Record<number, ValidationFunction>>({});
    const [validationErrorMessage, setValidationErrorMessage] = React.useState<string | null>(null);
    const registerValidation = useCallback((step: number, validate: ValidationFunction, validationErrorMessage?: string) => {
        validationFunctions.current[step] = validate;
        if(validationErrorMessage){
            setValidationErrorMessage(validationErrorMessage)
        }
    }, []);

    const validateStep = async (step: number) => {
        const validate = validationFunctions.current[step];
        if (validate) {
            return await validate();
        }
        return true;
    };

    return (
        <ValidationContext.Provider value={{ registerValidation, validateStep, validationErrorMessage }}>
            {children}
        </ValidationContext.Provider>
    );
};

export default AssessmentCreatorValidationProvider;

export const useCreateAssessmentValidation = () => {
    const context = useContext(ValidationContext);
    if (!context) {
        throw new Error('useValidation must be used within a ValidationProvider');
    }
    return context;
};