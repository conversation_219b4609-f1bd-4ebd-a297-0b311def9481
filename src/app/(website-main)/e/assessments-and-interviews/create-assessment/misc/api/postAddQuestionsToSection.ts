import { Axios } from "@/lib/api/axios"
import { useMutation } from "@tanstack/react-query"

type props = {
    section_id: string
    assessment_id: string
    questions: string[]
}
const addQToSection = async ({ section_id, assessment_id, questions }: props) => {
    const res = await Axios.post(`assessments/${assessment_id}/sections/${section_id}/add_question/`, { questions });
    return res.data
}


export const UseAddQuestionsToSection = () => {
    return useMutation({
        mutationFn: addQToSection,
        mutationKey: ['add-question-to-section']
    })
}