import { Axios } from "@/lib/api/axios";
import { useMutation } from "@tanstack/react-query";
interface props {
    last_page_url: number | string
    assessment_id: string
}
const proceed = async ({ last_page_url, assessment_id }: props) => {
    const res = await Axios.patch(`assessments/assessments/${assessment_id}/`, { last_page_url });
    return res.data
}


export const useAssesmentCreatorProceedToNextStep = () => {
    return useMutation({
        mutationFn: proceed,
        mutationKey: ['proceed-to-next-step']
    })
}