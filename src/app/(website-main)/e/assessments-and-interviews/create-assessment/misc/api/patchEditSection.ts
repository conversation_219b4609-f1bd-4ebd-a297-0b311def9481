import { Axios } from "@/lib/api/axios";
import { useMutation } from "@tanstack/react-query";



interface Props {
  assessment_id: string;
  section_id: string
  name: string
}
const mutationFn = async ({ assessment_id, section_id, name }: Props) => {
  const res = await Axios.patch(`/assessments/assessment-section/${assessment_id}/${section_id}`, { section_name: name })
  return res.data

}

export const useEditSection = () => {
  return useMutation({
    mutationFn
  })
}