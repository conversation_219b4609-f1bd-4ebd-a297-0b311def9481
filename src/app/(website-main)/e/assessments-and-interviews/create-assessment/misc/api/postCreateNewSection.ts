import { Axios } from "@/lib/api/axios"
import { useMutation } from "@tanstack/react-query"

type props = {
    section_name: string
    assessment_id: string
}
const createSection = async ({ section_name, assessment_id }: props) => {
    const res = await Axios.post(`/assessments/assessment-section/${assessment_id}/`, { section_name });
    return res.data
}


export const UseCreateAssessmentSection = () => {
    return useMutation({
        mutationFn: createSection,
        mutationKey: ['create-section']
    })
}