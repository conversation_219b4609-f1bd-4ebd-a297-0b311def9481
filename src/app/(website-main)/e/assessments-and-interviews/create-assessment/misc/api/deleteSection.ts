import { Axios } from "@/lib/api/axios";
import { useMutation, useQueryClient } from "@tanstack/react-query";

const deleteSection = async ({ assessment_id, section_id }: { assessment_id: string; section_id: string }) => {
    const res = await Axios.delete(`/assessments/assessment-section/${assessment_id}/${section_id}`);
    return res.data
}

export const UseDeleteAssessmentSection = () => {
    const queryClient = useQueryClient()
    return useMutation(
        deleteSection,
        {
            mutationKey: ['delete-section'],
            onSuccess(data, variables, context) {
                queryClient.invalidateQueries({
                    queryKey: ['get-assessment', variables.assessment_id]
                })

            },
        }

    )
}