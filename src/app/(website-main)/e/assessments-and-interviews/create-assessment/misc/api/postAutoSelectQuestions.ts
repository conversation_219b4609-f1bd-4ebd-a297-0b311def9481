import { Axios } from "@/lib/api/axios";
import { useMutation } from "@tanstack/react-query";

interface RootObject {
    question_pack_id: string;
    question_distribution: {
        [key: string]: number;
    };
    assessment_id: string;
    section_id: string;
}

const mutationFn = async (data: RootObject) => {
    const res = await Axios.post(`/assessments/add-random-questions/`, data);
    return res.data;
}

export const UseAutoSelectQuestions = () => {
    return useMutation(
        {
            mutationFn,
            mutationKey: ['auto-select-questions']
        }
    );
}