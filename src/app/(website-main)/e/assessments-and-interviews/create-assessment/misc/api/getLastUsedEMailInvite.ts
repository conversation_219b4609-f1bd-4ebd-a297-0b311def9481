import { Axios } from "@/lib/api/axios"
import { useQuery } from "@tanstack/react-query"

interface EmailInviteData {
    custom_email_subject: string;
    custom_email_message: string;
    custom_email_header: string;
    cover_image: string;
}

interface APIResponse {
    success: boolean;
    message: string;
    data: EmailInviteData;
}

const getLastUsedInvite = async () => {
    const res = await Axios.get("/assessments/email-invite/last/")
    return res.data as APIResponse
}

const getLastUsedCCustomEmailInvite = async () => {
    const res = await Axios.get("/assessments/email-invite/last-custom/")
    return res.data
}

export const useGetLLastUsedEmailInvite = () => {
    return useQuery({
        queryKey: ["lastUsedEmailInvite"],
        queryFn: getLastUsedInvite
    })
}

export const useGetLLastUsedCustomEmailInvite = () => {
    return useQuery({
        queryKey: ["lastUsedCustomEmailInvite"],
        queryFn: getLastUsedCCustomEmailInvite
    })
}