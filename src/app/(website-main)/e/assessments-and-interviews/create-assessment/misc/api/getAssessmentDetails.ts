import { Axios } from "@/lib/api/axios";
import { useQuery } from "@tanstack/react-query";
import { TAssessment } from "../types";

const getAssessment = async (assessmentId: string) => {
    const res = await Axios.get(`assessments/${assessmentId}/`);
    return res.data as TAssessment;
}


export const UseGetAssessmentDetails = (assessmentId: string) => {
    return useQuery({
        queryKey: ['get-assessment', assessmentId],
        queryFn: () => getAssessment(assessmentId),
        enabled: !!assessmentId
    })
}