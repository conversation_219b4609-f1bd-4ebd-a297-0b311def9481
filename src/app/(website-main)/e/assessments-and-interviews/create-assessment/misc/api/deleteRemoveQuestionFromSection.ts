import { Axios } from "@/lib/api/axios"
import { useMutation } from "@tanstack/react-query"

type props = {
    section_id: string
    question_id: string
}
const removeFromSection = async ({ section_id, question_id }: props) => {
    const res = await Axios.delete(`/assessments/assessment-sections/${section_id}/remove-question/${question_id}/`,);
    return res.data
}


export const UseRemoveQuestionsFromSection = () => {
    return useMutation({
        mutationFn: removeFromSection,
        mutationKey: ['add-question-to-section']
    })
}