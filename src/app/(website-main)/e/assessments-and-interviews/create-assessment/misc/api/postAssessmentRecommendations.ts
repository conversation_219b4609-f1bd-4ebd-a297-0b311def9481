import { Axios } from "@/lib/api/axios";
import { useMutation } from "@tanstack/react-query";
import { TAssessment, TAssessmentQuestionset } from "../types";
interface TProps {
    role_id: number
    role_level: string
}

const getRecommendations = async (data: TProps) => {
    const res = await Axios.post(`assessments/test-recommendations/`, data);
    return res.data as TRecommendedAssessmentQuestionPack[];
}


export const UseGetAssessmentRecommendation = () => {
    return useMutation(
        {
            mutationFn: getRecommendations,
            mutationKey: ['get-assessment-recommendations']
        }
    );
}



export interface TRecommendedAssessmentQuestionPack {
    id: string;
    total_questions: number;
    question_types: {
        [key: string]: number;
    };
    question_set: TAssessmentQuestionset[];
    relevant_roles: Relevantrole[];
    name: string;
    description: null | string;
    summary: null | string;
    skills_tested: null | string;
    experience_level: string;
    tags: string[];
    label: null;
    section: string;
}

interface Relevantrole {
    id: number;
    name: string;
    use_ai: boolean;
}

