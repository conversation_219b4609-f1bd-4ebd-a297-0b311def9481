import { Axios } from "@/lib/api/axios"
import { useQuery } from "@tanstack/react-query"
import { TAssessmentQuestionset } from "../types"

const getPacks = async () => {
    const res = await Axios.get(`/assessments/question-packs/`)
    return res.data as TQuestionPacks[]
}
export const useGetCustomQuestionPacks = (is_custom: boolean) => {
    return useQuery({
        queryFn: getPacks,
        queryKey: ['get-custom-question-packs'],
        enabled: is_custom
    })
}

export interface TQuestionPacks {
    id: string;
    question_set: TAssessmentQuestionset[];
    question_types: {
        [key: string]: number;
    };
    name: string;
    description: string;
    total_questions: number;
    is_intervew: boolean;
    recruiter: number;
}


// export interface TQuestionTypesObject =