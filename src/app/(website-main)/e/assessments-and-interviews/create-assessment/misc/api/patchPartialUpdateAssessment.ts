import { Axios } from "@/lib/api/axios";
import { useMutation } from "@tanstack/react-query";
import { TAssessment } from "../types";
interface props {
    data: Partial<TAssessment>
    assessment_id: string
}
const updateAssessment = async ({ data, assessment_id }: props) => {
    const res = await Axios.patch(`assessments/assessments/${assessment_id}/`, { ...data });
    return res.data
}


export const useAssesmentCreatorPartialUpdate = () => {
    return useMutation({
        mutationFn: updateAssessment,
        mutationKey: ['partially-update-assessment']
    })
}