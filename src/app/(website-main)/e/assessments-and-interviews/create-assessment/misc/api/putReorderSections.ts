import { Axios } from "@/lib/api/axios"
import { useMutation } from "@tanstack/react-query"

const reorderSections = async ({ new_order, assessment_id }: { new_order: string[], assessment_id: string }) => {
    const res = await Axios.put(`/assessments/${assessment_id}/reorder-sections/`, { new_order })
    return res.data
}


export const useReorderSections = () => {
    return useMutation({
        mutationFn: reorderSections,
        mutationKey: ['reorder-sections']
    })
}