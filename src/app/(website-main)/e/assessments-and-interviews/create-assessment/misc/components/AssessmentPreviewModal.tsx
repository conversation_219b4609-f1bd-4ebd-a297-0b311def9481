import { Accordion, AccordionContent, AccordionItem, AccordionTrigger, EmptyCard, Modal } from '@/components/shared'
import React from 'react'
import useAssessmentCreatorStore from '../store'
import { QUESTION_TYPES_DICTIONARY } from '../../../misc/constants/constants'
import { ArrowDown2, FolderOpen } from 'iconsax-react'

interface AssessmentPreviewModalProps {
    isModalOpen: boolean
    closeModal: () => void
}
const AssessmentPreviewModal: React.FC<AssessmentPreviewModalProps> = ({ isModalOpen, closeModal }) => {
    const assessmectSections = useAssessmentCreatorStore(state => state.sections)
    return (
        <Modal
            isModalOpen={isModalOpen}
            closeModal={closeModal}
            heading='Assessment Preview'
        >

            <Accordion type='single'>
                {
                    assessmectSections?.filter((section) => section?.section_name !== undefined)
                        .map((section, index) => (
                            <AccordionItem key={index} value={section.id}>
                                <AccordionTrigger className="!no-underline" value={section.id}>
                                    <div className="flex items-center justify-between py-0">
                                        <div className="flex items-center gap-2">
                                            Section :
                                            <span className="flex  h-[18px] min-w-[18px] items-center justify-center rounded-full bg-primary text-white">
                                                {index + 1}
                                            </span>
                                            <p className="">{section?.section_name}</p>
                                        </div>
                                    </div>
                                </AccordionTrigger>
                                <AccordionContent className="space-y-4">
                                    {section.question_set?.map((question, index) => (
                                        <div className="border p-4 space-y-2 rounded-xl" key={index}>
                                            <h3>Question {index + 1}</h3>
                                            <div
                                                dangerouslySetInnerHTML={{ __html: question.question }}
                                                className="!test-[0.78rem]"
                                            />

                                            <hr />

                                            {/* Multi choice questions */}
                                            {question?.type ===
                                                QUESTION_TYPES_DICTIONARY["multiple_choice"]
                                                    .value && (
                                                    <div>
                                                        <ol>
                                                            {question?.answer_options?.map((
                                                                option,
                                                                index,
                                                            ) => (
                                                                <li key={index}>
                                                                    <label className="flex items-center gap-2">
                                                                        <input type="radio" name={question.id} />
                                                                        {option}
                                                                    </label>
                                                                </li>
                                                            ))}
                                                        </ol>
                                                    </div>
                                                )}
                                            {/* Multi response questions */}
                                            {question?.type ===
                                                QUESTION_TYPES_DICTIONARY["multiple_response"]
                                                    .value && (
                                                    <div>
                                                        <ol>
                                                            {question?.answer_options?.map((
                                                                option,
                                                                index,
                                                            ) => (
                                                                <li key={index}>
                                                                    <label className="flex items-center gap-2">
                                                                        <input
                                                                            type="checkbox"
                                                                            name={question.id}
                                                                        />
                                                                        {option}
                                                                    </label>
                                                                </li>
                                                            ))}
                                                        </ol>
                                                    </div>
                                                )}
                                            {/* true or false questions */}
                                            {question?.type ===
                                                QUESTION_TYPES_DICTIONARY["true_false"].value && (
                                                    <div>
                                                        <ol>
                                                            {"true, false".split(", ").map((
                                                                option,
                                                                index,
                                                            ) => (
                                                                <li key={index}>
                                                                    <label className="flex capitalize items-center gap-2">
                                                                        <input
                                                                            type="radio"
                                                                            value={option}
                                                                            name={question.id}
                                                                        />
                                                                        {option}
                                                                    </label>
                                                                </li>
                                                            ))}
                                                        </ol>
                                                    </div>
                                                )}
                                            {/* fill in the blanks questions */}
                                            {question?.type ===
                                                QUESTION_TYPES_DICTIONARY["fill_in_the_blanks"]
                                                    .value && (
                                                    <div>
                                                        <ol>
                                                            {question?.answer_options?.map((
                                                                option,
                                                                index,
                                                            ) => (
                                                                <li key={index}>
                                                                    <label className="flex items-center gap-2">
                                                                        <input
                                                                            type="radio"
                                                                            value={option}
                                                                            name={question.id}
                                                                        />
                                                                        {option}
                                                                    </label>
                                                                </li>
                                                            ))}
                                                        </ol>
                                                    </div>
                                                )}
                                            {/* essay questions */}
                                            {question?.type ===
                                                QUESTION_TYPES_DICTIONARY["essay"].value && (
                                                    <textarea
                                                        placeholder="Write your answer here"
                                                        className="input-grey"
                                                    />
                                                )}
                                        </div>
                                    ))}
                                </AccordionContent>
                            </AccordionItem>
                        ))}

            </Accordion>

            {
                assessmectSections.length === 0 &&
                <div>

                    <EmptyCard
                        title='No sections added'
                        icon={<FolderOpen size={50} />}
                        content='Add sections to your assessment to preview it'
                        contentClass='text-sm text-body-text'
                        titleClass='text-header-text'
                    />
                </div>
            }
        </Modal>
    )
}

export default AssessmentPreviewModal