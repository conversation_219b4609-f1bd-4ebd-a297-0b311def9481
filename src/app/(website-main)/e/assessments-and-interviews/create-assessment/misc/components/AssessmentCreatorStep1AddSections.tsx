import React from 'react'
import useAssessmentCreatorStore from '../store';
import { Button, EmptyCard, Input } from '@/components/shared';
import CreateNewSectionModal from './CreateNewSectionModal';
import { useBooleanStateControl } from '@/hooks';
import AssessmentSectionCard from './AssessmentSectionCard';
import { UseGetAssessmentRecommendation, useGetCustomQuestionPacks } from '../api';
import { TRecommendedAssessmentQuestionPack } from '../api/postAssessmentRecommendations';
import AssessmentRecomendationCard from './AssessmentRecomendationCard';
import { shallow } from 'zustand/shallow';
import { SearchNormal } from 'iconsax-react';
import { cn } from '@/utils';
import { Spinner } from '@/components/shared/icons';
import { useCreateAssessmentValidation } from '../contexts';
import { TQuestionPacks } from '../api/getCustomQuestionPacks';
import { MoveLeft } from '@/app/(website-main)/e/jobs/misc/icons';

const AssessmentCreatorStep1 = () => {
    const { role, role_level, is_custom } = useAssessmentCreatorStore(state => ({
        role: state.role,
        role_level: state.role_level,
        is_custom: state.is_custom
    }));
    const { mutate: getRecommendations, isLoading: isGettingRecommendations } = UseGetAssessmentRecommendation()
    const { data: customQuestionPacks, isLoading: isGettingCustomPacks } = useGetCustomQuestionPacks(is_custom)

    const sections = useAssessmentCreatorStore(state => state.sections)
    const assessmentId = useAssessmentCreatorStore(state => state.id)
    const [activeSectionId, setActiveSectionId] = React.useState(sections[0]?.id || '0');
    const activeSection = React.useMemo(() => {
        return sections.find((section) => section.id == activeSectionId) || undefined
    }, [activeSectionId, sections])

    const [searchTerm, setSearchTerm] = React.useState('')
    const [filteredQuestionPacks, setFilteredQuestionPacks] = React.useState<TQuestionPacks[]>(customQuestionPacks || [])
    const [recommendations, setRecommendations] = React.useState<TRecommendedAssessmentQuestionPack[]>([])
    const [filteredRecommendations, setFilteredRecommendations] = React.useState<TRecommendedAssessmentQuestionPack[]>([])

    const {
        state: isCreateSectionModalOpen,
        setTrue: openCreateSectionModal,
        setFalse: closeCreateSectionModal
    } = useBooleanStateControl()


    React.useEffect(() => {
        if (!is_custom) {
            getRecommendations(
                { role_id: role.id, role_level: role_level },
                {
                    onSuccess: (data) => {
                        console.log(data)
                        setRecommendations(data)
                        setFilteredRecommendations(data)
                    }
                }
            )
        }
    }, [role.id, role_level])


    React.useEffect(() => {
        if (searchTerm.trim() !== '') {
            if (is_custom) {
                const filteredQuestionPacks = customQuestionPacks?.filter(questionPack => {
                    return (
                        questionPack.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        questionPack.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        Object.keys(questionPack.question_types).some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
                    );
                });
                setFilteredQuestionPacks(filteredQuestionPacks || []);

            }
            else {

                const filteredRecommendations = recommendations.filter(recommendation => {
                    return (
                        recommendation.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        recommendation.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        recommendation.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
                    );
                });
                setFilteredRecommendations(filteredRecommendations);
            }
        }
        else {
            if (is_custom) {
                setFilteredQuestionPacks(customQuestionPacks || [])
            }
            else {

                setFilteredRecommendations(recommendations)
            }
        }
    }, [searchTerm, customQuestionPacks, recommendations])

    const { registerValidation } = useCreateAssessmentValidation();

    React.useEffect(() => {
        if (sections.length) {
            registerValidation(1, async () => {
                return true;
            });
        }

        else {
            registerValidation(1, async () => {
                return false;
            },
                "Please add at least one section to proceed"
            )
        }
    }, [registerValidation, sections]);

    React.useEffect(() => {
        if (sections.length) {
            if (activeSection) {
                setActiveSectionId(activeSection.id)
            } else {
                setActiveSectionId(sections[0].id)
            }
        }
    }, [])

    const containerRef = React.useRef<HTMLDivElement | null>(null)
    const [showMoveLeft, setShowMoveLeft] = React.useState(false);
    const [showMoveRight, setShowMoveRight] = React.useState(false);
    React.useEffect(() => {
        const container = containerRef.current;
        if (!container) return;

        const handleScroll = () => {
            const showLeft = container.scrollLeft > 0;
            const showRight = container.scrollLeft + container.clientWidth < container.scrollWidth;
            setShowMoveLeft(showLeft);
            setShowMoveRight(showRight);
        };

        container.addEventListener('scroll', handleScroll);
        handleScroll();
        return () => {
            container.removeEventListener('scroll', handleScroll);
        };
    }, [containerRef.current?.scrollWidth, containerRef.current?.clientWidth]);

    const MoveToLeft = () => {
        if (containerRef.current) {
            containerRef.current.scrollLeft -= 500;
        }
    };

    const MoveToRight = () => {
        if (containerRef.current) {
            containerRef.current.scrollLeft += 500;
        }
    };

    return (
        <div className="h-full overflow-y-scroll space-y-5 overflow-x-hidden">
            <section>
                <header className="flex justify-between p-1.5 px-3">
                    <div>
                        <h4 className="font-semibold text-header-text">Question Sections</h4>
                        <p className='text-sm text-body-text max-w-sm'>
                            Select the section you want to add questions to or create a new section
                        </p>
                    </div>
                    <div className="flex items-center gap-4">
                        <Button variant="white" className="min-w-max !px-4" size="small" onClick={openCreateSectionModal}>
                            Add Section
                        </Button>
                        <button className='bg-white hover:bg-primary-light-active border-primary border rounded-full block aspect-square p-2.5 disabled:opacity-30' onClick={MoveToLeft} disabled={!showMoveLeft}>
                            <MoveLeft className=' scale-70' />
                        </button>
                        <button className='bg-white hover:bg-primary-light-active border-primary border rounded-full block aspect-square p-2.5 disabled:opacity-30' onClick={MoveToRight} disabled={!showMoveRight}>
                            <MoveLeft className='rotate-180 scale-70' />
                        </button>
                    </div>
                </header>

                <div className="max-w-full overflow-hidden">
                    <section className="w-full overflow-hidden overflow-x-scroll scroll-smooth" ref={containerRef}>
                        <div className='flex items-stretch gap-2.5'>

                            {
                                sections.map((section, index) => {
                                    return (
                                        <AssessmentSectionCard
                                            key={section.id}
                                            assessment_id={assessmentId}
                                            section={section}
                                            isActive={activeSectionId === section.id}
                                            setAsActive={() => setActiveSectionId(section.id)}
                                            setActiveSectionId={setActiveSectionId}
                                        />
                                    )

                                })
                            }


                            {
                                Array.from({ length: 4 - (sections.length || 0) }).map((_, index) => {
                                    return (
                                        <article onClick={openCreateSectionModal} key={index} className='flex items-center justify-center w-[225px] min-h-[120px] shrink-0 bg-white rounded-xl cursor-pointer border-2 border-transparent hover:border-primary'>
                                            <Button variant="extralight" className="" onClick={openCreateSectionModal}>
                                                Add Section
                                            </Button>
                                        </article>
                                    )
                                })

                            }
                        </div>
                    </section>
                </div>
            </section>

            <section className="flex items-center">
                <Input
                    rightIcon={<SearchNormal size={18} />}
                    type="text"
                    name="search assessments"
                    placeholder="Search"
                    // variant="showcase"
                    className='!h-[2.5rem] 2xl:!h-[2.5rem]'
                    containerClassName="max-w-[300px] text-sm "
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                />
            </section>


            <section>
                {
                    ((!is_custom && isGettingRecommendations) || (is_custom && isGettingCustomPacks)) ?
                        <div className='flex items-center justify-center w-full h-full'>
                            <Spinner />
                        </div>
                        :
                        <>
                            <p className='col-span-full text-sm text-body-text px-3 mb-1.5'>
                                {

                                    !!activeSection ?
                                        <span>
                                            Adding questions to section {sections.findIndex((sec) => sec.id == activeSection.id) + 1} :
                                            <span className="text-header-text">
                                                {" "}
                                                {activeSection.section_name}
                                            </span>
                                        </span>

                                        :

                                        "Select a section or create new section to add questions"
                                }
                            </p>
                            <div
                                className={cn("grid items-stretch  gap-4",
                                    ((!is_custom && !filteredRecommendations.length) || (is_custom && !filteredQuestionPacks.length)) && "flex items-center justify-center w-full py-12",
                                    ((!is_custom && filteredRecommendations.length && filteredRecommendations.length > 3) || (is_custom && filteredQuestionPacks.length && filteredQuestionPacks.length > 3)) ?
                                        "md:grid-cols-[repeat(auto-fit,minmax(240px,1fr))]"
                                        :
                                        "md:grid-cols-[repeat(auto-fill,minmax(240px,1fr))]"
                                )}
                            >

                                {
                                    filteredRecommendations?.map((recommendation, index) => {
                                        return (
                                            <AssessmentRecomendationCard
                                                key={index}
                                                question_pack={recommendation}
                                                isSectionSelected={!!activeSection}
                                                active_section={sections.find(section => section.id === activeSectionId) || {} as any}
                                            />
                                        )
                                    })
                                }
                                {
                                    filteredQuestionPacks?.map((questionPack, index) => {
                                        return (
                                            <AssessmentRecomendationCard
                                                key={index}
                                                is_custom={is_custom}
                                                question_pack={questionPack}
                                                isSectionSelected={!!activeSection}
                                                active_section={sections.find(section => section.id === activeSectionId) || {} as any}
                                            />
                                        )
                                    })
                                }
                                {
                                    searchTerm.trim() !== '' && ((!is_custom && filteredRecommendations.length === 0) || (is_custom && filteredQuestionPacks.length == 0)) &&
                                    <EmptyCard
                                        icon={<img src="/images/create-assessments/no-assessment.png" alt="writing with pen" />}
                                        title="No recommended pack matches your filters"
                                        content={
                                            <div className="flex flex-col items-center justify-center gap-3 p-2.5 text-balance text-sm text-body-text">
                                                No recommendations available for this search term
                                                <Button
                                                    size="tiny"
                                                    variant="extralight"
                                                    onClick={() => setSearchTerm('')}
                                                >
                                                    Clear Search
                                                </Button>
                                            </div>
                                        }
                                        titleClass='pt-4 text-medium text-lg leading-tight'
                                    />
                                }
                                {
                                    !is_custom && recommendations?.length === 0 &&
                                    <EmptyCard
                                        icon={
                                            <img
                                                src="/images/create-assessments/no-assessment.png"
                                                alt="writing with pen"
                                            />
                                        }
                                        title="No Recommendations"
                                        content={
                                            <div className="flex flex-col items-center justify-center gap-3 p-2.5 text-balance text-sm text-body-text">
                                                No recommendations available for this role

                                            </div>
                                        }
                                    />
                                }
                            </div>
                        </>
                }
            </section>





            <CreateNewSectionModal
                isModalOpen={isCreateSectionModalOpen}
                closeModal={closeCreateSectionModal}
                assessment_id={assessmentId}
                setActiveSectionId={setActiveSectionId}
            />
        </div >
    )
}

export default AssessmentCreatorStep1