'use client';

import { useMutation, useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { ArrowDown, ArrowRight2 } from 'iconsax-react';
import React from 'react';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/shared/tabs';
import { cn } from '@/utils';
import { useAssesmentCreatorPartialUpdate } from '../api';
import { useCreateAssessmentValidation } from '../contexts';
import { useAssessmentConfigurationValidation } from '../contexts/step3';
import useAssessmentCreatorStore from '../store';
import AssessmentConfiguratioDetails, {
  AssessmentDetailSchema,
} from './AssessmentConfiguratioDetails';
import AssessmentConfigurationOthers, {
  AssessmentConfigurationOthersSchema,
} from './AssessmentConfigurationOthers';
import AssessmentConfigurationProctoring from './AssessmentConfigurationProctoring';

type TabValidationState = {
  state: boolean;
  message: string;
};
export interface TabValidationStates {
  [key: string]: TabValidationState;
}
const AssessmentCreatorStep3 = () => {
    const store = useAssessmentCreatorStore()
    const step = useAssessmentCreatorStore(state => state.step)
    const start_time = useAssessmentCreatorStore(state => state.start_time)
    const deadline = useAssessmentCreatorStore(state => state.deadline)
    const time_limit = useAssessmentCreatorStore(state => state.time_limit)
    const reminder_time = useAssessmentCreatorStore(state => state.reminder_time)
    const commencement_settings = useAssessmentCreatorStore(state => state.commencement_settings)
    const assessment_name = useAssessmentCreatorStore(state => state.name)
    const description = useAssessmentCreatorStore(state => state.description)
    const is_shuffle_sections = useAssessmentCreatorStore(state => state.is_shuffle_sections)
    const is_shuffle = useAssessmentCreatorStore(state => state.is_shuffle)

    const [activeTab, setactiveTab] = React.useState('0')
    const [tabValidationStates, setTabValidationStates] = React.useState<TabValidationStates>({
        '0': {
            state: false,
            message: 'Check your inputs in the assessment details tab'

        },
        '1': {
            state: true,
            message: "Check your inputs in the assessment details tab"

        },
        '2': {
            state: true,
            message: 'Check your inputs in the proctoring details tab'

        },
        '3': {
            state: false,
            message: "Check your inputs in the 'Other Settings' section"

        }
    })

  const tabs = [
    {
      id: '0',
      key: 'details',
      title: 'Assessment details',
      description: 'See and update your assessment details and guidelines',
      component: <AssessmentConfiguratioDetails />,
    },
    {
      id: '1',
      key: 'team',
      title: 'Add team members',
      description: 'Here you can add teams who can access this assessment.',
      component: <>pop44444444</>,
    },
    {
      id: '2',
      key: 'Proctoring',
      title: 'Proctoring and Invigilation',
      description:
        'Here you can configure the anti-cheating settings for the assessment',
      component: <AssessmentConfigurationProctoring />,
    },
    {
      id: '3',
      key: 'others',
      title: 'Other settings',
      description: 'View and configure timer and deadline for this assessment',
      component: (
        <AssessmentConfigurationOthers
          setTabValidationStates={setTabValidationStates}
        />
      ),
    },
  ];

  const { mutate: updateAssessment } = useAssesmentCreatorPartialUpdate();
  React.useEffect(() => {
    const validateThirdTab = () => {
      ///Because the validation state for the third tab is initially false, we need to validate the inputs in the third tab
      /// and set the validation state to true if the inputs are valid, without the user needing to navigate to the third tab capish?
      const result = AssessmentConfigurationOthersSchema.safeParse({
        commencement_settings: commencement_settings ? 'yes' : 'no',
        time_limit,
        reminder_time: reminder_time || 1,
        start_time: new Date(start_time || 0),
        deadline: deadline ? new Date(deadline) : undefined,
      });
      if (result.success) {
        const start_time = new Date(result.data.start_time);
        const deadline = result.data.deadline
          ? new Date(result.data.deadline)
          : undefined;

        const local_start_time = start_time
          ? `${format(
              start_time,
              'yyyy-MM-dd'
            )}T${start_time.toLocaleTimeString(undefined, {
              hourCycle: 'h23',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
            })}`
          : undefined;

        const local_deadline_time = deadline
          ? `${format(deadline, 'yyyy-MM-dd')}T${deadline.toLocaleTimeString(
              undefined,
              {
                hourCycle: 'h23',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
              }
            )}`
          : undefined;
        updateAssessment({
          assessment_id: store.id,
          data: {
            ...result.data,
            commencement_settings: commencement_settings,
            start_time: local_start_time,
            deadline: local_deadline_time,
          },
        });
      }

      setTabValidationStates(prev => ({
        ...prev,
        '3': {
          state: result.success,
          message: result.success
            ? ''
            : 'Check your inputs in the other details tab',
        },
      }));
    };
    validateThirdTab();
  }, [
    start_time,
    deadline,
    time_limit,
    reminder_time,
    commencement_settings,
    activeTab,
    step,
  ]);
  React.useEffect(() => {
    const validateFirstTab = () => {
      ///Because the validation state for the first tab is initially false, we need to validate the inputs in the third tab
      /// and set the validation state to true if the inputs are valid, without the user needing to navigate to the third tab capish?
      const result = AssessmentDetailSchema.safeParse({
        name: assessment_name,
        description,
        is_shuffle_sections,
        is_shuffle,
      });

      setTabValidationStates(prev => ({
        ...prev,
        '0': {
          state: result.success,
          message: result.success
            ? ''
            : 'Check your inputs in the assessment details tab',
        },
      }));
    };
    validateFirstTab();
  }, [description, assessment_name]);

  const { validateTab } = useAssessmentConfigurationValidation();
  const handleTabChange = async (tabId: string) => {
    const isValid = await validateTab(activeTab);
    if (isValid) {
      setTabValidationStates(prev => ({
        ...prev,
        [activeTab]: { state: true, message: '' },
      }));
      setactiveTab(tabId);
    } else {
      setTabValidationStates(prev => ({
        ...prev,
        [activeTab]: {
          state: false,
          message: 'Please complete this section before moving on.',
        },
      }));
    }
  };

  /////////////////////////////////////////////////////////////////////////////////////
  /////// VALIDATION FOR THE CURRENT STEP IN ASSESSMENT CREATION FLOW
  /////// Not to be confused with the ones above which is validation for the tabs in the current step
  /////// It uses a similar method of validating from within and having the parent have access to the result of the validation
  const { registerValidation } = useCreateAssessmentValidation();
  React.useEffect(() => {
    registerValidation(
      3,
      async () => {
        return Object.values(tabValidationStates).every(state => state.state);
      },
      Object.values(tabValidationStates).find(state => !state.state)?.message ||
        'Make sure to complete all sections before proceeding'
    );
  }, [registerValidation, tabValidationStates, activeTab]);

  return (
    <Tabs
      value={activeTab}
      onValueChange={handleTabChange}
      className="relative grid h-full w-full grid-cols-[minmax(300px,0.4fr),1fr] divide-x overflow-hidden p-0"
    >
      <TabsList className="sticky top-0 flex max-h-full flex-col justify-start divide-y overflow-y-hidden bg-transparent">
        <div className="mb-10 px-4">
          <h1 className="heading-2">Settings and configuration</h1>
          <p className="text-[0.825rem] text-[#8C8CA1]">
            Please review, configure, edit, and make any necessary changes to
            your assessment before inviting the first candidate
          </p>
        </div>

        {tabs.map((section, index) => (
          <TabsTrigger
            key={index}
            value={section.id}
            disabled={section.key === 'team'}
            className={cn(
              'flex w-full max-w-full items-center justify-between gap-4 p-4 text-left',
              section.key === 'team' ? 'opacity-30' : 'cursor-pointer',
              section.id == activeTab && '!bg-primary-light !text-primary'
            )}
          >
            <div>
              <h2
                className={cn(
                  'heading-text text-sm',
                  section.id == activeTab && 'text-primary'
                )}
              >
                {section.title}
              </h2>
              <p
                className={`h-[2lh] max-w-[90%] text-xs [text-wrap:balance] ${
                  section.id == activeTab ? 'text-[#8C8CA1]' : 'helper-text'
                }`}
              >
                {section.description}
              </p>
            </div>
            {section.id == activeTab && (
              <span className="inline-block">
                <ArrowRight2 size={19} strokeWidth={2} />
              </span>
            )}
          </TabsTrigger>
        ))}
      </TabsList>

      {tabs.map((section, index) => (
        <TabsContent
          key={index}
          value={section.id}
          className="mt-0 max-h-full overflow-y-scroll px-4 pb-4 lg:px-8"
        >
          {section.component}
        </TabsContent>
      ))}
    </Tabs>
  );
};

export default AssessmentCreatorStep3;
