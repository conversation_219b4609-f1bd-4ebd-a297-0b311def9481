import React from 'react'
import { useParams } from 'next/navigation'
import { CloseSquare } from 'iconsax-react'
import toast from 'react-hot-toast'

import { But<PERSON>, Checkbox2, LoaderBtn, Modal } from '@/components/shared'
import { useQueryClient } from '@tanstack/react-query'

import { TRecommendedAssessmentQuestionPack } from '../api/postAssessmentRecommendations'
import useAssessmentCreatorStore from '../store'
import { UseAddQuestionsToSection, UseRemoveQuestionsFromSection } from '../api'
import { TQuestionPacks } from '../api/getCustomQuestionPacks'
import { useBooleanStateControl } from '@/hooks'
import AutoAddQuestionsToSectionModal from './AutoAddQuestionsToSectionModal'

interface AddQuestionsToSectionFromPacksModalProps {
    isModalOpen: boolean
    closeModal: () => void
    question_pack: TRecommendedAssessmentQuestionPack | TQuestionPacks
    section_id: string
    is_custom?: boolean
}

const AddQuestionsToSectionFromPacksModal = ({ is_custom, isModalOpen, closeModal, question_pack, section_id }: AddQuestionsToSectionFromPacksModalProps) => {
    const params = useParams()
    const [selectedQuestions, setSelectedQuestions] = React.useState<string[]>([])
    const sections = useAssessmentCreatorStore(state => state.sections)
    const allQuestionsIds = sections.map(sec => sec.question_set.flat()).flat().map(sec => sec.id)
    const allQuestionsIdsInCurentSection = sections.filter(sec => sec.id == section_id).map(sec => sec.question_set.flat()).flat().map(sec => sec.id)
    const previouslySelected = question_pack.question_set
        .filter(question => allQuestionsIdsInCurentSection.includes(question.id))
        .map(question => question.id);
    // const previouslySelected = question_pack.question_set.map((set) => set.id)
    // .filter(id => allQuestionsIdsInCurentSection.includes(id))

    const { mutate: addQuestions, isLoading: isAddingQuestion } = UseAddQuestionsToSection()
    const { mutate: removeQuestion } = UseRemoveQuestionsFromSection()
    const queryClient = useQueryClient()
    const handleAddQuestionsToSection = (questions?: string[]) => {
        addQuestions(
            {
                assessment_id: params.id as string,
                questions: questions || selectedQuestions,
                section_id
            },
            {
                onSuccess() {
                    queryClient.invalidateQueries({
                        queryKey: ['get-assessment', params.id as string]
                    })
                    setSelectedQuestions([])
                    toast.success("Added successfully")
                    closeModal()
                },
            }
        )
    }

    const handleRemoveQuestionFromSection = (question_id: string,) => {
        const section_where_question_is = sections.find(section => section.question_set.some(question => question.id === question_id))
        removeQuestion(
            {
                section_id: section_where_question_is?.id || section_id,
                question_id
            },
            {
                onSuccess() {
                    queryClient.invalidateQueries({
                        queryKey: ['get-assessment', params.id as string]
                    })
                    toast.success("Removed successfully")
                    // closeModal()
                },
                onError(error, variables, context) {
                    toast.error("Failed to remove question")
                },
            }
        )

    }

    const {
        state: isAutoSelectModalOpen,
        setTrue: openAutoSelectModal,
        setFalse: closeAutoSelectModal
    } = useBooleanStateControl()


    return (

        <>
            <Modal
                heading='Add Questions'
                customWidths
                contentClass='max-w-[700px] w-full'
                isModalOpen={isModalOpen}
                closeModal={closeModal}
                className='relative '
                footerClass="sticky bottom-0 bg-primary-light"
                footer={
                    <div className="grid grid-cols-3 gap-4 p-4 px-6 lg:py-6 w-full">
                        <Button
                            disabled={
                                !question_pack.question_set.length ||
                                previouslySelected.length === question_pack.question_set.length
                            }
                            onClick={() => {
                                const allIdsExcludingPreviouslySelected = question_pack.question_set
                                    .filter(question => !previouslySelected.includes(question.id))
                                    .map(question => question.id);

                                setSelectedQuestions(allIdsExcludingPreviouslySelected)
                                handleAddQuestionsToSection(allIdsExcludingPreviouslySelected)
                            }}
                            variant="outlined"
                        >
                            Add all ({question_pack?.question_set.length - (previouslySelected?.length || 0)})
                        </Button>
                        {
                            is_custom &&
                            <Button
                                disabled={
                                    !question_pack.question_set.length ||
                                    previouslySelected.length === question_pack.question_set.length
                                }
                                onClick={openAutoSelectModal}
                                variant="outlined"
                            >
                                Auto select
                            </Button>
                        }
                        <Button
                            variant={!selectedQuestions.length ? "extralight" : "default"}
                            disabled={!selectedQuestions.length}
                            onClick={() => handleAddQuestionsToSection()}
                        >
                            Add selected ({selectedQuestions.length})
                            {
                                isAddingQuestion && <LoaderBtn />
                            }
                        </Button>
                    </div>
                }

            >
                <header className="bg-grey rounded-xl p-2 px-4 mb-4">
                    <h2 className="text-header-text text-lg font-medium">{question_pack?.name}</h2>
                    <p className="text-sm text-helper-text">{question_pack?.description}</p>
                </header>
                <div className="pb-8">
                    <ul className="space-y-2">
                        {
                            question_pack?.question_set.map((question, index) => (
                                <li key={index}>
                                    {
                                        allQuestionsIds?.includes(question.id) ?

                                            <div className="p-4 rounded-md border block" >
                                                <div className="flex justify-between items-center">
                                                    <h2 className="text-lg text-header-text font-medium"> Question {index + 1} </h2>
                                                    <Button
                                                        className="text-xs h-7 w-7"
                                                        size="icon"
                                                        variant="red"
                                                        onClick={() => handleRemoveQuestionFromSection(question.id)}
                                                        title='Remove question'
                                                    >
                                                        <CloseSquare size={20} />
                                                    </Button >
                                                </div>

                                                <div
                                                    className="!text-body-text !text-[0.82rem] pr-4 lg:pr-8 "
                                                    dangerouslySetInnerHTML={{ __html: question.question }}
                                                />
                                            </div>
                                            :
                                            <label className="p-4 rounded-md border block cursor-pointer" >
                                                <div className="flex justify-between items-center">
                                                    <h2 className="text-[1.05rem]"> Question {index + 1} </h2>

                                                    <Checkbox2
                                                        checked={selectedQuestions.includes(question.id)}
                                                        onCheckedChange={(checked) => {
                                                            if (checked) {
                                                                setSelectedQuestions([...selectedQuestions, question.id])
                                                            }
                                                            else {

                                                                setSelectedQuestions(selectedQuestions.toSpliced(selectedQuestions.indexOf(question.id), 1))
                                                            }
                                                        }}
                                                        name="select question"
                                                        value={question.id}
                                                    />

                                                </div>
                                                <div
                                                    className="!text-body-text !text-[0.8rem] pr-4 lg:pr-8 "
                                                    dangerouslySetInnerHTML={{ __html: question.question }}
                                                />
                                            </label>

                                    }
                                </li>
                            ))}
                    </ul>
                </div>
            </Modal>

            <AutoAddQuestionsToSectionModal
                isModalOpen={isAutoSelectModalOpen}
                closeModal={closeAutoSelectModal}
                question_pack={question_pack}
                section_id={section_id}
                assessment_id={params.id as string}
            />
        </>
    )
}

export default AddQuestionsToSectionFromPacksModal