import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "@/components/shared/dialog"
import { Label, Input, Button, Checkbox2, SelectMultipleCombo, Badge } from "@/components/shared"
import { BULK_PROCTOR_OPTIONS } from '../../../../misc/constants/constants'
import { TBulkToleranceSetup } from '../../types'
import { convertKebabAndSnakeToTitleCase } from '@/utils/strings'
import { CloseCircle } from 'iconsax-react'
import useAssessmentCreatorStore from '../../store'

interface BulkProctoringModalProps {
  isOpen: boolean
  onClose: () => void
  updateBulkToleranceSetup: (setup: TBulkToleranceSetup) => void
  bulk_tolerance_setup: TBulkToleranceSetup | null
}

const BulkProctoringModal: React.FC<BulkProctoringModalProps> = ({
  isO<PERSON>,
  onClose,
  updateBulkToleranceSetup,
}) => {
  const bulk_tolerance_setup = useAssessmentCreatorStore(state => state.bulk_tolerance_setup)
  const [combinedTolerance, setCombinedTolerance] = useState(bulk_tolerance_setup?.combined_tolerance || 2)
  const [selectedOptions, setSelectedOptions] = useState<string[]>(
    bulk_tolerance_setup?.options.map(o => o.option) || []
  )
  React.useEffect(() => {
    setCombinedTolerance(bulk_tolerance_setup?.combined_tolerance || 0)
    setSelectedOptions(bulk_tolerance_setup?.options.map(o => o.option) || [])
  }, [bulk_tolerance_setup])

  const handleSave = () => {
    updateBulkToleranceSetup({
      combined_tolerance: combinedTolerance,
      options: selectedOptions.map(option => ({ option })),
    })
    onClose()
  }

  const handleOptionChange = (option: string, checked: boolean) => {
    if (checked) {
      setSelectedOptions([...selectedOptions, option])
    } else {
      setSelectedOptions(selectedOptions.filter(o => o !== option))
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>Bulk Proctoring Settings</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 p-4 xl:px-8 w-full">
          <Input
            label='Combined Tolerance'
            id="combined-tolerance"
            type="number"
            value={combinedTolerance}
            onChange={(e) => {
              setCombinedTolerance(Number(e.target.value))
            }}
            onBlur={(e)=>{
console.log("BLUR")
              if (parseInt(e.target.value) < 2) return;
              setCombinedTolerance(2)

            }}
            variant="showcase"
          />
          <div className="grid gap-2">
            <SelectMultipleCombo
              values={selectedOptions}
              onChange={(values) => setSelectedOptions(values)}
              options={BULK_PROCTOR_OPTIONS.map((option) => ({
                value: option.key, label: convertKebabAndSnakeToTitleCase(option.value)
              }))}
              label='Proctoring Options'
              labelKey='label'
              valueKey='value'
              name='proctoring-options'
              placeholder='Select proctoring options'
              showSelectedValues={false}
              className="w-full max-w-full truncate"
              variant="showcase"

            />

            <section className='flex items-center flex-wrap gap-1.5'>
              {
                selectedOptions.map((option) => {
                  return (
                    <Badge key={option} variant="light" className="" size="lg">
                      {convertKebabAndSnakeToTitleCase(option)}
                      <CloseCircle className='ml-2 cursor-pointer' size={22} onClick={() => handleOptionChange(option, false)} />
                    </Badge>
                  )
                })
              }
            </section>
          </div>
        </div>

        <DialogFooter className="w-full flex items-center justify-center p-5 bg-primary-light rounded-t-xl">
          <Button className='w-full' onClick={handleSave}>Save changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default BulkProctoringModal

