"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/shared/tabs"
import IndividualSetup from './Proctoring/IndividualSetup'
import BulkSetup from './Proctoring/BulkSetup'
import OtherSetup from './Proctoring/OtherSetup'
import BulkProctoringModal from './Proctoring/BulkProctoringModal'
import ProctorCountModal from './Proctoring/ProctorCountModal'
import useAssessmentCreatorStore from '../store'
import { cn } from '@/utils'
import { ASSESSMENT_CREATOR_PROCTORING_SECTION_OPTIONS, ASSESSMENT_CREATOR_PROCTORING_TOLERANCE_OPTIONS } from './constants'
import { useAssessmentConfigurationValidation } from '../contexts/step3'
import { useAssesmentCreatorPartialUpdate } from '../api'
import { useQueryClient } from '@tanstack/react-query'

const AssessmentConfigurationProctoring: React.FC = () => {
  const [activeTab, setActiveTab] = useState("individual_tolerance_settings")
  const [isBulkProctorModalOpen, setIsBulkProctorModalOpen] = useState(false)
  const [isToleranceModalOpen, setIsToleranceModalOpen] = useState(false)
  const [activeToleranceOptionIndex, setActiveToleranceOptionIndex] = useState(0)
  const assessment_id = useAssessmentCreatorStore(state => state.id)
  const { updateProctoringOption, updateToleranceLevel, updateBulkToleranceSetup
  } = useAssessmentCreatorStore(state => ({
    updateProctoringOption: state.updateProctoringOption,
    updateToleranceLevel: state.updateToleranceLevel,
    updateBulkToleranceSetup: state.updateBulkToleranceSetup,

  }))

  const bulk_tolerance_setup = useAssessmentCreatorStore(state => state.bulk_tolerance_setup)
  const is_bulk_tolerance = useAssessmentCreatorStore(state => state.is_bulk_tolerance)
  const tab_change_tolerance_level = useAssessmentCreatorStore(state => state.tab_change_tolerance_level)
  const multi_face_tolerance_level = useAssessmentCreatorStore(state => state.multi_face_tolerance_level)
  const window_change_tolerance_level = useAssessmentCreatorStore(state => state.window_change_tolerance_level)
  const full_screen_tolerance_level = useAssessmentCreatorStore(state => state.full_screen_tolerance_level)
  const different_face_tolerance_level = useAssessmentCreatorStore(state => state.different_face_tolerance_level)

  const openToleranceLevelModal = (index: number) => {
    setActiveToleranceOptionIndex(index)
    setIsToleranceModalOpen(true)
  }

  const { registerValidation } = useAssessmentConfigurationValidation();
  const { mutate: updateAssessment } = useAssesmentCreatorPartialUpdate()
  const queryClient = useQueryClient()

  React.useEffect(() => {
    const validate = () => {
      return new Promise<boolean>((resolve) => {
        updateAssessment(
          {
            assessment_id,
            data: {
              bulk_tolerance_setup,
              is_bulk_tolerance,
              tab_change_tolerance_level,
              multi_face_tolerance_level,
              window_change_tolerance_level,
              full_screen_tolerance_level,
              different_face_tolerance_level,
            }
          },
          {
            onSuccess: () => {
              queryClient.invalidateQueries({
                queryKey: ['get-assessment', assessment_id]
              })
              resolve(true)
            },
            onError: () => {
              resolve(false)
            }
          })
      })
    }
    registerValidation("2", validate, "Check your inputs in proctoring configuration section");
  }, [bulk_tolerance_setup, is_bulk_tolerance, tab_change_tolerance_level, multi_face_tolerance_level, window_change_tolerance_level, full_screen_tolerance_level, different_face_tolerance_level])




  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-primary">Proctoring</h3>
        <p className="text-xs text-helper-text">Here you can configure the anti-cheating settings for the assessment</p>
      </div>
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="relative ">
          {
            ASSESSMENT_CREATOR_PROCTORING_SECTION_OPTIONS.map((option) => (
              <TabsTrigger key={option.id} value={option.id}
                className={
                  cn(
                    'linetaboption relative min-w-max flex-1 transition-all duration-500 md:min-w-[5rem] md:max-w-max',
                    'py-2 md:py-1 md:px-4 xl:px-6 ',
                    '[@media(max-width:300px)]:text-xs font-normal leading-5 !text-[14.25px]',
                    'focus:outline-none active:outline-none !bg-transparent relative z-[2]',
                    option.id.toLowerCase() === activeTab.toLowerCase() ? '!relative active !bg-transparent' : '!relative linetaboption !bg-transparent'
                  )
                }
              >
                {option.name}
              </TabsTrigger>
            ))
          }
          <div className="h-[2px] z-1 absolute left-0 w-full bg-primary-light-active bottom-[10%]"></div>
        </TabsList>

        <TabsContent value="individual_tolerance_settings">
          <IndividualSetup
            openToleranceLevelModal={openToleranceLevelModal}
            proctoring_tolerance_options={ASSESSMENT_CREATOR_PROCTORING_TOLERANCE_OPTIONS}
          />
        </TabsContent>

        <TabsContent value="bulk_tolerance_settings">
          <BulkSetup
            openBulkProctorModal={() => setIsBulkProctorModalOpen(true)}
            bulk_tolerance_setup={bulk_tolerance_setup}
            is_bulk_tolerance={is_bulk_tolerance}
          />
        </TabsContent>

        <TabsContent value="other_setup_options">
          <OtherSetup
            updateProctoringOption={updateProctoringOption}
          />
        </TabsContent>
      </Tabs>

      <BulkProctoringModal
        isOpen={isBulkProctorModalOpen}
        onClose={() => setIsBulkProctorModalOpen(false)}
        updateBulkToleranceSetup={updateBulkToleranceSetup}
        bulk_tolerance_setup={bulk_tolerance_setup}
      />

      <ProctorCountModal
        isOpen={isToleranceModalOpen}
        onClose={() => setIsToleranceModalOpen(false)}
        activeToleranceOptionIndex={activeToleranceOptionIndex}
        updateToleranceLevel={updateToleranceLevel}
      />
    </div>
  )
}

export default AssessmentConfigurationProctoring

