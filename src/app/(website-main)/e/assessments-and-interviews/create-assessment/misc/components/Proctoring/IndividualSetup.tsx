import React from 'react'
import { But<PERSON> } from "@/components/shared"
import { numberToPosition } from '@/lib/utils/functions'
import { TProctoringToleranceOption } from '../../types'
import useAssessmentCreatorStore from '../../store'

interface IndividualSetupProps {
  proctoring_tolerance_options: TProctoringToleranceOption[]
  openToleranceLevelModal: (index: number) => void
}

const IndividualSetup: React.FC<IndividualSetupProps> = ({
  proctoring_tolerance_options,
  openToleranceLevelModal,
}) => {
  const assessment = useAssessmentCreatorStore()

  return (
    <div className="space-y-4">
      <header className='px-4'>
        <h3 className="text-header-text font-semibold">Individual Tolerance Settings</h3>
        <p className="text-helper-text max-w-[80%] text-xs">Set tolerance levels for individual proctoring options</p>
      </header>
      {
        proctoring_tolerance_options.map((option, index) => (
          <label key={option.id} className="flex items-start justify-between gap-4 rounded-xl border p-4 cursor-pointer">
            <div className="space-y-1">
              <h3 className="text-header-text font-medium capitalize text-[0.9rem]">{option.title}</h3>
              <p className="helper-text text-xs">
                {option.description}, their assessment will be ended at the{' '}
                <span className="font-bold text-black">
                  {numberToPosition(assessment[option.id as keyof typeof assessment] as number)}
                </span>{' '}
                attempt of doing so.
                <>
                  You can set the the tolerance level to fit how you want it by
                  clicking on{' '}
                  <button
                    className="font-semibold text-primary"
                    onClick={() => {
                      openToleranceLevelModal(index);
                    }}
                  >
                    Change count
                  </button>
                </>
              </p>
            </div>

          </label>
        ))
      }
    </div>
  )
}

export default IndividualSetup

