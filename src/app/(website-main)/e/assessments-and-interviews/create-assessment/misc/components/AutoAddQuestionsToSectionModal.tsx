import React from 'react'
import { useParams } from 'next/navigation'
import { CloseSquare } from 'iconsax-react'
import toast from 'react-hot-toast'

import { But<PERSON>, Checkbox2, LoaderBtn, Modal } from '@/components/shared'
import { useQueryClient } from '@tanstack/react-query'

import { TRecommendedAssessmentQuestionPack } from '../api/postAssessmentRecommendations'
import useAssessmentCreatorStore from '../store'
import { UseAddQuestionsToSection, UseAutoSelectQuestions, UseRemoveQuestionsFromSection } from '../api'
import { TQuestionPacks } from '../api/getCustomQuestionPacks'
import { convertKebabAndSnakeToTitleCase } from '@/utils/strings'

interface AutoAddQuestionsToSectionModalProps {
    isModalOpen: boolean
    closeModal: () => void
    question_pack: TRecommendedAssessmentQuestionPack | TQuestionPacks
    section_id: string
    assessment_id: string
}
interface QuestionCounts {
    [key: string]: number;
}

const AutoAddQuestionsToSectionModal = ({
    isModalOpen,
    closeModal,
    question_pack,
    section_id,
    assessment_id
}: AutoAddQuestionsToSectionModalProps) => {

    const [questionCounts, setQuestionCounts] = React.useState<QuestionCounts>(
        question_pack.question_types
    );

    const handleQuestionCountChange = (type: string, count: number) => {
        const maxCount = question_pack.question_types[type];
        if (count > maxCount) {
            setQuestionCounts((prev: QuestionCounts) => ({
                ...prev,
                [type]: maxCount,
            }));
            toast.error(`You can't have more than ${maxCount} ${type} questions.`);
        } else {
            setQuestionCounts((prev: QuestionCounts) => ({
                ...prev,
                [type]: count,
            }));
        }
    };


    const { mutate: addQuestions, isLoading: isAddingQuestions } = UseAutoSelectQuestions()
    const queryClient = useQueryClient()
    const onSave = () => {
        addQuestions(
            {
                assessment_id,
                section_id,
                question_pack_id: question_pack.id,
                question_distribution: questionCounts,
            },
            {
                onSuccess: () => {
                    toast.success("Added successfully")
                    queryClient.invalidateQueries({
                        queryKey: ['get-assessment', assessment_id]
                    })
                    closeModal();
                },
            }
        )
    }

    return (
        <Modal
            heading="Auto select questions"
            customWidths
            contentClass="max-w-[450px] w-full"
            isModalOpen={isModalOpen}
            closeModal={closeModal}
            className="relative"
            color='purple'
            bodyClass='rounded-t-none'
        >

            <div className="pb-8 space-y-2.5">
                {Object.entries(questionCounts).map(([type, count]) => (
                    <div key={type} className="flex items-center justify-between space-x-4 p-3 px-4 rounded-md bg-white">
                        <label htmlFor={type} className="text-sm text-body-text">
                            {convertKebabAndSnakeToTitleCase(type)}
                            <span className="font-medium">

                                ({count}):
                            </span>
                        </label>
                        <input
                            id={type}
                            type="number"
                            min="0"
                            value={count}
                            onChange={(e) =>
                                handleQuestionCountChange(type, parseInt(e.target.value))
                            }
                            className="border rounded-md px-2 py-1 text-sm max-w-[50px] bg-primary-light"
                        />
                    </div>
                ))}
            </div>
            <div className="flex items-center justify-center">
                <Button
                    onClick={onSave}
                    className='w-full'
                    disabled={isAddingQuestions}
                >
                    Save
                </Button>
            </div>
        </Modal>
    );
};

export default AutoAddQuestionsToSectionModal;