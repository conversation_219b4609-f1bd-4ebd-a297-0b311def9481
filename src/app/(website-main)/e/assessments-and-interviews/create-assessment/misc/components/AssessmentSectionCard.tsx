import React from 'react'
import { TAssessmentSection } from '../types'
import { Button, ConfirmDeleteModal } from '@/components/shared'
import { CloseSquare, Edit } from 'iconsax-react'
import { cn } from '@/utils'
import { convertKebabAndSnakeToTitleCase } from '@/utils/strings'
import { useBooleanStateControl } from '@/hooks'
import { UseDeleteAssessmentSection } from '../api'
import { useQueryClient } from '@tanstack/react-query'
import useAssessmentCreatorStore from '../store'
import { fill } from 'lodash'
import EditSectionModal from './EditSectionModal'


interface AssessmentSectionCardProps {
    section: TAssessmentSection
    setAsActive: (index?: string) => void
    setActiveSectionId: React.Dispatch<React.SetStateAction<string>>
    isActive: boolean
    assessment_id: string

}

const questionTypeEnums = {
    multiple_response: "Multi Response",
    multiple_choice: "Multi Choice",
    essay: "Essay",
    true_false: "True/False",
    fill_in_the_blanks: "Fill in the blanks"
}
const AssessmentSectionCard: React.FC<AssessmentSectionCardProps> = ({ assessment_id, isActive, section, setAsActive }) => {
    const queryClient = useQueryClient()
    const allSections = useAssessmentCreatorStore(state => state.sections)
    const currentSectionIndex = allSections.findIndex(s => s.id === section.id)


    const sectionQuestionTypes = React.useMemo(() => {
        const questionTypesCount = section?.question_set?.reduce((acc: { [key: string]: number }, question) => {
            if (question && question.type) {
                acc[question.type] = (acc[question.type] || 0) + 1;
            }
            return acc;
        }, {});

        return questionTypesCount;
    }, [section]);

    const { mutate: deleteSection, isLoading } = UseDeleteAssessmentSection()
    const handleDeleteSection = () => {
        const data = {
            section_id: section.id,
            assessment_id
        }
        deleteSection(data, {
            onSuccess: () => {
                setAsActive(allSections[currentSectionIndex - 1]?.id)
                queryClient.invalidateQueries({
                    queryKey: ['get-assessment', assessment_id]
                })
                closeConfirmDeleteModal()
            }
        })
    }
    const {
        state: isConfirmDeleteModalOpen,
        setTrue: openConfirmDeleteModal,
        setFalse: closeConfirmDeleteModal
    } = useBooleanStateControl()
    const {
        state: isEditSectioneModalOpen,
        setTrue: openEditSectioneModal,
        setFalse: closeEditSectioneModal
    } = useBooleanStateControl()


    return (
        <article
            className={cn("p-1.5 border-2 border-transparent shrink-0 rounded-2xl bg-white w-[225px]",
                isActive && "border-primary"
            )}
            onClick={() => setAsActive()}
        >
            <div className="bg-light-accent-bg h-full w-full rounded-xl min-h-[120px]" >
                <header className="flex items-center justify-between rounded-xl bg-[#ECE9FF] p-2 text-left capitalize text-primary">
                    <h2 className='text-sm'>
                        {section.section_name}:{" "}
                        <span className="font-bold">
                            {
                                section?.question_set
                                    ?.length ||
                                0
                            }
                        </span>
                    </h2>
                    <div className="flex items-center gap-2">
                        <Button
                            size="icon"
                            variant="extralight"
                            title="delete section"
                            className="h-7 w-7"
                            onClick={openEditSectioneModal}
                        >
                            <p className="sr-only">Edit section</p>
                            <Edit size={20} />
                        </Button>
                        <Button
                            size="icon"
                            variant="extralight"
                            title="delete section"
                            className="h-7 w-7"
                            onClick={openConfirmDeleteModal}
                        >
                            <p className="sr-only">Delete section</p>
                            <CloseSquare size={20} />
                        </Button>
                    </div>
                </header>
                <ul className="grid grid-rows-3 flex-col gap-1 px-4 py-1.5 text-left capitalize text-primary">
                    {
                        Object.entries(sectionQuestionTypes).slice(0, 3).map(
                            ([questionType, count], index) => (
                                <li
                                    key={index}
                                    className="flex items-center justify-between gap-4"
                                >
                                    <div className="flex flex-1 items-center justify-between gap-2">
                                        <p className="text-[0.82rem]">
                                            {questionTypeEnums[questionType as keyof typeof questionTypeEnums]}:{" "}
                                            <span className="font-bold">{count}</span>
                                        </p>
                                    </div>
                                </li>
                            )
                        )
                    }
                </ul>
            </div>

            <EditSectionModal
                closeModal={closeEditSectioneModal}
                isModalOpen={isEditSectioneModalOpen}
                section={section}
                assessment_id={assessment_id}
            />


            <ConfirmDeleteModal
                isModalOpen={isConfirmDeleteModalOpen}
                closeModal={closeConfirmDeleteModal}
                deleteFunction={handleDeleteSection}
                isDeleting={isLoading}
                title="Delete Section"
            >
                <p>
                    Are you sure you want to delete this section?
                </p>
            </ConfirmDeleteModal>
        </article>
    )
}

export default AssessmentSectionCard