import React, { FormEvent, useEffect, useState } from "react";
import Modal from "@/components/Modal";
import { Section } from "@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments";
import { Button, LoaderBtn } from "@/components/shared";
import { TAssessmentSection } from "../types";
import { useEditSection } from "../api";
import toast from "react-hot-toast";
import { useQueryClient } from "@tanstack/react-query";

interface Props {
  isModalOpen: boolean;
  closeModal: () => void;
  section: TAssessmentSection;
  assessment_id: string;
}

const EditSectionModal: React.FC<Props> = ({
  isModalOpen,
  closeModal,
  section,
  assessment_id
}) => {

  const [name, setName] = useState(section.section_name);

  const { mutate, isLoading } = useEditSection()
  const queryClient = useQueryClient()

  function submitForm(event: FormEvent) {
    event.preventDefault();
    mutate({
      assessment_id,
      section_id: section.id,
      name
    }, {
      onSuccess: (data) => {
        closeModal()
        toast.success("Section updated successfully")
        queryClient.invalidateQueries({
          queryKey: ['get-assessment', assessment_id]
        })
      }
    })
  }

  return (
    <Modal title="Edit section" close={closeModal} is_open={isModalOpen}>
      <form
        className="bg-light-accent-bg md:w-[447px] space-y-6 p-4 text-sm"
        onSubmit={submitForm}
      >
        <div>
          <label className="space-y-1">
            <p>Section name</p>
            <input
              type="text"
              value={name}
              className="input-white"
              onChange={e => setName(e.target.value)}
            />
          </label>
        </div>

        <Button type="submit" className=" w-full">
          Save
          {
            isLoading && <LoaderBtn />
          }
        </Button>
      </form>
    </Modal>
  );
};
export default EditSectionModal;
