import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, Di<PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/shared/dialog"
import { But<PERSON> } from "@/components/shared"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/shared/select"
import { ASSESSMENT_CREATOR_PROCTORING_TOLERANCE_OPTIONS } from '../constants'


interface ProctorCountModalProps {
    isOpen: boolean
    onClose: () => void
    activeToleranceOptionIndex: number
    updateToleranceLevel: (option: string, value: number) => void
}

const ProctorCountModal: React.FC<ProctorCountModalProps> = ({
    isOpen,
    onClose,
    activeToleranceOptionIndex,
    updateToleranceLevel,
}) => {
    const [selectedValue, setSelectedValue] = useState<string>('2')
    const activeOption = ASSESSMENT_CREATOR_PROCTORING_TOLERANCE_OPTIONS[activeToleranceOptionIndex]

    useEffect(() => {
        setSelectedValue('0')
    }, [activeToleranceOptionIndex])

    const handleSave = () => {
        updateToleranceLevel(activeOption.id, Number(selectedValue))

        onClose()
    }

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[525px]">
                <DialogHeader className='h-14'>
                    <DialogTitle>Set Tolerance Level for {activeOption?.title}</DialogTitle>
                </DialogHeader>
                <div className="grid gap-4 p-4 xl:px-8 w-full">
                    <p className="text-sm text-gray-500">{activeOption?.modal_description}</p>
                    <Select value={selectedValue} onValueChange={setSelectedValue}>
                        <SelectTrigger className="bg-primary-light">
                            <SelectValue placeholder="Select tolerance level" className='bg-primary-light' />
                        </SelectTrigger>
                        <SelectContent>
                            {[0, 2, 3, 4].map((value) => (
                                <SelectItem key={value} value={value.toString()} className="h-10 cursor-pointer hover:bg-primary-light">
                                    {value} {value === 1 ? 'attempt' : 'attempts'}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>
                <DialogFooter className="w-full flex items-center justify-center p-5 bg-primary-light rounded-t-xl">
                    <Button className='w-full' onClick={handleSave}>Save changes</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

export default ProctorCountModal

