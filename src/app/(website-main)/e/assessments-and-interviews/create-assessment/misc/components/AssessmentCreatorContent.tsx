import React from 'react';
import AssessmentCreatorStep1 from './AssessmentCreatorStep1AddSections';
import AssessmentCreatorStep2 from './AssessmentCreatorStep2.ModifySections';
import AssessmentCreatorStep3 from './AssessmentCreatorStep3Configure';
import { AssessmentConfigurationValidationProvider } from '../contexts/step3';
import AssessmentCreatorStep4 from './AssessmentCreatorStep4Invitations';
import { cn } from '@/utils';


interface AssessmentCreatorContentProps {
    step: number;
}

const AssessmentCreatorContent: React.FC<AssessmentCreatorContentProps> = ({
    step,
}) => {

    const renderAssessmentCreatorContent = () => {
        switch (step) {
            case 1:
                return (
                    <AssessmentCreatorStep1 />
                );
            case 2:
                return (
                    <AssessmentCreatorStep2 />
                );
            case 3:
                return (
                    <AssessmentConfigurationValidationProvider>
                        <AssessmentCreatorStep3 />
                    </AssessmentConfigurationValidationProvider>
                );
            case 4:
                return (
                    <AssessmentCreatorStep4 />
                );
            // case 4:
            //     return (
            //         <ResumeBuilderStep4Objective />
            //     );
            default:
                return null;
        }
    };

    return (
        <main className={cn(
            'grid max-h-full overflow-hidden rounded-[0.9375rem] py-2',
            step == 2 || step == 3 && 'bg-white',
        )}>
            {renderAssessmentCreatorContent()}
        </main>
    )
};

export default AssessmentCreatorContent;