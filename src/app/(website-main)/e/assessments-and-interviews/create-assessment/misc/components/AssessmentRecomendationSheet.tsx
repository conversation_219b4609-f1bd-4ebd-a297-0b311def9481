import { Sheet, SheetContent, SheetTitle } from '@/components/shared'
import React from 'react'
import { QUESTION_TYPES_DICTIONARY } from '../../../misc/constants/constants';
import { TRecommendedAssessmentQuestionPack } from '../api/postAssessmentRecommendations';
import { TQuestionPacks } from '../api/getCustomQuestionPacks';

interface AssessmentRecomendationSheetProps {
    isSheetOpen: boolean;
    closeSheet: () => void;
    question_pack: TRecommendedAssessmentQuestionPack | TQuestionPacks;
}

const AssessmentRecomendationSheet: React.FC<AssessmentRecomendationSheetProps> = ({ isSheetOpen, closeSheet, question_pack }) => {
    return (
        <Sheet
            open={isSheetOpen}
            onOpenChange={closeSheet}
            modal
        >
            <SheetContent className="!w-[90vw] !max-w-[600px] lg:px-6">
                <SheetTitle >Question details</SheetTitle>
                <div className="flex min-h-full items-start py-4">


                    <div className="space-y-4 text-sm">
                        <div className="flex gap-4">
                            {/* <button onClick={previewquestion_pack} type="button" className="btn-primary-bordered">
                                    Preview question_pack
                                </button> */}
                            {/* {is_added ? (
                                          <Button
                                              className="btn-primary-light heading-text font-normal"
                                              is_busy={is_removing}
                                              onClick={handleRemove}
                                          >
                                              Remove
                                          </Button>
                                      ) : (
                                          <Button
                                              type="button"
                                              is_busy={is_adding}
                                              className="btn-primary"
                                              onClick={handleAdd}
                                          >
                                              Add to question_pack
                                          </Button>
                                      )} */}
                        </div>
                        <div className="bg-grey rounded-xl p-4">
                            <h2 className="text-header-text font-medium text-base">{question_pack.name}</h2>
                            <p className="text-sm text-helper-text">{question_pack.description}</p>
                        </div>
                        <div className="text-sm text-helper-text bg-grey space-y-4 rounded-xl p-4">
                            <h2 className="text-header-text font-medium text-base">Pack details</h2>
                            <div className="flex gap-4">
                                <p>
                                    Total questions:{' '}
                                    <span className="heading-text">
                                        {question_pack.question_set?.length || 0}
                                    </span>
                                </p>
                                <p>
                                    Question type: <span className="heading-text">{question_pack.question_types.length}</span>
                                </p>
                            </div>
                            <ul className="heading-text flex flex-wrap gap-2 font-normal capitalize">
                                {
                                    'question_types' in question_pack && Array.isArray(question_pack.question_types) &&
                                    question_pack.question_types?.map(
                                        (
                                            question_type, index: number) => (
                                            <li
                                                key={index}
                                                className="inline-flex items-center gap-2 rounded-lg text-sm text-body-text bg-white border py-1.5 px-4"
                                            >
                                                {/* <QuestionTypeIcon question_type={question_type.toLowerCase()} /> */}
                                                {QUESTION_TYPES_DICTIONARY[question_type.toLowerCase()]?.name}
                                            </li>
                                        )
                                    )}
                            </ul>
                            <div className="space-y-2">
                                <h2>Skills</h2>
                                <ul className="heading-text flex flex-wrap gap-2 font-normal capitalize">
                                    {
                                        'tags' in question_pack && question_pack.tags?.map((tag, index) => (
                                            <li
                                                key={index}
                                                className="rounded-lg text-sm text-body-text bg-white border py-1.5 px-4"
                                            >
                                                {tag}
                                            </li>
                                        ))
                                    }
                                </ul>
                            </div>
                        </div>
                        <div className="space-y-2 p-2">
                            <h2 className="text-header-text font-medium text-base">Pack summary</h2>
                            {'summary' in question_pack && (
                                <p className="text-sm text-helper-text">{question_pack.summary}</p>
                            )}
                        </div>
                        <div className="space-y-2 p-2">
                            <h2 className="text-header-text font-medium text-base">
                                Skill tested in this pack
                            </h2>
                            {'skills_tested' in question_pack && (
                                <p className="text-sm text-helper-text whitespace-pre-wrap">
                                    {question_pack.skills_tested}
                                </p>
                            )}
                        </div>
                    </div>
                </div>
            </SheetContent>
        </Sheet>
    )
}

export default AssessmentRecomendationSheet