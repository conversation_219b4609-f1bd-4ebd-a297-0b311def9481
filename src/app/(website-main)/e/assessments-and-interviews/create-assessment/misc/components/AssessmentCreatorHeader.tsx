import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import React from 'react';
import toast from 'react-hot-toast';
import { Button, ErrorModal, LinkButton, LoaderBtn } from '@/components/shared';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { cn } from '@/utils';
import {
  convertKebabAndSnakeToTitleCase,
  convertToTitleCase,
} from '@/utils/strings';
import { useAssesmentCreatorProceedToNextStep } from '../api';
import { useCreateAssessmentValidation } from '../contexts';
import useAssessmentCreatorStore from '../store';
import AssessmentPreviewModal from './AssessmentPreviewModal';

const ASSESSMENT_CREATOR_STEPS = {
  SECTIONS: 1,
  TEMPLATE: 2,
  CONTACT: 3,
  OBJECTIVE: 4,
  EXPERIENCE: 5,
  EDUCATION: 6,
  SKILLS: 7,
  PROJECT: 8,
  ADDITIONS: 9,
  SUMMARY: 10,
};

export default function AssessmentCreatorHeader() {
  const step = useAssessmentCreatorStore(state => state.step);
  const assessmentId = useAssessmentCreatorStore(state => state.id);
  const assessmentName = useAssessmentCreatorStore(state => state.name);
  const assessmentRole = useAssessmentCreatorStore(state => state.role);
  const assessmentRoleLevel = useAssessmentCreatorStore(
    state => state.role_level
  );
  const assessmentSections = useAssessmentCreatorStore(state => state.sections);
  const allQuestionsIds = assessmentSections
    .map(sec => sec.question_set.flat())
    .flat()
    .map(sec => sec.id);

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();
  const {
    state: isPreviewModalOpen,
    setTrue: openPreviewModal,
    setFalse: closePreviewModal,
  } = useBooleanStateControl();

  const { validateStep, validationErrorMessage } =
    useCreateAssessmentValidation();
  const { setStep, updateStore } = useAssessmentCreatorStore(state => ({
    step: state.step,
    setStep: state.setStep,
    updateStore: state.updateStore,
  }));

  const { mutate: goToNextorPrevious, isLoading: isMoving } =
    useAssesmentCreatorProceedToNextStep();
  const handleNext = async () => {
    const isValid = await validateStep(step);
    if (isValid) {
      goToNextorPrevious(
        {
          last_page_url: step + 1,
          assessment_id: assessmentId,
        },
        {
          onSuccess(data) {
            // updateStore({ ...data })
            setStep(step + 1);
          },
        }
      );
    } else {
      openErrorModalWithMessage(
        validationErrorMessage || 'Please check your inputs and try again.'
      );
    }
  };
  const handleBack = () => {
    setStep(step - 1);
  };
  const router = useRouter();
  const handleSaveAsDraft = () => {
    toast.success('Assessment saved as draft');

    router.push(`/e/assessments-and-interviews/my-assessments`);
  };

  return (
    <div
      className={cn(
        'mt-1.5 flex items-center  gap-5 px-4 py-2',
        step < 4 ? 'bg-white' : ''
      )}
    >
      <section className="flex items-start gap-5">
        {step > 1 ? (
          <Button size="capsule" variant="extralight" onClick={handleBack}>
            Back
          </Button>
        ) : (
          <LinkButton
            href="/e/assessments-and-interviews/my-assessments"
            size="capsule"
            variant="extralight"
          >
            Back
          </LinkButton>
        )}
        {step < 4 && (
          <div className="flex flex-col gap-1.5">
            <h1 className="font-medium text-header-text">{assessmentName}</h1>
            <div className="flex items-center gap-5">
              <p className="text-xs text-helper-text md:text-[0.825rem]">
                Role:
                <span className="ml-2 text-header-text">
                  {assessmentRole.name}
                </span>
              </p>
              <p className="text-xs text-helper-text md:text-[0.825rem]">
                Level:
                <span className="ml-2 text-header-text">
                  {convertKebabAndSnakeToTitleCase(assessmentRoleLevel)}
                </span>
              </p>
              <p className="text-xs text-helper-text md:text-[0.825rem]">
                Total Questions:
                <span className="ml-2 text-header-text">
                  {allQuestionsIds.length}
                </span>
              </p>
            </div>
            <div className="relative flex items-center justify-start">
              {Object.keys(ASSESSMENT_CREATOR_STEPS).map((stepper, index) => (
                <React.Fragment key={index}>
                  {index > 0 && index < 4 && (
                    <div className="mr-3 flex items-center lg:gap-x-1">
                      <motion.div
                        className="relative h-[6px] overflow-hidden rounded-full bg-primary-light"
                        initial={false}
                        animate={{
                          width: step >= index ? '60px' : '45px',
                        }}
                        transition={{ duration: 0.3 }}
                      >
                        <motion.div
                          className="absolute left-0 top-0 h-[6px] rounded-full bg-primary"
                          initial={false}
                          animate={{
                            backgroundColor:
                              step >= index
                                ? 'rgb(117, 90, 226)'
                                : 'rgb(229, 231, 235)',
                            width: step >= index ? '100%' : '0px',
                          }}
                          transition={{ duration: 0.3 }}
                        />
                      </motion.div>
                    </div>
                  )}
                </React.Fragment>
              ))}
            </div>
          </div>
        )}
      </section>
      {step < 4 && (
        <section className="ml-auto flex items-center gap-4">
          <Button size="small" variant="light" onClick={handleSaveAsDraft}>
            Save as Draft
          </Button>
          <Button size="small" variant="extralight" onClick={openPreviewModal}>
            Preview test
          </Button>

          <Button size="small" onClick={handleNext}>
            Proceed
            {isMoving && <LoaderBtn />}
          </Button>
        </section>
      )}
      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        heading="Complete all necessary fields/sections"
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 hover:bg-red-950/80 sm:text-sm md:px-6"
            size="lg"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>

      <AssessmentPreviewModal
        isModalOpen={isPreviewModalOpen}
        closeModal={closePreviewModal}
      />
    </div>
  );
}
