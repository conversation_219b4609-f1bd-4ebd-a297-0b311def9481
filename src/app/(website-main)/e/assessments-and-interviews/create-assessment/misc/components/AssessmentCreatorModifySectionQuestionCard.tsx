import React from 'react'
import { Edit2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash } from 'iconsax-react';
import { useParams } from 'next/navigation';

import { useBooleanStateControl } from '@/hooks';
import { useQueryClient } from '@tanstack/react-query';

import useAssessmentCreatorStore from '../store';
import { TAssessmentQuestionset, TAssessmentSection } from '../types';
import { QUESTION_TYPES_DICTIONARY } from '../../../misc/constants/constants';
import EditQuestionModal from '../../../question-pack/misc/components/EditQuestionModal';
import CreateQuestionSelectTypeModal from '../../../question-pack/misc/components/CreateQuestionSelectTypeModal';
import { UseRemoveQuestionsFromSection } from '../api';
import toast from 'react-hot-toast';
import { ConfirmDeleteModal } from '@/components/shared';



interface AssessmentCreatorModifySectionQuestionCardProps {
    section?: TAssessmentSection
    index: number
    question: TAssessmentQuestionset
}

const AssessmentCreatorModifySectionQuestionCard = ({ section, question, index, }: AssessmentCreatorModifySectionQuestionCardProps) => {
    const queryClient = useQueryClient()

    const sections = useAssessmentCreatorStore(state => state.sections)
    const assessmentId = useAssessmentCreatorStore(state => state.id)
    const params = useParams()


    const {
        state: isConfirmDeleteQuestionModalOpen,
        setTrue: openConfirmDeleteQuestionModal,
        setFalse: closeConfirmDeleteQuestionModal
    } = useBooleanStateControl()

    const {
        state: isEditQuestionModalOpen,
        setTrue: openEditQuestionModal,
        setFalse: closeEditQuestionModal
    } = useBooleanStateControl()

    const sectionQuestionTypes = React.useMemo(() => {
        const questionTypesCount = section?.question_set?.reduce((acc: { [key: string]: number }, question) => {
            if (question && question.type) {
                acc[question.type] = (acc[question.type] || 0) + 1;
            }
            return acc;
        }, {});

        return questionTypesCount;
    }, [section]);




    const { mutate: removeQuestion, isLoading: isDeletingQuestion } = UseRemoveQuestionsFromSection()
    const handleRemoveQuestionFromSection = () => {
        removeQuestion(
            {
                section_id: section?.id! || '',
                question_id: question.id
            },
            {
                onSuccess() {
                    queryClient.invalidateQueries({
                        queryKey: ['get-assessment', params.id as string]
                    })
                    toast.success("Removed successfully")
                    closeConfirmDeleteQuestionModal()
                    // closeModal()
                },
                onError(error, variables, context) {
                    toast.error("Failed to remove question")
                },
            }
        )

    }

    return (

        <div className="space-y-2 rounded-xl border p-4">
            <div className="flex items-center justify-between">
                <h3 className="heading-text">Question {index + 1}</h3>
                <div className="flex items-center gap-2">
                    <button
                        type="button"
                        className="btn-icon-primary-light"
                        onClick={openEditQuestionModal}
                        title="edit question"
                    >
                        <Edit2 size={17} />
                    </button>
                    <button
                        type="button"
                        title="delete question"
                        className="btn-icon-primary-light"
                        onClick={openConfirmDeleteQuestionModal}
                    >
                        <Trash size={17} />
                    </button>
                </div>
            </div>
            <div className="flex flex-wrap gap-3">
                {
                    question.images?.map((url, index) => (
                        <div key={index} className="relative">
                            <img
                                src={url}
                                alt={`Question image ${index + 1}`}
                                className="h-24 w-24 rounded-lg object-cover"
                            />

                        </div>
                    ))
                }
            </div>
            <div
                dangerouslySetInnerHTML={{ __html: question.question }}
                className='tiptap ProseMirror !text-sm !mt-0 mb-1'
            />

            {/* Multi choice questions */}
            {question?.type ===
                QUESTION_TYPES_DICTIONARY['multiple_choice'].value && (
                    <div>
                        <ol className="list-[upper-alpha] pl-6 text-body-text">
                            {question?.answer_options?.map((option, index) => (
                                <li key={index}>{option}</li>
                            ))}
                        </ol>
                    </div>
                )}


            {/* Multi response questions */}
            {question?.type ===
                QUESTION_TYPES_DICTIONARY['multiple_response'].value && (
                    <div>
                        <ol className="list-[upper-alpha] pl-6 text-body-text">
                            {question?.answer_options?.map((option, index) => (
                                <li key={index}>{option}</li>
                            ))}
                        </ol>
                    </div>
                )}


            {/* fill in the blanks questions */}
            {question?.type ===
                QUESTION_TYPES_DICTIONARY['fill_in_the_blanks'].value && (
                    <div>
                        <ol className="list-[upper-alpha] pl-6 text-body-text">
                            {question?.answer_options?.map((option, index) => (
                                <li key={index}>{option}</li>
                            ))}
                        </ol>
                    </div>
                )}

            {/* true or false questions */}
            {question?.type ===
                QUESTION_TYPES_DICTIONARY['true_false'].value && (
                    <div>
                        <ol className="list-[upper-alpha] capitalize pl-6 text-body-text">
                            {"true, false".split(", ").map((option, index) => (
                                <li key={index}>{option}</li>
                            ))}
                        </ol>
                    </div>
                )}

            {/* essay questions */}
            {question?.type === QUESTION_TYPES_DICTIONARY['essay'].value && (
                <div className="bg-grey min-h-[100px] rounded-md p-4 text-xs">
                    <p>Answer should be entered here..</p>
                </div>
            )}

            <EditQuestionModal
                closeEditQuestionModal={closeEditQuestionModal}
                isEditQuestionModalOpen={isEditQuestionModalOpen}
                question={question}
                questionType={question.type as "multiple_choice" | "multiple_response" | "fill_in_the_blanks" | "true_false" | "essay"}
                assessmentId={assessmentId}
                sectionId={section?.id}
                onSave={() => { }}
            />

            <ConfirmDeleteModal
                isModalOpen={isConfirmDeleteQuestionModalOpen}
                closeModal={closeConfirmDeleteQuestionModal}
                deleteFunction={handleRemoveQuestionFromSection}
                title="Delete question"
                isDeleting={isDeletingQuestion}
            >
                <p>
                    Are you sure you want to delete this question?
                </p>
            </ConfirmDeleteModal>
        </div>

    )
}

export default AssessmentCreatorModifySectionQuestionCard