"use client"

import React, { useRef, useState } from 'react'
import { z } from 'zod'
import { Pause, Play } from 'iconsax-react'
import { useForm, Controller } from "react-hook-form"

import { zodResolver } from "@hookform/resolvers/zod"
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Assessment } from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments'
import Switch from '@/components/Switch'
import { Axios } from '@/lib/api/axios'
import { FileUpload, Input, Textarea, Button, LoaderBtn } from '@/components/shared'

import useAssessmentCreatorStore from '../store'
import { useAssessmentConfigurationValidation } from '../contexts/step3'
import { useAssesmentCreatorPartialUpdate } from '../api'


const MAX_VIDEO_SIZE = 30 * 1024 * 1024; // 30MB

export const AssessmentDetailSchema = z.object({
    name: z.string().min(1, "Assessment name is required"),
    description: z.string().max(400, "Description must be 400 characters or less"),
    is_shuffle_sections: z.boolean(),
    is_shuffle: z.boolean(),
    intro_video: z.string().optional(),
})

type AssessmentDetailFormData = z.infer<typeof AssessmentDetailSchema>

const AssessmentDetail: React.FC = () => {
    const updateStore = useAssessmentCreatorStore(state => state.updateStore)

    const [isPlaying, setIsPlaying] = useState(false)
    const [currentVideoTime, setCurrentVideoTime] = useState('0:00')
    const videoRef = useRef<HTMLVideoElement | null>(null)
    const {
        id: assessment_id,
        name: assessment_name,
        description: assessment_description,
        is_shuffle_sections: assessment_is_shuffle_sections,
        is_shuffle: assessment_is_shuffle,
        intro_video_url: assessment_intro_video
    } = useAssessmentCreatorStore()

    const { control, handleSubmit, watch, setValue, formState: { errors }, trigger } = useForm<AssessmentDetailFormData>({
        resolver: zodResolver(AssessmentDetailSchema),
        defaultValues: {
            name: assessment_name,
            description: assessment_description,
            is_shuffle_sections: assessment_is_shuffle_sections,
            is_shuffle: assessment_is_shuffle,
            intro_video: assessment_intro_video || "",
        },
        mode: 'onChange'
    })
    const clearIntroVideo = () => {
        setValue('intro_video', undefined)
    }

    const introVideo = watch('intro_video')

    const handleVideoUpload = (file: File) => {
        const reader = new FileReader()
        reader.onloadend = () => {
            const base64String = reader.result as string
            setValue('intro_video', base64String)
        }
        reader.readAsDataURL(file)
    }

    const togglePlay = () => {
        if (videoRef.current) {
            if (isPlaying) {
                videoRef.current.pause()
            } else {
                videoRef.current.play()
            }
            setIsPlaying(!isPlaying)
        }
    }





    const queryClient = useQueryClient()
    const { mutate: generateIntroMessageMutation, isLoading } = useMutation(
        () => Axios.get<string>(`assessments/generate-intro-message/${assessment_id}`),
        {
            onSuccess: (result) => {
                setValue('description', result.data)
            }
        }
    )

    const { registerValidation } = useAssessmentConfigurationValidation();
    const { mutate: updateAssessment } = useAssesmentCreatorPartialUpdate()
    React.useEffect(() => {
        const subscription = watch((value, { name, type }) => {
            updateStore({
                name: value.name,
                description: value.description,
                is_shuffle_sections: value.is_shuffle_sections,
                is_shuffle: value.is_shuffle,
            });
        });

        const validate = async () => {
            const canGoOn = await trigger();
            if (canGoOn) {
                updateAssessment(
                    {
                        assessment_id,
                        data: {
                            name: watch("name"),
                            description: watch("description"),
                            is_shuffle_sections: watch("is_shuffle_sections"),
                            is_shuffle: watch("is_shuffle"),
                            intro_video_url: !watch("intro_video")?.startsWith('https://res.cloudinary.com') ? watch('intro_video') : undefined
                        }
                    },
                    {
                        onSuccess: () => {
                            queryClient.invalidateQueries({
                                queryKey: ['get-assessment', assessment_id]
                            })
                        },
                    })
                return true
            }
            else return false
        }
        registerValidation("0", validate, "Check your inputs in assessment details sections");
        return () => subscription.unsubscribe();

    }, [registerValidation, watch]);

    return (
        <form className="space-y-6">
            <div>
                <h2 className="text-lg font-medium text-primary">Assessment details</h2>
                <p className="helper-text text-xs">
                    See and update your assessment details and guidelines
                </p>
            </div>
            <Controller
                name="name"
                control={control}
                render={({ field }) => (
                    <Input
                        type="text"
                        labelClass="text-header-text"
                        className="h-10"
                        label="Assessment name"
                        placeholder="Assessment name"
                        hasError={!!errors.name}
                        errorMessage={errors.name?.message}
                        variant="showcase"
                        {...field}
                    />
                )}
            />

            <div>
                <div className="flex items-center justify-between gap-4 mb-2">
                    <label htmlFor="assessment_intro" className='text-sm text-header-text font-medium'>Assessment Intro message / description</label>
                    <Button
                        className="text-sm"
                        variant="light"
                        size="capsule"
                        type="button"
                        onClick={(e) => {
                            e.preventDefault()
                            e.stopPropagation()
                            generateIntroMessageMutation()
                        }}
                    >
                        Generate intro message
                        {
                            isLoading && <LoaderBtn />
                        }
                    </Button>
                </div>
                <Controller
                    name="description"
                    control={control}

                    render={({ field }) => (
                        <Textarea
                            id="assessment_intro"
                            placeholder="Enter details of this assessment to guide candidates"
                            hasError={!!errors.description}
                            errorMessage={errors.description?.message}
                            variant="showcase"
                            maxLength={400}
                            {...field}
                        />
                    )}
                />
                <p className="text-right text-xxs mt-1">
                    {watch('description')?.length || 0}/400 characters remaining
                </p>
            </div>

            <div className="space-y-2">
                <label className="text-header-text text-sm font-medium">
                    Upload intro video <span className="helper-text">(Optional)</span>
                </label>
                <FileUpload
                    accept={{ 'video/mp4': [] }}
                    maxSizeMB={30}
                    onFileSelect={(file) => {
                        if (file) {
                            handleVideoUpload(file)
                        }
                        else return
                    }}
                    className="mt-3"
                />

                {introVideo && (
                    <div className="group/intro-video relative flex items-center justify-center md:w-[40%]">
                        <video
                            className="relative max-h-[200px] w-full rounded-xl object-cover"
                            src={introVideo}
                            autoPlay={false}
                            ref={videoRef}
                            onEnded={() => {
                                setIsPlaying(false)
                                setCurrentVideoTime('0:00')
                            }}
                        >
                            <span className="absolute left-0 top-0 m-4 rounded-md bg-black/20 p-4">
                                {currentVideoTime}
                            </span>
                        </video>
                        <div className="absolute inset-0 hidden group-hover/intro-video:block">
                            <button
                                className="absolute inset-0 z-10 flex items-center justify-center"
                                onClick={togglePlay}
                                type="button"
                            >
                                {isPlaying ? <Pause className="text-primary" variant="Bold" size={50} /> : <Play className="text-primary" variant="Bold" size={50} />}
                            </button>
                            <button
                                onClick={clearIntroVideo}
                                className="absolute right-0 top-0 z-10 m-4"
                                type="button"
                            >
                                <span
                                    title="clear video"
                                    className="flex aspect-square w-7 items-center justify-center rounded-full bg-white text-lg"
                                >
                                    &times;
                                </span>
                            </button>
                        </div>
                    </div>
                )}
            </div>

            <Controller
                name="is_shuffle_sections"
                control={control}
                render={({ field }) => (
                    <label className="flex items-start justify-between gap-4 rounded-xl border p-4 cursor-pointer">
                        <div className="space-y-1">
                            <h3 className="text-sm font-medium text-header-text">Do you want sections shuffling?</h3>
                            <p className="max-w-[80%] text-xs text-body-text">
                                If you toggle this on, the candidates would all have their sections
                                ordered randomly.
                            </p>
                        </div>
                        <Switch
                            checked={field.value}
                            setChecked={(state) => field.onChange(state)}
                        />
                    </label>
                )}
            />

            <Controller
                name="is_shuffle"
                control={control}
                render={({ field }) => (
                    <label className="flex items-start justify-between gap-4 rounded-xl border p-4 cursor-pointer">
                        <div className="space-y-1">
                            <h3 className="text-sm font-medium text-header-text">Do you want question shuffling?</h3>
                            <p className="max-w-[80%] text-xs text-body-text">
                                If you toggle this on, the candidates would all have their questions
                                ordered randomly.
                            </p>
                        </div>
                        <Switch
                            checked={field.value}
                            setChecked={(state) => field.onChange(state)}
                        />
                    </label>
                )}
            />
        </form>
    )
}

export default AssessmentDetail

