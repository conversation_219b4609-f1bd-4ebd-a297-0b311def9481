import { TProctoringToleranceOption } from "../../types";

interface TProctoringToleranceOptionWithModalDescription extends TProctoringToleranceOption {
    modal_description: string;
}
export const ASSESSMENT_CREATOR_PROCTORING_TOLERANCE_OPTIONS: TProctoringToleranceOptionWithModalDescription[] = [
    {
        id: 'full_screen_tolerance_level',
        title: 'full screen mode',
        description:
            'All assessment are to be done within the full screen mode, in a case where candidates exit the full screen',
        modal_description:
            "Full screen mode restricts candidates from the ability to change tabs thereby mitigating cheating chances. Determine the tolerance level of how many times you would like to permit candidates on exit full screen mode.",
    },
    {
        id: 'window_change_tolerance_level',
        title: 'Window change',
        description:
            'All assessment are to be done within one window, in a case where candidates attempt to change a window',
        modal_description: "Window change restricts candidates from the ability to open and switch between another window thereby mitigating cheating chances.Determine the tolerance level of how many times you would like to permit candidates on attempt opening or switch window.",
    },

    {
        id: 'tab_change_tolerance_level',
        title: 'Tab change',
        description:
            'All assessments must be completed within a single browser tab. If a candidate switches tabs',
        modal_description: `All assessments must be completed within a single browser tab. If a candidate switches tabs`,
    },
    {
        id: 'multi_face_tolerance_level',
        title: 'Multiple face',
        description:
            'Only one face should be visible during the assessment. If multiple faces are detected',
        modal_description:
            'Only one face should be visible during the assessment. If multiple faces are detected',
    },
    {
        id: 'different_face_tolerance_level',
        title: 'Different face',
        description:
            'Assessments require consistent facial recognition. If a different face is detected during ',
        modal_description:
            'Assessments require consistent facial recognition. If a different face is detected during ',
    },
];

export const ASSESSMENT_CREATOR_PROCTORING_SECTION_OPTIONS = [
    {
        id: 'individual_tolerance_settings',
        title: 'Individual tolerance settings',
        name: 'Individual setup',
        description:
            'Here you can  configure Proctoring options on an individual basis',
    },
    {
        id: 'bulk_tolerance_settings',
        title: 'Bulk tolerance settings',
        name: 'Bulk tolerance setup',

        description:
            'Here you can combine multiple Proctoring options and set same tolerance count across all Proctoring options. ',
    },
    {
        id: 'other_setup_options',
        title: 'Other assessment settings',
        name: 'Other setup options',

        description:
            'Here, you can tap the toggle below activate or deactivate the default settings for each assessment, allowing you to customize the assessment experience to meet specific requirements.',
    },
];