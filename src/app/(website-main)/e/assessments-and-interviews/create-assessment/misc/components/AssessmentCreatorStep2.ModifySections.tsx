import React from 'react'
import { Reorder } from "framer-motion";
import toast from 'react-hot-toast';

import { useQueryClient } from '@tanstack/react-query';

import useAssessmentCreatorStore from '../store';
import { UseGetAssessmentRecommendation, useReorderSections } from '../api';
import { TRecommendedAssessmentQuestionPack } from '../api/postAssessmentRecommendations';
import { useCreateAssessmentValidation } from '../contexts';
import AssessmentCreatorModifySectionCard from './AssessmentCreatorModifySectionCard';
import AssessmentCreatorModifySectionDetails from './AssessmentCreatorModifySectionDetails';


const AssessmentCreatorStep2 = () => {
    const { mutate: getRecommendations, isLoading: isGettingRecommendations } = UseGetAssessmentRecommendation()
    const { role, role_level, is_custom } = useAssessmentCreatorStore(state => ({
        role: state.role,
        role_level: state.role_level,
        is_custom: state.is_custom
    }));
    const sections = useAssessmentCreatorStore(state => state.sections)
    const assessmentId = useAssessmentCreatorStore(state => state.id)
    const [activeSectionId, setActiveSectionId] = React.useState(sections[0]?.id || '0');
    const activeSection = React.useMemo(() => {
        return sections.find((section) => section.id == activeSectionId) || undefined
    }, [activeSectionId, sections])

    const [searchTerm, setSearchTerm] = React.useState('')
    const [recommendations, setRecommendations] = React.useState<TRecommendedAssessmentQuestionPack[]>([])
    const [filteredRecommendations, setFilteredRecommendations] = React.useState<TRecommendedAssessmentQuestionPack[]>([])

    React.useEffect(() => {
        if (is_custom) {
        }
        else {

            getRecommendations(
                { role_id: role.id, role_level: role_level },
                {
                    onSuccess: (data) => {
                        setRecommendations(data)
                        setFilteredRecommendations(data)
                    }
                }
            )
        }
    }, [])


    React.useEffect(() => {
        if (searchTerm.trim() !== '') {
            const filteredRecommendations = recommendations.filter(recommendation => {
                return (
                    recommendation.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    recommendation.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    recommendation.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
                );
            });
            setFilteredRecommendations(filteredRecommendations);
        }
        else {
            setFilteredRecommendations(recommendations)
        }
    }, [searchTerm])

    const { registerValidation } = useCreateAssessmentValidation();

    React.useEffect(() => {
        if (sections.length) {
            if (activeSection) {
                setActiveSectionId(activeSection.id)
            } else {
                setActiveSectionId(sections[0].id)
            }
            registerValidation(2, async () => {
                return true;
            });
        } else {
            registerValidation(2, async () => {
                return false;
            },
                "Please add at least one section to proceed"
            )
        }
    }, [registerValidation, sections]);

    const { mutate: reorderSections } = useReorderSections()
    const queryClient = useQueryClient()

    return (
        <div className="grid grid-rows-[max-content,1fr] h-full overflow-y-hidden space-y-2">
            <header className="p-1.5 px-3">
                <p className='text-sm text-body-text'>
                    You can create your custom questions and set score and test duration
                    or select more from out questions library
                </p>
            </header>
            <section className="relative grid lg:grid-cols-2 px-4 gap-5 max-h-full overflow-y-scroll pt-1">
                <Reorder.Group
                    axis="y"
                    animate="true"
                    dragElastic={1}
                    className="flex flex-col gap-4 md:sticky top-0 h-max"
                    values={sections.map(section => section.id)}
                    onReorder={(newOrder) => {
                        reorderSections({
                            assessment_id: assessmentId,
                            new_order: newOrder
                        }, {
                            onSuccess: () => {
                                toast.success('Sections reordered successfully')
                                queryClient.invalidateQueries({
                                    queryKey: ['get-assessment', assessmentId]
                                })
                            },
                            onError: () => {
                                toast.error('Failed to reorder sections')
                            }

                        })
                    }}
                >
                    {
                        sections?.map((section, index) => (
                            <AssessmentCreatorModifySectionCard
                                key={section.id}
                                section={section}
                                index={index}
                                isActive={section.id === activeSectionId}
                                onClick={() => setActiveSectionId(section.id)}
                            />
                        ))
                    }
                </Reorder.Group>

                <AssessmentCreatorModifySectionDetails
                    section={sections.find((section) => section.id == activeSectionId) || undefined}
                    index={sections.findIndex((section) => section.id == activeSectionId)}
                    isActive={activeSectionId === activeSectionId}
                    onClick={() => { }}
                />
            </section>

        </div>
    )
}

export default AssessmentCreatorStep2