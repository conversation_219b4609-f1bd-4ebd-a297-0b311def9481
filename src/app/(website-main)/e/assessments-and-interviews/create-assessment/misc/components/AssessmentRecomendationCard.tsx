import React from 'react'
import { TAssessmentQuestionPack } from '../../../misc/components/CreateQuestionPackStep3NamedPackModal';
import { TRecommendedAssessmentQuestionPack } from '../api/postAssessmentRecommendations';
import { convertKebabAndSnakeToTitleCase, truncateToLength } from '@/utils/strings';
import { TAssessmentSection } from '../types';
import { Button } from '@/components/shared';
import AddQuestionsToSectionFromPacksModal from './AddQuestionsToSectionFromPacksModal';
import { useBooleanStateControl } from '@/hooks';
import AssessmentRecomendationSheet from './AssessmentRecomendationSheet';
import { TQuestionPacks } from '../api/getCustomQuestionPacks';

interface AssessmentCreatorContentProps {
    question_pack: TRecommendedAssessmentQuestionPack | TQuestionPacks
    active_section: TAssessmentSection
    isSectionSelected?: boolean
    is_custom?: boolean
}

const AssessmentRecomendationCard: React.FC<AssessmentCreatorContentProps> = ({ is_custom, question_pack, active_section, isSectionSelected }) => {
    const {
        state: isAddQuestionsModalOpen,
        setTrue: openSelectQuestionsModal,
        setFalse: closeSelectQuestionsModal
    } = useBooleanStateControl()

    const {
        state: isDetailsSheetOpen,
        setTrue: openDetailsSheet,
        setFalse: closeDetailsSheet
    } = useBooleanStateControl()

    // console.log(question_pack.question_types)

    return (
        <article className='h-full rounded-md bg-white'>
            <div className="flex flex-col gap-y-4 relative rounded-xl overflow-hidden p-3.5 h-full">
                <span className="absolute rounded-none rounded-bl-xl bg-primary-light text-xxs py-1 px-5 text-primary top-0 right-0">
                    My pack
                </span>

                <h2 className="text- font-medium text-header-text h-[2lh] mt-2 overflow-hidden text-ellipsis" title={question_pack.name}>
                    {question_pack.name}
                </h2>
                <p className="text-xs text-body-text h-[5lh] overflow-hidden grow">{question_pack.description}</p>
                <section className="space-y-2.5">
                    <p className="text-sm text-header-text font-medium">
                        <span className="text-helper-text text-sm font-normal">
                            Total questions:
                        </span>{" "}
                        {question_pack.total_questions}
                    </p>
                    <ul className="flex items-center flex-wrap gap-x-2 gap-y-1.5 text-xs min-h-[2lh]">
                        {

                            !!question_pack.question_types ?
                                Object.entries(question_pack.question_types)?.map(
                                    ([type, number], index) => (
                                        <li className="bg-primary-light rounded-full px-3 py-1.5 text-xxs text-primary" key={index}>
                                            <span className="text-ellipsis overflow-hidden whitespace-nowrap">
                                                {convertKebabAndSnakeToTitleCase(type)}:{" "}
                                                <span className="font-semibold">
                                                    {number}
                                                </span>
                                            </span>
                                        </li>
                                    ),
                                )
                                :
                                'tags' in question_pack && question_pack.tags?.map(
                                    (item) => (
                                        <li className="bg-primary-light rounded-full px-3 py-1.5 text-xxs text-primary" key={item}>
                                            <span className="max-w-[5ch] text-ellipsis overflow-hidden whitespace-nowrap">
                                                {truncateToLength(item, 15)}
                                            </span>
                                        </li>
                                    ),
                                )
                        }

                    </ul>
                    <footer className="flex gap-2 justify-end text-xs mt-auto">

                        <Button
                            type="button"
                            onClick={openDetailsSheet}
                            size="tiny"
                            variant="extralight"
                        >
                            Details
                        </Button>

                        <Button
                            type="button"
                            onClick={openSelectQuestionsModal}
                            size="tiny"
                            disabled={!isSectionSelected}
                            title={isSectionSelected ? 'Select questions' : 'Select a section to add questions'}
                        >
                            Add
                        </Button>

                    </footer>
                    {
                        !isSectionSelected &&
                        <p className="text-red-500 text-xs">
                            Select a section to add questions
                        </p>
                    }
                </section>
            </div>

            <AddQuestionsToSectionFromPacksModal
                isModalOpen={isAddQuestionsModalOpen}
                closeModal={closeSelectQuestionsModal}
                question_pack={question_pack}
                section_id={active_section.id}
                is_custom={is_custom}
            />
            <AssessmentRecomendationSheet
                isSheetOpen={isDetailsSheetOpen}
                closeSheet={closeDetailsSheet}
                question_pack={question_pack}
            />
        </article>
    )
}

export default AssessmentRecomendationCard

//     < QuestionPackDetailModal
// close = { closeQuestionPackDetailModal }
// is_open = { is_question_pack_detail_modal_open }
// questionPack = { active_question_pack }
// setCurrentQuestionPack = {() => setActiveQuestionPack(question_pack)}
// openDrawer = { openQuestionPackDetailModal }
//     />