import React, { useState } from 'react';
import { Input, Select } from '@/components/shared';
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/shared/select';

type TimeUnit = 'minutes' | 'hours' | 'days' | 'weeks';

interface TimeLimitInputProps {
    value: number;
    onChange: (value: number) => void;
    hasError?: boolean
    errorMessage?: string
}

const TIME_UNITS: { [key in TimeUnit]: number } = {
    minutes: 60,
    hours: 3600,
    days: 86400,
    weeks: 604800,
};

export const TimeLimitInput: React.FC<TimeLimitInputProps> = ({ value, onChange, hasError, errorMessage }) => {
    const [timeUnit, setTimeUnit] = useState<TimeUnit>('minutes');

    const handleValueChange = (inputValue: string) => {
        const numericValue = Number(inputValue);
        if (!isNaN(numericValue)) {
            onChange(numericValue * TIME_UNITS[timeUnit]);
        }
    };

    const handleUnitChange = (newUnit: TimeUnit) => {
        setTimeUnit(newUnit);
        // Convert the current value to the new unit
        const currentSeconds = value;
        const newValue = Math.round(currentSeconds / TIME_UNITS[newUnit]);
        onChange(newValue * TIME_UNITS[newUnit]);
    };

    const displayValue = Math.round(value / TIME_UNITS[timeUnit]);

    return (
        <div className="flex items-center space-x-2">
            <Input
                type="number"
                value={displayValue}
                onChange={(e) => handleValueChange(e.target.value)}
                className="w-24"
                hasError={hasError}
                errorMessage={errorMessage}
            />
            <Select
                placeholder='Select unit'
                name='time-unit'
                defaultValue={timeUnit}
                options={[
                    { name: 'Minutes', value: 'minutes' },
                    { name: 'Hours', value: 'hours' },
                    { name: 'Days', value: 'days' },
                    { name: 'Weeks', value: 'weeks' },
                ]}
                labelKey="name"
                valueKey="value"
                onChange={(value) => handleUnitChange(value as TimeUnit)}
                className="w-[110px] text-sm h-10"
            />

        </div>
    );
};
