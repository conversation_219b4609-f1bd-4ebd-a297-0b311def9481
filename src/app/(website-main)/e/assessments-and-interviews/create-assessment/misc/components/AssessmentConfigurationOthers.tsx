'use client';

import { Input, Select } from '@/components/shared';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';
import { useAssesmentCreatorPartialUpdate } from '../api';
import { useAssessmentConfigurationValidation } from '../contexts/step3';
import useAssessmentCreatorStore from '../store';
import { TabValidationStates } from './AssessmentCreatorStep3Configure';
import { TimeLimitInput } from './TimeLimitInput';


import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { PickerValue } from "@mui/x-date-pickers/internals";
import dayjs from "dayjs";


export const AssessmentConfigurationOthersSchema = z
  .object({
    commencement_settings: z.enum(['yes', 'no']),
    time_limit: z.number().min(1, 'Time limit must be at least 1 minute'),
    reminder_time: z.number().min(1, 'Reminder time must be at least 1 hour'),
    start_time: z.date(),
    deadline: z.date().optional(),
  })
  .refine(
    data => {
      if (data.commencement_settings === 'yes' && data.deadline) {
        return data.start_time < data.deadline;
      }
      return true;
    },
    {
      message: 'Start time must be before deadline',
      path: ['start_time'],
    }
  )
  .refine(
    data => {
      if (data.commencement_settings === 'yes' && !data.deadline) {
        return false;
      }
      return true;
    },
    {
      message:
        'Deadline must be set if you have selected yes for commencement settings',
      path: ['deadline'],
    }
  )
  .refine(
    data => {
      const today = new Date();
      if (data.deadline) {
        return data.deadline > today;
      }
      return true;
    },
    {
      message: 'Deadline must be in the future',
      path: ['deadline'],
    }
  );

export type AssessmentConfigurationOthersFormData = z.infer<
  typeof AssessmentConfigurationOthersSchema
>;

interface AssessmentConfigurationOthersProps {
  setTabValidationStates: React.Dispatch<
    React.SetStateAction<TabValidationStates>
  >;
}

const AssessmentConfigurationOthers: React.FC<
  AssessmentConfigurationOthersProps
> = ({ }) => {
  const assessment_id = useAssessmentCreatorStore(state => state.id);
  const time_limit = useAssessmentCreatorStore(state => state.time_limit);
  const reminder_time = useAssessmentCreatorStore(state => state.reminder_time);
  const start_time = useAssessmentCreatorStore(state => state.start_time);
  const deadline = useAssessmentCreatorStore(state => state.deadline);
  const commencement_settings = useAssessmentCreatorStore(
    state => state.commencement_settings
  );
  const updateStore = useAssessmentCreatorStore(state => state.updateStore);

  const {
    control,
    watch,
    trigger,
    formState: { errors, isValid },
    setError,
    clearErrors,
    setValue,
  } = useForm<AssessmentConfigurationOthersFormData>({
    resolver: zodResolver(AssessmentConfigurationOthersSchema),
    defaultValues: {
      commencement_settings: commencement_settings ? 'yes' : 'no',
      time_limit,
      reminder_time: reminder_time || 1,
      start_time: new Date(start_time || Date.now() - 1 * 24 * 60 * 60 * 1000),
      deadline: new Date(deadline || Date.now() + 3 * 24 * 60 * 60 * 1000),
    },
    mode: 'onChange',
  });

  const commencementSetting = watch('commencement_settings');
  const startTime = watch('start_time');
  const deadlineTime = watch('deadline');

  const queryClient = useQueryClient();
  const { registerValidation } = useAssessmentConfigurationValidation();
  const { mutate: updateAssessment } = useAssesmentCreatorPartialUpdate();

  React.useEffect(() => {
    if (commencementSetting === 'yes' && !deadline) {
      updateStore({
        deadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
      });
    }
    if (commencementSetting === 'yes' && startTime && deadlineTime) {
      if (startTime >= deadlineTime) {
        setError('start_time', {
          type: 'manual',
          message: 'Start time must be before deadline',
        });
      } else {
        clearErrors('start_time');
      }
    }
  }, [commencementSetting, startTime, deadlineTime, setError, clearErrors]);

  React.useEffect(() => {
    const subscription = watch((value, { name, type }) => {
      ////Doing this because for some reason, the time is being converted to UTC +00:00
      ////So no matter the time I set, it always shows 1 hour ahead(cos hee's UTC +1)
      const local_start_time = value.start_time
        ? `${format(
          value.start_time,
          'yyyy-MM-dd'
        )}T${value.start_time.toLocaleTimeString(undefined, {
          hourCycle: 'h23',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        })}`
        : undefined;

      const local_deadline_time = value.deadline
        ? `${format(
          value.deadline,
          'yyyy-MM-dd'
        )}T${value.deadline.toLocaleTimeString(undefined, {
          hourCycle: 'h23',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        })}`
        : undefined;

      updateStore({
        time_limit: value.time_limit,
        reminder_time: value.reminder_time,
        start_time: local_start_time,
        deadline: local_deadline_time,
        commencement_settings: value.commencement_settings == 'yes',
      });
      if (isValid) {
        updateAssessment(
          {
            assessment_id,
            data: {
              time_limit: value.time_limit,
              reminder_time: value.reminder_time,
              start_time: local_start_time,
              deadline: local_deadline_time,
              commencement_settings: value.commencement_settings == 'yes',
            },
          },
          {
            onSuccess: () => {
              queryClient.invalidateQueries({
                queryKey: ['get-assessment', assessment_id],
              });
            },
          }
        );
      }
    });
    const validate = async () => {
      const canGoOn = await trigger();
      if (canGoOn) {
        ////Doing this because for some reason, the time is being converted to UTC +00:00
        ////So no matter the time I set, it always shows 1 hour ahead(cos hee's UTC +1)
        const local_start_time = `${format(
          startTime,
          'yyyy-MM-dd'
        )}T${startTime.toLocaleTimeString(undefined, {
          hourCycle: 'h23',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        })}`;
        const local_deadline_time = deadlineTime
          ? `${format(
            deadlineTime,
            'yyyy-MM-dd'
          )}T${deadlineTime.toLocaleTimeString(undefined, {
            hourCycle: 'h23',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
          })}`
          : undefined;

        const data = {
          time_limit: watch("time_limit"),
          reminder_time: watch("reminder_time"),
          start_time: local_start_time,
          deadline: local_deadline_time,
          commencement_settings: watch("commencement_settings") == 'yes',
        }
        updateStore(data)
        updateAssessment(
          {
            assessment_id,
            data
          },
          {
            onSuccess: () => {
              queryClient.invalidateQueries({
                queryKey: ['get-assessment', assessment_id]
              })
            },
          }
        )
        return true
      }
      else return false
    }

    registerValidation("3", validate, "Check your inputs in 'Other Settings' section");
    return () => subscription.unsubscribe();
  }, [registerValidation, watch, trigger, updateStore, updateAssessment, assessment_id, queryClient]);



  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>

      <form className="space-y-6">
        <div>
          <h2 className="text-lg font-medium text-primary">Other settings</h2>
          <p className="helper-text text-xs">
            View and configure timer and deadline for this assessment
          </p>
        </div>
        <div className="space-y-4">
          <div className="space-y-1">
            <h3 className="text-sm font-semibold text-header-text">
              Commencement settings
            </h3>
            <p className="text-xs text-body-text">
              Select <span className="font-medium text-header-text">'YES'</span>{' '}
              if you wish for candidates to complete the assessment at their
              convenience within a designated time frame, or choose{' '}
              <span className="font-medium text-header-text">'NO'</span> if you
              prefer them to commence the assessment at the specific time and date
              you have specified.
            </p>
          </div>
          <Controller
            name="commencement_settings"
            control={control}
            render={({ field }) => (
              <Select
                hasError={!!errors.commencement_settings}
                errorMessage={errors.commencement_settings?.message}
                placeholder="Select commencement setting"
                options={[
                  { name: 'Yes', value: 'yes' },
                  { name: 'No', value: 'no' },
                ]}
                labelKey="name"
                valueKey="value"
                className="h-10 max-w-[200px] text-sm"
                variant="showcase"
                {...field}
              />
            )}
          />
        </div>

        <section className="space-y-4">
          <div className="space-y-1">
            <h3 className="text-sm font-semibold text-header-text">
              Set assessment time limit
            </h3>
            <p className="text-xs text-body-text">
              Once started, candidates are required to submit assessment within
              this time.
            </p>
          </div>
          <Controller
            name="time_limit"
            control={control}
            defaultValue={0}
            render={({ field }) => (
              <TimeLimitInput
                value={field.value}
                onChange={field.onChange}
                hasError={!!errors.time_limit}
                errorMessage={errors.time_limit?.message}
              />
            )}
          />
        </section>

        <section className="space-y-4">
          <div className="space-y-1">
            <h3 className="text-sm font-semibold text-header-text">
              Reminder Time
            </h3>
            <p className="text-xxs text-body-text md:text-[0.735rem]">
              How many hours from now should the reminder be sent
            </p>
          </div>
          <Controller
            name="reminder_time"
            control={control}
            render={({ field }) => (
              <Input
                type="number"
                {...field}
                onChange={e => field.onChange(Number(e.target.value))}
                placeholder="Reminder time in hours"
                className="max-w-[350px] text-sm"
                variant="showcase"
              />
            )}
          />
        </section>

        <section className="space-y-4">
          <div className="space-y-1">
            <h3 className="text-sm font-semibold text-header-text">
              Date and time settings
            </h3>
            <p className="text-xxs text-body-text md:text-[0.735rem]">
              Specify the start and end date and time for candidates to start and
              complete this assessment.
            </p>
          </div>
          {/* <Controller
          name="start_time"
          control={control}
          render={({ field }) => (
            // <DateTimePicker
            //   label="Start"
            //   className="max-w-[350px] text-sm"
            //   value={field.value}
            //   onChange={field.onChange}
            //   hasError={!!errors.start_time}
            //   errorMessage={errors.start_time?.message}
            //   variant="showcase"
            //   disabledDays={{ before: new Date() }}
            // />
            <DateTimePicker
            value={dayjs(field.value)}
            minDateTime={dayjs()}
            defaultValue={dayjs((new Date()).toISOString())}
             onChange={() => {}}
            className="rounded-medium text-smmax-w-[350px]"
          />
          )}
        /> */}

        <div className="flex flex-col gap-y-4 w-max">

          <Controller
            name="start_time"
            control={control}
            render={({ field }) => (
              <DateTimePicker
                value={field.value ? dayjs(field.value) : null}   // keep synced with RHF
                onChange={(newValue) => field.onChange(newValue?.toISOString())}
                minDateTime={dayjs()}  // prevents past dates
                label="Start"

                 timeSteps={{ minutes: 1 }} 
                className='flex'
                slotProps={{
                  textField: {
                    error: !!errors.start_time,
                    helperText: errors.start_time?.message,
                    className: "max-w-[350px] text-sm",
                  },
                }}
              />
            )}
          />

          {
            commencementSetting == 'yes' && (
              <Controller
                name="deadline"
                control={control}
                render={({ field }) => (
                  <DateTimePicker
                    value={field.value ? dayjs(field.value) : null}   // keep synced with RHF
                    onChange={(newValue) => field.onChange(newValue?.toISOString())}
                    minDateTime={dayjs(start_time)}  // prevents past dates
                 timeSteps={{ minutes: 1 }} 
                  
                    label="End"
                    slotProps={{
                      textField: {
                        error: !!errors.deadline,
                        helperText: errors.deadline?.message,
                        className: "max-w-[350px] text-sm",
                      },
                    }}
                  />
                )}
              />

            )
          }
</div>
          {/* {commencementSetting === 'yes' && (
          <Controller
            name="deadline"
            control={control}
            render={({ field }) => (
              <></>
              // <DateTimePicker
              //   label="End"
              //   className="max-w-[350px] text-sm "
              //   value={field.value!}
              //   onChange={field.onChange}
              //   hasError={!!errors.deadline}
              //   errorMessage={errors.deadline?.message}
              //   variant="showcase"
              // />
            )}
          />
        )} */}
        </section>
      </form>
    </LocalizationProvider>
  );
};

export default AssessmentConfigurationOthers;
