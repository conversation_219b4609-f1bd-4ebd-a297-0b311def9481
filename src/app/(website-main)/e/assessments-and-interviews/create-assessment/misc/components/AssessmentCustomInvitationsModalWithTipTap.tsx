import React, {
  FormEvent,
  useCallback,
  useEffect,
  useReducer,
  useRef,
  useState,
} from 'react';
import '@/styles/quill.css';
import { Node } from '@tiptap/core';
import BulletList from '@tiptap/extension-bullet-list';
import { default as TipTapLink } from '@tiptap/extension-link';
import ListItem from '@tiptap/extension-list-item';
import OrderedList from '@tiptap/extension-ordered-list';
import TextAlign from '@tiptap/extension-text-align';
import Underline from '@tiptap/extension-underline';
import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import {
  AlignCenter,
  AlignLeft,
  AlignRight,
  Bold,
  Italic,
  LinkIcon,
  List,
  ListOrdered,
  Strikethrough,
  Underline as UnderlineIcon,
  Unlink,
  Upload,
  UploadIcon,
} from 'lucide-react';
import Image from 'next/image';
import { useDropzone } from 'react-dropzone';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  FileUpload,
  Input,
  Label,
  Modal,
  Separator,
  ToggleGroup,
  ToggleGroupItem,
} from '@/components/shared';
import { blobToBase64 } from '@/lib/utils/functions';
// import { Separator } from "@/components/ui/separator"
import { cn } from '@/utils';

type FormDataType = {
  subject: string;
  message: string;
  cover_image: Blob | undefined;
};

interface Props {
  isModalOpen: boolean;
  closeModal: () => void;
  coverImageURL?: string
  coverImageFile: File | null;
  setCoverImageFile: (file: File | null) => void;
  emailSubject: string;
  setEmailSubject: (subject: string) => void;
  emailMessage: string;
  setEmailMessage: (message: string) => void;
}

const TokenNode = Node.create({
  name: 'token',

  group: 'inline',

  inline: true,

  addAttributes() {
    return {
      value: {
        default: null,
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'span.token-placeholder',
        getAttrs: dom => ({
          value: dom.getAttribute('data-value'),
        }),
      },
    ];
  },

  renderHTML({ node }) {
    return [
      'span',
      { class: 'token-placeholder', 'data-value': node.attrs.value },
      `{{ ${node.attrs.value} }}`,
    ];
  },
});

const EditOfferLetterModal: React.FC<Props> = ({
  isModalOpen,
  closeModal,
  coverImageURL,
  coverImageFile,
  setCoverImageFile,
  emailSubject,
  setEmailSubject,
  emailMessage,
  setEmailMessage,
}) => {
  const [isLoadingImageFromURL, setIsLoadingImageFromURL] = useState(false);
  const [urlDerivedFile, setUrlDerivedFile] = useState<File | null>(null);

  // Function to convert URL to File object for display purposes only
  const createFileFromURL = async (url: string, filename: string = 'cover-image.jpg'): Promise<File> => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      return new File([blob], filename, { type: blob.type || 'image/jpeg' });
    } catch (error) {
      console.error('Error creating file from URL:', error);
      throw error;
    }
  };

  // Effect to convert coverImageURL to File for display purposes only
  useEffect(() => {
    if (coverImageURL && !coverImageFile) {
      setIsLoadingImageFromURL(true);
      createFileFromURL(coverImageURL)
        .then(file => {
          setUrlDerivedFile(file); // Store separately, don't set as coverImageFile
        })
        .catch(error => {
          console.error('Failed to load image from URL:', error);
        })
        .finally(() => {
          setIsLoadingImageFromURL(false);
        });
    } else if (coverImageFile) {
      // Clear URL-derived file when user uploads a new file
      setUrlDerivedFile(null);
    }
  }, [coverImageURL, coverImageFile]);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: false,
        orderedList: false,
        listItem: false,
      }),
      TokenNode,
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      BulletList,
      OrderedList,
      ListItem,
      TipTapLink.configure({
        openOnClick: false,
        autolink: true,
        defaultProtocol: 'https',
      }),
    ],
    content: emailMessage || '', // Set initial content
    onUpdate: ({ editor }) => {
      setEmailMessage(convertPlaceholdersToTokens(editor.getHTML()));
    },
  });

  // Update editor content when emailMessage prop changes
  useEffect(() => {
    if (editor && emailMessage && editor.getHTML() !== emailMessage) {
      // Convert tokens back to placeholder spans for the editor
      const contentWithPlaceholders = convertTokensToPlaceholders(emailMessage);
      editor.commands.setContent(contentWithPlaceholders);
    }
  }, [editor, emailMessage]);
  const tokens = [
    { value: 'assessment_name', title: 'assessment name' },
    { value: 'start_time', title: 'start time' },
    { value: 'invitation_link', title: 'invitation link' },
    { value: 'candidate_name', title: 'candidate name' },
    { value: 'candidate_email', title: 'candidate email' },
    { value: 'job_position', title: 'job position' },
    { value: 'company_name', title: 'company name' },
    { value: 'time_limit', title: 'time limit' },
    { value: 'start_date', title: 'start date' },
    { value: 'deadline', title: 'deadline' },
    { value: 'auth_method1', title: 'auth method1' },
    { value: 'auth_method2', title: 'auth method2' },
    { value: 'auth_method1_value', title: 'auth method 1 value' },
    { value: 'auth_method2_value', title: 'auth method 2 value' },
  ];

  const insertToken = (token: string) => {
    if (editor) {
      editor.commands.insertContent(
        `<span class="token-placeholder" data-value="${token}">{{ ${token} }}</span>`
      );
      // editor.commands.insertContent(`<span class="token-placeholder" data-value="${token}"> {{ ${token} }} </span>`);
    }
  };
  const convertPlaceholdersToTokens = (htmlContent: string) => {
    const parser = new DOMParser();
    const dom = parser.parseFromString(htmlContent, 'text/html');

    // Find all elements with the class name for token placeholders
    dom.querySelectorAll('.token-placeholder').forEach(tokenElement => {
      const tokenValue = (tokenElement as HTMLElement).dataset.value;
      tokenElement.replaceWith(`{{ ${tokenValue} }}`); // format
    });

    return dom.body.innerHTML;
  };

  const convertTokensToPlaceholders = (htmlContent: string) => {
    // Convert {{ token }} format back to placeholder spans for the editor
    return htmlContent.replace(
      /\{\{\s*([^}]+)\s*\}\}/g,
      '<span class="token-placeholder" data-value="$1">{{ $1 }}</span>'
    );
  };

  const [subjectError, setSubjectError] = useState(false);
  const [messageError, setMessageError] = useState(false);
  const [coverImageError, setCoverImageError] = useState(false);

  const [, forceUpdate] = useReducer(x => x + 1, 0);

  useEffect(() => {
    if (editor) {
      editor.on('selectionUpdate', () => {
        forceUpdate();
      });
    }
  }, [editor]);

  const setLink = useCallback(() => {
    if (editor) {
      const previousUrl = editor.getAttributes('link').href;
      const url = window.prompt('URL', previousUrl);

      if (url === null) {
        return;
      }

      if (url === '') {
        editor.chain().focus().extendMarkRange('link').unsetLink().run();
        return;
      }

      editor
        .chain()
        .focus()
        .extendMarkRange('link')
        .setLink({ href: url })
        .run();
    }
  }, [editor]);

  const handleValidateBeforeSave = () => {
    console.log('emailSubject', emailSubject, 'emailMessage', emailMessage);

    //if any field is filled
    const hasImage = coverImageFile || coverImageURL;

    if (
      emailSubject.trim() !== '' ||
      editor?.getHTML().trim() !== '' ||
      hasImage
    ) {
      //check individually for unfuilled fields
      if (emailSubject.trim() === '') {
        setSubjectError(true);
      } else if (editor?.getHTML().trim() === '') {
        setSubjectError(false);
        setMessageError(true);
      } else if (!hasImage) {
        setSubjectError(false);
        setMessageError(false);
        setCoverImageError(true);
      } else if (
        emailSubject.trim() !== '' &&
        editor?.getHTML().trim() !== '' &&
        hasImage
      ) {
        setSubjectError(false);
        setMessageError(false);
        setCoverImageError(false);
        closeModal();
        return true;
      }
      return false;
    } else if (
      emailSubject.trim() === '' &&
      (editor?.getHTML().trim() === '' ||
        editor?.getHTML().trim() === '<p></p>') &&
      coverImageFile == null
    ) {
      setSubjectError(false);
      setMessageError(false);
      setCoverImageError(false);
      editor?.commands.setContent('');
      closeModal();
    }
  };
  const clearAndCancel = () => {
    setEmailSubject('');
    editor?.commands.setContent('');
    setEmailMessage('');
    setCoverImageFile(null);
    closeModal();
  };

  if (!editor) {
    return null;
  }

  return (
    <Modal
      heading="Customize Custom Invite Email"
      closeModal={closeModal}
      isModalOpen={isModalOpen}
      contentClass="w-[95vw] max-w-[1400px]"
      allowDismiss={false}
      customWidths
    // portalClass="grid grid-rows-[max-content,1fr,max-content] h-[calc(100vh_-_2rem)] overflow-y-hidden"
    >
      <form
        className="relative flex h-full w-full flex-col overflow-hidden bg-[#F8F9FB] "
        onSubmit={() => { }}
      >
        <div className="grid w-full grow grid-cols-2 gap-4 overflow-y-scroll p-4">
          <Card className="border-none">
            <CardHeader className="text-lg font-semibold 2xl:text-xl">
              Edit Invite Email
            </CardHeader>
            <CardContent className="">
              <div className="space-y-6">
                <Input
                  label="You can change these fields to create a custom invitation email template"
                  labelClass="text-sm text-helper-text"
                  className="w-full rounded-xl border p-4"
                  id="email-subject"
                  type="text"
                  name="email subject"
                  placeholder="Enter email subject here"
                  value={emailSubject}
                  onChange={e => setEmailSubject(e.target.value)}
                  hasError={subjectError}
                  errorMessage="This field is required"
                  variant="showcase"
                />

                <div>
                  <FileUpload
                    label="COVER IMAGE"
                    accept={{ 'image/*': [] }}
                    maxSizeMB={15}
                    dragActiveClassName="bg-green-50 border-green-500"
                    hint="Files types: PNG, JPG, Max size: 10MB"
                    value={coverImageFile || urlDerivedFile}
                    onFileSelect={(file) => {
                      setCoverImageFile(file);
                      setUrlDerivedFile(null); // Clear URL-derived file when user uploads new file
                    }}
                    onFileRemove={() => {
                      setCoverImageFile(null);
                      // Don't restore urlDerivedFile here - let user re-upload if needed
                    }}
                    disabled={isLoadingImageFromURL}
                  />
                  {isLoadingImageFromURL && (
                    <div className="mt-2 text-sm text-blue-600">
                      Loading existing cover image...
                    </div>
                  )}
                  {coverImageError && (
                    <div className="mt-1 text-sm text-red-500">
                      Cover image is required
                    </div>
                  )}
                </div>

                {/* Token Buttons */}
                <div className="text-[0.85rem] font-normal text-body-text">
                  <Label className="mb-3 mt-4">
                    To customize this email, click the placeholder options
                    displayed below and place them where you will like to have
                    them.
                  </Label>
                  <div className="flex flex-wrap gap-2 rounded-xl border border-[#E4E4E4] p-4">
                    {tokens.map(token => (
                      <Badge
                        key={token.value}
                        variant="light"
                        shape="rounded"
                        className="cursor-pointer border-none font-normal"
                        size="lg"
                        onClick={() => insertToken(token.value)}
                      >
                        {token.title}
                      </Badge>
                    ))}
                  </div>
                </div>

                <article className={`rounded-xl border-[0.7px] ${messageError ? 'border-red-500' : 'border-[#E4E4E4]'}`}>
                  {messageError && (
                    <div className="px-4 py-2 text-sm text-red-500">
                      This field is required
                    </div>
                  )}
                  <header>
                    <ToggleGroup
                      type="multiple"
                      className="flex-wrap justify-start border-b"
                    >
                      <ToggleGroupItem
                        value="bold"
                        aria-label="Toggle bold"
                        onClick={() =>
                          editor.chain().focus().toggleBold().run()
                        }
                        data-state={editor.isActive('bold') ? 'on' : 'off'}
                      >
                        <Bold className="h-4 w-4" />
                      </ToggleGroupItem>
                      <ToggleGroupItem
                        value="italic"
                        aria-label="Toggle italic"
                        onClick={() =>
                          editor.chain().focus().toggleItalic().run()
                        }
                        data-state={editor.isActive('italic') ? 'on' : 'off'}
                      >
                        <Italic className="h-4 w-4" />
                      </ToggleGroupItem>
                      <ToggleGroupItem
                        value="italic"
                        aria-label="Toggle italic"
                        onClick={() =>
                          editor.chain().focus().toggleStrike().run()
                        }
                        data-state={editor.isActive('italic') ? 'on' : 'off'}
                      >
                        <Strikethrough className="h-4 w-4" />
                      </ToggleGroupItem>
                      <ToggleGroupItem
                        value="underline"
                        aria-label="Toggle underline"
                        onClick={() =>
                          editor.chain().focus().toggleUnderline().run()
                        }
                        data-state={editor.isActive('underline') ? 'on' : 'off'}
                      >
                        <UnderlineIcon className="h-4 w-4" />
                      </ToggleGroupItem>

                      <Separator orientation="vertical" className="mx-1 h-8" />

                      <ToggleGroupItem
                        value="left"
                        aria-label="Align left"
                        onClick={() =>
                          editor.chain().focus().setTextAlign('left').run()
                        }
                        data-state={
                          editor.isActive({ textAlign: 'left' }) ? 'on' : 'off'
                        }
                      >
                        <AlignLeft className="h-4 w-4" />
                      </ToggleGroupItem>
                      <ToggleGroupItem
                        value="center"
                        aria-label="Align center"
                        onClick={() =>
                          editor.chain().focus().setTextAlign('center').run()
                        }
                        data-state={
                          editor.isActive({ textAlign: 'center' })
                            ? 'on'
                            : 'off'
                        }
                      >
                        <AlignCenter className="h-4 w-4" />
                      </ToggleGroupItem>
                      <ToggleGroupItem
                        value="right"
                        aria-label="Align right"
                        onClick={() =>
                          editor.chain().focus().setTextAlign('right').run()
                        }
                        data-state={
                          editor.isActive({ textAlign: 'right' }) ? 'on' : 'off'
                        }
                      >
                        <AlignRight className="h-4 w-4" />
                      </ToggleGroupItem>

                      <Separator orientation="vertical" className="mx-1 h-8" />

                      <ToggleGroupItem
                        value="bulletList"
                        aria-label="Toggle bullet list"
                        onClick={() =>
                          editor.chain().focus().toggleBulletList().run()
                        }
                        data-state={
                          editor.isActive('bulletList') ? 'on' : 'off'
                        }
                      >
                        <List className="h-4 w-4" />
                      </ToggleGroupItem>
                      <ToggleGroupItem
                        value="orderedList"
                        aria-label="Toggle ordered list"
                        onClick={() =>
                          editor.chain().focus().toggleOrderedList().run()
                        }
                        data-state={
                          editor.isActive('orderedList') ? 'on' : 'off'
                        }
                      >
                        <ListOrdered className="h-4 w-4" />
                      </ToggleGroupItem>

                      <Separator orientation="vertical" className="mx-1 h-8" />

                      <ToggleGroupItem
                        value="bulletList"
                        aria-label="Toggle bullet list"
                        onClick={setLink}
                        data-state={editor.isActive('link') ? 'on' : 'off'}
                      >
                        <LinkIcon className="h-4 w-4" />
                      </ToggleGroupItem>
                      <ToggleGroupItem
                        value="orderedList"
                        aria-label="Toggle ordered list"
                        onClick={() => editor.chain().focus().unsetLink().run()}
                        disabled={!editor.isActive('link')}
                      >
                        <Unlink className="h-4 w-4" />
                      </ToggleGroupItem>
                    </ToggleGroup>
                  </header>

                  <section className="min-h-[500px]  p-4">
                    <EditorContent
                      editor={editor}
                      value={emailMessage}
                      className="prose max-w-none"
                    />
                  </section>
                </article>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-lg font-semibold 2xl:text-xl">
              Preview
            </CardHeader>
            <CardContent className="p-6 pt-3">
              <h3 className="font-medium underline">Subject: {emailSubject}</h3>
              {(coverImageFile || coverImageURL) && (
                <div className="relative inline-block h-[7.2rem] w-full rounded-[0.975rem]">
                  <Image
                    src={
                      coverImageFile
                        ? URL.createObjectURL(coverImageFile)
                        : coverImageURL || ''
                    }
                    alt="Cover Image Preview"
                    fill
                    objectFit="cover"
                    className={cn('rounded-lg border border-gray-200')}
                  />
                </div>
              )}
              <div className="tiptap max-w-none">
                <div
                  dangerouslySetInnerHTML={{
                    __html: convertPlaceholdersToTokens(editor.getHTML()),
                  }}
                  className="tiptap prose min-h-[100px] max-w-none pt-4"
                />

                <section>
                  <h3 className="mb-2 mt-6 font-medium">Assessment Details:</h3>
                  <ul className="ml-2.5 mt-0 list-none space-y-1 text-sm">
                    <p className="text-black/80">
                      Assessment Name:{' '}
                      <span className="font-medium text-[#1C1C1C]">
                        {'{ assessment_name }'}
                      </span>{' '}
                    </p>
                    <p className="text-black/80">
                      Assessment Start Date:{' '}
                      <span className="font-medium text-[#1C1C1C]">
                        {'{ start_date }'}
                      </span>{' '}
                    </p>
                    <p className="text-black/80">
                      Duration:{' '}
                      <span className="font-medium text-[#1C1C1C]">
                        {'{ time_limit }'}
                      </span>{' '}
                    </p>
                    <p className="text-black/80">
                      Assessment deadline: This assessment is valid until{' '}
                      <span className="font-medium text-[#1C1C1C]">
                        {'{ deadline }'}
                      </span>
                      .{' '}
                    </p>
                    <p className="text-black/80">
                      The &apos;Take assessment&apos; button provided below will
                      only be active from{' '}
                      <span className="font-medium text-[#1C1C1C]">
                        {'{ start_date }'}
                      </span>{' '}
                      until{' '}
                      <span className="font-medium text-[#1C1C1C]">
                        {'{ deadline }'}
                      </span>
                      .{' '}
                    </p>
                    <p className="text-black/80">
                      The &apos;Take assessment&apos; button provided below will
                      only be active during the mentioned time period.{' '}
                    </p>
                  </ul>
                </section>

                <button className="btn-primary mb-4 text-sm" type="button">
                  Take Assessment
                </button>

                <footer>
                  <p className="text-sm text-black/80">
                    Use the following details to log in to the assessment:{' '}
                    <span className="font-medium text-[#1C1C1C]">
                      "{'{ auth_method1 }'}" - {'{ auth_method1_value }'} "
                      {'{ auth_method2 }'}" - {'{ auth_method2_value }'}
                    </span>
                  </p>
                  <p className="text-sm text-black/80">
                    Note that you can only take this assessment once. Good luck
                    !
                  </p>
                </footer>
              </div>
            </CardContent>
          </Card>
        </div>
      </form>

      <div className="sticky bottom-0 flex items-center justify-end gap-4 bg-white p-4">
        <button
          type="button"
          onClick={clearAndCancel}
          className="btn-primary-light"
        >
          {' '}
          Clear and Cancel
        </button>
        <button
          onClick={handleValidateBeforeSave}
          type="submit"
          className="btn-primary"
        >
          {' '}
          Save{' '}
        </button>
      </div>
    </Modal>
  );
};

export default EditOfferLetterModal;
