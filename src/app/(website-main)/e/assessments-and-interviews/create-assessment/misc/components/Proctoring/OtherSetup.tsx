import { useQueryClient } from '@tanstack/react-query';
import React from 'react';
import { Switch } from '@/components/shared';
import { useAssesmentCreatorPartialUpdate } from '../../api';
import { useAssessmentCreatorStore } from '../../store';
import { TProctoringOptions } from '../../types';

interface OtherSetupProps {
  updateProctoringOption: (option: TProctoringOptions, value: boolean) => void;
}

const OtherSetup: React.FC<OtherSetupProps> = ({ updateProctoringOption }) => {
  const assessment = useAssessmentCreatorStore();

  const options: {
    key: TProctoringOptions;
    label: string;
    description: string;
  }[] = [
    {
      key: 'invigilate_assessment',
      label: 'Invigilate Assessment',
      description:
        'By tapping the toggle on you will be given the ability to invigilate this assessment live. With this on you will be required to view an assessment detail and enter click on invigilate to view the candidates taking this assessment',
    },
    {
      label: 'Identity verification',
      key: 'is_identity_verification',
      description: `Tap toggle to have the assessment onboarding require identify verification for candidates. This would require candidates to provide valid IDs e.g NIN, <PERSON><PERSON>’s card, Int’l passport.`,
    },
    {
      key: 'is_stop_screen_sharing',
      label: 'Stop Screen Sharing',
      description:
        'Tap toggle to activate the option to restrict candidates from being able to share their screen during the assessment.',
    },
    {
      key: 'is_webcam_snapshot',
      label: 'Webcam Snapshot',
      description:
        "Tap toggle to enable capturing periodic snapshots of the candidate throughout the assessment. These snapshots will be accessible in the candidate's post-assessment report.",
    },
    {
      key: 'is_restrict_copying',
      label: 'Restrict Copying',
      description:
        'Tap toggle to prevent candidates from copying assessment content',
    },
    {
      key: 'is_restrict_tab_change',
      label: 'Restrict Tab Change',
      description:
        'Tap toggle to prevent candidates from changing tabs during assessment.  You can view tab changing activity in the candidate’s post-assessment report.',
    },
    {
      key: 'is_track_paste',
      label: 'External source paste tracking',
      description: 'Track when candidates paste content',
    },
  ];

  const { mutate: updateAssessment } = useAssesmentCreatorPartialUpdate();
  const queryClient = useQueryClient();

  return (
    <div className="space-y-4">
      <header className="px-4">
        <h3 className="font-semibold text-header-text">Other Setup Options</h3>
        <p className="text-xs text-helper-text">
          Configure additional proctoring settings
        </p>
      </header>

      {options.map((option, index) => (
        <label
          key={option.key}
          htmlFor={option.key}
          aria-disabled={index == 0}
          className="flex cursor-pointer items-start justify-between gap-4 rounded-xl border p-4"
        >
          <input type="radio" name={option.label} className="hidden" />
          <div className="space-y-1">
            <h3 className="text-[0.9rem] font-medium text-header-text">
              {option.label}
            </h3>
            <p className="helper-text max-w-[80%] text-xs">
              {option.description}
            </p>
          </div>
          <Switch
            id={option.key}
            disabled={index == 0}
            checked={assessment[option.key]}
            onCheckedChange={checked => {
              updateProctoringOption(option.key, checked);
              updateAssessment(
                {
                  assessment_id: assessment.id,
                  data: {
                    [option.key]: checked,
                  },
                },
                {
                  onSuccess: () => {
                    queryClient.invalidateQueries({
                      queryKey: ['get-assessment', assessment.id],
                    });
                    assessment.updateStore({
                      [option.key]: checked,
                    });
                  },
                }
              );
            }}
          />
        </label>
      ))}
    </div>
  );
};

export default OtherSetup;
