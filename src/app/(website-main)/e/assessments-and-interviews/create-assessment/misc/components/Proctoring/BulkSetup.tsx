import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@/components/shared"
import { TBulkToleranceSetup } from '../../types'
import { CloseCircle } from 'iconsax-react'
import { convertKebabAndSnakeToTitleCase } from '@/utils/strings'
import useAssessmentCreatorStore from '../../store'

interface BulkSetupProps {
  openBulkProctorModal: () => void
  bulk_tolerance_setup: TBulkToleranceSetup | null
  is_bulk_tolerance: boolean
}

const BulkSetup: React.FC<BulkSetupProps> = ({
  openBulkProctorModal,
}) => {
  const {
    updateProctoringOption,
    updateToleranceLevel,
    updateBulkToleranceSetup,
    bulk_tolerance_setup,
    is_bulk_tolerance,
  } = useAssessmentCreatorStore()


  return (
    <div className="space-y-4">
      <header className='px-4'>
        <h3 className="text-header-text font-semibold">Bulk Tolerance Settings</h3>
        <p className="text-helper-text max-w-[80%] text-xs">Set tolerance levels for multiple proctoring options at once</p>
      </header>
      {
        bulk_tolerance_setup ? (
          <div className="border p-4 rounded-lg">
            <p className='text-helper-text'>
              Combined tolerance:
              <span className='text-header-text font-medium ml-1.5'>
                {bulk_tolerance_setup.combined_tolerance}
              </span>
            </p>
            <section className='flex items-center flex-wrap gap-1.5'>
              {
                bulk_tolerance_setup.options.map((o, index) => {
                  return (
                    <Badge key={index} variant="light" className="" size="lg">
                      {convertKebabAndSnakeToTitleCase(o.option as unknown as string)}
                      {/* <CloseCircle className='ml-2 cursor-pointer' size={20} onClick={() => handleOptionChange(option, false)} /> */}
                    </Badge>
                  )
                })
              }
            </section>
            {/* <p>Options: {bulk_tolerance_setup.options.map(o => o.option).join(', ')}</p> */}
          </div>
        ) : (
          <p className='p-4 text-helper-text text-sm'>No bulk tolerance settings configured.</p>
        )
      }
      <Button onClick={openBulkProctorModal} variant="extralight" size="thin">
        {is_bulk_tolerance ? 'Edit Bulk Settings' : 'Set Bulk Tolerance'}
      </Button>
    </div>
  )
}

export default BulkSetup

