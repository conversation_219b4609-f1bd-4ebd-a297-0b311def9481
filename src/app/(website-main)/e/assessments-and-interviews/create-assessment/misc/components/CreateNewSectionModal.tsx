import { Button, Input, LoaderBtn, Modal } from '@/components/shared'
import React from 'react'
import { UseCreateAssessmentSection } from '../api'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import toast from 'react-hot-toast'
import { useQueryClient } from '@tanstack/react-query'

interface CreateNewSectionModalProps {
    isModalOpen: boolean
    closeModal: () => void
    assessment_id: string
    setActiveSectionId: React.Dispatch<React.SetStateAction<string>>
}
const schema = z.object({
    section_name: z.string({ message: 'Section name is required' }).min(1, 'Section name is required')
})
type formType = z.infer<typeof schema>


const CreateNewSectionModal: React.FC<CreateNewSectionModalProps> = ({ isModalOpen, closeModal, assessment_id, setActiveSectionId }) => {
    const { mutate: createSection, isLoading } = UseCreateAssessmentSection()
    const { register, handleSubmit, formState: { errors }, reset } = useForm<formType>()
    const queryClient = useQueryClient()

    const submitForm = (data: formType) => {
        const dataToSubmit = {
            section_name: data.section_name,
            assessment_id
        }
        createSection(dataToSubmit, {
            onSuccess: (data) => {
                setActiveSectionId(data.section?.id)
                reset()
                toast.success('Section created successfully')
                queryClient.invalidateQueries({
                    queryKey: ['get-assessment', assessment_id]
                })
                closeModal()
            }
        })
    }
    return (
        <Modal
            heading='New Section'
            isModalOpen={isModalOpen}
            closeModal={closeModal}
        >
            <form onSubmit={handleSubmit(submitForm)}>
                <Input
                    {...register('section_name', { required: true })}
                    label='Section Name'
                    placeholder='Enter section name'
                    hasError={!!errors.section_name}
                    errorMessage={errors.section_name?.message}
                    variant="showcase"
                />

                <Button className='w-full mt-7'>Create Section
                    {
                        isLoading && <LoaderBtn />
                    }

                </Button>
            </form>

        </Modal>
    )
}

export default CreateNewSectionModal