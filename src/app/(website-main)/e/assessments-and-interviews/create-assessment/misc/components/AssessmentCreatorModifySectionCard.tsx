import React from 'react'
import { useParams } from 'next/navigation';
import { Reorder } from "framer-motion";
import { CloseSquare, More, SearchNormal, TickCircle } from 'iconsax-react';

import { useBooleanStateControl } from '@/hooks';
import { ConfirmDeleteModal, DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, EmptyCard, Input } from '@/components/shared';
import { useQueryClient } from '@tanstack/react-query';

import useAssessmentCreatorStore from '../store';
import { TAssessmentSection } from '../types';
import CreateQuestionSelectTypeModal from '../../../question-pack/misc/components/CreateQuestionSelectTypeModal';
import { UseDeleteAssessmentSection } from '../api';



interface AssessmentCreatorModifySectionCardProps {
    section: TAssessmentSection
    index: number
    isActive: boolean
    onClick: () => void
}

const AssessmentCreatorModifySectionCard = ({ section, index, isActive, onClick }: AssessmentCreatorModifySectionCardProps) => {
    const queryClient = useQueryClient()
    const assessmentId = useAssessmentCreatorStore(state => state.id)
    const params = useParams()

    const {
        state: isCreateQuestionModalOpen,
        setTrue: openCreateQuestionModal,
        setFalse: closeCreateQuestionModal
    } = useBooleanStateControl()

    const sectionQuestionTypes = React.useMemo(() => {
        const questionTypesCount = section?.question_set?.reduce((acc: { [key: string]: number }, question) => {
            if (question && question.type) {
                acc[question.type] = (acc[question.type] || 0) + 1;
            }
            return acc;
        }, {});

        return questionTypesCount;
    }, [section]);

    const {
        state: isConfirmDeleteModalOpen,
        setTrue: openConfirmDeleteModal,
        setFalse: closeConfirmDeleteModal
    } = useBooleanStateControl()

    const { mutate: deleteSection, isLoading } = UseDeleteAssessmentSection()
    const handleDeleteSection = () => {
        const data = {
            section_id: section.id,
            assessment_id: assessmentId
        }
        deleteSection(data, {
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: ['get-assessment', assessmentId]
                })
                closeConfirmDeleteModal()
            }
        })
    }


    return (
        <Reorder.Item
            key={section.id}
            value={section.id}
            onClick={onClick}
            className='cursor-grab'
        >
            <div className={`rounded-xl bg-white p-1 text-sm outline-1 outline-primary ${isActive ? "outline" : ""}`}>
                <div className="bg-light-accent-bg flex items-center justify-between rounded-t-xl p-2 text-primary">
                    <div className="flex gap-4">
                        <h2 className="flex items-center gap-2">
                            Section
                            <span className="flex w-max px-1 min-w-4 min-h-3.5 items-center justify-center rounded-full bg-primary text-white">
                                {index + 1}
                            </span>
                        </h2>
                        <p>
                            {section?.section_name}: {section?.question_set.length}
                        </p>
                    </div>
                    <div className="flex items-center gap-4">
                        <p className="flex items-center gap-2">
                            <TickCircle size={20} /> {section?.total_points || 0} points
                        </p>
                        <DropdownMenu>
                            <DropdownMenuTrigger>
                                <More className='rotate-90' size={18} />
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className='rounded-lg' align='end'>
                                <DropdownMenuItem
                                    className="flex items-center gap-2 w-full cursor-pointer rounded-lg"
                                    onClick={openConfirmDeleteModal}
                                >
                                    <CloseSquare size={18} />
                                    Delete Section
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>

                    </div>
                </div>
                <div className="flex items-center justify-between p-4 text-xs">
                    <p className="text-primary">{sectionQuestionTypes.length} question types</p>
                    <div className="flex gap-2 items-center">
                        <button
                            className="btn-primary-light"
                            type="button"
                            onClick={openCreateQuestionModal}
                        >
                            Add custom question
                        </button>
                        <button
                            type="button"
                            className="btn-primary-light"
                            onClick={() => {
                                // viewSection(index);
                            }}
                        >
                            View Questions
                        </button>
                    </div>
                </div>
            </div>

            <CreateQuestionSelectTypeModal
                isSelectQuestionTypeModalOpen={isCreateQuestionModalOpen}
                closeSelectQuestionTypeModal={closeCreateQuestionModal}
                assessment_id={params.id as string}
                section_id={section.id}
                refetch={() => {
                    queryClient.invalidateQueries({
                        queryKey: ['get-assessment', assessmentId]
                    })
                }}
            />

            <ConfirmDeleteModal
                isModalOpen={isConfirmDeleteModalOpen}
                closeModal={closeConfirmDeleteModal}
                deleteFunction={handleDeleteSection}
                isDeleting={isLoading}
                title="Delete Section"
            >
                <p>
                    Are you sure you want to delete this section?
                </p>
            </ConfirmDeleteModal>
        </Reorder.Item>
    )
}

export default AssessmentCreatorModifySectionCard