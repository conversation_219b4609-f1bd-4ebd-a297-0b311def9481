import React from 'react'
import useAssessmentCreatorStore from '../store';
import { Button, EmptyCard, Input } from '@/components/shared';
import { useBooleanStateControl } from '@/hooks';
import AssessmentRecomendationCard from './AssessmentRecomendationCard';
import { shallow } from 'zustand/shallow';
import { SearchNormal, TickCircle } from 'iconsax-react';
import { useCreateAssessmentValidation } from '../contexts';
import { Reorder } from "framer-motion";
import { TAssessmentSection } from '../types';
import CreateQuestionSelectTypeModal from '../../../question-pack/misc/components/CreateQuestionSelectTypeModal';
import { useQueryClient } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import { QUESTION_TYPES_DICTIONARY } from '../../../misc/constants/constants';
import AssessmentCreatorModifySectionQuestionCard from './AssessmentCreatorModifySectionQuestionCard';
import { cn } from '@/utils';

interface AssessmentCreatorModifySectionDetailsProps {
    section?: TAssessmentSection
    index: number
    isActive: boolean
    onClick: () => void
}

const AssessmentCreatorModifySectionDetails = ({ section, index, isActive, onClick }: AssessmentCreatorModifySectionDetailsProps) => {
    const queryClient = useQueryClient()
    const assessmentId = useAssessmentCreatorStore(state => state.id)
    const params = useParams()

    const sectionQuestionTypes = React.useMemo(() => {
        const questionTypesCount = section?.question_set?.reduce((acc: { [key: string]: number }, question) => {
            if (question && question.type) {
                acc[question.type] = (acc[question.type] || 0) + 1;
            }
            return acc;
        }, {});

        return questionTypesCount;
    }, [section]);
    const [selectedQuestionType, setSelectedQuestionType] = React.useState<string>('all')



    const {
        state: isCreateQuestionModalOpen,
        setTrue: openCreateQuestionModal,
        setFalse: closeCreateQuestionModal
    } = useBooleanStateControl()




    return (
        <section>
            {
                section ?
                    <div className="rounded-xl bg-white p-6">
                        <div className="section-container text-sm">
                            <div className="flex justify-between text-primary">
                                <div className="flex gap-4">
                                    <h2 className="flex items-center gap-2">
                                        Section
                                        <span className="flex aspect-square w-[18px] items-center justify-center rounded-full bg-primary text-white">
                                            {index + 1}
                                        </span>
                                    </h2>
                                    <p>
                                        {section?.section_name}:{" "}
                                        {section?.question_set.length}
                                    </p>
                                </div>
                                <div className="flex gap-2">
                                    <p className="flex items-center gap-2">
                                        <TickCircle size={18} /> {section?.total_points || 0} points
                                    </p>
                                </div>
                            </div>
                            <ul className="flex flex-wrap items-center gap-2 gap-y-1 capitalize">
                                {
                                    Object.keys({ all: 'all', ...sectionQuestionTypes }).map((question_type, index) => (
                                        <li
                                            key={index}
                                            className={cn(
                                                "rounded-full text-xs text-primary bg-primary-light py-1.5 px-4 border border-transparent cursor-pointer",
                                                selectedQuestionType == question_type && "border-primary"
                                            )}
                                            onClick={() => setSelectedQuestionType(question_type)}
                                        >
                                            {QUESTION_TYPES_DICTIONARY[question_type.toLowerCase()]?.name || "All"}
                                        </li>
                                    ))
                                }
                            </ul>

                            <ul className="section-container">
                                {
                                    section?.question_set.map((question, question_index) => {
                                        if (question.type == selectedQuestionType) {
                                            return (
                                                <AssessmentCreatorModifySectionQuestionCard
                                                    index={question_index}
                                                    question={question}
                                                    section={section}
                                                />
                                            )
                                        }
                                        else if (selectedQuestionType == 'all') {
                                            return (
                                                <AssessmentCreatorModifySectionQuestionCard
                                                    index={question_index}
                                                    question={question}
                                                    section={section}
                                                />
                                            )
                                        }
                                    })
                                }
                            </ul>
                            <button
                                className="btn-primary-bordered w-full"
                                type="button"
                                onClick={openCreateQuestionModal}
                            >
                                Add custom question
                            </button>
                        </div>
                    </div>

                    :
                    <div className='rounded-xl bg-white p-6'>
                        <EmptyCard
                            title="No section selected"
                            contentClass='text-sm text-body-text'
                            content="Select a section to view details, create one if you don't have any section"
                        />
                    </div>

            }
            <CreateQuestionSelectTypeModal
                isSelectQuestionTypeModalOpen={isCreateQuestionModalOpen}
                closeSelectQuestionTypeModal={closeCreateQuestionModal}
                assessment_id={params.id as string}
                section_id={section?.id!}
                refetch={() => {
                    queryClient.invalidateQueries({
                        queryKey: ['get-assessment', assessmentId]
                    })
                }}
            />
        </section>
    )
}

export default AssessmentCreatorModifySectionDetails