export interface TAssessment {
  id: string;
  sections: TAssessmentSection[];
  interview_questions: any[];
  feedbacks: any[];
  name: string;
  description?: string;
  type: string;
  intro_video_url: string | undefined;
  role_level: string;
  support_email: null;
  time: string;
  is_published: boolean;
  commencement_settings: boolean;
  deadline: string | undefined;
  start_time: string | undefined;
  time_limit: number;
  deadline_reminder: boolean;
  reminder_time: number;
  total_questions: number;
  no_candidates: number;
  completed: any[];
  paused: any[];
  ongoing: any[];
  not_started: any[];
  invigilate_assessment: boolean;
  is_stop_screen_sharing: boolean;
  full_screen_tolerance_level: number;
  window_change_tolerance_level: number;
  tab_change_tolerance_level: number;
  multi_face_tolerance_level: number;
  different_face_tolerance_level: number;
  is_bulk_tolerance: boolean;
  bulk_tolerance_setup: TBulkToleranceSetup | null;
  is_webcam_snapshot: boolean;
  is_restrict_copying: boolean;
  is_restrict_tab_change: boolean;
  is_identity_verification: boolean;
  is_track_paste: boolean;
  is_shuffle: boolean;
  is_shuffle_sections: boolean;
  is_custom: boolean;
  last_page_url: string;
  reminder_sent: boolean;
  created_at: string;
  updated_at: string;
  role: TAssessmentRole;
  creator: TAssessmentCreator;
  assessment_team_members: any[];
}

export interface TAssessmentCreator {
  id: number;
  company_email: string;
  verified: boolean;
  type_of_recruiter: string;
  profile_picture: null;
  role: string;
  created_at: string;
  updated_at: string;
  user: string;
  company: number;
}

export interface TAssessmentRole {
  id: number;
  name: string;
  use_ai: boolean;
}

export interface TAssessmentSection {
  id: string;
  section_name: string;
  no_multiple_choice: number;
  no_essay: number;
  no_coding: number;
  no_fill_in_the_blanks: number;
  no_multiple_response: number;
  no_true_false: number;
  total_points: number;
  order: number;
  test: any[];
  question_pack: any[];
  question_set: TAssessmentQuestionset[];
}

export interface TAssessmentQuestionset {
  id: string;
  type: string;
  question: string;
  answer_options_type: string;
  answer_options: (string | string)[];
  answer: (string | string)[];
  experience_level: string;
  category: string | null;
  points: number;
  label: null;
  tags: null;
  is_custom: boolean;
  time: number;
  created_at: string;
  updated_at: string;
  instructions?: string;
  images?: string[];
  image_urls?: string[];
  is_shuffled?: boolean;
  has_image: boolean;
  role: string | null;
  test: string;
  section: string;
  creator: TAssessmentCreator;
}

export interface TBulkToleranceSetup {
  combined_tolerance: number;
  options: { option: string }[];
}

export type TProctoringOptions =
  | 'invigilate_assessment'
  | 'is_stop_screen_sharing'
  | 'is_webcam_snapshot'
  | 'is_identity_verification'
  | 'is_restrict_copying'
  | 'is_restrict_tab_change'
  | 'is_track_paste';

export interface TProctoringToleranceOption {
  id: string;
  title: string;
  description: string;
}
