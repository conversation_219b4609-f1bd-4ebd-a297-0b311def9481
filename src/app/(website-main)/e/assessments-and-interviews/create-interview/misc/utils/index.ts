interface htmlToTokenSProps {
  html_string: string;
  auth_type?: 'email' | 'file';
  cover_image: string;
  is_interview?: boolean;
}

export async function htmlToTokens(data: htmlToTokenSProps) {
  const { html_string, auth_type, cover_image, is_interview } = data;

  const parser = new DOMParser();
  const dom = parser.parseFromString(html_string, 'text/html');

  const base64CoverImage =
    cover_image ??
    'https://res.cloudinary.com/dvdnjlxa8/image/upload/v1696419285/Group_40747_thpo8x.png';
  const EMAIL_TEMPLATE_WITH_AUTH = (
    is_interview
      ? CUSTOM_INTERVIEW_EMAIL_TEMPLATE(base64CoverImage)
      : CUSTOM_EMAIL_TEMPLATE(base64CoverImage)
  ).replace(
    '{{#auth#}}',
    (auth_type || 'email') == 'email'
      ? CUSTOM_TEMPLATE_EMAIL_AUTH_PARTIAL
      : CUSTOM_TEMPLATE_FILE_AUTH_PARTIAL
  );
  // const EMAIL_TEMPLATE_WITH_AUTH = CUSTOM_EMAIL_TEMPLATE(base64CoverImage).replace("{{#auth#}}", (auth_type || "email") == "email" ? CUSTOM_TEMPLATE_EMAIL_AUTH_PARTIAL : CUSTOM_TEMPLATE_FILE_AUTH_PARTIAL)

  return unescape(
    encodeURIComponent(
      EMAIL_TEMPLATE_WITH_AUTH.replace(
        '{{#content_goes_here#}}',
        dom.body.innerHTML
      )
    )
  );
}

export const CUSTOM_EMAIL_TEMPLATE = (cover_image: string) => {
  return `
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<html lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <style>
      @font-face {
        font-family: "DM sans";
        font-style: normal;
        font-weight: 400;
        mso-font-alt: Verdana;
        src: url("https://fonts.googleapis.com/css2?family=DM+Sans:opsz,wght@9..40,100;9..40,200;9..40,300;9..40,400;9..40,500;9..40,600;9..40,700;9..40,800;9..40,900;9..40,1000&amp;family=Montserrat&amp;display=swap");
        /* format(&#x27; woff2&#x27; ); */
      }

      * {
        font-family: "DM sans", Verdana;
      }
    </style>
  </head>

  <body data-id="__react-email-body" style="
      width: max-content;
      height: max-content;
      margin: 0 auto;
      background: #f4f4f4ff;
    ">
    <table align="center" width="100%" data-id="__react-email-container" role="presentation" cellspacing="0" cellpadding="0" border="0" style="
        max-width: 37.5em;
        width: 100vw;
        height: max-content;
        overflow-x: hidden;
        padding-top: 0px;
        box-sizing: border-box;
        background-color: white;
      ">
      <tbody>
        <tr style="width: 100%">
          <td>
            <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation">
              <tbody>
                <tr>
                  <td>
                    <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation" style="
                        width: 100%;
                        height: 20%;
                        display: flex;
                        flex-direction: column;
                        color: rgb(255, 255, 255);
                        row-gap: 0.625rem;
                        justify-content: center;
                        align-items: center;
                        background-color: rgb(132, 39, 189);
                        background: url("${cover_image}");
                      ">
                      <tbody>
                        <tr>
                          <td style="position:relative">
                            <img data-id="react-email-img" src="${cover_image}"  style="
                                display: block;
                                outline: none;
                                border: none;
                                text-decoration: none;
                                width: 100%;
                                max-width: 700px;
                                max-height: 150px;
                                object-fit: cover;
                              ">
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation" style="
                        padding-left: 2.5rem;
                        padding-right: 2.5rem;
                        padding-top: 2.5rem;
                        padding-bottom: 2.5rem;
                      ">
                      <tbody>
                        <tr>
                          <td>
                            <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation">
                              <tbody>
                                <tr>
                                  <td>
                                    {{#content_goes_here#}}
                                    <p data-id="react-email-text" style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 1.5rem;
                                        padding-bottom: 0.5rem;
                                      "> <strong> Assessment Details </strong>
                                    </p>
                                    <p data-id="react-email-text" style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 0px;
                                        padding-bottom: 0px;
                                      ">
                                      Assessment Name: <strong>{{ assessment_name }}</strong>
                                    </p>
                                    <p data-id="react-email-text" style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 2px;
                                        padding-bottom: 2px;

                                      ">
                                    {% if start_date %}
                                        Assessment Start Date: <strong>{{ start_date }}</strong><br>
                                    {% endif %}
                                    Duration: <strong>{{ time_limit }}</strong><br>
                                    </p>
                                    <p data-id="react-email-text" style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 0px;
                                        padding-bottom: 0px;
                                      ">
                                    Assessment deadline: This assessment is valid until <strong>{{ deadline }}</strong>.<br>
                                    {% if start_date %}
                                        The ‘Take assessment’ button provided below will only be active from <strong>{{ start_date }}</strong> until <strong>{{ deadline }}</strong>.<br>
                                    {% else %}
                                        The ‘Take assessment’ button provided below will only be active during the mentioned time period.
                                    {% endif %}
                                    </p>
                                    <a href="{{ invitation_link }}" data-id="react-email-button" target="_blank" style="
                                        line-height: 100%;
                                        text-decoration: none;
                                        display: inline-block;
                                        max-width: 100%;
                                        padding: 0.75rem;
                                        background-color: rgb(117, 90, 226);
                                        border-radius: 0.375rem;
                                        padding-top: 0.5rem;
                                        padding-bottom: 0.5rem;
                                        margin-top: 0.5rem;
                                        margin-bottom: 0.5rem;
                                      "><span></span><span style="
                                          max-width: 100%;
                                          display: inline-block;
                                          line-height: 120%;
                                          mso-padding-alt: 0px;
                                          mso-text-raise: 0;
                                        "><p data-id="react-email-text" style="
                                            font-size: 14px;
                                            line-height: 24px;
                                            margin: 0px;
                                            color: rgb(255, 255, 255);
                                          ">
                                          Take Assessment
                                        </p></span><span></span></a>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                            <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation">
                              <tbody>
                                <tr>
                                  <td>
                                    <!-- <p
                                      data-id="react-email-text"
                                      style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 0.25rem;
                                        padding-bottom: 0.25rem;
                                        margin-top: 0.5rem;
                                      "
                                    >
                                      Use your "{{ auth_method1 }}" and "{{ auth_method2 }}"" details to login to the
                                      assessment,
                                    </p> -->
                                    <p data-id="react-email-text" style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 1rem;
                                        padding-bottom: 0.25rem;
                                      ">
                                      Use the following details to log in to the
                                      assessment:<br>
                                      {{#auth#}}
                                    </p>
                                    <p data-id="react-email-text" style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 0px;
                                        padding-bottom: 0px;
                                        margin-top: 1.3rem;
                                      ">
                                      Note that you can only take this
                                      assessment once.
                                    </p>
                                    <p data-id="react-email-text" style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 0px;
                                        padding-bottom: 0px;
                                      ">
                                      Good luck !
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>

  <style>
    @import url(&quot;https://fonts.googleapis.com/css2?family=DM+Sans:opsz,wght@9..40,100;9..40,200;9..40,300;9..40,400;9..40,500;9..40,600;9..40,700;9..40,800;9..40,900;9..40,1000&amp;family=Montserrat&amp;display=swap&quot;);

    .email-text {
      font-family: DM Sans, sans-serif;
    }
  </style>

</body></html>
`;
};

export const CUSTOM_INTERVIEW_EMAIL_TEMPLATE = (cover_image: string) => {
  return `
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<html lang="en">
  <head>
    <style>
      @font-face {
        font-family: "DM sans";
        font-style: normal;
        font-weight: 400;
        mso-font-alt: Verdana;
        src: url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');
      }

      * {
        font-family: 'DM Sans', sans-serif;
      }
    </style>
  </head>

  <body style="width: max-content; height: max-content; margin: 0 auto; background: #f4f4f4ff;">
    <table align="center" width="100%" role="presentation" cellspacing="0" cellpadding="0" border="0"
      style="max-width: 37.5em; width: 100vw; height: max-content; overflow-x: hidden; padding-top: 0px; box-sizing: border-box; background-color: white;">
      <tbody>
        <tr style="width: 100%">
          <td>
            <table align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation">
              <tbody>
                <tr>
                  <td>
                    <table align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
                      style="width: 100%; height: 20%; display: flex; flex-direction: column; color: rgb(255, 255, 255); row-gap: 0.625rem; justify-content: center; align-items: center; background-color: rgb(132, 39, 189);">
                      <tbody>
                        <tr>
                          <td>
                            <img src="${cover_image}"
                              style="display: block; outline: none; border: none; text-decoration: none;"/>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <!-- Introduction Section -->
                    <table align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
                      style="padding-left: 5rem; padding-right: 5rem; padding-top: 2.5rem; padding-bottom: 2.5rem;">
                      <tbody>
                        <tr>
                          <td>
                            <p style="font-size: 14px; line-height: 24px; margin: 0px; padding-top: 0.5rem; padding-bottom: 0.5rem;">
                              {% if candidate_name %}
                                Dear {{ candidate_name }},
                              {% else %}
                                Dear Candidate,
                              {% endif %}
                            </p>
                            <p style="font-size: 14px; line-height: 24px; margin: 0px; padding-top: 0.5rem; padding-bottom: 0.5rem;">
                              We are pleased to invite you to participate in an interview for the {{ job_position }}
                              role at {{ company_name }}. We were impressed with your profile and would like to learn more about your experience and skills.
                            </p>
                            <!-- Interview Details Section -->
                            <p style="font-size: 14px; line-height: 24px; margin: 0px; padding-top: 0.5rem; padding-bottom: 0.5rem; font-weight: 600;">
                              Interview Details
                            </p>
                            <p style="font-size: 14px; line-height: 24px; margin: 0px; padding-top: 0px; padding-bottom: 0px;">
                              Interview Type: <strong>{{ interview_name }}</strong>
                            </p>
                            <p style="font-size: 14px; line-height: 24px; margin: 0px; padding-top: 2px; padding-bottom: 2px;">
                              {% if start_time and deadline %}
                                Available from: <strong>{{ start_time }}</strong><br />
                                Must complete before: <strong>{{ deadline }}</strong><br />
                              {% endif %}
                              Duration: <strong>{{ time_limit }}</strong><br />
                            </p>
                            <!-- Take Interview Button Section -->
                            <table align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation">
                              <tbody>
                                <tr>
                                  <td>
                                    <a href="{{ invitation_link }}" target="_blank"
                                      style="line-height: 100%; text-decoration: none; display: inline-block; max-width: 100%; padding: 0.75rem; background-color: rgb(117, 90, 226); border-radius: 0.375rem; padding-top: 0.5rem; padding-bottom: 0.5rem; margin-top: 0.5rem; margin-bottom: 0.5rem;">
                                      <span style="max-width: 100%; display: inline-block; line-height: 120%; mso-padding-alt: 0px; mso-text-raise: 0;">
                                        <p style="font-size: 14px; line-height: 24px; margin: 0px; color: rgb(255, 255, 255);">
                                          Join Interview
                                        </p>
                                      </span>
                                    </a>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                            <!-- Login Details Section -->
                            <p style="font-size: 14px; line-height: 24px; margin: 0px; padding-top: 0.25rem; padding-bottom: 0.25rem;">
                              Please use the following credentials to access your interview:
                            </p>
                            <p style="font-size: 14px; line-height: 24px; margin: 0px; padding-top: 0px; padding-bottom: 0px; font-weight: bold;">
                              Email: {{ candidate_email }}
                            </p>
                            <p style="font-size: 14px; line-height: 24px; margin: 0px; padding-bottom: 0.25rem; font-weight: bold;">
                              Token: {{ otp }}
                            </p>
                            <!-- Additional Information Section -->
                            <p style="font-size: 14px; line-height: 24px; margin: 0px; padding-top: 0.5rem; padding-bottom: 0.5rem; font-weight: 600;">
                              Important Information
                            </p>
                            <p style="font-size: 14px; line-height: 24px; margin: 0px; padding-top: 0.5rem; padding-bottom: 0.5rem;">
                              Please ensure you have:
                            </p>
                            <ol style="font-size: 14px; line-height: 24px; margin: 0px; padding-left: 1rem; padding-bottom: 0.5rem;">
                              <li>A stable internet connection</li>
                              <li>A quiet environment free from distractions</li>
                              <li>A working webcam and microphone</li>
                              <li>Any relevant documents or materials ready</li>
                            </ol>
                            <p style="font-size: 14px; line-height: 24px; margin: 0px; padding-top: 0.5rem; padding-bottom: 0.5rem;">
                              We look forward to speaking with you!<br /><br />
                              Best regards,<br />
                              {{ company_name }} Team
                            </p>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <!-- Footer Section -->
                    <table align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation"
                      style="padding-left: 5rem; padding-right: 5rem; padding-top: 1.5rem; padding-bottom: 1.5rem;">
                      <tbody>
                        <tr>
                          <td>
                            <hr style="width: 100%; height: 1px; background: #f4f4f4ff; border: none;"/>
                            <p style="font-size: 10px; line-height: 16px; margin: 0px; padding-top: 0.5rem; padding-bottom: 0.5rem; color: rgb(166, 166, 166); text-align: center;">
                              You are receiving this email because you applied for a role at {{ company_name }}.
                            </p>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <img src="{{ email_template_header|default:'https://res.cloudinary.com/dvdnjlxa8/image/upload/v1696419285/Group_40747_thpo8x.png' }}"
                      style="display: block; outline: none; border: none; text-decoration: none; width: 100%;"/>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
  </body>
</html>
`;
};

export const CUSTOM_TEMPLATE_EMAIL_AUTH_PARTIAL = `<b>"email" - {{ candidate_email }}</b><br>
<b>"passcode" - {{ otp }}</b>`;

export const CUSTOM_TEMPLATE_FILE_AUTH_PARTIAL = `<b>"{{ auth_method1 }}" - {{ auth_method1_value }}</b><br>
<b>"{{ auth_method2 }}" - {{ auth_method2_value }}</b> `;
