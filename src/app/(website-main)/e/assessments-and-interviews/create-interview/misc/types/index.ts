export interface TInterview {
    step: number,
    id: string;
    name: string;
    questions: TInterviewQuestion[];
    role: number;
    role_level: string;
    creator: number;
    start_time: Date | null;
    deadline: Date | null;
    total_questions: number;
    no_candidates: number;
    completed: number;
    paused: any[];
    ongoing: number;
    not_started: number;
    is_published: boolean;
    invite_info: {
        message: string;
    };
    candidates: any[];
}

export interface TInterviewQuestion {
    id: string;
    question: string;
    type: string;
    answer?: string;
    points?: number;
    time_limit?: number;
    time?: number;
    section?: string;
}

export interface TInterviewCreator {
    id: number;
    company_email: string;
    verified: boolean;
    type_of_recruiter: string;
    profile_picture: string | null;
    role: string;
    created_at: string;
    updated_at: string;
    user: string;
    company: number;
}

export interface TInterviewQuestionPack {
    id: string;
    question_set: TInterviewQuestion[];
    name: string;
    description: string;
    recruiter: number;
}