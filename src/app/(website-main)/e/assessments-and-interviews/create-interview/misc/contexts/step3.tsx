'use client'

import React, { createContext, useContext, useRef, useState } from 'react';

type ValidationFunction = () => Promise<boolean>;

interface ValidationContextType {
    registerValidation: (section: string, validate: ValidationFunction, validationErrorMessage?: string) => void;
    validateSection: (section: string) => Promise<boolean>;
    validationErrorMessage: string | null;
}

const ValidationContext = createContext<ValidationContextType | null>(null);

export const AssessmentConfigurationValidationProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
    const validationFunctions = useRef<Record<string, ValidationFunction>>({});
    const [validationErrorMessage, setValidationErrorMessage] = useState<string | null>(null);

    const registerValidation = (section: string, validate: ValidationFunction, validationErrorMessage?: string) => {
        validationFunctions.current[section] = validate;
        if (validationErrorMessage) {
            setValidationErrorMessage(validationErrorMessage); 
        }
    };

    const validateSection = async (section: string) => {
        const validate = validationFunctions.current[section];
        if (validate) {
            return await validate();
        }
        return true;
    };

    return (
        <ValidationContext.Provider value={{ registerValidation, validateSection, validationErrorMessage }}>
            {children}
        </ValidationContext.Provider>
    );
};

export const useAssessmentConfigurationValidation = () => {
    const context = useContext(ValidationContext);
    if (!context) {
        throw new Error('useAssessmentConfigurationValidation must be used within an AssessmentCreatorValidationProvider');
    }
    return context;
};

