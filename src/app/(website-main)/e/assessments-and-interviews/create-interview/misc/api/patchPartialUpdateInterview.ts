import { Axios } from "@/lib/api/axios";
import { useMutation } from "@tanstack/react-query";
import { TInterview } from "../types";
interface props {
    data: Partial<TInterview>
    assessment_id: string
}
const updateInterview = async ({ data, assessment_id }: props) => {
    const res = await Axios.patch(`/assessments/assessment-interview/${assessment_id}/`, { ...data });
    return res.data
}


export const useInterviewCreatorPartialUpdate = () => {
    return useMutation({
        mutationFn: updateInterview,
        mutationKey: ['partially-update-interview']
    })
}