import { Axios } from "@/lib/api/axios"
import { useQuery } from "@tanstack/react-query"
import { TInterviewQuestion, TInterviewQuestionPack } from "../../../create-interview/misc/types"

const getDetails = async () => {
    const response = await Axios.get(`assessments/interview-packs/`)
    return response.data as TInterviewQuestionPack[]
}

export const useGetInterviewPacks = (id?: string) => {
    return useQuery({
        queryKey: ['get-questinterviewion-pack-details', id],
        queryFn: getDetails
    })
}

