import { Axios } from "@/lib/api/axios"
import { useMutation } from "@tanstack/react-query"

type props = {
    interview_id: string
    questions: string[]
}
const addQuestions = async ({ interview_id, questions }: props) => {
    const res = await Axios.patch(`assessments/assessment-interview/${interview_id}/`, { add_questions: questions });
    return res.data
}


export const UseAddQuestionsToInterview = () => {
    return useMutation({
        mutationFn: addQuestions,
        mutationKey: ['add-question-to-section']
    })
}