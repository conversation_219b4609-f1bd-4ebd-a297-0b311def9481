import { Axios } from "@/lib/api/axios"
import { useMutation } from "@tanstack/react-query"

type props = {
    interview_id: string
    questions: string[]
}
const addQuestions = async ({ interview_id, questions }: props) => {
    const res = await Axios.patch(`assessments/assessment-interview/${interview_id}/`, { remove_questions: questions });
    return res.data
}

export const UseDeleteQuestionsFromInterview = () => {
    return useMutation({
        mutationFn: addQuestions,
        mutationKey: ['delete-question-from-section-in-interview-creator']
    })
}