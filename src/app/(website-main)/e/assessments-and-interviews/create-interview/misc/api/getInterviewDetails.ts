import { Axios } from "@/lib/api/axios";
import { useQuery } from "@tanstack/react-query";
import { TInterview } from "../types";

const getInterview = async (assessmentId: string) => {
    const res = await Axios.get(`/assessments/assessment-interview/${assessmentId}/`);
    return res.data as TInterview;
}


export const UseGetInterviewDetails = (assessmentId: string) => {
    return useQuery({
        queryKey: ['get-interview', assessmentId],
        queryFn: () => getInterview(assessmentId),
        enabled: !!assessmentId
    })
}