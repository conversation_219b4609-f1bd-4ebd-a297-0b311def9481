import { Axios } from "@/lib/api/axios";
import { useMutation } from "@tanstack/react-query";

interface RootObject {
    assessment_interview: string;
    candidates: Candidate[];
    auth_method: any[];
    custom_email_subject?: string;
    custom_email_message?: string;
    cover_image?: string;
    is_from_job_post: boolean;
    invite_document?: any;
}

interface Candidate {
    name: string;
    email: string;
}


const sendInvite = async (data: RootObject) => {
    const res = await Axios.post(`/assessments/interview-invites/`, data,
        {
            headers: {

                'Content-Type': data.invite_document ? 'multipart/form-data' : 'application/json'
            }
        }
    )
    return res.data
}

export const useSendInterviewInvite = () => {
    return useMutation({
        mutationFn: sendInvite,
        mutationKey: ['send-invite']
    })
}