import { Axios } from "@/lib/api/axios"
import { useMutation } from "@tanstack/react-query"

type props = {
    interview_id: string
    questions: string[]
}
const removeQuestions = async ({ interview_id, questions }: props) => {
    const res = await Axios.post(`assessments/assessment-interview/${interview_id}/`, { remove_questions: questions });
    return res.data
}


export const UseRemoveQuestionsFromInterview = () => {
    return useMutation({
        mutationFn: removeQuestions,
        mutationKey: ['remove-question-from-interview']
    })
}