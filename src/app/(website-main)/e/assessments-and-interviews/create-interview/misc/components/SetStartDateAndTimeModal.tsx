import { But<PERSON>, DateTime<PERSON>icker, Modal } from '@/components/shared'
import { Axios } from '@/lib/api/axios'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import { format } from 'date-fns'
import { useParams } from 'next/navigation'
import React from 'react'
import { Controller, useForm } from 'react-hook-form'
import toast from 'react-hot-toast'
import { z, ZodError } from 'zod'
import useInterviewCreatorStore from '../store'


const SetStartDateAndTimeModalSchema = z.object({
    start_time: z.date(),
    deadline: z.date().optional(),
}).refine(data => {
    if (data.deadline && data.start_time > data.deadline!) {
        throw new ZodError([{ code: "custom", message: "Start time must be before deadline", path: ["start_time"] }])
    }
    const today = new Date()
    if (data.start_time < today) {
        throw new ZodError([{ code: "custom", message: "Start time must be in the future", path: ["start_time"] }])
    }
    if (data.deadline && data.deadline < today) {
        throw new ZodError([{ code: "custom", message: "Deadline must be in the future", path: ["deadline"] }])
    }
    return true
})

export type SetStartDateAndTimeModalFormData = z.infer<typeof SetStartDateAndTimeModalSchema>
interface SetStartDateAndTimeModalProps {
    isModalOpen: boolean;
    closeModal: (isClosedClicked?: boolean) => void;
}
const SetStartDateAndTimeModal: React.FC<SetStartDateAndTimeModalProps> = ({ isModalOpen, closeModal }) => {
    const params = useParams()
    const start_time = useInterviewCreatorStore(state => state.start_time)
    const deadline = useInterviewCreatorStore(state => state.deadline)
    const interview_id = useInterviewCreatorStore(state => state.id)
    const { control, handleSubmit, watch, trigger, formState: { errors, isValid }, } = useForm<SetStartDateAndTimeModalFormData>({
        resolver: zodResolver(SetStartDateAndTimeModalSchema),
        defaultValues: {
            start_time: new Date(start_time || new Date()),
            deadline: new Date(deadline || new Date()),
        },
        mode: 'onChange'
    })
    const { mutate: setTimes } = useMutation({
        mutationFn: async () => {
            const start_time = watch('start_time')
            const deadline = watch('deadline')
            const local_start_time = start_time
                ? `${format(start_time, 'yyyy-MM-dd')}T${start_time.toLocaleTimeString(undefined, { hourCycle: 'h23', hour: '2-digit', minute: '2-digit', second: '2-digit' })}`
                : undefined;
            const local_deadline = deadline
                ? `${format(deadline, 'yyyy-MM-dd')}T${deadline.toLocaleTimeString(undefined, { hourCycle: 'h23', hour: '2-digit', minute: '2-digit', second: '2-digit' })}`
                : undefined;
            const res = await Axios.patch(`assessments/assessment-interview/${params.id || interview_id}/`, {
                start_time: local_start_time,
                deadline: local_deadline
            })
            return res.data
        },
    })
    const submitForm = () => {
        setTimes(undefined, {
            onSuccess() {
                closeModal(false)
                toast.success('Start date and time set successfully')
            },
        })
    }

    return (
        <Modal
            heading='Set Start Date and Time'
            isModalOpen={isModalOpen}
            closeModal={() => closeModal(true)}
        >
            <form className="space-y-4" onSubmit={handleSubmit(submitForm)}>
                <div className="space-y-1">
                    <h3 className="text-header-text text-sm font-semibold">Date and time settings</h3>
                    <p className="text-xxs md:text-[0.735rem] text-body-text">
                        Specify the start and end date and time for candidates to start and
                        complete this assessment.
                    </p>
                </div>
                <Controller
                    name="start_time"
                    control={control}
                    render={({ field }) => (
                        <DateTimePicker
                            label="Start date and time"
                            labelClassName='!font-normal text-[0.82rem]'
                            className="max-w-[350px] text-sm"
                            value={field.value}
                            onChange={field.onChange}
                            hasError={!!errors.start_time}
                            errorMessage={errors.start_time?.message}
                            variant="showcase"
                        />
                    )}
                />

                <Controller
                    name="deadline"
                    control={control}
                    render={({ field }) => (
                        <DateTimePicker
                            label="End date and time"
                            labelClassName='!font-normal text-[0.82rem]'
                            className="max-w-[350px] text-sm "
                            value={field.value || new Date()}
                            onChange={field.onChange}
                            hasError={!!errors.start_time}
                            errorMessage={errors.start_time?.message}
                            variant="showcase"
                        />
                    )}
                />

                <div className="flex justify-center items-center p-5">
                    <Button
                        className="w-full"
                        type="submit"
                    >
                        Set date and time
                    </Button>
                </div>

            </form>

        </Modal>
    )
}

export default SetStartDateAndTimeModal