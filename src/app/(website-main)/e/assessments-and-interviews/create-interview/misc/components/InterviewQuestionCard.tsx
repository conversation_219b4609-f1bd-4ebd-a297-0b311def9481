import React from 'react'
import { TInterviewQuestion } from '../types'
import { Button, ConfirmDeleteModal } from '@/components/shared'
import { useBooleanStateControl } from '@/hooks'
import { cn } from '@/utils'
import { CloseSquare } from 'iconsax-react'
import { UseDeleteQuestionsFromInterview } from '../api'
import { useQueryClient } from '@tanstack/react-query'
import toast from 'react-hot-toast'

interface InterviewQuestionCardProps {
  question: TInterviewQuestion
  index: number
  interview_id: string
}

const InterviewQuestionCard: React.FC<InterviewQuestionCardProps> = ({ question, interview_id, index }) => {

  const {
    state: isConfirmDeleteModalOpen,
    setTrue: openConfirmDeleteModal,
    setFalse: closeConfirmDeleteModal
  } = useBooleanStateControl()

  const { mutate: removeQuestion, isLoading: isDeletingQuestion } = UseDeleteQuestionsFromInterview()
  const queryClient = useQueryClient()
  const handleRemoveQuestionFromInterview = () => {
    removeQuestion(
      {
        interview_id,
        questions: [question.id],
      },
      {
        onSuccess() {
          queryClient.invalidateQueries({
            queryKey: ['get-interview', interview_id]
          })
          toast.success("Removed successfully")
          closeConfirmDeleteModal()
        },
      }
    )

  }

  return (
    <article className={cn("p-1.5 border-2 border-transparent  rounded-2xl bg-white")}>
      <div className="bg-light-accent-bg h-full w-full rounded-xl min-h-[120px]" >
        <header className="flex items-center justify-between rounded-xl bg-[#ECE9FF] p-2 text-left capitalize text-primary">
          <h2 className='text-sm'>
            Question {index + 1}
          </h2>
          <div className="flex items-center gap-2">
            <Button
              size="icon"
              variant="extralight"
              title="delete section"
              className="h-7 w-7"
              onClick={openConfirmDeleteModal}
            >
              <p className="sr-only">Delete section</p>
              <CloseSquare size={20} />
            </Button>


          </div>
        </header>

        <div
          dangerouslySetInnerHTML={{ __html: question.question }}
          className="text-[0.725rem] !text-primary !px-2 py-1.5 "
        />
      </div>


      <ConfirmDeleteModal
        isModalOpen={isConfirmDeleteModalOpen}
        closeModal={closeConfirmDeleteModal}
        deleteFunction={handleRemoveQuestionFromInterview}
        isDeleting={isDeletingQuestion}
        title="Delete Question"
      >
        <p>
          Are you sure you want to delete this question?
        </p>
      </ConfirmDeleteModal>
    </article>
  )
}

export default InterviewQuestionCard