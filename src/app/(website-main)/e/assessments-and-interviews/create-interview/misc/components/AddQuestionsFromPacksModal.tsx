import React from 'react'
import { useParams } from 'next/navigation'
import { CloseSquare } from 'iconsax-react'
import toast from 'react-hot-toast'

import { But<PERSON>, Checkbox2, LoaderBtn, Modal } from '@/components/shared'
import { useQueryClient } from '@tanstack/react-query'

import { useInterviewCreatorStore } from '../store'
import { TInterviewQuestionPack } from '../types'
import { UseAddQuestionsToInterview, UseDeleteQuestionsFromInterview } from '../api'

interface AddQuestionsToSectionFromPacksModalProps {
    isModalOpen: boolean
    closeModal: () => void
    question_pack: TInterviewQuestionPack
}
const AddQuestionsToSectionFromPacksModal = ({ isModalOpen, closeModal, question_pack, }: AddQuestionsToSectionFromPacksModalProps) => {
    const params = useParams()

    const [selectedQuestions, setSelectedQuestions] = React.useState<string[]>([])
    const questions = useInterviewCreatorStore(state => state.questions)
    const allQuestionsIds = questions.map(question => question.id)
    const previouslySelected = questions.map(question => question.id)

    const { mutate: addQuestions, isLoading: isAddingQuestion } = UseAddQuestionsToInterview()
    const { mutate: removeQuestion } = UseDeleteQuestionsFromInterview()
    const queryClient = useQueryClient()
    const handleAddQuestionsToSection = (questions?: string[]) => {
        addQuestions(
            {
                interview_id: params.id as string,
                questions: questions || selectedQuestions,
            },
            {
                onSuccess() {
                    queryClient.invalidateQueries({
                        queryKey: ['get-interview', params.id as string]
                    })
                    setSelectedQuestions([])
                    toast.success("Added successfully")
                    closeModal()
                },
            }
        )
    }

    const handleRemoveQuestionFromSection = (question_id: string,) => {
        removeQuestion(
            {
                interview_id: params.id as string,
                questions: [question_id],
            },
            {
                onSuccess() {
                    queryClient.invalidateQueries({
                        queryKey: ['get-interview', params.id as string]
                    })
                    toast.success("Removed successfully")
                },
            }
        )

    }


    return (
        <Modal
            heading='Add Questions'
            customWidths
            contentClass='max-w-[650px] w-full'
            isModalOpen={isModalOpen}
            closeModal={closeModal}
            className='relative '
            footer={
                <div className="grid grid-cols-2 gap-4 p-4 px-6 lg:py-6 w-full">
                    <Button
                        disabled={
                            !question_pack.question_set.length ||
                            previouslySelected.length === question_pack.question_set.length
                        }
                        onClick={() => {
                            const allIdsExcludingPreviouslySelected = question_pack.question_set
                                .filter(question => !previouslySelected.includes(question.id))
                                .map(question => question.id);

                            setSelectedQuestions(allIdsExcludingPreviouslySelected)
                            handleAddQuestionsToSection(allIdsExcludingPreviouslySelected)
                        }}
                        variant="outlined"
                    >
                        Add all ({question_pack?.question_set.length - (previouslySelected?.length || 0)}) questions
                    </Button>
                    <Button
                        variant={!selectedQuestions.length ? "extralight" : "default"}
                        disabled={!selectedQuestions.length}
                        onClick={() => handleAddQuestionsToSection()}
                    >
                        Add selected ({selectedQuestions.length}) questions
                        {
                            isAddingQuestion && <LoaderBtn />
                        }
                    </Button>
                </div>
            }

        >
            <header className="bg-grey rounded-xl p-2 px-4 mb-4">
                <h2 className="text-header-text text-lg font-medium">{question_pack?.name}</h2>
                <p className="text-sm text-helper-text">{question_pack?.description}</p>
            </header>
            <div className="pb-8">
                <ul className="space-y-2">
                    {
                        question_pack?.question_set.map((question, index) => (
                            <li key={index}>
                                {
                                    allQuestionsIds?.includes(question.id) ?

                                        <div className="p-4 rounded-md border block" >
                                            <div className="flex justify-between items-center">
                                                <h2 className="text-lg text-header-text font-medium"> Question {index + 1} </h2>
                                                <Button
                                                    className="text-xs h-7 w-7"
                                                    size="icon"
                                                    variant="red"
                                                    onClick={() => handleRemoveQuestionFromSection(question.id)}
                                                    title='Remove question'
                                                >
                                                    <CloseSquare size={20} />
                                                </Button >
                                            </div>

                                            <div
                                                className="!text-body-text !text-[0.82rem] pr-4 lg:pr-8 "
                                                dangerouslySetInnerHTML={{ __html: question.question }}
                                            />
                                        </div>
                                        :
                                        <label className="p-4 rounded-md border block" >
                                            <div className="flex justify-between items-center">
                                                <h2 className="text-lg"> Question {index + 1} </h2>

                                                <Checkbox2
                                                    checked={selectedQuestions.includes(question.id)}
                                                    onCheckedChange={(checked) => {
                                                        if (checked) {
                                                            setSelectedQuestions([...selectedQuestions, question.id])
                                                        }
                                                        else {

                                                            setSelectedQuestions(selectedQuestions.toSpliced(selectedQuestions.indexOf(question.id), 1))
                                                        }
                                                    }}
                                                    name="select question"
                                                    value={question.id}
                                                />

                                            </div>
                                            <div
                                                className="!text-body-text !text-[0.82rem] pr-4 lg:pr-8 "
                                                dangerouslySetInnerHTML={{ __html: question.question }}
                                            />
                                        </label>

                                }
                            </li>
                        ))}
                </ul>
            </div>
        </Modal>
    )
}

export default AddQuestionsToSectionFromPacksModal