import React from 'react'
import { SearchNormal } from 'iconsax-react';

import { cn } from '@/utils';
import { Button, EmptyCard, Input } from '@/components/shared';
import { Spinner } from '@/components/shared/icons';

import { useInterviewCreatorStore } from '../store';
import { useCreateInterviewValidation } from '../contexts';
import { TInterviewQuestionPack } from '../types';
import { useGetInterviewPacks } from '../api/getInterviewPacks';
import InterviewPackCard from './InterviewPackCard';
import InterviewQuestionCard from './InterviewQuestionCard';

const InterviewCreatorContentStep1AddQuestions = () => {

    // const { mutate: getRecommendations, isLoading: isGettingRecommendations } = UseGetAssessmentRecommendation()
    const { data: customInterviewPacks, isLoading: isGettingInterviewPacks } = useGetInterviewPacks()

    const questions = useInterviewCreatorStore(state => state.questions)
    const interviewId = useInterviewCreatorStore(state => state.id)
    const isInterviewCustom = useInterviewCreatorStore(state => state.id !== "custom")


    const [searchTerm, setSearchTerm] = React.useState('')
    const [filteredQuestionPacks, setFilteredQuestionPacks] = React.useState<TInterviewQuestionPack[]>(customInterviewPacks ?? [])
    // const [recommendations, setRecommendations] = React.useState<TRecommendedAssessmentQuestionPack[]>([])
    const [filteredRecommendations, setFilteredRecommendations] = React.useState<TInterviewQuestionPack[]>([])


    React.useEffect(() => {
        if (searchTerm.trim() !== '') {
            if (isInterviewCustom) {
                const filteredQuestionPacks = customInterviewPacks?.filter(questionPack => {
                    return (
                        questionPack.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        questionPack.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        questionPack.question_set.some(tag => tag.question.toLowerCase().includes(searchTerm.toLowerCase()))
                    );
                });
                setFilteredQuestionPacks(filteredQuestionPacks || []);
            }
            else {

                // const filteredRecommendations = recommendations.filter(recommendation => {
                //     return (
                //         recommendation.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                //         recommendation.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                //         recommendation.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
                //     );
                // });
                // setFilteredRecommendations(filteredRecommendations);
            }
        }
        else {
            if (isInterviewCustom) {
                setFilteredQuestionPacks(customInterviewPacks || [])
            }
            else {

            }
        }
    }, [searchTerm, customInterviewPacks])

    const { registerValidation } = useCreateInterviewValidation();

    React.useEffect(() => {
        if (questions.length) {
            registerValidation(1, async () => {
                return true;
            });
        }

        else {
            registerValidation(1, async () => {
                return false;
            },
                "Please add at least one section to proceed"
            )
        }
    }, [registerValidation, questions]);





    return (
        <div className="h-full overflow-y-scroll space-y-5">
            <section>
                <header className="p-1.5 px-3">
                    <h4 className="font-semibold text-header-text">Question questions</h4>
                    <p className='text-sm text-body-text'>
                        Select the section you want to add questions to or create a new section
                    </p>
                </header>
                <div className='grid lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-2.5'>

                    {
                        questions.map((question, index) => {
                            return (
                                <InterviewQuestionCard
                                    question={question}
                                    key={index}
                                    index={index}
                                    interview_id={interviewId}
                                />
                            )

                        })
                    }
                    {
                        Array.from({ length: 5 - (questions.length || 0) }).map((_, index) => {
                            return (
                                <article key={index} className='flex items-center justify-center min-h-[120px] bg-white rounded-xl'>
                                    <Button variant="extralight">
                                        Question {questions.length + index + 1}
                                    </Button>
                                </article>
                            );
                        })
                    }

                </div>
            </section>

            <section className="flex items-center">
                <Input
                    rightIcon={<SearchNormal size={18} />}
                    type="text"
                    name="search assessments"
                    placeholder="Search"
                    // variant="showcase"
                    className='!h-[2.5rem] 2xl:!h-[2.5rem]'
                    containerClassName="max-w-[300px] text-sm "
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                />
            </section>


            <section>
                {
                    (isGettingInterviewPacks) ?
                        <div className='flex items-center justify-center w-full h-full'>
                            <Spinner />
                        </div>
                        :
                        <>

                            <div
                                className={cn("grid items-stretch  gap-4",
                                    ((!isInterviewCustom && !filteredRecommendations.length) || (isInterviewCustom && !filteredQuestionPacks.length)) && "flex items-center justify-center w-full py-12",
                                    ((!isInterviewCustom && filteredRecommendations.length && filteredRecommendations.length > 3) || (isInterviewCustom && filteredQuestionPacks.length && filteredQuestionPacks.length > 3)) ?
                                        "md:grid-cols-[repeat(auto-fit,minmax(240px,1fr))]"
                                        :
                                        "md:grid-cols-[repeat(auto-fill,minmax(240px,1fr))]"
                                )}
                            >

                                {
                                    filteredRecommendations?.map((recommendation, index) => {
                                        return (
                                            <>
                                            </>
                                        )
                                    })
                                }
                                {
                                    filteredQuestionPacks?.map((questionPack, index) => {
                                        return (
                                            <InterviewPackCard
                                                key={index}
                                                question_pack={questionPack}
                                            />
                                        )
                                    })
                                }
                                {
                                    searchTerm.trim() !== '' && ((!isInterviewCustom && filteredRecommendations.length === 0) || (isInterviewCustom && filteredQuestionPacks.length == 0)) &&
                                    <EmptyCard
                                        icon={<img src="/images/create-assessments/no-assessment.png" alt="writing with pen" />}
                                        title="No recommended pack matches your filters"
                                        content={
                                            <div className="flex flex-col items-center justify-center gap-3 p-2.5 text-balance text-sm text-body-text">
                                                No recommendations available for this search term
                                                <Button
                                                    size="tiny"
                                                    variant="extralight"
                                                    onClick={() => setSearchTerm('')}
                                                >
                                                    Clear Search
                                                </Button>
                                            </div>
                                        }
                                        titleClass='pt-4 text-medium text-lg leading-tight'
                                    />
                                }
                                {/* {
                                    !isInterviewCustom && recommendations?.length === 0 &&
                                    <EmptyCard
                                        icon={
                                            <img
                                                src="/images/create-assessments/no-assessment.png"
                                                alt="writing with pen"
                                            />
                                        }
                                        title="No Recommendations"
                                        content={
                                            <div className="flex flex-col items-center justify-center gap-3 p-2.5 text-balance text-sm text-body-text">
                                                No recommendations available for this role

                                            </div>
                                        }
                                    />
                                } */}
                            </div>
                        </>
                }
            </section>






        </div >
    )
}

export default InterviewCreatorContentStep1AddQuestions