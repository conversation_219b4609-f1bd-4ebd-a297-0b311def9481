import React, { <PERSON>E<PERSON>, use<PERSON><PERSON>back, useR<PERSON>, useEffect, useState, useReducer } from "react";
import "@/styles/quill.css"
import { useDropzone } from "react-dropzone";
import { blobToBase64 } from "@/lib/utils/functions";
import { Input, Modal } from "@/components/shared";

import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Underline from '@tiptap/extension-underline'
import TextAlign from '@tiptap/extension-text-align'
import BulletList from '@tiptap/extension-bullet-list'
import { default as TipTapLink } from '@tiptap/extension-link'
import ListItem from '@tiptap/extension-list-item'
import OrderedList from '@tiptap/extension-ordered-list'
import { Bold, Italic, Underline as UnderlineIcon, List, AlignLeft, AlignCenter, AlignRight, Upload, Strikethrough, ListOrdered, LinkIcon, Unlink, UploadIcon, } from 'lucide-react'
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Separator } from "@/components/shared"
import { Card, CardContent } from "@/components/shared"
import { Label } from "@/components/shared"
import { ToggleGroup, ToggleGroupItem } from "@/components/shared"
// import { Separator } from "@/components/ui/separator"
import { cn } from '@/utils'
import Image from 'next/image'


type FormDataType = {
    subject: string,
    message: string,
    cover_image: Blob | undefined,
}



interface Props {
    isModalOpen: boolean;
    closeModal: () => void;
    coverImageFile: File | null
    setCoverImageFile: (file: File | null) => void
    emailSubject: string
    setEmailSubject: (subject: string) => void
    emailMessage: string
    setEmailMessage: (message: string) => void
}

import { Node } from '@tiptap/core';

const TokenNode = Node.create({
    name: 'token',

    group: 'inline',

    inline: true,

    addAttributes() {
        return {
            value: {
                default: null,
            },
        };
    },

    parseHTML() {
        return [
            {
                tag: 'span.token-placeholder',
                getAttrs: (dom) => ({
                    value: dom.getAttribute('data-value'),
                }),
            },
        ];
    },

    renderHTML({ node }) {
        return [
            'span',
            { class: 'token-placeholder', 'data-value': node.attrs.value },
            `{{ ${node.attrs.value} }}`
        ];
    },
});



const EditOfferLetterModal: React.FC<Props> = ({
    isModalOpen,
    closeModal,
    coverImageFile,
    setCoverImageFile,
    emailSubject,
    setEmailSubject,
    emailMessage,
    setEmailMessage,


}) => {
    const editor = useEditor({
        extensions: [
            StarterKit.configure({
                bulletList: false,
                orderedList: false,
                listItem: false,
            }),
            TokenNode,
            Underline,
            TextAlign.configure({
                types: ['heading', 'paragraph'],
            }),
            BulletList,
            OrderedList,
            ListItem,
            TipTapLink.configure({
                openOnClick: false,
                autolink: true,
                defaultProtocol: 'https',
            }),
        ],
        onUpdate: ({ editor }) => {
            setEmailMessage(convertPlaceholdersToTokens(editor.getHTML()))
        },
    })
    const tokens = [
        { value: "assessment_name", title: "assessment name" },
        { value: "start_time", title: "start time" },
        { value: "invitation_link", title: "invitation link" },
        { value: "candidate_name", title: "candidate name" },
        { value: "candidate_email", title: "candidate email" },
        { value: "job_position", title: "job position" },
        { value: "company_name", title: "company name" },
        { value: "time_limit", title: "time limit" },
        { value: "start_date", title: "start date" },
        { value: "deadline", title: "deadline" },
        { value: "auth_method1", title: "auth method1" },
        { value: "auth_method2", title: "auth method2" },
        { value: "auth_method1_value", title: "auth method 1 value" },
        { value: "auth_method2_value", title: "auth method 2 value" },
    ]

    const insertTokenn = (token: string) => {
        if (editor) {
            editor.commands.insertContent(`{{ ${token} }}`)
        }
    }

    const insertToken = (token: string) => {
        if (editor) {
            editor.commands.insertContent(`<span class="token-placeholder" data-value="${token}">{{ ${token} }}</span>`);
            // editor.commands.insertContent(`<span class="token-placeholder" data-value="${token}"> {{ ${token} }} </span>`);
        }
    };
    const convertPlaceholdersToTokens = (htmlContent: string) => {
        const parser = new DOMParser();
        const dom = parser.parseFromString(htmlContent, "text/html");

        // Find all elements with the class name for token placeholders
        dom.querySelectorAll(".token-placeholder").forEach((tokenElement) => {
            const tokenValue = (tokenElement as HTMLElement).dataset.value;
            tokenElement.replaceWith(`{{ ${tokenValue} }}`); // format
        });

        return dom.body.innerHTML;
    };

    const [subjectError, setSubjectError] = useState(false);
    const [messageError, setMessageError] = useState(false);
    const [coverImageError, setCoverImageError] = useState(false);

    const [, forceUpdate] = useReducer((x) => x + 1, 0)

    useEffect(() => {
        if (editor) {
            editor.on('selectionUpdate', () => {
                forceUpdate()
            })
        }
    }, [editor])



    const setLink = useCallback(() => {
        if (editor) {
            const previousUrl = editor.getAttributes('link').href
            const url = window.prompt('URL', previousUrl)

            if (url === null) {
                return
            }

            if (url === '') {
                editor.chain().focus().extendMarkRange('link').unsetLink()
                    .run()
                return
            }

            editor.chain().focus().extendMarkRange('link').setLink({ href: url })
                .run()
        }
    }, [editor])

    const handleValidateBeforeSave = () => {
        console.log(
            "emailSubject", emailSubject,
            "emailMessage", emailMessage,
        )

        //if any field is filled
        if (emailSubject.trim() !== "" || editor?.getHTML().trim() !== "" || coverImageFile !== null) {
            //check individually for unfuilled fields
            if (emailSubject.trim() === "") {
                setSubjectError(true)
            } else if (editor?.getHTML().trim() === "") {
                setSubjectError(false)
                setMessageError(true)
            } else if (!coverImageFile) {
                setSubjectError(false)
                setMessageError(false)
                setCoverImageError(true)
            }
            else if (emailSubject.trim() !== "" && editor?.getHTML().trim() !== "" && !!coverImageFile) {
                setSubjectError(false)
                setMessageError(false)
                setCoverImageError(false)
                closeModal()
                return true;
            }
            return false;
        }
        else if (emailSubject.trim() === "" && (editor?.getHTML().trim() === "" || editor?.getHTML().trim() === "<p></p>") && coverImageFile == null) {
            setSubjectError(false)
            setMessageError(false)
            setCoverImageError(false)
            editor?.commands.setContent("");
            closeModal()
        }

    }
    const clearAndCancel = () => {
        setEmailSubject("")
        editor?.commands.setContent("");
        setEmailMessage("")
        setCoverImageFile(null)
        closeModal()
    }


    if (!editor) {
        return null
    }

    return (
        <Modal
            heading="Customize Custom Invite Emai"
            closeModal={closeModal} isModalOpen={isModalOpen}
            contentClass="w-[95vw] max-w-[1400px]"
            allowDismiss={false}
            customWidths
        // portalClass="grid grid-rows-[max-content,1fr,max-content] h-[calc(100vh_-_2rem)] overflow-y-hidden"
        >
            <form className="flex flex-col relative w-full h-full overflow-hidden bg-[#F8F9FB] " onSubmit={() => { }}>
                <div className="grid grid-cols-2 w-full p-4 gap-4 grow overflow-y-scroll">

                    <Card className="border-none">
                        <CardHeader className="text-lg 2xl:text-xl font-semibold">
                            Edit Invite Email
                        </CardHeader>
                        <CardContent className="">
                            <div className="space-y-6">
                                <Input
                                    label="You can change these fields to create a custom invitation email template"
                                    labelClass="text-sm text-helper-text"
                                    className="border rounded-xl p-4 w-full"
                                    id="email-subject" type="text"
                                    name="email subject" placeholder="Enter email subject here" value={emailSubject}
                                    onChange={(e) => setEmailSubject(e.target.value)}
                                    hasError={subjectError}
                                    errorMessage="This field is required"
                                    variant="showcase"
                                />


                                <FileUpload
                                    label="COVER IMAGE"
                                    accept={{ 'image/*': [] }}
                                    maxSizeMB={15}
                                    dragActiveClassName="bg-green-50 border-green-500"
                                    hint="Files types: PNG, JPG, Max size: 10MB"
                                    value={coverImageFile}
                                    onFileSelect={setCoverImageFile}
                                    onFileRemove={() => setCoverImageFile(null)}
                                />



                                {/* Token Buttons */}
                                <div className="text-body-text font-normal text-[0.85rem]">
                                    <Label className="mt-4 mb-3">To customize this email, click the placeholder options displayed below
                                        and place them where you will like to have them.
                                    </Label>
                                    <div className="flex flex-wrap gap-2 border border-[#E4E4E4] p-4 rounded-xl">
                                        {tokens.map((token) => (
                                            <Badge
                                                key={token.value}
                                                variant="light"
                                                shape="rounded"
                                                className='cursor-pointer border-none font-normal'
                                                size="lg"
                                                onClick={() => insertToken(token.value)}
                                            >
                                                {token.title}
                                            </Badge>
                                        ))}
                                    </div>
                                </div>


                                <article className="border-[0.7px] border-[#E4E4E4] rounded-xl">
                                    <header>
                                        <ToggleGroup type="multiple" className="justify-start flex-wrap border-b">
                                            <ToggleGroupItem
                                                value="bold"
                                                aria-label="Toggle bold"
                                                onClick={() => editor.chain().focus().toggleBold().run()}
                                                data-state={editor.isActive('bold') ? 'on' : 'off'}
                                            >
                                                <Bold className="h-4 w-4" />
                                            </ToggleGroupItem>
                                            <ToggleGroupItem
                                                value="italic"
                                                aria-label="Toggle italic"
                                                onClick={() => editor.chain().focus().toggleItalic().run()}
                                                data-state={editor.isActive('italic') ? 'on' : 'off'}
                                            >
                                                <Italic className="h-4 w-4" />
                                            </ToggleGroupItem>
                                            <ToggleGroupItem
                                                value="italic"
                                                aria-label="Toggle italic"
                                                onClick={() => editor.chain().focus().toggleStrike().run()}
                                                data-state={editor.isActive('italic') ? 'on' : 'off'}
                                            >
                                                <Strikethrough className="h-4 w-4" />
                                            </ToggleGroupItem>
                                            <ToggleGroupItem
                                                value="underline"
                                                aria-label="Toggle underline"
                                                onClick={() => editor.chain().focus().toggleUnderline().run()}
                                                data-state={editor.isActive('underline') ? 'on' : 'off'}
                                            >
                                                <UnderlineIcon className="h-4 w-4" />
                                            </ToggleGroupItem>

                                            <Separator orientation="vertical" className="mx-1 h-8" />

                                            <ToggleGroupItem
                                                value="left"
                                                aria-label="Align left"
                                                onClick={() => editor.chain().focus().setTextAlign('left').run()}
                                                data-state={editor.isActive({ textAlign: 'left' }) ? 'on' : 'off'}
                                            >
                                                <AlignLeft className="h-4 w-4" />
                                            </ToggleGroupItem>
                                            <ToggleGroupItem
                                                value="center"
                                                aria-label="Align center"
                                                onClick={() => editor.chain().focus().setTextAlign('center').run()}
                                                data-state={editor.isActive({ textAlign: 'center' }) ? 'on' : 'off'}
                                            >
                                                <AlignCenter className="h-4 w-4" />
                                            </ToggleGroupItem>
                                            <ToggleGroupItem
                                                value="right"
                                                aria-label="Align right"
                                                onClick={() => editor.chain().focus().setTextAlign('right').run()}
                                                data-state={editor.isActive({ textAlign: 'right' }) ? 'on' : 'off'}
                                            >
                                                <AlignRight className="h-4 w-4" />
                                            </ToggleGroupItem>

                                            <Separator orientation="vertical" className="mx-1 h-8" />

                                            <ToggleGroupItem
                                                value="bulletList"
                                                aria-label="Toggle bullet list"
                                                onClick={() => editor.chain().focus().toggleBulletList().run()}
                                                data-state={editor.isActive('bulletList') ? 'on' : 'off'}
                                            >
                                                <List className="h-4 w-4" />
                                            </ToggleGroupItem>
                                            <ToggleGroupItem
                                                value="orderedList"
                                                aria-label="Toggle ordered list"
                                                onClick={() => editor.chain().focus().toggleOrderedList().run()}
                                                data-state={editor.isActive('orderedList') ? 'on' : 'off'}
                                            >
                                                <ListOrdered className="h-4 w-4" />
                                            </ToggleGroupItem>

                                            <Separator orientation="vertical" className="mx-1 h-8" />

                                            <ToggleGroupItem
                                                value="bulletList"
                                                aria-label="Toggle bullet list"
                                                onClick={setLink}
                                                data-state={editor.isActive('link') ? 'on' : 'off'}
                                            >
                                                <LinkIcon className="h-4 w-4" />
                                            </ToggleGroupItem>
                                            <ToggleGroupItem
                                                value="orderedList"
                                                aria-label="Toggle ordered list"
                                                onClick={() => editor.chain().focus().unsetLink().run()}
                                                disabled={!editor.isActive('link')}
                                            >
                                                <Unlink className="h-4 w-4" />
                                            </ToggleGroupItem>
                                        </ToggleGroup>
                                    </header>


                                    <section className="min-h-[500px]  p-4">
                                        <EditorContent
                                            editor={editor}
                                            value={emailMessage}
                                            className="prose max-w-none"
                                        />
                                    </section>
                                </article>
                            </div>
                        </CardContent>
                    </Card>


                    <Card>
                        <CardHeader className="text-lg 2xl:text-xl font-semibold">
                            Preview
                        </CardHeader>
                        <CardContent className="p-6 pt-3">
                            <h3 className="underline font-medium">
                                Subject: {emailSubject}
                            </h3>
                            {
                                coverImageFile &&
                                <div className="relative inline-block rounded-[0.975rem] h-[7.2rem] w-full">
                                    <Image
                                        src={URL.createObjectURL(coverImageFile)}
                                        alt="Cover Image Preview"
                                        fill
                                        objectFit='cover'
                                        className={cn("rounded-lg border border-gray-200")}
                                    />
                                </div>

                            }
                            <div className="tiptap max-w-none">
                                <div
                                    dangerouslySetInnerHTML={{ __html: convertPlaceholdersToTokens(editor.getHTML()) }}
                                    className="tiptap min-h-[100px] pt-4 prose max-w-none"
                                />

                                <section>
                                    <p className="font-semibold mt-6 mb-1.5">Interview Details:</p>
                                    <ul className="space-y-1 text-sm ml-2.5 mt-0 list-none">
                                        <p className="text-black/80">
                                            Interview Type: <span className="font-medium text-[#1C1C1C]">{"{ interview_name }"}</span> </p>
                                        <p className="text-black/80">
                                            Available from: <span className="font-medium text-[#1C1C1C]">{"{ start_date }"}</span> </p>
                                        <p className="text-black/80">
                                            Duration: <span className="font-medium text-[#1C1C1C]">{"{ time_limit }"}</span> </p>
                                        <p className="text-black/80">
                                            Must complete before: <span className="font-medium text-[#1C1C1C]">{"{ deadline }"}</span>. </p>
                                    </ul>
                                </section>

                                <button className="btn-primary text-sm mb-4" type="button">Join Interview</button>

                                <footer>
                                    <p className="flex flex-col text-sm text-black/80" >
                                        Please use the following credentials to access your interview:
                                        <span className="font-medium text-[#1C1C1C]">"{"{ auth_method1 }"}" - {"{ auth_method1_value }"} </span>
                                        <span className="font-medium text-[#1C1C1C]">"{"{ auth_method2 }"}" - {"{ auth_method2_value }"} </span>
                                    </p>
                                    <div className="mt-8">
                                        <p className="font-semibold text-header-text">Important Details</p>
                                        {/* ALLOWWWW 🌚🌚 */}
                                        <p style={{ fontSize: '14px', lineHeight: '24px', margin: '0px', paddingTop: '0.5rem', paddingBottom: '0.5rem' }}>
                                            Please ensure you have:
                                        </p>
                                        {/* ALLOWWWW 🌚🌚 */}
                                        <ol className="!list-none" style={{ fontSize: '14px', lineHeight: '24px', margin: '0px', paddingLeft: '1rem', paddingBottom: '0.5rem' }}>
                                            <li className="!list-none">A stable internet connection</li>
                                            <li className="!list-none">A quiet environment free from distractions</li>
                                            <li className="!list-none">A working webcam and microphone</li>
                                            <li className="!list-none">Any relevant documents or materials ready</li>
                                        </ol>
                                    </div>
                                </footer>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </form>

            <div className="sticky bottom-0 flex gap-4 items-center justify-end p-4 bg-white">
                <button type="button" onClick={clearAndCancel} className="btn-primary-light"> Clear and Cancel</button>
                <button onClick={handleValidateBeforeSave} type="submit" className="btn-primary"> Save </button>
            </div>
        </Modal>
    );
};

export default EditOfferLetterModal;
