import React from 'react'
import { truncateToLength } from '@/utils/strings';
import { Button } from '@/components/shared';
import AddQuestionsToSectionFromPacksModal from './AddQuestionsFromPacksModal';
import { useBooleanStateControl } from '@/hooks';
import { TInterviewQuestionPack } from '../types';

interface AssessmentCreatorContentProps {
    question_pack: TInterviewQuestionPack
}

const InterviewPackCard: React.FC<AssessmentCreatorContentProps> = ({ question_pack }) => {
    const {
        state: isAddQuestionsModalOpen,
        setTrue: openSelectQuestionsModal,
        setFalse: closeSelectQuestionsModal
    } = useBooleanStateControl()

    const {
        state: isDetailsSheetOpen,
        setTrue: openDetailsSheet,
        setFalse: closeDetailsSheet
    } = useBooleanStateControl()


    return (
        <article className='h-full rounded-md bg-white '>
            <div className="flex flex-col gap-y-4 relative rounded-xl overflow-hidden p-3.5 h-full">
                <span className="absolute rounded-none rounded-bl-xl bg-primary-light text-xxs py-1 px-5 text-primary top-0 right-0">
                    My pack
                </span>

                <h2 className="text- font-medium text-header-text h-[2lh] mt-2 overflow-hidden text-ellipsis" title={question_pack.name}>
                    {question_pack.name}
                </h2>
                <p className="text-xs text-body-text h-[5lh] overflow-hidden">{question_pack.description}</p>
                <section className="space-y-2.5">
                    <p className="text-sm text-header-text font-medium">
                        <span className="text-helper-text text-sm font-normal">
                            Total questions:
                        </span>{" "}
                        {question_pack.question_set.length}
                    </p>

                    <footer className="flex gap-2 justify-end text-xs mt-auto">

                        <Button
                            type="button"
                            onClick={openDetailsSheet}
                            size="tiny"
                            variant="extralight"
                        >
                            Details
                        </Button>

                        <Button
                            type="button"
                            onClick={openSelectQuestionsModal}
                            size="tiny"
                        >
                            Add
                        </Button>

                    </footer>

                </section>
            </div>

            <AddQuestionsToSectionFromPacksModal
                isModalOpen={isAddQuestionsModalOpen}
                closeModal={closeSelectQuestionsModal}
                question_pack={question_pack}
            />
            {/* <AssessmentRecomendationSheet
                isSheetOpen={isDetailsSheetOpen}
                closeSheet={closeDetailsSheet}
                question_pack={question_pack}
            /> */}
        </article>
    )
}

export default InterviewPackCard;

