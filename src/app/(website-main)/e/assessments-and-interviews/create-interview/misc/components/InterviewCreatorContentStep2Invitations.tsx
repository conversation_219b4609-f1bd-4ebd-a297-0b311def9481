'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { AxiosError } from 'axios';
import {
  Add,
  CardTick,
  CloseSquare,
  DocumentDownload,
  DocumentUpload,
} from 'iconsax-react';
import { useRouter } from 'next/navigation';
import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import { z } from 'zod';
// import { useAssesmentCreatorPartialUpdate, useSendAssessmentInvite } from '../api'
import { uploadToServer } from '@/app/(public-links)/jobs/misc/components/application/upload';
import {
  Button,
  Checkbox2,
  ErrorModal,
  LinkButton,
  LoaderBtn,
  Modal,
} from '@/components/shared';
import { Input } from '@/components/shared/input';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { Axios } from '@/lib/api/axios';
import { useLoading } from '@/lib/contexts/LoadingContext';
import { formatBytes, secondsToLargestUnit } from '@/lib/utils/functions';
import { cn } from '@/utils';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { ROLE_LEVELS_DICTIONARY } from '../../../misc/constants/constants';
import {
  AnalyzedFileErrorType,
  AnalyzedFileType,
} from '../../../misc/types/create-assessments';
import {
  useInterviewCreatorPartialUpdate,
  useSendInterviewInvite,
} from '../api';
import { useInterviewCreatorStore } from '../store';
import { htmlToTokens } from '../utils';
import AssessmentCustomInvitationsModal from './AssessmentCustomInvitationsModalWithTipTap';
import SetStartDateAndTimeModal from './SetStartDateAndTimeModal';

const schema = z
  .object({
    custom_email_subject: z.string().optional(),
    custom_email_message: z.string().optional(),
    cover_image: z.string().optional(),
    invitations: z.array(
      z.object({
        name: z
          .string({ message: 'Enter name' })
          .min(1, { message: 'Name is required' }),
        email: z
          .string({ message: 'Enter email' })
          .email({ message: 'Invalid email' }),
      })
    ),
    fileDetails: z
      .object({
        name: z.string(),
        size: z.number(),
      })
      .nullable(),

    selectedFileFields: z.array(z.string()).optional(),
    candidatePool: z.string().optional(),
  })
  .refine(data => {
    if (
      data.fileDetails &&
      (!data.selectedFileFields || data.selectedFileFields.length !== 2)
    ) {
      throw new z.ZodError([
        {
          path: ['selectedFileFields'],
          message: 'You must select exactly 2 fields from the file',
          code: 'custom',
        },
      ]);
    }
    return true;
  });

const InterviewCreatorContentStep2Invitations: React.FC = () => {
  const router = useRouter();
  const [invitationType, setInvitationType] = useState<
    'email' | 'file' | 'pool'
  >('email');
  const [analyzedFileDetails, setAnalyzedFileDetails] =
    useState<AnalyzedFileType>({} as AnalyzedFileType);
  const [isAnalyzingFile, setIsAnalyzingFile] = useState(false);
  const [analysisErrors, setAnalysisErrors] = useState<
    AnalyzedFileErrorType | undefined
  >(undefined);
  const [showSummary, setShowSummary] = useState(false);
  const [coverImageFile, setCoverImageFile] = useState<File | null>(null);
  const { isUploading } = useLoading();

  const { mutate: updateAssessment } = useInterviewCreatorPartialUpdate();
  const { mutate: sendInvite, isLoading: isSendingInvite } =
    useSendInterviewInvite();
  const assessment = useInterviewCreatorStore();

  const {
    state: isCustomInvitationModalOpen,
    setTrue: openCustomInvitationModal,
    setFalse: closeCustomInvitationModal,
  } = useBooleanStateControl();
  const { state: isSuccessModalOpen, setTrue: openSuccessModal } =
    useBooleanStateControl();

  const {
    isErrorModalOpen,
    openErrorModalWithMessage,
    closeErrorModal,
    setErrorModalMessage,
    setErrorModalState,
  } = useErrorModalState();

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: {
      custom_email_subject: '',
      custom_email_message: '',
      invitations: [{ name: '', email: '' }],
      fileDetails: null as File | null,
      selectedFileFields: [] as string[],
      candidatePool: '',
    },
    resolver: zodResolver(schema),
  });
  const fileDetails = watch('fileDetails');
  const selectedFileFields = watch('selectedFileFields');
  const invitations = watch('invitations');

  const questions = useInterviewCreatorStore(state => state.questions);
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'invitations',
  });

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      const file = acceptedFiles[0];
      setValue('fileDetails', file);
      uploadFileForAnalysis(file);
    },
    [setValue]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [],
      'application/vnd.ms-excel': [],
    },
    maxSize: 10 * 1024 * 1024, //10mb
  });

  const uploadFileForAnalysis = async (file: File) => {
    setIsAnalyzingFile(true);
    setAnalysisErrors(undefined);
    const formData = new FormData();
    formData.append('file', file);

    try {
      const res = await Axios.post('/assessments/analyze-excel/', formData);
      const result = res.data;
      if (result.error) {
        throw result;
      }
      setAnalyzedFileDetails(result);
      setValue('invitations', result.names_and_emails);
      setValue('selectedFileFields', []);
    } catch (error) {
      setAnalysisErrors(error as unknown as any);
    } finally {
      setIsAnalyzingFile(false);
    }
  };

  const onSubmit = () => {
    setShowSummary(true);
  };

  const handleFinalSubmit = async () => {
    const data = watch();
    let coverImageURL;
    if (coverImageFile) {
      const uploadedURL = await uploadToServer(coverImageFile);
      coverImageURL = uploadedURL.secure_url;
    }
    const email_message =
      data.custom_email_subject?.trim() !== ''
        ? btoa(
            await htmlToTokens({
              html_string: data.custom_email_subject,
              auth_type: 'email',
              cover_image: !!coverImageURL
                ? '{{ cover_image }}'
                : 'https://res.cloudinary.com/dvdnjlxa8/image/upload/v1696419285/Group_40747_thpo8x.png',
              is_interview: true,
            })
          )
        : undefined;
    if (invitationType === 'file') {
      const submitFormData = new FormData();
      submitFormData.append('assessment', assessment.id);
      if (data.fileDetails) {
        submitFormData.append('invite_document', data.fileDetails);
      }

      sendInvite(
        {
          assessment_interview: assessment.id,
          candidates: data.invitations,
          auth_method: data.selectedFileFields,
          is_from_job_post: false,
          invite_document: data.fileDetails,
          cover_image: coverImageURL,
          custom_email_subject: data.custom_email_subject,
          custom_email_message: email_message,
        },
        {
          onSuccess() {
            openSuccessModal();
            updateAssessment({
              assessment_id: assessment.id,
              data: {
                is_published: true,
                // last_page_url: '',
              },
            });
          },
          onError(error) {
            const errMessage = formatAxiosErrorMessage(error as AxiosError);
            openErrorModalWithMessage(errMessage);
          },
        }
      );
      // sendInvite()
    } else {
      sendInvite(
        {
          assessment_interview: assessment.id,
          candidates: data.invitations,
          auth_method: [],
          is_from_job_post: false,
          invite_document: null,
          cover_image: coverImageURL,
          custom_email_subject: data.custom_email_subject,
          custom_email_message: email_message,
        },
        {
          onSuccess() {
            openSuccessModal();
            updateAssessment({
              assessment_id: assessment.id,
              data: {
                is_published: true,
                // last_page_url: '',
              },
            });
          },
          onError(error) {
            const errMessage = formatAxiosErrorMessage(error as AxiosError);
            openErrorModalWithMessage(errMessage);
          },
        }
      );
    }
  };
  const {
    state: isDateAndTimeMoalOpen,
    setTrue: openDateAndTimeModal,
    setFalse: closeDateAndTimeModal,
  } = useBooleanStateControl(true);

  const total_time = assessment.questions.reduce(
    (acc, time) => (time.time ? time.time + acc : acc),
    0
  );

  return (
    <div className="grid h-full w-full overflow-y-scroll">
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="mx-auto flex w-full max-w-[900px] justify-center justify-self-center rounded-xl bg-white py-8 "
      >
        <div className="relative w-full max-w-[750px] space-y-6">
          {showSummary ? (
            <>
              <section className="space-y-4">
                <h1 className="text-xl font-semibold text-header-text">
                  Interview Assessment summary
                </h1>
                <div className="grid gap-2 md:grid-cols-3">
                  <div>
                    <p className="font-medium capitalize text-header-text">
                      {assessment.name}
                    </p>
                    <p className="text-sm text-helper-text">
                      Name of assessment
                    </p>
                  </div>
                  <div>
                    <p className="font-medium capitalize text-header-text">
                      {ROLE_LEVELS_DICTIONARY[assessment.role_level]?.name}
                    </p>
                    <p className="text-sm text-helper-text">Role level</p>
                  </div>
                  <div>
                    <p className="font-medium text-header-text">
                      {`${secondsToLargestUnit(total_time).time} ${
                        secondsToLargestUnit(total_time).unit
                      }`}
                    </p>
                    <p className="text-sm text-helper-text">Assessment time</p>
                  </div>
                  <div>
                    <p className="font-medium text-header-text">
                      {
                        watch('invitations').filter(
                          invitation => invitation.email !== ''
                        ).length
                      }
                    </p>
                    <p className="text-sm text-helper-text">No of invites</p>
                  </div>
                  <div>
                    <p className="font-medium text-header-text">
                      {questions.length}
                    </p>
                    <p className="text-sm text-helper-text">No of Questions</p>
                  </div>
                </div>
              </section>
              <section className="space-y-4">
                <h2 className="font-medium text-header-text">Questions:</h2>
                <ul className="space-y-1">
                  {questions?.map((section, index) => (
                    <li
                      key={index}
                      className="btn-base heading-text flex items-center justify-between bg-grey py-4 font-normal"
                    >
                      <div className="flex items-center gap-2">
                        <span className="flex h-[22px] w-[22px] items-center justify-center rounded-full bg-[#0E0E2C] text-white">
                          {index + 1}
                        </span>
                        <p className="px-1.5 text-sm text-body-text">
                          {section.question}
                        </p>
                      </div>
                      <div className="flex items-center gap-2 text-xs">
                        <DocumentDownload />
                      </div>
                    </li>
                  ))}
                </ul>
              </section>

              <div>
                <h3 className="font-medium text-header-text">Invitees:</h3>
                <ul className="space-y-3">
                  {invitations.map((invite, index) => (
                    <li
                      key={index}
                      className="grid grid-cols-[1fr,1fr,max-content] items-center gap-4"
                    >
                      <Input
                        value={invite.name}
                        variant="showcase"
                        readOnly
                        disabled
                      />
                      <Input
                        value={invite.email}
                        variant="showcase"
                        readOnly
                        disabled
                      />
                      {fields.length > 1 && (
                        <Button
                          type="button"
                          size="icon"
                          onClick={() => remove(index)}
                          variant="unstyled"
                        >
                          <CloseSquare className="bg-red-100 text-danger" />
                        </Button>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            </>
          ) : (
            <>
              <h2 className="text-2xl font-bold">Invite Candidates</h2>
              <div className="flex space-x-4 rounded-xl bg-primary-light px-3 py-1.5">
                <Button
                  className={cn(
                    'hover:bg-primary-light-hover',
                    invitationType === 'email' && '!bg-primary'
                  )}
                  size="thin"
                  onClick={() => setInvitationType('email')}
                  type="button"
                  variant={invitationType === 'email' ? 'default' : 'unstyled'}
                >
                  Email Invite
                </Button>
                <Button
                  className={cn(
                    'hover:bg-primary-light-hover',
                    invitationType === 'file' && '!bg-primary'
                  )}
                  size="thin"
                  onClick={() => setInvitationType('file')}
                  type="button"
                  variant={invitationType === 'file' ? 'default' : 'unstyled'}
                >
                  Bulk Invite
                </Button>
                <Button
                  className={cn(
                    'hover:bg-primary-light-hover',
                    invitationType === 'pool' && '!bg-primary'
                  )}
                  size="thin"
                  onClick={() => setInvitationType('pool')}
                  type="button"
                  variant={invitationType === 'pool' ? 'default' : 'unstyled'}
                >
                  Candidate Pool
                </Button>
              </div>

              {invitationType === 'email' && (
                <div className="space-y-4">
                  {fields.map((field, index) => (
                    <div
                      key={field.id}
                      className="flex w-full items-center space-x-4"
                    >
                      <Controller
                        name={`invitations.${index}.name`}
                        control={control}
                        render={({ field }) => (
                          <Input
                            label="Name"
                            hasError={!!errors?.invitations?.[index]?.name}
                            containerClassName="grow"
                            errorMessage={
                              errors?.invitations?.[index]?.name
                                ?.message as string
                            }
                            variant="showcase"
                            {...field}
                            placeholder="Name"
                          />
                        )}
                      />
                      <Controller
                        name={`invitations.${index}.email`}
                        control={control}
                        render={({ field }) => (
                          <Input
                            label="Email"
                            hasError={!!errors?.invitations?.[index]?.email}
                            containerClassName="grow"
                            errorMessage={
                              errors?.invitations?.[index]?.email
                                ?.message as string
                            }
                            variant="showcase"
                            {...field}
                            placeholder="Email"
                            type="email"
                          />
                        )}
                      />
                      {fields.length > 1 && (
                        <Button
                          type="button"
                          size="icon"
                          onClick={() => remove(index)}
                          variant="unstyled"
                        >
                          <CloseSquare />
                        </Button>
                      )}
                    </div>
                  ))}
                  <Button
                    type="button"
                    size="small"
                    variant="light"
                    onClick={() => append({ name: '', email: '' })}
                    icon={<Add />}
                  >
                    Add More
                  </Button>
                </div>
              )}

              {invitationType === 'file' && (
                <div>
                  <p className="text-sm text-body-text [text-wrap:balance]">
                    Here you can invite candidates in bulk by uploading an excel
                    file containing at least two colums, one column with the
                    <span className="font-medium text-header-text">
                      {' '}
                      candidates email
                    </span>{' '}
                    and then
                    <span className="font-medium text-header-text">
                      any other
                    </span>{' '}
                    desired details
                  </p>
                  <div className="flex gap-2">
                    <a
                      className="btn-primary-light mt-4 text-sm"
                      href="/files/assessment-and-interviews/getlinked_sample_invitations_template.xlsx"
                      target="_blank"
                    >
                      Download sample file
                    </a>
                    {/* <Link href="#" className="btn-primary-transparent text-sm">
                                        Learn more
                                    </Link> */}
                  </div>

                  {fileDetails ? (
                    <div className="mt-4 flex h-[5.9375rem] max-w-[60%] items-center gap-4 rounded-xl bg-primary-light p-3">
                      <div className="shrink-0">
                        <img
                          src="/images/icons/excel.png"
                          alt="excel icon"
                          className="scale-150"
                        />
                      </div>
                      <div className="grow">
                        <p className="text-sm font-medium text-primary">
                          {fileDetails.name}
                        </p>
                        <p className="text-xs text-header-text">
                          Size: {formatBytes(fileDetails.size)}
                        </p>
                        <p className="flex items-center gap-2">
                          analyzing
                          {isAnalyzingFile && <LoaderBtn />}
                        </p>
                      </div>
                      <div>
                        <button
                          onClick={() => {
                            setValue('fileDetails', null);
                            setAnalyzedFileDetails({} as AnalyzedFileType);
                          }}
                        >
                          <span className="flex aspect-square w-7 items-center justify-center rounded-full bg-white text-lg">
                            &times;
                          </span>
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div
                      {...getRootProps()}
                      className="mt-3 flex h-[5.9375rem] max-w-[60%] cursor-pointer items-center gap-4 rounded-xl border-[0.3px] border-dashed border-primary p-6"
                    >
                      <input {...getInputProps()} />
                      {isDragActive ? (
                        <p className="text-sm text-primary">
                          Drop the files here ...
                        </p>
                      ) : (
                        <div className="flex gap-4">
                          <div className="flex items-center justify-center rounded-full bg-primary-light p-3">
                            <DocumentUpload
                              className="text-primary"
                              size={24}
                            />
                          </div>
                          <div className="">
                            <p className="font-sans text-xs font-semibold text-primary sm:text-sm">
                              Tap to upload invite document
                            </p>
                            <span className="font-sans text-xs font-semibold text-primary opacity-75">
                              Files types: excel, Max size: 10MB
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                  {Object.keys(analyzedFileDetails).length > 0 && (
                    <div className="mt-6">
                      <p className="mb-2 text-sm text-body-text [text-wrap:balance]">
                        Determine how candidates get to access the assessments
                        by selecting from the options below.The default option
                        is an auto-generated{' '}
                        <span className="font-medium text-header-text">
                          &quot;Passcode&quot;{' '}
                        </span>
                        from Getlinked. This is mandatory to access assessment:
                      </p>
                      <section className="flex items-center gap-6 rounded-lg bg-primary-light px-4 py-3">
                        {analyzedFileDetails.fields?.map((field, index) => (
                          <label
                            key={index}
                            className="flex items-center space-x-2"
                          >
                            <Controller
                              name="selectedFileFields"
                              control={control}
                              render={({ field: { onChange, value } }) => (
                                <Checkbox2
                                  checked={value.includes(field)}
                                  className="h-5 w-5 rounded-lg"
                                  onCheckedChange={e => {
                                    const updatedFields = e
                                      ? [...value, field]
                                      : value.filter(
                                          (f: string) => f !== field
                                        );
                                    onChange(updatedFields);
                                  }}
                                />
                              )}
                            />
                            <span className="text-sm font-medium">{field}</span>
                          </label>
                        ))}
                      </section>
                    </div>
                  )}
                </div>
              )}

              {invitationType === 'pool' && (
                <Controller
                  name="candidatePool"
                  control={control}
                  render={({ field }) => (
                    <Input {...field} placeholder="Search candidate pool" />
                  )}
                />
              )}
            </>
          )}

          <footer className="sticky bottom-0 mt-8 flex items-center justify-end gap-4 bg-white p-4 shadow-md">
            {showSummary ? (
              <div className="flex space-x-4">
                <Button
                  onClick={() => setShowSummary(false)}
                  type="button"
                  variant="extralight"
                >
                  Edit invitations
                </Button>
                <Button
                  onClick={openCustomInvitationModal}
                  variant="extralight"
                >
                  Customize invite email
                </Button>
                <Button onClick={handleFinalSubmit} type="button">
                  Publish interview
                  {isSendingInvite && <LoaderBtn />}
                </Button>
              </div>
            ) : (
              <Button type="submit">Add candidates and continue</Button>
            )}
          </footer>
        </div>

        <AssessmentCustomInvitationsModal
          coverImageFile={coverImageFile}
          setCoverImageFile={setCoverImageFile}
          emailMessage={watch('custom_email_message')}
          emailSubject={watch('custom_email_subject')}
          setEmailMessage={(value: string) =>
            setValue('custom_email_message', value)
          }
          setEmailSubject={(value: string) =>
            setValue('custom_email_subject', value)
          }
          isModalOpen={isCustomInvitationModalOpen}
          closeModal={closeCustomInvitationModal}
        />
        <Modal
          isModalOpen={isSuccessModalOpen}
          heading="Success"
          allowDismiss={false}
          bodyClass="p-0"
        >
          <div className="space-y-2 bg-[#F5F3FF] p-4 text-center text-sm text-[#675E8B]">
            <div className="flex flex-col items-center gap-2 px-12 py-4">
              <CardTick size={50} className="text-primary" />
              <h2 className="heading-2 text-primary">
                Interview assessment has been created successfully
              </h2>
              <p>
                Your '{assessment.name}' interview assessment has been
                successfully created, and invitations have been sent to{' '}
                {
                  watch('invitations').filter(invitation => invitation.name)
                    .length
                }{' '}
                candidates.
              </p>
            </div>
          </div>
          <div className="rounded-xl bg-white p-4">
            <div className="flex items-center justify-end gap-2">
              <LinkButton
                onClick={() => assessment.clearStore()}
                href={`/e/assessments-and-interviews/interviews/${assessment.id}`}
              >
                View assessment details
              </LinkButton>
            </div>
          </div>
        </Modal>

        <ErrorModal
          isErrorModalOpen={isErrorModalOpen}
          setErrorModalState={setErrorModalState}
          subheading="Something went wrong"
        >
          <div className="rounded-t-2xl bg-red-100 p-5">
            <Button variant="red" onClick={closeErrorModal}>
              Okay
            </Button>
          </div>
        </ErrorModal>

        <SetStartDateAndTimeModal
          closeModal={(backClicked?: boolean) => {
            closeDateAndTimeModal();
            backClicked && assessment.setStep(assessment.step - 1 || 1);
          }}
          isModalOpen={isDateAndTimeMoalOpen}
        />
      </form>
    </div>
  );
};

export default InterviewCreatorContentStep2Invitations;
