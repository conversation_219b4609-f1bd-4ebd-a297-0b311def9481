import React from 'react';
import Interview<PERSON><PERSON>ContentStep1AddQuestions from './InterviewCreatorContentStep1AddQuestions';
import InterviewCreatorContentStep2Invitations from './InterviewCreatorContentStep2Invitations';


interface InterviewCreatorContentProps {
    step: number;
}

const InterviewCreatorContent: React.FC<InterviewCreatorContentProps> = ({
    step,
}) => {

    const renderInterviewCreatorContent = () => {
        switch (step) {
            case 1:
                return (
                    <InterviewCreatorContentStep1AddQuestions />
                );
            case 2:
                return (
                    <InterviewCreatorContentStep2Invitations />
                );

            // case 4:
            //     return (
            //         <ResumeBuilderStep4Objective />
            //     );
            default:
                return null;
        }
    };

    return (
        <main className='grid max-h-full overflow-y-hidden rounded-[0.9375rem]'>
            {renderInterviewCreatorContent()}
        </main>
    )
};

export default InterviewCreatorContent;