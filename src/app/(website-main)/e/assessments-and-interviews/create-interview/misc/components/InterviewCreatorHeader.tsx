import React from 'react';
import { motion } from 'framer-motion';
import { convertKebabAndSnakeToTitleCase, convertToTitleCase } from '@/utils/strings';
import { cn } from '@/utils';
import useInterviewCreatorStore from '../store';
import { But<PERSON>, ErrorModal, LinkButton, LoaderBtn } from '@/components/shared';
import { useErrorModalState } from '@/hooks';
import { useCreateInterviewValidation } from '../contexts';

const interview_CREATOR_STEPS = {
  SECTIONS: 1,
  TEMPLATE: 2,
  CONTACT: 3,
  OBJECTIVE: 4,
  EXPERIENCE: 5,
  EDUCATION: 6,
  SKILLS: 7,
  PROJECT: 8,
  ADDITIONS: 9,
  SUMMARY: 10,
};

export default function InterviewCreatorHeader() {
  const step = useInterviewCreatorStore((state) => state.step)
  const interviewId = useInterviewCreatorStore((state) => state.id)
  const interviewName = useInterviewCreatorStore((state) => state.name)
  const interviewRole = useInterviewCreatorStore((state) => state.role)
  const interviewRoleLevel = useInterviewCreatorStore((state) => state.role_level)
  const interviewQuestions = useInterviewCreatorStore((state) => state.questions)

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const { validateStep, validationErrorMessage } = useCreateInterviewValidation()
  const { setStep, updateStore } = useInterviewCreatorStore(state => ({
    step: state.step,
    setStep: state.setStep,
    updateStore: state.updateStore
  }));

  // const { mutate: goToNextorPrevious, isLoading: isMoving } = useAssesmentCreatorProceedToNextStep()
  const handleNext = async () => {
    const isValid = await validateStep(step);
    if (isValid) {
      // goToNextorPrevious({
      //   last_page_url: step + 1,
      //   interview_id: interviewId
      // }, {
      //   onSuccess(data) {
      //     console.log(data)
      //     // updateStore({ ...data })
      //     setStep(step + 1);
      //   }
      // })
      setStep(step + 1);
    }
    else {
      openErrorModalWithMessage(validationErrorMessage || 'Please check your inputs and try again.')
    }
  };
  const handleBack = () => {
    setStep(step - 1);
  }

  return (
    <div className={cn(
      "flex items-center gap-5  px-4 py-2 mt-1.5",
      step < 4 ? "bg-white" : ""
    )}>
      <section className="flex items-start gap-5">

        {
          step > 1 ?
            <Button size="capsule" variant="extralight" onClick={handleBack}>
              Back
            </Button>
            :
            <LinkButton href="/e/assessments-and-interviews/interviews-library" size="capsule" variant="extralight">
              Back
            </LinkButton>
        }
        {
          step < 2 &&
          <div className="flex flex-col gap-1.5">
            <h1 className="text-header-text font-medium">
              {interviewName}
            </h1>
            <div className="flex items-center gap-5">
              <p className="text-helper-text text-xs md:text-[0.825rem]">
                Role:
                <span className='text-header-text ml-2'>
                  {interviewRole}
                </span>
              </p>
              <p className="text-helper-text text-xs md:text-[0.825rem]">
                Level:
                <span className='text-header-text ml-2'>
                  {convertKebabAndSnakeToTitleCase(interviewRoleLevel)}
                </span>
              </p>
              <p className="text-helper-text text-xs md:text-[0.825rem]">
                Total Questions:
                <span className='text-header-text ml-2'>
                  {interviewQuestions.length}
                </span>
              </p>
            </div>
            <div className="relative flex items-center justify-start">
              {
                Object.keys(interview_CREATOR_STEPS).map((stepper, index) => (
                  <React.Fragment key={index} >
                    {
                      index > 0 && index < 4 && (
                        <div className="flex items-center lg:gap-x-1 mr-3">
                          <motion.div
                            className="relative h-[6px] rounded-full bg-primary-light overflow-hidden"
                            initial={false}
                            animate={{
                              width: step >= index ? '60px' : '45px'
                            }}
                            transition={{ duration: 0.3 }}
                          >

                            <motion.div
                              className="absolute h-[6px] rounded-full bg-primary top-0 left-0"
                              initial={false}
                              animate={{
                                backgroundColor: step >= index ? 'rgb(117, 90, 226)' : 'rgb(229, 231, 235)',
                                width: step >= index ? '100%' : '0px'
                              }}
                              transition={{ duration: 0.3 }}
                            />
                          </motion.div>
                        </div>
                      )}
                  </React.Fragment>
                ))}
            </div>
          </div>
        }
      </section>
      {
        step < 2 &&
        <section className="flex items-center gap-4 ml-auto">
          {/* <Button
            size="small"
            variant="extralight"

          >
            Preview test
          </Button> */}

          <Button
            size="small"
            onClick={handleNext}
          >
            Proceed
            {/* {
              isMoving && <LoaderBtn />
            } */}
          </Button>
        </section>
      }
      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        heading='Complete all necessary fields'
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 hover:bg-red-950/80 px-1.5 sm:text-sm md:px-6"
            size="lg"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </div>
  )
}