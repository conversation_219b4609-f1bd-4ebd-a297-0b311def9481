import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { TInterview, TInterviewQuestion } from '../types';

const initialState: TInterview = {
    step: 1,
    id: '',
    name: '',
    questions: [],
    role: 0,
    role_level: '',
    creator: 0,
    start_time: null,
    deadline: null,
    total_questions: 0,
    no_candidates: 0,
    completed: 0,
    paused: [],
    ongoing: 0,
    not_started: 0,
    is_published: false,
    invite_info: {
        message: "No invites found for the interview."
    },
    candidates: []
};

interface InterviewStore extends TInterview {
    step: number;
    questions: TInterviewQuestion[];
    total_questions: number;
    setStep: (step: number) => void;
    setId: (id: string) => void;
    setName: (name: string) => void;
    setRoleLevel: (roleLevel: string) => void;
    setIsPublished: (isPublished: boolean) => void;
    setQuestions: (questions: TInterviewQuestion[]) => void;
    addQuestion: (question: TInterviewQuestion) => void;
    updateQuestion: (questionId: string, updatedQuestion: Partial<TInterviewQuestion>) => void;
    removeQuestion: (questionId: string) => void;
    updateStore: (updatedStore: Partial<InterviewStore>) => void;
    clearStore: () => void;
}

export const useInterviewCreatorStore = create<InterviewStore>()(
    devtools(
        persist(
            (set) => ({
                ...initialState,
                step: 1,
                setStep: (step) => set({ step }),
                setId: (id) => set({ id }),
                setName: (name) => set({ name }),
                setRoleLevel: (role_level) => set({ role_level }),
                setIsPublished: (is_published) => set({ is_published }),
                setQuestions: (questions) => set({ questions }),
                addQuestion: (question) => set((state) => ({
                    questions: [...state.questions, question],
                    total_questions: state.total_questions + 1
                })),
                updateQuestion: (questionId, updatedQuestion) => set((state) => ({
                    questions: state.questions.map((question) =>
                        question.id === questionId ? { ...question, ...updatedQuestion } : question
                    ),
                })),
                removeQuestion: (questionId) => set((state) => ({
                    questions: state.questions.filter((question) => question.id !== questionId),
                    total_questions: state.total_questions - 1
                })),
                updateStore: (updatedStore) => set(updatedStore),
                clearStore: () => set(initialState),
            }),
            {
                name: 'getlinked_ai_interview_store',
                getStorage: () => sessionStorage,
            }
        )
    )
);

export default useInterviewCreatorStore;

