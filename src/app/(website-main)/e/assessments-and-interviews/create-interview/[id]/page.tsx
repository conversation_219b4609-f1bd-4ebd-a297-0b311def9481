'use client'

import React from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';

import { cn } from '@/utils';

import useInterviewCreatorStore from '../misc/store';
import InterviewCreatorValidationProvider from '../misc/contexts';
import toast from 'react-hot-toast';
import Interview<PERSON><PERSON>Header from '../misc/components/InterviewCreatorHeader';
import Interview<PERSON><PERSON>Content from '../misc/components/InterviewCreatorContent';
import { UseGetInterviewDetails } from '../misc/api';

const InterviewCreator = () => {
    const updateStore = useInterviewCreatorStore(state => state.updateStore)
    const step = useInterviewCreatorStore(state => state.step)

    const router = useRouter()
    const params = useParams();
    const assessmentId = params.id;

    //// Gets id from params, then fetches assessment details
    //// Sets the store so details can be used at any point
    //// Refetches and ee-sets store if details change
    const { data, isLoading } = UseGetInterviewDetails(assessmentId as string)
    React.useEffect(() => {
        if (data && !isLoading) {
            updateStore(data)
        }
        else if (!isLoading && !data) {
            toast.error('Interview not found')
            router.push('/e/assessments-and-interviews/my-assessments')
        }
    }, [data, isLoading])

    return (
        //// Validation provider wraps the content of the creator
        //// This allows for validation to be used from even outside the steps
        //// So to move from step 2 to step 3, a validation can be run on the current form from outside of it
        <InterviewCreatorValidationProvider>
            <div className={cn(
                "relative h-full grid grid-rows-[max-content,1fr] gap-2.5 overflow-y-hidden rounded-[1.25rem] px-2",
                step < 4 ? "" : ""
            )}>
                <InterviewCreatorHeader />
                <InterviewCreatorContent step={step} />
            </div>
        </InterviewCreatorValidationProvider>
    );
};

export default InterviewCreator;