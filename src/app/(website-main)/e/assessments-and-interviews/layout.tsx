"use client"
import { AppHeaderRecruiter } from '@/components/shared'
import React from 'react'
interface Props {
    children: React.ReactNode
}

const AssessmentsandInterviewsLayout: React.FC<Props> = ({ children }) => {
    return (
        <main className="main">
            <div className='maincontentwithheader grid grid-rows-[max-content_1fr] flex-col bg-[#F8F9FB] h-full pt-0 max-md:pb-16'>
                <AppHeaderRecruiter title='Assessment and interview' className='bg-white md:!pt-2' />
                {children}
            </div>
        </main>
    )
}

export default AssessmentsandInterviewsLayout
