'use client';

import { useMutation, useQuery } from '@tanstack/react-query';
import Link from 'next/link';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import {
  Dispatch,
  FormEvent,
  SetStateAction,
  useEffect,
  useState,
} from 'react';
import {
  DragDropContext,
  Draggable,
  Droppable,
  DropResult,
} from 'react-beautiful-dnd';
import toast from 'react-hot-toast';
import { useDebouncedCallback } from 'use-debounce';
import {
  CANDIDATES_ASSESSMENT_STATUSES,
  PROCTORING_OPTIONS,
  PROCTORING_SECTION_OPTIONS,
  PROCTORING_TOLERANCE_OPTIONS,
  QUESITON_TYPES,
  ROLE_LEVELS_DICTIONARY,
  SECTION_OPTIONS,
  YES_OR_NO,
} from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants';
import {
  AssessmentObject,
  AssessmentResult,
  CandidateAssessmentStatus,
  CandidateInviteResult,
  FetchCandidateInviteResults,
  FetchCandidateResult,
  ProctoringOptions,
  Question,
  Section,
} from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import CaretDownIcon from '@/app/(website-main)/t/dashboard/misc/icons/CaretDown';
import Disclosure from '@/components/Disclosure';
import DocumentDownloadIcon from '@/components/icons/jsx/DocumentDownloadIcon';
// import DropdownMenu from "@/components/DropdownMenu";
import EllipsesVerticalIcon from '@/components/icons/jsx/EllipsesVerticalIcon';
import SearchIcon from '@/components/icons/jsx/SearchIcon';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  LinkButton,
} from '@/components/shared';
import Button from '@/components/shared/Button';
import ListBox from '@/components/shared/listbox';
import Loader from '@/components/shared/loader';
import Switch from '@/components/Switch';
import { useBooleanStateControl } from '@/hooks';
import app_fetch from '@/lib/api/appFetch.js';
import { downloadBlob, secondsToLargestUnit } from '@/lib/utils/functions';
import useAssessmentCreatorStore from '../../create-assessment/misc/store';
import { editAssessmentAPI } from '../../misc/api/Assessment';
import { deleteQuestionAPI, editQuestionAPI } from '../../misc/api/Questions';
import { fetchRoleAPI } from '../../misc/api/Roles';
import {
  changeOrder,
  deleteSectionAPI,
  editSectionAPI,
} from '../../misc/api/Sections';
import DeleteQuestionModal from '../../misc/components/DeleteQuestionModal';
import DeleteSectionModal from '../../misc/components/DeleteSectionModal';
import EditQuestionModal from '../../misc/components/EditQuestionModal';
import EditSectionModal from '../../misc/components/EditSectionModal';
import InviteCandidatesModal from '../../misc/components/InviteCandidatesModal';
import QuestionsListItem from '../../misc/components/QuestionsListItem';
import TimeInput from '../../misc/components/TimeInput';
import reorderSections from '../../misc/functions/reorderSections';
import AssessmentCandidatesList from './components/AssessmentCandidatesList';
import AssessmentInviteStatuses from './components/AssessmentInviteStatuses';
import Proctoring from './components/Proctoring/Proctoring';
import ResendInviteModal from './components/ResendInviteModal';
import useStrictDroppable from './hooks/useStrictEnabled';
import { ExportReportsPopover } from './misc/components';
import { OldDraggableSection } from './OldDraggableSections';

export default function Dashboard({
  params,
  searchParams: search_params,
}: {
  params: { assessment_id: string };
  searchParams?: {
    page?: string;
    search_filter?: string;
    status_filter?: string;
  };
}) {
  const path_name = usePathname();
  const searchParams = useSearchParams();
  const { replace } = useRouter();
  const [assessment, setAssessment] = useState(
    {} as AssessmentResult & AssessmentObject & { is_started: boolean }
  );

  const tabs = [
    {
      title: 'Candidates',
      query_param: undefined,
    },
    {
      title: 'Invite Status',
      query_param: 'invite_status',
    },
    {
      title: 'Assessment Details',
      query_param: 'assessment_details',
    },
    {
      title: 'More Details',
      query_param: 'more_details',
    },
    {
      title: 'Team Details',
      query_param: 'team_details',
    },
  ];

  const status_filter = search_params?.status_filter;
  const search_filter = search_params?.search_filter;
  const assessment_id = params.assessment_id;

  function filterData(
    filter_term: string | undefined,
    filter_key: 'search_filter' | 'status_filter'
  ) {
    const params = new URLSearchParams(searchParams.toString());
    if (filter_term) {
      params.set(filter_key, filter_term);
    } else {
      params.delete(filter_key);
    }
    replace(`${path_name}?${params.toString()}`);
  }

  const setSearchFilter = useDebouncedCallback(term => {
    filterData(term, 'search_filter');
  }, 0);

  function setStatusFilter(status: string | undefined) {
    filterData(status, 'status_filter');
  }

  function clearFilters() {
    replace(`${path_name}`);
  }

  async function fetchCandidates(): Promise<FetchCandidateResult> {
    return await app_fetch(
      `assessments/candidate/result-detail/${assessment_id}`
    ).then(async response => {
      const res = await response.json();
      if (response.ok) {
        return res;
      } else {
        throw res.detail;
      }
    });
  }

  const {
    isSuccess: isFetchCandidatesSuccess,
    data: fetch_candidates_data,
    isLoading: isFetchingCandidates,
    isError: isFetchCandidatesError,
    error: fetchCandidatesError,
    refetch: refetchCandidates,
  } = useQuery({
    queryKey: ['candidates', `assessment=${assessment_id}`],
    queryFn: fetchCandidates,
  });

  // FETCH INVITE STATUSES

  async function fetchCandidateInviteStatuses(): Promise<FetchCandidateInviteResults> {
    return await app_fetch(
      `assessments/email-invite-logs?invite_id=${assessment.invite_info?.invite}`
    ).then(async response => {
      const res = await response.json();
      if (response.ok) {
        return res;
      } else {
        throw res.detail;
      }
    });
  }

  const {
    isSuccess: isFetchCandidatesInviteSuccess,
    data: fetch_candidate_invite_statuses,
    isLoading: isCandidateInviteStatusLoading,
    isError: isCandidateInviteStatusError,
    error: fetchCandidateInviteStatusError,
    refetch: refetchCandidateInviteStatus,
  } = useQuery({
    queryKey: ['invite-statuss', `assessment=${assessment_id}`],
    queryFn: fetchCandidateInviteStatuses,
    enabled: !!assessment?.invite_info?.invite,
  });

  // useEffect(() => {
  // refetchCandidateInviteStatus();
  // }, [assessment.invite_info]);

  const findTabFromQueryParams = () => {
    switch (useSearchParams().get('tab')) {
      case 'invite_status':
        return 1;
      case 'assessment_details':
        return 2;
      case 'team_details':
        return 3;
      case 'more_details':
        return 2;
      default:
        return 0;
    }
  };
  const [current_tab, setCurrentTab] = useState(findTabFromQueryParams());

  function getSwitch(_switch: ProctoringOptions) {
    switch (_switch) {
      case 'stop_screen_sharing':
      case 'invigilate_assessment':
        return _switch;
      default:
        return `is_${_switch}` as ProctoringOptions;
    }
  }

  function editAssessmentProctoringSwitch(_switch: ProctoringOptions) {
    const request_body = {} as typeof assessment;
    // @ts-ignore

    request_body[`is_${_switch}`] = !assessment[getSwitch(_switch)];
    request_body.id = assessment.id;
    // console.log(request_body, 'SWITCH BODY');
    return editAssessmentAPI(request_body);
  }

  function updateLiveAssessment(update: any) {
    let parsedUpdate = { ...update } as typeof assessment;
    parsedUpdate.id = assessment.id;
    // console.log(parsedUpdate, 'ASSESSMENT');
    setUpdateIsHappening(true);

    editAssessmentAPI(parsedUpdate)
      .then(() => {
        setAssessment({ ...assessment, ...update });
      })
      .finally(() => {
        setUpdateIsHappening(false);
        toast.success('Assessment Updated ');
      });
  }

  function setProctoringSwitch(
    _switch: ProctoringOptions,
    _new_switch_state: boolean
  ) {
    setIsSwitchBusy({ ...is_switch_busy, [getSwitch(_switch)]: true });
    setUpdateIsHappening(true);
    editAssessmentProctoringSwitch(_switch)
      .then(() => {
        // @ts-ignore
        setAssessment({
          ...assessment,
          // @ts-ignore
          [getSwitch(_switch)]: !assessment[getSwitch(_switch)],
        });
      })
      .finally(() => {
        setIsSwitchBusy({ ...is_switch_busy, [getSwitch(_switch)]: false });
        setUpdateIsHappening(false);
      });
  }

  const [updateisHapenning, setUpdateIsHappening] = useState<boolean>(false);
  const [is_switch_busy, setIsSwitchBusy] = useState(
    {} as Record<ProctoringOptions, boolean>
  );

  const [is_assessment_time_form_busy, setIsAssessmentTimeFormBusy] =
    useState(false);

  function editAssessmentTimeSettings(e: FormEvent) {
    e.preventDefault();
    if (assessment.time_limit <= 0) {
      alert('You need to set a time limit greater than 0');
      return;
    }

    if (new Date(assessment.deadline) <= new Date(assessment.start_time)) {
      toast.error('End time cannot be before start time');
      return;
    }

    setIsAssessmentTimeFormBusy(true);
    editAssessment([
      'commencement_settings',
      'time_limit',
      'start_time',
      'deadline',
    ]).finally(() => {
      setIsAssessmentTimeFormBusy(false);
      setIsEditTimingSettings(false);
      toast.success('Assessment updated');
    });
  }

  const [is_assessment_details_form_busy, setIsAssessmentDetailsFormBusy] =
    useState(false);

  function editAssessmentDetailsSettings(e: FormEvent) {
    e.preventDefault();
    setIsAssessmentDetailsFormBusy(true);
    editAssessment(['name', 'description']).finally(() => {
      setIsAssessmentDetailsFormBusy(false);
      setIsEditAssessmentDetails(false);
    });
  }

  type keyofAssessment = keyof typeof assessment;

  function editAssessment(parts: keyofAssessment[]) {
    const request_body = {} as typeof assessment;

    parts.map(field => {
      // @ts-ignore
      request_body[field] = assessment[field];
    });
    request_body.id = assessment.id;

    return editAssessmentAPI(request_body);
  }

  const [is_loading_assessment, setIsLoadingAssessment] = useState(true);

  function reloadAssessment() {
    let _assessment = {} as typeof assessment;
    setIsLoadingAssessment(true);
    app_fetch(`assessments/assessments-detail/${params.assessment_id}`)
      .then(response => response.json())
      .then(result => {
        console.log(result, 'ASSESMENT DETAILS');
        _assessment = { ...result, candidates: [] };
        setAssessment({ ..._assessment });
      })
      .finally(() => {
        app_fetch(`assessments/${params.assessment_id}`)
          .then(response => response.json())
          .then(result => {
            result.sections = result.sections.sort(
              (a: Section, b: Section) => a.order! - b.order!
            );
            _assessment = { ...result, ..._assessment };
            _assessment.commencement_settings =
              _assessment.commencement_settings ? 'yes' : 'no';
            _assessment.start_time = _assessment.start_time?.split('+')[0];
            _assessment.deadline = _assessment.deadline?.split('+')[0];
            _assessment.is_started = result.start_time
              ? new Date() > new Date(result?.start_time)
              : result.completed?.length + result.ongoing?.length == 0;

            setAssessment({ ..._assessment });
          })
          .finally(() => {
            setAssessment({ ..._assessment });
            setIsLoadingAssessment(false);
          });
      });
  }

  useEffect(() => {
    if (params.assessment_id) {
      reloadAssessment();
    }
  }, [params.assessment_id]);

  const [active_section_index, setActiveSectionIndex] = useState(0);
  const [active_section, setActiveSection] = useState(
    assessment?.sections?.[active_section_index]
  );
  useEffect(() => {
    setActiveSection(assessment?.sections?.[active_section_index]);
  }, [active_section_index, assessment.sections]);

  const [is_edit_section_modal_open, setIsEditSectionModalOpen] =
    useState(false);

  function closeEditSectionModal() {
    setIsEditSectionModalOpen(false);
    setActiveSectionIndex(0);
  }

  function openEditSectionModal() {
    setIsEditSectionModalOpen(true);
  }

  function editSection(section: Section, section_index: number) {
    return new Promise((resolve, reject) => {
      const question_types_counts = {} as Record<string, number>;
      QUESITON_TYPES.map(_question_type => {
        // @ts-ignore
        question_types_counts[_question_type.count_name] =
          // @ts-ignore
          section[_question_type.count_name] || 0;
      });

      editSectionAPI(section, assessment.id)
        .then(response => response.json())
        .then(() => {
          const new_sections = [...assessment.sections];
          // @ts-ignore
          new_sections[section_index] = section;
          setAssessment({ ...assessment, sections: new_sections });
          resolve({ message: 'success' });
        })
        .catch(error => {
          reject(error);
        });
    });
  }

  const [is_edit_timing_settings, setIsEditTimingSettings] = useState(false);
  const [is_edit_assessment_details, setIsEditAssessmentDetails] =
    useState(false);
  const [is_edit_proctoring_settings, setIsEditProctoringSettings] =
    useState(false);

  const [is_editing_section, setIsEditingSection] = useState(false);

  function handleEditSection(section: Section) {
    setIsEditingSection(true);
    editSection(section, active_section_index).finally(() => {
      setIsEditingSection(false);
      closeEditSectionModal();
    });
  }

  const [is_delete_section_modal_open, setIsDeleteSectionModalOpen] =
    useState(false);

  function openDeleteSectionModal() {
    setIsDeleteSectionModalOpen(true);
  }

  function closeDeleteSectionModal() {
    setIsDeleteSectionModalOpen(false);
  }

  function deleteSection(section_index: number) {
    return new Promise((resolve, reject) => {
      const section = assessment.sections[section_index];
      /* send a delete request */
      deleteSectionAPI(section.id, assessment.id)
        .then(_result => {
          /* if success, delete the section entry from the assessment */
          const new_sections = assessment.sections.splice(section_index, 1);
          setAssessment({ ...assessment, sections: new_sections });
          closeDeleteSectionModal();
          resolve({ message: 'success' });
        })
        .catch(e => reject(e))
        .finally(() => {
          closeDeleteSectionModal;
        });
    });
  }

  function handleSectionOption(_selected_option: (typeof SECTION_OPTIONS)[0]) {
    if (_selected_option.title == 'edit section') {
      openEditSectionModal();
    }
    if (_selected_option.title == 'delete section') {
      openDeleteSectionModal();
    }
  }

  const [is_edit_question_modal_open, setIsEditQuestionModalOpen] =
    useState(false);

  function closeEditQuestionModal() {
    setIsEditQuestionModalOpen(false);
  }

  function openEditQuestionModal() {
    setIsEditQuestionModalOpen(true);
  }

  const [active_question_index, setActiveQuestionIndex] = useState(0);
  const [is_editing_question, setIsEditingQuestion] = useState(false);

  function handleEditQuestion(edited_question: Question) {
    setIsEditingQuestion(true);
    editQuestionAPI(edited_question, active_section.id, assessment.id)
      .then(_result => {
        const section = assessment.sections[active_section_index];
        const new_sections = assessment.sections;
        const new_question_set = section.question_set;
        new_question_set[active_question_index] = edited_question; // result.question

        new_sections[active_section_index] = {
          ...section,
          question_set: new_question_set,
        };
        setAssessment({ ...assessment, sections: new_sections });
      })
      .finally(() => {
        setIsEditingQuestion(false);
        closeEditQuestionModal();
      });
  }

  function deleteQuestion(question_index: number, section_index: number) {
    return new Promise((resolve, reject) => {
      const active_question =
        assessment.sections[section_index]?.question_set[question_index];
      deleteQuestionAPI(active_question.id, active_section.id)
        .then(() => {
          const new_sections = assessment.sections;
          new_sections[section_index].question_set = new_sections[
            section_index
          ].question_set.filter(question => question.id !== active_question.id);
          setAssessment({ ...assessment, sections: new_sections });
          resolve({ message: 'success' });
        })
        .catch(e => reject({ error: e }));
    });
  }

  const [is_delete_question_modal_open, setIsDeleteQuestionModalOpen] =
    useState(false);

  function openDeleteQuestionModal() {
    setIsDeleteQuestionModalOpen(true);
  }

  function closeDeleteQuestionModal() {
    setIsDeleteQuestionModalOpen(false);
  }

  const [is_invite_candidates_modal_open, setIsInviteCandidatesModalOpen] =
    useState(false);

  function openInviteCandidatesModal() {
    setIsInviteCandidatesModalOpen(true);
  }

  function closeInviteCandidatesModal() {
    setIsInviteCandidatesModalOpen(false);
  }

  const [is_sending_report, setIsSendingReport] = useState(false);

  async function sendResult() {
    setIsSendingReport(true);
    const options = {
      method: 'POST',
      body: JSON.stringify({
        get_questions: 'true',
      }),
    };
    app_fetch(
      `assessments/export-assessment-report/${params.assessment_id}/`,
      options
    )
      .then(response => response.blob())
      .then(result => {
        downloadBlob(result, `getLinked_${params.assessment_id}.xlsx`);
        setIsSendingReport(false);
      });
  }

  const roles_query = useQuery({
    queryKey: ['roles', assessment?.role],
    queryFn: async () => await fetchRoleAPI(assessment.role),
    enabled: assessment?.role !== undefined,
  });

  const [enabled] = useStrictDroppable(false);

  function reorderSections(source_index: number, destination_index: number) {
    const _sections = assessment.sections.slice();
    const [removed] = _sections.splice(source_index, 1);
    _sections.splice(destination_index, 0, removed);
    return _sections;
  }

  async function changeOrder({
    source,
    destination,
    draggableId,
  }: Pick<DropResult, 'source' | 'destination' | 'draggableId'>) {
    const new_sections = reorderSections(source.index!, destination?.index!);
    return app_fetch(`assessments/${assessment.id}/reorder-sections/`, {
      method: 'PUT',
      body: JSON.stringify({
        new_order: new_sections.reduce((acc, curr) => {
          acc.push(curr.id);
          return acc;
        }, [] as string[]),
      }),
    }).then(res => res.json());
  }

  const changeOrderMutation = useMutation({
    mutationFn: changeOrder,
    onMutate: ({ source, destination, draggableId }) => {
      const original_sections = assessment.sections;
      const original_active_section_index = active_section_index;

      const _sections = reorderSections(source.index!, destination?.index!);

      setAssessment({
        ...assessment,
        sections: _sections,
      });

      if (active_section_index === source.index) {
        setActiveSectionIndex(destination?.index!);
      }

      return {
        original_sections,
        original_active_section_index,
      };
    },
    onError: (error, _vars, context) => {
      setActiveSectionIndex(context?.original_active_section_index!);
      setAssessment({ ...assessment, sections: context?.original_sections! });
    },
  });

  function handleChangeOrder({ source, destination, draggableId }: DropResult) {
    changeOrderMutation.mutate({ source, destination, draggableId });
  }
  const setAssessmentCreatorStep = useAssessmentCreatorStore(
    state => state.setStep
  );
  const last_page_url = assessment.last_page_url;
  const handleSetLastPage = () => {
    setAssessmentCreatorStep(Number(last_page_url || 1));
  };

  // @ts-ignore
  return (
    <div className="-mt-3 space-y-4">
      <section className="flex items-end justify-between gap-2 bg-white px-2 py-4 max-md:flex-wrap">
        <div className="flex items-start gap-4">
          <LinkButton
            href="/e/assessments-and-interviews/my-assessments"
            size="capsule"
            variant="extralight"
          >
            Back
          </LinkButton>
          <div className="space-y-1">
            <h1
              className={`${
                is_loading_assessment ? 'skeleton-loading' : ''
              } heading-2`}
            >
              {assessment.assessment_name}
            </h1>
            <ul className="heading-text flex gap-2 text-sm">
              {roles_query.isSuccess && (
                <li className="font-medium">
                  <span className="helper-text font-normal max-md:block">
                    Role:{' '}
                  </span>
                  {roles_query.data.name}
                </li>
              )}
              <li className="font-medium">
                <span className="helper-text font-normal max-md:block">
                  Role level:{' '}
                </span>
                <span
                  className={`${
                    is_loading_assessment ? 'skeleton-loading' : ''
                  } capitalize`}
                >
                  {' '}
                  {
                    ROLE_LEVELS_DICTIONARY[assessment.experience_level]?.name
                  }{' '}
                </span>
              </li>
              <li className="font-medium">
                <span className="helper-text font-normal max-md:block">
                  Total questions:{' '}
                </span>
                <span
                  className={`${
                    is_loading_assessment ? 'skeleton-loading' : ''
                  }`}
                >
                  {' '}
                  {assessment.total_no_questions}{' '}
                </span>
              </li>
            </ul>
          </div>
        </div>
        <div className="flex gap-2 max-md:ml-auto">
          <p>{assessment?.is_published}</p>
          {assessment?.is_published !== undefined && (
            <>
              {assessment?.is_published ? (
                <>
                  <div className="group/invite-btn relative">
                    <button
                      className="btn-primary-light"
                      onClick={openInviteCandidatesModal}
                      disabled={
                        assessment.commencement_settings == 'yes' &&
                        new Date(assessment.deadline) < new Date()
                      }
                    >
                      Invite candidates
                    </button>
                    {assessment.commencement_settings == 'yes' &&
                      new Date(assessment.deadline) < new Date() && (
                        <span className="absolute -bottom-10 hidden w-max -translate-x-[25%] items-center justify-center rounded-xl bg-grey p-2 text-center shadow-lg group-hover/invite-btn:flex">
                          You cannot invite after the deadline.
                        </span>
                      )}
                  </div>
                </>
              ) : (
                <>
                  <LinkButton
                    onClick={handleSetLastPage}
                    // href={`${path_name}/${assessment.last_page_url || 'edit'}`}
                    href={`/e/assessments-and-interviews/create-assessment/${
                      params.assessment_id || assessment.id
                    }`}
                    variant="extralight"
                  >
                    Continue editing
                  </LinkButton>
                </>
              )}
            </>
          )}
          {/* <button className="btn-primary-light" title="options">
            <EllipsesVerticalIcon />
          </button> */}
        </div>
      </section>

      <div className="space-y-1 text-sm">
        <section className="flex gap-4 rounded-xl bg-white p-2 md:p-8 max-md:flex-wrap max-md:justify-center">
          {/* TOP INFOS */}

          <ul className="flex items-center divide-x rounded-xl border p-2 text-center">
            <li className="px-4">
              <div className="space-y-1">
                <p
                  className={`${
                    is_loading_assessment ? 'skeleton-loading' : ''
                  } heading-1`}
                >
                  {assessment.total_invites}
                </p>
                <p className="text-xs">Invites</p>
              </div>
            </li>
            <li className="px-4">
              <div>
                <p
                  className={`${
                    is_loading_assessment ? 'skeleton-loading' : ''
                  } heading-1`}
                >
                  {assessment.completed}
                  <span
                    className={`text-xxs font-normal ${
                      is_loading_assessment ? '' : 'text-green-500'
                    }`}
                  >
                    /
                    {assessment?.no_candidates
                      ? Math.round(
                          (assessment.completed / assessment?.no_candidates) *
                            100
                        )
                      : 100}
                    %
                  </span>
                </p>
                <p className="text-xs">Completed</p>
              </div>
            </li>
            <li className="px-4">
              <div>
                <p
                  className={`${
                    is_loading_assessment ? 'skeleton-loading' : ''
                  } heading-1`}
                >
                  {assessment.ongoing}
                  <span
                    className={`text-xxs font-normal ${
                      is_loading_assessment ? '' : 'text-yellow-500'
                    }`}
                  >
                    /
                    {assessment.no_candidates
                      ? Math.round(
                          (assessment.ongoing / assessment?.no_candidates) * 100
                        )
                      : 100}
                    %
                  </span>
                </p>
                <p className="text-xs">Started</p>
              </div>
            </li>
            <li className="px-4">
              <div>
                <p
                  className={`${
                    is_loading_assessment ? 'skeleton-loading' : ''
                  } heading-1`}
                >
                  {assessment.not_started}
                  <span
                    className={`text-xxs font-normal ${
                      is_loading_assessment ? '' : 'text-red-500'
                    }`}
                  >
                    /
                    {assessment.no_candidates
                      ? Math.round(
                          (assessment.not_started / assessment?.no_candidates) *
                            100
                        )
                      : 100}
                    %
                  </span>
                </p>
                <p className="text-xs">Not started</p>
              </div>
            </li>
            <li className="px-4">
              <div className="space-y-1">
                <p
                  className={`${
                    is_loading_assessment ? 'skeleton-loading' : ''
                  } heading-1`}
                >
                  {Math.floor(assessment.average_score * 100) / 100}
                </p>
                <p className="text-xs">Average score</p>
              </div>
            </li>
          </ul>

          {/* INVIGILATION */}
          <div>
            <p className="flex h-full cursor-not-allowed flex-col items-center justify-between gap-2 rounded-xl border p-2 px-5 py-4">
              <svg
                width={37}
                height={34}
                viewBox="0 0 37 34"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M34.81 0H1.618C.73 0 0 .729 0 1.619v24.286c0 .89.729 1.619 1.619 1.619h12.143v1.619h-1.214A4.06 4.06 0 0 0 8.5 33.19c0 .446.364.81.81.81h17.809c.445 0 .81-.364.81-.81a4.06 4.06 0 0 0-4.048-4.047h-1.214v-1.62h12.142c.891 0 1.62-.728 1.62-1.618V1.619C36.429.73 35.7 0 34.809 0ZM23.88 30.762c1.052 0 1.943.688 2.307 1.619H10.241a2.424 2.424 0 0 1 2.307-1.62H23.88Zm-2.833-1.62H15.38v-1.618h5.667v1.619Zm13.762-3.237H1.618V1.619h33.19v24.286ZM3.642 22.667c0-.446.364-.81.81-.81h27.523c.445 0 .81.364.81.81 0 .445-.365.81-.81.81H4.452a.812.812 0 0 1-.81-.81Zm4.047-6.881h.81v1.619a2.436 2.436 0 0 0 2.429 2.428h2.023c.446 0 .81-.364.81-.81v-3.237h1.133l15.3 3.238h.162c.203 0 .364-.081.526-.162.203-.162.284-.405.284-.648V4.452a.955.955 0 0 0-.284-.647c-.202-.162-.445-.203-.688-.162l-15.3 3.238H7.69A2.436 2.436 0 0 0 5.262 9.31v4.047a2.436 2.436 0 0 0 2.428 2.429Zm4.453 2.428h-1.214a.812.812 0 0 1-.81-.81v-1.618h2.024v2.428Zm17.405-1.012-13.762-2.914v-5.95l13.762-2.914v11.778ZM6.88 9.31c0-.446.364-.81.81-.81h6.476v5.667H7.69a.812.812 0 0 1-.809-.81V9.31Z"
                  fill="#CECECE"
                />
              </svg>
              <span>Invigilation</span>
            </p>
          </div>

          {/* VIEW ANALYTICS */}

          <div>
            <p className="flex h-full cursor-not-allowed flex-col items-center justify-between gap-2 rounded-xl border p-2 px-5 py-4">
              <svg
                width={89}
                height={33}
                viewBox="0 0 89 33"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect width={13} height={33} rx={2} fill="#E9E9E9" />
                <rect x={19} width={13} height={33} rx={2} fill="#E9E9E9" />
                <rect x={38} width={13} height={33} rx={2} fill="#E9E9E9" />
                <rect x={57} width={13} height={33} rx={2} fill="#E9E9E9" />
                <rect x={76} width={13} height={33} rx={2} fill="#E9E9E9" />
                <path
                  d="m4 18.158 18.41 5.784a10 10 0 0 0 6.653-.232l10.938-4.296a10 10 0 0 1 6.494-.28l11.428 3.383a10 10 0 0 0 7.568-.778l8.83-4.74a10 10 0 0 1 7.752-.721L88 18.158"
                  stroke="#D9D9D9"
                />
                <circle cx={87} cy={18} r={2} fill="#D9D9D9" />
                <circle cx={62} cy={23} r={2} fill="#D9D9D9" />
                <circle cx={42} cy={19} r={2} fill="#D9D9D9" />
                <circle cx={25} cy={24} r={2} fill="#D9D9D9" />
                <circle cx={3} cy={18} r={2} fill="#D9D9D9" />
              </svg>
              <span>View analytics</span>
            </p>
          </div>
        </section>

        {/* TABS */}
        {!isFetchingCandidates && (
          <section className="flex items-center justify-between rounded-xl bg-white px-4">
            <div className="rounded-md bg-white px-4 pt-2">
              <nav className="">
                <ul className="flex items-center gap-4">
                  {tabs.map((tab, index) => (
                    <li
                      key={index}
                      className={
                        index == current_tab
                          ? 'border-b-3 border-primary p-4 pb-2 text-primary'
                          : 'border-b-3 border-transparent p-4 pb-2'
                      }
                    >
                      <label className="cursor-pointer">
                        <input
                          type="radio"
                          name="active tab"
                          className="hidden"
                          value={index}
                          onChange={e => {
                            setCurrentTab(parseInt(e.target.value));
                          }}
                        />
                        {tab.title}
                      </label>
                    </li>
                  ))}
                </ul>
              </nav>
            </div>
            {current_tab == 0 && fetch_candidates_data?.candidates?.length && (
              <div className="flex items-center gap-12">
                <div className="flex gap-2">
                  <div>
                    <ListBox
                      options={CANDIDATES_ASSESSMENT_STATUSES}
                      className="border"
                      placeholder="Status"
                      // @ts-ignore
                      active_option={status_filter}
                      setActiveOption={status => {
                        setStatusFilter(status as CandidateAssessmentStatus);
                      }}
                      // @ts-ignore
                      value_key="value"
                      // @ts-ignore
                      readable_text_key="title"
                    />
                  </div>
                  <div className="btn-bordered flex items-center gap-2 py-0">
                    <SearchIcon />
                    <input
                      type="text"
                      name="search assessments"
                      placeholder="Search"
                      className="input-base"
                      defaultValue={searchParams
                        .get('search_filter')
                        ?.toString()}
                      onChange={e => setSearchFilter(e.target.value)}
                    />
                  </div>
                  {(status_filter || search_filter) && (
                    <button onClick={clearFilters} className="btn-bordered">
                      Clear filters
                    </button>
                  )}

                  <ExportReportsPopover assessment_id={assessment_id} />
                </div>
              </div>
            )}
          </section>
        )}

        {/* candidates */}
        {current_tab == 0 && (
          <section className="rounded-xl bg-white p-4">
            {isFetchingCandidates ? (
              <Loader />
            ) : fetch_candidates_data?.candidates?.length == 0 ? (
              <div className="flex  items-center justify-center">
                <div className="rounded-md border border-[#DADADA]/20 bg-gradient-to-b from-[#755ae20a] via-transparent to-transparent p-12 text-center">
                  <div className="container max-w-xs space-y-2">
                    <div className="container flex justify-center">
                      <img
                        src="/images/create-assessments/no-assessment.png"
                        alt="writing with pen"
                      />
                    </div>
                    {!fetch_candidates_data.candidates.length ? (
                      <>
                        <h2>This assessment has no candidates</h2>
                      </>
                    ) : (
                      <>
                        <h2>No candidate matches your filters</h2>
                        <p className="text-sm text-[#7D8590]">
                          clear the filters to get more results
                        </p>
                        <button className="btn-primary" onClick={clearFilters}>
                          Clear filters
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <AssessmentCandidatesList
                isSuccess={isFetchCandidatesSuccess}
                refetch={refetchCandidates}
                data={fetch_candidates_data}
                isLoading={isFetchingCandidates}
                isError={isFetchCandidatesError}
                error={fetchCandidatesError}
              />
            )}
          </section>
        )}
        {/* INVITES */}
        {current_tab == 1 && (
          <section className="rounded-xl bg-white p-4">
            {isCandidateInviteStatusLoading ? (
              <Loader />
            ) : fetch_candidate_invite_statuses?.data?.length == 0 ? (
              <div className="flex  items-center justify-center">
                <div className="rounded-md border border-[#DADADA]/20 bg-gradient-to-b from-[#755ae20a] via-transparent to-transparent p-12 text-center">
                  <div className="container max-w-xs space-y-2">
                    <div className="container flex justify-center">
                      <img
                        src="/images/create-assessments/no-assessment.png"
                        alt="writing with pen"
                      />
                    </div>
                    {!fetch_candidate_invite_statuses?.data.length ? (
                      <>
                        <h2>This assessment has no invitations</h2>
                      </>
                    ) : (
                      <>
                        <h2>No status matches your filters</h2>
                        <p className="text-sm text-[#7D8590]">
                          clear the filters to get more results
                        </p>
                        <button className="btn-primary" onClick={clearFilters}>
                          Clear filters
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <AssessmentInviteStatuses
                invite_id={assessment.invite_info?.invite as string}
                isSuccess={isFetchCandidatesInviteSuccess}
                refetch={refetchCandidateInviteStatus}
                data={fetch_candidate_invite_statuses}
                isLoading={isCandidateInviteStatusLoading}
                isError={isCandidateInviteStatusError}
                error={fetchCandidateInviteStatusError}
              />
            )}
          </section>
        )}
        {/* ASSESSMENT DETAILS */}
        {current_tab == 2 && (
          <section>
            {is_loading_assessment ? (
              <Loader />
            ) : (
              <div className="grid items-start gap-2 text-base md:grid-cols-2">
                <section className="space-y-4 rounded-xl bg-white p-4">
                  <div>
                    <h2 className="heading-text">Questions</h2>
                    <p className="helper-text text-sm">
                      Preview and reposition assessment sections
                    </p>
                  </div>

                  <section></section>
                  <div>
                    {assessment.sections.length == 0 ? (
                      <div className="grid flex-1 place-content-center py-20">
                        <div className="rounded-md border border-[#DADADA]/20 bg-gradient-to-b from-[#755ae20a] via-transparent to-transparent p-12 text-center">
                          <div className="container max-w-xs space-y-2">
                            <div className="container flex justify-center">
                              <img
                                src="/images/create-assessments/no-assessment.png"
                                alt="writing with pen"
                              />
                            </div>
                            <h2>This Assessment has no sections</h2>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <OldDraggableSection
                        setAssessment={setAssessment}
                        assessment={assessment}
                        setActiveSectionIndex={setActiveSectionIndex}
                        setActiveQuestionIndex={setActiveQuestionIndex}
                        openEditQuestionModal={openEditQuestionModal}
                        openDeleteQuestionModal={openDeleteQuestionModal}
                        handleSectionOption={handleSectionOption}
                      />
                    )}
                  </div>
                </section>
                <section className="space-y-4 rounded-xl bg-white p-4 text-sm">
                  <div className="flex items-start justify-between">
                    <div>
                      <h2 className="heading-text">
                        Welcome details and description
                      </h2>
                      <p className="header-text text-sm">
                        Assessment intro and description
                      </p>
                    </div>
                    {!is_edit_assessment_details && (
                      <button
                        type="button"
                        className="btn-primary-bordered"
                        onClick={e => {
                          e.preventDefault();
                          setIsEditAssessmentDetails(true);
                        }}
                      >
                        Edit
                      </button>
                    )}
                  </div>
                  <form
                    className="space-y-6"
                    onSubmit={editAssessmentDetailsSettings}
                  >
                    <div>
                      <label className="space-y-2">
                        <p className="">Assessment name</p>
                        <input
                          type="text"
                          disabled={!is_edit_assessment_details}
                          placeholder="Assessment name"
                          className="input-grey"
                          value={assessment.name}
                          onChange={e => {
                            setAssessment({
                              ...assessment,
                              name: e.target.value,
                            });
                          }}
                        />
                      </label>
                    </div>
                    <div>
                      <label className="space-y-2">
                        <p className="">
                          Assessment Intro message / description
                        </p>
                        <textarea
                          required
                          disabled={!is_edit_assessment_details}
                          value={assessment.description}
                          onChange={e => {
                            setAssessment({
                              ...assessment,
                              description: e.target.value,
                            });
                          }}
                          placeholder="Enter details of this assessment to guide candidates"
                          className="input-grey min-h-[100px]"
                          maxLength={400}
                        />
                      </label>
                      {is_edit_assessment_details && (
                        <p className="text-right text-xxs">
                          {assessment?.description?.length || 0}/400 characters
                          remaining
                        </p>
                      )}
                    </div>
                    {is_edit_assessment_details && (
                      <Button
                        className="btn-primary"
                        type="submit"
                        is_busy={is_assessment_details_form_busy}
                      >
                        Save and Update
                      </Button>
                    )}
                  </form>
                </section>
              </div>
            )}
          </section>
        )}
        {/* === === === === ===
    === MORE DETAILS === 
    === === === === */}
        {current_tab == 3 && (
          <div className="grid items-start gap-2 md:grid-cols-2">
            <section className="space-y-4 rounded-xl bg-white p-4">
              <div className="flex items-start justify-between">
                <div>
                  <h2 className="heading-text">Proctoring and invigilation</h2>
                  <p className="header-text text-sm">
                    Here you can configure the anti-cheating settings for the
                    assessment
                  </p>
                </div>

                {is_edit_proctoring_settings ? (
                  <button
                    type="button"
                    className="btn-primary-bordered"
                    onClick={e => {
                      e.preventDefault();
                      setIsEditProctoringSettings(false);
                    }}
                  >
                    Save
                  </button>
                ) : (
                  <button
                    type="button"
                    className="btn-primary-bordered"
                    onClick={e => {
                      e.preventDefault();
                      setIsEditProctoringSettings(true);
                    }}
                  >
                    Edit
                  </button>
                )}
              </div>

              <Proctoring
                assessment={assessment}
                setProctoringSwitch={setProctoringSwitch}
                updateAssessment={updateLiveAssessment}
                proctoring_section_options={PROCTORING_SECTION_OPTIONS}
                proctoring_tolerance_options={PROCTORING_TOLERANCE_OPTIONS}
                editOptions={is_edit_proctoring_settings}
                hideSaveButton={true}
              />
            </section>
            <section className="space-y-4 rounded-xl bg-white p-4">
              <div className="flex items-start justify-between">
                <div>
                  <h2 className="heading-text">Assessment deadline</h2>
                  <p className="header-text text-sm">
                    View and configure timer and deadline for this assessment
                  </p>
                </div>
                {!is_edit_timing_settings && (
                  <button
                    type="button"
                    className="btn-primary-bordered"
                    onClick={e => {
                      e.preventDefault();
                      setIsEditTimingSettings(true);
                    }}
                  >
                    Edit
                  </button>
                )}
              </div>
              <form onSubmit={editAssessmentTimeSettings} className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-1">
                    <h3 className="heading-text">Commencement settings</h3>
                    <p className="text-xs">
                      Choose <span className="font-bold">'YES'</span> if you
                      want candidates to take the exam simultaneously or{' '}
                      <span className="font-bold">'NO'</span> if you prefer them
                      to start at your specified time and date."
                    </p>
                  </div>
                  {is_edit_timing_settings ? (
                    <p className="flex gap-2">
                      {/* @ts-ignore */}
                      <ListBox
                        placeholder="Select commencement setting"
                        className="border"
                        options={YES_OR_NO}
                        value_key="value"
                        readable_text_key="readable_string"
                        active_option={assessment.commencement_settings}
                        setActiveOption={_option => {
                          setAssessment({
                            ...assessment,
                            commencement_settings:
                              _option == 'yes' ? 'yes' : 'no',
                          });
                        }}
                      />
                    </p>
                  ) : (
                    <p>
                      <span className="input-white w-max border capitalize">
                        {assessment.commencement_settings}{' '}
                      </span>
                    </p>
                  )}
                </div>
                <div className="space-y-4">
                  <div className="space-y-1">
                    <h3 className="heading-text">Set assessment time limit</h3>
                    <p className="text-xs">
                      Once started, candidates are required to submit assessment
                      within this time.
                    </p>
                  </div>
                  {is_edit_timing_settings ? (
                    <TimeInput
                      value={assessment.time_limit}
                      onChange={val => {
                        setAssessment({ ...assessment, time_limit: val });
                      }}
                    />
                  ) : (
                    <p className="input-white w-max border">
                      {secondsToLargestUnit(assessment?.time_limit).time}{' '}
                      {secondsToLargestUnit(assessment?.time_limit).unit}
                    </p>
                  )}
                </div>
                <div className="space-y-4">
                  <div className="space-y-1">
                    <h3 className="heading-text">Date and time settings</h3>
                    <p className="text-xs">
                      Specify the start and end date and time for candidates to
                      start and complete this assessment.
                    </p>
                  </div>
                  <label className="flex items-center gap-4">
                    Start:
                    <input
                      className="input-white w-min border"
                      type="datetime-local"
                      disabled={
                        !is_edit_timing_settings || assessment.is_started
                      }
                      required
                      value={assessment.start_time}
                      name="date"
                      onChange={event => {
                        setAssessment({
                          ...assessment,
                          start_time: event.target.value,
                        });
                      }}
                    />
                  </label>
                  {assessment.commencement_settings == 'yes' && (
                    <label className="flex items-center gap-4">
                      End:
                      <input
                        className="input-white w-min border"
                        type="datetime-local"
                        disabled={!is_edit_timing_settings}
                        required
                        value={assessment.deadline}
                        min={assessment.start_time}
                        name="date"
                        onChange={event => {
                          setAssessment({
                            ...assessment,
                            deadline: event.target.value,
                          });
                        }}
                      />
                    </label>
                  )}
                </div>
                {is_edit_timing_settings && (
                  <Button
                    className="btn-primary"
                    type="submit"
                    is_busy={is_assessment_time_form_busy}
                  >
                    Save and Update
                  </Button>
                )}
              </form>
            </section>
          </div>
        )}
        {/* more details */}
        {current_tab == 4 && <p>Team Details</p>}
      </div>

      <EditSectionModal
        section={assessment?.sections?.[active_section_index]}
        handleSubmit={section => {
          handleEditSection(section);
        }}
        is_busy={is_editing_section}
        close={closeEditSectionModal}
        is_open={is_edit_section_modal_open}
      />

      <DeleteSectionModal
        is_open={is_delete_section_modal_open}
        close={closeDeleteSectionModal}
        section_index={active_section_index}
        onDelete={section_index => deleteSection(section_index)}
      />

      <EditQuestionModal
        is_busy={is_editing_question}
        is_open={is_edit_question_modal_open}
        close={closeEditQuestionModal}
        question={
          assessment?.sections?.[active_section_index]?.question_set[
            active_question_index
          ]
        }
        onSave={handleEditQuestion}
      />

      <DeleteQuestionModal
        is_open={is_delete_question_modal_open}
        close={closeDeleteQuestionModal}
        section_index={active_section_index}
        question_index={active_question_index}
        onDelete={(question_index, section_index) =>
          deleteQuestion(question_index, section_index)
        }
      />

      <InviteCandidatesModal
        assessment_id={assessment?.id}
        is_open={is_invite_candidates_modal_open}
        onSuccess={reloadAssessment}
        close={closeInviteCandidatesModal}
      />
    </div>
  );
}
