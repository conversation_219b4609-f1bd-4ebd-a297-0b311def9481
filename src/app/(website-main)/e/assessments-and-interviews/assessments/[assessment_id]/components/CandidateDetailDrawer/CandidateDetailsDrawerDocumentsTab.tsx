
import React from 'react'
import { SmallSpinner } from '@/components/shared/icons'
import ViewDOCX from './ViewDOCX'
import ViewPDF from './ViewPDF'

interface Props {
    isLoadingCVFile: boolean
    cvFileName: string
    cvFile: string
}

const CandidateDetailsDocumentsTab: React.FC<Props> = ({ isLoadingCVFile, cvFileName, cvFile }) => {
    return (
        <>
            {
                isLoadingCVFile ?
                    <div className="flex items-center justify-center w-full h-full">
                        <SmallSpinner color="#755AE2" />
                    </div>
                    :
                    !isLoadingCVFile && !cvFile ?
                        <p className='p-8'>NO CV FILE</p>
                        :
                        cvFileName?.split('.').pop()?.toLowerCase() === "pdf" ?
                            <ViewPDF cvFile={cvFile} />
                            :
                            <ViewDOCX file={cvFile} />
            }
        </>
    )
}

export default CandidateDetailsDocumentsTab
