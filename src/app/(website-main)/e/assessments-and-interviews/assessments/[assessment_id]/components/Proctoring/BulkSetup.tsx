import React from 'react';
import { BulkProctorOption } from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';

type BulkProctoringProp = {
  option: {
    id: string;
    title: string;
    name: string;
    description: string;
  };
  openBulkProctorModal: () => void;
  toleranceLevel: number;
  selectedBulkProctoringOptions: BulkProctorOption[];
  editOptions: boolean;
};
const BulkSetup = ({
  option,
  openBulkProctorModal,
  toleranceLevel,
  selectedBulkProctoringOptions,
  editOptions,
}: BulkProctoringProp) => (
  <div className="space-y-4">
    <h3 className="heading-2 mb-1">{option.title}</h3>
    <p className="helper-text max-w-[80%] text-xs">{option.description}</p>
    <div className=" ">
      {selectedBulkProctoringOptions.length > 0 && toleranceLevel > 0 && (
        <div className="rounded p-4 text-primary-dark">
          You currently have an active tolerance level set for{' '}
          {selectedBulkProctoringOptions.length} violations.
        </div>
      )}
      {editOptions && (
        <button
          className="btn-primary-light font-bold"
          onClick={openBulkProctorModal}
          type="button"
        >
          {selectedBulkProctoringOptions.length <= 0 && toleranceLevel <= 0
            ? 'Tap set bulk tolerance'
            : 'Tap to edit and update settings'}
        </button>
      )}
    </div>
  </div>
);

export default BulkSetup;
