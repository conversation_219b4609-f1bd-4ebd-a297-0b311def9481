import React from 'react';
import {
  Assessment,
  ProctoringOptions,
  ProctoringToleranceOption,
} from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import { numberToPosition, secondsToLargestUnit } from '@/lib/utils/functions';

type IndividualProctoringProp = {
  option: {
    id: string;
    title: string;
    name: string;
    description: string;
  };
  proctoring_tolerance_options: ProctoringToleranceOption[];
  openToleranceLevelModal: (st: number) => void;
  assessment: Assessment;
  editOptions: boolean;
};

const IndividualSetup = ({
  option,
  proctoring_tolerance_options,
  openToleranceLevelModal,
  assessment,
  editOptions,
}: IndividualProctoringProp) => (
  <div className="space-y-4">
    <h3 className="heading-2 mb-1">{option.title}</h3>
    <p className="helper-text max-w-[80%] text-xs">{option.description}</p>
    <div className="space-y-2">
      {proctoring_tolerance_options.map((option, index) => (
        <label
          key={index}
          className="flex items-start justify-between gap-4 rounded-xl border p-4"
        >
          <div className="space-y-1">
            <h3 className="heading-text capitalize">{option.title}</h3>
            <p className="helper-text text-xs">
              {option.description}, their assessment will be ended at the{' '}
              <span className="font-bold text-black">
                {
                  //@ts-ignore
                  numberToPosition(assessment?.[option.id])
                }
              </span>{' '}
              attempt of doing so.
              {editOptions && (
                <>
                  You can set the the tolerance level to fit how you want it by
                  clicking on{' '}
                  <button
                    className="font-semibold text-primary"
                    onClick={() => {
                      openToleranceLevelModal(index);
                    }}
                  >
                    Change count
                  </button>
                </>
              )}
            </p>
          </div>
        </label>
      ))}
    </div>
  </div>
);

export default IndividualSetup;
