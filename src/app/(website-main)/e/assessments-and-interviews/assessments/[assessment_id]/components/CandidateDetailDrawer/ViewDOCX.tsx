import mammoth from 'mammoth';
import React, { useEffect, useState } from 'react';
import { SmallSpinner, Spinner } from '@/components/shared/icons';

interface props {
  file: string;
}
const ViewDOCX: React.FC<props> = ({ file }) => {
  const [isLoading, setLoading] = useState(true);
  const [htmlContent, setHtmlContent] = useState<string>('');
  const [error, setError] = useState<string>('');
  useEffect(() => {
    convertDocxToHtml();
  }, []);

  const convertDocxToHtml = async () => {
    try {
      const buffer = Buffer.from(file, 'base64');
      const arrayBuffer = buffer.buffer.slice(
        buffer.byteOffset,
        buffer.byteOffset + buffer.byteLength
      );
      const { value: html } = await mammoth.convertToHtml({
        arrayBuffer: arrayBuffer,
      });
      setHtmlContent(html);
    } catch (error) {
      // console.log(error)
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      {isLoading && (
        <div className="flex h-full w-full items-center justify-center">
          <SmallSpinner color="#755AE2" />
        </div>
      )}
      <div
        className="scale-95"
        dangerouslySetInnerHTML={{ __html: htmlContent }}
      />
    </>
  );
};
export default ViewDOCX;
