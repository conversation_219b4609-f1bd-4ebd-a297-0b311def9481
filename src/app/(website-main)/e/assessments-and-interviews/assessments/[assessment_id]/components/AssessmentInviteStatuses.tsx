'use client';

import { format } from 'date-fns';
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'react-hot-toast';
import TableActionButton from '@/app/(website-main)/e/assessments-and-interviews/misc/components/TableActionButton';
import {
  ASSESSMENT_STATUSES_DICTIONARY,
  CANDIDATE_TABLE_OPTIONS,
  CANDIDATE_TABLE_OPTIONS_DICTIONARY,
} from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants';
import DropdownMenu from '@/components/DropdownMenu';
import FlagIcon from '@/components/icons/FlagIcon';
import Avatar from '@/components/shared/avatar-alt';
import Select from '@/components/shared/select-simple';
import { useBooleanStateControl } from '@/hooks';
import app_fetch from '@/lib/api/appFetch';
import DeleteCandidateModal from '../../../misc/components/DeleteCandidateModal';
import InviteCandidatesModal from '../../../misc/components/InviteCandidatesModal';
import {
  CandidateInviteResult,
  FetchCandidateInviteResults,
  FetchCandidateResult,
} from '../../../misc/types/create-assessments';
import CandidateDetailsDrawer from './CandidateDetailDrawer';
import ResendInviteModal from './ResendInviteModal';

type props = {
  isSuccess: boolean;
  data?: FetchCandidateInviteResults;
  isLoading: boolean;
  isError: boolean;
  error: unknown;
  refetch: () => void;

  invite_id: string;
};

export default function AssessmentInviteStatuses({
  isSuccess,
  isError,
  isLoading,
  data,
  error,
  refetch,

  invite_id,
}: props) {
  const router = useRouter();
  const params = useParams();
  const assessment_id = params.assessment_id as string;

  const searchParams = useSearchParams();

  const status_filter = searchParams?.get('status_filter');
  const search_filter = searchParams?.get('search_filter');

  const [display_length, setDisplayLength] = useState('50');

  const display_length_options = [
    { name: '2' },
    { name: '50' },
    { name: '100' },
    { name: '500' },
    { name: '1000' },
  ].filter(
    option =>
      Number(option.name) <= (data?.data?.length || 50) ||
      Number(option.name) <= 50
  );

  const displayed_invites =
    data?.data
      ?.filter(invite => {
        const search_filter_exist = !!search_filter;
        const status_filter_exist = !!status_filter;
        const _candidate_name_filtered = (invite?.recipient || '')
          .toLowerCase()
          ?.includes(search_filter?.toLowerCase() as string);
        const _candidate_email_filtered = invite?.recipient_name
          .toLowerCase()
          ?.includes(search_filter?.toLowerCase() as string);
        const _candidate_status_filtered = invite?.status
          .toLowerCase()
          ?.includes(status_filter || '');

        const candidate_name_filtered =
          search_filter_exist && _candidate_name_filtered;
        const candidate_email_filtered =
          search_filter_exist && _candidate_email_filtered;
        const candidate_status_filtered =
          status_filter_exist && _candidate_status_filtered;

        let should_show = true;

        if (search_filter) {
          should_show =
            should_show &&
            (candidate_email_filtered || candidate_name_filtered);
        }

        if (status_filter) {
          should_show = should_show && candidate_status_filtered;
        }

        return should_show;
      })
      .slice(0, Number(display_length)) || [];

  const {
    state: isResendInviteModalOpen,
    setFalse: closeResendInviteModal,
    setTrue: openResendInviteModal,
  } = useBooleanStateControl();
  const [activeCandidateInvite, setActiveCandidateInvite] =
    useState<CandidateInviteResult | null>(null);

  const initResend = (index: number) => {
    setActiveCandidateInvite(displayed_invites[index]);
    openResendInviteModal();
  };

  const resendInvite = async (recipientEmail: string) => {
    return new Promise((resolve, reject) => {
      const options = {
        method: 'POST',
        body: JSON.stringify({
          assessment_id: assessment_id,
          candidate_email: recipientEmail,
          assessment_invite_id: invite_id,
        }),
      };
      app_fetch(`assessments/resend-invite/`, options)
        .then(res => res.json())
        .then(result => {
          toast.success(result.message);
          refetch();
          resolve(result);
        })
        .catch(e => {
          toast.error('Unable to resend message');
          reject(e);
        });
    });
  };

  const cleanRecepient = (recipient: string) => {
    if (recipient.slice(0, 1) == '[') {
      return recipient.slice(2, recipient.length - 2);
    } else {
      return recipient;
    }
  };

  return (
    <>
      <table className="w-full divide-y border-b">
        <thead>
          <tr>
            {'S/N, candidate, email, invite date, status, action'
              .split(', ')
              .map((heading, index) => (
                <th
                  className="pb-4 text-left font-normal capitalize"
                  key={index}
                >
                  {heading}
                </th>
              ))}
          </tr>
        </thead>
        <tbody className="divide-y  ">
          {isLoading && (
            <div className="p-4">
              <p>Loading...</p>
            </div>
          )}
          {isError && (
            <div className="p-4 text-red-500">
              <p>Error loading candidates</p>
            </div>
          )}

          {isSuccess &&
            displayed_invites.map((invite, index) => (
              <tr className="cursor-pointer hover:bg-primary-light" key={index}>
                <td className="p-4">{index + 1}</td>
                <td className="p-4">
                  <div className="flex items-center gap-2">
                    <Avatar name={invite?.recipient_name || 'NO NAME'} />
                    {invite?.recipient_name || 'NO NAME'}
                  </div>
                </td>
                <td className="p-4">{cleanRecepient(invite.recipient)}</td>
                <td className="p-4">
                  {format(new Date(invite.sent_at), 'dd/MM/yyyy, hh:mma')}
                </td>

                <td className="p-4">
                  <p
                    className={` ${invite.status === 'success'
                        ? 'text-green-500'
                        : 'text-red-500'
                      } } `}
                  >
                    {invite.status}
                  </p>
                </td>

                <td className="p-4">
                  {invite.status == 'failure' && (
                    <div className="inline-flex justify-start">
                      <button
                        onClick={() => initResend(index)}
                        className="flex items-center gap-4 rounded-lg bg-[#F8F9FB] px-4 py-2 text-xs outline outline-1 outline-[#EAE6FB]"
                      >
                        <p>Resend</p>
                      </button>
                    </div>
                  )}
                </td>
              </tr>
            ))}
        </tbody>
      </table>

      <div className="mt-2 flex items-center justify-between gap-4 px-8">
        <label className="flex items-center gap-2">
          Displayed candidates:
          <Select
            name="displayed candidates limit"
            value={display_length}
            labelKey="name"
            onChange={setDisplayLength}
            options={display_length_options}
            placeholder="Limit number of displayed candidates"
            valueKey="name"
          />
        </label>
        <div className="flex gap-2">
          <p> Total candidates: {data?.data.length} </p>
          {displayed_invites.length !== data?.data.length && (
            <p className="border-l pl-2">
              {' '}
              Filtered candidates: {displayed_invites.length}{' '}
            </p>
          )}
        </div>
      </div>

      <ResendInviteModal
        is_open={isResendInviteModalOpen}
        close={() => {
          closeResendInviteModal();
          setActiveCandidateInvite(null);
        }}
        activeInvite={activeCandidateInvite}
        onResend={resendInvite}
      />
    </>
  );
}
