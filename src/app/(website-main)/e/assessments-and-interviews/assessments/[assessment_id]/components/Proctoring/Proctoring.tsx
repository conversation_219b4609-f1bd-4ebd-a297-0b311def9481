import React, { useEffect, useState } from 'react';
import ProctorCountModal from '@/app/(website-main)/e/assessments-and-interviews/assessments/[assessment_id]/components/Proctoring/ProctorCountModal';
import { BULK_PROCTOR_OPTIONS } from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants';
import {
  Assessment,
  BulkProctorOption,
  ProctoringOptions,
  ProctoringToleranceOption,
  proctorOption,
} from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import Switch from '@/components/Switch';
import { useBooleanStateControl } from '@/hooks';
import app_fetch from '@/lib/api/appFetch';
import BulkProctoringModal from './BulkProctoringModal';
import BulkSetup from './BulkSetup';
import IndividualSetup from './IndividualSetup';
import OtherSetup from './OtherSetup';

type ProctoringProps = {
  assessment: Assessment;
  proctoring_section_options: {
    id: string;
    title: string;
    name: string;
    description: string;
  }[];

  setProctoringSwitch: (_switch: ProctoringOptions, state: boolean) => void;
  proctoring_tolerance_options: ProctoringToleranceOption[];
  updateAssessment: (val: any) => void;
  editOptions?: boolean;
  hideSaveButton?: boolean;
};

const Proctoring = ({
  assessment,
  proctoring_section_options,
  proctoring_tolerance_options,
  setProctoringSwitch,

  updateAssessment,
  editOptions,
  hideSaveButton,
}: ProctoringProps) => {
  const [currentSectionIndex, setCurrentSectionIndex] = useState<number>(0);
  const [toleranceLevel, setToleranceLevel] = useState<number>(0);
  const [selectedBulkProctoringOptions, setSelectedBulProctoringOptions] =
    useState<BulkProctorOption[]>([]);

  useEffect(() => {
    // console.log('UPDATING PROCTOR WITH EFFECT');
    // console.log(selectedBulkProctoringOptions);
    // console.log(assessment.bulk_tolerance_setup);
    if (selectedBulkProctoringOptions.length <= 0) {
      if (assessment.bulk_tolerance_setup) {
        setToleranceLevel(
          parseInt(assessment.bulk_tolerance_setup.combined_tolerance)
        );

        let selectedOption: BulkProctorOption[] = [];

        assessment.bulk_tolerance_setup.options.forEach(o => {
          let findProctoroption = BULK_PROCTOR_OPTIONS.find(
            _o => _o.key == o.option
          );
          if (findProctoroption) {
            selectedOption.push({
              key: findProctoroption.key,
              value: findProctoroption.value,
            });
          }
        });
        setSelectedBulProctoringOptions(selectedOption);
      }
    }
  }, [assessment]);

  const addSelectedProctoringOption = (_option: string) => {
    let selectedOption = BULK_PROCTOR_OPTIONS.find(o => o.key == _option);
    if (!selectedOption) return;
    let existsInSelectedOption = selectedBulkProctoringOptions.find(
      o => o.key == _option
    );
    if (existsInSelectedOption) return;
    let allSelectionOptions = [
      ...selectedBulkProctoringOptions,
      selectedOption,
    ];
    setSelectedBulProctoringOptions(allSelectionOptions);

    updateAssessment({
      bulk_tolerance_setup: {
        ...assessment.bulk_tolerance_setup,
        options: allSelectionOptions.map(sb => ({ option: sb.key })),
      },
    });
  };
  const removeSelectedProctoringOption = (index: number) => {
    let selected = [...selectedBulkProctoringOptions];
    selected.splice(index, 1);
    setSelectedBulProctoringOptions(selected);
  };

  const updateToleranceLevel = (_tolerancelevel: number) => {
    updateAssessment({
      bulk_tolerance_setup: {
        ...assessment.bulk_tolerance_setup,
        combined_tolerance: _tolerancelevel,
      },
    });
    setToleranceLevel(_tolerancelevel);
  };

  const updateBulkProctoringAssessment = () => {
    setLoadingState(true);
    let payload = {
      bulk_tolerance_setup: {
        combined_tolerance: toleranceLevel,
        options: selectedBulkProctoringOptions.map(sb => ({ option: sb.key })),
      },
    };

    const options = {
      method: 'PATCH',
      body: JSON.stringify(payload),
    };
    app_fetch(`assessments/assessments/${assessment.id}/`, options)
      .then(res => res.json())
      .then(result => {
        setLoadingState(false);
        closeBulkProctorModal();
        console.log(result, 'RESULT');
      })
      .catch(e => {
        setLoadingState(true);
        closeBulkProctorModal();

        //  console.log(e, 'PATCH EROR');
      });
  };

  const {
    state: isBulkProctorModalOpen,
    setFalse: closeBulkProctorModal,
    setTrue: openBulkProctorModal,
  } = useBooleanStateControl(false);

  const [loadingState, setLoadingState] = useState<boolean>(false);

  const tolerance_values = [
    // { value: 0 },
    // { value: 1 },
    { value: 2 },
    { value: 3 },
    { value: 4 },
  ];

  const [active_tolerance_option_index, setActiveToleranceOptionIndex] =
    useState(0);

  const [is_tolerance_modal_open, setIsToleranceModalOpen] = useState(false);
  function closeToleranceLevelModal() {
    setIsToleranceModalOpen(false);
  }

  function openToleranceLevelModal(option_index: number) {
    setActiveToleranceOptionIndex(option_index);
    setIsToleranceModalOpen(true);
  }

  return (
    <div className="space-y-6">
      <div className="flex items-start gap-4 border-b-4  border-gray-300">
        {proctoring_section_options.map((option, i) => (
          <div
            className={` cursor-pointer border-b-4 p-2 font-medium 
                    ${i == currentSectionIndex
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-900'
              }
                    `}
            onClick={() => setCurrentSectionIndex(i)}
          >
            {option.name}
          </div>
        ))}
      </div>

      {proctoring_section_options[currentSectionIndex].id ==
        'individual_tolerance_settings' && (
          <IndividualSetup
            editOptions={editOptions == null ? true : (editOptions as boolean)}
            assessment={assessment}
            openToleranceLevelModal={openToleranceLevelModal}
            option={proctoring_section_options[currentSectionIndex]}
            proctoring_tolerance_options={proctoring_tolerance_options}
          />
        )}

      {proctoring_section_options[currentSectionIndex].id ==
        'bulk_tolerance_settings' && (
          <BulkSetup
            editOptions={editOptions == null ? true : (editOptions as boolean)}
            option={proctoring_section_options[currentSectionIndex]}
            openBulkProctorModal={openBulkProctorModal}
            toleranceLevel={toleranceLevel}
            selectedBulkProctoringOptions={selectedBulkProctoringOptions}
          />
        )}
      {proctoring_section_options[currentSectionIndex].id ==
        'other_setup_options' && (
          <OtherSetup
            editOptions={editOptions == null ? true : (editOptions as boolean)}
            assessment={assessment}
            option={proctoring_section_options[currentSectionIndex]}
            setProctoringSwitch={setProctoringSwitch}
          />
        )}

      <BulkProctoringModal
        openModal={isBulkProctorModalOpen}
        closeModal={closeBulkProctorModal}
        options={BULK_PROCTOR_OPTIONS}
        selected={selectedBulkProctoringOptions}
        removeOption={removeSelectedProctoringOption}
        addOption={addSelectedProctoringOption}
        toleranceLevel={toleranceLevel}
        updateToleranceLevel={updateToleranceLevel}
        updateBulkProctoringAssessment={updateBulkProctoringAssessment}
        loadingState={loadingState}
      />

      <ProctorCountModal
        is_tolerance_modal_open={is_tolerance_modal_open}
        active_tolerance_option_index={active_tolerance_option_index}
        updateAssessment={updateAssessment}
        closeToleranceLevelModal={closeToleranceLevelModal}
        tolerance_values={tolerance_values}
        assessment={assessment}
        hideSave={hideSaveButton}
      />
    </div>
  );
};

export default Proctoring;
