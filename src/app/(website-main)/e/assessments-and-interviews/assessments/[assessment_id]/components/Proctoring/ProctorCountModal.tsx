import { PROCTORING_TOLERANCE_OPTIONS } from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants';
import { Assessment } from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import Modal from '@/components/Modal';
import ListBox from '@/components/shared/listbox';

const ProctorCoutModal = ({
  is_tolerance_modal_open,
  closeToleranceLevelModal,
  active_tolerance_option_index,
  tolerance_values,
  updateAssessment,
  assessment,
  hideSave,
}: {
  is_tolerance_modal_open: boolean;
  active_tolerance_option_index: number;
  closeToleranceLevelModal: () => void;
  tolerance_values: { value: number }[];
  updateAssessment: (update: any) => void;
  assessment: Assessment;

  hideSave?: boolean;
}) => (
  <Modal
    is_open={is_tolerance_modal_open}
    close={closeToleranceLevelModal}
    title="Change count"
  >
    <form
      className="max-w-[440px] space-y-4 p-4"
      onSubmit={e => {
        e.preventDefault();
        closeToleranceLevelModal();
      }}
    >
      <h2 className="font-semibold capitalize">
        {PROCTORING_TOLERANCE_OPTIONS[active_tolerance_option_index].title}
      </h2>
      <div>
        <p className="helper-text">
          {
            PROCTORING_TOLERANCE_OPTIONS[active_tolerance_option_index]
              .modal_description
          }
        </p>
      </div>
      <div className="space-y-1">
        <h3 className="heading-text">Tolerance Level</h3>
        <div className="flex gap-2">
          <ListBox
            placeholder="Select tolerance level"
            className="w-full border"
            options={tolerance_values}
            value_key="value"
            readable_text_key="value"
            active_option={
              /* @ts-ignore */
              assessment?.[
                PROCTORING_TOLERANCE_OPTIONS[active_tolerance_option_index].id
              ]
            }
            setActiveOption={_option => {
              updateAssessment({
                [PROCTORING_TOLERANCE_OPTIONS[active_tolerance_option_index]
                  .id]: _option,
              });
            }}
          />
        </div>
      </div>

      {hideSave ? null : <button className="btn-primary w-full">Save</button>}
    </form>
  </Modal>
);

export default ProctorCoutModal;
