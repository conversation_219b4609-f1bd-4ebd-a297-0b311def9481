import React, { FormEvent, useState } from 'react';
import DeleteSectionIcon from '@/components/icons/DeleteSectionIcon.jsx';
import Modal from '@/components/Modal';
import Button from '@/components/shared/Button';
import { CandidateInviteResult } from '../../../misc/types/create-assessments';

interface Props {
  is_open: boolean;
  close: () => void;
  activeInvite: CandidateInviteResult | null;
  onResend: (inviteId: string) => Promise<any>;
}

const DeleteQuestionModal: React.FC<Props> = ({
  is_open,
  close,
  activeInvite,
  onResend,
}) => {
  const [is_busy, setIsBusy] = useState(false);
  async function performAction(e: FormEvent) {
    if (activeInvite) {
      setIsBusy(true);
      const res = await onResend(activeInvite.recipient);
      setIsBusy(false);

      close();
    }
  }

  return (
    <Modal
      is_open={is_open}
      title="Resend Invite?"
      close={() => {
        if (!is_busy) return;
        close();
      }}
    >
      <div className="space-y-2 bg-[#F5F3FF] p-4 text-center text-sm text-[#675E8B] md:w-[466px]">
        <div className="flex flex-col items-center gap-2 px-12 py-4">
          {/* <DeleteSectionIcon /> */}
          <h2 className="heading-2 text-primary">Resend Invite</h2>
          <p>
            Kindly confirm that you want to resend this invite to{' '}
            {activeInvite?.recipient_name}?
          </p>
        </div>
      </div>
      <div className="rounded-xl bg-white p-4">
        <div className="flex items-center justify-end gap-2">
          <button
            className="btn-primary-light"
            onClick={close}
            type="button"
            disabled={is_busy}
          >
            No
          </button>
          <Button
            className="btn-primary"
            is_busy={is_busy}
            onClick={performAction}
          >
            Yes, resend
          </Button>
        </div>
      </div>
    </Modal>
  );
};
export default DeleteQuestionModal;
