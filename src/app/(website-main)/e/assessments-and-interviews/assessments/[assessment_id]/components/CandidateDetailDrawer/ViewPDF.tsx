
import React, { useState } from 'react';
import { Small<PERSON>pin<PERSON>, Spinner } from '@/components/shared/icons';

interface props {
    cvFile: string
}
const ViewPDF: React.FC<props> = ({ cvFile }) => {
    const [isLoading, setLoading] = useState(true);

    const handleLoad = () => {
        setLoading(false);
    };

    if (!cvFile) {
        setLoading(false)
        return (
            <div>
                ERROR!!!!! - PLACEHOLDER
            </div>
        )
    }


    return (
        <>
            {
                isLoading &&
                <div className='w-full h-full flex items-center justify-center'>
                    <SmallSpinner color="#755AE2" />
                </div>
            }
            <embed className="w-full h-full min-h-700px" onLoad={handleLoad} src={`data:application/pdf;base64,${cvFile}`} type="application/pdf"></embed>
            {/* <object className="w-full h-full min-h-700px" onLoad={handleLoad} data={`data:application/pdf;base64,${cvFile}`} type="application/pdf">
                {
                    isLoading && <SmallSpinner />
                }
            </object> */}
        </>
    );
}
export default ViewPDF
