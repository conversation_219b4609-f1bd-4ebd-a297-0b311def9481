import { useEffect } from 'react';
import { PROCTORING_OPTIONS } from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants';
import {
  Assessment,
  ProctoringOptions,
  ProctoringToleranceOption,
} from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import Switch from '@/components/Switch';

type OtherSetupProp = {
  option: {
    id: string;
    title: string;
    name: string;
    description: string;
  };
  assessment: Assessment;
  setProctoringSwitch: (_switch: ProctoringOptions, state: boolean) => void;
  editOptions: boolean;
};
const OtherSetup = ({
  option,
  setProctoringSwitch,
  assessment,
  editOptions,
}: OtherSetupProp) => {
  console.log(PROCTORING_OPTIONS);
  return (
    <div className="space-y-4">
      <h3 className="heading-2 mb-1">{option.title}</h3>
      <p className="helper-text max-w-[80%] text-xs">{option.description}</p>
      <div className="space-y-2">
        <label className="flex items-start justify-between gap-4 rounded-xl border p-4">
          <input type="radio" name="invigilate assessment" className="hidden" />
          <div className="space-y-1">
            <h3 className="heading-text">Invigilate assessment</h3>
            <p className="helper-text max-w-[80%] text-xs">
              By tapping the toggle on you will be given the ability to
              invigilate this assessment live. With this on you will be required
              to view an assessment detail and enter click on invigilate to view
              the candidates taking this assessment.
            </p>
          </div>
          <Switch
            // @ts-ignore
            checked={assessment['is_invigilate_assessment']}
            setChecked={state => {
              setProctoringSwitch('invigilate_assessment', state);
            }}
          />
        </label>
        {PROCTORING_OPTIONS.map((option, _index) => (
          <label
            key={_index}
            className="flex items-start justify-between gap-4 rounded-xl border p-4"
          >
            <input type="radio" name={option.title} className="hidden" />
            <div className="space-y-1">
              <h3 className="heading-text">{option.title}</h3>
              <p className="helper-text max-w-[80%] text-xs">
                {option.description}
              </p>
            </div>
            <Switch
              // @ts-ignore
              disabled={!editOptions}
              checked={assessment[`is_${option.key}`]}
              setChecked={state => {
                setProctoringSwitch(option.key, state);
              }}
            />
          </label>
        ))}
      </div>
    </div>
  );
};

// @ts-ignore
export default OtherSetup;
