import { CloseCircle } from 'iconsax-react';
import { BulkProctorOption } from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import Modal from '@/components/Modal';
import { SmallSpinner } from '@/components/shared/icons';
import ListBox from '@/components/shared/listbox';

type BulkProctoringProps = {
  openModal: boolean;
  closeModal: () => void;
  options: BulkProctorOption[];
  selected: BulkProctorOption[];
  removeOption: (index: number) => void;
  updateToleranceLevel: (tl: number) => void;
  toleranceLevel: number;
  addOption: (option: string) => void;
  updateBulkProctoringAssessment: () => void;
  loadingState: boolean;
};

const BulkProctoringModal = ({
  openModal,
  options,
  closeModal,
  addOption,
  removeOption,
  selected,
  toleranceLevel,
  updateToleranceLevel,
  updateBulkProctoringAssessment,
  loadingState,
}: BulkProctoringProps) => (
  <Modal title="Set bulk tolerance" is_open={openModal} close={closeModal}>
    <form
      className="max-w-[440px] space-y-4 p-4"
      onSubmit={e => {
        e.preventDefault();
        updateBulkProctoringAssessment();
      }}
    >
      <h2 className="font-semibold capitalize">Configure Bulk Tolerance</h2>
      <div>
        <p className="helper-text">
          Choose the proctoring options you’d like to include in your bulk
          tolerance settings, then specify the applicable tolerance level count.
        </p>
      </div>
      <div className="">
        <h3 className="heading-text">Proctoring Options</h3>
        <div className="flex gap-2">
          <ListBox
            placeholder="Select"
            className="w-full border"
            options={options}
            value_key="key"
            readable_text_key="value"
            active_option=""
            setActiveOption={_option => {
              addOption(_option);
            }}
          />
        </div>
        <div className="mt-5 flex flex-wrap gap-3">
          {selected.map((s, i) => (
            <div className="btn-primary-light border-rounded flex items-center justify-between p-2 px-4">
              <span>{s.value}</span>
              <button
                type="button"
                className="transparent ml-4 border-0"
                onClick={() => removeOption(i)}
              >
                <CloseCircle />
              </button>
            </div>
          ))}
        </div>

        <div className="mt-10">
          <label className="space-y-2">
            <p className="">Tolerance Level</p>
            <input
              type="number"
              placeholder="Tolerance level"
              className="input-grey"
              value={toleranceLevel}
              onChange={e => {
                if (parseInt(e.target.value) < 2) return;
                updateToleranceLevel(parseInt(e.target.value));
              }}
            />
          </label>
        </div>
      </div>

      <button className="btn-primary flex w-full items-center justify-center">
        Save
        {loadingState && <SmallSpinner />}
      </button>
    </form>
  </Modal>
);

export default BulkProctoringModal;
