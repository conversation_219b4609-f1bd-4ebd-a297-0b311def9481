'use client';

import { useQuery } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import * as React from 'react';
import toast from 'react-hot-toast';
import {
  <PERSON><PERSON>,
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>ger,
  LoaderBtn,
  Skeleton,
  ToolTip,
} from '@/components/shared';
import {
  DoubleForward,
  Eye,
  Info,
  Spinner,
  StrokeCircleCheck,
} from '@/components/shared/icons';
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/shared/tabs';
import { useWindowWidth } from '@/hooks';
import app_fetch from '@/lib/api/appFetch';
import { cn } from '@/utils';
import { getInitials } from '@/utils/strings';
import {
  CandidateAboutResult,
  JobApplicationData,
} from '../../../../misc/types/create-assessments';
import CandidateDetailsAboutTabs from './CandidateDetailDrawerAboutTab';
import CandidateDetailDrawerCommentsTab from './CandidateDetailDrawerCommentsTab';
import CandidateDetailsDocumentsTab from './CandidateDetailsDrawerDocumentsTab';

type _props = {
  candidate_data?: JobApplicationData;
  isDrawerOpen: boolean;
  closeDrawer: () => void;
  openDrawer: () => void;
};

type _optional_props =
  | {
      show_pagination: false;
      moveToPrevious?: never;
      moveToNext?: never;
    }
  | {
      show_pagination?: true;
      moveToNext: () => void;
      moveToPrevious: () => void;
    };

type props = _optional_props & _props;

const CandidateDetailsDrawer: React.FC<props> = ({
  candidate_data,
  isDrawerOpen,
  show_pagination = true,
  closeDrawer,
  moveToNext,
  moveToPrevious,
}) => {
  const windowWidth = useWindowWidth();
  const params = useParams();

  let [isLoadingDetails, setIsLoadingDetails] = React.useState(false);

  async function getCandidateAbout(): Promise<CandidateAboutResult> {
    return app_fetch(
      `recruiter/candidate_applied_job_detail/${candidate_data?.id}`
    ).then(res => res.json());
  }

  const {
    data: candidate_application_details,
    isLoading: is_loading_candidate_application_details,
    error: candidate_application_details_error,
    isError: is_candidate_application_details_error,
  } = useQuery({
    queryKey: [],
    queryFn: getCandidateAbout,
    enabled: !!candidate_data?.id,
  });

  const tabListRef = React.useRef<HTMLDivElement>(null);
  const [currentTab, setCurrentTab] = React.useState('about');
  const categoryArray = [
    {
      id: 1,
      title: 'About',
    },
    {
      id: 2,
      title: 'Documents',
    },
    {
      id: 3,
      title: 'Assessments',
    },
    {
      id: 4,
      title: 'Comments',
    },
    {
      id: 5,
      title: 'Journey',
    },
  ];

  const close = () => {
    closeDrawer();
    setCurrentTab('about');
  };

  React.useEffect(() => {
    if (!candidate_data) {
      close();
    }
  }, [candidate_data]);

  if (!candidate_data) {
    return <div></div>;
  }

  return (
    <Drawer
      onClose={() => close()}
      open={isDrawerOpen}
      dismissible
      direction={windowWidth < 720 ? 'bottom' : 'right'}
    >
      <DrawerContent className="!m-0 h-[90vh] w-full overflow-hidden rounded-l-2xl border-none bg-white !p-0 md:left-auto md:right-0 md:h-screen md:w-[60%] md:max-w-[750px]">
        <div className="relative flex max-h-full grow flex-col overflow-y-hidden">
          <div className="flex w-full items-center justify-center bg-primary md:hidden">
            <div className="mx-auto mb-1 mt-3 h-1 w-[100px] rounded-full bg-white/60" />
          </div>

          <DrawerHeader className="sticky top-0 flex w-full items-center justify-between bg-primary px-5 text-white md:px-8 max-md:pt-1">
            <h3>Candidate Details</h3>
            <DrawerClose
              onClick={close}
              className="rounded-lg bg-white/30 px-6 py-2 text-sm"
            >
              Close
            </DrawerClose>
          </DrawerHeader>

          <section className="flex grow flex-col overflow-y-scroll">
            <header className="flex items-start gap-4 p-6 pb-4">
              {isLoadingDetails ? (
                <Skeleton className="h-14 w-14 shrink-0 rounded-full" />
              ) : (
                <div className="flex h-14 w-14 shrink-0 items-center justify-center rounded-full bg-primary text-xl font-bold text-white">
                  {getInitials(candidate_data?.name || 'First Last')}
                </div>
              )}
              <div>
                {isLoadingDetails ? (
                  <>
                    <Skeleton className="h-4 w-28 shrink-0 rounded-md md:w-60" />
                    <div className="mt-2.5 flex items-center gap-2">
                      <Skeleton className="h-2.5 w-16 shrink-0 rounded-md md:w-40" />
                      <Skeleton className="h-8 w-6 shrink-0 rounded-md md:w-32" />
                      <Skeleton className="h-8 w-6 shrink-0 rounded-md md:w-32" />
                    </div>
                  </>
                ) : (
                  <>
                    <h4 className="text-lg font-medium text-primary md:text-xl">
                      {candidate_data?.name}
                    </h4>
                    <section className="flex flex-wrap items-center gap-3">
                      <p className="text-sm">{candidate_data?.email}</p>
                    </section>
                  </>
                )}
              </div>
            </header>

            <Tabs
              defaultValue={'about'}
              className="!grid grow grid-rows-[max-content,1fr] overflow-x-hidden"
            >
              <TabsList
                ref={tabListRef}
                className={cn(
                  'flex items-center justify-start gap-1.5 overflow-x-auto bg-[#F1EFFC] p-[0.25rem] px-6 [scrollbar-width:none] md:justify-start '
                )}
              >
                {categoryArray?.map((cat: { id: number; title: string }) => {
                  const { title, id } = cat;
                  return (
                    <TabsTrigger
                      className={cn(
                        'taboption relative min-w-[6rem] flex-1 transition-all duration-500 md:min-w-[5rem] md:max-w-max',
                        'rounded-full py-1.5 md:rounded-[0.55rem] md:px-4 lg:px-6',
                        'text-xs font-normal leading-5 xs:text-sm',
                        'focus:outline-none active:outline-none',
                        title.toLowerCase() === currentTab.toLowerCase()
                          ? 'active'
                          : 'taboption text-body-text '
                      )}
                      onClick={() => setCurrentTab(title.toLowerCase())}
                      value={title.toLowerCase()}
                      key={id}
                    >
                      <span className="flex items-center gap-3">{title}</span>
                    </TabsTrigger>
                  );
                })}
              </TabsList>

              {is_loading_candidate_application_details &&
              is_candidate_application_details_error ? (
                <div className="flex h-full w-full items-center justify-center">
                  <Spinner />
                </div>
              ) : (
                <TabsContent
                  className="mt-2 max-w-full overflow-y-scroll"
                  value={currentTab.toLowerCase()}
                >
                  {/* /////////////////////////////////////////////////////////////////////////// */}
                  {/* /////////////////////////////////////////////////////////////////////////// */}
                  {currentTab.toLowerCase() === 'about' && (
                    <CandidateDetailsAboutTabs
                      data={candidate_application_details!}
                    />
                  )}

                  {/* /////////////////////////////////////////////////////////////////////////// */}
                  {/* /////////////////////////////////////////////////////////////////////////// */}
                  {currentTab.toLowerCase() === 'documents' && (
                    <CandidateDetailsDocumentsTab
                      isLoadingCVFile={false}
                      cvFileName={
                        candidate_application_details?.job_application.cv || ''
                      }
                      cvFile={
                        candidate_application_details?.job_application
                          .cv_data || ''
                      }
                    />
                  )}

                  {/* /////////////////////////////////////////////////////////////////////////// */}
                  {/* /////////////////////////////////////////////////////////////////////////// */}
                  {currentTab.toLowerCase() === 'comments' && (
                    <CandidateDetailDrawerCommentsTab
                      assessment_id={params.assessment_id as string}
                      candidate_email={candidate_data.email}
                    />
                  )}
                  {/* /////////////////////////////////////////////////////////////////////////// */}
                  {/* /////////////////////////////////////////////////////////////////////////// */}
                  {currentTab.toLowerCase() === 'journey' &&
                    candidate_data?.trail && (
                      <p>Journey tab</p>
                      //<CandidateDetailsTrailTab data={basicDetails?.trail} />
                    )}
                </TabsContent>
              )}
            </Tabs>
          </section>

          {show_pagination && (
            <DrawerFooter className="sticky bottom-0 flex w-full flex-row flex-wrap items-center justify-between rounded-t-xl bg-primary-light text-white md:px-8 ">
              <Button
                size="tiny"
                variant="outlined"
                onClick={moveToPrevious}
                icon={<DoubleForward className="rotate-180" />}
              >
                Prev
              </Button>
              <Button size="tiny" variant="outlined" onClick={moveToNext}>
                Next
                <DoubleForward />
              </Button>
            </DrawerFooter>
          )}
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export default CandidateDetailsDrawer;
