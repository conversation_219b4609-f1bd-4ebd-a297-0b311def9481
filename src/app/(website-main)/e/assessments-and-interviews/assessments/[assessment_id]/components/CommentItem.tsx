import EllipsesVerticalIcon from "@/components/icons/jsx/EllipsesVerticalIcon";
import app_fetch from "@/lib/api/appFetch";
import Avatar from "@/components/shared/avatar-alt";
import { Rating } from "@smastrom/react-rating";
import { COMMENT_OPTIONS_DICTIONARY, COMMENT_OPTIONS } from "../../../misc/constants/constants";
import { Comment, CommentActions } from "../../../misc/types/create-assessments";
import DropdownMenu from "@/components/DropdownMenu";
import IconElement from "@/components/icons/jsx/IconElement";

const StarIcon = (
  <path d="M398.799,141.794c-43.394-3.977-86.776-6.52-130.158-8.418C258.835,99.302,242.633-4.751,193.173,0.169
      c-39.659,3.944-61.012,90.515-73.08,130.306c-32.333,0.283-64.692,1.062-97.09,2.416c-14.735,0.615-27.908,17.9-18.207,31.732
      c19.157,27.316,44.198,49.389,70.487,70.103c-11.83,38.196-21.665,77.499-29.759,116.53c-3.504,16.91-5.31,32.212,3.881,44.82
      c2.411,9.987,12.018,18.494,22.429,18.029c51.805-2.313,93.872-44.738,133.991-77.119c33.156,26.317,66.309,52.64,99.475,78.951
      c12.835,10.183,37.057,5.178,35.798-14.828c-3.039-48.158-15.477-96.473-30.599-144.041c32.951-25.229,65.899-50.459,99.11-75.353
      C426.818,168.817,420.858,143.814,398.799,141.794z" />
);



interface CommentProps {
  comment: Comment,
  onAction: (action: CommentActions, comment: Comment) => void,
  is_use_dropdown?: Boolean,
}

const CommentItem: React.FC<CommentProps> = ({ comment, onAction, is_use_dropdown = true }) => {
  function handleCommentAction(comment: Comment, selected_option: typeof COMMENT_OPTIONS_DICTIONARY[keyof typeof COMMENT_OPTIONS_DICTIONARY]) {
    onAction(selected_option.title as CommentActions, comment)
  }

  return (
    <div className="space-y-2">
      <div className="flex gap-2 justify-between">
        <div className="flex gap-2 items-center">
          <Avatar name={comment.recruiter_name} />
          <h2 className="heading-text">{comment.recruiter_name}</h2>
        </div>
        <div className="helper-text flex gap-3 items-center">
          <div>
            <Rating
              readOnly
              style={{ maxWidth: 120 }}
              itemStyles={{
                itemShapes: StarIcon,
                activeFillColor: "#755AE2",
                inactiveFillColor: "#E2DDFF",
              }}
              value={comment.rating}
            />
          </div>
          <div className="w-1 bg-gray-200 h-[50%]"></div>
          <p>{`${new Date(comment.timestamp).toLocaleString()}`}</p>
          {is_use_dropdown ? (
            <DropdownMenu
              options={COMMENT_OPTIONS}
              readable_text_key="title"
              callback={(_selected_option) =>
                handleCommentAction(
                  comment,
                  _selected_option,
                )}
              button={
                <button>
                  <EllipsesVerticalIcon />
                </button>
              }
            />

          ) : (
            <div className="flex gap-2 items-center">
              {COMMENT_OPTIONS.map((option, index) => (
                <button key={index} onClick={() => handleCommentAction(comment, option)}>
                  {option?.icon && <IconElement icon={option.icon} />}
                </button>
              ))}
            </div>

          )}
        </div>
      </div>
      <p className="p-2 helper-text">{comment.text}</p>
    </div>
  )
}

export default CommentItem;
