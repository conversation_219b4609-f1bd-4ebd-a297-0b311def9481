'use client';

import { format } from 'date-fns';
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from 'next/navigation';
import React, { useState } from 'react';
import TableActionButton from '@/app/(website-main)/e/assessments-and-interviews/misc/components/TableActionButton';
import {
  ASSESSMENT_STATUSES_DICTIONARY,
  CANDIDATE_TABLE_OPTIONS,
  CANDIDATE_TABLE_OPTIONS_DICTIONARY,
} from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants';
import DropdownMenu from '@/components/DropdownMenu';
import FlagIcon from '@/components/icons/FlagIcon';
import Avatar from '@/components/shared/avatar-alt';
import Select from '@/components/shared/select-simple';
import { useBooleanStateControl } from '@/hooks';
import DeleteCandidateModal from '../../../misc/components/DeleteCandidateModal';
import { FetchCandidateResult } from '../../../misc/types/create-assessments';
import CandidateDetailsDrawer from './CandidateDetailDrawer';

type props = {
  isSuccess: boolean;
  data?: FetchCandidateResult;
  isLoading: boolean;
  isError: boolean;
  error: unknown;
  refetch: () => void;
};

export default function AssessmentCandidatesList({
  isSuccess,
  isError,
  isLoading,
  data,
  error,
  refetch,
}: props) {
  const router = useRouter();
  const params = useParams();
  const assessment_id = params.assessment_id as string;

  const searchParams = useSearchParams();

  const status_filter = searchParams?.get('status_filter');
  const search_filter = searchParams?.get('search_filter');

  const [display_length, setDisplayLength] = useState('50');

  const display_length_options = [
    { name: '2' },
    { name: '50' },
    { name: '100' },
    { name: '500' },
    { name: '1000' },
  ].filter(
    option =>
      Number(option.name) <= (data?.candidates?.length || 50) ||
      (Number(option.name) === 500 && (data?.candidates?.length || 0) > 100) ||
      (Number(option.name) === 1000 && (data?.candidates?.length || 0) > 500) ||
      Number(option.name) <= 50
  );

  const displayed_candidates = React.useMemo(() => {
    return (
      data?.candidates
        ?.filter(candidate => {
          const search_filter_exist = !!search_filter;
          const status_filter_exist = !!status_filter;
          const _candidate_name_filtered = (candidate?.candidate_name || '')
            .toLowerCase()
            ?.includes(search_filter?.toLowerCase() as string);
          const _candidate_email_filtered = candidate?.candidate_email
            .toLowerCase()
            ?.includes(search_filter?.toLowerCase() as string);
          const _candidate_status_filtered = candidate?.status
            .toLowerCase()
            ?.includes(status_filter || '');

          const candidate_name_filtered =
            search_filter_exist && _candidate_name_filtered;
          const candidate_email_filtered =
            search_filter_exist && _candidate_email_filtered;
          const candidate_status_filtered =
            status_filter_exist && _candidate_status_filtered;

          let should_show = true;

          if (search_filter) {
            should_show =
              should_show &&
              (candidate_email_filtered || candidate_name_filtered);
          }

          if (status_filter) {
            should_show = should_show && candidate_status_filtered;
          }

          return should_show;
        })
        .slice(0, Number(display_length)) || []
    );
  }, [data?.candidates, search_filter, status_filter, display_length, data]);

  console.log(data);

  function refetchQuery() {
    refetch();
  }

  const {
    state: is_candidate_details_drawer_open,
    setTrue: openCandidateDetailsDrawer,
    setFalse: closeCandidateDetailsDrawer,
  } = useBooleanStateControl(false);

  const {
    state: is_delete_candidate_drawer_open,
    setTrue: openDeleteCandidateDrawer,
    setFalse: closeDeleteCandidateDrawer,
  } = useBooleanStateControl(false);

  const [active_candidate_index, setActiveCandidateIndex] = useState(0);
  function moveToNextCandidate() {
    setActiveCandidateIndex(
      (active_candidate_index + 1 + (data?.candidates.length || 0)) %
        (data?.candidates.length || 1)
    );
  }

  function moveToPreviousCandidate() {
    setActiveCandidateIndex(
      (active_candidate_index - 1 + (data?.candidates.length || 0)) %
        (data?.candidates.length || 1)
    );
  }

  function handleAssessmentAction(candidate: any, selected_option: any) {
    switch (selected_option) {
      case CANDIDATE_TABLE_OPTIONS_DICTIONARY['view details']:
        router.push(
          `/e/assessments-and-interviews/assessments/${assessment_id}/candidates/${candidate.candidate_email}/`
        );
        break;
      case CANDIDATE_TABLE_OPTIONS_DICTIONARY['quick view']:
        openCandidateDetailsDrawer();
        break;
      case CANDIDATE_TABLE_OPTIONS_DICTIONARY['remove candidate']:
        openDeleteCandidateDrawer();
        break;
    }
  }

  return (
    <>
      <table className="w-full divide-y border-b">
        <thead>
          <tr>
            {'rank, candidate, score, invite date, date taken, status, forced submission, flags, action'
              .split(', ')
              .map((heading, index) => (
                <th
                  className="pb-4 text-left font-normal capitalize"
                  key={index}
                >
                  {heading}
                </th>
              ))}
          </tr>
        </thead>
        <tbody className="divide-y  ">
          {isLoading && (
            <div className="p-4">
              <p>Loading...</p>
            </div>
          )}
          {isError && (
            <div className="p-4 text-red-500">
              <p>Error loading candidates</p>
            </div>
          )}

          {isSuccess &&
            displayed_candidates.map((candidate, index) => (
              <tr
                className="cursor-pointer hover:bg-primary-light"
                onClick={() => {
                  router.push(
                    `/e/assessments-and-interviews/assessments/${params.assessment_id}/candidates/${candidate.candidate_email}/`
                  );
                }}
                key={index}
              >
                <td className="p-4">{index + 1}</td>
                <td className="p-4">
                  <div className="flex items-center gap-2">
                    <Avatar name={candidate?.candidate_name || 'NO NAME'} />
                    {candidate.candidate_name || 'NO NAME'}
                  </div>
                </td>
                <td className="p-4">
                  {candidate.overall_percentage
                    ? candidate.overall_percentage + '%'
                    : '--'}
                </td>
                <td className="p-4">
                  {format(
                    new Date(candidate.invite_date),
                    'dd/MM/yyyy, hh:mma'
                  )}
                </td>
                <td className="p-4">
                  {candidate.date_taken
                    ? format(
                        new Date(candidate.date_taken),
                        'dd/MM/yyyy, hh:mma'
                      )
                    : '-'}
                </td>
                <td className="p-4">
                  <p
                    className={` ${
                      candidate.status === 'ongoing'
                        ? 'text-yellow-500'
                        : candidate.status === 'completed'
                        ? 'text-green-500'
                        : 'text-red-500'
                    } } `}
                  >
                    {ASSESSMENT_STATUSES_DICTIONARY[candidate.status]?.title}
                  </p>
                </td>
                <td className="p-4">
                  {candidate.was_forced_submission ? (
                    <p className="text-red-500">Yes</p>
                  ) : (
                    <p className=" text-black-500">--</p>
                  )}
                </td>
                <td className="p-4">
                  <p className="flex items-center gap-2">
                    <FlagIcon />
                    <span>
                      {' '}
                      {Intl.NumberFormat(navigator.languages).format(
                        candidate.flags_count
                      )}{' '}
                    </span>
                  </p>
                </td>
                <td className="p-4">
                  <div className="inline-flex justify-start">
                    <DropdownMenu
                      options={CANDIDATE_TABLE_OPTIONS}
                      readable_text_key="title"
                      callback={_selected_option => {
                        setActiveCandidateIndex(index);
                        handleAssessmentAction(candidate, _selected_option);
                      }}
                      button={<TableActionButton />}
                    />
                  </div>
                </td>
              </tr>
            ))}
        </tbody>
      </table>

      <div className="mt-2 flex items-center justify-between gap-4 px-8">
        <label className="flex items-center gap-2">
          Displayed candidates:
          <Select
            name="displayed candidates limit"
            value={display_length}
            labelKey="name"
            onChange={setDisplayLength}
            options={display_length_options}
            placeholder="Limit number of displayed candidates"
            valueKey="name"
          />
        </label>
        <div className="flex gap-2">
          <p> Total candidates: {data?.candidates.length} </p>
          {displayed_candidates.length !== data?.candidates.length && (
            <p className="border-l pl-2">
              {' '}
              Filtered candidates: {displayed_candidates.length}{' '}
            </p>
          )}
        </div>
      </div>

      <CandidateDetailsDrawer
        candidate_data={
          data?.candidates[active_candidate_index]?.job_application
        }
        isDrawerOpen={is_candidate_details_drawer_open}
        closeDrawer={closeCandidateDetailsDrawer}
        openDrawer={openCandidateDetailsDrawer}
        moveToNext={moveToNextCandidate}
        moveToPrevious={moveToPreviousCandidate}
      />

      <DeleteCandidateModal
        assessment_id={assessment_id}
        candidate_id={data?.candidates[active_candidate_index].id}
        is_open={is_delete_candidate_drawer_open}
        close={closeDeleteCandidateDrawer}
        onComplete={refetchQuery}
      />
    </>
  );
}
