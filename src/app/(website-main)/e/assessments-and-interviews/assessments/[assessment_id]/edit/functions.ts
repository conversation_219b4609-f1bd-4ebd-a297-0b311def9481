import app_fetch from "@/lib/api/appFetch";
import { Assessment, CreateQuestion, Question, Section } from "@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments";

  export function handleAddCustomQuestion(assessment:Assessment, setAssessment:any, question:CreateQuestion, section_index:number){
    return new Promise((resolve, reject) => {
      const new_sections = assessment.sections;
      let _active_section = new_sections[section_index];


      /* if the active section has no ID i.e it doesnt exist in the backend, create it */
      if (!_active_section.id) {
        const section = {
          "section_name": "",
          "test": [],
        };
        app_fetch(
          `assessments/assessment-section/${assessment.id}/`,
          {
            method: "POST",
            body: section,
          },
        ).then(async (response) => await response.json())
          .then((result) => {
            _active_section = result.assessment.sections[section_index];
          })
          .catch((error) => {
            reject(error);
            return;
          });
      }

    const _form_data = new FormData()

    const data = {
      ...question,
      "category": "role_specific",
      role: assessment.role.id,
      experience_level: assessment.role_level,
    }

    Object.entries(data).map(([key, val])=>{
      if (key==="images"){
        const _val = val as unknown as  File[]
        _val.map((val)=>{
          _form_data.append(key, val, `${val.name}.${val.type.split("/")[1]}`)
        })
        return
      }
      //@ts-ignore
      _form_data.set(key, val)
    })

    app_fetch(
      `assessments/${assessment.id}/sections/${_active_section.id}/add_question/`,
      {
        method: "POST",
        body: _form_data
      },
      false
    )
      .then(async (response) => await response.json())
      .then((result:{message:string, section:Section}) => {

        const question_types = new Set(
          new_sections[section_index]?.question_types,
        );
        question_types.add(question.type);

        new_sections[section_index] = {
          ...result.section,
          name: result.section.section_name || "",
          question_types: Array.from(question_types),
        };
        setAssessment({...assessment, sections:new_sections})
        resolve({ message: "success" });
      })
      .catch((error) => {
        reject(error);
      });
  });
}

export function blobToBase64(blob:Blob) {
  return new Promise((resolve, _) => {
    const reader = new FileReader();
    reader.onloadend = () => resolve(reader.result);
    reader.readAsDataURL(blob);
  });
}

