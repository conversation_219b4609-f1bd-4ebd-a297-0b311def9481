'use client';

import { useMutation } from '@tanstack/react-query';
import Link from 'next/link';
import { useParams, usePathname } from 'next/navigation';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import UploadIcon from '@/app/(public-links)/jobs/misc/icons/UploadIcon';
import {
  PROCTORING_OPTIONS, PROCTORING_SECTION_OPTIONS,PROCTORING_TOLERANCE_OPTIONS,
  TEAM_MEMBER_ROLES,
  YES_OR_NO,
} from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants';
import {
  Assessment,
  ProctoringOptions,
  ProctoringToleranceOption,
  TeamMember,
  TeamMemberRoleTypes,
} from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import { PlayIcon } from '@/app/(website-main)/t/showcase/misc/icons';
import OpenEmailIcon from '@/components/icons/jsx/OpenEmailIcon';
import QuestionPackSuccessIcon from '@/components/icons/jsx/QuestionPackSuccessIcon';
import Modal from '@/components/Modal';
import { Loader } from '@/components/shared';
import Button from '@/components/shared/Button';
import Checkbox from '@/components/shared/Checkbox/Checkbox';
import CaretDownIcon from '@/components/shared/icons/CaretDown';
import ListBox from '@/components/shared/listbox';
import Switch from '@/components/Switch';
import app_fetch from '@/lib/api/appFetch';
import { numberToPosition, secondsToLargestUnit } from '@/lib/utils/functions';
import { updateAssessmentLastEditURL } from '../../../../misc/api/Assessment';
import CreateAssessmentHeader from '../../../../misc/components/CreateAssessmentHeader';
import TimeInput from '../../../../misc/components/TimeInput';
import { blobToBase64 } from '../functions';
import AssessmentDetail from './components/AssessmentDetail';
import Others from './components/Others';
import Proctoring from '../../components/Proctoring/Proctoring';
import ProctorCountModal from '../../components/Proctoring/ProctorCountModal';
import Team from './components/Team';

export default function Page() {
  const path_name = usePathname();

  const sections = [
    {
      id: 0,
      key: 'details',
      title: 'Assessment details',
      description: 'See and update your assessment details and guidelines',
    },
    {
      id: 1,
      key: 'team',
      title: 'Add team members',
      description: 'Here you can add teams who can access this assessment.',
    },
    {
      id: 2,
      key: 'Proctoring',
      title: 'Proctoring and Invigilation',
      description:
        'Here you can configure the anti-cheating settings for the assessment',
    },
    {
      id: 3,
      key: 'others',
      title: 'Other settings',
      description: 'View and configure timer and deadline for this assessment',
    },
  ];

  const [active_page_section_index, setActivePageSectionIndex] = useState(0);

  const params = useParams();

  /*  === ASSESSMENT === */
  const [assessment, setAssessment] = useState({} as Assessment);

  function updateAssessment(update: any) {
    const changed_assessment = { ...assessment, ...update };
    setAssessment(changed_assessment);
  }

  useEffect(() => {
    app_fetch(`assessments/${params.assessment_id}/`)
      .then(res => res.json())
      .then(res => {
        (res.commencement_settings = res.commencement_settings ? 'yes' : 'no'),
          (res.start_time = res.start_time?.split('+')[0]),
          (res.deadline = res.deadline?.split('+')[0]),
          updateAssessment(res);
      });
  }, []);

  const [is_proceeding_to_next_step, setIsProceedingToNextStep] =
    useState(false);

  const [is_proceed_options_modal_open, setIsProceedOptionsModalOpen] =
    useState(false);
  function closeProceedOptionsModal() {
    setIsProceedOptionsModalOpen(false);
  }
  function openProceedOptionsModal() {
    setIsProceedOptionsModalOpen(true);
  }

  function handlePreProceed() {
    if (!assessment.description || assessment.description.length < 15) {
      setActivePageSectionIndex(0);
      alert(
        'You need to add a description with up to 15 characters to continue'
      );
      return;
    }

    if (!assessment.start_time) {
      setActivePageSectionIndex(3);
      alert('You need to set a start time');
      return;
    }

    if (assessment.time_limit <= 0) {
      setActivePageSectionIndex(3);
      alert('You need to set a time limit greater than 0');
      return;
    }

    if (assessment.commencement_settings == 'yes' && !assessment.deadline) {
      setActivePageSectionIndex(3);
      alert('You need to set a deadline');
      return;
    }

    if (
      assessment.commencement_settings == 'yes' &&
      assessment.start_time > assessment.deadline
    ) {
      setActivePageSectionIndex(3);
      alert('The deadline cannot be earlier than the star time');
      return;
    }

    function editAssessment() {
      return new Promise(async (resolve, reject) => {
        const _assessment: Assessment = assessment;
        let body = {
          name: _assessment.name,
          description: _assessment.description,

          is_shuffle: _assessment.is_shuffle,
          is_shuffle_sections: _assessment.is_shuffle_sections,

          is_webcam_snapshot: _assessment.is_webcam_snapshot,
          is_restrict_copying: _assessment.is_restrict_copying,
          is_restrict_tab_change: _assessment.is_restrict_tab_change,
          is_track_paste: _assessment.is_track_paste,
          stop_screen_sharing: _assessment.is_stop_screen_sharing,
          invigilate_assessment: _assessment.is_invigilate_assessment,
          full_screen_tolerance_level: _assessment.full_screen_tolerance_level,
          window_change_tolerance_level:
            _assessment.window_change_tolerance_level,

          time_limit: _assessment.time_limit,
          deadline: _assessment.deadline,
          start_time: _assessment.start_time,
          commencement_settings: _assessment.commencement_settings == 'yes',
          type:
            _assessment.commencement_settings == 'yes' ? 'flexible' : 'fixed',

          intro_video_url: undefined as any,
        };

        async function sendRequest() {
          const options = {
            method: 'PATCH',
            body: JSON.stringify(body),
          };

          await app_fetch(`assessments/assessments/${_assessment.id}/`, options)
            .then(response => response.json())
            .then(result => resolve({ message: 'success' }))
            .catch(error => reject(error));
        }

        sendRequest().then(() => {
          if (_assessment.intro_video) {
            blobToBase64(_assessment.intro_video).then((base_64_vid: any) => {
              body = { intro_video_url: base_64_vid } as any;
              sendRequest();
            });
          }
        });
      });
    }

    updateAssessmentLastEditURL(assessment.id, 'edit/settings');

    setIsProceedingToNextStep(true);
    editAssessment()
      .then(() => {
        saveTeamMembers();
      })
      .finally(() => {
        openProceedOptionsModal();
        setIsProceedingToNextStep(false);
      });
  }

  /*=== TEAM MEMBERS*/

  const [team_members, setTeamMembers] = useState([] as TeamMember[]);
  const [filtered_team_members, setFilteredTeamMembers] = useState(
    [] as TeamMember[]
  );
  const [is_loading_team_members, setIsLoadingTeamMembers] = useState(false);
  const [selected_team_members, setSelectedTeamMembers] = useState(
    [] as TeamMember[]
  );
  const [team_member_search_term, setTeamMemberSearchTerm] = useState('');

  useEffect(() => {
    setIsLoadingTeamMembers(true);
    app_fetch('recruiter/team/')
      .then(res => res.json())
      .then((res: { member: TeamMember }[]) => {
        const _res = res.reduce((acc, curr) => {
          acc.push({
            id: curr.member.id,
            company_email: curr.member.company_email,
          });
          return acc;
        }, [] as TeamMember[]);
        setTeamMembers(_res);
      })
      .finally(() => {
        setIsLoadingTeamMembers(false);
      });
  }, []);

  useEffect(() => {
    setFilteredTeamMembers(
      team_members.filter(
        team_member =>
          !selected_team_members.includes(team_member) &&
          team_member.company_email?.includes(team_member_search_term)
      )
    );
  }, [team_members, team_member_search_term]);

  function addTeamMember(team_member: TeamMember) {
    setSelectedTeamMembers([...selected_team_members, team_member]);
  }
  function removeTeamMember(team_member: TeamMember) {
    setSelectedTeamMembers(
      selected_team_members.filter(
        _team_member => _team_member.id != team_member.id
      )
    );
  }

  const [is_add_team_members_modal_open, setIsAddTeamMembersModalOpen] =
    useState(false);
  function closeAddTeamMembersModal() {
    setIsAddTeamMembersModalOpen(false);
  }
  function openAddTeamMembersModal() {
    setIsAddTeamMembersModalOpen(true);
  }

  const [is_saving_team_members, setIsSavingTeamMembers] = useState(false);
  function saveTeamMembers() {
    setIsSavingTeamMembers(true);
    const options = {
      method: 'POST',
      body: JSON.stringify(selected_team_members),
    };
    app_fetch(`assessments/${assessment.id}/update-role/`, options)
      .then(() => {
        closeAddTeamMembersModal();
      })
      .finally(() => {
        setIsSavingTeamMembers(false);
      });
  }

  /* intro video */
  const [videoLength, setVideoLength] = useState('0:00');
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const [is_playing, setIsPlaying] = useState<boolean>(false);
  const [current_video_time, setCurrentVideoTime] = useState<string>('0:00');

  function clearIntroVideo() {
    setVideoLength('0:00');
    setCurrentVideoTime('0:00');
    const _assessment = { intro_video: undefined };
    updateAssessment(_assessment);
    setVideoUrl('');
  }

  const togglePlay = () => {
    if (videoRef.current) {
      const totalSeconds: number = Math.floor(videoRef.current.duration);
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;
      const length = `${minutes}:${seconds.toString().padStart(2, '0')}`;
      setVideoLength(length);

      if (is_playing) {
        setIsPlaying(false);
        videoRef.current.pause();
      } else {
        setIsPlaying(true);
        videoRef.current.play();
      }
    }
  };

  const [video_url, setVideoUrl] = useState('');

  function previewIntroVideo(acceptedFiles: any) {
    const formdata = new FormData();
    formdata.append('file', acceptedFiles[0]);
    const blob = new Blob([acceptedFiles[0]], {
      type: 'video/mp4', // or whatever your Content-Type is
    });
    updateAssessment({ intro_video: blob });
    setVideoUrl(URL.createObjectURL(acceptedFiles[0]));
    return;
  }

  function onDrop(acceptedFiles: any) {
    previewIntroVideo(acceptedFiles);
  }

  // using callback breaks the states for some weird reason
  /* const onDrop = useCallback((acceptedFiles: any) => { */
  /*   updateAssessment({intro_test:"something"}) */
  /*   previewIntroVideo(acceptedFiles); */
  /* }, []); */

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      'video/mp4': [],
    },
    maxSize: 10 * 1024 * 1024, //10mb
  });

  function setProctoringSwitch(
    _switch: ProctoringOptions,
    new_switch_state: boolean
  ) {
    switch (_switch) {
      case 'webcam_snapshot':
        updateAssessment({ is_webcam_snapshot: new_switch_state });
        break;
      case 'restrict_copying':
        updateAssessment({ is_restrict_copying: new_switch_state });
        break;
      case 'restrict_tab_change':
        updateAssessment({ is_restrict_tab_change: new_switch_state });
        break;
      case 'track_paste':
        updateAssessment({ is_track_paste: new_switch_state });
        break;
      case 'stop_screen_sharing':
        updateAssessment({ is_stop_screen_sharing: new_switch_state });
        break;
      case 'invigilate_assessment':
        updateAssessment({ is_invigilate_assessment: new_switch_state });
        break;
    }
  }



  const [activeProctoringSection, setActiveProctoringSection] =
    useState<number>(0);





  async function generateIntroMessageAPI() {
    return app_fetch(
      `assessments/generate-intro-message/${assessment.id}`
    ).then(res => res.json());
  }

  const generateIntroMessageMutation = useMutation({
    mutationFn: generateIntroMessageAPI,
    onSuccess: (result: string) => {
      updateAssessment({ description: result });
    },
  });

  return (
    <div>
      <CreateAssessmentHeader
        assessment={assessment}
        current_step={3}
        is_proceeding={is_proceeding_to_next_step}
        back_page_url={`${path_name.replace('/settings', '/questions')}`}
        handleProceed={handlePreProceed}
      />
      <div className="grid divide-x rounded-xl bg-white md:grid-cols-3">
        <aside className="space-y-2 py-4">
          <section className="sticky top-28 max-h-min">
            <div className="px-4">
              <h1 className="heading-2">Settings and configuration</h1>
              <p className="text-sm text-[#8C8CA1]">
                Please review, configure, edit, and make any necessary changes
                to your assessment before inviting the first candidate
              </p>
            </div>
            <nav>
              <ul className="divide-light-accent-bg divide-y">
                {sections.map((section, index) => (
                  <li key={index}>
                    <label
                      className={`flex items-center justify-between gap-4 ${
                        section.key === 'team' ? 'opacity-30' : 'cursor-pointer'
                      } p-4 ${
                        section.id == active_page_section_index
                          ? 'bg-light-accent-bg'
                          : ''
                      } `}
                    >
                      <input
                        type="radio"
                        name="active_page_section_id"
                        className="hidden"
                        value={section.id}
                        disabled={section.key === 'team'}
                        onChange={e =>
                          setActivePageSectionIndex(parseInt(e.target.value))
                        }
                      />
                      <div>
                        <h2
                          className={`heading-text text-sm ${
                            section.id == active_page_section_index
                              ? 'text-primary'
                              : ''
                          }`}
                        >
                          {section.title}
                        </h2>
                        <p
                          className={`text-xs ${
                            section.id == active_page_section_index
                              ? 'text-[#8C8CA1]'
                              : 'helper-text'
                          }`}
                        >
                          {section.description}
                        </p>
                      </div>
                      {section.id == active_page_section_index && (
                        <span className="inline-block -rotate-90">
                          <CaretDownIcon />
                        </span>
                      )}
                    </label>
                  </li>
                ))}
              </ul>
            </nav>
          </section>
        </aside>

        <div className="space-y-6 px-8 py-4 text-sm md:col-span-2">
          <div>
            <h2 className="heading-2 text-primary">
              {sections[active_page_section_index]?.title}
            </h2>
            <p className="helper-text text-xs">
              {sections[active_page_section_index]?.description}
            </p>
          </div>

          {/* Assessment details */}
          {sections[active_page_section_index]?.key == 'details' && (
            <AssessmentDetail
              assessment={assessment}
              updateAssessment={updateAssessment}
              generateIntroMessageMutation={generateIntroMessageMutation}
              getInputProps={getInputProps}
              getRootProps={getRootProps}
              is_playing={is_playing}
              video_url={video_url}
              videoRef={videoRef}
              setCurrentVideoTime={setCurrentVideoTime}
              setIsPlaying={setIsPlaying}
              current_video_time={current_video_time}
              togglePlay={togglePlay}
              clearIntroVideo={clearIntroVideo}
            />
          )}

          {/* teams */}
          {sections[active_page_section_index].key == 'team' && (
            <Team
              selected_team_members={selected_team_members}
              team_members={team_members}
              openAddTeamMembersModal={openAddTeamMembersModal}
              setSelectedTeamMembers={setSelectedTeamMembers}
              removeTeamMember={removeTeamMember}
            />
          )}

          {/* Proctoring */}
          {sections[active_page_section_index].key == 'Proctoring' && (
            <Proctoring
              assessment={assessment}
              updateAssessment={updateAssessment}
              proctoring_section_options={PROCTORING_SECTION_OPTIONS}
              setProctoringSwitch={setProctoringSwitch}
              proctoring_tolerance_options={PROCTORING_TOLERANCE_OPTIONS}
             />
          )}

          {/* Assessment deadline */}
          {sections[active_page_section_index].key == 'others' && (
            <Others
              assessment={assessment}
              updateAssessment={updateAssessment}
            />
          )}
        </div>

        <Modal
          title="Options to continue"
          is_open={is_proceed_options_modal_open}
          close={closeProceedOptionsModal}
        >
          <div className="flex justify-center gap-2 p-4 max-md:flex-wrap ">
            <Link
              className="btn-modal-choice"
              href={`${path_name.replace('edit/settings', 'edit/invitations')}`}
            >
              <div className="flex flex-col justify-between gap-2">
                <div className="flex aspect-square w-14 items-center justify-center rounded-full bg-white">
                  <OpenEmailIcon />
                </div>
                <p className="heading-text text-primary">
                  Invite candidates to assessment
                </p>
                <p className="helper-text text-xs">
                  Invite candidates via email an have them take assessments with
                  ease.
                </p>
              </div>
            </Link>
            <Link
              className="btn-modal-choice"
              href={`/e/assessments-and-interviews/assessments/${assessment.id}`}
            >
              <div className="flex flex-col justify-between gap-2">
                <div className="grid aspect-square w-14 place-items-center rounded-full bg-white">
                  <QuestionPackSuccessIcon />
                </div>
                <p className="heading-text text-primary">
                  Save assessment for later
                </p>
                <p className="helper-text text-xs">
                  Not ready to invite candidates for your assessment ? Save for
                  later or add to your job pipline
                </p>
              </div>
            </Link>
          </div>
        </Modal>

        <Modal
          title="Add team member"
          is_open={is_add_team_members_modal_open}
          close={closeAddTeamMembersModal}
        >
          <form
            className="w-[443px] bg-[#F5F3FF] text-sm"
            onSubmit={e => {
              e.preventDefault();
              closeAddTeamMembersModal();
            }}
          >
            {is_loading_team_members ? (
              <Loader />
            ) : (
              <>
                <div className="space-y-4 p-4">
                  <div className="">
                    <label className="space-y-2">
                      <p className="">
                        {' '}
                        Kindly enter employee email below to add members{' '}
                      </p>
                      <div className="space-y-4 rounded-xl">
                        <input
                          type="text"
                          placeholder="Enter employee email"
                          value={team_member_search_term}
                          onChange={e =>
                            setTeamMemberSearchTerm(e.target.value)
                          }
                          className="input-white"
                        />
                        <ul className="space-y-1">
                          {is_loading_team_members ? (
                            <Loader />
                          ) : filtered_team_members.length ? (
                            filtered_team_members.map((team_member, index) => (
                              <li key={index}>
                                <label className="flex items-center gap-2 rounded-xl border bg-white p-4">
                                  <Checkbox
                                    name="team member"
                                    value={team_member}
                                    checked={selected_team_members.includes(
                                      team_member
                                    )}
                                    onChange={() => {
                                      if (
                                        selected_team_members.includes(
                                          team_member
                                        )
                                      ) {
                                        removeTeamMember(team_member);
                                      } else {
                                        addTeamMember(team_member);
                                      }
                                    }}
                                  />
                                  <p>{team_member.company_email}</p>
                                </label>
                              </li>
                            ))
                          ) : (
                            <div className="text-center text-opacity-60">
                              <p>No team member matches your search</p>
                            </div>
                          )}
                        </ul>
                      </div>
                    </label>
                  </div>
                </div>
                <div className="bg-white p-4">
                  <Button
                    onClick={closeAddTeamMembersModal}
                    type="button"
                    className="btn-primary w-full"
                  >
                    Add team members
                  </Button>
                </div>
              </>
            )}
          </form>
        </Modal>


      </div>
    </div>
  );
}
