"use client";

import { useEffect } from "react";
import { useCreateAssessmentStore } from "@/app/(website-main)/e/assessments-and-interviews/misc/store/createAssessmentStore";

type Props = {
  children: React.ReactNode
}

export default function ManageAssessment({children}:Props){
  const assessment = useCreateAssessmentStore((state) => state);
  useEffect(() => {
    return () => {
      if (!window.location.pathname.split("/").includes("edit")){
        assessment.resetState()
      }
    };
  }, []);
  return children
}
