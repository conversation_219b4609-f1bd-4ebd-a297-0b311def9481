import React from 'react';
import Datetime from 'react-datetime';
import 'react-datetime/css/react-datetime.css';
import TimeInput from '@/app/(website-main)/e/assessments-and-interviews/misc/components/TimeInput';
import {
  PROCTORING_OPTIONS,
  TEAM_MEMBER_ROLES,
  YES_OR_NO,
} from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants';
import { Assessment } from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import ListBox from '@/components/shared/listbox';

type OtherProps = {
  assessment: Assessment;
  updateAssessment: (val: any) => void;
};

const Others = ({ assessment, updateAssessment }: OtherProps) => (
  <form className="space-y-6">
    <div className="space-y-4">
      <div className="space-y-1">
        <h3 className="heading-text">Commencement settings</h3>
        <p className="text-xs">
          Select <span className="font-bold">'YES'</span> if you wish for
          candidates to complete the assessment at their convenience within a
          designated time frame, or choose{' '}
          <span className="font-bold">'NO'</span> if you prefer them to commence
          the assessment at the specific time and date you have specified.
        </p>
      </div>
      <div className="flex gap-2">
        {/* @ts-ignore */}
        <ListBox
          placeholder="Select commencement setting"
          className="border"
          options={YES_OR_NO}
          value_key="value"
          readable_text_key="readable_string"
          active_option={assessment.commencement_settings}
          setActiveOption={(_option: any) => {
            updateAssessment({ commencement_settings: _option });
          }}
        />
      </div>
    </div>
    <div className="space-y-4">
      <div className="space-y-1">
        <h3 className="heading-text">Set assessment time limit</h3>
        <p className="text-xs">
          Once started, candidates are required to submit assessment within this
          time.
        </p>
      </div>

      <TimeInput
        value={assessment.time_limit}
        onChange={val => {
          updateAssessment({ time_limit: val });
        }}
      />
    </div>
    <div className="space-y-4">
      <div className="space-y-1">
        <h3 className="heading-text">Reminder Time</h3>
        <p className="text-xs">
          How many hours from now should the reminder be sent
        </p>
      </div>
      <label className="flex items-center gap-4">
        Hours:
        <input
          className="input-white w-min border"
          type="number"
          required
          name="date"
          value={assessment.reminder_time}
          onChange={event => {
            updateAssessment({ reminder_time: event.target.value });
          }}
        />
      </label>
    </div>
    <div className="space-y-4">
      <div className="space-y-1">
        <h3 className="heading-text">Date and time settings</h3>
        <p className="text-xs">
          Specify the start and end date and time for candidates to start and
          complete this assessment.
        </p>
      </div>
      <label className="flex items-center gap-4">
        Start:
        {/* <Datetime
           
          name="date"
          value={assessment.start_time}
          onChange={event => {
            updateAssessment({ start_time: event.target.value });
          }}
        />{' '} */}
        <input
          className="input-white w-min border"
          type="datetime-local"
          required
          name="date"
          value={assessment.start_time}
          onChange={event => {
            updateAssessment({ start_time: event.target.value });
          }}
        />
      </label>
      {assessment.commencement_settings == 'yes' && (
        <label className="flex items-center gap-4">
          End:
          <input
            className="input-white w-min border"
            type="datetime-local"
            required
            value={assessment.deadline}
            name="date"
            onChange={event => {
              updateAssessment({ deadline: event.target.value });
            }}
          />
        </label>
      )}
    </div>
  </form>
);

export default Others;
