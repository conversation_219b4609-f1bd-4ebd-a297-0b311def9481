'use client'
import { useEffect, useState } from "react"
import { create } from "zustand"
import { usePathname, usePara<PERSON>, useRouter } from "next/navigation"

import DropdownMenu from "@/components/DropdownMenu"
import AddCustomQuestionModal from "@/app/(website-main)/e/assessments-and-interviews/misc/components/AddCustomQuestionModal"
import DeleteQuestionModal from "../../../../misc/components/DeleteQuestionModal"
import DeleteSectionModal from "../../../../misc/components/DeleteSectionModal"
import EditQuestionModal from "../../../../misc/components/EditQuestionModal"
import EditSectionModal from "../../../../misc/components/EditSectionModal"
import QuestionTypeModal from "../../../../misc/components/QuestionTypeModal"
import QuestionsListItem from "../../../../misc/components/QuestionsListItem"
import EllipsesVerticalIcon from "@/components/icons/jsx/EllipsesVerticalIcon"
import TickCircleIcon from "@/components/icons/TickCircleIcon"
import app_fetch from "@/lib/api/appFetch"
import { QUESITON_TYPES, QUESTION_TYPES_DICTIONARY, SECTION_OPTIONS } from "@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants"
import { Assessment, CreateQuestion, Question, Section } from "@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments"
import CreateAssessmentHeader from "../../../../misc/components/CreateAssessmentHeader"

import { deleteQuestionAPI, editQuestionAPI } from "../../../../misc/api/Questions"
import { deleteSectionAPI, updateAssessmentLastEditURL } from "../../../../misc/api/Assessment"
import { handleAddCustomQuestion } from "../functions"
import { editSectionAPI } from "../../../../misc/api/Sections"
import { DragDropContext, Draggable, DropResult, Droppable } from "react-beautiful-dnd"
import { useMutation } from "@tanstack/react-query"
import useStrictDroppable from "../../hooks/useStrictEnabled"
import { useBooleanStateControl } from "@/hooks"
import CreateQuestionSelectTypeModal from "../../../../question-pack/misc/components/CreateQuestionSelectTypeModal"


export default function Dashboard() {
  const params = useParams()

  const [assessment, setAssessment] = useState({} as Assessment)

  const [active_section_index, setActiveSectionIndex] = useState(0);
  const [active_section, setActiveSection] = useState(
    assessment?.sections?.[active_section_index],
  );

  useEffect(() => {
    app_fetch(`assessments/${params.assessment_id}/`)
      .then((res) => res.json())
      .then(res => {
        res.sections.map((section: Section,) => {
          section.question_types = Array.from(new Set([...section.question_set.reduce((acc, curr) => { acc.push(curr.type); return acc }, [] as string[])])) // this could be slow
        })
        res.sections = res.sections.sort((a: Section, b: Section) => a.order! - b.order!)
        setAssessment(res)
        setFilterQuestionType(res.sections[0]?.question_types?.[0])
      })
  }, []);

  useEffect(() => {
    setActiveSection(assessment?.sections?.[active_section_index]);
  }, [active_section_index, assessment, assessment.sections]);

  function viewSection(index: number) {
    setActiveSectionIndex(index);
  }

  const [is_edit_section_modal_open, setIsEditSectionModalOpen] = useState(
    false,
  );
  function closeEditSectionModal() {
    setIsEditSectionModalOpen(false);
  }
  function openEditSectionModal() {
    setIsEditSectionModalOpen(true);
  }
  const {
    state: isCreateQuestionSelectTypeModalOpen,
    setTrue: openCreateQuestionSelectTypeModal,
    setFalse: closeCreateQuestionSelectTypeModal,
  } = useBooleanStateControl()

  function deleteSection(section_index: number) {
    return new Promise((resolve, reject) => {
      const section = assessment.sections[section_index];
      /* send a delete request */
      deleteSectionAPI(section.id, assessment.id)
        /* .then(async (response) => await response.json()) */
        .then((result) => {
          const new_sections = assessment.sections.toSpliced(section_index, 1);

          setAssessment({ ...assessment, sections: new_sections })
          resolve({ message: "success" });
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  type EditSectionDataType = {
    section_name: string;
    test: string;
    no_multiple_choices?: number;
    no_essay?: number;
    no_coding?: number;
  };

  function editSection(section: EditSectionDataType, section_index: number) {
    return new Promise((resolve, reject) => {
      const question_types_counts = {} as Record<string, number>;
      QUESITON_TYPES.map((_question_type) => {
        // @ts-ignore
        question_types_counts[_question_type.count_name] = section[_question_type.count_name] || 0;
      });

      const edited_section = {
        // section: assessment.sections[active_section_index].id,
        // @ts-ignore
        section_name: section.name,
        id: assessment.sections[section_index]?.id,
        ...question_types_counts,
      }

      // @ts-ignore
      editSectionAPI(edited_section, assessment?.id)
        .then((response: any) => response.json())
        .then((res: { section: Section }) => {
          const new_sections = [...assessment.sections];

          res.section.question_types = Array.from(new Set([...res.section.question_set.reduce((acc, curr) => { acc.push(curr.type); return acc }, [] as string[])])) // this could be slow
          new_sections[section_index] = res.section;

          setAssessment({ ...assessment, sections: new_sections })
          resolve({ message: "success" });
        })
        .catch((error: any) => {
          reject(error);
        });
    });
  }

  const [is_editing_section, setIsEditingSection] = useState(false);
  function handleEditSection(section: EditSectionDataType) {
    setIsEditingSection(true);
    editSection(section, active_section_index)
      .finally(() => {
        setIsEditingSection(false);
        closeEditSectionModal();
      });
  }

  const [is_delete_section_modal_open, setIsDeleteSectionModalOpen] = useState(
    false,
  );
  function openDeleteSectionModal() {
    setIsDeleteSectionModalOpen(true);
  }
  function closeDeleteSectionModal() {
    setIsDeleteSectionModalOpen(false);
  }

  function handleSectionOption(_selected_option: typeof SECTION_OPTIONS[0]) {
    if (_selected_option.title == "edit section") {
      openEditSectionModal();
    }
    if (_selected_option.title == "delete section") {
      openDeleteSectionModal();
    }
  }


  const useActiveQuestionState = create((set) => ({
    active_question: {} as Question,
    active_question_index: 0,
    setIndex: (new_index: number) => set((state: any) => ({ ...state, active_question_index: new_index })),
    setQuestion: (new_question: Question) => set((state: any) => ({ ...state, active_question: new_question }))
  }))

  const [question_type, setQuestionType] = useState<string>();
  const [filter_question_type, setFilterQuestionType] = useState(
    active_section?.question_types?.[0]?.toLowerCase(),
  );

  let question_state = useActiveQuestionState() as any

  const [active_question_index, setActiveQuestionIndex] = useState(0);
  const [active_question, setActiveQuestion] = useState(
    assessment?.sections?.[active_section_index]?.question_set?.[
    active_question_index
    ],
  );

  useEffect(() => {
    setActiveQuestion(
      assessment?.sections?.[active_section_index]?.question_set?.[
      active_question_index
      ],
    );
  }, [active_question_index, assessment.sections]);

  const [is_edit_question_modal_open, setIsEditQuestionModalOpen] = useState(
    false,
  );
  function closeEditQuestionModal() {
    setIsEditQuestionModalOpen(false);
  }
  function openEditQuestionModal() {
    setIsEditQuestionModalOpen(true);
  }

  function deleteQuestion(question_index: number, section_index: number) {
    return new Promise((resolve, reject) => {
      const new_sections = assessment.sections;
      const section = assessment.sections[section_index];
      const question = section.question_set[question_index];

      deleteQuestionAPI(question?.id, section?.id)
        .then((res) => res.json())
        .then((_section: { section: Section }) => {
          const section_question_types = [] as any
          Object.entries(QUESTION_TYPES_DICTIONARY).map(([key, value], index) => {
            //@ts-ignore
            const count = _section.section[value.count_name]
            if (count) {
              section_question_types.push(value.value)
            }
          })

          _section.section.question_types = section_question_types

          new_sections[section_index] = _section.section;

          setAssessment({ ...assessment, sections: new_sections });
          resolve({ message: "success" });
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  function editQuestion(edited_question: Question, question_index: number, section_index: number) {
    const section = assessment.sections[section_index];
    const question = section.question_set[question_index];

    return new Promise((resolve, reject) => {
      editQuestionAPI(edited_question, section.id, assessment.id)
        .then(async (response) => await response.json())
        .then((result: { message: string, question: Question }) => {
          const new_sections = assessment.sections;
          const new_question_set = section.question_set;
          new_question_set[question_index] = result.question; // result.question

          new_sections[section_index] = {
            ...section,
            question_set: new_question_set,
          };

          setAssessment({ ...assessment, sections: new_sections });
          resolve({ message: "success" });
        }).catch((error) => {
          reject(error);
        });
    });
  }

  const [is_editing_question, setIsEditingQuestion] = useState(false);
  function handleEditQuestion(edited_question: Question) {
    setIsEditingQuestion(true);
    editQuestion(
      edited_question,
      active_question_index,
      active_section_index,
    )
      .then(() => {
        closeEditQuestionModal()
      })
      .finally(() => {
        setIsEditingQuestion(false);
      });
  }

  const [is_delete_question_modal_open, setIsDeleteQuestionModalOpen] =
    useState(
      false,
    );
  function openDeleteQuestionModal() {
    setIsDeleteQuestionModalOpen(true);
  }
  function closeDeleteQuestionModal() {
    setIsDeleteQuestionModalOpen(false);
  }

  const [is_question_type_modal_open, setIsQuestionTypeModalOpen] = useState(
    false,
  );
  function closeQuestionTypeModal() {
    setIsQuestionTypeModalOpen(false);
  }
  function openQuestionTypeModal() {
    setIsQuestionTypeModalOpen(true);
  }

  const [is_add_custom_question_modal_open, setIsAddCustomQuestionModalOpen] =
    useState(false);
  function closeAddCustomQuestionModal() {
    setIsAddCustomQuestionModalOpen(false);
  }
  function openAddCustomQuestionModal() {
    setIsAddCustomQuestionModalOpen(true);
  }

  function setAssessmentSections(sections: Section[]) {
    setAssessment({ ...assessment, sections })
  }

  const [is_adding_custom_question, setIsAddingCustomQuestion] = useState(
    false,
  );
  function addCustomQuestion(question: CreateQuestion) {
    setIsAddingCustomQuestion(true);
    handleAddCustomQuestion(assessment, setAssessment, question, active_section_index).then(() => {
      setAssessmentSections(assessment.sections);
    }).finally(() => {
      setIsAddingCustomQuestion(false);
    });
  }

  const path_name = usePathname();
  const router = useRouter();
  const [is_proceeding_to_next_step, setIsProceedingToNextStep] = useState(
    false,
  );

  function handleProceed() {
    updateAssessmentLastEditURL(assessment.id, "edit/questions")
    setIsProceedingToNextStep(true);
    router.push(`${path_name.replace("edit/questions", "edit/settings")}`);
  }

  const [enabled] = useStrictDroppable(false)

  function reorderSections(source_index: number, destination_index: number) {
    const _sections = assessment.sections.slice()
    const [removed] = _sections.splice(source_index, 1)
    _sections.splice(destination_index, 0, removed)
    return _sections
  }


  async function changeOrder({ source, destination, draggableId }: Pick<DropResult, "source" | "destination" | "draggableId">) {
    const new_sections = reorderSections(source.index!, destination?.index!)
    return app_fetch(`assessments/${assessment.id}/reorder-sections/`, {
      method: "PUT",
      body: JSON.stringify({
        new_order: new_sections.reduce((acc, curr) => { acc.push(curr.id); return acc }, [] as string[])
      })
    }).then(res => res.json())
  }

  const changeOrderMutation = useMutation({
    mutationFn: changeOrder,
    onMutate: ({ source, destination, draggableId }) => {
      const original_sections = assessment.sections
      const original_active_section_index = active_section_index

      const _sections = reorderSections(source.index!, destination?.index!)


      setAssessment({
        ...assessment,
        sections: _sections
      })

      if (active_section_index === source.index) {
        setActiveSectionIndex(destination?.index!)
      }

      return {
        original_sections, original_active_section_index
      }
    },
    onError: (error, _vars, context) => {
      setActiveSectionIndex(context?.original_active_section_index!)
      setAssessment({ ...assessment, sections: context?.original_sections! })
    }
  })

  function handleChangeOrder({ source, destination, draggableId }: DropResult) {
    changeOrderMutation.mutate({ source, destination, draggableId })
  }

  return (
    <div>
      <CreateAssessmentHeader
        assessment={assessment}
        is_proceeding={is_proceeding_to_next_step}
        back_page_url={`${path_name.replace("edit/questions", "edit")}`}
        current_step={2}
        handleProceed={handleProceed}
      />

      <div className="space-y-4 p-4 pl-0">
        <p className="text-sm">
          You can create your custom questions and set score and test duration
          or select more from out questions library
        </p>
        <div className="grid md:grid-cols-2 items-start gap-4">
          <section className="md:sticky top-32">
            <DragDropContext onDragEnd={handleChangeOrder} >
              {enabled && (
                <Droppable droppableId="sections" type="group">
                  {(provided) => (
                    <ul className="space-y-2" {...provided.droppableProps} ref={provided.innerRef}>
                      {assessment.sections?.map((section, index) => (
                        <div key={section.id} >
                          <Draggable index={index} draggableId={section.id}>
                            {(_provided) => (
                              <li ref={_provided.innerRef} {..._provided.draggableProps} {..._provided.dragHandleProps} >
                                <div
                                  className={`rounded-xl bg-white p-1 text-sm outline-1 outline-primary ${index == active_section_index ? "outline" : ""
                                    }`}
                                >
                                  <div className="bg-light-accent-bg flex items-center justify-between rounded-t-xl p-2 text-primary">
                                    <div className="flex gap-4">
                                      <h2 className="flex items-center gap-2">
                                        Section
                                        <span className="flex aspect-square w-[18px] items-center justify-center rounded-full bg-primary text-white">
                                          {index + 1}
                                        </span>
                                      </h2>
                                      <p>
                                        {section?.section_name}: {section?.question_set.length}
                                      </p>
                                    </div>
                                    <div className="flex items-center gap-4">
                                      <p className="flex items-center gap-2">
                                        <TickCircleIcon /> {section?.total_points || 0} points
                                      </p>
                                      <DropdownMenu
                                        options={SECTION_OPTIONS}
                                        readable_text_key="title"
                                        onOpen={() => {
                                          setActiveSection(section)
                                          setActiveSectionIndex(index)
                                        }}
                                        callback={(_selected_option) => {
                                          handleSectionOption(_selected_option)
                                        }}
                                        button={
                                          <button
                                            type="button"
                                            className="btn-base"
                                            title="view section options"
                                          >
                                            <EllipsesVerticalIcon />
                                          </button>
                                        }
                                      />
                                    </div>
                                  </div>
                                  <div className="flex items-center justify-between p-4 text-xs">
                                    <p className="text-primary">{section.question_types.length} question types</p>
                                    <div className="flex gap-2 items-center">
                                      <button
                                        className="btn-primary-light"
                                        type="button"
                                        onClick={() => {
                                          openQuestionTypeModal();
                                        }}
                                      >
                                        Add custom question
                                      </button>
                                      <button
                                        type="button"
                                        className="btn-primary-light"
                                        onClick={() => {
                                          viewSection(index);
                                        }}
                                      >
                                        View Questions
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </li>

                            )}
                          </Draggable>


                        </div>
                      ))}
                      {provided.placeholder}
                    </ul>
                  )}
                </Droppable>
              )}
            </DragDropContext>
          </section>
          <section>
            <div className="rounded-xl bg-white p-6">
              <div className="section-container text-sm">
                <div className="flex justify-between text-primary">
                  <div className="flex gap-4">
                    <h2 className="flex items-center gap-2">
                      Section
                      <span className="flex aspect-square w-[18px] items-center justify-center rounded-full bg-primary text-white">
                        {active_section_index + 1}
                      </span>
                    </h2>
                    <p>
                      {active_section?.name}:{" "}
                      {active_section?.question_set.length}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <p className="flex items-center gap-2">
                      <TickCircleIcon /> {active_section?.total_points || 0} points
                    </p>
                  </div>
                </div>
                <ul className="flex flex-wrap items-center gap-2 gap-y-1 capitalize">
                  {active_section?.question_types?.map(
                    (question_type, index) => (
                      <li
                        key={index}
                      >
                        <label
                          className={`btn-primary-light rounded-2xl block ${question_type?.toLowerCase() === filter_question_type
                            ? "transition-default outline outline-1 outline-primary "
                            : "cursor-pointer"
                            }`}
                        >
                          <input
                            type="radio"
                            value={question_type?.toLowerCase()}
                            onChange={(e) =>
                              setFilterQuestionType(e.target.value)}
                            name="filter_question_type"
                            className="hidden"
                          />
                          {QUESTION_TYPES_DICTIONARY[question_type]?.name}:{" "}
                          {active_section?.question_set.filter((question) => {
                            return (
                              question.type === question_type?.toLowerCase()
                            );
                          }).length}
                        </label>
                      </li>
                    ),
                  )}
                </ul>
                <p>
                  For each of the following questions, choose the most
                  appropriate answer:
                </p>
                <ul className="section-container">
                  {active_section?.question_set
                    .map((question, question_index) => {
                      if (question.type == filter_question_type) {
                        return (
                          <li key={question?.id}>
                            <QuestionsListItem
                              question={question}
                              index={question_index}
                              assessmentId={assessment.id}
                              sectionId={active_section.id}
                              onEditQuestion={(question: Question) => {
                                const section = assessment.sections[active_section_index];
                                const new_sections = assessment.sections;
                                const new_question_set = section.question_set;
                                new_question_set[question_index] = question;
                                new_sections[active_section_index] = {
                                  ...section,
                                  question_set: new_question_set,
                                };

                                setAssessment({ ...assessment, sections: new_sections });
                              }}

                              deleteQuestion={() => {
                                question_state.setIndex(question_index)
                                question_state.setQuestion(question)
                                setActiveQuestionIndex(question_index)
                                setActiveQuestion(question);
                                openDeleteQuestionModal();
                              }}
                            />
                          </li>
                        )
                      }
                    }

                    )}
                </ul>
                <button
                  className="btn-primary-bordered w-full"
                  type="button"
                  onClick={() => {
                    openQuestionTypeModal();
                  }}
                >
                  Add custom question
                </button>
              </div>
            </div>
          </section>
        </div>
      </div>

      <EditSectionModal
        section={active_section}
        is_busy={is_editing_section}
        handleSubmit={(section) => {
          //@ts-ignore
          handleEditSection(section);
        }}
        close={closeEditSectionModal}
        is_open={is_edit_section_modal_open}
      />

      <QuestionTypeModal
        is_open={is_question_type_modal_open}
        close={closeQuestionTypeModal}
        callback={(question_type) => {
          setQuestionType(question_type);
          closeQuestionTypeModal();
          openAddCustomQuestionModal();
        }}
      />

      <AddCustomQuestionModal
        is_open={is_add_custom_question_modal_open}
        is_busy={is_adding_custom_question}
        close={closeAddCustomQuestionModal}
        question_type={question_type}
        handleSubmit={(question) => {
          addCustomQuestion(question);
        }}
      />

      <DeleteSectionModal
        is_open={is_delete_section_modal_open}
        close={closeDeleteSectionModal}
        section_index={active_section_index}
        onDelete={(section_index) => deleteSection(section_index)}
      />

      <DeleteQuestionModal
        is_open={is_delete_question_modal_open}
        close={closeDeleteQuestionModal}
        section_index={active_section_index}
        /* question_index={question_state.active_question_index} */
        question_index={active_question_index}
        onDelete={(question_index, section_index) => deleteQuestion(question_index, section_index)}
      />
    </div>
  )
}
