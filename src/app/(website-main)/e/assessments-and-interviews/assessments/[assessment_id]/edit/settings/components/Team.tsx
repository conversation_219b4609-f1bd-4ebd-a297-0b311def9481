import React, { Dispatch, SetStateAction } from 'react';
import {
  PROCTORING_OPTIONS,
  TEAM_MEMBER_ROLES,
  YES_OR_NO,
} from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants';
import {
  Assessment,
  ProctoringOptions,
  TeamMember,
  TeamMemberRoleTypes,
} from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import AddTeamMateIcon from '@/components/icons/jsx/AddTeamMateIcon';

type TeamProps = {
  selected_team_members: TeamMember[];
  team_members: TeamMember[];
  openAddTeamMembersModal: () => void;
  setSelectedTeamMembers: Dispatch<SetStateAction<TeamMember[]>>;
  removeTeamMember: (teamMember: TeamMember) => void;
};

const Team = ({
  selected_team_members,
  team_members,
  openAddTeamMembersModal,
  setSelectedTeamMembers,
  removeTeamMember,
}: TeamProps) => (
  <section className="max-w-xl">
    {selected_team_members.length == 0 ? (
      <div className="m-auto flex max-w-xs flex-col items-center justify-center gap-2 py-12 text-center">
        <AddTeamMateIcon />
        <p>Your team members will appear here</p>
        <p className="helper-text text-xs">
          Add team members who can access this assessment
        </p>
        {team_members.length == 0 ? (
          <p className="text-red-400">
            You need to have team members in your account to continue.
          </p>
        ) : (
          <button onClick={openAddTeamMembersModal} className="btn-primary">
            Add team member
          </button>
        )}
      </div>
    ) : (
      <ul className="space-y-2">
        {selected_team_members.map((member, index) => (
          <li
            key={index}
            className="input-base flex items-center justify-between border"
          >
            <p>{member.company_email}</p>
            <div className="flex items-center gap-2">
              <div className="flex gap-2">
                {/* @ts-ignore */}
                <ListBox
                  placeholder="Select Role"
                  className="border"
                  options={TEAM_MEMBER_ROLES}
                  value_key="id"
                  readable_text_key="id"
                  active_option={selected_team_members[index].role}
                  setActiveOption={(_option: TeamMemberRoleTypes) => {
                    const _selected_team_members = selected_team_members;
                    _selected_team_members[index].role = _option;
                    setSelectedTeamMembers(_selected_team_members);
                  }}
                />
              </div>
              <button
                onClick={() => {
                  removeTeamMember(member);
                }}
                className="flex w-6 items-center justify-center rounded-full bg-primary/20 text-xl"
                type="button"
              >
                &times;
              </button>
            </div>
          </li>
        ))}
        <button
          onClick={openAddTeamMembersModal}
          className="btn-primary-bordered w-full border-dashed"
        >
          Add team member
        </button>
      </ul>
    )}
  </section>
);

export default Team;
