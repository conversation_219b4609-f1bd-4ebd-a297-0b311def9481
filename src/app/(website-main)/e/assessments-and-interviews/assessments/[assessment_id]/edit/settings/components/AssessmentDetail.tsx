import { UseMutationResult } from '@tanstack/react-query';
import React, { Dispatch, MutableRefObject, SetStateAction } from 'react';
import { DropzoneInputProps, DropzoneRootProps } from 'react-dropzone';
import UploadIcon from '@/app/(public-links)/jobs/misc/icons/UploadIcon';
import {
  Assessment,
  ProctoringOptions,
  TeamMember,
  TeamMemberRoleTypes,
} from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import { PlayIcon } from '@/app/(website-main)/t/showcase/misc/icons';
import Button from '@/components/shared/Button';
import Switch from '@/components/Switch';

type AssessmentDetailProp = {
  assessment: Assessment;
  updateAssessment: (val: any) => void;
  generateIntroMessageMutation: UseMutationResult<
    string,
    unknown,
    void,
    unknown
  >;
  getInputProps: <T extends DropzoneInputProps>(props?: T) => T;
  getRootProps: <T extends DropzoneRootProps>(props?: T) => T;
  is_playing: boolean;
  video_url: string;
  videoRef: MutableRefObject<HTMLVideoElement | null>;
  setIsPlaying: Dispatch<SetStateAction<boolean>>;
  setCurrentVideoTime: Dispatch<SetStateAction<string>>;
  current_video_time: string;
  togglePlay: () => void;
  clearIntroVideo: () => void;
};

const AssessmentDetail = ({
  assessment,
  is_playing,
  updateAssessment,
  generateIntroMessageMutation,
  getInputProps,
  getRootProps,

  video_url,
  videoRef,
  setIsPlaying,
  setCurrentVideoTime,
  current_video_time,
  togglePlay,
  clearIntroVideo,
}: AssessmentDetailProp) => (
  <form className="space-y-6">
    <div>
      <label className="space-y-2">
        <p className="">Assessment name</p>
        <input
          type="text"
          placeholder="Assessment name"
          className="input-grey"
          value={assessment.name}
          onChange={e => {
            updateAssessment({ name: e.target.value });
          }}
        />
      </label>
    </div>
    <div>
      <div className="space-y-2">
        <div className="flex items-center justify-between gap-4">
          <label htmlFor="assessment_intro">
            <p className="">Assessment Intro message / description</p>
          </label>
          <div>
            <Button
              className="btn-primary-light"
              onClick={() => generateIntroMessageMutation.mutate()}
              is_busy={generateIntroMessageMutation.isLoading}
            >
              Generate intro message
            </Button>
          </div>
        </div>
        <textarea
          required
          id="assessment_intro"
          value={assessment.description}
          onChange={e => {
            updateAssessment({ description: e.target.value });
          }}
          placeholder="Enter details of this assessment to guide candidates"
          className="input-grey min-h-[100px]"
          maxLength={400}
        />
      </div>
      <p className="text-right text-xxs">
        {assessment?.description?.length || 0}/400 characters remaining
      </p>
    </div>

    <div className="space-y-2">
      <label>
        <p>
          Upload intro video <span className="helper-text">(Optional)</span>
        </p>
      </label>
      {!assessment.intro_video && (
        <div
          className={`${
            false ? 'border border-red-600' : ''
          } mt-3 flex h-[5.9375rem] cursor-pointer items-center gap-4 rounded-[0.875rem] border-[0.3px] border-dashed border-[rgb(117,90,226)] p-6 md:max-w-[60%]`}
          {...getRootProps()}
        >
          <div className="">
            <UploadIcon />
          </div>
          <div className="">
            <p className="font-sans text-xs font-semibold text-[#755AE2] sm:text-sm">
              Tap to upload intro video
            </p>
            <span className="font-sans text-xs font-semibold text-[#755AE2] opacity-75">
              Files types: MP4, Max size: 10MB
            </span>
          </div>
          <input hidden {...getInputProps()} />
        </div>
      )}
      {assessment.intro_video && (
        <div className="group/intro-video relative flex items-center justify-center md:w-[40%]">
          {assessment.intro_video && (
            <video
              className="relative max-h-[200px] w-full rounded-xl object-cover"
              src={video_url}
              autoPlay={false}
              ref={videoRef}
              onEnded={() => {
                setIsPlaying(false);
                setCurrentVideoTime('0:00');
              }}
            >
              <span className="absolute left-0 top-0 m-4 rounded-md bg-black/20 p-4">
                {current_video_time}
              </span>
            </video>
          )}

          <div>
            <div className="absolute inset-0 hidden group-hover/intro-video:block">
              {is_playing && (
                <div className="absolute inset-0 z-10 flex items-center justify-center">
                  <button
                    className="flex cursor-pointer flex-col items-center justify-center"
                    onClick={togglePlay}
                  >
                    <p>Pause</p>
                    <PlayIcon />
                  </button>
                </div>
              )}
              <button
                onClick={clearIntroVideo}
                className="absolute right-0 top-0 z-10 m-4"
                type="button"
              >
                <span
                  title="clear video"
                  className="flex aspect-square w-7 items-center justify-center rounded-full bg-white text-lg"
                >
                  &times;
                </span>
              </button>
            </div>

            {!is_playing && (
              <div className="absolute inset-0 flex items-center justify-center">
                <button
                  className="z-10 flex cursor-pointer flex-col items-center justify-center"
                  onClick={togglePlay}
                >
                  <PlayIcon />
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>

    <label className="flex items-start justify-between gap-4 rounded-xl border p-4">
      <input type="radio" name="shuffle sections" className="hidden" />
      <div className="space-y-1">
        <h3 className="heading-text ">Do you want sections shuffling?</h3>
        <p className="max-w-[80%] text-xs">
          If you toggle this on, the candidates would all have their sections
          ordered randomly.
        </p>
      </div>
      <Switch
        checked={assessment.is_shuffle_sections}
        setChecked={state => {
          updateAssessment({ is_shuffle_sections: state });
        }}
      />
    </label>

    <label className="flex items-start justify-between gap-4 rounded-xl border p-4">
      <input type="radio" name="shuffle" className="hidden" />
      <div className="space-y-1">
        <h3 className="heading-text ">Do you want question shuffling?</h3>
        <p className="max-w-[80%] text-xs">
          If you toggle this on, the candidates would all have their questions
          ordered randomly.
        </p>
      </div>
      <Switch
        checked={assessment.is_shuffle}
        setChecked={state => {
          updateAssessment({ is_shuffle: state });
        }}
      />
    </label>
  </form>
);

export default AssessmentDetail;
