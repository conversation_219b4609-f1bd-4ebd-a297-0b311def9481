"use client";

import { usePathname, useRouter, } from "next/navigation";
import { useEffect, useState } from "react";
import AssessmentDetailModal from "../../../misc/components/AssessmentDetailModal";
import { useParams } from "next/navigation";
import CreateAssessmentHeader from "../../../misc/components/CreateAssessmentHeader";
import EditSectionModal from "../../../misc/components/EditSectionModal";
import DropdownMenu from "@/components/DropdownMenu";
import EllipsesVerticalIcon from "@/components/icons/jsx/EllipsesVerticalIcon";
import ListBox from "@/components/shared/listbox";
import Modal from "@/components/Modal";
import {
  QUESITON_TYPES,
  QUESTION_TYPES_DICTIONARY,
  SECTION_OPTIONS,
} from "@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants";
import app_fetch from "@/lib/api/appFetch.js";
import {
  Assessment,
  LibraryAssessment,
  QuestionPack,
  Section,
} from "@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments";
import Button from "@/components/shared/Button";
import DeleteSectionModal from "../../../misc/components/DeleteSectionModal";
import { Checkbox2, Loader } from "@/components/shared";
import QuestionPackDetailModal from "../../../misc/components/QuestionPackDetailModal";
import { addLibrarySetToSection, addQuestionPackToSectionApi, deleteSectionAPI, removeLibrarySetFromSection, removeQuestionPackFromSectionApi, updateAssessmentLastEditURL } from "../../../misc/api/Assessment";
import { editSectionAPI } from "../../../misc/api/Sections";
import CreateNewQuestionPackBtn from "../../../misc/components/CreateNewQuestionPackBtn";
import { useMutation } from "@tanstack/react-query";
import IconElement from "@/components/icons/jsx/IconElement";
import { SearchNormal } from "iconsax-react";
import { useGetQuestionsSections } from "../../../interview-pack/misc/api";

function getQuestionTypes(section: Section) {
  const section_question_types = [] as any
  Object.entries(QUESTION_TYPES_DICTIONARY).map(([key, value], index) => {
    //@ts-ignore
    const count = section[value.count_name]
    if (count) {
      section_question_types.push(value.value)
    }
  })
  return section_question_types
}

export default function Dashboard() {
  const path_name = usePathname();
  const router = useRouter();

  /* get linked library assessments */
  const [recommended_tests, setRecommendedTests] = useState<LibraryAssessment[]>([]);
  const [is_fetching_recommended_tests, setIsFetchingRecommendedTests] = useState(true);

  const [added_recommendations, setAddedRecommendations] = useState({} as Record<string, string | undefined>)

  const [active_library_assessment, setActiveLibraryAssessment] = useState<LibraryAssessment>();

  /* custom question packs */
  const [my_question_packs, setMyQuestionPacks] = useState<QuestionPack[]>([]);

  const [active_question_pack, setActiveQuestionPack] = useState<QuestionPack>();


  const [is_question_pack_detail_modal_open, setIsQuestionPackDetailModalOpen] =
    useState(false);
  function openQuestionPackDetailModal() {
    setIsQuestionPackDetailModalOpen(true);
  }
  function closeQuestionPackDetailModal() {
    setIsQuestionPackDetailModalOpen(false);
  }

  const [is_fetching_my_qustion_packs, setIsFetchingMyQuestionPacks] = useState(false);
  const [used_questions, setUsedQuestions] = useState<Record<string, string>>({}) //_used_questions.question = section_id || undefined 

  const [assessment, setAssessment] = useState({} as Assessment)
  const params = useParams()

  useEffect(() => {
    app_fetch(`assessments/${params.assessment_id}/`)
      .then((res) => res.json())
      .then(res => {
        const _assessment = res as typeof assessment

        const _added_recommendations = {} as typeof added_recommendations

        const _used_questions = {} as typeof used_questions;

        _assessment.sections.map(section => {

          section.question_set.map(question => {
            _used_questions[question.id] = section.id
          })


          section.question_types = getQuestionTypes(section)
          section.test.map(test => {
            //@ts-ignore
            _added_recommendations[test.id] = section.id
          })
          section.question_pack.map(_question_pack => {
            //@ts-ignore
            _added_recommendations[_question_pack.id] = section.id
          })
        })
        setUsedQuestions(_used_questions)
        setAssessment(_assessment)

        setAddedRecommendations(_added_recommendations)

        if (!_assessment.is_custom) {
          if (_assessment.role?.id && _assessment.role_level) {
            const raw = JSON.stringify({
              role_id: _assessment.role.id,
              role_level: _assessment.role_level,
            });

            const requestOptions = {
              method: "POST",
              body: raw,
            };

            setIsFetchingRecommendedTests(true);
            app_fetch("assessments/test-recommendations/", requestOptions)
              .then((response) => response.json())
              .then((result) => {
                setRecommendedTests(result);
              })
              .catch((error) => console.log("error", error))
              .finally(() => {
                setIsFetchingRecommendedTests(false);
              });
          }
        } else {
          app_fetch("assessments/question-packs/")
            .then((response) => response.json())
            .then((result) => {
              setMyQuestionPacks(result);
            })
            .finally(() => {
              setIsFetchingMyQuestionPacks(false);
            });
        }
      })
  }, [params.assessment_id]);


  const [active_section_index, setActiveSectionIndex] = useState(0);
  const [active_section, setActiveSection] = useState<Section>();

  useEffect(() => {
    setActiveSection(assessment?.sections?.[active_section_index]);
  }, [active_section_index, assessment?.sections]);


  const [is_edit_section_modal_open, setIsEditSectionModalOpen] = useState(
    false,
  );
  function closeEditSectionModal() {
    setIsEditSectionModalOpen(false);
  }
  function openEditSectionModal() {
    setIsEditSectionModalOpen(true);
  }

  const [is_editing_section, setIsEditingSection] = useState(false);
  function editSection(section: Section, section_index: number) {
    return new Promise((resolve, reject) => {
      const question_types_counts = {} as Record<string, number>;
      QUESITON_TYPES.map((_question_type) => {
        // @ts-ignore
        question_types_counts[_question_type.count_name] = section[_question_type.count_name] || 0;
      });

      const edited_section = {
        // section: assessment.sections[active_section_index].id,
        // @ts-ignore
        section_name: section.section_name,
        ...question_types_counts,
        id: assessment.sections[section_index]?.id,
      }
      // @ts-ignore
      editSectionAPI(edited_section, assessment.id)
        .then((response) => response.json())
        .then((response: { assessment: Assessment, message: string, section: Section }) => {

          response.section.question_types = getQuestionTypes(response.section)

          const new_sections = assessment.sections
          new_sections[section_index] = response.section

          setAssessment({ ...assessment, sections: new_sections })
          resolve({ message: "success" });

        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  function handleEditSection(section: Section) {
    setIsEditingSection(true);
    editSection(section, active_section_index)
      .finally(() => {
        setIsEditingSection(false);
        closeEditSectionModal();
      });
  }

  const [is_delete_section_modal_open, setIsDeleteSectionModalOpen] = useState(
    false,
  );
  function openDeleteSectionModal() {
    setIsDeleteSectionModalOpen(true);
  }
  function closeDeleteSectionModal() {
    setIsDeleteSectionModalOpen(false);
  }

  function deleteSection(section_index: number) {
    const section = assessment.sections[section_index];
    return new Promise((resolve, reject) => {
      /* send a delete request */
      deleteSectionAPI(section.id, assessment.id)
        /* .then(async (response) => await response.json()) */
        .then((result: any) => {
          /* if success, delete the section entry from the assessment */
          /* clear added library sets */
          const cleared_tests = {} as Record<string, any>;
          // @ts-ignore
          section.test.reduce((acc, curr) => { acc.push(curr.id); return acc }, []).map((test_id) => { cleared_tests[test_id] = undefined; });
          setAddedRecommendations({ ...added_recommendations, ...cleared_tests })

          const _used_questions = used_questions
          Object.entries(used_questions).map(([question, section_id], index) => {
            if (section_id == section.id) {
              delete _used_questions[question]
            }
          })
          setUsedQuestions(_used_questions)

          const _sections = assessment.sections
          _sections.splice(section_index, 1)

          setAssessment({ ...assessment, sections: _sections })
          resolve({ message: "success" });
        })
        // @ts-ignore
        .catch((error) => {
          reject(error);
        });
    });


  }

  function handleSectionOption(_selected_option: typeof SECTION_OPTIONS[0]) {
    if (_selected_option.title == "edit section") {
      openEditSectionModal();
    }
    if (_selected_option.title == "delete section") {
      openDeleteSectionModal();
    }
  }


  const [is_section_name_modal_open, setIsSectionNameModalOpen] = useState(false)
  const [section_name, setSectionName] = useState("")

  function openSectionNameModal() {
    setIsSectionNameModalOpen(true)
  }
  function closeSectionNameModal() {
    setIsSectionNameModalOpen(false)
    setSectionName("")
  }


  const [selected_questions, setSelectedQuestions] = useState<string[]>([])
  const [is_select_questions_modal_open, setIsSelectQuestionsModalOpen] = useState(false)
  function openSelectQuestionsModal() {
    setSelectedQuestions([])
    setIsSelectQuestionsModalOpen(true)
  }
  function closeSelectQuestionsModal() {
    setIsSelectQuestionsModalOpen(false)
  }

  async function createAssessmentSectionAPI(section_name: string) {
    return await app_fetch(`assessments/assessment-section/${params.assessment_id}/`, {
      method: "POST",
      body: JSON.stringify({
        section_name
      })
    }).then(res => res.json())
  }

  const createAssessmentSectionMutation = useMutation({
    mutationFn: createAssessmentSectionAPI,
    onSuccess: async (result: { message: string, assessment: Assessment, section: Section }) => {
      let new_sections = assessment.sections
      new_sections[active_section_index] = result.section
      setAssessment({ ...assessment, sections: new_sections })

      if (selected_questions) {
        await addQuestionsToSectionHelperFn().then(() => {
          closeSectionNameModal()
        })
      }
      else {
        closeSectionNameModal()
      }
    }
  })

  async function createAssessmentSection(name: string) {
    createAssessmentSectionMutation.mutate(name)
  }

  async function addQuestionsToSectionAPI({ section_id, question_ids }: { section_id: string, question_ids: string[] }) {
    return await app_fetch(`assessments/${params.assessment_id}/sections/${section_id}/add_question/`, {
      method: "POST",
      body: JSON.stringify({
        questions: question_ids
      })
    }).then(res => res.json())
  }

  async function addQuestionsToSectionHelperFn() {

    const is_section_empty = assessment?.sections?.[active_section_index] === undefined

    if (is_section_empty) {
      openSectionNameModal()
    }

    else {
      addQuestionsToSectionMutation.mutate({ section_id: assessment?.sections?.[active_section_index]?.id, question_ids: selected_questions })
    }

  }

  const addQuestionsToSectionMutation = useMutation({
    mutationFn: addQuestionsToSectionAPI,
    onSuccess: (result: { message: string, section: Section }, variables) => {
      const _used_questions = used_questions
      variables.question_ids.map(question_id => {
        _used_questions[question_id] = variables.section_id;
      })
      setUsedQuestions(_used_questions)

      let new_sections = assessment.sections
      result.section.question_types = getQuestionTypes(result.section)
      new_sections[active_section_index] = result.section
      setAssessment({ ...assessment, sections: new_sections })

      closeSelectQuestionsModal()
    }
  })

  function addSelectedQuestions() {
    addQuestionsToSectionHelperFn()
  }

  function addAllQuestions() {
    if (active_question_pack) {
      setSelectedQuestions(active_question_pack?.question_set.reduce((acc, curr) => { acc.push(curr.id); return acc }, [] as string[]))
      addQuestionsToSectionHelperFn();
    }
  }

  async function removeQuestionFromSectionAPI({ question_id, section_id }: { question_id: string, section_id: string }) {
    return await app_fetch(`assessments/assessment-sections/${section_id}/remove-question/${question_id}/`, { method: "DELETE" }).then(res => res.json())
  }

  const removeQuestionFromSectionMutation = useMutation({
    mutationFn: removeQuestionFromSectionAPI,
    onMutate: (variables) => {
      setUsedQuestions({ ...used_questions, [variables.question_id]: '' })
    },
    onSuccess: (result: { message: string, section: Section }, variables) => {
      const _used_questions = used_questions
      delete _used_questions[variables.question_id]
      setUsedQuestions({ ..._used_questions })

      let new_sections = assessment.sections
      result.section.question_types = getQuestionTypes(result.section)
      new_sections[active_section_index] = result.section
      setAssessment({ ...assessment, sections: new_sections })
    },
    onError: (error, variables) => {
      setUsedQuestions({ ...used_questions, [variables.question_id]: variables.section_id })
    }
  })

  function removeUsedQuestion(question_id: string, section_id: string) {
    removeQuestionFromSectionMutation.mutate({ question_id, section_id })
  }

  const [is_adding_library_set_to_section, setIsAddingLibrarySetToSection] = useState({} as Record<string, boolean>);

  function addAssessmentQuestionsToSection(_assessment: LibraryAssessment) {
    closeSectionNameModal()
    setSectionName("")
    setIsAddingLibrarySetToSection({
      ...is_adding_library_set_to_section,
      [_assessment.id]: true,
    });

    let new_sections = assessment.sections
    const is_section_empty = assessment?.sections?.[active_section_index] === undefined

    addLibrarySetToSection(
      assessment,
      _assessment,
      active_section_index,
      is_section_empty
    ).then((res: any) => res.json()).then((result: { message: string, section: Section }) => {
      result.section.question_types = getQuestionTypes(result.section)
      new_sections[active_section_index] = result.section
      setAddedRecommendations({ ...added_recommendations, [_assessment.id]: assessment?.sections?.[active_section_index].id })
      setAssessment({ ...assessment, sections: new_sections })
    })
      .finally(() => {
        setIsAddingLibrarySetToSection({
          ...is_adding_library_set_to_section,
          [_assessment.id]: false,
        });
      });
  }

  const [is_removing_library_set_from_section, setIsRemovingLibrarySetFromSection,] = useState({} as Record<string, boolean>);
  function removeAssessmentQuestionsFromSection(_assessment: LibraryAssessment,) {
    setIsRemovingLibrarySetFromSection({
      ...is_removing_library_set_from_section,
      [_assessment.id]: true,
    });

    const assessment_section_index = added_recommendations[_assessment.id]

    // @ts-ignore
    const active_section_index = assessment.sections.reduce((acc, curr) => { acc.push(curr.id); return acc }, []).indexOf(assessment_section_index)

    // @ts-ignore
    removeLibrarySetFromSection(assessment, _assessment, active_section_index)
      // @ts-ignore
      .then((response) => response.json())
      .then((result: { message: string, section: Section }) => {
        setAddedRecommendations({ ...added_recommendations, [_assessment.id]: undefined })

        const new_sections = assessment.sections;

        result.section.question_types = getQuestionTypes(result.section)
        new_sections[active_section_index] = result.section

        /* new_sections[active_section_index] = { */
        /*   ...result.section, */
        /*   name: result.section.section_name || "", */
        /* }; */

        // make section null if it is now empty after removing questions
        /* if (new_sections[active_section_index].question_set.length === 0) { */
        /*   new_sections[active_section_index] = undefined; */
        /* } */

        setAssessment({ ...assessment, sections: new_sections })
      })
      .catch((error: any) => {
      })
      .finally(() => {
        setIsRemovingLibrarySetFromSection({
          ...is_removing_library_set_from_section,
          [_assessment.id]: false,
        });
      });
  }

  const [is_question_detail_modal_open, setIsQuestionDetailModalOpen] =
    useState(false);
  function closeQuestionDetailModal() {
    setIsQuestionDetailModalOpen(false);
  }
  function openQuestionDetailModal() {
    setIsQuestionDetailModalOpen(true);
  }


  // filters
  const [assessment_type_filter, setAssessmentTypeFilter] = useState<string | undefined>();
  const [search_term, setSearchTerm] = useState<string | undefined>(undefined);
  const [my_filtered_question_packs, setMyFilteredQuestionPacks] = useState<QuestionPack[]>([]);
  const [filtered_recommended_tests, setFilteredRecommendedTests] = useState<LibraryAssessment[]>([]);

  function clearFilters() {
    setAssessmentTypeFilter(undefined);
    setSearchTerm(undefined);
  }

  useEffect(() => {
    /* get linked library */
    setFilteredRecommendedTests(
      recommended_tests.filter((test) => {
        let should_show = true;

        if (assessment_type_filter) {
          const is_assessment_type_match = assessment_type_filter.toLowerCase()
            .includes(test.label?.toLowerCase());
          should_show = should_show && is_assessment_type_match;
        }

        if (search_term) {
          const is_name_match = test.name.toLowerCase().includes(
            search_term.toLowerCase(),
          );
          const is_description_match = test.description.toLowerCase().includes(
            search_term.toLowerCase(),
          );

          should_show = should_show && (is_name_match || is_description_match);
        }

        return should_show;
      }),
    );

    setMyFilteredQuestionPacks(
      my_question_packs.filter((test) => {
        let should_show = true;

        // do not show empty question sets
        if (test.question_set.length == 0) {
          should_show = false
        }

        if (assessment_type_filter) {
          if (test.label) {
            const is_assessment_type_match = assessment_type_filter.toLowerCase().includes(test.label.toLowerCase());
            should_show = should_show && is_assessment_type_match;
          }
        }

        if (search_term) {
          const is_name_match = test.name.toLowerCase().includes(
            search_term.toLowerCase(),
          );
          const is_description_match = test.description?.toLowerCase().includes(
            search_term.toLowerCase(),
          );

          should_show = should_show && (is_name_match || is_description_match);
        }

        return should_show;
      }),
    );
  }, [
    search_term,
    assessment_type_filter,
    recommended_tests,
    my_question_packs,
  ]);

  function truncateToLength(str: string, len: number) {
    if (str.length > len) {
      return str.slice(0, len) + "...";
    } else {
      return str;
    }
  }


  function addQuestionPackToSection(_assessment: QuestionPack) {
    closeSectionNameModal()
    setSectionName("")
    setIsAddingLibrarySetToSection({
      ...is_adding_library_set_to_section,
      [_assessment.id]: true,
    });

    let new_sections = assessment.sections
    const is_section_empty = assessment?.sections?.[active_section_index]?.section_name === undefined

    addQuestionPackToSectionApi(
      assessment,
      _assessment,
      active_section_index,
      is_section_empty
    ).then((res: any) => res.json()).then((result: { message: string, section: Section }) => {

      result.section.question_types = getQuestionTypes(result.section)
      new_sections[active_section_index] = result.section

      setAddedRecommendations({ ...added_recommendations, [_assessment.id]: assessment?.sections?.[active_section_index].id })
      setAssessment({ ...assessment, sections: new_sections })
    })
      .finally(() => {
        setIsAddingLibrarySetToSection({
          ...is_adding_library_set_to_section,
          [_assessment.id]: false,
        });
      });
  }


  function removeQuestionPackFromSection(_question_pack: QuestionPack,) {

    const assessment_section_index = added_recommendations[_question_pack.id]

    // @ts-ignore
    const active_section_index = assessment.sections.reduce((acc, curr) => { acc.push(curr.id); return acc }, []).indexOf(assessment_section_index)

    setIsRemovingLibrarySetFromSection({
      ...is_removing_library_set_from_section,
      [_question_pack.id]: true,
    });

    // @ts-ignore
    removeQuestionPackFromSectionApi(assessment, _question_pack, active_section_index)
      // @ts-ignore
      .then((response) => response.json())
      .then((result: { message: string, section: Section }) => {
        setAddedRecommendations({ ...added_recommendations, [_question_pack.id]: undefined })
        const new_sections = assessment.sections;

        result.section.question_types = getQuestionTypes(result.section)
        new_sections[active_section_index] = result.section

        // make section null if it is now empty after removing questions
        /* if (new_sections[active_section_index].question_set.length === 0) { */
        /*   new_sections[active_section_index] = undefined; */
        /* } */
        setAssessment({ ...assessment, sections: new_sections })
      })
      .catch((error: any) => {
      })
      .finally(() => {
        setIsRemovingLibrarySetFromSection({
          ...is_removing_library_set_from_section,
          [_question_pack.id]: false,
        });
      });

  }

  const [is_proceeding_to_next_step, setIsProceedingToNextStep] = useState(
    false,
  );
  function handleProceed() {
    /* validate proceed */
    if (assessment.sections.filter(section => section !== undefined).length) {
      setIsProceedingToNextStep(true);
      updateAssessmentLastEditURL(assessment.id, "edit")
      router.push(`${path_name.replace("edit", "edit/questions")}`);
    }
    else {
      alert("You need to add a section to continue")
    }

  }

  const { data: questionSections, isLoading: isGettingSections } =
    useGetQuestionsSections();
  const ASSESSMENT_TYPES = questionSections?.results?.map(section => ({ id: section.id, name: section.name })) || []

  

  return (
    <div>

      <CreateAssessmentHeader
        assessment={assessment}
        back_page_url={`/e/assessments-and-interviews/assessments/${params.assessment_id}`}
        handleProceed={handleProceed}
        is_proceeding={is_proceeding_to_next_step}
      />

      <div className="space-y-4 py-4">
        <section className="section-container">
          <h2 className="heading-2">Question Sections</h2>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
            {[...Array(5)].map((_, index) => (
              <div
                key={index}
                role="button"
                className={`flex min-h-[150px] items-center justify-center rounded-xl bg-white p-1 ${active_section_index == index ? "outline !outline-[1px] outline-primary focus:outline focus:outline-primary" : ""}`}
                onClick={() => {
                  setActiveSectionIndex(index);
                }}
              >
                {
                  // if section is not undefined
                  assessment?.sections?.[index]
                    ? (
                      <div className="bg-light-accent-bg h-full w-full rounded-xl">
                        <div className="flex items-center justify-between rounded-xl bg-[#ECE9FF] p-2 text-left capitalize text-primary">
                          <h2>
                            {assessment.sections[index].section_name}:{" "}
                            <span className="font-bold">
                              {assessment.sections[index].number_of_questions ||
                                assessment.sections[index]?.question_set
                                  ?.length ||
                                0}
                            </span>
                          </h2>
                          <div className="flex items-center gap-2">
                            <Button onClick={() => handleSectionOption(SECTION_OPTIONS[1])} className="btn-icon-primary" title="delete section" >
                              <p className="sr-only">Delete section</p>
                              <IconElement icon="close-square" />
                            </Button>


                            <DropdownMenu
                              options={SECTION_OPTIONS}
                              readable_text_key="title"
                              onOpen={() => {
                                setActiveSectionIndex(index)
                              }}
                              callback={(_selected_option) => {
                                handleSectionOption(_selected_option)
                              }
                              }
                              button={
                                <div className="btn-icon-primary" title="view section options" > <EllipsesVerticalIcon /> </div>
                              }
                            />
                          </div>
                        </div>
                        <ul className="space-y-4 p-4 text-left capitalize text-primary">
                          {assessment.sections[index]?.question_types
                            ?.slice(0, 2)
                            .map((_question_type, _question_type_index) => (
                              <li
                                key={_question_type_index}
                                className="flex items-center justify-between gap-4"
                              >
                                <div className="flex flex-1 items-center justify-between gap-2">
                                  <p>
                                    {QUESTION_TYPES_DICTIONARY[_question_type]
                                      ?.name || _question_type}
                                    :{" "}
                                    <span className="font-bold">
                                      {assessment?.sections[
                                        index
                                      ]?.question_set?.filter((question) => {
                                        return question.type ===
                                          _question_type;
                                      }).length}
                                    </span>
                                  </p>
                                </div>
                                <button
                                  type="button"
                                  className="btn-icon-primary"
                                  onClick={openEditSectionModal}
                                  title="edit section"
                                >
                                  <p className="sr-only"> Edit section </p>
                                  <IconElement icon="edit-square" />
                                </button>
                              </li>
                            ))}
                        </ul>
                      </div>
                    )
                    : (
                      <div className="p-3">
                        <p className="btn-primary-light flex-1">
                          Section {index + 1}
                        </p>
                      </div>
                    )
                }
              </div>
            ))}
          </div>
        </section>
        <section>
          <div className="flex max-md:flex-wrap gap-2">
            <ListBox
              options={ASSESSMENT_TYPES}
              className="border"
              placeholder="Assessment type"
              // @ts-ignore
              active_option={assessment_type_filter}
              setActiveOption={(_assessment_type) => {
                setAssessmentTypeFilter(_assessment_type);
              }}
              value_key="name"
              readable_text_key="name"
            />
            <div className="btn-bordered flex items-center gap-2 py-0">
              <SearchNormal />
              <input
                type="text"
                name="search assessments"
                placeholder="Search"
                className="input-base"
                value={search_term}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            {(search_term || assessment_type_filter) && (
              <button className="btn-bordered" onClick={clearFilters}>
                Clear filters
              </button>
            )}
          </div>
        </section>

        {/* library questions */}
        {!assessment.is_custom && (
          <>
            <section className="section-container">
              <p className="md:max-w-[60vw] text-sm">
                These assessments evaluate a candidate's specific job-related
                skills, technical knowledge, and competencies required for the
                role. They focus on the candidate's ability to perform the tasks
                associated with the job role.
              </p>
            </section>

            <section>
              {is_fetching_recommended_tests
                ? (<Loader />)
                : filtered_recommended_tests.length === 0
                  ? (
                    <div className="flex min-h-[600px] items-center justify-center">
                      <div className="rounded-md bg-gradient-to-b from-[#755ae20a] via-transparent to-transparent p-12 border border-[#DADADA]/20 text-center">
                        <div className="container max-w-xs space-y-2">
                          <div className="container flex justify-center">
                            <img
                              src="/images/create-assessments/no-assessment.png"
                              alt="writing with pen"
                            />
                          </div>
                          {recommended_tests.length == 0 ? (
                            <>
                              <h2>There are currently no recommended tests available for the selected options</h2>
                              <p className="text-sm text-[#7D8590]">You could try again later.</p>
                            </>
                          ) : (
                            <>
                              <h2>No recommended pack matches your filters</h2>
                              <p className="text-sm text-[#7D8590]">Clear the filters to see more results.</p>
                              <button
                                onClick={clearFilters}
                                className="btn-primary inline-flex gap-2"
                                type="button"
                              >
                                Clear filters
                              </button>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                  : (
                    <>
                      <div className="grid md:grid-cols-4 gap-4">
                        {filtered_recommended_tests.map((_assessment, index) => (
                          <div
                            key={index}
                            className={`space-y-2 rounded-md transition ease-in-out overflow-hidden ${added_recommendations[_assessment.id] ? "border-l-8 border-[#755AE280]" : ""} bg-white p-4`}
                          >
                            <h2 className="heading-2">{_assessment.name}</h2>
                            <p
                              className="text-xs max-h-[4lh] overflow-hidden"
                              style={{
                                maskImage: "linear-gradient(#fff, transparent)",
                              }}
                            >
                              {_assessment.description}
                            </p>
                            <p className="heading-text text-sm">
                              <span className="helper-text font-normal">
                                Total questions:
                              </span>{" "}
                              {_assessment.question_set?.length}
                            </p>
                            <ul className="flex items-center gap-2 text-xs">
                              {_assessment.tags?.slice(0, 3).map((tag) => (
                                <li className="btn-primary-light-pill" key={tag} title={tag}>
                                  <span className="max-w-[5ch] text-ellipsis overflow-hidden whitespace-nowrap">
                                    {truncateToLength(tag, 8)}
                                  </span>
                                </li>
                              ))}
                            </ul>
                            <div className="flex justify-end gap-2 text-xs">
                              <button
                                className="btn-primary-light heading-text font-normal"
                                type="button"
                                onClick={() => {
                                  setActiveLibraryAssessment(_assessment);
                                  openQuestionDetailModal();
                                }}
                              >
                                Details
                              </button>
                              {added_recommendations[_assessment.id] && (
                                <Button
                                  is_busy={is_removing_library_set_from_section[
                                    _assessment.id
                                  ]}
                                  className="btn-primary-light heading-text font-normal"
                                  onClick={() =>
                                    removeAssessmentQuestionsFromSection(
                                      _assessment,
                                    )}
                                >
                                  Remove
                                </Button>
                              )}

                              <Button
                                type="button"
                                is_busy={is_adding_library_set_to_section[
                                  _assessment.id
                                ]}
                                className="btn-primary"
                                onClick={() => {

                                  setActiveQuestionPack(_assessment);
                                  openSelectQuestionsModal()
                                  return

                                }
                                }
                              >
                                Add
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </>
                  )}
            </section>
          </>
        )}

        {/* custom questions */}
        {assessment.is_custom && (
          <>
            <h2 className="heading-text">Your created question packs</h2>
            <section>
              {is_fetching_my_qustion_packs
                ? (<Loader />)
                : my_filtered_question_packs.length === 0
                  ? (
                    <div className="flex min-h-[600px] items-center justify-center">
                      <div className="rounded-md bg-gradient-to-b from-[#755ae20a] via-transparent to-transparent p-12 border border-[#DADADA]/20 text-center">
                        <div className="container max-w-xs space-y-2">
                          <div className="container flex justify-center">
                            <img
                              src="/images/create-assessments/no-assessment.png"
                              alt="writing with pen"
                            />
                          </div>
                          {my_question_packs.length == 0 ? (
                            <>
                              <h2>You do not have any question packs</h2>
                              <p className="text-sm text-[#7D8590]">You can create one with the button</p>
                              <CreateNewQuestionPackBtn redirect_link={path_name} />
                            </>
                          ) : (
                            <>
                              <h2>No recommended pack matches your filters</h2>
                              <p className="text-sm text-[#7D8590]">Clear the filters to see more results.</p>
                              <button
                                onClick={clearFilters}
                                className="btn-primary inline-flex gap-2"
                                type="button"
                              >
                                Clear filters
                              </button>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                  : (
                    <ul className="grid items-stretch md:grid-cols-[repeat(auto-fit,minmax(280px,1fr))] gap-4">
                      {[...my_filtered_question_packs].reverse().map((
                        question_pack,
                        index,
                      ) => (
                        <li key={index}>
                          <div className="space-y-2 relative rounded-md bg-white p-4 overflow-hidden">
                            <h2 className="text-lg font-semibold text-header-text truncate mt-2">{question_pack.name}</h2>
                            <span className="absolute rounded-none btn-primary-light text-xs py-1 px-4 -top-2 right-0">
                              My pack
                            </span>
                            <p className="text-xs">{question_pack.description}</p>
                            <p className="heading-text">
                              <span className="helper-text font-normal">
                                Total questions
                              </span>{" "}
                              {question_pack.total_questions}
                            </p>
                            <ul className="flex items-center gap-2 text-xs">
                              {question_pack.tags?.map(
                                (item) => (
                                  <li className="btn-primary-light-pill" key={item}>
                                    <span className="max-w-[5ch] text-ellipsis overflow-hidden whitespace-nowrap">
                                      {truncateToLength(item, 8)}
                                    </span>
                                  </li>
                                ),
                              )}

                            </ul>
                            <div className="flex gap-2 justify-end text-xs">

                              <QuestionPackDetailModal
                                close={closeQuestionPackDetailModal}
                                is_open={is_question_pack_detail_modal_open}
                                questionPack={active_question_pack}
                                setCurrentQuestionPack={() => setActiveQuestionPack(question_pack)}
                                openDrawer={openQuestionPackDetailModal}
                              />

                              {added_recommendations[question_pack.id]
                                ? (
                                  <Button
                                    is_busy={is_removing_library_set_from_section[
                                      question_pack.id
                                    ]}
                                    className="btn-primary-light heading-text font-normal"
                                    onClick={() =>
                                      removeQuestionPackFromSection(
                                        question_pack,
                                      )}
                                  >
                                    Remove
                                  </Button>
                                )
                                : (
                                  <Button
                                    type="button"
                                    is_busy={is_adding_library_set_to_section[question_pack.id]}
                                    className="btn-primary"
                                    onClick={() => {
                                      setActiveQuestionPack(question_pack);
                                      openSelectQuestionsModal()
                                      return
                                      if (assessment.sections?.[active_section_index]) {
                                        addQuestionPackToSection(question_pack)
                                      }
                                      else {
                                        setActiveQuestionPack(question_pack);
                                        openSectionNameModal()
                                      }
                                    }}
                                  >
                                    Add
                                  </Button>
                                )}
                            </div>
                          </div>
                        </li>
                      ))}

                      <li>
                        <CreateNewQuestionPackBtn redirect_link={path_name} variant="card" />
                      </li>
                    </ul>
                  )}
            </section>
          </>
        )}
      </div>

      <Modal is_open={is_section_name_modal_open} title={`Name section ${active_section_index + 1}`} close={closeSectionNameModal}>
        <form onSubmit={(e) => { e.preventDefault(); createAssessmentSection(section_name) }}
          className="bg-light-accent-bg space-y-4 md:w-[440px] p-4">
          <div>
            <label className="space-y-1">
              <p>Section name</p>
              <input
                type="text"
                placeholder="Enter section name e.g Skill section"
                value={section_name}
                className="input-white"
                onChange={(event) => {
                  setSectionName(event.target.value)
                }}
              />
            </label>
          </div>
          <Button type="submit" className="btn-primary w-full"
            is_busy={createAssessmentSectionMutation.isLoading || addQuestionsToSectionMutation.isLoading}
          >
            Save
          </Button>
        </form>
      </Modal>

      <AssessmentDetailModal
        close={closeQuestionDetailModal}
        is_open={is_question_detail_modal_open}
        is_adding={active_library_assessment ? is_adding_library_set_to_section[active_library_assessment.id] : false}
        is_removing={active_library_assessment ? is_removing_library_set_from_section[active_library_assessment.id] : false}
        assessment={active_library_assessment}
        //@ts-ignore
        is_added={active_library_assessment ? added_recommendations[active_library_assessment.id] : false}
        handleAdd={() => {
          if (assessment.sections[active_section_index]) {
            if (active_library_assessment) addAssessmentQuestionsToSection(active_library_assessment)
          }
          else {
            setActiveLibraryAssessment(active_library_assessment);
            closeQuestionDetailModal()
            openSectionNameModal()
          }
        }
        }
        handleRemove={() => {
          if (active_library_assessment)
            removeAssessmentQuestionsFromSection(active_library_assessment)
        }}
      />

      <DeleteSectionModal
        is_open={is_delete_section_modal_open}
        close={closeDeleteSectionModal}
        section_index={active_section_index}
        onDelete={deleteSection}
      />

      {active_section && (
        <EditSectionModal
          section={active_section}
          is_busy={is_editing_section}
          handleSubmit={(section) => { handleEditSection(section); }}
          close={closeEditSectionModal}
          is_open={is_edit_section_modal_open}
        />
      )}
      <Modal
        should_hide_overflow={true}
        is_open={is_select_questions_modal_open}
        close={closeSelectQuestionsModal}
        title="Questions"
        portalClass="w-full md:w-[80%] max-w-[750px]"
      >
        <div className="p-4 pb-0 space-y-4 w-full max-h-[80vh] overflow-y-auto">
          <div className="bg-grey rounded-xl p-2">
            <h2 className="heading-2">{active_question_pack?.name}</h2>
            <p className="helper-text">{active_question_pack?.description}</p>
          </div>
          <div className="pb-8">
            <ul className="space-y-2">
              {active_question_pack?.question_set.map((question, index) => (
                <li key={index}>
                  {
                    used_questions[question.id] !== undefined ? (
                      <div className="p-4 rounded-md border block" >
                        <div className="flex justify-between items-center">
                          <h2 className="text-lg text-header-text font-medium"> Question {index + 1} </h2>
                          <Button className="text-xs btn-danger-bordered" onClick={() => removeUsedQuestion(question.id, used_questions[question.id])} is_busy={used_questions[question.id] == ''}>
                            remove
                          </Button >
                        </div>

                        <div
                          className="!text-body-text"
                          dangerouslySetInnerHTML={{ __html: question.question }}
                        />
                      </div>
                    ) : (
                      <label className="p-4 rounded-md border block" >
                        <div className="flex justify-between items-center">
                          <h2 className="text-lg"> Question {index + 1} </h2>

                          <Checkbox2
                            checked={selected_questions.includes(question.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedQuestions([...selected_questions, question.id])
                              }
                              else {

                                setSelectedQuestions(selected_questions.toSpliced(selected_questions.indexOf(question.id), 1))
                              }
                            }}
                            name="select question"
                            value={question.id}
                          />                        

                        </div>
                          <div
                            className="!text-body-text"
                            dangerouslySetInnerHTML={{ __html: question.question }}
                          />
                      </label>
                    )
                  }
                </li>
              ))}
            </ul>
          </div>
          <div className="sticky shadow-[rgba(17,_17,_26,_0.1)_0px_-12px_12px] bottom-0 mt-1 bg-white p-4 border-t transition-all -mx-4">
            <div className="flex items-center gap-2">
              <Button is_busy={addQuestionsToSectionMutation.isLoading} type="button" disabled={!active_question_pack?.question_set.length} onClick={addAllQuestions} className="btn-primary-bordered w-full">
                Add all ({active_question_pack?.question_set.length}) question(s)
              </Button>
              <Button disabled={!selected_questions.length} onClick={addSelectedQuestions} is_busy={addQuestionsToSectionMutation.isLoading} className="btn-primary w-full">
                Add ({selected_questions.length}) selected question(s)
              </Button>
            </div>
          </div>
        </div>
      </Modal>
    </div>

  );
}
