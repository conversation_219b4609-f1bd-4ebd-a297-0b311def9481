"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { useParams, usePathname, useRouter } from "next/navigation";

import DocumentDownloadDarkIcon from "@/components/icons/DocumentDownloadDarkIcon";
import AssessmentSuccessModalIcon from "@/components/icons/AssessmentSuccessModalIcon";
import Modal from "@/components/Modal";
import { useCreateAssessmentStore } from "@/app/(website-main)/e/assessments-and-interviews/misc/store/createAssessmentStore";
import { secondsToLargestUnit } from "@/lib/utils/functions";
import { ROLE_LEVELS_DICTIONARY } from "@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants";
import Button from "@/components/shared/Button";
import CustomInvitationModal from "../../../../misc/components/CustomInvitationModal";
import app_fetch from "@/lib/api/appFetch";
import { Assessment, Section } from "@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments";
import InviteCandidatesModal from "../../../../misc/components/InviteCandidatesModal";

import { publishAssessment } from "../../../../misc/api/Assessment";


export default function Dashboard() {
  const params = useParams()
  const router = useRouter()

  const [assessment, setAssessment] = useState({} as Assessment)
  const assessment_store = useCreateAssessmentStore((state) => state);
  const [questions_count, setQuestionsCount] = useState(0);
  const setAssessmentID = useCreateAssessmentStore((state) => state.setId);

  useEffect(() => {
    app_fetch(`assessments/${params.assessment_id}/`)
      .then((res) => res.json())
      .then(res => {
        res.start_time = res.start_time?.split("+")[0]
        res.deadline = res.deadline?.split("+")[0]

        // if loaded successfully and there is no start time or there is no deadline (when commencement is set to yes)
        if ((res.id) && (!res.start_time || (res.commencement_settings && !res.deadline))) {
          alert("This assessment's time settings are wrongly configured. Please fix to continue.")
          router.push(`/e/assessments-and-interviews/assessments/${params.assessment_id}/edit/settings`)
          return
        }

        setAssessmentID(res.id);
        if (res.commencement_settings) { res.commencement_settings = "yes" }
        else { res.commencement_settings = "no" }

        let count = 0;
        res.sections.forEach((section: Section) => {
          count += section?.question_set?.length || 0;
        });
        setQuestionsCount(count);

        setAssessment(res)
      })
  }, []);


  const [is_success_modal_open, setIsSuccessModalOpen] = useState(false);
  function openSuccessModal() {
    setIsSuccessModalOpen(true);
  }

  const [is_invitees_modal_open, setIsInviteesModalOpen] = useState(false);
  function openInviteesModal() {
    setIsInviteesModalOpen(true);
  }
  function closeInviteesModal() {
    setIsInviteesModalOpen(false);
  }

  const [is_invite_candidates_modal_open, setIsInviteCandidatesModalOpen] = useState(false);
  function openInviteCandidatesModal() {
    setIsInviteCandidatesModalOpen(true)
  }
  function closeInviteCandidatesModal() {
    setIsInviteCandidatesModalOpen(false)
  }

  const [is_sending_invites, setIsSendingInvites] = useState(false);
  function submitAssessment() {
    if (assessment_store.invitations.filter(invitation => invitation.name !== "").length === 0) {
      openSuccessModal();
      return
    }

    setIsSendingInvites(true);
    switch (assessment_store.choosen_invitation_type) {
      case "file":
        assessment_store.sendBulkInvitation()
          .then(() => {
            publishAssessment(assessment.id)
            openSuccessModal();
          })
          .catch(() => {
            return;
          })
          .finally(() => {
            publishAssessment(assessment.id)
            setIsSendingInvites(false);
          });
        break;
      case "email":
        assessment_store.sendEmailInvitations()
          .then(() => {
            publishAssessment(assessment.id)
            openSuccessModal();
          })
          .catch(() => {
            return;
          })
          .finally(() => {
            setIsSendingInvites(false);
          });
        break;
      case "pool":
        break;
    }

  }

  const [is_custom_invitation_modal_open, setIsCustomInvitationModalOpen] = useState(false);

  function openCustomInvitationModal() {
    setIsCustomInvitationModalOpen(true);
  }

  function closeCustomInvitationModal() {
    setIsCustomInvitationModalOpen(false);
  }

  function updateCustomInvitation(form_data: any) {
    assessment_store.setCustomEmailMessage(form_data.message);
    assessment_store.setCustomEmailSubject(form_data.subject);
    assessment_store.setCustomEmailCoverImage(form_data.cover_image);
  }

  const path_name = usePathname();

  return (
    <div className="flex max-md:flex-col gap-4 pb-4 pr-4">
      <p>
        <Link
          href={`${path_name.replace("edit/summary", "edit/invitations")}`}
          className="btn-primary-light-pill bg-white"
        >
          Back
        </Link>
      </p>
      <div className="flex-1">
        <div className="mx-auto max-w-3xl">
          <div className="space-y-8 rounded-t-xl bg-white p-4 px-12">
            <section className="space-y-4">
              <h1 className="heading-1">Assessment summary</h1>
              <div className="grid md:grid-cols-3 gap-2">
                <div>
                  <p className="heading-2 capitalize">{assessment.name}</p>
                  <p className="helper-text text-sm">Name of assessment</p>
                </div>
                <div>
                  <p className="heading-2 capitalize">
                    {ROLE_LEVELS_DICTIONARY[assessment.role_level]?.name}
                  </p>
                  <p className="helper-text text-sm">Role level</p>
                </div>
                <div>
                  <p className="heading-2">
                    {`${secondsToLargestUnit(assessment?.time_limit).time} ${secondsToLargestUnit(assessment?.time_limit).unit}`}
                  </p>
                  <p className="helper-text text-sm">Assessment time</p>
                </div>
                <div>
                  <p className="heading-2">
                    {assessment_store.invitations.filter(
                      (invitation) => (invitation.email !== ""),
                    ).length}
                  </p>
                  <p className="helper-text text-sm">No of invites</p>
                </div>
                <div>
                  <p className="heading-2">{questions_count}</p>
                  <p className="helper-text text-sm">No of Questions</p>
                </div>
              </div>
            </section>

            <section className="space-y-4">
              <h2 className="heading-2">Question section:</h2>
              <ul className="space-y-1">
                {assessment?.sections?.filter((section) => section?.name !== undefined)
                  .map((section, index) => (
                    <li
                      key={index}
                      className="btn-base bg-grey heading-text flex items-center justify-between py-4 font-normal"
                    >
                      <div className="flex items-center gap-2">
                        <span className="flex h-[22px] w-[22px] items-center justify-center rounded-full bg-[#0E0E2C] text-white">
                          {index + 1}
                        </span>
                        <p className="">{section.name}</p>
                      </div>
                      <div className="flex items-center gap-2 text-xs">
                        <DocumentDownloadDarkIcon />
                        <p>{section.total_points || 0} points</p>
                      </div>
                    </li>
                  ))}
              </ul>
            </section>

            <section className="space-y-4">
              <h2 className="heading-2">
                <span className="helper-text text-sm font-normal">
                  No of invites:
                </span>{" "}
                {assessment_store.invitations.filter(
                  (invitation) => (invitation.email !== ""),
                ).length}
              </h2>
              <ul className="space-y-1">
                {assessment_store.invitations
                  .slice(0, 10)
                  .filter((invitaion) => invitaion.name !== "")
                  .map((invitation, index) => (
                    <li key={index} className="relative flex items-center justify-between">
                      <button
                        onClick={() => { assessment_store.deleteInvitee(invitation.email) }}
                        className="absolute peer right-0 m-4"
                        type="button"
                      >
                        <span
                          title="delete invitee"
                          className="bg-white rounded-full w-7 aspect-square flex items-center justify-center text-lg"
                        >
                          &times;
                        </span>
                      </button>
                      <div className="peer-hover:bg-red-100 btn-base bg-grey flex-1 heading-text grid grid-cols-2 items-center py-4 font-normal">
                        <p>
                          <span className="helper-text">Name:</span>{" "}
                          {invitation.name}
                        </p>
                        <p>
                          <span className="helper-text">Email:</span>{" "}
                          {invitation.email}
                        </p>
                      </div>
                    </li>
                  ))}
              </ul>
              <div className="flex gap-2">
                <button
                  className="btn-primary-light"
                  type="button"
                  onClick={openInviteesModal}
                >
                  View all
                </button>
                <Link
                  className="btn-primary-light"
                  href={`${path_name.replace("edit/summary", "edit/invitations")}`}
                >
                  + Invite more candidates
                </Link>
              </div>
            </section>
          </div>
          <div className="flex items-center justify-end gap-4 rounded-b-xl bg-white p-4 shadow-[-20px_-20px_40px_0px_#7D85900D]">
            <button
              className="btn-primary-light px-20"
              type="button"
              onClick={openCustomInvitationModal}
            >
              {assessment_store.custom_email_message ? "Edit" : "Customize"} invitation email
            </button>
            <Button
              className="btn-primary"
              type="button"
              onClick={submitAssessment}
              is_busy={is_sending_invites}
            >
              Publish Assessment
            </Button>
          </div>
        </div>
      </div>
      <Modal
        is_open={is_invitees_modal_open}
        title="Invitees"
        close={closeInviteesModal}
      >
        <div className="min-w-[628px] p-4">
          <ul className="space-y-1">
            {assessment_store.invitations
              .filter((invitaion) => invitaion.name !== "")
              .map((invitation, index) => (
                <li key={index} className="relative flex items-center justify-between">
                  <button
                    onClick={() => { assessment_store.deleteInvitee(invitation.email) }}
                    className="absolute peer right-0 m-4"
                    type="button"
                  >
                    <span
                      title="delete invitee"
                      className="bg-white rounded-full w-7 aspect-square flex items-center justify-center text-lg"
                    >
                      &times;
                    </span>
                  </button>
                  <div className="peer-hover:bg-red-100 btn-base bg-grey flex-1 heading-text grid grid-cols-2 items-center py-4 font-normal">
                    <p>
                      <span className="helper-text">Name:</span>{" "}
                      {invitation.name}
                    </p>
                    <p>
                      <span className="helper-text">Email:</span>{" "}
                      {invitation.email}
                    </p>
                  </div>

                </li>
              ))}
          </ul>
        </div>
      </Modal>
      <Modal
        is_open={is_success_modal_open}
        title="Success"
        close={() => { }}
        is_close_by_button_only={true}
        hide_close_btn={true}
      >
        <div className="md:w-[547px] space-y-2 bg-[#F5F3FF] text-center p-4 text-sm text-[#675E8B]">
          <div className="flex flex-col gap-2 items-center py-4 px-12">
            <AssessmentSuccessModalIcon />
            <h2 className="heading-2 text-primary">
              Assessment has been created successfully
            </h2>
            <p>
              Your '{assessment.name}' assessment has been successfully created,
              and invitations have been sent to {assessment_store.invitations.filter(invitation => invitation.name).length}
              {" "}
              candidates.
            </p>
          </div>
        </div>
        <div className="rounded-xl bg-white p-4">
          <div className="flex items-center justify-end gap-2">
            <Link
              className="btn-primary"
              href={`/e/assessments-and-interviews/assessments/${assessment.id}`}
            >
              View assessment details
            </Link>
          </div>
        </div>
      </Modal>

      <CustomInvitationModal initial_data={{ message: assessment_store.custom_email_message || "", subject: assessment_store.custom_email_subject || "", cover_image: assessment_store.cover_image }} is_open={is_custom_invitation_modal_open} close={closeCustomInvitationModal} handleSubmit={(form_data) => { updateCustomInvitation(form_data); closeCustomInvitationModal() }} />

      <InviteCandidatesModal
        assessment_id={assessment.id}
        invitation_type={assessment_store.choosen_invitation_type}
        is_open={is_invite_candidates_modal_open}
        close={closeInviteCandidatesModal}
      />
    </div>
  );
}
