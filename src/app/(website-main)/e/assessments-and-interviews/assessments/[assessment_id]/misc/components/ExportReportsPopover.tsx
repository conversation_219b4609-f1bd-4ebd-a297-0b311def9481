import React, { useRef, useState } from 'react';
import { Button, Input } from '@/components/shared';
import { Checkbox2 } from '@/components/shared/';
import {
    Popover,
    PopoverContent,
    PopoverTrigger
} from '@/components/shared';
import { getAccessToken } from '@/utils';
import toast from 'react-hot-toast';

interface ExportReportsPopoverProps {
    assessment_id: string;
}

const ExportReportsPopover: React.FC<ExportReportsPopoverProps> = ({ assessment_id }) => {
    const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
    const [isExporting, setIsExporting] = useState(false);
    const [email, SetEmail] = useState('')
    const possibleStatuses = [
        { label: "All", value: 'all' },
        { label: "Completed", value: "completed" },
        { label: "In Progress", value: "ongoing" },
        { label: "Not Started", value: "not_started" }
    ];

    const handleExport = async () => {
        if (!email || email.trim() === '') {
            toast.error('Please enter an email address.');
            return;
        }
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            toast.error('Please enter a valid email address.');
            return;
        }
        try {
            setIsExporting(true);

            const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/assessments/export-assessment-report/${assessment_id}/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${getAccessToken()}`
                },
                body: JSON.stringify({
                    get_questions: "false",
                    statuses: selectedStatuses.filter(status => status !== 'all'),
                    email
                })
            });
            toast.success("Export successfully, Check your mail for the report")


            // if (!response.ok) throw new Error('Export failed');

            // // Get the filename from the header if available
            // const contentDisposition = response.headers.get('content-disposition');
            // let filename = 'assessment-report.xlsx';
            // if (contentDisposition) {
            //     const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
            //     if (filenameMatch && filenameMatch[1]) {
            //         filename = filenameMatch[1].replace(/['"]/g, '');
            //     }
            // }

            // // Get the blob directly from the response
            // const blob = await response.blob();

            // // Create download link
            // const url = window.URL.createObjectURL(blob);
            // const link = document.createElement('a');
            // link.href = url;
            // link.setAttribute('download', filename);
            // document.body.appendChild(link);
            // link.click();
            // link.remove();
            // window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Export failed:', error);
            toast.error("Export failed")
            // You might want to show an error message to the user here
        } finally {
            setIsExporting(false);
        }
    };

    const handleStatusChange = (checked: boolean, value: string) => {
        if (checked) {
            if (value === 'all') {
                setSelectedStatuses(['all']);
            } else {
                const newStatuses = selectedStatuses.filter(s => s !== 'all');
                setSelectedStatuses([...newStatuses, value]);
            }
        } else {
            setSelectedStatuses(selectedStatuses.filter(s => s !== value));
        }
    };

    return (
        <Popover>
            <PopoverTrigger asChild>
                <Button variant="outlined" className="btn-bordered">
                    Export
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-60 min-h-max max-h-[400px] overflow-auto p-4">
                <div className="flex flex-col gap-4">
                    <section className="flex flex-col gap-2.5">
                        {possibleStatuses.map((status, index) => (
                            <div key={status.value} className="flex items-center space-x-2">
                                <Checkbox2
                                    id={`${status.label}-${status.value}-${index}`}
                                    checked={selectedStatuses.includes(status.value)}
                                    onCheckedChange={(checked: boolean) => handleStatusChange(checked, status.value)}
                                />
                                <label
                                    htmlFor={status.value}
                                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                >
                                    {status.label}
                                </label>
                            </div>
                        ))}
                    </section>
                    <Input
                        value={email}
                        onChange={(e) => SetEmail(e.target.value)}
                        placeholder="Enter email"
                        type="email"
                        label="Email"
                        variant="dark_showcase"
                        className="w-full mt-2"
                    />
                </div>
                <Button
                    onClick={handleExport}
                    className="w-full mt-6"
                    disabled={selectedStatuses.length === 0 || isExporting}
                >
                    {isExporting ? 'Exporting...' : 'Export'}
                </Button>
            </PopoverContent>
        </Popover>
    );
};

export default ExportReportsPopover;