"use client";

import Link from "next/link";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { secondsToLargestUnit } from "@/lib/utils/functions";
import DropdownMenu from "@/components/DropdownMenu";
import EllipsesVerticalIcon from "@/components/icons/jsx/EllipsesVerticalIcon";
import {
    CANDIDATES_ASSESSMENT_STATUSES,
    PROCTORING_OPTIONS,
    QUESITON_TYPES,
    ROLE_LEVELS_DICTIONARY,
    SECTION_OPTIONS,
    YES_OR_NO,
} from "@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants";
import { Dispatch, FormEvent, SetStateAction, useEffect, useState } from "react";
import app_fetch from "@/lib/api/appFetch.js";
import Button from "@/components/shared/Button";
import SearchIcon from "@/components/icons/jsx/SearchIcon";
import { AssessmentObject, AssessmentResult, CandidateAssessmentStatus, ProctoringOptions, Question, Section, FetchCandidateResult } from "@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments";
import Loader from "@/components/shared/loader";
import InviteCandidatesModal from "../../misc/components/InviteCandidatesModal";
import Disclosure from "@/components/Disclosure";
import DocumentDownloadIcon from "@/components/icons/jsx/DocumentDownloadIcon";
import CaretDownIcon from "@/app/(website-main)/t/dashboard/misc/icons/CaretDown";
import DeleteSectionModal from "../../misc/components/DeleteSectionModal";
import EditSectionModal from "../../misc/components/EditSectionModal";
import QuestionsListItem from "../../misc/components/QuestionsListItem";
import EditQuestionModal from "../../misc/components/EditQuestionModal";
import DeleteQuestionModal from "../../misc/components/DeleteQuestionModal";
import { deleteQuestionAPI, editQuestionAPI } from "../../misc/api/Questions";
import { changeOrder, deleteSectionAPI, editSectionAPI } from "../../misc/api/Sections";
import ListBox from "@/components/shared/listbox";
import Switch from "@/components/Switch";
import { editAssessmentAPI } from "../../misc/api/Assessment";
import { downloadBlob } from "@/lib/utils/functions";
import { useMutation, useQuery } from "@tanstack/react-query";
import { fetchRoleAPI } from "../../misc/api/Roles";
import AssessmentCandidatesList from "./components/AssessmentCandidatesList";
import { useDebouncedCallback } from 'use-debounce'
import TimeInput from "../../misc/components/TimeInput";
import { DragDropContext, Draggable, DropResult, Droppable } from "react-beautiful-dnd";
import useStrictDroppable from "./hooks/useStrictEnabled";
import reorderSections from "../../misc/functions/reorderSections";



type OldDraggableSectionsProps = {
    assessment: AssessmentResult & AssessmentObject & { is_started: boolean };
    setAssessment: Dispatch<SetStateAction<AssessmentResult & AssessmentObject & { is_started: boolean }>>;
    setActiveSectionIndex: Dispatch<SetStateAction<number>>;
    handleSectionOption: (_selected_option: any) => void;
    setActiveQuestionIndex: Dispatch<SetStateAction<number>>;
    openDeleteQuestionModal: () => void;
    openEditQuestionModal: () => void;
}
export function OldDraggableSection({
    assessment,
    setAssessment,
    setActiveSectionIndex,
    handleSectionOption,
    setActiveQuestionIndex,
    openDeleteQuestionModal,
    openEditQuestionModal,

}: OldDraggableSectionsProps) {
    const [enabled] = useStrictDroppable(false)


    const changeOrderMutation = useMutation({
        mutationFn: changeOrder,
        onMutate: ({ source_index, destination_index }) => {
            const original_sections = assessment.sections
            /* const original_active_section_index = active_section_index */
            const _sections = reorderSections({ source_index, destination_index, assessment })
            setAssessment({
                ...assessment,
                sections: _sections
            })
            return {
                original_sections, //original_active_section_index
            }
        },
        onError: (error, _vars, context) => {
            /* setActiveSectionIndex(context?.original_active_section_index!) */
            setAssessment({ ...assessment, sections: context?.original_sections! })
        }
    })

    function handleChangeOrder({ source, destination, draggableId }: DropResult) {
        changeOrderMutation.mutate({ source_index: source.index!, destination_index: destination?.index!, assessment })
    }

    return (
        <>

            {assessment.sections && (
                <DragDropContext onDragEnd={handleChangeOrder} >
                    {enabled && (
                        <div>
                            <Droppable droppableId="sections" type="group">
                                {(provided) => (

                                    <ul className="space-y-1" ref={provided.innerRef} {...provided.droppableProps}>
                                        {assessment.sections
                                            .map((section, index) => (
                                                <Draggable
                                                    key={index}
                                                    index={index}
                                                    draggableId={String(index)}
                                                >
                                                    {(_provided) => (
                                                        <li
                                                            {..._provided.draggableProps}
                                                            ref={_provided.innerRef}
                                                        >
                                                            <Disclosure
                                                                title={
                                                                    <div className="flex items-center justify-between py-0">
                                                                        <div className="flex items-center gap-2">
                                                                            <span className="flex aspect-square text-sm w-[20px] items-center justify-center rounded-full bg-primary text-white">
                                                                                {index + 1}
                                                                            </span>
                                                                            <p className="">{section.section_name || "untitled section"}</p>
                                                                        </div>
                                                                        <div className="flex items-center gap-4">
                                                                            <div className="flex items-center gap-2 text-xs">
                                                                                <DocumentDownloadIcon /> {section.total_points || 0} points
                                                                            </div>
                                                                            {/* <span> */}
                                                                            {/*   <CaretDownIcon /> */}
                                                                            {/* </span> */}
                                                                            <span
                                                                                {..._provided.dragHandleProps}
                                                                            >
                                                                                <CaretDownIcon />
                                                                            </span>

                                                                            <DropdownMenu
                                                                                options={SECTION_OPTIONS}
                                                                                readable_text_key="title"
                                                                                callback={(_selected_option) => {
                                                                                    setActiveSectionIndex(index);
                                                                                    handleSectionOption(_selected_option);
                                                                                }}
                                                                                button={
                                                                                    <button
                                                                                        type="button"
                                                                                        className="btn-base"
                                                                                        title="view section options"
                                                                                    >
                                                                                        <EllipsesVerticalIcon />
                                                                                    </button>
                                                                                }
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                }
                                                            >
                                                                <ul className="space-y-1">
                                                                    {section?.question_set.map(
                                                                        (question, question_index) => (
                                                                            <li key={question_index}>
                                                                                <QuestionsListItem
                                                                                    question={question}
                                                                                    assessmentId={assessment.id}
                                                                                    sectionId={section.id}
                                                                                    deleteQuestion={() => {


                                                                                        setActiveSectionIndex(index);
                                                                                        setActiveQuestionIndex(question_index);
                                                                                        openDeleteQuestionModal();
                                                                                    }}
                                                                                    index={question_index}
                                                                                    onEditQuestion={(question: Question) => {
                                                                                        const section = assessment.sections[index];
                                                                                        const new_sections = assessment.sections;
                                                                                        const new_question_set = section.question_set;
                                                                                        new_question_set[question_index] = question;
                                                                                        new_sections[index] = {
                                                                                            ...section,
                                                                                            question_set: new_question_set,
                                                                                        };

                                                                                        setAssessment({ ...assessment, sections: new_sections });
                                                                                    }}
                                                                                />
                                                                            </li>
                                                                        ),
                                                                    )}
                                                                </ul>
                                                            </Disclosure>
                                                        </li>
                                                    )}
                                                </Draggable>

                                            ))}

                                        {provided.placeholder}
                                    </ul>
                                )}
                            </Droppable>
                        </div>
                    )}
                </DragDropContext>
            )}

        </>
    )
}
