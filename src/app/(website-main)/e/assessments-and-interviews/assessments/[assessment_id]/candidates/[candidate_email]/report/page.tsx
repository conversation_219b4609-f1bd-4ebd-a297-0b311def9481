"use client";

import { useEffect, useState } from "react";

import "@smastrom/react-rating/style.css";
import Loader from "@/components/shared/loader";
import { getCandidateResultQuery } from "../../../../../misc/queries/assessments";
import Report from "./components/Report";
import { ProctoringFlag } from "@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments";


export default function Dashboard(
  { params }: { params: { assessment_id: string; candidate_email: string } },
) {
  const [CANDIDATE_EMAIL, setCandidateEmail] = useState("");

  useEffect(() => {
    setCandidateEmail(params.candidate_email.replace("%40", "@"));
  }, [params.candidate_email]);


  const { data: candidate, isSuccess: is_loading_candidate_success, isLoading: is_loading_candidate, isError: is_loading_candidate_error, error: load_candidate_error } = getCandidateResultQuery({ candidate_email: CANDIDATE_EMAIL, assessment_id: params.assessment_id })

  const [flag_dict, setFlagDict] = useState({} as Record<string, ProctoringFlag[]>);
  useEffect(() => {
    if (candidate) {
      const _flags_dict = {} as Record<string, ProctoringFlag[]>
      candidate.proctoring_report?.flags.map((flag: ProctoringFlag) => {
        if (!_flags_dict[flag.flag]) {
          _flags_dict[flag.flag] = []
        }
        _flags_dict[flag.flag].push(flag)
      })
      setFlagDict(_flags_dict)
    }
  }, [candidate])




  if (is_loading_candidate) {
    return <Loader />
  }
  if (is_loading_candidate_error) {
    return <p>error loading candidate</p>
  }

  return (
    <Report candidate={candidate} flag_dict={flag_dict} />
  );
}
