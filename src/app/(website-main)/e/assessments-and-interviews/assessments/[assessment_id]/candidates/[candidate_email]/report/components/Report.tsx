'use client'

import { QUESTION_TYPES_DICTIONARY } from "@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants";
import { CandidateResult, ProctoringFlag } from "@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments";
import { downLoadPdf } from "@/app/(website-main)/t/showcase/misc/utils/downLoadPdf";
import FlagIcon from "@/components/icons/jsx/FlagIcon";
import app_fetch from "@/lib/api/appFetch";
import { drawDOM, exportPDF } from "@progress/kendo-drawing";
import { useEffect } from "react";


type Props = {
  candidate: CandidateResult;
  flag_dict: Record<string, ProctoringFlag[]>;
}

function getTimeDifference(start: string | Date, end: string | Date) {
  start = new Date(start)
  end = new Date(end)
  /* const diff = new Date(new Date(start).getTime() - new Date(end).getTime()) */
  /* return diff */
  /* let start, end */
  // Handle cases where times are on opposite sides of midnight
  if (end < start) {
    end.setDate(end.getDate() + 1);
  }

  // Calculate the difference in milliseconds
  // @ts-ignore
  let diff = end - start;

  // Convert milliseconds to hours, minutes, and seconds
  let ms = diff % 1000;
  let ss = Math.floor(diff / 1000) % 60;
  let mm = Math.floor(diff / 1000 / 60) % 60;
  let hh = Math.floor(diff / 1000 / 60 / 60);

  return {
    ms, ss, mm, hh
  }
}

export default function Report({ candidate, flag_dict }: Props) {
  const CANDIDATE_EMAIL = candidate.candidate_email

  useEffect(() => {
    drawDOM(document.getElementById("pdf-div")!, {
      paperSize: "A4"
    }).then((group) => {
      return exportPDF(group)
    }).then((datauri) => {

      app_fetch(`assessments/send-result/`, {
        method: "POST",
        body: JSON.stringify({
          candidate_email: candidate.candidate_email,
          candidate_name: candidate.result.candidate_name,
          assessment_id: candidate.assessment.id,
          base64_pdf: datauri.split(";base64,")[1]
        })

      }).then(() => {
        const element = document.createElement("div")
        element.setAttribute("id", "result-sent")
        document.body.appendChild(element)
      })

    })
  }, [])

  return (
    <div id="pdf-div">
      <section className="h-[842px] px-12 p-4 flex bg-gradient-to-b from-[#F0EDFF] to-[#C59EDE]">
        <div className="p-12 w-3/4 bg-[#250A35;] text-grey/60 flex flex-col justify-between">
          <div>
            <div className="text-white">
              <h1 className="font-clash text-[2rem] font-black 2xl:text-4xl">Getlinked</h1>
              <p className="font-light text-sm">by Whispaconnect</p>
            </div>
          </div>

          <div className="text-sm space-y-3">
            <p>Assessment report for:</p>
            <p className="text-[2rem] text-white font-clash capitalize">{candidate?.assessment?.name}</p>
            <p>Date: {new Date(candidate?.result?.time_started).toLocaleString()}</p>
          </div>

          <div className="text-sm">
            <p>
              Copyright {new Date().getFullYear()} Getlinked.
            </p>
            <p>
              All rights reserved.
            </p>
          </div>
        </div>
      </section>

      <section id="report" className="py-4 space-y-6 bg-white text-sm">
        <section className="flex items-start gap-4 p-4 px-12 bg-grey">
          <div className="flex flex-1 items-center gap-4">
            <div className="bg-light-accent-bg relative z-0 aspect-square w-24 flex items-center justify-center rounded-full bg-primary-light">
              {candidate.proctoring_report?.profile_photo ? (
                <img src={candidate?.proctoring_report?.profile_photo} className="w-24  aspect-square rounded-full" />
              ) : (
                <h2 className="text-2xl font-bold text-primary"> {candidate.result?.candidate_name?.split(" ").slice(0, 2).map(name => (name.slice(0, 1))) || "-"} </h2>
              )}
              <span className="absolute right-0 top-0 shadow-md flex aspect-square w-8 items-center justify-center rounded-full bg-primary text-xs text-white">
                {candidate.result?.status == "completed" ? Math.round(candidate.result?.overall_percentage) : "--"}%
              </span>
            </div>
            <div className="flex-1 space-y-2">
              <div className="">

                <div className="flex items-start gap-4">
                  <h1 className="heading-2">
                    {candidate.result?.candidate_name}
                  </h1>
                  <span className="rounded-full p-2 bg-primary/20 text-xs text-primary capitalize">
                    {candidate.result?.status}
                  </span>
                </div>
                <div className="text-black/60">
                  <p className="pr-2">{CANDIDATE_EMAIL}</p>
                  <div className="flex divide-x">
                    <p className="pr-2">Phone number</p>
                    <p className="pl-2">
                      {candidate.proctoring_report?.location}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section className="space-y-2 text-black/50">
          <header className="p-4 px-12 bg-grey heading-text">
            Candidate Overall
          </header>

          <ul className="p-4 px-12 bg-grey grid grid-cols-5 divide-x text-xs">
            <li className="">
              <h3 className="heading-text text-base">{candidate?.result?.total_questions}</h3>
              <p>No of questions</p>
            </li>
            <li className="pl-4">
              <h3> <span className="heading-text text-base">{candidate?.result?.total_questions_answered}</span> <span className="text-green-500">/ {candidate?.result?.total_questions}</span></h3>
              <p>Questions Answered</p>
            </li>
            {/* <li className="pl-4"> */}
            {/*   <h3> <span className="heading-text text-base">-</span> <span className="text-green-500">/ {candidate?.result?.total_questions}</span></h3> */}
            {/*   <p>Correct Answers</p> */}
            {/* </li> */}
            <li className="pl-4">
              <h3 className="heading-text text-base">
                {`${getTimeDifference(candidate?.result?.time_started, candidate?.result?.time_ended).hh}hr ${getTimeDifference(candidate?.result?.time_started, candidate?.result?.time_ended).mm}min(s)`}
              </h3>
              <p>Time Taken</p>
            </li>
            <li className="pl-4">
              <h3 className="heading-text text-base">{candidate?.proctoring_report?.flags.length || "0"}</h3>
              <p>Flags recorded</p>
            </li>
          </ul>
          <div className="py-6 px-12 space-y-2">
            <h2 className="heading-text">Assessment submission</h2>
            <ul className="grid grid-cols-4 divide-x">
              <li className="">
                <h3 className="">Start time</h3>
                <p className="font-bold text-black">{new Date(candidate?.result?.time_started).toLocaleString()}</p>
              </li>
              <li className="pl-4">
                <h3 className="">End time</h3>
                <p className="font-bold text-black">{new Date(candidate?.result?.time_ended).toLocaleString()}</p>
              </li>
              <li className="pl-4">
                <h3 className="">Time Taken</h3>
                <p className="font-bold text-black">
                  {`${getTimeDifference(candidate?.result?.time_started, candidate?.result?.time_ended).hh}hr ${getTimeDifference(candidate?.result?.time_started, candidate?.result?.time_ended).mm}min(s)`}
                </p>
                <p className="font-bold text-black"></p>
              </li>
            </ul>

            <div>


              {
                (candidate.proctoring_report?.was_forced_submission_full_screen) && (
                  <p>Assessment ended abruptly because the candidate exceeded the set tolerance level of <span className="font-bold">{candidate.assessment.full_screen_tolerance_level}</span> for <span className="font-bold">“Full Screen”</span> </p>
                )
              }

              {
                (candidate.proctoring_report?.was_forced_submission_window_change) && (
                  <p>Assessment ended abruptly because candidate the exceeded the set tolerance level of <span className="font-bold">{candidate.assessment.window_change_tolerance_level}</span> for <span className="font-bold">“Window Change”</span> </p>
                )
              }

            </div>

          </div>
        </section>

        <section className="space-y-2">
          <header className="p-4 px-12 bg-grey heading-text">
            Proctoring Report
          </header>
          <div className="pt-4 px-12">
            <table className="w-full">
              <thead className="text-left">
                <tr>
                  <th className="w-[50%]">
                    Flag:
                  </th>
                  <th className="w-[25%]">
                    Count
                  </th>
                  <th>
                    Last time stamp
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y">
                {Object.keys(flag_dict).map((key, _index) => (
                  <tr  >
                    <td>
                      <p className="flex items-center gap-3 py-4">
                        <FlagIcon /> {key}
                      </p>
                    </td>
                    <td>
                      {flag_dict[key].length} times
                    </td>
                    <td>
                      {flag_dict[key][flag_dict[key].length - 1]?.time}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="pt-4 px-12">
            <table className="w-full">
              <thead className="text-left">
                <tr>
                  <th className="w-[50%]">
                    Tolerance Levels:
                  </th>
                  <th className="w-[25%]">
                    Used
                  </th>
                  <th>
                    Total
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y">
                <tr  >
                  <td>
                    <p className="flex items-center gap-3 py-4">
                      <FlagIcon /> Full Screen Exits
                    </p>
                  </td>
                  <td>
                    {candidate.proctoring_report?.full_screen_tolerance_used}
                  </td>
                  <td>
                    {candidate.assessment?.full_screen_tolerance_level}
                  </td>
                </tr>
                <tr  >
                  <td>
                    <p className="flex items-center gap-3 py-4">
                      <FlagIcon /> Window change
                    </p>
                  </td>
                  <td>
                    {candidate.proctoring_report?.window_change_tolerance_used}
                  </td>
                  <td>
                    {candidate.assessment?.window_change_tolerance_level}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </section>
        <section>
          <header className="p-4 px-12 bg-grey heading-text">
            <h2> Questions Answered </h2>
          </header>
          <div className="pt-4 space-y-4">
            {Object.entries(candidate?.performance?.report ?? {}).map(([key, value], index) => (
              <div className="px-12 space-y-2" key={key}>
                <h3 className="heading-text">Section {index + 1}: {key}</h3>
                <table className="w-full text-center">
                  <thead  >
                    <tr>
                      <th className="text-left font-normal w-[30%]">Question type</th>
                      <th className="font-normal">
                        Answered
                      </th>
                      <th className="font-normal">
                        Not Answered
                      </th>
                      <th className="font-normal">
                        Correct
                      </th>
                    </tr>
                  </thead>

                  <tbody className="text-black/70">
                    {Object.entries(value).map(([_key, _value], index) => (
                      <tr className="" key={_key}>
                        <td className="text-left pt-2 capitalize">
                          {QUESTION_TYPES_DICTIONARY[_key].name}
                        </td>
                        <td>
                          {_value.answered}
                        </td>
                        <td>
                          {_value.not_answered}
                        </td>
                        <td>
                          {_value.correct_answer}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ))}
          </div>
        </section>

        <section>
          <header className="p-4 px-12 bg-grey heading-text">
            Recommendations
          </header>
          <div className="pt-4 px-12">
            <p>No recommendation for this assessment</p>
          </div>
        </section>
      </section>

    </div>
  )
}
