'use client';

import Link from 'next/link';
import { useEffect, useRef, useState } from 'react';
import '@smastrom/react-rating/style.css';
import { Dialog } from '@headlessui/react';
import { PDFExport } from '@progress/kendo-react-pdf';
import { Rating } from '@smastrom/react-rating';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { ArrowDown2 } from 'iconsax-react';
import {
  ASSESSMENT_STATUSES_DICTIONARY,
  QUESTION_TYPES_DICTIONARY,
} from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants';
import {
  CandidateResult,
  Comment,
  CommentActions,
  ProctoringFlag,
  proctorOption,
} from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import Disclosure from '@/components/Disclosure';
import FlagIcon from '@/components/icons/jsx/FlagIcon';
import Modal from '@/components/Modal';
import Button from '@/components/shared/Button';
import StarIcon from '@/components/shared/icons/Star';
import Loader from '@/components/shared/loader';
import { useBooleanStateControl } from '@/hooks';
import app_fetch from '@/lib/api/appFetch.js';
import { downloadBlob } from '@/lib/utils/functions';
import CandidateSectionAnswer from '../../../../misc/components/CandidateSectionAnswersList';
import {
  getCandidateCommentsQuery,
  getCandidateResultQuery,
} from '../../../../misc/queries/assessments';
import CandidateDetailsDrawer from '../../components/CandidateDetailDrawer';
import CommentItem from '../../components/CommentItem';
import { LinkButton } from '@/components/shared';

export default function Dashboard({
  params,
}: {
  params: { assessment_id: string; candidate_email: string };
}) {
  const [CANDIDATE_EMAIL, setCandidateEmail] = useState('');

  useEffect(() => {
    setCandidateEmail(params.candidate_email.replace('%40', '@'));
  }, [params.candidate_email]);

  type ProctoringDictFlag = ProctoringFlag & { index: number };
  const [flag_dict, setFlagDict] = useState(
    {} as Record<string, ProctoringDictFlag[]>
  );
  const [is_view_flag_images_modal_open, setIsViewFlagImagesModalOpen] =
    useState(false);
  const [active_flag_image_index, setActiveFlagImageIndex] = useState(0);
  const [active_flag_image_type, setActiveFlagImageType] = useState('');
  const [flagSlideContent, setFlagSlideContent] = useState<
    ProctoringDictFlag[]
  >([]);
  const [is_comments_modal_open, setIsCommentsModalOpen] = useState(false);

  function goToNextFlagImage() {
    setActiveFlagImageIndex(
      (active_flag_image_index + 1 + flagSlideContent.length!) %
      (flagSlideContent.length! || 1)
    );
  }

  function goToPreviousFlagImage() {
    setActiveFlagImageIndex(
      (active_flag_image_index - 1 + flagSlideContent.length!) %
      (flagSlideContent.length! || 1)
    );
  }

  function viewFlagImage(flag_index: number, type: string, key?: string) {
    if (key) {
      setFlagSlideContent(flag_dict[key] as []);
    } else {
      setFlagSlideContent(candidate?.proctoring_report?.flags as []);
    }

    setActiveFlagImageIndex(flag_index);
    setActiveFlagImageType(type);

    setIsViewFlagImagesModalOpen(true);
  }

  function openCommentsModal() {
    setIsCommentsModalOpen(true);
  }

  function closeCommentsModal() {
    setIsCommentsModalOpen(false);
  }

  function handleCommentAction(action: CommentActions, comment: Comment) {
    switch (action) {
      case 'edit comment':
        break;
      case 'delete comment':
        app_fetch(`assessments/comments/${comment.id}/`, {
          method: 'DELETE',
        }).then(() => {
          refetchCandidates();
        });
        break;
    }
  }

  const {
    data: comments,
    refetch: refetchCandidates,
    isSuccess: is_loading_comments_success,
    isLoading: is_loading_comments,
    isError: is_comments_error,
    error: comments_error,
  } = getCandidateCommentsQuery({
    candidate_email: CANDIDATE_EMAIL,
    assessment_id: params.assessment_id,
  });

  const {
    data: candidate,
    isSuccess: is_loading_candidate_success,
    isLoading: is_loading_candidate,
    isError: is_loading_candidate_error,
    error: load_candidate_error,
  } = getCandidateResultQuery({
    candidate_email: CANDIDATE_EMAIL,
    assessment_id: params.assessment_id,
  });

  useEffect(() => {
    console.log(candidate, 'CANDIDATE');
    if (candidate) {
      const _flags_dict = {} as Record<string, ProctoringDictFlag[]>;
      candidate.proctoring_report?.clean_flags?.map(
        (flag: ProctoringFlag, index) => {
          if (!_flags_dict[flag.flag]) {
            _flags_dict[flag.flag] = [];
          }
          _flags_dict[flag.flag].push({ ...flag, index });
        }
      );
      setFlagDict(_flags_dict);
    }
  }, [candidate]);

  const {
    state: is_candidate_details_drawer_open,
    setTrue: openCandidateDetailsDrawer,
    setFalse: closeCandidateDetailsDrawer,
  } = useBooleanStateControl(false);

  const [is_sending_report, setIsSendingReport] = useState(false);
  function sendResult() {
    setIsSendingReport(true);
    const options = {
      method: 'POST',
      body: JSON.stringify({
        candidate_email: CANDIDATE_EMAIL,
        assessment_id: params.assessment_id,
      }),
    };
    app_fetch('assessments/export-report/', options)
      .then(response => response.blob())
      .then(result => {
        downloadBlob(result, `getLinked_${params.assessment_id}.xlsx`);
        setIsSendingReport(false);
      });
  }

  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');

  async function submitComment() {
    const options = {
      method: 'POST',
      body: JSON.stringify({
        rating,
        text: comment,
      }),
    };
    return app_fetch(
      `assessments/result_overview/${candidate?.id}/comments/`,
      options
    ).then(response => response.json());
  }

  function submitRating() {
    makeCommentMutation.mutate();
  }

  const query_client = useQueryClient();

  const makeCommentMutation = useMutation({
    mutationFn: submitComment,
    onSuccess: () => {
      setRating(0);
      setComment('');
      query_client.invalidateQueries({
        queryKey: [
          'candidate_comments',
          `candidate_email=${CANDIDATE_EMAIL}`,
          `assessment_id=${params.assessment_id}`,
        ],
      });
    },
  });

  const [current_tab, setCurrentTab] = useState(0);
  const tabs = ['Assessment Overview', 'Proctoring'];
  const proctorTabs = [
    { id: 'individual_tolerance', name: 'Individual tolerance' },
    { id: 'bulk_tolerance', name: 'Bulk tolerance' },
  ];
  const [currentProctorTab, setCurrentProctorTab] = useState(0);

  const flaggedTabs = [
    { id: 'flagged_image', name: 'Flagged Image' },
    { id: 'flagged_screeshot', name: 'Flagged Screenshot' },
  ];
  const [currentFlaggedTab, setCurrentFlaggedTab] = useState(0);

  const [report_pdf, setReportPDF] = useState<PDFExport | null>(null);
  const pdf_export_ref = useRef<HTMLDivElement>(null);

  if (is_loading_candidate) {
    return <Loader />;
  }
  if (is_loading_candidate_error) {
    return <p>error loading candidate</p>;
  }

  function downloadReport() {
    pdf_export_ref.current?.classList.remove('hidden');
    report_pdf?.save(() => {
      pdf_export_ref.current?.classList.add('hidden');
    });
  }

  function getTimeDifference(start: string | Date, end: string | Date) {
    start = new Date(start);
    end = new Date(end);
    /* const diff = new Date(new Date(start).getTime() - new Date(end).getTime()) */
    /* return diff */
    /* let start, end. */
    // Handle cases where times are on opposite sides of midnight
    if (end < start) {
      end.setDate(end.getDate() + 1);
    }

    // Calculate the difference in milliseconds
    // @ts-ignore
    let diff = end - start;

    // Convert milliseconds to hours, minutes, and seconds
    let ms = diff % 1000;
    let ss = Math.floor(diff / 1000) % 60;
    let mm = Math.floor(diff / 1000 / 60) % 60;
    let hh = Math.floor(diff / 1000 / 60 / 60);

    /* console.log(`${hh}hr ${mm}min ${ss}sec ${ms}ms`); */
    return {
      ms,
      ss,
      mm,
      hh,
    };
  }

  return (
    <div className="m-4 ml-0 space-y-2">
    
      <div className="flex items-start gap-4 bg-white p-4 text-sm">
     
        <Link
          className="btn-primary-light-pill"
          href={`/e/assessments-and-interviews/assessments/${params.assessment_id}`}
        >
          Back
        </Link>



        <div className="flex flex-1 items-start gap-4">
          <div className="bg-light-accent-bg relative z-0 flex aspect-square w-24 items-center justify-center rounded-full bg-primary-light">
            {candidate.proctoring_report?.profile_photo ? (
              <img
                src={candidate?.proctoring_report?.profile_photo}
                className="aspect-square  w-24 rounded-full"
              />
            ) : (
              <h2 className="text-2xl font-bold text-primary">
                {' '}
                {candidate.result?.candidate_name
                  ?.split(' ')
                  .slice(0, 2)
                  .map(name => name.slice(0, 1)) || '-'}{' '}
              </h2>
            )}
            <span className="absolute right-0 top-0 flex aspect-square w-8 items-center justify-center rounded-full bg-primary text-xs text-white shadow-md">
              {candidate.result?.status == 'completed'
                ? Math.round(candidate.result?.overall_percentage)
                : '--'}
              %
            </span>
          </div>
          <div className="flex-1 space-y-2">
            <div>
              <div className="flex items-end justify-between">
                <h1 className="heading-2 text-base text-primary">
                  {candidate.result?.candidate_name}
                </h1>

                <div className="flex items-center mb-2 gap-2">
                  
                  {candidate.previous_candidate_email && <LinkButton
                    href={`/e/assessments-and-interviews/assessments/${params.assessment_id}/candidates/${candidate.previous_candidate_email}/`}
                    size="capsule"
                     variant="extralight"
                  >
                    Previous
                  </LinkButton>
}
                 {candidate.next_candidate_email && <LinkButton
                    href={`/e/assessments-and-interviews/assessments/${params.assessment_id}/candidates/${candidate.next_candidate_email}/`}
                    size="capsule"
                    variant="extralight"
                  >
                    Next
                  </LinkButton>
}
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex divide-x">
                  <p className="pr-2">{CANDIDATE_EMAIL}</p>
                  <p className="pl-2">
                    {candidate.proctoring_report?.location}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    className="btn-primary-light"
                    onClick={openCandidateDetailsDrawer}
                    disabled={!candidate?.result?.job_application}
                  >
                    View details
                  </Button>
                  <Button
                    /* is_busy={is_sending_report} */
                    className="btn-primary-light"
                    onClick={downloadReport}
                    disabled={candidate?.result?.status !== 'completed'}
                  >
                    Export result
                  </Button>
                </div>
              </div>
            </div>
            <hr />
            <div className="flex gap-6">
              <div>
                <p className="heading-text">{candidate?.assessment?.name}</p>
                <p className="text-xs">Assessment</p>
              </div>
              <div>
                {candidate.result?.status == 'completed'
                  ? candidate.result?.overall_percentage
                  : '--'}
                %<p className="text-xs">Score</p>
              </div>
              <div>
                <p
                  className={` ${candidate.result?.status === 'ongoing'
                    ? 'text-yellow-500'
                    : candidate.result?.status === 'completed'
                      ? 'text-green-500'
                      : 'text-red-500'
                    } } `}
                >
                  {
                    ASSESSMENT_STATUSES_DICTIONARY[candidate.result?.status]
                      ?.title
                  }
                </p>
                <p className="text-xs">Status</p>
              </div>
              <div>
                <p>
                  {new Date(candidate.result?.invite_date).toLocaleString()}
                </p>
                <p className="text-xs">Invited date</p>
              </div>
              <div>
                <p>
                  {candidate.result?.date_taken
                    ? new Date(candidate.result?.date_taken).toLocaleString()
                    : '-'}
                </p>
                <p className="text-xs">Completed date</p>
              </div>

              {candidate.result?.completion_duration && (
                <div>
                  <p>
                    {candidate.result?.completion_duration
                      ? candidate.result?.completion_duration + ' min(s)'
                      : '-'}
                  </p>
                  <p className="text-xs">Completed duration</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <nav className="tab-nav w-full">
        <ul className="tab-list">
          {tabs.map((tab, index) => (
            <li
              key={index}
              className={
                index == current_tab ? 'active-tab-item' : 'text-primary-dark'
              }
            >
              <label className="cursor-pointer">
                <input
                  type="radio"
                  name="active tab"
                  className="hidden"
                  value={index}
                  onChange={e => setCurrentTab(parseInt(e.target.value))}
                />
                {tab}
              </label>
            </li>
          ))}
        </ul>
      </nav>
      {is_loading_candidate ? (
        <Loader />
      ) : (
        <div className="grid gap-2 md:grid-cols-2">
          {/* assessment overview */}
          {tabs[current_tab].toLowerCase() == 'assessment overview' && (
            <>
              <div className="space-y-2 rounded-xl bg-white p-4">
                <h2 className="heading-text">Performance</h2>
                {!candidate?.performance?.section_scores && (
                  <p className="text-sm italic text-helper-text">
                    Nothing to show yet.
                  </p>
                )}
                {candidate.performance?.section_scores &&
                  Object.keys(candidate.performance.section_scores ?? {})
                    .length <= 0 && <p>No questions answered</p>}
                <ul className="space-y-1">
                  {candidate.performance?.section_scores &&
                    Object.keys(candidate.performance.section_scores ?? {}).map(
                      (key, index) => (
                        <li key={index} className="">
                          <CandidateSectionAnswer
                            index={index}
                            assessment_id={params.assessment_id}
                            candidate_email={params.candidate_email}
                            section_id={
                              candidate.performance.section_scores[key]
                                .section_id
                            }
                          >
                            <div className="btn-base heading-text flex items-center justify-between bg-grey py-4 font-normal">
                              <div className="flex items-center gap-2">
                                <span className="flex aspect-square w-[20px] items-center justify-center rounded-full bg-primary text-sm text-white">
                                  {index + 1}
                                </span>
                                <p className=""> {key} </p>
                              </div>
                              <div className="flex items-center gap-4">
                                <p className="font-bold">
                                  {candidate.performance?.section_scores[key]
                                    .percentage === 0
                                    ? 0
                                    : Math.round(
                                      candidate.performance?.section_scores[
                                        key
                                      ].percentage
                                    ) || '-- '}
                                  %
                                </p>
                                <ArrowDown2 />
                              </div>
                            </div>
                          </CandidateSectionAnswer>
                        </li>
                      )
                    )}
                </ul>
                <ul className="space-y-1"></ul>
              </div>
              <div className="">
                <div className="top-[80px] space-y-4 rounded-xl bg-white p-4 text-sm md:sticky">
                  <div className="space-y-2">
                    <h2 className="heading-text">Give rating</h2>
                    <p>
                      Provide your personal rating for this candidate based on
                      your impressions and interactions
                    </p>
                    <div>
                      <Rating
                        style={{ maxWidth: 120 }}
                        itemStyles={{
                          itemShapes: StarIcon,
                          activeFillColor: '#755AE2',
                          inactiveFillColor: '#E2DDFF',
                        }}
                        value={rating}
                        onChange={setRating}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h2 className="heading-text">Comment:</h2>
                    <label className="space-y-1">
                      <p aria-hidden="true" className="hidden">
                        Comment
                      </p>
                      <textarea
                        className="input-grey min-h-[200px]"
                        placeholder="Add notes regarding this candidate"
                        value={comment}
                        onChange={e => setComment(e.target.value)}
                      ></textarea>
                    </label>
                  </div>
                  <div className="flex justify-end">
                    <Button
                      is_busy={makeCommentMutation.isLoading}
                      onClick={submitRating}
                      className="btn-primary"
                    >
                      Submit
                    </Button>
                  </div>

                  <h2 className="heading-text">Comments:</h2>
                  {is_loading_comments && (
                    <p className="helper-text">loading comments...</p>
                  )}
                  {is_loading_comments_success && (
                    <>
                      {comments?.length > 0 ? (
                        <>
                          <CommentItem
                            comment={comments[comments.length - 1]}
                            onAction={(action, comment) =>
                              handleCommentAction(action, comment)
                            }
                          />
                          <div className="flex justify-end">
                            <button
                              onClick={openCommentsModal}
                              className="btn-primary-light"
                            >
                              View more comments
                            </button>
                          </div>
                        </>
                      ) : (
                        <p className="helper-text">
                          All comments will appear here...
                        </p>
                      )}
                    </>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Proctoring */}
          {tabs[current_tab].toLowerCase() == 'proctoring' && (
            <>
              <div className="space-y-4 rounded-xl bg-white p-4 text-sm">
                <div className="space-y-2">
                  <div>
                    <h2 className="heading-2">Onboarding Images</h2>
                    <p className="text-helper-text">
                      Profile image and identity card images taken on onboarding
                    </p>
                  </div>
                  {candidate.proctoring_report?.profile_photo ? (
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex flex-col items-center gap-2">
                        <div className="w-full overflow-hidden rounded-xl border border-[#E0E0E0] bg-[#F8F9FB]">
                          <img
                            src={candidate.proctoring_report?.profile_photo}
                            alt="candidate's profile image"
                            className="h-[300px] w-full object-cover"
                          />
                        </div>
                        <p className="rounded-full bg-light-background p-2 text-primary">
                          Profile image
                        </p>
                      </div>
                      <div className="flex flex-col items-center gap-2">
                        <div className="w-full overflow-hidden rounded-xl border border-[#E0E0E0] bg-[#F8F9FB]">
                          <img
                            src={candidate.proctoring_report?.photo_id}
                            alt="candidate's identity image"
                            className="h-[300px] w-full object-cover"
                          />
                        </div>
                        <p className="rounded-full bg-light-background p-2 text-primary">
                          Identity card image
                        </p>
                      </div>
                    </div>
                  ) : (
                    <div className="grid min-h-[300px] place-items-center rounded-xl border border-[#E0E0E0] bg-[#F8F9FB]">
                      <div className="flex flex-col items-center gap-2 text-center">
                        <img src="/image icon.png" alt="image placeholder" />
                        <p className="text-xs text-helper-text">
                          Web cam recording will appear here
                        </p>
                      </div>
                    </div>
                  )}
                </div>
                <div className="space-y-2">
                  <div className="flex items-start gap-4 border-b-4  border-gray-300">
                    {flaggedTabs.map((option, i) => (
                      <div
                        className={` cursor-pointer border-b-4 p-2 font-medium 
                    ${i == currentFlaggedTab
                            ? 'border-primary text-primary'
                            : 'border-transparent text-gray-900'
                          }
                    `}
                        onClick={() => setCurrentFlaggedTab(i)}
                      >
                        {option.name}
                      </div>
                    ))}
                  </div>

                  {/* FLAGGED IMAGE */}
                  {currentFlaggedTab == 0 && (
                    <>
                      <div>
                        {/* <h2 className="heading-2">Flagged Screenshots</h2> */}
                        <p className="text-helper-text">
                          Images taken during the assessment
                        </p>
                      </div>
                      {candidate.proctoring_report?.flags?.length ? (
                        <div className="grid grid-cols-3 gap-2">
                          {candidate.proctoring_report?.flags.map(
                            (flag, index) => (
                              <Button
                                onClick={() =>
                                  viewFlagImage(index, 'screenshot')
                                }
                                key={index}
                                className="grid min-h-[100px] place-items-center rounded-xl border border-[#E0E0E0] bg-[#F8F9FB]"
                              >
                                <div className="flex flex-col items-center gap-2 text-center">
                                  <img
                                    className="cover rounded-xl"
                                    src={flag.screenshot}
                                    alt={`${flag.flag} - ${flag.time}`}
                                  />
                                </div>
                              </Button>
                            )
                          )}
                        </div>
                      ) : (
                        <p className="italic text-helper-text">
                          No images recorded.
                        </p>
                      )}
                    </>
                  )}
                  {/* FLAGGED SCREENSHOT */}
                  {currentFlaggedTab == 1 && (
                    <>
                      <div>
                        {/* <h2 className="heading-2">Flagged Screenshots</h2> */}
                        <p className="text-helper-text">
                          Screenshots taken when taken during the assessment
                        </p>
                      </div>
                      {candidate.proctoring_report?.flags?.length ? (
                        <div className="grid grid-cols-3 gap-2">
                          {candidate.proctoring_report?.flags.map(
                            (flag, index) => (
                              <Button
                                onClick={() => viewFlagImage(index, 'capture')}
                                key={index}
                                className="grid min-h-[100px] place-items-center rounded-xl border border-[#E0E0E0] bg-[#F8F9FB]"
                              >
                                <div className="flex flex-col items-center gap-2 text-center">
                                  <img
                                    className="cover rounded-xl"
                                    src={flag.pc_capture}
                                    alt={`${flag.flag} - ${flag.time}`}
                                  />
                                </div>
                              </Button>
                            )
                          )}
                        </div>
                      ) : (
                        <p className="italic text-helper-text">
                          No images recorded.
                        </p>
                      )}
                    </>
                  )}
                </div>
              </div>

              <div className="space-y-6 rounded-xl bg-white p-4 text-sm">
                <div className="space-y-2">
                  <div>
                    <h2 className="heading-2">Proctoring report</h2>
                  </div>
                  <ul className="space-y-1">
                    <li>
                      <div className="grid grid-cols-2 gap-2 ">
                        <p>Device used</p>
                        <p className="font-bold">
                          {candidate.proctoring_report?.device_used || '-'}
                        </p>
                      </div>
                    </li>
                    <li>
                      <div className="grid grid-cols-2 gap-2">
                        <p>Webcam enabled?</p>
                        <p className="font-bold capitalize">
                          {`${candidate.proctoring_report?.webcam_enabled || '-'
                            }`}
                        </p>
                      </div>
                    </li>
                    <li>
                      <div className="grid grid-cols-2 gap-2">
                        <p>Location</p>
                        <p className="font-bold">
                          {candidate.proctoring_report?.location || '-'}
                        </p>
                      </div>
                    </li>
                  </ul>
                </div>

                <div className="flex items-start gap-4 border-b-4  border-gray-300">
                  {proctorTabs.map((option, i) => (
                    <div
                      className={` cursor-pointer border-b-4 p-2 font-medium 
                    ${i == currentProctorTab
                          ? 'border-primary text-primary'
                          : 'border-transparent text-gray-900'
                        }
                    `}
                      onClick={() => setCurrentProctorTab(i)}
                    >
                      {option.name}
                    </div>
                  ))}
                </div>
                {/* INDIVIDUAL */}
                {currentProctorTab == 0 && (
                  <div className="space-y-2">
                    <div className="grid grid-cols-4 px-4 font-bold">
                      <h3 className="col-span-2 -ml-4"> Tolerance Levels: </h3>
                      <p> Used </p>
                      <p>Total allowed</p>
                    </div>
                    <div className="space-y-1">
                      <div className="grid grid-cols-4 rounded-xl bg-grey p-4">
                        <p className="col-span-2 flex items-center gap-3">
                          <FlagIcon /> Full Screen Exits
                        </p>
                        <p>
                          {' '}
                          {
                            candidate.proctoring_report
                              ?.full_screen_tolerance_used
                          }{' '}
                        </p>
                        <p>
                          {' '}
                          {
                            candidate.assessment?.full_screen_tolerance_level
                          }{' '}
                        </p>
                      </div>
                      <div className="grid grid-cols-4 rounded-xl bg-grey p-4">
                        <p className="col-span-2 flex items-center gap-3">
                          <FlagIcon /> Window change
                        </p>
                        <p>
                          {
                            candidate.proctoring_report
                              ?.window_change_tolerance_used
                          }
                        </p>
                        <p>
                          {candidate.assessment?.window_change_tolerance_level}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
                {/* BULK PROCTORING */}
                {currentProctorTab == 1 && (
                  <div className="space-y-2">
                    {candidate.assessment.bulk_tolerance_setup && (
                      <>
                        <p>
                          Bulk tolerance was set to end the assessment on the{' '}
                          {
                            candidate.assessment.bulk_tolerance_setup
                              .combined_tolerance
                          }{' '}
                          violation of the flags highlighted below.
                        </p>
                        <div className="grid grid-cols-4 px-4 font-bold">
                          <h3 className="col-span-2 -ml-4">
                            {' '}
                            Tolerance Levels:{' '}
                          </h3>
                          <p> Used </p>
                        </div>
                        <div className="space-y-1">
                          {candidate.proctoring_report &&
                            candidate.proctoring_report.bulk_proctor_option?.map(
                              po => (
                                <div className="grid grid-cols-4 rounded-xl bg-grey p-4">
                                  <p className="col-span-2 flex items-center gap-3">
                                    <FlagIcon />
                                    {po.key == proctorOption.fullScreenExit
                                      ? 'Full Screen Exit'
                                      : po.key == proctorOption.windowChange
                                        ? 'Window Change'
                                        : po.key == proctorOption.multiFace
                                          ? 'Multiple Face'
                                          : po.key == proctorOption.differentFace
                                            ? 'Different Face'
                                            : po.key == proctorOption.illegalObject
                                              ? 'Illegal Object'
                                              : ''}
                                  </p>
                                  <p>{po.value} time(s)</p>
                                </div>
                              )
                            )}
                        </div>
                      </>
                    )}
                    {!candidate.assessment.bulk_tolerance_setup && (
                      <p>No Bulk proctoring was setup for this assessment</p>
                    )}
                  </div>
                )}

                <div className="space-y-2">
                  <h2 className="heading-text">Proctoring flags log</h2>
                  {candidate?.proctoring_report?.clean_flags?.length ? (
                    <ul className="space-y-1">
                      {Object.keys(flag_dict).map((key, _index) => (
                        <li key={_index}>
                          <Disclosure
                            title={
                              <div className="flex items-center justify-between py-0">
                                <div className="flex items-center gap-2">
                                  <span>{_index + 1}. </span>
                                  <FlagIcon />
                                  <p className="">
                                    {key.replaceAll('_', ' ')}:{' '}
                                    <span className="text-red-400">
                                      {flag_dict[key].length} times
                                    </span>
                                  </p>
                                </div>
                                <span>
                                  <ArrowDown2 />
                                </span>
                              </div>
                            }
                          >
                            <ul className="ml-4 space-y-1">
                              {flag_dict[key].map((flag, index: number) => (
                                <li
                                  key={index}
                                  className="grid grid-cols-9 items-center gap-4"
                                >
                                  <span>{index + 1}.</span>
                                  <div className="col-span-8 grid grid-cols-3 items-center gap-2">
                                    <p>{flag.time}</p>
                                    <div className="flex items-center gap-1">
                                      <FlagIcon />
                                      <p>{flag.flag.replaceAll('_', ' ')}</p>
                                    </div>
                                    <Button
                                      onClick={() =>
                                        viewFlagImage(index, 'screenshot', key)
                                      }
                                    >
                                      <img
                                        src={flag.screenshot}
                                        className="aspect-square h-8 rounded-full"
                                        alt=""
                                      />
                                    </Button>

                                    {/* <Button
                                        onClick={() =>
                                          viewFlagImage(index, 'capture', key)
                                        }
                                      >
                                        <img
                                          src={flag.pc_capture}
                                          className="aspect-square h-8 rounded-full"
                                          alt=""
                                        />
                                      </Button> */}
                                  </div>
                                </li>
                              ))}
                            </ul>
                          </Disclosure>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="helper-text italic">No flags recorded</p>
                  )}
                </div>
              </div>
            </>
          )}
        </div>
      )}
      <Dialog
        open={is_comments_modal_open}
        onClose={closeCommentsModal}
        className="relative z-50"
      >
        {
          <>
            <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

            <div className="fixed inset-0 w-screen overflow-y-auto">
              <div className="flex min-h-full items-start">
                <Dialog.Panel className="max-w-1g ml-auto flex min-h-screen min-w-[450px] flex-col overflow-hidden bg-white sm:min-w-[300px]">
                  <header className="flex items-center justify-between gap-4 bg-primary p-8 pb-4 pt-6 text-white">
                    <Dialog.Title className="font-medium">
                      Comments
                    </Dialog.Title>
                    <button
                      onClick={closeCommentsModal}
                      className="rounded-xl bg-white bg-opacity-20 px-5 py-2 text-xs"
                    >
                      Close
                    </button>
                  </header>

                  <div className="space-y-4 p-4">
                    <h2 className="heading-text">Comments:</h2>
                    <ul className="h-full w-[526px] flex-1 space-y-4 text-sm">
                      {is_loading_comments && (
                        <p className="helper-text">loading comments...</p>
                      )}
                      {is_loading_comments_success && (
                        <>
                          {comments?.length > 0 ? (
                            comments.map((comment, index) => (
                              <li key={index} className="border-b">
                                <CommentItem
                                  comment={comment}
                                  onAction={(action, comment) =>
                                    handleCommentAction(action, comment)
                                  }
                                />
                              </li>
                            ))
                          ) : (
                            <p className="helper-text">
                              All comments will appear here...
                            </p>
                          )}
                        </>
                      )}
                    </ul>
                  </div>
                </Dialog.Panel>
              </div>
            </div>
          </>
        }
      </Dialog>

      <Modal
        title={`Flagged image ${active_flag_image_index + 1}/${flagSlideContent.length
          }`}
        is_open={is_view_flag_images_modal_open}
        close={() => setIsViewFlagImagesModalOpen(false)}
      >
        <div className="min-w-[600px] space-y-4 p-4">
          <div>
            <h2 className="heading-2">Flag screenshots</h2>
            <p className="text-helper-text">
              Images show screenshots taken during the asssessment
            </p>
          </div>
          {flagSlideContent.length ? (
            <>
              <div className="relative grid min-h-[300px] place-items-center overflow-hidden rounded-xl border border-danger/50 bg-[#F8F9FB]">
                <div className="absolute left-0 top-0 m-1 flex gap-1 overflow-hidden rounded-md text-sm text-white">
                  <div className="relative p-2">
                    <div className="absolute inset-0 bg-danger/50" />
                    <p className="relative">
                      {' '}
                      {flagSlideContent[active_flag_image_index]?.flag}{' '}
                    </p>
                  </div>
                  <div className="relative p-2">
                    <div className="absolute inset-0 bg-danger/50" />
                    <p className="relative">
                      {' '}
                      {flagSlideContent[active_flag_image_index]?.time}{' '}
                    </p>
                  </div>
                </div>
                <img
                  src={
                    active_flag_image_type.trim() === 'screenshot'.trim()
                      ? flagSlideContent[active_flag_image_index]?.screenshot
                      : flagSlideContent[active_flag_image_index]?.pc_capture
                  }
                  alt={`${flagSlideContent[active_flag_image_index]?.flag} - ${flagSlideContent[active_flag_image_index]?.time}`}
                  className="image-cover w-full max-w-7xl"
                />
              </div>
              <div className="flex justify-between">
                <Button
                  className="btn-primary-bordered"
                  onClick={goToPreviousFlagImage}
                >
                  Previous image
                </Button>

                {/* hide download image button */}
                {true ? (
                  <span></span>
                ) : (
                  <Button className="btn-primary-light hidden items-center gap-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 12 12"
                      fill="none"
                    >
                      <path
                        d="M7.5 11.375H4.5C1.785 11.375 0.625 10.215 0.625 7.5V4.5C0.625 1.785 1.785 0.625 4.5 0.625H7.5C10.215 0.625 11.375 1.785 11.375 4.5V7.5C11.375 10.215 10.215 11.375 7.5 11.375ZM4.5 1.375C2.195 1.375 1.375 2.195 1.375 4.5V7.5C1.375 9.805 2.195 10.625 4.5 10.625H7.5C9.805 10.625 10.625 9.805 10.625 7.5V4.5C10.625 2.195 9.805 1.375 7.5 1.375H4.5Z"
                        fill="#755AE2"
                      />
                      <path
                        d="M6.0007 7.6301C5.9057 7.6301 5.8107 7.5951 5.7357 7.5201L4.2357 6.0201C4.0907 5.8751 4.0907 5.6351 4.2357 5.4901C4.3807 5.3451 4.6207 5.3451 4.7657 5.4901L6.0007 6.7251L7.2357 5.4901C7.3807 5.3451 7.6207 5.3451 7.7657 5.4901C7.9107 5.6351 7.9107 5.8751 7.7657 6.0201L6.2657 7.5201C6.1907 7.5951 6.0957 7.6301 6.0007 7.6301Z"
                        fill="#755AE2"
                      />
                      <path
                        d="M6 7.62988C5.795 7.62988 5.625 7.45988 5.625 7.25488V3.25488C5.625 3.04988 5.795 2.87988 6 2.87988C6.205 2.87988 6.375 3.04988 6.375 3.25488V7.25488C6.375 7.46488 6.205 7.62988 6 7.62988Z"
                        fill="#755AE2"
                      />
                      <path
                        d="M5.99924 9.11508C4.94424 9.11508 3.88424 8.94508 2.87924 8.61008C2.68424 8.54508 2.57924 8.33008 2.64424 8.13508C2.70924 7.94008 2.91924 7.83008 3.11924 7.90008C4.97924 8.52008 7.02424 8.52008 8.88424 7.90008C9.07924 7.83508 9.29424 7.94008 9.35924 8.13508C9.42424 8.33008 9.31924 8.54508 9.12424 8.61008C8.11424 8.95008 7.05424 9.11508 5.99924 9.11508Z"
                        fill="#755AE2"
                      />
                    </svg>

                    <span> Download Image </span>
                  </Button>
                )}

                <Button
                  className="btn-primary-bordered"
                  onClick={goToNextFlagImage}
                >
                  Next image
                </Button>
              </div>
            </>
          ) : null}
        </div>
      </Modal>

      <CandidateDetailsDrawer
        show_pagination={false}
        candidate_data={candidate?.result?.job_application}
        isDrawerOpen={is_candidate_details_drawer_open}
        closeDrawer={closeCandidateDetailsDrawer}
        openDrawer={openCandidateDetailsDrawer}
      />

      <PDFExport
        fileName={`${candidate?.result?.candidate_name} - ${candidate?.assessment?.name}`}
        paperSize="A4"
        title="Candidate result"
        ref={r => setReportPDF(r)}
      >
        <div className="hidden" ref={pdf_export_ref}>
          <section className="flex h-[842px] bg-gradient-to-b from-[#F0EDFF] to-[#C59EDE] p-4 px-12">
            <div className="flex w-3/4 flex-col justify-between bg-[#250A35;] p-12 text-grey/60">
              <div>
                <div className="text-white">
                  <h1 className="font-clash text-[2rem] font-black 2xl:text-4xl">
                    Getlinked
                  </h1>
                  <p className="text-sm font-light">by Whispaconnect</p>
                </div>
              </div>

              <div className="space-y-3 text-sm">
                <p>Assessment report for:</p>
                <p className="font-clash text-[2rem] capitalize text-white">
                  {candidate?.assessment?.name}
                </p>
                <p>
                  Date:{' '}
                  {new Date(candidate?.result?.time_started).toLocaleString()}
                </p>
              </div>

              <div className="text-sm">
                <p>Copyright {new Date().getFullYear()} Getlinked.</p>
                <p>All rights reserved.</p>
              </div>
            </div>
          </section>

          <section id="report" className="space-y-6 bg-white py-4 text-sm">
            <section className="flex items-start gap-4 bg-grey p-4 px-12">
              <div className="flex flex-1 items-center gap-4">
                <div className="bg-light-accent-bg relative z-0 flex aspect-square w-24 items-center justify-center rounded-full bg-primary-light">
                  {candidate.proctoring_report?.profile_photo ? (
                    <img
                      src={candidate?.proctoring_report?.profile_photo}
                      className="aspect-square  w-24 rounded-full"
                    />
                  ) : (
                    <h2 className="text-2xl font-bold text-primary">
                      {' '}
                      {candidate.result?.candidate_name
                        ?.split(' ')
                        .slice(0, 2)
                        .map(name => name.slice(0, 1)) || '-'}{' '}
                    </h2>
                  )}
                  <span className="absolute right-0 top-0 flex aspect-square w-8 items-center justify-center rounded-full bg-primary text-xs text-white shadow-md">
                    {candidate.result?.status == 'completed'
                      ? Math.round(candidate.result?.overall_percentage)
                      : '--'}
                    %
                  </span>
                </div>
                <div className="flex-1 space-y-2">
                  <div className="">
                    <div className="flex items-start gap-4">
                      <h1 className="heading-2">
                        {candidate.result?.candidate_name}
                      </h1>
                      <span className="rounded-full bg-primary/20 p-2 text-xs capitalize text-primary">
                        {candidate.result?.status}
                      </span>
                    </div>
                    <div className="text-black/60">
                      <p className="pr-2">{CANDIDATE_EMAIL}</p>
                      <div className="flex divide-x">
                        <p className="pr-2">Phone number</p>
                        <p className="pl-2">
                          {candidate.proctoring_report?.location}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            <section className="space-y-2 text-black/50">
              <header className="heading-text bg-grey p-4 px-12">
                Candidate Overall
              </header>

              <ul className="grid grid-cols-5 divide-x bg-grey p-4 px-12 text-xs">
                <li className="">
                  <h3 className="heading-text text-base">
                    {candidate?.result?.total_questions}
                  </h3>
                  <p>No of questions</p>
                </li>
                <li className="pl-4">
                  <h3>
                    {' '}
                    <span className="heading-text text-base">
                      {candidate?.result?.total_questions_answered}
                    </span>{' '}
                    <span className="text-green-500">
                      / {candidate?.result?.total_questions}
                    </span>
                  </h3>
                  <p>Questions Answered</p>
                </li>
                {/* <li className="pl-4"> */}
                {/*   <h3> <span className="heading-text text-base">-</span> <span className="text-green-500">/ {candidate?.result?.total_questions}</span></h3> */}
                {/*   <p>Correct Answers</p> */}
                {/* </li> */}
                <li className="pl-4">
                  <h3 className="heading-text text-base">
                    {`${getTimeDifference(
                      candidate?.result?.time_started,
                      candidate?.result?.time_ended
                    ).hh
                      }hr ${getTimeDifference(
                        candidate?.result?.time_started,
                        candidate?.result?.time_ended
                      ).mm
                      }min(s)`}
                  </h3>
                  <p>Time Taken</p>
                </li>
                <li className="pl-4">
                  <h3 className="heading-text text-base">
                    {candidate?.proctoring_report?.flags?.length || '0'}
                  </h3>
                  <p>Flags recorded</p>
                </li>
              </ul>
              <div className="space-y-2 px-12 py-6">
                <h2 className="heading-text">Assessment submission</h2>
                <ul className="grid grid-cols-4 divide-x">
                  <li className="">
                    <h3 className="">Start time</h3>
                    <p className="font-bold text-black">
                      {new Date(
                        candidate?.result?.time_started
                      ).toLocaleString()}
                    </p>
                  </li>
                  <li className="pl-4">
                    <h3 className="">End time</h3>
                    <p className="font-bold text-black">
                      {new Date(candidate?.result?.time_ended).toLocaleString()}
                    </p>
                  </li>
                  <li className="pl-4">
                    <h3 className="">Time Taken</h3>
                    <p className="font-bold text-black">
                      {`${getTimeDifference(
                        candidate?.result?.time_started,
                        candidate?.result?.time_ended
                      ).hh
                        }hr ${getTimeDifference(
                          candidate?.result?.time_started,
                          candidate?.result?.time_ended
                        ).mm
                        }min(s)`}
                    </p>
                    <p className="font-bold text-black"></p>
                  </li>
                </ul>

                <div>
                  {candidate.proctoring_report
                    ?.was_forced_submission_full_screen && (
                      <p>
                        Assessment ended abruptly because the candidate exceeded
                        the set tolerance level of{' '}
                        <span className="font-bold">
                          {candidate.assessment.full_screen_tolerance_level}
                        </span>{' '}
                        for <span className="font-bold">“Full Screen”</span>{' '}
                      </p>
                    )}

                  {candidate.proctoring_report
                    ?.was_forced_submission_window_change && (
                      <p>
                        Assessment ended abruptly because candidate the exceeded
                        the set tolerance level of{' '}
                        <span className="font-bold">
                          {candidate.assessment.window_change_tolerance_level}
                        </span>{' '}
                        for <span className="font-bold">“Window Change”</span>{' '}
                      </p>
                    )}
                </div>
              </div>
            </section>

            <section className="space-y-2">
              <header className="heading-text bg-grey p-4 px-12">
                Proctoring Report
              </header>
              <div className="px-12 pt-4">
                <table className="w-full">
                  <thead className="text-left">
                    <tr>
                      <th className="w-[50%]">Flag:</th>
                      <th className="w-[25%]">Count</th>
                      <th>Last time stamp</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    {Object.keys(flag_dict).map((key, _index) => (
                      <tr>
                        <td>
                          <p className="flex items-center gap-3 py-4">
                            <FlagIcon /> {key}
                          </p>
                        </td>
                        <td>{flag_dict[key].length} times</td>
                        <td>
                          {flag_dict[key][flag_dict[key].length - 1]?.time}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="px-12 pt-4">
                <table className="w-full">
                  <thead className="text-left">
                    <tr>
                      <th className="w-[50%]">Tolerance Levels:</th>
                      <th className="w-[25%]">Used</th>
                      <th>Total</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    <tr>
                      <td>
                        <p className="flex items-center gap-3 py-4">
                          <FlagIcon /> Full Screen Exits
                        </p>
                      </td>
                      <td>
                        {
                          candidate.proctoring_report
                            ?.full_screen_tolerance_used
                        }
                      </td>
                      <td>
                        {candidate.assessment?.full_screen_tolerance_level}
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <p className="flex items-center gap-3 py-4">
                          <FlagIcon /> Window change
                        </p>
                      </td>
                      <td>
                        {
                          candidate.proctoring_report
                            ?.window_change_tolerance_used
                        }
                      </td>
                      <td>
                        {candidate.assessment?.window_change_tolerance_level}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </section>
            <section>
              <header className="heading-text bg-grey p-4 px-12">
                <h2> Questions Answered </h2>
              </header>
              <div className="space-y-4 pt-4">
                {Object.entries(candidate?.performance?.report ?? {}).map(
                  ([key, value], index) => (
                    <div className="space-y-2 px-12" key={key}>
                      <h3 className="heading-text">
                        Section {index + 1}: {key}
                      </h3>
                      <table className="w-full text-center">
                        <thead>
                          <tr>
                            <th className="w-[30%] text-left font-normal">
                              Question type
                            </th>
                            <th className="font-normal">Answered</th>
                            <th className="font-normal">Not Answered</th>
                            <th className="font-normal">Correct</th>
                          </tr>
                        </thead>

                        <tbody className="text-black/70">
                          {Object.entries(value).map(
                            ([_key, _value], index) => (
                              <tr className="" key={_key}>
                                <td className="pt-2 text-left capitalize">
                                  {QUESTION_TYPES_DICTIONARY[_key].name}
                                </td>
                                <td>{_value.answered}</td>
                                <td>{_value.not_answered}</td>
                                <td>{_value.correct_answer}</td>
                              </tr>
                            )
                          )}
                        </tbody>
                      </table>
                    </div>
                  )
                )}
              </div>
            </section>

            <section>
              <header className="heading-text bg-grey p-4 px-12">
                Recommendations
              </header>
              <div className="px-12 pt-4">
                <p>No recommendation for this assessment</p>
              </div>
            </section>
          </section>
        </div>
      </PDFExport>
    </div>
  );
}
