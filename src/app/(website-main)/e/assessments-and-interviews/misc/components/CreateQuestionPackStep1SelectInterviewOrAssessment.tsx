import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { Sms, TableDocument, Video } from 'iconsax-react';
import Link from 'next/link';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { AltButton, AltModal, Input, Textarea } from '@/components/shared';
import { useBooleanStateControl } from '@/hooks';
import { Axios } from '@/lib/api/axios';
import CreateQuestionPackStep2SelectTypeModal from './CreateQuestionPackStep2SelectTypeModal';

const schema = z.object({
  name: z
    .string({ message: 'Enter pack name' })
    .min(3, { message: 'Enter at least 3 characters' }),
  description: z
    .string({ message: 'Enter pack description' })
    .min(10, { message: 'Enter at least 10 characters' }),
});

type formType = z.infer<typeof schema>;

interface CreateQuestionPackStep1SelectInterviewOrAssessmentProps {
  isCreateQuestionPackStep1SelectInterviewOrAssessmentOpen: boolean;
  closeCreateQuestionPackStep1SelectInterviewOrAssessment: () => void;
  clientId?: string;
}

const CreateQuestionPackStep1SelectInterviewOrAssessment = ({
  isCreateQuestionPackStep1SelectInterviewOrAssessmentOpen,
  closeCreateQuestionPackStep1SelectInterviewOrAssessment,
  clientId,
}: CreateQuestionPackStep1SelectInterviewOrAssessmentProps) => {
  const {
    handleSubmit,
    register,
    formState: { errors },
  } = useForm<formType>({
    resolver: zodResolver(schema),
  });
  const [isInterview, setIsInterview] = React.useState(false);

  const {
    state: isCreateStep2ModalOpen,
    setTrue: openStep2Modal,
    setFalse: closeCreateStep2Modal,
  } = useBooleanStateControl();

  return (
    <>
      <AltModal
        is_open={isCreateQuestionPackStep1SelectInterviewOrAssessmentOpen}
        close={closeCreateQuestionPackStep1SelectInterviewOrAssessment}
        title="options to continue"
      >
        <div className="flex justify-center gap-2 px-6 py-4">
          <button
            className="btn-modal-choice"
            onClick={() => {
              setIsInterview(false);
              openStep2Modal();
            }}
          >
            <div className="flex flex-col justify-between gap-2">
              <div className="grid aspect-square w-14 place-items-center rounded-full bg-white">
                <TableDocument className="text-primary" />
              </div>
              <p className="heading-text text-primary">Assessment Packs</p>
              <p className="helper-text text-xs">
                Here you can questions and questions for your regular
                assessments..
              </p>
            </div>
          </button>
          <button
            className="btn-modal-choice"
            onClick={() => {
              setIsInterview(true);
              openStep2Modal();
            }}
          >
            <div className="flex flex-col justify-between gap-2">
              <div className="flex aspect-square w-16 items-center justify-center rounded-full bg-white">
                <Video className="text-primary" />
              </div>
              <p className="heading-text text-primary">Interview Packs</p>
              <p className="helper-text text-xs">
                Here you can questions and questions for your interview
                assessment.
              </p>
            </div>
          </button>
        </div>
      </AltModal>

      <CreateQuestionPackStep2SelectTypeModal
        isCreateQuestionPackModalOpen={isCreateStep2ModalOpen}
        closeCreateQuestionPackModal={closeCreateStep2Modal}
        isInterview={isInterview}
        clientId={clientId}
      />
    </>
  );
};

export default CreateQuestionPackStep1SelectInterviewOrAssessment;
