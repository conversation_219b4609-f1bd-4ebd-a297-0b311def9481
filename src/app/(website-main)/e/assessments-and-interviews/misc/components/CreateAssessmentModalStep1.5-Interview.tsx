import { AltModal } from '@/components/shared';
import React from 'react'
import { LibraryQuestionIcon } from './icons';
import CustomLibraryIcon from '@/components/icons/jsx/CustomLibraryIcon';

interface InitiateInterviewAssessmentModalProps {
  isCreateInterviewAsessmentModalOpen: boolean;
  closeCreateInterviewAssessmentModal: () => void;
  openAssessmentDetailsModal: () => void;
  setIntialAssessmentData: (data: any) => void;
  initialAssessmentData: any;
}

const InitiateInterviewAssessmentModal: React.FC<InitiateInterviewAssessmentModalProps> = ({ isCreateInterviewAsessmentModalOpen, closeCreateInterviewAssessmentModal, openAssessmentDetailsModal, setIntialAssessmentData, initialAssessmentData }) => {
  return (
      <AltModal
          is_open={isCreateInterviewAsessmentModalOpen}
          close={closeCreateInterviewAssessmentModal}
          title="Select Options"
      >
          <div className="grid md:grid-cols-2 gap-4 p-4">
              <button
                  type="button"
                  className="btn-modal-choice  "
                  onClick={() => {
                      setIntialAssessmentData({ ...initialAssessmentData, is_interview: true });
                      openAssessmentDetailsModal();
                  }}
              >
                  <div>
                      <div className="flex aspect-square w-16 items-center justify-center rounded-full bg-white">
                          <LibraryQuestionIcon />
                      </div>
                      <p className="text-[0.9rem] font-semibold mt-2 text-primary"> Getlinked interview questions </p>
                      <p className="helper-text text-xs">
                          Have getlinked provide you with interview questions and have candidates answer with video
                      </p>
                  </div>
              </button>

              <button
                  className="btn-modal-choice"
                  type="button"
                  onClick={() => {
                      setIntialAssessmentData({ ...initialAssessmentData, is_interview: true, is_custom: true });
                      openAssessmentDetailsModal();
                  }}
              >
                  <div>
                      <div className="flex aspect-square w-16 items-center justify-center rounded-full bg-white">
                          <CustomLibraryIcon />
                      </div>
                      <p className="text-[0.9rem] font-semibold mt-2 text-primary"> Custom interview question </p>
                      <p className="helper-text text-xs">
                          Provide your interview questions and have candidates answer with video
                      </p>
                  </div>
              </button>
          </div>
      </AltModal>
  )
}

export default InitiateInterviewAssessmentModal