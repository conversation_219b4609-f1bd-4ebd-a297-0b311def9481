import Link from "next/link";
import React, { useEffect, useState } from "react";
import {
  ROLE_LEVELS_DICTIONARY,
} from "../constants/constants"
import { Assessment } from "../types/create-assessments";
import Button from "@/components/shared/Button";
import PreviewAssessmentSectionsModal from "./PreviewAssessmentSectionsModal";
import { secondsToLargestUnit } from "@/lib/utils/functions";
import { useQuery } from "@tanstack/react-query";
import { fetchRoleAPI } from "@/app/(website-main)/e/assessments-and-interviews/misc/api/Roles";
import { LinkButton } from "@/components/shared";

interface Props {
  assessment: Assessment;
  back_page_url: string;
  next_page_url?: string;
  current_step?: number;
  handleProceed: () => void;
  is_proceeding?: boolean;
}
const TOTAL_STEPS = 3;

const CreateAssessmentHeader: React.FC<Props> = ({
  assessment,
  current_step = 1,
  back_page_url,
  next_page_url,
  handleProceed,
  is_proceeding,
}) => {
  const [questions_count, setQuestionsCount] = useState(0);
  useEffect(() => {
    let count = 0;
    assessment?.sections?.forEach((section) => {
      count += section?.question_set?.length || 0;
    });
    setQuestionsCount(count);
  }, [assessment]);

  const [is_assessment_preview_modal_open, setIsAssessmentPreviewModalOpen] =
    useState(false);
  function openAssessmentPreviewModal() {
    setIsAssessmentPreviewModalOpen(true);
  }
  function closeAssessmentPreviewModal() {
    setIsAssessmentPreviewModalOpen(false);
  }

  const roles_query = useQuery({
    queryKey: ["roles", assessment?.role?.id],
    queryFn: async () => await fetchRoleAPI(assessment.role?.id.toString()),
    enabled: assessment?.role !== undefined
  })

  return (
    <>
      <section className="-mt-3 flex max-md:flex-wrap gap-4 items-center justify-between bg-white px-2 py-4">
        <div className="flex items-start gap-4">
          <LinkButton href={back_page_url} size="capsule" variant="extralight">
            Back
          </LinkButton>
          <div className="space-y-1">
            <h1 className="heading-2 capitalize">{assessment.name}</h1>
            <ul className="heading-text flex gap-2 text-sm capitalize">
              {roles_query.isSuccess && (
                <li className="font-medium">
                  <span className="helper-text max-md:block font-normal">Role: {" "}</span>
                  {roles_query.data.name}
                </li>
              )}
              <li className="font-medium">
                <span className="helper-text max-md:block font-normal">Role level:{" "}</span>
                {ROLE_LEVELS_DICTIONARY[assessment.role_level]?.name}
              </li>
              <li className="font-medium">
                <span className="helper-text max-md:block font-normal">
                  Total questions: {" "}
                </span>
                {questions_count}
              </li>
              <li className="font-medium  normal-case">
                <span className="helper-text max-md:block font-normal">Time:{" "}</span>
                {`${secondsToLargestUnit(assessment?.time_limit).time || 0} ${secondsToLargestUnit(assessment?.time_limit).unit}`}
              </li>
            </ul>
            <div>
              <ul className="mt-2 flex gap-2">
                {[...Array(TOTAL_STEPS)].map((_, index) => (
                  <li
                    key={index}
                    className={`h-2 w-12 rounded-full ${index + 1 <= current_step
                      ? "bg-primary"
                      : "bg-light-accent-bg"
                      }`}
                  >
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
        <div className="flex gap-2 max-md:ml-auto">
          <button
            className="btn-primary-light"
            onClick={openAssessmentPreviewModal}
            type="button"
          >
            Preview test
          </button>
          {next_page_url ? (
            <Link href={next_page_url} className="btn-primary">
              Proceed
            </Link>
          ) : (
            <Button
              is_busy={is_proceeding}
              className="btn-primary"
              onClick={handleProceed}
            >
              Proceed
            </Button>
          )}
        </div>
      </section>
      <PreviewAssessmentSectionsModal
        assessment={assessment}
        is_open={is_assessment_preview_modal_open}
        close={closeAssessmentPreviewModal}
      />
    </>
  );
};
export default CreateAssessmentHeader;
