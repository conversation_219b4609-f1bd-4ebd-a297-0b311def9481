import { CloseCircle } from 'iconsax-react';
import Link from 'next/link';
import React, { FormEvent, useCallback, useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import toast from 'react-hot-toast';
import readXlsxFile from 'read-excel-file';
import { additionalInvitesAPI } from '@/app/(website-main)/e/assessments-and-interviews/misc/api/Invitations';
import {
  AnalyzedFileErrorType,
  AnalyzedFileType,
  Assessment,
  InvitationTypes,
  InviteInfoType,
} from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import UploadIcon from '@/app/(website-main)/t/showcase/misc/components/cv-upload/icons/UploadIcon';
import Modal from '@/components/Modal';
import app_fetch from '@/lib/api/appFetch';
import { formatBytes } from '@/lib/utils/functions';
import { Loader } from '../../../../../../components/shared';
import Button from '../../../../../../components/shared/Button';
import Checkbox from '../../../../../../components/shared/Checkbox/Checkbox';

interface Props {
  assessment_id?: string;
  invitation_type?: InvitationTypes;
  is_open: boolean;
  is_interview?: boolean;
  close: () => void;
  onSuccess?: () => void;
}

const InviteCandidatesModal: React.FC<Props> = ({
  assessment_id,
  invitation_type,
  is_open,
  close,
  onSuccess,
  is_interview = false,
}) => {
  const [candidates, setCandidates] = useState([] as Record<string, string>[]);
  const [selectedMethod, setSelectedMethod] = useState<'email' | 'file'>('email');

  const [is_loading_invite_info, setIsLoadingInviteInfo] = useState(false);
  // @ts-ignore
  const [invite_info, setInviteInfo] = useState(undefined as InviteInfoType);

  function addMoreEmail() {
    setCandidates([...candidates, { name: '', email: '' }]);
  }
  const removeCandidate = (index: number) => {
    let newCandidates = candidates.splice(index, 1);
    setCandidates(newCandidates);
  };

  useEffect(() => {
    if (assessment_id) {
      setIsLoadingInviteInfo(true);
      const endpoint = is_interview
        ? `assessments/assessment-interview/${assessment_id}`
        : `assessments/assessments-detail/${assessment_id}/`;
      app_fetch(endpoint)
        .then(res => res.json())
        .then(result => {
          //   console.log(result.invite_info, 'RESULTADO');
          result.invite_info = {
            ...result.invite_info,
            invitation_type:
              invitation_type || result.invite_info?.invitation_type,
          };
          setInviteInfo(result.invite_info);
          // Always start with email method by default
          addMoreEmail();
        })
        .finally(() => {
          setIsLoadingInviteInfo(false);
        });
    }
  }, [assessment_id]);

  useEffect(() => {
    return () => {
      clearFile();
      setCandidates([]);
    };
  }, [assessment_id]);

  type FileDetailsType = { name: string; size: number };
  const [file_details, setFileDetails] = useState({} as FileDetailsType);
  const [selected_file_fields, setSelectedFileFields] = useState(
    [] as string[]
  );

  const onDrop = useCallback((acceptedFiles: any) => {
    uploadFileForAnalysis(acceptedFiles);
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [],
      'application/vnd.ms-excel': [],
    },
    maxSize: 10 * 1024 * 1024, //10mb
  });

  const [analyzed_file_details, setAnalyzedFileDetails] = useState(
    {} as AnalyzedFileType
  );
  const [analysis_errors, setAnalysisErrors] = useState<
    AnalyzedFileErrorType | undefined
  >(undefined);

  function clearFile() {
    /* setIsBusy(true) */
    setAnalysisErrors(undefined);
    setFileDetails({} as FileDetailsType);
    setAnalyzedFileDetails({} as AnalyzedFileType);
    setSelectedFileFields([]);
  }

  async function uploadFileForAnalysis(acceptedFiles: any) {
    setIsBusy(true);
    setAnalysisErrors(undefined);

    const data = acceptedFiles[0];
    setFileDetails(acceptedFiles[0]);

    const formdata = new FormData();
    formdata.append('file', acceptedFiles[0]);

    const requestOptions = {
      method: 'POST',
      body: formdata,
    };

    app_fetch('assessments/analyze-excel/', requestOptions, false)
      .then(response => response.json())
      .then(result => {
        if (result.error) {
          throw result;
        }
        setAnalyzedFileDetails(result);
        setCandidates(result.names_and_emails);
      })
      .catch(error => {
        setAnalysisErrors(error);
      })
      .finally(() => {
        setIsBusy(false);
      });

    return;
  }

  const [is_busy, setIsBusy] = useState(false);
  async function submitInvitations(e: FormEvent) {
    e.preventDefault();
    if (candidates.length) {
      // Validation for manual entry method
      if (selectedMethod === 'email') {
        if (
          candidates.some(
            candidate =>
              !candidate.name ||
              candidate.name.trim() == '' ||
              !candidate.email ||
              candidate.email.trim() == ''  ||
              !candidate.email.trim().includes('@') ||
              !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(candidate.email)
          )
        ) {
          toast.error('Please fill in a name and valid email for all candidate.');
          return;
        }
      }

      // Validation for file upload method
      if (selectedMethod === 'file') {
        if (selected_file_fields.length !== 2) {
          toast.error('Please select exactly 2 fields for authentication.');
          return;
        }
        if (analysis_errors) {
          toast.error('Please fix the errors in your file before proceeding.');
          return;
        }
      }

      if (invite_info?.invite || invite_info?.invite_id) {
        setIsBusy(true);
        additionalInvitesAPI(
          candidates,
          invite_info?.invite_id || invite_info?.invite,
          is_interview
        )
          .then(result => {
            if (result?.ok) {
              if (onSuccess) {
                onSuccess();
              }
              close();
            }
          })
          .finally(() => {
            setIsBusy(false);
          });
      }
    }
  }

  return (
    <Modal is_open={is_open} title="Invite candidates" close={close}>
      <div className="space-y-4  p-4 text-sm text-[#675E8B]">
        {is_loading_invite_info ? (
          <Loader />
        ) : (
          <>
            <div>
              <h2 className="heading-1">Invite candidates</h2>
              <p className="helper-text text-xs">
                Invite and manage assessment invitations
              </p>
            </div>

            {/* Method selection */}
            <div className="space-y-4">
              <p className="helper-text">
                Choose how you want to invite candidates:
              </p>
              <div className="flex gap-4">
                <button
                  type="button"
                  onClick={() => {
                    setSelectedMethod('email');
                    if (candidates.length === 0) {
                      addMoreEmail();
                    }
                  }}
                  className={`px-4 py-2 rounded-md border transition-colors ${
                    selectedMethod === 'email'
                      ? 'bg-primary text-white border-primary'
                      : 'bg-white text-gray-700 border-gray-300 hover:border-primary'
                  }`}
                >
                  Manual Entry
                </button>
                <button
                  type="button"
                  onClick={() => setSelectedMethod('file')}
                  className={`px-4 py-2 rounded-md border transition-colors ${
                    selectedMethod === 'file'
                      ? 'bg-primary text-white border-primary'
                      : 'bg-white text-gray-700 border-gray-300 hover:border-primary'
                  }`}
                >
                  File Upload
                </button>
              </div>
            </div>

            {/* emails */}
            {selectedMethod === 'email' && (
              <div className="space-y-4 md:w-[680px]">
                <p className="helper-text">
                  Enter candidates name and email to invite them to your
                  assessment?.
                </p>
                <form className="space-y-4" onSubmit={submitInvitations}>
                  {candidates.map((candidate, index) => (
                    <div
                      className="grid gap-4 rounded-md md:grid-cols-2 max-md:border max-md:p-2"
                      key={index}
                    >
                      <label className="space-y-2">
                        <p className="">Name</p>
                        <input
                          type="text"
                          value={candidate.name}
                          onChange={e => {
                            const _candidates = [...candidates];
                            _candidates[index].name = e.target.value;
                            setCandidates(_candidates);
                          }}
                          placeholder="Enter name"
                          className="input-grey"
                        />
                      </label>
                      <div className="flex items-end">
                        <label className="flex-auto space-y-2">
                          <p className="">Email</p>
                          <input
                            type="text"
                            value={candidate.email}
                            onChange={e => {
                              const _candidates = [...candidates];
                              _candidates[index].email = e.target.value;
                              setCandidates(_candidates);
                            }}
                            placeholder="Enter email"
                            className="input-grey"
                          />
                        </label>
                        {candidates.length > 1 && (
                          <button
                            type="button"
                            className="m-4"
                            onClick={() => {
                              removeCandidate(index);
                            }}
                          >
                            <CloseCircle />
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                  <div className="flex items-center justify-between">
                    <button
                      className="btn-primary-light"
                      type="button"
                      onClick={addMoreEmail}
                    >
                      + Add more
                    </button>

                    <Button
                      is_busy={is_busy}
                      className="btn-primary px-20"
                      type="button"
                      onClick={submitInvitations}
                    >
                      Add candidates
                    </Button>
                  </div>
                </form>
              </div>
            )}

            {selectedMethod === 'file' && (
              <div className="w-[490px] space-y-4">
                <p className="helper-text">
                  Here you can invite candidates in bulk by uploading an excel
                  file containing candidate email and other desired details
                </p>
                <div className="flex gap-2">
                  <a
                    className="btn-primary-light"
                    href="/files/assessment-and-interviews/getlinked_sample_invitations_template.xlsx"
                    target="_blank"
                  >
                    Download sample file
                  </a>
                  <Link href="#" className="btn-primary-transparent">
                    Learn more
                  </Link>
                </div>

                {Object.keys(file_details).length < 1 && (
                  <div
                    className={`${
                      false ? 'border border-red-600' : ''
                    } mt-3 flex h-[5.9375rem] max-w-[90%] cursor-pointer items-center gap-4 rounded-[0.875rem] border-[0.3px] border-dashed border-[rgb(117,90,226)] p-6`}
                    {...getRootProps()}
                  >
                    <div className="">
                      <UploadIcon />
                    </div>
                    <div className="">
                      <p className="font-sans text-xs font-semibold text-[#755AE2] sm:text-sm">
                        Tap to upload invite document
                      </p>
                      <span className="font-sans text-xs font-semibold text-[#755AE2] opacity-75">
                        Files types: excel, Max size: 10MB
                      </span>
                    </div>
                    <input hidden {...getInputProps()} />
                  </div>
                )}

                {Object.keys(file_details).length >= 1 && (
                  <>
                    <div className="bg-light-accent-bg max-w-[60%] rounded-md p-4 text-xs">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-2">
                          <div>
                            <img
                              src="/images/icons/excel.png"
                              alt="excel icon"
                            />
                          </div>
                          <div>
                            <p className="max-w-[20ch] overflow-hidden text-ellipsis whitespace-nowrap font-bold">
                              {file_details?.name}
                            </p>
                            <p className="text-[#4E4E4E]">
                              {formatBytes(file_details?.size)}
                            </p>
                          </div>
                        </div>
                        <button onClick={clearFile}>
                          <span className="flex aspect-square w-7 items-center justify-center rounded-full bg-white text-lg">
                            &times;
                          </span>
                        </button>
                      </div>
                    </div>
                    {Object.keys(analyzed_file_details).length > 0 && (
                      <>
                        <ul className="flex max-w-[60%] items-center gap-2 capitalize ">
                          {analyzed_file_details.fields.map((field, index) => (
                            <li
                              key={index}
                              className="bg-light-accent-bg rounded-md p-4 text-center"
                            >
                              <p className="text-xl text-primary">
                                {analyzed_file_details.field_details[field]}
                              </p>
                              <p className="text-xs text-[#3E3873]">{field}</p>
                            </li>
                          ))}
                        </ul>
                        <p className="helper-text">
                          Determine how candidates get to access the
                          assessment?. by selecting from the options below.The
                          default option is ‘Passcode’ as this is mandatory to
                          access assessment?.
                        </p>

                        <div className="flex items-center gap-4">
                          {analyzed_file_details.fields.map((field, index) => (
                            <label
                              key={index}
                              className="bg-light-accent-bg flex items-center gap-2 rounded-md p-4 text-sm"
                            >
                              <Checkbox
                                checked={selected_file_fields?.includes(field)}
                                disabled={
                                  selected_file_fields?.length > 1 &&
                                  !selected_file_fields.includes(field)
                                }
                                onChange={e => {
                                  if (
                                    selected_file_fields?.includes(
                                      e.target.value
                                    )
                                  ) {
                                    const _selected =
                                      selected_file_fields.filter(
                                        field => field != e.target.value
                                      );
                                    setSelectedFileFields(_selected);
                                  } else {
                                    setSelectedFileFields([
                                      ...selected_file_fields,
                                      e.target.value,
                                    ]);
                                  }
                                }}
                                name="auth fields"
                                value={field}
                              />
                              {field}
                            </label>
                          ))}
                        </div>

                        {analysis_errors && (
                          <div className="rounded-md bg-red-200/20 p-2 text-sm text-red-500">
                            <p className="font-bold">{analysis_errors.error}</p>
                            <ul>
                              {Object.keys(analysis_errors.details).map(
                                (row, index) => {
                                  const error = analysis_errors.details[row];
                                  return (
                                    <div className="pl-2" key={index}>
                                      <p>
                                        {' '}
                                        {row}: {error.email || '?'} -{' '}
                                        {error.error}
                                      </p>
                                    </div>
                                  );
                                }
                              )}
                            </ul>
                          </div>
                        )}
                      </>
                    )}
                  </>
                )}
                <Button
                  is_busy={is_busy}
                  onClick={submitInvitations}
                  disabled={
                    selectedMethod === 'file'
                      ? (selected_file_fields.length > 0 &&
                          selected_file_fields.length !== 2) ||
                        !!analysis_errors ||
                        !candidates.length
                      : !candidates.length
                  }
                  className={`px-20 ${
                    (selectedMethod === 'file'
                      ? (selected_file_fields.length > 0 &&
                          selected_file_fields.length !== 2) ||
                        !!analysis_errors ||
                        !candidates.length
                      : !candidates.length)
                      ? 'btn-primary-bordered'
                      : 'btn-primary'
                  }`}
                  type="button"
                >
                  Add candidates
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </Modal>
  );
};
export default InviteCandidatesModal;
