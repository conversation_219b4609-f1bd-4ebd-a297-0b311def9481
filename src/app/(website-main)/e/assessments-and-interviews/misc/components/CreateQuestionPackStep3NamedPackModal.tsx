import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  AltButton,
  AltModal,
  Input,
  LoaderBtn,
  Textarea,
} from '@/components/shared';
import { Axios } from '@/lib/api/axios';
import { Question } from '../types/create-assessments';

export interface TAssessmentQuestionPack {
  id: string;
  question_set: Question[];
  name: string;
  description: string;
  total_questions: number;
  is_intervew: false;
  recruiter: number;
  clientId: string;
}

const schema = z.object({
  name: z
    .string({ message: 'Enter pack name' })
    .min(3, { message: 'Enter at least 3 characters' }),
  description: z
    .string({ message: 'Enter pack description' })
    .min(10, { message: 'Enter at least 10 characters' }),
});

type formType = z.infer<typeof schema>;

interface CreateNamedPackModalProps {
  isCreateNamedPackModalOpen: boolean;
  closeCreateNamedPackModal: () => void;
  isInterview: boolean;
  clientId?: string;
}

const CreateNamedPackModal = ({
  isCreateNamedPackModalOpen,
  isInterview,
  closeCreateNamedPackModal,
  clientId,
}: CreateNamedPackModalProps) => {
  const router = useRouter();
  const {
    handleSubmit,
    register,
    formState: { errors },
    getValues,
    trigger,
  } = useForm<formType>({
    resolver: zodResolver(schema),
  });
  const fn = async (data: formType) => {
    const endpoint = isInterview
      ? '/assessments/interview-packs/'
      : '/assessments/question-packs/';
    const response = await Axios.post(endpoint, {
      ...data,
      ...(clientId ? { clientId } : {}),
    });
    return response.data as TAssessmentQuestionPack;
  };
  const { mutate: createPack, isLoading: iscCreatingPack } = useMutation({
    mutationFn: fn,
  });

  const submitForm = async (data: formType) => {
    const isValid = await trigger();
    if (!isValid) return;
    createPack(
      {
        ...data,
        ...(clientId ? { clientId } : {}),
      },
      {
        onSuccess(data) {
          if (isInterview) {
            router.push(
              `/e/assessments-and-interviews/interview-pack/${data.id}?new=true`
            );
          } else {
            router.push(
              `/e/assessments-and-interviews/question-pack/${data.id}?new=true`
            );
          }
        },
      }
    );
  };

  return (
    <AltModal
      is_open={isCreateNamedPackModalOpen}
      title="Create question pack"
      close={closeCreateNamedPackModal}
      is_close_by_button_only={true}
    >
      <div className="">
        <form
          className="bg-light-accent-bg max-w-[450px]  space-y-4 bg-[#F5F3FF] p-4 py-4 text-center text-sm text-[#675E8B] md:w-[547px]"
          onSubmit={handleSubmit(submitForm)}
        >
          <div>
            <label className="space-y-1 text-left">
              <p className="heading-text font-medium">Name of pack</p>
              <Input
                variant="showcase"
                type="text"
                placeholder="Enter pack name"
                hasError={!!errors.name}
                errorMessage={errors.name?.message}
                {...register('name')}
              />
            </label>
          </div>
          <div>
            <label className="space-y-1 text-left">
              <p className="heading-text font-medium">Description</p>
              <Textarea
                variant="showcase"
                {...register('description')}
                className="input-white min-h-[150px] resize-none"
                placeholder="Enter pack description"
                hasError={!!errors.description}
                errorMessage={errors.description?.message}
              />
            </label>
          </div>
          <div className="mt-4 flex items-center gap-2">
            <AltButton
              type="button"
              onClick={closeCreateNamedPackModal}
              className="btn-primary-bordered h-10 w-full"
            >
              Cancel
            </AltButton>
            <button
              type="submit"
              className="btn-primary flex h-10 w-full items-center justify-center gap-4"
            >
              Proceed
              {iscCreatingPack && <LoaderBtn />}
            </button>
          </div>
        </form>
      </div>
    </AltModal>
  );
};

export default CreateNamedPackModal;
