'use client'

import React from 'react';
/* import { QUESTION_TYPES_DICTIONARY } from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants'; */
import { useFetchCandidateSectionAnswers } from '../../assessments/[assessment_id]/candidates/[candidate_email]/api/queries';
import Disclosure from '@/components/Disclosure';

interface Props {
  index: number;
  assessment_id: string;
  candidate_email: string;
  section_id: string;
  children: React.ReactNode
}

const QuestionsListItem: React.FC<Props> = ({
  assessment_id,
  candidate_email,
  section_id,
  index,
  children,
}) => {

  const { data, isSuccess, isLoading, isError, error } = useFetchCandidateSectionAnswers(
    candidate_email,
    assessment_id,
    section_id,
  );

  return (
    <Disclosure title={children} >
      {isLoading && (
        <p>loading answers</p>
      )}
      {isError && (
        <p>error loading answers</p>
      )}

      {isSuccess && (
        <div className="">
          <ul className="space-y-1">
            {data.map(
              (question, question_index) => (
                <li key={question_index} className='space-y-4 divide-y rounded-xl border p-4'>
                  <div className="space-y-2">
                    <h3 className='heading-text text-md'>
                      Question {question_index + 1}
                    </h3>
                    <div
                      className='tiptap ProseMirror !text-sm'
                      dangerouslySetInnerHTML={{ __html: question.question_body }}
                    />
                  </div>
                  <div className="space-y-2 pt-4">
                    <h3 className='heading-text text-md'>
                      Answer
                    </h3>
                    <div className={`rounded-xl tiptap  p-4 ${question.is_correct ? 'bg-green-500/10 text-green-500' : 'bg-red-500/20 text-red-500'}`}
                      dangerouslySetInnerHTML={{ __html: question.answer[0] || "__" }}
                    >
                    </div>
                    <div className='rounded-xl p-4 bg-primary/5'>
                      <h3 className="heading-text text-md space-y-2">
                        Score: {question.score || 0}
                      </h3>
                    </div>
                    {question.ai_comments && (
                      <div className='rounded-xl p-4 bg-primary/5'>
                        <h3 className="heading-text text-md space-y-2">
                          Reason for score
                        </h3>
                        <p>
                          {question.ai_comments}
                        </p>
                      </div>
                    )}
                  </div>
                </li>
              ),
            )}
          </ul>
        </div>
      )}
    </Disclosure>

  );
};
export default QuestionsListItem;
