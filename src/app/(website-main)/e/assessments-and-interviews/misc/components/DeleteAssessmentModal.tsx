import React, { FormEvent, useState } from "react";
import Modal from "@/components/Modal";
import Button from "../../../../../../components/shared/Button";
import DeleteSectionIcon from "@/components/icons/jsx/DeleteSectionIcon.jsx";
import app_fetch from "@/lib/api/appFetch";

interface Props {
  is_open: boolean;
  close: () => void;
  /* assessment_id: string; */
  assessment_id?: string;
  onComplete: () => void;
}

const DeleteAssessmentModal: React.FC<Props> = ({
  is_open,
  close,
  /* assessment_id */
  assessment_id,
  onComplete
}) => {
  const [is_busy, setIsBusy] = useState(false);
  async function performAction(e: FormEvent) {
    e.preventDefault();
    if (assessment_id) {
      const options = {
        method: "DELETE",
      }

      setIsBusy(true);
      await app_fetch(`assessments/${assessment_id}/delete/`, options)
        .then(() => {
          onComplete()
        })
        .finally(() => {
          setIsBusy(false);
          close();
        });
    }
  }

  return (
    <Modal
      is_open={is_open}
      title="Delete Assessment?"
      close={close}
    >
      <div className="md:w-[466px] space-y-2 bg-[#F5F3FF] text-center p-4 text-sm text-[#675E8B]">
        <div className="flex flex-col gap-2 items-center py-4 px-12">
          <DeleteSectionIcon />
          <h2 className="heading-2 text-primary">
            Delete assessment
          </h2>
          <p>
            Are you sure you want to delete this assessment ?
          </p>
        </div>
      </div>
      <div className="rounded-xl bg-white p-4">
        <div className="flex items-center justify-end gap-2">
          <button
            className="btn-primary-light"
            onClick={close}
            type="button"
          >
            No
          </button>
          <Button
            className="btn-primary"
            is_busy={is_busy}
            onClick={performAction}
          >
            Yes, delete
          </Button>
        </div>
      </div>
    </Modal>
  );
};
export default DeleteAssessmentModal;
