import DropdownMenu from "@/components/DropdownMenu";
import EllipsesVerticalIcon from "@/components/icons/jsx/EllipsesVerticalIcon";
import { QUESTION_OPTIONS, QUESTION_TYPES_DICTIONARY } from "../constants/constants";
import { Question } from "../types/create-assessments";

interface Props {
  index: number;
  callback: (option: any) => void;
  question: Question;
}

const Card: React.FC<Props> = ({ index, callback, question }) => {
  return (
    <div
      className="rounded-xl outline-primary hover:outline bg-white flex-1"
    >
      <div className="space-y-2 rounded-md bg-white p-4">
        <div className="item-center flex justify-between">
          <h2 className="heading-2">Question {index + 1}</h2>
          <DropdownMenu
            options={QUESTION_OPTIONS}
            readable_text_key="title"
            callback={(_selected_option) => {
              callback(_selected_option);
            }}
            button={
              <button
                type="button"
                className="btn-base"
                title="view options"
              >
                <EllipsesVerticalIcon />
              </button>
            }
          />
        </div>
        <p className="heading-text">
          <span className="helper-text font-normal">
            Question type:
          </span>{" "}
          {QUESTION_TYPES_DICTIONARY[question?.type]?.name}
        </p>
        <p className="text-xs">{question?.question}</p>
      </div>
    </div>
  )
}

export default Card
