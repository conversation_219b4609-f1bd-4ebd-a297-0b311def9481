import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { MoneyArchive, ReceiptEdit, Sms } from 'iconsax-react';
import Link from 'next/link';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { AltButton, AltModal, Input, Textarea } from '@/components/shared';
import { useBooleanStateControl } from '@/hooks';
import { Axios } from '@/lib/api/axios';
import CreateNamedPackModal from './CreateQuestionPackStep3NamedPackModal';

const schema = z.object({
  name: z
    .string({ message: 'Enter pack name' })
    .min(3, { message: 'Enter at least 3 characters' }),
  description: z
    .string({ message: 'Enter pack description' })
    .min(10, { message: 'Enter at least 10 characters' }),
});

type formType = z.infer<typeof schema>;

interface CreateQuestionPackModalProps {
  isCreateQuestionPackModalOpen: boolean;
  closeCreateQuestionPackModal: () => void;
  isInterview: boolean;
  clientId?: string;
}

const CreateQuestionPackStep2SelectTypeModal = ({
  isCreateQuestionPackModalOpen,
  isInterview,
  closeCreateQuestionPackModal,
  clientId,
}: CreateQuestionPackModalProps) => {
  const {
    handleSubmit,
    register,
    formState: { errors },
  } = useForm<formType>({
    resolver: zodResolver(schema),
  });

  const {
    state: isCreateNamedPackModalOpen,
    setTrue: openNamedPackModal,
    setFalse: closeCreateNamedPackModal,
  } = useBooleanStateControl();

  return (
    <>
      <AltModal
        is_open={isCreateQuestionPackModalOpen}
        close={closeCreateQuestionPackModal}
        title="options to continue"
      >
        <div className="flex justify-center gap-2 px-6 py-4">
          <button className="btn-modal-choice" onClick={openNamedPackModal}>
            <div className="flex flex-col justify-between gap-2">
              <div className="grid aspect-square w-14 place-items-center rounded-full bg-white">
                <MoneyArchive className="text-primary" />
              </div>
              <p className="heading-text text-primary">Named pack</p>
              <p className="helper-text text-xs">
                Here you can name a pack and have several question types in it.
              </p>
            </div>
          </button>
          <Link
            className="btn-modal-choice"
            href={`/e/assessments-and-interviews/create-questions`}
          >
            <div className="flex flex-col justify-between gap-2">
              <div className="flex aspect-square w-16 items-center justify-center rounded-full bg-white">
                <ReceiptEdit className="text-primary" />
              </div>
              <p className="heading-text text-primary">My Questions</p>
              <p className="helper-text text-xs">
                Here you can create a question without having it in a specific
                pack.
              </p>
            </div>
          </Link>
        </div>
      </AltModal>

      <CreateNamedPackModal
        closeCreateNamedPackModal={closeCreateNamedPackModal}
        isCreateNamedPackModalOpen={isCreateNamedPackModalOpen}
        isInterview={isInterview}
        clientId={clientId}
      />
    </>
  );
};

export default CreateQuestionPackStep2SelectTypeModal;
