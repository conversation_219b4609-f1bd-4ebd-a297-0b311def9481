'use client'

import React, { FormEvent, useEffect, useState } from 'react';
import Modal from '@/components/Modal';
import { InterviewPackQuestion, Question } from '../types/create-assessments';
import ListBox from '@/components/shared/listbox';
import Button from '../../../../../../components/shared/Button';
import TimeInput from './TimeInput';
import { useGetQuestionsSections } from '../../interview-pack/misc/api';

interface Props {
  is_open: boolean;
  is_busy?: boolean;
  close: () => void;
  question?: InterviewPackQuestion;
  handleSubmit: (question: InterviewPackQuestion) => void;
}

const AddCustomInterviewQuestionModal: React.FC<Props> = ({
  is_open,
  is_busy,
  close,
  handleSubmit,
  question = { time: 300 } as InterviewPackQuestion
}) => {
  const initial_question = question
  function _close() {
    setNewQuestion({ ...initial_question });
    close();
  }
  const [new_question, setNewQuestion] = useState({ ...initial_question });

  useEffect(() => {
    setNewQuestion({ ...question });
  }, [is_open])

  function submitForm(event: FormEvent) {
    event.preventDefault();

    handleSubmit(new_question);
    _close();
  }
  const { data: questionSections, isLoading: isGettingSections } =
    useGetQuestionsSections();


  return (
    <Modal is_open={is_open} close={_close} title={`${initial_question.id ? 'Edit' : 'Add a new'} question`}>
      <form className="default-text-styles" onSubmit={submitForm}>
        <div className="md:w-[520px] p-4 text-sm">
          <div className="space-y-6">
            <div>
              <label className="space-y-2">
                <p>Question category</p>
              </label>
              <ListBox
                className="bg-grey"
                options={questionSections?.results.
                  map(section => ({ id: section.id, name: section.name })) || []}
                active_option={new_question.section as any}
                setActiveOption={(section) => {
                  setNewQuestion({
                    ...new_question,
                    section,
                  });
                }}
                value_key="id"
                readable_text_key="name"
              />
            </div>

            <div>
              <label className="space-y-2">
                <p>Enter question</p>
              </label>

              <textarea
                required
                placeholder="Enter question here"
                className="input-grey min-h-[200px]"
                name="question"
                value={new_question.question}
                onChange={event => {
                  setNewQuestion({
                    ...new_question,
                    question: event.target.value,
                  });
                }}
              />
            </div>
          </div>

          <div>
            <label className="">
              <p>Time</p>
              <TimeInput value={new_question.time} onChange={(val) => { setNewQuestion({ ...new_question, time: val }) }} />
            </label>
          </div>

          <Button is_busy={is_busy} className="btn-primary w-full" type="submit">
            Save question
          </Button>
        </div>
      </form>
    </Modal>
  );
};
export default AddCustomInterviewQuestionModal;
