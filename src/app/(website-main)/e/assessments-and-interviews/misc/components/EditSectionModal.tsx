import React, { FormEvent, useEffect, useState } from "react";
import EllipsesVerticalIcon from "@/components/icons/jsx/EllipsesVerticalIcon";
import Modal from "@/components/Modal";
import { QUESTION_TYPES_DICTIONARY } from "@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants";
import { Section } from "@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments";
import Button from "../../../../../../components/shared/Button";

interface Props {
  is_open: boolean;
  is_busy?: boolean;
  close: () => void;
  section: Section;
  handleSubmit: (section: Section) => void;
}

const EditSectionModal: React.FC<Props> = ({
  is_open,
  is_busy,
  close,
  section,
  handleSubmit,
}) => {
  const [edited_section, setEditedSection] = useState({} as Section);

  useEffect(() => {
    const _section = section;
    _section?.question_types?.map((_question_type) => {
      const count = section?.question_set?.filter(
        (question) => question.type === _question_type,
      ).length;
      // @ts-ignore
      _section[QUESTION_TYPES_DICTIONARY[_question_type]?.count_name] = count;
    });
    setEditedSection({ ..._section });
  }, [section]);

  function submitForm(event: FormEvent) {
    event.preventDefault();
    handleSubmit(edited_section);
  }

  return (
    <Modal title="Edit section" close={close} is_open={is_open}>
      <form
        className="bg-light-accent-bg md:w-[447px] space-y-6 p-4 text-sm"
        onSubmit={submitForm}
      >
        <div>
          <label className="space-y-1">
            <p>Section name</p>
            <input
              type="text"
              value={edited_section?.section_name}
              className="input-white"
              onChange={(event) => {
                setEditedSection({
                  ...edited_section,
                  section_name: event.target.value,
                });
              }}
            />
          </label>
        </div>
        <div className="space-y-1">
          <p>Question types</p>
          <ul className="space-y-1 p-4 text-left capitalize text-primary">
            {edited_section?.question_types?.map(
              (_question_type, _question_type_index) => (
                <li
                  key={_question_type_index}
                  className="flex items-center justify-between gap-4"
                >
                  <div className="input-white flex items-center justify-between">
                    <p>{QUESTION_TYPES_DICTIONARY[_question_type]?.name}</p>
                    <div className="flex items-center gap-1">
                      <p className="text-xs">No of Questions:</p>
                      <input
                        type="number"
                        className="input-grey max-w-[6ch] px-[2ch] py-2"
                        placeholder="input number of questions"
                        onChange={(e) => {
                          const key = QUESTION_TYPES_DICTIONARY[_question_type]
                            ?.count_name;
                          setEditedSection(
                            {
                              ...edited_section,
                              [key]: parseInt(e.target.value),
                            },
                          );
                        }}
                        // @ts-ignore
                        value={edited_section[QUESTION_TYPES_DICTIONARY[_question_type]?.count_name]}
                      />
                      <button
                        className="btn-base"
                        type="button"
                        title="view options"
                      >
                        <EllipsesVerticalIcon />
                      </button>
                    </div>
                  </div>
                </li>
              ),
            )}
          </ul>
        </div>
        <Button is_busy={is_busy} type="submit" className="btn-primary w-full">
          Save
        </Button>
      </form>
    </Modal>
  );
};
export default EditSectionModal;
