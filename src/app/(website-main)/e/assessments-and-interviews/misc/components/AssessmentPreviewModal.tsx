import React, { FormEvent, useEffect, useState } from 'react';
import Modal from '@/components/Modal';
import { QUESTION_TYPES_DICTIONARY } from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants';
import { LibraryAssessment, Question } from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';

interface Props {
  is_open: boolean;
  close: () => void;
  assessment: LibraryAssessment | undefined;
}

const AssessmentPreviewModal: React.FC<Props> = ({
  is_open,
  close,
  assessment,
}) => {

  const [question_type_filter, setQuestionTypeFilter] = useState<string | undefined>()
  const [filtered_questions, setFilteredQuestions] = useState<Question[]>([])
  useEffect(() => {
    if (assessment) {
      setFilteredQuestions(
        assessment.question_set.filter((question: Question) => {
          let should_show = true
          if (question_type_filter) {
            should_show = should_show && question?.type.toLowerCase() === question_type_filter.toLowerCase()
          }
          return should_show
        })
      )
    }
  }, [assessment, question_type_filter])

  console.log(assessment, "PREVIEW")
  return (
    <Modal
      title="Assessment preview"
      is_open={is_open}
      close={close}
    >
      {
        assessment == undefined ?
          <p className='md:w-[768px]  p-5'>No questions available to preview</p>
          :
          <div className="md:w-[768px] p-4">
            <div className="space-y-4">
              <h3>Title: {assessment?.name}</h3>
              <ul className="flex flex-wrap gap-2">
                {assessment?.question_types?.map(
                  (question_type: string, index) => (
                    <li>
                      <label
                        className={`btn-primary-light block ${question_type.toLowerCase() === question_type_filter
                          ? "transition-default outline outline-1 outline-primary "
                          : "cursor-pointer"
                          }`}
                      >
                        <input
                          type="radio"
                          value={question_type.toLowerCase()}
                          onChange={(e) =>
                            setQuestionTypeFilter(e.target.value)}
                          name="question type filter"
                          className="hidden"
                        />
                        {QUESTION_TYPES_DICTIONARY[question_type]?.name}
                      </label>
                    </li>
                  ),
                )}
              </ul>
              <ul className="space-y-4">
                {
                  filtered_questions.map((question, index) => (
                    <div className="border p-4 space-y-2 rounded-xl">
                      <h3>Question {index + 1}</h3>
                      <p>{question.question}</p>

                      <hr />

                      {/* Multi choice questions */}
                      {question?.type ===
                        QUESTION_TYPES_DICTIONARY['multiple_choice'].value && (
                          <div>
                            <ol>
                              {question?.answer_options?.map((option, index) => (
                                <li key={index}>
                                  <label className="flex items-center gap-2" >
                                    <input type="radio" name={question.id} />
                                    {option}
                                  </label>
                                </li>
                              ))}
                            </ol>
                          </div>
                        )}
                      {/* Multi response questions */}
                      {question?.type ===
                        QUESTION_TYPES_DICTIONARY['multiple_response'].value && (
                          <div>
                            <ol >
                              {question?.answer_options?.map((option, index) => (
                                <li key={index}>
                                  <label className="flex items-center gap-2" >
                                    <input type="checkbox" name={question.id} />
                                    {option}
                                  </label>
                                </li>
                              ))}
                            </ol>
                          </div>
                        )}
                      {/* true or false questions */}
                      {question?.type ===
                        QUESTION_TYPES_DICTIONARY['true_false'].value && (
                          <div>
                            <ol >
                              {"true, false".split(", ").map((option, index) => (
                                <li key={index}>
                                  <label className="flex capitalize items-center gap-2" >
                                    <input type="radio" value={option} name={question.id} />
                                    {option}
                                  </label>
                                </li>
                              ))}
                            </ol>
                          </div>
                        )}
                      {/* fill in the blanks questions */}
                      {question?.type ===
                        QUESTION_TYPES_DICTIONARY['fill_in_the_blanks'].value && (
                          <div>
                            <ol >
                              {question?.answer_options?.map((option, index) => (
                                <li key={index}>
                                  <label className="flex items-center gap-2" >
                                    <input type="radio" value={option} name={question.id} />
                                    {option}
                                  </label>
                                </li>
                              ))}
                            </ol>
                          </div>
                        )}
                      {/* essay questions */}
                      {question?.type === QUESTION_TYPES_DICTIONARY['essay'].value && (
                        <textarea placeholder="Write your answer here" className="input-grey" />
                      )}
                    </div>
                  ))
                }
              </ul>


            </div>
          </div>
      }
    </Modal>
  )
}

export default AssessmentPreviewModal
