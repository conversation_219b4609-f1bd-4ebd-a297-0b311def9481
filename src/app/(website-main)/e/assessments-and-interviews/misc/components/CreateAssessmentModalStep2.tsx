import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  AltModal,
  Button,
  ErrorModal,
  Input,
  LoaderBtn,
} from '@/components/shared';
import FormError from '@/components/shared/form-error';
import ListBox from '@/components/shared/listbox';
import { useErrorModalState } from '@/hooks';
import { Axios } from '@/lib/api/axios';
import useUserStore from '@/store/userInfoStore';
import { formatAxiosErrorMessage } from '@/utils/errors';
import useAssessmentCreatorStore from '../../create-assessment/misc/store';
import useInterviewCreatorStore from '../../create-interview/misc/store';
import { BETA_TESTER_OPTION, ROLE_LEVELS } from '../constants/constants';
import { useCreateAssessmentStore } from '../store/createAssessmentStore';
import { Assessment } from '../types/create-assessments';
import RoleSelect from './RoleSelect';

const assessmentFormSchema = z.object({
  name: z
    .string()
    .min(1, 'Name is required')
    .max(100, 'Name must be less than 100 characters'),
  role: z.string().min(1, 'Role is required'),
  experience_level: z.string().min(1, 'Role level is required'),
  is_beta_tester_assessment: z.string().optional(),
});

type AssessmentFormData = z.infer<typeof assessmentFormSchema>;

interface CreateAssessmentDetailsModalProps {
  isAssessmentDetailsModalOpen: boolean;
  closeAssessmentDetailsModal: () => void;
  initialAssessmentData: any;
  clientId2?: string;
}

const CreateAssessmentDetailsModal: React.FC<
  CreateAssessmentDetailsModalProps
> = ({
  isAssessmentDetailsModalOpen,
  closeAssessmentDetailsModal,
  initialAssessmentData,
  clientId2 = '',
}) => {
  const router = useRouter();
  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors, isSubmitting },
    watch,
  } = useForm<AssessmentFormData>({
    resolver: zodResolver(assessmentFormSchema),
    defaultValues: {
      name: initialAssessmentData.name || '',
      role: initialAssessmentData.role || '',
      experience_level: initialAssessmentData.experience_level || '',
      is_beta_tester_assessment: 'no',
    },
  });

  type TDTOData = {
    name: string;
    role: string;
    role_level: string;
    is_custom: boolean;
    is_beta_tester_assessment?: boolean;
  };
  const setAssessmentID = useCreateAssessmentStore(state => state.setId);
  const setAssessmentIsAutomaticallyGenerated = useCreateAssessmentStore(
    state => state.setIsAutomaticallyGenerated
  );
  const setAssessmentShouldOpenFirstQuestionModal = useCreateAssessmentStore(
    state => state.setAssessmentShouldOpenFirstQuestionModal
  );
  const { setId, clearStore: clearAssessmentCreatorStore } =
    useAssessmentCreatorStore();
  const { setId: setInterviewId, clearStore: clearInterviewCreatorStore } =
    useInterviewCreatorStore();
  const { getUser: getSavedUser } = useUserStore();

  const { mutate: createAssessment, isLoading: isCreatingAssessment } =
    useMutation({
      mutationFn: async (data: TDTOData) => {
        const endpoint = initialAssessmentData.is_interview
          ? `assessments/assessment-interview/`
          : 'assessments/create/';
        const res = await Axios.post(endpoint, data);
        return res.data as Assessment;
      },
    });

  const searchParams = useSearchParams();
  const clientId = searchParams.get('clientId') || '';

  const onSubmitHandler = async (data: AssessmentFormData) => {
    const dataToSend = {
      name: data.name,
      role: data.role,
      role_level: data.experience_level,
      is_custom: initialAssessmentData.is_custom,
      client: clientId ? clientId : clientId2,
      is_beta_tester_assessment:
        data.is_beta_tester_assessment &&
        data.is_beta_tester_assessment.toLowerCase() == 'yes'.toLowerCase()
          ? true
          : false,
    };

    createAssessment(dataToSend, {
      onSuccess: data => {
        console.log(data);
        if (initialAssessmentData.is_interview) {
          clearInterviewCreatorStore();
          setInterviewId(data.id);
          router.push(
            `/e/assessments-and-interviews/create-interview/${data.id}`
          );
          return;
        } else {
          clearAssessmentCreatorStore();
          setId(data.id);
          router.push(
            `/e/assessments-and-interviews/create-assessment/${data.id}`
          );
          return;
        }
      },
      onError: error => {
        const errorMessage = formatAxiosErrorMessage(error as any);
        openErrorModalWithMessage(errorMessage);
      },
    });
  };

  return (
    <>
      <AltModal
        title={`Create new ${
          initialAssessmentData.is_interview ? 'interview' : 'assessment'
        }`}
        close={closeAssessmentDetailsModal}
        is_open={isAssessmentDetailsModalOpen}
      >
        <form
          className="bg-primary-light p-6 text-sm md:min-w-[450px]"
          onSubmit={handleSubmit(onSubmitHandler)}
        >
          <div className="inputdiv">
            <label className="space-y-1">
              <p className="heading-text font-medium">
                Name this{' '}
                {initialAssessmentData.is_interview
                  ? 'interview'
                  : 'assessment'}
              </p>
              <Input
                {...register('name')}
                type="text"
                hasError={!!errors.name}
                errorMessage={errors.name?.message}
                placeholder={`Enter ${
                  initialAssessmentData.is_interview
                    ? 'interview'
                    : 'assessment'
                } name`}
              />
            </label>
          </div>

          <RoleSelect
            value={watch('role')}
            onChange={(role: string) =>
              setValue('role', role, {
                shouldValidate: true,
              })
            }
            hasError={!!errors.role}
            errorMessage={errors.role?.message}
          />
          {errors.role && <FormError errorMessage={errors.role.message} />}

          <div className="inputdiv">
            <label>Role level</label>
            <ListBox
              active_option={watch('experience_level')}
              setActiveOption={(roleLevel: string) => {
                setValue('experience_level', roleLevel, {
                  shouldValidate: true,
                });
              }}
              options={ROLE_LEVELS}
              placeholder="Select an option"
              value_key="id"
              readable_text_key="name"
              className="text-[0.8125rem]"
              itemClass="text-[0.8125rem] !overflow-hidden"
              triggerClass="text-body-text"
            />
            {errors.experience_level && (
              <FormError errorMessage={errors.experience_level.message} />
            )}
          </div>

          {getSavedUser()?.user.is_superuser && (
            <div className="inputdiv">
              <label>Beta tester assessment (Optional)</label>
              <ListBox
                active_option={watch('is_beta_tester_assessment')}
                setActiveOption={(betaTester: string) => {
                  setValue('is_beta_tester_assessment', betaTester);
                }}
                options={BETA_TESTER_OPTION}
                placeholder="Select an option"
                value_key="id"
                readable_text_key="name"
                className="text-[0.8125rem]"
                itemClass="text-[0.8125rem] !overflow-hidden"
                triggerClass="text-body-text"
              />
              {errors.is_beta_tester_assessment && (
                <FormError
                  errorMessage={errors.is_beta_tester_assessment.message}
                />
              )}
            </div>
          )}
          <Button
            type="submit"
            disabled={isSubmitting || isCreatingAssessment}
            className="mt-8 flex w-full items-center gap-2"
          >
            Proceed
            {isSubmitting || (isCreatingAssessment && <LoaderBtn />)}
          </Button>
        </form>
      </AltModal>

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={errorModalMessage || 'Something went wrong.'}
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 hover:border-red-950 hover:text-red-950 sm:text-sm md:px-6"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </>
  );
};

export default CreateAssessmentDetailsModal;
