import React, { ReactNode } from 'react';
import { QUESITON_TYPES } from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants';
import QuestionTypeIcon from '../../../../../../components/icons/jsx/QuestionTypeIcon';
import Modal from '../../../../../../components/Modal';

interface ModalProps {
  is_open: boolean;
  //   title: string;
  close: () => void;
  callback: (question_type: string) => void;
}

const QuestionTypeModal: React.FC<ModalProps> = ({
  is_open,
  //   title,
  close,
  callback,
}) => {
  return (
    <Modal is_open={is_open} close={close} title="Questions types">
      <div className="grid grid-cols-2  md:grid-cols-4 justify-center gap-2 p-4 ">
        {QUESITON_TYPES.map((item, index) =>
          "multiple_response, multiple_choice, essay, fill_in_the_blanks, true_false".split(", ").includes(item.value) ? (
            <button
              key={index}
              type="button"
              className="btn-modal-choice text-center"
              onClick={() => callback(item.value)}
            >
              <div className="flex flex-col items-center space-y-1">
                <div className="flex aspect-square w-16 items-center justify-center rounded-full bg-white">
                  <QuestionTypeIcon question_type={item.value} is_large={true} />
                </div>
                <p className="heading-text text-sm font-normal capitalize">
                  {item.name}
                </p>
              </div>
            </button>
          )
            : (
              <div
                key={index}
                className="btn-modal-choice hover:outline-none cursor-auto text-center relative group/btn"
              >
                <div className="flex flex-col items-center space-y-1">
                  <div className="flex aspect-square w-16 items-center justify-center rounded-full bg-white">
                    <QuestionTypeIcon question_type={item.value} is_large={true} />
                  </div>
                  <p className="heading-text text-sm font-normal capitalize">
                    {item.name}
                  </p>
                </div>
                <p className='absolute inset-0 group-hover/btn:grid place-items-center hidden'>
                  <span className="btn-base bg-[#F1EFFC] cursor-auto drop-shadow-md">Coming soon</span>
                </p>
              </div>
            )
        )}
      </div>
    </Modal>
  );
};
export default QuestionTypeModal;
