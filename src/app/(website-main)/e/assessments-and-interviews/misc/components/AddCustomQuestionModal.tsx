import React, { FormEvent, useCallback, useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { toast } from 'react-hot-toast';
import {
  ALPHABETS,
  MULTIPLE_CHOICE_OPTIONS_LIMIT,
  MULTIPLE_CHOICE_OPTIONS_MIN_LIMIT,
  TRUE_OR_FALSE,
} from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants';
import CloseCircleIcon from '@/components/icons/CloseCircleIcon';
import Modal from '@/components/Modal';
import Checkbox from '@/components/shared/Checkbox/Checkbox';
import ListBox from '@/components/shared/listbox';
import ListBoxStrings from '@/components/shared/listbox-strings';
import Button from '../../../../../../components/shared/Button';
import { UploadIcon } from '../../../jobs/misc/icons';
import { CreateQuestion, Question } from '../types/create-assessments';
import { RichTextEditor } from '@/components/shared';
import { useGetQuestionsSections } from '../../interview-pack/misc/api';

interface Props {
  is_open: boolean;
  is_busy?: boolean;
  close: () => void;
  question_type?: string;
  handleSubmit: (question: CreateQuestion) => void;
}
export const validateImageDimensions = async (
  imageURL: string,
  dimensions: { height: number; width: number }[]
) => {
  return new Promise((resolve, reject) => {
    let img = new Image();
    img.onload = function () {
      let isWithinRange = false;
      // alert(img.width + ' ' + img.height);
      dimensions.forEach(d => {
        if (d.height == img.height && d.width == img.width) {
          isWithinRange = true;
        }
      });
      resolve(isWithinRange);
    };
    img.src = imageURL;
  });
};
const AddCustomQuestionModal: React.FC<Props> = ({
  is_open,
  is_busy,
  close,
  question_type,
  handleSubmit,
}) => {
  function _close() {
    setNewQuestion({} as Question);
    close();
  }
  const [new_question, setNewQuestion] = useState({} as Question);

  // console.log(new_question.section, "categorey")

  // function submitForm(event: FormEvent) {
  //   event.preventDefault();

  //   if (new_question.type !== "essay" && !new_question.answer.length) {
  //     alert("You need to select an answer")
  //     return
  //   }
  //   console.log(new_question, new_question.section, "section")
  //   handleSubmit({ ...new_question, section: new_question.section || "answer", answer: new_question.answer || ["answer"], images: image_blobs });
  //   // _close();

  // }

  function submitForm(event: FormEvent) {
    event.preventDefault();

    // Validate if an answer is selected
    if (new_question.type !== 'essay' && !new_question.answer?.length) {
      alert('You need to select an answer');
      return;
    }

    // Ensure `section` has a value before submitting
    const sectionId = new_question.section || 'answer';

    handleSubmit({
      ...new_question,
      section: sectionId, // Use `sectionId` to ensure the latest value is passed
      answer: new_question.answer || ['answer'],
      images: image_blobs,
    });

    _close();
  }

  useEffect(() => {
    if (is_open) {
      // reset and set question type

      let _new_question = {
        type: question_type,
      } as Question;

      //  set up 4 empty options
      const is_multiple_choice_match = question_type == 'multiple_choice';
      const is_multiple_response_match = question_type == 'multiple_response';
      const is_fill_in_the_blanks_match = question_type == 'fill_in_the_blanks';
      if (
        is_multiple_response_match ||
        is_multiple_choice_match ||
        is_fill_in_the_blanks_match
      ) {
        _new_question.answer_options = ['', '', '', ''];
      }

      setNewQuestion(_new_question);

      setImageURLs([]);
      setImageBlobs([]);
      setImageFiles([]);
    }
  }, [is_open]);

  const [image_urls, setImageURLs] = useState([] as string[]);
  const [image_blobs, setImageBlobs] = useState([] as Blob[]);
  const [image_files, setImageFiles] = useState([] as File[]);

  function blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, _) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.readAsDataURL(blob);
    });
  }

  async function convertUploadedImage(acceptedFiles: any) {
    const _images_urls = image_urls;
    const _images_blobs = image_blobs;
    const _images_files = image_files;

    const processImage = async (file: any) => {
      const blob = new Blob([file], {
        type: file.type,
      });
      const image_base64 = await blobToBase64(blob);
      let sizeValid = await validateImageDimensions(image_base64, [
        { height: 300, width: 900 },
        { height: 400, width: 1200 },
        { height: 500, width: 1500 },
      ]);
      if (!sizeValid) {
        toast.error('Some of the images do not match the required dimension');
        return;
      }
      _images_files.push(file);
      _images_urls.push(image_base64);
      _images_blobs.push(blob);
    };

    acceptedFiles.forEach(processImage);

    setImageURLs(_images_urls);
    setImageBlobs(_images_blobs);
    setImageFiles(_images_files);
  }


  const onDrop = useCallback(async (acceptedFiles: any) => {
    convertUploadedImage(acceptedFiles);
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    multiple: true,
    accept: {
      'image/*': [],
    },
    maxSize: 10 * 1024 * 1024, //10mb
  });

  function deleteImage(index: number) {
    const _image_urls = image_urls;
    const _image_blobs = image_blobs;
    const _image_files = image_files;

    _image_urls.splice(index, 1);
    _image_blobs.splice(index, 1);

    setImageURLs(_image_urls);
    setImageBlobs(_image_blobs);
    setImageFiles(_image_files);
  }
  const { data: questionSections, isLoading: isGettingSections } =
    useGetQuestionsSections();
  const ASSESSMENT_TYPES = questionSections?.results.map(section => ({
    id: section.id,
    name: section.name,
  })) || [];


  return (
    <Modal is_open={is_open} close={_close} title="Add a new question">
      <form className="default-text-styles" onSubmit={submitForm}>
        <div className="space-y-2 p-4 md:w-[800px]">
          <h2 className="heading-2 text-primary"> Images </h2>
          <div className="flex flex-wrap gap-2">
            <div className="flex flex-wrap gap-2">
              {image_urls.map((image_url, index) => (
                <div className="flex-1 space-y-4  shadow-sm" key={index}>
                  <div
                    className="relative flex items-center justify-center overflow-hidden"
                    style={{ width: '900px', height: '300px' }}
                  >
                    <img
                      className="relative aspect-square h-full w-full rounded-xl object-cover"
                      src={image_url}
                    /* ref={imageRef} */
                    />
                    <button
                      className="size-8 absolute right-0 top-0 m-4 grid place-items-center rounded-full p-2 text-2xl hover:bg-black/20"
                      type="button"
                      onClick={() => deleteImage(index)}
                    >
                      &times;
                    </button>
                  </div>
                </div>
              ))}
            </div>

            <div
              className={`mt-3 flex h-[5.9375rem] cursor-pointer items-center gap-4 rounded-[0.875rem] border-[0.3px] border-dashed border-[rgb(117,90,226)] bg-white p-6`}
              {...getRootProps()}
            >
              <div className="">
                <UploadIcon />
              </div>
              <div className="">
                <p className="font-sans text-xs font-semibold text-[#755AE2] sm:text-sm">
                  Tap to upload images for this question
                </p>
                <span className="font-sans text-xs font-semibold text-[#755AE2] opacity-75">
                  Files types: PNG, JPG, Max size: 10MB,
                  <br /> Allowed dimenstions: 1500 X 500, 1200 X 400 or 900 X
                  300
                </span>
              </div>
              <input hidden {...getInputProps()} />
            </div>
          </div>
        </div>

        {/* mutlti response question */}
        {question_type == 'multiple_response' && (
          <div className="grid gap-4 p-4 text-sm md:w-[1072px] md:grid-cols-2">
            <div className="space-y-6">
              <h2 className="heading-2 text-primary">
                Create a multiple response question
              </h2>

              <div>
                <label className="space-y-2">
                  <p>Question category</p>
                </label>

                <ListBox
                  className="bg-grey"
                  options={ASSESSMENT_TYPES}
                  active_option={new_question.section as any} // Use `section` here if that's where you want to store the selected category
                  setActiveOption={(category: string) => {
                    setNewQuestion({
                      ...new_question,
                      section: category, // Set `section` here
                    });
                  }}
                  value_key="id"
                  readable_text_key="name"
                />
              </div>

              <p className="helper-text text-xs">
                Candidates will be required to select one or more options from a
                provided list of choices. Please ensure that the correct
                answer(s) are chosen for scoring purposes.
              </p>

              <RichTextEditor
                placeholder="Enter your question here"
                className="min-h-[200px] bg-[#FAFAFA] "
                onChange={(content) =>
                  setNewQuestion({
                    ...new_question,
                    question: content,
                  })
                }
              />

            </div>
            <div className="space-y-6">
              <div className="space-y-4">
                <h2 className="heading-2 text-primary">
                  Enter answer options and select the right answer
                </h2>
                <ul className="space-y-2">
                  {new_question.answer_options?.map((_, index) => (
                    <li key={index}>
                      <label className="flex items-center gap-2">
                        <span>
                          {ALPHABETS[index]}
                          {')'}
                        </span>
                        <input
                          type="text"
                          required
                          className="input-grey"
                          placeholder="Enter answer"
                          value={new_question.answer_options[index]}
                          onChange={event => {
                            let new_options = [...new_question.answer_options];
                            let old_option = new_question.answer_options[index];
                            let answer_index =
                              new_question.answer?.indexOf(old_option);
                            let new_answers = new_question.answer;
                            console.log(new_answers, 'NEW');
                            if (answer_index >= 0) {
                              new_answers[answer_index] = event.target.value;
                            }
                            new_options[index] = event.target.value;
                            setNewQuestion({
                              ...new_question,
                              answer_options: new_options,
                              answer: new_answers,
                            });
                          }}
                        />
                        <div className="flex items-center gap-2">
                          <button
                            type="button"
                            onClick={() => {
                              if (
                                new_question.answer_options.length >
                                MULTIPLE_CHOICE_OPTIONS_MIN_LIMIT
                              ) {
                                let new_options = [
                                  ...new_question.answer_options,
                                ];
                                new_options.splice(index, 1);

                                let answer_index =
                                  new_question.answer_options.indexOf(
                                    new_question.answer_options[index]
                                  );
                                let new_answers =
                                  answer_index >= 0
                                    ? new_question.answer_options.toSpliced(
                                      answer_index
                                    )
                                    : new_question.answer_options;

                                setNewQuestion({
                                  ...new_question,
                                  answer_options: new_options,
                                  answer: new_answers,
                                });
                              }
                            }}
                            title="delete option"
                          >
                            <CloseCircleIcon />
                          </button>

                          <Checkbox
                            disabled={!!!new_question.answer_options[index]}
                            onChange={e => {
                              if (e.target.checked) {
                                setNewQuestion({
                                  ...new_question,
                                  answer: new_question.answer
                                    ? new_question.answer.concat([
                                      e.target.value,
                                    ])
                                    : [e.target.value],
                                });
                              } else {
                                setNewQuestion({
                                  ...new_question,
                                  answer: new_question.answer.toSpliced(
                                    new_question.answer.indexOf(
                                      e.target.value,
                                      1
                                    )
                                  ),
                                });
                              }
                            }}
                            name="select answer"
                            checked={new_question.answer?.includes(
                              new_question.answer_options[index]
                            )}
                            value={new_question.answer_options[index]}
                          />
                        </div>
                      </label>
                    </li>
                  ))}
                </ul>
                <div className="flex items-center justify-between">
                  <button
                    className="btn-primary-light"
                    type="button"
                    disabled={
                      new_question?.answer_options?.length >=
                      MULTIPLE_CHOICE_OPTIONS_LIMIT
                    }
                    onClick={() => {
                      if (
                        new_question?.answer_options?.length <
                        MULTIPLE_CHOICE_OPTIONS_LIMIT
                      ) {
                        setNewQuestion({
                          ...new_question,
                          answer_options: [...new_question.answer_options, ''],
                        });
                      }
                    }}
                  >
                    + Add another option
                  </button>
                  <div>
                    <label className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        name="shuffle"
                        id=""
                        checked={new_question.is_shuffled}
                        onChange={event => {
                          setNewQuestion({
                            ...new_question,
                            is_shuffled: event.target.checked,
                          });
                        }}
                      />
                      <p>Shuffle options</p>
                    </label>
                  </div>
                </div>
                <div>
                  <p>How do you want to score this question?</p>
                  <label className="space-y-2">
                    <input
                      type="number"
                      required
                      name="score"
                      className="input-grey"
                      placeholder="Enter score"
                      value={new_question.points}
                      onChange={event => {
                        setNewQuestion({
                          ...new_question,
                          points: parseInt(event.target.value),
                        });
                      }}
                    />
                  </label>
                </div>
              </div>
              <Button
                is_busy={is_busy}
                className="btn-primary w-full"
                type="submit"
              >
                Save question
              </Button>
            </div>
          </div>
        )}

        {/* mutlti choice question */}
        {question_type == 'multiple_choice' && (
          <div className="grid gap-4 p-4 text-sm md:w-[1072px] md:grid-cols-2">
            <div className="space-y-6">
              <h2 className="heading-2 text-primary">
                Create multi choice question
              </h2>

              <div>
                <label className="space-y-2">
                  <p>Question category</p>
                </label>

                <ListBox
                  className="bg-grey"
                  options={ASSESSMENT_TYPES}
                  active_option={new_question.category as any}
                  setActiveOption={(category: string) => {
                    setNewQuestion({
                      ...new_question,
                      section: category,
                    });
                  }}
                  value_key="id"
                  readable_text_key="name"
                />
              </div>

              <p className="helper-text text-xs">
                Candidates will be required to select one from a provided list
                of choices. Please ensure that the correct answer are chosen for
                scoring purposes.
              </p>

              <RichTextEditor
                placeholder="Enter your question here"
                className="min-h-[200px] bg-[#FAFAFA] "
                onChange={(content) =>
                  setNewQuestion({
                    ...new_question,
                    question: content,
                  })
                }
              // initialContent={watch('question')}
              />
            </div>
            <div className="space-y-6">
              <div className="space-y-4">
                <h2 className="heading-2 text-primary">
                  Enter answer options and select the right answer
                </h2>
                <ul className="space-y-2">
                  {new_question.answer_options?.map((_, index) => (
                    <li key={index}>
                      <label className="flex items-center gap-2">
                        <span>
                          {ALPHABETS[index]}
                          {')'}
                        </span>
                        <input
                          type="text"
                          required
                          className="input-grey"
                          placeholder="Enter answer"
                          value={new_question.answer_options[index]}
                          onChange={event => {
                            let new_options = [...new_question.answer_options];
                            new_options[index] = event.target.value;

                            setNewQuestion({
                              ...new_question,
                              answer_options: new_options,
                            });
                          }}
                        />
                        <button
                          type="button"
                          onClick={() => {
                            if (
                              new_question.answer_options.length >
                              MULTIPLE_CHOICE_OPTIONS_MIN_LIMIT
                            ) {
                              let new_options = [
                                ...new_question.answer_options,
                              ];
                              new_options.splice(index, 1);
                              setNewQuestion({
                                ...new_question,
                                answer_options: new_options,
                              });
                            }
                          }}
                          title="delete option"
                        >
                          <CloseCircleIcon />
                        </button>
                      </label>
                    </li>
                  ))}
                </ul>
                <div className="flex items-center justify-between">
                  <button
                    className="btn-primary-light"
                    type="button"
                    disabled={
                      new_question?.answer_options?.length >=
                      MULTIPLE_CHOICE_OPTIONS_LIMIT
                    }
                    onClick={() => {
                      if (
                        new_question?.answer_options?.length <
                        MULTIPLE_CHOICE_OPTIONS_LIMIT
                      ) {
                        setNewQuestion({
                          ...new_question,
                          answer_options: [...new_question.answer_options, ''],
                        });
                      }
                    }}
                  >
                    + Add another answer
                  </button>
                  <div>
                    <label className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        name="shuffle"
                        id=""
                        checked={new_question.is_shuffled}
                        onChange={event => {
                          setNewQuestion({
                            ...new_question,
                            is_shuffled: event.target.checked,
                          });
                        }}
                      />
                      <p>Shuffle answers</p>
                    </label>
                  </div>
                </div>
                <div className="space-y-2">
                  <p>Select right answer to question</p>
                  <ListBoxStrings
                    className="bg-grey"
                    options={new_question.answer_options?.filter(
                      (option: string) => option.trim() !== ''
                    )}
                    active_option={new_question.answer as any}
                    setActiveOption={answer => {
                      setNewQuestion({
                        ...new_question,
                        answer: [answer],
                      });
                    }}
                  />
                </div>
                <div>
                  <p>How do you want to score this question?</p>
                  <label className="space-y-2">
                    <input
                      type="number"
                      required
                      name="score"
                      className="input-grey"
                      placeholder="Enter score"
                      value={new_question.points}
                      onChange={event => {
                        setNewQuestion({
                          ...new_question,
                          points: parseInt(event.target.value),
                        });
                      }}
                    />
                  </label>
                </div>
              </div>
              <Button
                is_busy={is_busy}
                className="btn-primary w-full"
                type="submit"
              >
                Save question
              </Button>
            </div>
          </div>
        )}

        {/* fill in the blanks question */}
        {question_type == 'fill_in_the_blanks' && (
          <div className="grid gap-4 p-4 text-sm md:w-[1072px] md:grid-cols-2">
            <div className="space-y-6">
              <h2 className="heading-2 text-primary">
                Create fill in the blanks question.
              </h2>

              <div>
                <label className="space-y-2">
                  <p>Question category</p>
                </label>

                <ListBox
                  className="bg-grey"
                  options={ASSESSMENT_TYPES}
                  active_option={new_question.category as any}
                  setActiveOption={(category: string) => {
                    setNewQuestion({
                      ...new_question,
                      category,
                    });
                  }}
                  value_key="id"
                  readable_text_key="name"
                />
              </div>

              <p className="helper-text text-xs">
                You can create the blanks by typing _ (1 underscore). <br />
                Candidates will be required to select one or more options from a
                provided list of choices. Please ensure that the correct
                answer(s) are chosen for scoring purposes.
              </p>

              <RichTextEditor
                placeholder="Enter your question here"
                className="min-h-[200px] bg-[#FAFAFA] "
                onChange={(content) =>
                  setNewQuestion({
                    ...new_question,
                    question: content,
                  })
                }
              // initialContent={watch('question')}
              />
            </div>
            <div className="space-y-6">
              <div className="space-y-4">
                <h2 className="heading-2 text-primary">
                  Enter answer options and select the right answer
                </h2>
                <ul className="space-y-2">
                  {new_question.answer_options?.map((_, index) => (
                    <li key={index}>
                      <label className="flex items-center gap-2">
                        <span>
                          {ALPHABETS[index]}
                          {')'}
                        </span>
                        <input
                          type="text"
                          required
                          className="input-grey"
                          placeholder="Enter answer"
                          value={new_question.answer_options[index]}
                          onChange={event => {
                            let new_options = [...new_question.answer_options];
                            new_options[index] = event.target.value;
                            setNewQuestion({
                              ...new_question,
                              answer_options: new_options,
                            });
                          }}
                        />
                        <button
                          type="button"
                          onClick={() => {
                            if (
                              new_question.answer_options.length >
                              MULTIPLE_CHOICE_OPTIONS_MIN_LIMIT
                            ) {
                              let new_options = [
                                ...new_question.answer_options,
                              ];
                              new_options.splice(index, 1);
                              setNewQuestion({
                                ...new_question,
                                answer_options: new_options,
                              });
                            }
                          }}
                          title="delete option"
                        >
                          <CloseCircleIcon />
                        </button>
                      </label>
                    </li>
                  ))}
                </ul>
                <div className="flex items-center justify-between">
                  <button
                    className="btn-primary-light"
                    type="button"
                    onClick={() => {
                      setNewQuestion({
                        ...new_question,
                        answer_options: [...new_question.answer_options, ''],
                      });
                    }}
                  >
                    + Add another answer
                  </button>
                  <div>
                    <label className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        name="shuffle"
                        id=""
                        checked={new_question.is_shuffled}
                        onChange={event => {
                          setNewQuestion({
                            ...new_question,
                            is_shuffled: event.target.checked,
                          });
                        }}
                      />
                      <p>Shuffle answers</p>
                    </label>
                  </div>
                </div>
                <div className="space-y-2"></div>
                <div className="space-y-2">
                  <p>Select right answer to question</p>
                  <ListBoxStrings
                    className="bg-grey"
                    options={new_question.answer_options?.filter(
                      (option: string) => option.trim() !== ''
                    )}
                    active_option={new_question.answer as any}
                    setActiveOption={answer => {
                      setNewQuestion({
                        ...new_question,
                        answer: [answer],
                      });
                    }}
                  />
                </div>
                <div>
                  <p>How do you want to score this question?</p>
                  <label className="space-y-2">
                    <input
                      type="number"
                      required
                      name="score"
                      className="input-grey"
                      placeholder="Enter score"
                      value={new_question.points}
                      onChange={event => {
                        setNewQuestion({
                          ...new_question,
                          points: parseInt(event.target.value),
                        });
                      }}
                    />
                  </label>
                </div>
              </div>
              <Button
                is_busy={is_busy}
                className="btn-primary w-full"
                type="submit"
              >
                Save question
              </Button>
            </div>
          </div>
        )}

        {/* essay question */}
        {question_type == 'essay' && (
          <div className="p-4 text-sm md:w-[520px]">
            <div className="space-y-6">
              <h2 className="heading-2 text-primary">
                Create text input question
              </h2>

              <p className="helper-text text-xs">
                Candidates will be asked to respond to a question in by typing
                in a text input field. Enter Question in the input below
              </p>

              <RichTextEditor
                placeholder="Enter your question here"
                className="min-h-[200px] bg-[#FAFAFA] "
                onChange={(content) =>
                  setNewQuestion({
                    ...new_question,
                    question: content,
                  })
                }
              // initialContent={watch('question')}
              />
            </div>
            <div className="space-y-6">
              <div>
                <label className="space-y-2">
                  <p>Question category</p>
                </label>
                <ListBox
                  className="bg-grey"
                  options={ASSESSMENT_TYPES}
                  active_option={new_question.category as any}
                  setActiveOption={category => {
                    setNewQuestion({
                      ...new_question,
                      category,
                    });
                  }}
                  value_key="id"
                  readable_text_key="name"
                />
              </div>

              <div>
                <p>How do you want to score this question?</p>
                <label className="space-y-2">
                  <input
                    type="number"
                    required
                    name="score"
                    className="input-grey"
                    placeholder="Enter score"
                    value={new_question.points}
                    onChange={event => {
                      setNewQuestion({
                        ...new_question,
                        points: parseInt(event.target.value),
                      });
                    }}
                  />
                </label>
              </div>
              <div>
                <p>Enter instructions for scoring the user</p>
                <label className="space-y-2">
                  <RichTextEditor
                    placeholder={`Please focus on the following points while grading:\n - Clarity of the answer\n - Use of relevant examples\n - Logical flow and structure\n - Grammar and spelling `}
                    className="min-h-[200px] bg-[#FAFAFA] "
                    onChange={(content) =>
                      setNewQuestion({
                        ...new_question,
                        instructions: content,
                      })
                    }
                  />
                  {/* */}
                </label>
              </div>
              <Button
                is_busy={is_busy}
                className="btn-primary w-full"
                type="submit"
              >
                Save question
              </Button>
            </div>
          </div>
        )}

        {/* true or false */}
        {question_type == 'true_false' && (
          <div className="p-4 text-sm md:w-[520px]">
            <div className="space-y-6">
              <h2 className="heading-2 text-primary">
                Create true or false question
              </h2>

              <p className="helper-text text-xs">
                Candidates will be asked to respond to the question by choosing
                etiher true or false
              </p>

              <RichTextEditor
                placeholder="Enter your question here"
                className="min-h-[200px] bg-[#FAFAFA] "
                onChange={(content) =>
                  setNewQuestion({
                    ...new_question,
                    question: content,
                  })
                }
              // initialContent={watch('question')}
              />
            </div>
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <p>Select right answer to question</p>
                  <ListBox
                    className="bg-grey"
                    options={TRUE_OR_FALSE}
                    value_key="value"
                    readable_text_key="readable_string"
                    active_option={new_question.answer as any}
                    setActiveOption={answer => {
                      setNewQuestion({
                        ...new_question,
                        answer: [answer],
                      });
                    }}
                  />
                </div>
              </div>

              <div>
                <label className="space-y-2">
                  <p>Question category</p>
                </label>
                <ListBox
                  className="bg-grey"
                  options={ASSESSMENT_TYPES}
                  active_option={new_question.category as any}
                  setActiveOption={category => {
                    setNewQuestion({
                      ...new_question,
                      category,
                    });
                  }}
                  value_key="id"
                  readable_text_key="name"
                />
              </div>

              <div>
                <p>How do you want to score this question?</p>
                <label className="space-y-2">
                  <input
                    type="number"
                    required
                    name="score"
                    className="input-grey"
                    placeholder="Enter score"
                    value={new_question.points}
                    onChange={event => {
                      setNewQuestion({
                        ...new_question,
                        points: parseInt(event.target.value),
                      });
                    }}
                  />
                </label>
              </div>
              <Button
                is_busy={is_busy}
                className="btn-primary w-full"
                type="submit"
              >
                Save question
              </Button>
            </div>
          </div>
        )}
      </form>
    </Modal>
  );
};
export default AddCustomQuestionModal;
