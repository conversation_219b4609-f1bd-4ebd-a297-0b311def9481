import React, { FormEvent, useEffect, useState } from 'react';
import Modal from '@/components/Modal';
import { Section } from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import EditIcon from '../../../../../../components/icons/jsx/EditIcon';
import TrashIcon from '../../../../../../components/icons/jsx/TrashIcon';

interface Props {
  is_open: boolean;
  close: () => void;
  section: Section;
  active_question_index: number;
  handleSubmit: (section: Section) => void;
}

const QuestionPreviewModal: React.FC<Props> = ({
  is_open,
  close,
  section,
  active_question_index,
  handleSubmit,
}) => {
  const [edited_section, setEditedSection] = useState({ ...section });
  const [_active_question_index, setActiveQuestionIndex] = useState(0);

  useEffect(() => {
    setActiveQuestionIndex(active_question_index);
  }, [active_question_index]);

  useEffect(() => {
    setEditedSection({ ...section });
  }, [section]);

  function submitForm(event: FormEvent) {
    event.preventDefault();
    handleSubmit(edited_section);
  }

  return (
    <Modal title="Questions preview" close={close} is_open={is_open}>
      <div className="grid min-h-[400px] w-[985px] grid-cols-4 gap-4 bg-white p-4">
        <div className="col-span-3 flex flex-col justify-between space-y-8">
          <div className="space-y-2 rounded-xl border p-4 text-sm">
            <div className="flex items-center justify-between gap-4">
              <h2 className="heading-text">
                Question {_active_question_index + 1}:
              </h2>
              <div className="flex items-center gap-2">
                <button
                  type="button"
                  className="btn-icon-primary-light"
                  // onClick={editQuestion}
                  title="edit question"
                >
                  <EditIcon />
                </button>
                <button
                  type="button"
                  title="delete question"
                  className="btn-icon-primary-light"
                >
                  <TrashIcon />
                </button>
              </div>
            </div>
            {edited_section?.question_set &&
              _active_question_index !== undefined && (
                <p>
                  {
                    edited_section?.question_set[_active_question_index]
                      ?.question
                  }
                </p>
              )}
          </div>
          <div className="flex justify-end">
            <button className="btn-primary">Save and continue</button>
          </div>
        </div>
        <ul className="space-y-2">
          {edited_section?.question_set?.map((question, index) => (
            <li key={index}>
              <button
                className={`w-full text-left ${index === _active_question_index
                  ? 'rounded-xl outline outline-1'
                  : ''
                  }`}
                type="button"
                onClick={() => {
                  setActiveQuestionIndex(index);
                }}
              >
                <div className="heading-text bg-light-accent-bg rounded-xl p-4 text-sm">
                  <h2>Question {index + 1} :</h2>
                  <p className="font-normal">{question.question}</p>
                </div>
              </button>
            </li>
          ))}
        </ul>
      </div>
    </Modal>
  );
};
export default QuestionPreviewModal;
