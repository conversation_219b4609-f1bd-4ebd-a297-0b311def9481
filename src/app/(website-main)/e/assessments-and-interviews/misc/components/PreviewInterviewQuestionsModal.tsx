import React, { FormEvent, useEffect, useState } from 'react';
import Modal from '@/components/Modal';
import { Assessment, InterviewAssessment, InterviewAssessmentQuestion, Section } from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import Disclosure from "@/components/Disclosure";
import app_fetch from '@/lib/api/appFetch';
import { Loader } from '../../../../../../components/shared';

interface Props {
  is_open: boolean;
  close: () => void;
  assessment: InterviewAssessment | undefined;
}

const PreviewInterviewQuestionsModal: React.FC<Props> = ({
  is_open,
  close,
  assessment,
}) => {

  const [is_loading_sections, setIsLoadingSections] = useState(false)
  const [questions, setQuestions] = useState([] as InterviewAssessmentQuestion[])

  useEffect(()=>{
      setIsLoadingSections(true)
    if (assessment?.questions){
      setQuestions(assessment.questions)
      setIsLoadingSections(false)
      return
    }

    else if (assessment?.id){
    app_fetch(`assessments/assessment-interview/${assessment.id}/`).then((res)=>res.json()).then((result:{id:string, questions:InterviewAssessmentQuestion[]})=>{
        setQuestions(result.questions)
      }).finally(()=>{
          setIsLoadingSections(false)
        })
    }

  }, [assessment])

  return (
      <Modal
        title="Assessment preview"
        is_open={is_open}
        close={close}
      >
        <div className="md:w-[768px] p-4">
        {is_loading_sections?(<Loader/>):(
          <ul className="space-y-1">
            {questions
              .map((question, index) => (
                <li key={index}>
                  <div className="border p-4 space-y-2 rounded-xl">
                    <div className="flex items-center gap-2 text-sm">
                      Question
                      <span className="flex aspect-square w-[18px] items-center justify-center rounded-full bg-primary text-white">
                        {index + 1}
                      </span>
                    </div>
                      <p className="bg-light-background rounded-xl p-2">{question.question}</p>
                  </div>
                </li>
              ))}
          </ul>
        )}
        </div>
      </Modal>
  )
}

export default PreviewInterviewQuestionsModal
