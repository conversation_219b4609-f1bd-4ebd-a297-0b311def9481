import React, { useState } from "react";

import { QUESTION_TYPES_DICTIONARY } from "@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants";
import { LibraryAssessment } from "@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments";
import { useWindowWidth } from "@/hooks";

import QuestionTypeIcon from "../../../../../../components/icons/jsx/QuestionTypeIcon";
import { Drawer, DrawerContent, DrawerTrigger, But<PERSON>, DrawerHeader, DrawerClose } from "../../../../../../components/shared";

interface Props {
  is_open: boolean;
  assessment: LibraryAssessment | undefined;
  close: () => void;
  openDrawer: () => void;
  setCurrentQuestionPack: () => void;
}

const LibraryPackDetailModal: React.FC<Props> = ({
  is_open,
  close,
  assessment,
  openDrawer,
  setCurrentQuestionPack
}) => {
  const title = "Question details";
  const windowWidth = useWindowWidth()

  return (

    <Drawer onClose={() => close()} dismissible direction={windowWidth < 720 ? "bottom" : "right"} >
      <DrawerTrigger asChild className="max-w-max" >
        <Button variant="extralight" size='tiny' className="max-w-max text-header-text" onClick={() => { setCurrentQuestionPack(); openDrawer() }}>Details</Button>
      </DrawerTrigger>


      <DrawerContent className="w-full md:w-[40%] md:right-0 md:left-auto md:max-w-[600px] h-[90vh] md:h-screen !m-0 !p-0 bg-white border-none rounded-l-2xl overflow-hidden">
        {assessment && (
          <div className="grow flex flex-col relative max-h-full overflow-y-hidden">
            <div className="w-full flex items-center justify-center bg-primary md:hidden">
              <div className="mx-auto mt-3 mb-1 h-1 w-[100px] rounded-full bg-white/60" />
            </div>

            <DrawerHeader className="sticky top-0 flex items-center justify-between bg-primary w-full text-white max-md:pt-1 px-5 md:px-8">
              <h3>Question Pack Details</h3>
              <DrawerClose className="text-sm bg-white/20 px-6 py-2 rounded-lg">Close</DrawerClose>
            </DrawerHeader>


            <section className="w-full h-full space-y-4 p-4 md:px-5 text-sm overflow-y-scroll ">
              <div className="bg-grey rounded-xl p-5">
                <h2 className="text-header-text text-[1.025rem] font-medium leading-snug ">{assessment.name}</h2>
                <p className="text-[#7D8590] text-sm leading-tight">{assessment.description}</p>
              </div>

              <div className="text-body-text text-[0.9rem] bg-grey rounded-xl p-5">
                <h2 className="text-header-text text-sm font-medium">Assessment details</h2>

                <div className="mt-2 mb-6">
                  <div className="flex gap-4">
                    <p className="text-sm text-body-text">
                      Total questions:{"  "}
                      <span className="text-header-text font-semibold">
                        {assessment.question_set?.length || 0}
                      </span>
                    </p>
                    <p className="text-sm text-body-text">
                      Question type: <span className="text-header-text font-semibold">{assessment.question_types.length}</span>
                    </p>
                  </div>
                  <ul className="heading-text flex flex-wrap gap-x-2 gap-y-1.5 font-normal capitalize">
                    {assessment.question_types?.map(
                      (question_type, index: number) => (
                        <li
                          key={index}
                          className="flex items-center justify-center gap-1.5 rounded-xl bg-white border p-2 text-xs text-header-text"
                        >
                          <QuestionTypeIcon
                            question_type={question_type.toLowerCase()}
                          />
                          <span>
                            {QUESTION_TYPES_DICTIONARY[
                              question_type.toLowerCase()
                            ]?.name}
                          </span>
                        </li>
                      ),
                    )}
                  </ul>
                </div>

                <div className="space-y-1">
                  <h2 className="text-sm text-body-text">Skills</h2>
                  <ul className="heading-text flex flex-wrap gap-x-2 gap-y-1.5 font-normal capitalize">
                    {assessment.tags?.map((tag, index) => (
                      <li
                        key={index}
                        className="rounded-xl bg-white border p-2 py-2.5 text-xs"
                      >
                        {tag}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <div className="bg-grey rounded-xl p-5">
                <h2 className="text-header-text text-base font-medium">Assessment summary</h2>
                <p className="text-[#7D8590]">{assessment.summary}</p>
              </div>

              <div className="bg-grey rounded-xl p-5">
                <h2 className="text-header-text text-base font-medium">
                  Skill tested in this assessment
                </h2>
                <p className="text-[#7D8590] whitespace-pre-wrap">
                  {assessment.skills_tested}
                </p>
              </div>
            </section>
          </div>
        )}
      </DrawerContent>

    </Drawer >
  )
};
export default LibraryPackDetailModal;
