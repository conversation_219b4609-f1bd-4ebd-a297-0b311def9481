
import Select<PERSON>ingle<PERSON>ombo from "./_Primitive";
import { useQueryClient, useMutation, useQuery } from '@tanstack/react-query'
import { createRoleAPI, fetchRolesAPI } from "@/app/(website-main)/e/assessments-and-interviews/misc/api/Roles";
import AltModal from '@/components/Modal'
import { useState } from "react";
import Button from "@/components/shared/Button";

type Props = {
  value: any;
  onChange: (role: string) => void;
  hideLabel?: boolean;
  containerClass?: string
  hasError?: boolean
  errorMessage?: string
}


const RoleSelect = ({ value, onChange, hideLabel, containerClass, hasError, errorMessage }: Props) => {
  const query_client = useQueryClient()
  const roles_query_key = ["roles"]
  const roles_query = useQuery({
    queryKey: roles_query_key,
    queryFn: fetchRolesAPI,
  })

  const roles_mutation = useMutation({
    mutationFn: createRoleAPI,
    onSuccess: (result, vars, context) => {
      query_client.setQueryData(roles_query_key, (old: any[] = []) => {
        old.push(result)
        //@ts-ignore
        onChange(result.id.toString())
        return old
      })
      closeAddNewRoleModal()
    },
    onError: (error: string, vars, context) => {
      setNewRoleError(error)
    }
  })


  const [new_role_name, setNewRoleName] = useState("")
  const [is_add_new_role_modal_open, setIsAddNewRoleModalOpen] = useState(false)

  function closeAddNewRoleModal() {
    setIsAddNewRoleModalOpen(false)
  }

  function openAddNewRoleModal() {
    setIsAddNewRoleModalOpen(true)
  }

  function handleAddNewRole(keyword: string) {
    setNewRoleName(keyword)
    openAddNewRoleModal()
  }

  const [new_role_error, setNewRoleError] = useState("")
  function submitNewRole() {
    setNewRoleError("")

    if (!new_role_name) {
      setNewRoleName("Role name cannot be blank")
      return;
    }

    roles_mutation.mutate(new_role_name)
  }

  return (
    <>
      {roles_query.isSuccess && (
        <SelectSingleCombo
          isLoadingOptions={roles_query.isLoading}
          options={roles_query.data}
          label="What role are you hiring for ?"
          hideLabel={hideLabel}
          handleAddNewRole={(param) => handleAddNewRole(param)}
          onChange={(value: string) => {
            //@ts-ignore
            onChange(String(value))
          }}
          value={value}
          valueKey="id"
          labelKey="name"
          placeholder="Select role"
          name="Role name"
          itemClass="text-[0.8125rem] !overflow-hidden"
          containerClass={containerClass}
          hasError={hasError}
          errorMessage={errorMessage}
        />
      )}
      {roles_query.isLoading && (
        <p>Loading roles...</p>
      )}
      {roles_query.isError && (
        <p className="text-danger text-center">Error Loading roles...</p>
      )}

      <AltModal
        is_open={is_add_new_role_modal_open}
        close={closeAddNewRoleModal}
        title="Add a new job role"
      >
        <form onSubmit={e => { e.preventDefault(); submitNewRole() }} className="bg-light-accent-bg max-w-[450px] space-y-4 p-4 text-sm" >
          <div>
            <label className="space-y-1">
              <p className="heading-text font-medium">What is the job role you want to add?</p>
              <input
                value={new_role_name}
                onChange={(e) => setNewRoleName(e.target.value)}
                required
                className="input-white"
                type="text"
                name="name of job role"
                placeholder="Enter job role name"
              />
            </label>
          </div>
          {new_role_error && (<p className="text-danger text-center">{new_role_error}</p>)}
          <Button is_busy={roles_mutation.isLoading} type="button" onClick={submitNewRole} className="btn-primary w-full"> Proceed </Button>
        </form>
      </AltModal>

    </>
  )
}
export default RoleSelect 
