import React, { FormEvent, useState } from "react";
import Modal from "@/components/Modal";
import Button from "@/components/shared/Button";
import DeleteSectionIcon from "@/components/icons/DeleteSectionIcon.jsx";

interface Props {
  is_open: boolean;
  close: () => void;
  section_index: number;
  question_index: number;
  onDelete: (question_index: number, section_index: number,) => Promise<any>;
}

const DeleteQuestionModal: React.FC<Props> = ({
  is_open,
  close,
  question_index,
  section_index,
  onDelete,
}) => {
  const [is_busy, setIsBusy] = useState(false);
  async function performAction(e: FormEvent) {
    e.preventDefault();
    setIsBusy(true);
    onDelete(question_index, section_index)
      .finally(() => {
        setIsBusy(false);
        close();
      });
  }

  return (
    <Modal
      is_open={is_open}
      title="Delete question?"
      close={close}
    >
      <div className="md:w-[466px] space-y-2 bg-[#F5F3FF] text-center p-4 text-sm text-[#675E8B]">
        <div className="flex flex-col gap-2 items-center py-4 px-12">
          <DeleteSectionIcon />
          <h2 className="heading-2 text-primary">
            Delete question {question_index + 1}
          </h2>
          <p>
            Are you sure you want to delete this question ?
          </p>
        </div>
      </div>
      <div className="rounded-xl bg-white p-4">
        <div className="flex items-center justify-end gap-2">
          <button
            className="btn-primary-light"
            onClick={close}
            type="button"
          >
            No
          </button>
          <Button
            className="btn-primary"
            is_busy={is_busy}
            onClick={performAction}
          >
            Yes, delete
          </Button>
        </div>
      </div>
    </Modal>
  );
};
export default DeleteQuestionModal;
