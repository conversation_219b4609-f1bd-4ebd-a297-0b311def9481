'use client'

import { useEffect, useState } from "react";
import { TIME_LIMIT_UNITS } from "../constants/constants";
import { TimeLimitUnits } from "../types/create-assessments";
import { secondsToLargestUnit } from "@/lib/utils/functions";
import ListBox from "@/components/shared/listbox";

type Props = {
  value: number;
  onChange: (value: number) => void
}
export default function TimeInput({ value, onChange }: Props) {
  const [time_value, setTimeValue] = useState(0)
  const [time_unit, setTimeUnit] = useState<TimeLimitUnits>("minute(s)")

  /** * @description : covert time from seconds to time unit equivalent */
  function resetTime(value: number) {
    setTimeUnit(secondsToLargestUnit(value).unit as TimeLimitUnits)
    setTimeValue(secondsToLargestUnit(value).time as number)
  }

  /** * @description : covert time back to seconds from time unit equivalent */
  useEffect(
    () => {
      let formatted_time_limit = time_value || 1;
      switch (time_unit) {
        case "second(s)":
          break;
        case "minute(s)":
          formatted_time_limit *= 60;
          break;
        case "hour(s)":
          formatted_time_limit *= 60 * 60;
          break;
        case "day(s)":
          formatted_time_limit *= 60 * 60 * 24;
          break;

        default:
          break;
      }
      onChange(formatted_time_limit);
    }, [time_value, time_unit]
  )

  useEffect(() => {
    resetTime(value)
  }, [])

  function _changeTime(new_time: number) {
    setTimeValue(new_time)
  }

  function _changeTimeUnit(new_time_unit: TimeLimitUnits) {
    setTimeUnit(new_time_unit)
  }

  return (
    <div className="flex pb-2 gap-2">
      <input
        type="number"
        placeholder="time value"
        className="input-white max-w-[12ch] border"
        value={time_value}
        onChange={(event) => {
          _changeTime(parseInt(event.target.value))
        }}
      />
      {/* @ts-ignore */}
      <ListBox placeholder="Select time unit" className="border !lowercase" active_option={time_unit} setActiveOption={(_option: TimeLimitUnits) => {
        _changeTimeUnit(_option);
      }} value_key="value" readable_text_key="readable_string" options={TIME_LIMIT_UNITS} />
    </div>
  )
}
