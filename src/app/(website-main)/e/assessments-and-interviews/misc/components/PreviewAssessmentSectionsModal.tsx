import React, { FormEvent, useEffect, useState } from 'react';
import Modal from '@/components/Modal';
import { QUESTION_TYPES_DICTIONARY } from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants';
import { Assessment, Section } from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import Disclosure from "@/components/Disclosure";
import app_fetch from '@/lib/api/appFetch';
import { Loader } from '../../../../../../components/shared';
import { ArrowDown2 } from 'iconsax-react';

interface Props {
  is_open: boolean;
  close: () => void;
  assessment: Assessment | undefined;
}

const PreviewAssessmentSectionsModal: React.FC<Props> = ({
  is_open,
  close,
  assessment,
}) => {

  const [is_loading_sections, setIsLoadingSections] = useState(false)
  const [sections, setSections] = useState([] as Section[])

  useEffect(()=>{
      setIsLoadingSections(true)
    if (assessment?.sections){
      setSections(assessment.sections)
      setIsLoadingSections(false)
      return
    }

    else if (assessment?.id){
    app_fetch(`assessments/${assessment.id}/`).then((res)=>res.json()).then((result:{id:string, sections:Section[]})=>{
        setSections(result.sections)
      }).finally(()=>{
          setIsLoadingSections(false)
        })
    }

  }, [assessment])

  return (
      <Modal
        title="Assessment preview"
        is_open={is_open}
        close={close}
      >
        <div className="md:w-[768px] p-4">
        {is_loading_sections?(<Loader/>):(
          <ul className="space-y-1">
            {sections?.filter((section) => section?.name !== undefined || section?.section_name !== undefined)
              .map((section, index) => (
                <li key={index}>
                  <Disclosure
                    title={
                      <div className="flex items-center justify-between py-0">
                        <div className="flex items-center gap-2">
                          Section :
                          <span className="flex aspect-square w-[18px] items-center justify-center rounded-full bg-primary text-white">
                            {index + 1}
                          </span>
                          <p className="">{section?.name}</p>
                        </div>
                        <ArrowDown2 />
                      </div>
                    }
                  >
                    <ul className="space-y-4">
                      {section.question_set?.map((question, index) => (
                        <div className="border p-4 space-y-2 rounded-xl" key={index}>
                          <h3>Question {index + 1}</h3>
                          <p>{question.question}</p>

                          <hr />

                          {/* Multi choice questions */}
                          {question?.type ===
                              QUESTION_TYPES_DICTIONARY["multiple_choice"]
                                .value && (
                            <div>
                              <ol>
                                {question?.answer_options?.map((
                                  option,
                                  index,
                                ) => (
                                  <li key={index}>
                                    <label className="flex items-center gap-2">
                                      <input type="radio" name={question.id} />
                                      {option}
                                    </label>
                                  </li>
                                ))}
                              </ol>
                            </div>
                          )}
                          {/* Multi response questions */}
                          {question?.type ===
                              QUESTION_TYPES_DICTIONARY["multiple_response"]
                                .value && (
                            <div>
                              <ol>
                                {question?.answer_options?.map((
                                  option,
                                  index,
                                ) => (
                                  <li key={index}>
                                    <label className="flex items-center gap-2">
                                      <input
                                        type="checkbox"
                                        name={question.id}
                                      />
                                      {option}
                                    </label>
                                  </li>
                                ))}
                              </ol>
                            </div>
                          )}
                          {/* true or false questions */}
                          {question?.type ===
                              QUESTION_TYPES_DICTIONARY["true_false"].value && (
                            <div>
                              <ol>
                                {"true, false".split(", ").map((
                                  option,
                                  index,
                                ) => (
                                  <li key={index}>
                                    <label className="flex capitalize items-center gap-2">
                                      <input
                                        type="radio"
                                        value={option}
                                        name={question.id}
                                      />
                                      {option}
                                    </label>
                                  </li>
                                ))}
                              </ol>
                            </div>
                          )}
                          {/* fill in the blanks questions */}
                          {question?.type ===
                              QUESTION_TYPES_DICTIONARY["fill_in_the_blanks"]
                                .value && (
                            <div>
                              <ol>
                                {question?.answer_options?.map((
                                  option,
                                  index,
                                ) => (
                                  <li key={index}>
                                    <label className="flex items-center gap-2">
                                      <input
                                        type="radio"
                                        value={option}
                                        name={question.id}
                                      />
                                      {option}
                                    </label>
                                  </li>
                                ))}
                              </ol>
                            </div>
                          )}
                          {/* essay questions */}
                          {question?.type ===
                              QUESTION_TYPES_DICTIONARY["essay"].value && (
                            <textarea
                              placeholder="Write your answer here"
                              className="input-grey"
                            />
                          )}
                        </div>
                      ))}
                    </ul>
                  </Disclosure>
                </li>
              ))}
          </ul>
        )}
        </div>
      </Modal>
  )
}

export default PreviewAssessmentSectionsModal
