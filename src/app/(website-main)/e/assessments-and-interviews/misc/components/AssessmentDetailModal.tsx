import { Dialog } from '@headlessui/react';
import React, { useState } from 'react';
import { QUESTION_TYPES_DICTIONARY } from '../constants/constants';
import { LibraryAssessment } from '../types/create-assessments';
import QuestionTypeIcon from '../../../../../../components/icons/jsx/QuestionTypeIcon';
import Button from '../../../../../../components/shared/Button';
import AssessmentPreviewModal from './AssessmentPreviewModal';

interface Props {
  is_open: boolean;
  is_added: boolean;
  is_adding?: boolean;
  is_removing?: boolean;
  assessment?: LibraryAssessment;
  close: () => void;
  handleAdd: () => void;
  handleRemove: () => void;
}

const AssessmentDetailModal: React.FC<Props> = ({
  is_open,
  close,
  assessment,
  is_added,
  is_adding,
  is_removing,
  handleAdd,
  handleRemove,
}) => {
  const title = 'Question details';

  const [is_assessment_preview_modal_open, setIsAssessmentPreviewModalOpen] = useState(false)
  function openAssessmentPreviewModal() {
    setIsAssessmentPreviewModalOpen(true)
  }
  function closeAssessmentPreviewModal() {
    setIsAssessmentPreviewModalOpen(false)
  }

  function previewAssessment() {
    close()
    openAssessmentPreviewModal()
  }


  return (
    <>
      <Dialog open={is_open} onClose={close} className="relative z-50">
        {assessment && (
          <>
            <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
            <div className="fixed inset-0 w-screen overflow-y-auto">
              <div className="flex min-h-full items-start p-4">
                <Dialog.Panel className="Zg:min-w-[450px] max-w-1g ml-auto overflow-hidden rounded-[1.25rem] bg-white sm:min-w-[300px] ">
                  <header className="flex items-center justify-between gap-4 bg-primary p-8 pb-4 pt-6 text-white">
                    <Dialog.Title className="font-medium">{title}</Dialog.Title>
                    <button
                      onClick={close}
                      className="rounded-xl bg-white bg-opacity-20 px-5 py-2 text-xs"
                    >
                      Close
                    </button>
                  </header>

                  <div className="md:w-[526px] space-y-4 p-4 text-sm">
                    <div className="flex gap-4">
                      <button onClick={previewAssessment} type="button" className="btn-primary-bordered">
                        Preview assessment
                      </button>
                      {is_added ? (
                        <Button
                          className="btn-primary-light heading-text font-normal"
                          is_busy={is_removing}
                          onClick={handleRemove}
                        >
                          Remove
                        </Button>
                      ) : (
                        <Button
                          type="button"
                          is_busy={is_adding}
                          className="btn-primary"
                          onClick={handleAdd}
                        >
                          Add to assessment
                        </Button>
                      )}
                    </div>
                    <div className="bg-grey rounded-xl p-2">
                      <h2 className="heading-2">{assessment.name}</h2>
                      <p className="helper-text">{assessment.description}</p>
                    </div>
                    <div className="helper-text bg-grey space-y-4 rounded-xl p-2">
                      <h2 className="heading-2">Assessment details</h2>
                      <div className="flex gap-4">
                        <p>
                          Total questions:{' '}
                          <span className="heading-text">
                            {assessment.question_set?.length || 0}
                          </span>
                        </p>
                        <p>
                          Question type: <span className="heading-text">{assessment.question_types.length}</span>
                        </p>
                      </div>
                      <ul className="heading-text flex flex-wrap gap-2 font-normal capitalize">
                        {assessment.question_types?.map(
                          (question_type, index: number) => (
                            <li
                              key={index}
                              className="inline-flex items-center gap-2 rounded-xl bg-white border p-2"
                            >
                              <QuestionTypeIcon question_type={question_type.toLowerCase()} />
                              {QUESTION_TYPES_DICTIONARY[question_type.toLowerCase()]?.name}
                            </li>
                          )
                        )}
                      </ul>
                      <div className="space-y-2">
                        <h2>Skills</h2>
                        <ul className="heading-text flex flex-wrap gap-2 font-normal capitalize">
                          {assessment.tags?.map((tag, index) => (
                            <li
                              key={index}
                              className="rounded-xl bg-white border p-2"
                            >
                              {tag}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <h2 className="heading-2">Assessment summary</h2>
                      <p className="helper-text">{assessment.summary}</p>
                    </div>
                    <div className="space-y-2">
                      <h2 className="heading-2">
                        Skill tested in this assessment
                      </h2>
                      <p className="helper-text whitespace-pre-wrap">
                        {assessment.skills_tested}
                      </p>
                    </div>
                  </div>
                </Dialog.Panel>
              </div>
            </div>
          </>
        )}
      </Dialog>

      <AssessmentPreviewModal
        is_open={is_assessment_preview_modal_open}
        assessment={assessment}
        close={closeAssessmentPreviewModal}
      />
    </>
  );
};
export default AssessmentDetailModal;
