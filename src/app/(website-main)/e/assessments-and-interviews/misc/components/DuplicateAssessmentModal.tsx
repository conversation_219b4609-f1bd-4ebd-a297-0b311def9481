import { ConfirmActionModal } from '@/components/shared';
import React, { useState } from 'react';
import toast from 'react-hot-toast';
import { duplicateAssessmentAPI } from '../api/duplicateAssessment';
import { TAssessmentListItem } from '../api/getMyAssessments';

interface DuplicateAssessmentModalProps {
  is_open: boolean;
  close: () => void;
  assessment: TAssessmentListItem;
  onSuccess: () => void;
}

const today = new Date().toISOString();

const DuplicateAssessmentModal: React.FC<DuplicateAssessmentModalProps> = ({
  is_open,
  close,
  assessment,
  onSuccess,
}) => {
  const [isDuplicating, setIsDuplicating] = useState(false);

  const handleDuplicate = async () => {
    try {
      setIsDuplicating(true);
      await duplicateAssessmentAPI(assessment.id);
      onSuccess();
      close();
    } catch (error) {
      console.error('Error duplicating assessment:', error);
      toast.error('Error duplicating assessment');
    } finally {
      setIsDuplicating(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <ConfirmActionModal
      isModalOpen={is_open}
      closeModal={close}
      title="Duplicate Assessment"
      confirmFunction={handleDuplicate}
      isConfirmingAction={isDuplicating}
      icon={
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M16 12.9V17.1C16 20.6 14.6 22 11.1 22H6.9C3.4 22 2 20.6 2 17.1V12.9C2 9.4 3.4 8 6.9 8H11.1C14.6 8 16 9.4 16 12.9Z"
            stroke="white"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M22 6.9V11.1C22 14.6 20.6 16 17.1 16H16V12.9C16 9.4 14.6 8 11.1 8H8V6.9C8 3.4 9.4 2 12.9 2H17.1C20.6 2 22 3.4 22 6.9Z"
            stroke="white"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      }
    >
      <div className="space-y-4">
        <p className="text-secondary-text text-sm">
          You are about to create a duplicate of this assessment. The duplicate will include all sections, questions, and some settings from the original assessment.
        </p>
        
        <div className="bg-gray-50 rounded-lg p-4 space-y-3">
          <h4 className="font-medium text-header-text">Assessment Details</h4>
          
          <div className="grid grid-cols-1 gap-2 text-sm">
            <div className="flex justify-between">
              <span className="text-body-text">Name:</span>
              <span className="font-medium text-header-text">{assessment.name} - Copy</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-body-text">Status:</span>
              <span className={`font-medium ${assessment.is_published ? 'text-green-600' : 'text-orange-600'}`}>
                {assessment.is_published ? 'Published' : 'Draft'}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-body-text">Candidates:</span>
              <span className="font-medium text-header-text">{assessment.no_candidates}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-body-text">Completed:</span>
              <span className="font-medium text-header-text">{assessment.completed.length}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-body-text">Created:</span>
              <span className="font-medium text-header-text">{formatDate(today)}</span>
            </div>
            
            {assessment.deadline && (
              <div className="flex justify-between">
                <span className="text-body-text">Deadline:</span>
                <span className="font-medium text-header-text">{formatDate(assessment.deadline)}</span>
              </div>
            )}
          </div>
        </div>
        
        <p className="text-secondary-text text-sm">
          The duplicate will be created as a draft and you can modify it as needed before publishing.
        </p>
      </div>
    </ConfirmActionModal>
  );
};

export default DuplicateAssessmentModal; 
