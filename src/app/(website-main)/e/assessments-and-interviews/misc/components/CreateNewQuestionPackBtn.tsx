import Link from "next/link";

type Props = {
  variant?: "primary" | "card";
  redirect_link?: string;
}
export default function CreateNewQuestionPackBtn({variant="primary", redirect_link}:Props){
  let link = `/e/assessments-and-interviews/tests-library/create-pack`
  if (redirect_link){
    link += `?redirect=${redirect_link}`
  }

  if (variant=="card"){
    return (
      <div className="space-y-2 h-full grid place-items-center relative rounded-md bg-white p-4 overflow-hidden">
        <Link className="btn-primary-light inline-flex gap-2" href={link}> Create a new question pack</Link>
      </div>
    )
  }

  if (variant=="primary"){
    return (
      <Link className="btn-primary inline-flex gap-2" href={link}> Create a new question pack</Link>
    )
  }
}
