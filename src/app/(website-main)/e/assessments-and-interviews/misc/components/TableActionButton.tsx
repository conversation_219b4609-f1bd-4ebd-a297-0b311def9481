import React from 'react';
import EllipsesVerticalIcon from '@/components/icons/jsx/EllipsesVerticalIcon';

const TableActionButton: React.FC = () => {
  return (
    <button
      onClick={e => e.stopPropagation()}
      className="py-2 px-4 flex items-center gap-4 rounded-lg bg-[#F8F9FB] outline outline-1 outline-[#EAE6FB] text-xs"
    >
      <p>Action</p>
      <span className='opacity-70'>
        <EllipsesVerticalIcon />
      </span>
    </button>
  );
};
export default TableActionButton;
