import React, { FormEvent, useCallback, useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import toast from 'react-hot-toast';
import {
  ALPHABETS,
  MULTIPLE_CHOICE_OPTIONS_LIMIT,
  MULTIPLE_CHOICE_OPTIONS_MIN_LIMIT,
  QUESTION_TYPES_DICTIONARY,
} from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants';
import { Question } from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import Modal from '@/components/Modal';
import Button from '@/components/shared/Button';
import Checkbox from '@/components/shared/Checkbox/Checkbox';
import DocumentDownloadIcon from '../../../../../../components/icons/jsx/DocumentDownloadIcon';
import PlusIconDark from '../../../../../../components/icons/jsx/PlusIconDark';
import TrashIcon from '../../../../../../components/icons/jsx/TrashIcon';
import ListBoxStrings from '../../../../../../components/shared/listbox-strings';
import { UploadIcon } from '../../../jobs/misc/icons';

interface Props {
  is_open: boolean;
  is_busy?: boolean;
  should_use_answer_index?: boolean;
  close: () => void;
  onSave: (edited_question: Question) => void;
  question: Question;
}

const EditQuestionModal: React.FC<Props> = ({
  is_open,
  is_busy,
  should_use_answer_index,
  close,
  question,
  onSave,
}) => {
  const [edited_question, setEditedQuestion] =
    React.useState<Question>(question);

  useEffect(() => {
    setEditedQuestion(question);
  }, [is_open, question]);

  function handleSubmit(e: FormEvent) {
    e.preventDefault();

    if (!edited_question.answer.length && edited_question.type !== 'essay') {
      alert('You need to select an answer');
      return;
    }

    onSave(edited_question);
  }

  const [image_urls, setImageURLs] = useState([] as string[]);
  const [image_blobs, setImageBlobs] = useState([] as Blob[]);
  const [image_files, setImageFiles] = useState([] as File[]);

  useEffect(() => {
    const _image_urls = question?.images || [];
    setImageURLs(_image_urls);
  }, [question]);

  function blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, _) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.readAsDataURL(blob);
    });
  }

  async function convertUploadedImage(acceptedFiles: any) {
    const _images_urls = image_urls;
    const _images_blobs = image_blobs;
    const _images_files = image_files;

    const processImage = async (file: any) => {
      const blob = new Blob([file], {
        type: file.type,
      });
      const image_base64 = await blobToBase64(blob);
      let sizeValid = await validateImageDimensions(image_base64, [
        { height: 300, width: 900 },
        { height: 400, width: 1200 },
        { height: 500, width: 1500 },
      ]);
      if (!sizeValid) {
        toast.error('Some of the images do not match the required dimension');
        return;
      }
      _images_files.push(file);
      _images_urls.push(image_base64);
      _images_blobs.push(blob);
    };

    acceptedFiles.forEach(processImage);

    setImageURLs(_images_urls);
    setImageBlobs(_images_blobs);
    setImageFiles(_images_files);
  }

  const validateImageDimensions = async (
    imageURL: string,
    dimensions: { height: number; width: number }[]
  ) => {
    return new Promise((resolve, reject) => {
      let img = new Image();
      img.onload = function () {
        let isWithinRange = false;
        // alert(img.width + ' ' + img.height);
        dimensions.forEach(d => {
          if (d.height == img.height && d.width == img.width) {
            isWithinRange = true;
          }
        });
        resolve(isWithinRange);
      };
      img.src = imageURL;
    });
  };

  const onDrop = useCallback(async (acceptedFiles: any) => {
    convertUploadedImage(acceptedFiles);
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    multiple: true,
    accept: {
      'image/*': [],
    },
    maxSize: 10 * 1024 * 1024, //10mb
  });

  function deleteImage(index: number) {
    const _image_urls = image_urls;
    const _image_blobs = image_blobs;
    const _image_files = image_files;

    _image_urls.splice(index, 1);
    _image_blobs.splice(index, 1);

    setImageURLs(_image_urls);
    setImageBlobs(_image_blobs);
    setImageFiles(_image_files);
  }

  return (
    <Modal is_open={is_open} title={'Edit question'} close={close}>
      <form
        onSubmit={handleSubmit}
        className="default-text-styles bg-light-accent-bg space-y-4 text-sm md:w-[539px]"
      >
        <div className="bg-light-accent-bg space-y-4 p-4">
          <div className="space-y-2  ">
            <p className="heading-text"> Images </p>
            <div className="flex flex-wrap gap-2">
              <div className="flex gap-2 overflow-x-auto">
                {image_urls.map((image_url, index) => (
                  <div className="flex-1 space-y-4  shadow-sm" key={index}>
                    <div
                      className="relative flex items-center justify-center overflow-hidden"
                      style={{ width: '900px', height: '300px' }}
                    >
                      <img
                        className="relative aspect-square h-full w-full rounded-xl object-cover"
                        src={image_url}
                        /* ref={imageRef} */
                      />
                      <button
                        className="size-8 absolute right-0 top-0 m-4 grid place-items-center rounded-full p-2 text-2xl hover:bg-black/20"
                        type="button"
                        onClick={() => deleteImage(index)}
                      >
                        &times;
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              <div
                className={`mt-3 flex h-[5.9375rem] cursor-pointer items-center gap-4 rounded-[0.875rem] border-[0.3px] border-dashed border-[rgb(117,90,226)] bg-white p-6`}
                {...getRootProps()}
              >
                <div className="">
                  <UploadIcon />
                </div>
                <div className="">
                  <p className="font-sans text-xs font-semibold text-[#755AE2] sm:text-sm">
                    Tap to upload images for this question
                  </p>
                  <span className="font-sans text-xs font-semibold text-[#755AE2] opacity-75">
                    Files types: PNG, JPG, Max size: 10MB
                  </span>
                </div>
                <input hidden {...getInputProps()} />
              </div>
            </div>
          </div>

          {/* Multiple response questions */}
          {edited_question?.type ===
            QUESTION_TYPES_DICTIONARY['multiple_response'].value && (
            <>
              <p className="">
                Candidates will be asked to choose one or more items from a list
                of choices.{' '}
              </p>
              <div>
                <label className="space-y-1">
                  <p className="heading-text">Question:</p>
                  <textarea
                    name="question"
                    className="input-white"
                    value={edited_question?.question}
                    onChange={e => {
                      setEditedQuestion({
                        ...edited_question,
                        question: e.target.value,
                      });
                    }}
                  ></textarea>
                </label>
              </div>
              <div className="space-y-1">
                <p className="heading-text">Options:</p>
                <ul className="space-y-1">
                  {edited_question?.answer_options?.map((option, index) => (
                    <li key={index}>
                      <div className="flex items-center justify-between gap-4">
                        <div className="btn-base h-full bg-[#E9E4FF]">
                          <span className="font-medium">
                            {ALPHABETS[index]}
                          </span>
                        </div>
                        <input
                          type="text"
                          className="input-white flex-1"
                          placeholder="enter option value here"
                          value={edited_question.answer_options[index]}
                          onChange={event => {
                            let new_options = [
                              ...edited_question.answer_options,
                            ];
                            let old_option =
                              edited_question.answer_options[index];
                            let answer_index =
                              edited_question.answer?.indexOf(old_option);
                            let new_answers = edited_question.answer;
                            if (answer_index >= 0) {
                              new_answers[answer_index] = event.target.value;
                            }
                            new_options[index] = event.target.value;
                            setEditedQuestion({
                              ...edited_question,
                              answer_options: new_options,
                              answer: new_answers,
                            });
                          }}
                        />
                        <button type="button" title="...">
                          <DocumentDownloadIcon />
                        </button>
                        <div className="flex items-center gap-2">
                          <button
                            type="button"
                            title="remove option"
                            onClick={() => {
                              if (
                                edited_question.answer_options.length >
                                MULTIPLE_CHOICE_OPTIONS_MIN_LIMIT
                              ) {
                                let new_options = [
                                  ...edited_question.answer_options,
                                ];
                                new_options.splice(index, 1);

                                let answer_index =
                                  edited_question.answer_options.indexOf(
                                    edited_question.answer_options[index]
                                  );
                                let new_answers =
                                  answer_index >= 0
                                    ? edited_question.answer_options.toSpliced(
                                        answer_index
                                      )
                                    : edited_question.answer_options;

                                setEditedQuestion({
                                  ...edited_question,
                                  answer_options: new_options,
                                  answer: new_answers,
                                });
                              }
                            }}
                          >
                            <TrashIcon />
                          </button>
                          <Checkbox
                            disabled={!!!edited_question.answer_options[index]}
                            onChange={e => {
                              if (e.target.checked) {
                                setEditedQuestion({
                                  ...edited_question,
                                  answer: edited_question.answer
                                    ? edited_question.answer.concat([
                                        e.target.value,
                                      ])
                                    : [e.target.value],
                                });
                              } else {
                                setEditedQuestion({
                                  ...edited_question,
                                  answer: edited_question.answer.toSpliced(
                                    edited_question.answer.indexOf(
                                      e.target.value,
                                      1
                                    )
                                  ),
                                });
                              }
                            }}
                            name="select answer"
                            checked={edited_question.answer?.includes(
                              edited_question.answer_options[index]
                            )}
                            value={edited_question.answer_options[index]}
                          />
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
              <button
                className="btn-primary-bordered container flex w-[80%] items-center justify-center gap-4 border-dashed text-black"
                type="button"
                disabled={
                  edited_question?.answer_options?.length >=
                  MULTIPLE_CHOICE_OPTIONS_LIMIT
                }
                onClick={() => {
                  if (
                    edited_question?.answer_options?.length <
                    MULTIPLE_CHOICE_OPTIONS_LIMIT
                  ) {
                    setEditedQuestion({
                      ...edited_question,
                      answer_options: [...edited_question.answer_options, ''],
                    });
                  }
                }}
              >
                <PlusIconDark />
                Add answer option
              </button>

              <div className="space-y-2">
                <p className="heading-text">Score</p>
                <label className="space-y-2">
                  <input
                    type="number"
                    required
                    name="score"
                    className="input-white"
                    placeholder="Enter score"
                    value={edited_question.points}
                    onChange={event => {
                      setEditedQuestion({
                        ...edited_question,
                        points: parseInt(event.target.value),
                      });
                    }}
                  />
                </label>
              </div>
            </>
          )}

          {/* Multi choice questions */}
          {edited_question?.type ===
            QUESTION_TYPES_DICTIONARY['multiple_choice'].value && (
            <>
              <p className="">
                Candidates will be asked to choose one or more items from a list
                of choices.{' '}
              </p>
              <div>
                <label className="space-y-1">
                  <p className="heading-text">Question:</p>
                  <textarea
                    name="question"
                    className="input-white"
                    value={edited_question?.question}
                    onChange={e => {
                      setEditedQuestion({
                        ...edited_question,
                        question: e.target.value,
                      });
                    }}
                  ></textarea>
                </label>
              </div>
              <div className="space-y-1">
                <p className="heading-text">Options:</p>
                <ul className="space-y-1">
                  {edited_question?.answer_options?.map((option, index) => (
                    <li key={index}>
                      <div className="flex items-center justify-between gap-4">
                        <div className="btn-base h-full bg-[#E9E4FF]">
                          <span className="font-medium">
                            {ALPHABETS[index]}
                          </span>
                        </div>
                        <input
                          type="text"
                          className="input-white flex-1"
                          placeholder="enter option value here"
                          value={edited_question.answer_options[index]}
                          onChange={event => {
                            let new_options = [
                              ...edited_question.answer_options,
                            ];
                            new_options[index] = event.target.value;
                            setEditedQuestion({
                              ...edited_question,
                              answer_options: new_options,
                            });
                          }}
                        />
                        <button type="button" title="...">
                          <DocumentDownloadIcon />
                        </button>
                        <button
                          type="button"
                          title="remove option"
                          onClick={() => {
                            if (
                              edited_question.answer_options.length >
                              MULTIPLE_CHOICE_OPTIONS_MIN_LIMIT
                            ) {
                              let new_options = [
                                ...edited_question.answer_options,
                              ];
                              new_options.splice(index, 1);
                              setEditedQuestion({
                                ...edited_question,
                                answer_options: new_options,
                              });
                            }
                          }}
                        >
                          <TrashIcon />
                        </button>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
              <button
                className="btn-primary-bordered container flex w-[80%] items-center justify-center gap-4 border-dashed text-black"
                type="button"
                disabled={
                  edited_question?.answer_options?.length >=
                  MULTIPLE_CHOICE_OPTIONS_LIMIT
                }
                onClick={() => {
                  if (
                    edited_question?.answer_options?.length <
                    MULTIPLE_CHOICE_OPTIONS_LIMIT
                  ) {
                    setEditedQuestion({
                      ...edited_question,
                      answer_options: [...edited_question.answer_options, ''],
                    });
                  }
                }}
              >
                <PlusIconDark />
                Add answer option
              </button>
              <div className="space-y-2">
                <p className="heading-text">Answer:</p>
                <ListBoxStrings
                  className="bg-white"
                  options={edited_question.answer_options?.filter(
                    (option: string) => option.trim() !== ''
                  )}
                  active_option={edited_question?.answer?.[0]}
                  setActiveOption={answer => {
                    setEditedQuestion({
                      ...edited_question,
                      answer: [answer],
                    });
                  }}
                />
              </div>

              <div className="space-y-2">
                <p className="heading-text">Score</p>
                <label className="space-y-2">
                  <input
                    type="number"
                    required
                    name="score"
                    className="input-white"
                    placeholder="Enter score"
                    value={edited_question.points}
                    onChange={event => {
                      setEditedQuestion({
                        ...edited_question,
                        points: parseInt(event.target.value),
                      });
                    }}
                  />
                </label>
              </div>
            </>
          )}

          {/* essay questions */}
          {edited_question?.type ===
            QUESTION_TYPES_DICTIONARY['essay'].value && (
            <>
              <div className="flex flex-col gap-y-4">
                <p className="">
                  Candidates will be asked to write out their answers.
                </p>
                <div>
                  <label className="space-y-1">
                    <p className="heading-text">Question:</p>
                    <textarea
                      name="question"
                      className="input-white h-max min-h-[100px]"
                      value={edited_question?.question}
                      onChange={e => {
                        setEditedQuestion({
                          ...edited_question,
                          question: e.target.value,
                        });
                      }}
                    ></textarea>
                  </label>
                </div>

                <div className="space-y-2">
                  <p className="heading-text">Score</p>
                  <label className="space-y-2">
                    <input
                      type="number"
                      required
                      name="score"
                      className="input-white"
                      placeholder="Enter score"
                      value={edited_question.points}
                      onChange={event => {
                        setEditedQuestion({
                          ...edited_question,
                          points: parseInt(event.target.value),
                        });
                      }}
                    />
                  </label>
                </div>
                <div>
                  <p className="heading-text">
                    Enter instructions for scoring the user
                  </p>
                  <label className="space-y-2">
                    <textarea
                      required
                      placeholder={`Please focus on the following points while grading:\n - Clarity of the answer\n - Use of relevant examples\n - Logical flow and structure\n - Grammar and spelling `}
                      className="input-grey min-h-[200px]"
                      name="question"
                      value={edited_question.instructions}
                      onChange={event => {
                        setEditedQuestion({
                          ...edited_question,
                          instructions: event.target.value,
                        });
                      }}
                    />
                  </label>
                </div>
              </div>
            </>
          )}

          {/* true or false questions */}
          {edited_question?.type ===
            QUESTION_TYPES_DICTIONARY['true_false'].value && (
            <>
              <p className="">
                Candidates will be asked to choose either true or false.
              </p>
              <div>
                <label className="space-y-1">
                  <p className="heading-text">Question:</p>
                  <textarea
                    name="question"
                    className="input-white"
                    value={edited_question?.question}
                    onChange={e => {
                      setEditedQuestion({
                        ...edited_question,
                        question: e.target.value,
                      });
                    }}
                  ></textarea>
                </label>
              </div>
              <div className="space-y-1">
                <p className="heading-text">Answer:</p>
                <ul className="space-y-1">
                  {'true, false'.split(', ').map((option, index) => (
                    <li key={index}>
                      <label className="flex items-center gap-2 capitalize">
                        <input
                          checked={edited_question.answer[0] == option}
                          onChange={e => {
                            setEditedQuestion({
                              ...edited_question,
                              answer: [e.target.value],
                            });
                          }}
                          name={question.id}
                          value={option}
                          type="radio"
                        />
                        {option}
                      </label>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="space-y-2">
                <p className="heading-text">Score</p>
                <label className="space-y-2">
                  <input
                    type="number"
                    required
                    name="score"
                    className="input-white"
                    placeholder="Enter score"
                    value={edited_question.points}
                    onChange={event => {
                      setEditedQuestion({
                        ...edited_question,
                        points: parseInt(event.target.value),
                      });
                    }}
                  />
                </label>
              </div>
            </>
          )}

          {/* fill in the blanks questions */}
          {edited_question?.type ===
            QUESTION_TYPES_DICTIONARY['fill_in_the_blanks'].value && (
            <>
              <p className="">
                Candidates will be asked to choose one or more items from a list
                of choices.{' '}
              </p>
              <div>
                <label className="space-y-1">
                  <p className="heading-text">Question:</p>
                  <textarea
                    name="question"
                    className="input-white"
                    value={edited_question?.question}
                    onChange={e => {
                      setEditedQuestion({
                        ...edited_question,
                        question: e.target.value,
                      });
                    }}
                  ></textarea>
                </label>
              </div>
              <div className="space-y-1">
                <p className="heading-text">Answers:</p>
                <ul className="space-y-1">
                  {edited_question?.answer_options?.map((option, index) => (
                    <li key={index}>
                      <div className="flex items-center justify-between gap-4">
                        <div className="btn-base h-full bg-[#E9E4FF]">
                          <span className="font-medium">
                            {ALPHABETS[index]}
                          </span>
                        </div>
                        <input
                          type="text"
                          className="input-white flex-1"
                          placeholder="enter option value here"
                          value={edited_question.answer_options[index]}
                          onChange={event => {
                            let new_options = [
                              ...edited_question.answer_options,
                            ];
                            new_options[index] = event.target.value;
                            setEditedQuestion({
                              ...edited_question,
                              answer_options: new_options,
                            });
                          }}
                        />
                        <button type="button" title="...">
                          <DocumentDownloadIcon />
                        </button>
                        <button
                          type="button"
                          title="remove option"
                          onClick={() => {
                            if (
                              edited_question.answer_options.length >
                              MULTIPLE_CHOICE_OPTIONS_MIN_LIMIT
                            ) {
                              let new_options = [
                                ...edited_question.answer_options,
                              ];
                              new_options.splice(index, 1);
                              setEditedQuestion({
                                ...edited_question,
                                answer_options: new_options,
                              });
                            }
                          }}
                        >
                          <TrashIcon />
                        </button>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
              <button
                className="btn-primary-bordered container flex w-[80%] items-center justify-center gap-4 border-dashed text-black"
                type="button"
                onClick={() => {
                  setEditedQuestion({
                    ...edited_question,
                    answer_options: [...edited_question.answer_options, ''],
                  });
                }}
              >
                <PlusIconDark />
                Add answer option
              </button>
              <div className="space-y-2">
                <p className="heading-text">Answer:</p>
                <ListBoxStrings
                  className="bg-white"
                  options={edited_question.answer_options?.filter(
                    (option: string) => option.trim() !== ''
                  )}
                  active_option={edited_question.answer as any}
                  setActiveOption={answer => {
                    setEditedQuestion({
                      ...edited_question,
                      answer: [answer],
                    });
                  }}
                />
              </div>
              <div className="space-y-2">
                <p className="heading-text">Score</p>
                <label className="space-y-2">
                  <input
                    type="number"
                    required
                    name="score"
                    className="input-white"
                    placeholder="Enter score"
                    value={edited_question.points}
                    onChange={event => {
                      setEditedQuestion({
                        ...edited_question,
                        points: parseInt(event.target.value),
                      });
                    }}
                  />
                </label>
              </div>
            </>
          )}
        </div>
        <div className="bg-white p-4">
          <Button
            is_busy={is_busy}
            className="btn-primary w-full"
            type="submit"
          >
            Save edit
          </Button>
        </div>
      </form>
    </Modal>
  );
};
export default EditQuestionModal;
