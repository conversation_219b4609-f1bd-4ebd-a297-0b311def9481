import React from 'react';
import CustomAssessmentIcon from '@/components/icons/jsx/CustomAssessmentIcon';
import CustomLibraryIcon from '@/components/icons/jsx/CustomLibraryIcon';
import InterviewAssessmentIcon from '@/components/icons/jsx/InterviewAssessmentIcon';
import LibraryQuestionIcon from '@/components/icons/jsx/LibraryQuestionIcon';
import OpenEmailIcon from '@/components/icons/jsx/OpenEmailIcon.jsx';
import QuestionPackSuccessIcon from '@/components/icons/jsx/QuestionPackSuccessIcon.jsx';
import AltModal from '@/components/Modal';
import { Button } from '@/components/shared';
import { useBooleanStateControl } from '@/hooks';
import { AssessmentState } from '../types/create-assessments';
import InitiateInterviewAssessmentModal from './CreateAssessmentModalStep1.5-Interview';
import CreateAssessmentDetailsModal from './CreateAssessmentModalStep2';

interface CreateAssessmentModalProps {
  isCreateAssessmentModalOpen: boolean;
  closeCreateAssessmentModal: () => void;
  clientId2?: string;
}

const CreateAssessmentModal: React.FC<CreateAssessmentModalProps> = ({
  isCreateAssessmentModalOpen,
  closeCreateAssessmentModal,
  clientId2,
}) => {
  const [initialAssessmentData, setIntialAssessmentData] = React.useState<
    AssessmentState & any
  >({
    id: '',
    name: '',
    description: '',
    summary: '',
    assessment_type: '',
    role: '',
    experience_level: '',
    question_type: '',
    is_custom: false,
    is_interview: false,
    should_open_first_question_modal: false,
    is_automatically_generated: false,
    sections: [],
  });

  const {
    state: isCreateInterviewAsessmentModalOpen,
    setTrue: openCreateInterviewAssessmentModal,
    setFalse: closeCreateInterviewAssessmentModal,
  } = useBooleanStateControl();
  const {
    state: isAssessmentDetailsModalOpen,
    setTrue: openAssessmentDetailsModal,
    setFalse: closeAssessmentDetailsModal,
  } = useBooleanStateControl();

  const handleInitiateInterviewAssessmentCreation = () => {
    setIntialAssessmentData({
      name: '',
      assessment_type: '',
      role: '',
      experience_level: '',
      is_custom: false,
      should_open_first_question_modal: false,
      question_type: '',
      is_automatically_generated: false,
      sections: [],
    });
    openCreateInterviewAssessmentModal();
  };
  return (
    <>
      <AltModal
        is_open={isCreateAssessmentModalOpen}
        close={closeCreateAssessmentModal}
        title="Questions creation"
      >
        <div className="grid gap-4 p-4 md:grid-cols-2">
          <button
            type="button"
            className="btn-modal-choice"
            onClick={() => {
              setIntialAssessmentData({
                ...initialAssessmentData,
                is_interview: false,
              });
              openAssessmentDetailsModal();
              closeCreateAssessmentModal();
            }}
          >
            <div>
              <div className="flex aspect-square w-16 items-center justify-center rounded-full bg-white">
                <LibraryQuestionIcon />
              </div>
              <p className="mt-2 text-[0.9rem] font-semibold text-primary">
                Select from Getlinked questions library
              </p>
              <p className="helper-text text-xs">
                Utilize our question and tailor it to suit your assessment needs
              </p>
            </div>
          </button>

          <button
            className="btn-modal-choice"
            type="button"
            onClick={() => {
              setIntialAssessmentData({
                ...initialAssessmentData,
                is_custom: true,
                is_interview: false,
              });
              openAssessmentDetailsModal();
            }}
          >
            <div>
              <div className="flex aspect-square w-16 items-center justify-center rounded-full bg-white">
                <CustomLibraryIcon />
              </div>
              <p className="mt-2 text-[0.9rem] font-semibold text-primary">
                Select from your questions library
              </p>
              <p className="helper-text text-xs">
                Create your own questions to suit your assessment needs
              </p>
            </div>
          </button>
          <button
            className="btn-modal-choice pointer-events-none !opacity-30"
            disabled
          >
            <div>
              <div className="flex aspect-square w-16 items-center justify-center rounded-full bg-white">
                <CustomAssessmentIcon />
              </div>
              <p className="mt-2 text-[0.9rem] font-semibold text-primary">
                AI custom assessment creation
              </p>
              <p className="helper-text text-xs">
                Give tips and watch us generate tailored questions that fit your
                assessment needs.
              </p>
            </div>
          </button>
          <button
            className="btn-modal-choice"
            onClick={handleInitiateInterviewAssessmentCreation}
          >
            <div>
              <div className="flex aspect-square w-16 items-center justify-center rounded-full bg-white">
                <InterviewAssessmentIcon />
              </div>
              <p className="mt-2 text-[0.9rem] font-semibold text-primary">
                Interview assessment
              </p>
              <p className="helper-text text-xs">
                Create a video assessment for your candidates
              </p>
            </div>
          </button>
        </div>
      </AltModal>

      <InitiateInterviewAssessmentModal
        closeCreateInterviewAssessmentModal={
          closeCreateInterviewAssessmentModal
        }
        isCreateInterviewAsessmentModalOpen={
          isCreateInterviewAsessmentModalOpen
        }
        openAssessmentDetailsModal={openAssessmentDetailsModal}
        initialAssessmentData={initialAssessmentData}
        setIntialAssessmentData={setIntialAssessmentData}
      />

      <CreateAssessmentDetailsModal
        initialAssessmentData={initialAssessmentData}
        closeAssessmentDetailsModal={closeAssessmentDetailsModal}
        isAssessmentDetailsModalOpen={isAssessmentDetailsModalOpen}
        clientId2={clientId2}
      />
    </>
  );
};

export default CreateAssessmentModal;
