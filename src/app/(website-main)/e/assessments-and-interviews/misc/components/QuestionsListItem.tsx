import React, { useEffect, useState } from 'react';
import EditIcon from '@/components/icons/EditIcon';
import TrashIcon from '@/components/icons/TrashIcon';
import { QUESTION_TYPES_DICTIONARY } from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants';
import { Question } from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import EditQuestionModal from '../../question-pack/misc/components/EditQuestionModal';
import { useBooleanStateControl } from '@/hooks';

interface Props {
  question: Question;
  index: number;
  deleteQuestion: () => void;
  assessmentId: string
  sectionId: string
  onEditQuestion: (question: Question) => void
}

const QuestionsListItem: React.FC<Props> = ({
  question,
  index,
  // editQuestion,
  deleteQuestion,
  assessmentId,
  sectionId,
  onEditQuestion
}) => {
  const {
    state: isEditQuestionModalOpen,
    setTrue: openEditQuestionModal,
    setFalse: closeEditQuestionModal,
  } = useBooleanStateControl();
  return (
    <div className="space-y-2 rounded-xl border p-4">
      <div className="flex items-center justify-between">
        <h3 className="heading-text">Question {index + 1}</h3>
        <div className="flex items-center gap-2">
          <button
            type="button"
            className="btn-icon-primary-light"
            onClick={openEditQuestionModal}
            title="edit question"
          >
            <EditIcon />
          </button>
          <button
            type="button"
            title="delete question"
            className="btn-icon-primary-light"
            onClick={deleteQuestion}
          >
            <TrashIcon />
          </button>
        </div>
      </div>
      <div
        dangerouslySetInnerHTML={{ __html: question.question }}
        className='tiptap ProseMirror'
      />

      {/* Multi choice questions */}
      {question?.type ===
        QUESTION_TYPES_DICTIONARY['multiple_choice'].value && (
          <div>
            <ol className="list-[upper-alpha] pl-6">
              {question?.answer_options?.map((option, index) => (
                <li key={index}>{option}</li>
              ))}
            </ol>
          </div>
        )}


      {/* Multi response questions */}
      {question?.type ===
        QUESTION_TYPES_DICTIONARY['multiple_response'].value && (
          <div>
            <ol className="list-[upper-alpha] pl-6">
              {question?.answer_options?.map((option, index) => (
                <li key={index}>{option}</li>
              ))}
            </ol>
          </div>
        )}


      {/* fill in the blanks questions */}
      {question?.type ===
        QUESTION_TYPES_DICTIONARY['fill_in_the_blanks'].value && (
          <div>
            <ol className="list-[upper-alpha] pl-6">
              {question?.answer_options?.map((option, index) => (
                <li key={index}>{option}</li>
              ))}
            </ol>
          </div>
        )}

      {/* true or false questions */}
      {question?.type ===
        QUESTION_TYPES_DICTIONARY['true_false'].value && (
          <div>
            <ol className="list-[upper-alpha] capitalize pl-6">
              {"true, false".split(", ").map((option, index) => (
                <li key={index}>{option}</li>
              ))}
            </ol>
          </div>
        )}

      {/* essay questions */}
      {question?.type === QUESTION_TYPES_DICTIONARY['essay'].value && (
        <div className="bg-grey min-h-[100px] rounded-md p-4 text-xs">
          <p>Answer should be entered here..</p>
        </div>
      )}

      <EditQuestionModal
        closeEditQuestionModal={closeEditQuestionModal}
        isEditQuestionModalOpen={isEditQuestionModalOpen}
        question={question}
        questionType={question.type as "multiple_choice" | "multiple_response" | "fill_in_the_blanks" | "true_false" | "essay"}
        assessmentId={assessmentId}
        sectionId={sectionId}
        onSave={onEditQuestion}
      />
    </div>
  );
};
export default QuestionsListItem;
