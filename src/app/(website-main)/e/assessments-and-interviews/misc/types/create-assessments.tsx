export type Section = {
  id: string;
  name: string;
  section_name?: string;
  category: string;
  //   total_questions: number;
  total_points?: number;
  question_types: string[];
  question_types_dict: Record<string, number>;
  question_set: Question[];
  test: string[]; // a list of linked library test ids
  question_pack: string[]; // a list of linked custom question packs

  number_of_questions?: number;

  order?: number;
};

export type Invitation = {
  name: string;
  email: string;
  sent?: boolean;
};

export type Role = {
  use_ai: boolean;
  id: number;
  name: string;
};

export type _Question = {
  id: string;
  type: string;
  title: string;
  question: string;
  instructions?: string;
  answer_options: string[];
  is_shuffled?: boolean;
  is_custom?: boolean;
  should_ai_answer?: boolean;
  points?: number;
  time_to_answer?: number;
  answer: string[];
  role: Role;
  experience_level: string;
  category: string;
  label: string;
  tags: string[];
  created_at: string;
  updated_at: string;
  test: string;
  section: string;
};

export type Question = {
  images?: string[];
  image_urls?: string[];
} & _Question;

export type CreateQuestion = {
  images?: Blob[];
} & _Question;

export type QuestionType = {
  value: string;
  name: string;
  count_name: string;
};

export type QuestionTypeDitionary = {
  [key: string]: QuestionType;
};

export type Comment = {
  id: string;
  timestamp: string;
  text: string;
  result_overview: string;
  recruiter_name: string;
  rating: number;
};

export interface LibraryAssessment {
  id: string;
  total_questions: number;
  question_types: string[];
  // question_set: (Questionset | Questionset2)[];
  question_set: Question[];
  relevant_roles: Relevantrole[];
  label: string;
  name: string;
  description: string;
  summary: string;
  skills_tested: string;
  experience_level: string;
  tags: string[];
  section: string;
}

interface Relevantrole {
  id: number;
  name: string;
  use_ai: boolean;
}

interface Questionset2 {
  id: string;
  type: string;
  question: string;
  answer_options: string[];
  answer: string[];
  role: number;
  experience_level: string;
  category: null;
  points: number;
  label: null;
  tags: any[];
  test: string;
  section: null;
  is_custom: boolean;
  time: number;
  creator: null | number;
  created_at: string;
  updated_at: string;
  instructions: string;
  image_urls: any[] | null;
  has_image: boolean;
  is_shuffled: boolean;
}

interface Questionset {
  id: string;
  type: string;
  question: string;
  answer_options: string[];
  answer: string[];
  role: null;
  experience_level: null;
  category: null;
  points: number;
  label: null;
  tags: any[];
  test: string;
  section: null;
  is_custom: boolean;
  time: number;
  creator: number;
  created_at: string;
  updated_at: string;
  instructions: string;
  image_urls: any[] | null;
  has_image: boolean;
  is_shuffled: boolean;
}

// export type LibraryAssessment = {
//   id: string;
//   name: string;
//   description: string;
//   summary: string;
//   skills_tested: string;
//   role: string;
//   experience_level: string;
//   label: string;
//   tags: string[];
//   section: string;
//   category: string;

//   total_questions: number;

//   question_types: string[];
//   question_types_dict: Record<string, number>;

//   question_set: Question[];
// };

export type QuestionPack = {
  name: string;
  id: string;
  description: string;
  tags?: string[];
  label?: string;
  question_set: Question[];
  total_questions: number;

  question_types: string[];
};

export type TimeLimitUnits = 'second(s)' | 'minute(s)' | 'hour(s)' | 'day(s)';
export type InvitationTypes = 'email' | 'pool' | 'file';
export type InviteInfoType = {
  invite: string; //invite id
  invite_id?: string;
  invitation_type: InvitationTypes;
};

export type AssessmentDeadlineSettings = {
  time_limit: number;
  deadline?: any;
  start_time?: any;
  commencement_settings?: 'yes' | 'no';
};

export type InterviewAssessment2 = AssessmentDeadlineSettings & {
  id: string;
  name: string;
  assessment_type?: string;
  description: string;
  summary: string;
  intro_video?: Blob;
  is_custom: boolean;
  is_published: boolean;

  last_page_url: string;

  skills_tested: string;
  role: number;
  experience_level: string;
  role_level: string;
  label: string;
  tags: string[];
  //   section: string;
  //   total_questions: number;
  //   question_types: string[];
  question_types_dict: QuestionTypeDitionary;
  sections: Section[];
  invitations: Invitation[];
  invite_info?: InviteInfoType;

  type?: 'flexible' | 'fixed';

  is_shuffle: boolean;
  is_shuffle_sections: boolean;
  reminder_time: number;

  // protoring options
  is_webcam_snapshot: boolean;
  is_restrict_copying: boolean;
  is_restrict_tab_change: boolean;
  is_track_paste: boolean;
  is_stop_screen_sharing: boolean;
  is_invigilate_assessment: boolean;
  is_identity_verification: boolean;
  full_screen_tolerance_level: number;
  window_change_tolerance_level: number;

  bulk_tolerance_setup: {
    combined_tolerance: string;
    options: { option: string }[];
  };
};

export type Assessment = AssessmentDeadlineSettings & {
  id: string;
  name: string;
  assessment_type?: string;
  description: string;
  summary: string;
  intro_video?: Blob;
  is_custom: boolean;
  is_published: boolean;

  last_page_url: string;

  skills_tested: string;
  role: Role;
  experience_level: string;
  role_level: string;
  label: string;
  tags: string[];
  //   section: string;
  //   total_questions: number;
  //   question_types: string[];
  question_types_dict: QuestionTypeDitionary;
  sections: Section[];
  invitations: Invitation[];
  invite_info?: InviteInfoType;

  type?: 'flexible' | 'fixed';

  is_shuffle: boolean;
  is_shuffle_sections: boolean;
  reminder_time: number;

  // protoring options
  is_webcam_snapshot: boolean;
  is_restrict_copying: boolean;
  is_restrict_tab_change: boolean;
  is_track_paste: boolean;
  is_stop_screen_sharing: boolean;
  is_invigilate_assessment: boolean;
  is_identity_verification: boolean;
  full_screen_tolerance_level: number;
  window_change_tolerance_level: number;

  bulk_tolerance_setup: {
    combined_tolerance: string;
    options: { option: string }[];
  };
};

// this is for already created assessments
export type AssessmentObject = Assessment & {
  ongoing: Candidate[];
  completed: Candidate[];
  not_started: Candidate[];
  no_candidates: number;
  created_at: string;
  candidates_data: { candidate_email: string; candidate_name: string }[];
};
export type InterviewAssessmentObject = InterviewAssessment & {
  ongoing: Candidate[];
  completed: Candidate[];
  not_started: Candidate[];
  no_candidates: number;
  created_at: string;
  candidates_data: { candidate_email: string; candidate_name: string }[];
};

export type CandidateAssessmentStatus = 'not_started' | 'completed' | 'ongoing';

export type CommentActions = 'edit comment' | 'delete comment';

export type JobApplicationData = {
  id: string;
  name: string;
  email: string;
  years_of_experience: string;
  current_location: string;
  phone_number: string;
  about_me: string | null;
  gender: string | null;
  portfolio_link: string | null;
  linkedin_link: string | null;
  cover_letter_text: string;
  cover_letter_file: string;
  custom_job_requirements: string[];
  cv: string;
  cv_data: string;
  cv_role: string | null;
  percentage_match: number;
  matching_reason: string;
  strength: string[] | null;
  weakness: string[] | null;
  cv_and_resume_id: string;
  cover_letter_id: string | null;
  expected_salary: string;
  is_talent: boolean;
  unique_id: string;
  job_stage: string;
  trail: string[];
  status: string | null;
  is_existing_in_cv_parser_tbl: boolean;
  is_seen: boolean;
  created_at: string;
  updated_at: string;
  job: {
    id: string;
    unique_id: string;
    job_title: string;
    description: string;
    proficiency_level: string;
    working_option: string;
    work_experience: string;
    job_type: string;
    industry: string;
    industry_category: string | null;
    job_tags: string | null;
    location: string;
    salary_type: string | null;
    salary_range: string;
    min_salary: number;
    max_salary: number;
    fixed_salary: number | null;
    salary_currency: string;
    salary_negotiable: boolean;
    responsibilities: string[] | null;
    qualifications: string[] | null;
    requirements: string[] | null;
    compulsory_requirements: string[] | null;
    exclusion_list: string[] | null;
    added_advantage: string[] | null;
    application_deadline: string;
    application_start_date: string;
    accpting_applications: boolean;
    benefits: string[] | null;
    job_status: boolean;
    show_in_career_page: string;
    type_of_assessment: string;
    job_custom_fields: string[];
    company_overview: string;
    job_application_requirement_custom_fields: string[] | null;
    post_as_anonymous: boolean;
    logo: string;
    pipeline_type: boolean;
    viewers_count: number;
    created_at: string;
    updated_at: string;
    company: string;
    recruiter: string;
    requirement_custom_fields: string;
    pipeline: string;
    talent: string[];
    bookmarks: string[];
    team_member: string[];
  };
  pipeline: string | null;
};

export type CandidateAboutResult = {
  job_application: JobApplicationData;
  cv_parser_id: string;
  skills: string[];
  score_reason: string;
};

export type Candidate = {
  was_forced_submission: any;
  id: string;
  name: string;
  candidate_email: string;
  candidate_name: string;
  overall_percentage: number;
  total_score?: number;
  flags_count: number;
  invite_date: string;
  date_taken?: string;
  status: CandidateAssessmentStatus;
  job_application: JobApplicationData;
};

export type AssessmentResult = {
  assessment_name: string;
  role: string;
  experience_level: string;
  total_no_questions: number;
  total_invites: number;
  completed: number;
  ongoing: number;
  not_started: number;
  average_score: number;
  candidates: Candidate[];
};

export type ProctoringFlag = {
  flag: string;
  time: string;
  screenshot: string;
  pc_capture: string;
};

// FOR NOW CLEAN FLAG TYPE
export interface CleanFlag {
  'Too close from camera'?: string;
  'No Face'?: string;
  'Multiple Face'?: string;
  'Illegal Object'?: string;
}

export type CandidateResult = {
  id: string;
  candidate_email: string;
  assessment: Assessment;
  previous_candidate_email: string | null;
  next_candidate_email: string | null;
  result: Candidate &
  JobApplicationData & {
    time_started: string;
    time_ended: string;
    completion_duration: number;
    total_questions: number;
    total_questions_answered: number;
  };
  performance: {
    candidate_email: string;
    assessment_id: string;
    report: Record<
      string,
      Record<
        string,
        {
          answered: number;
          not_answered: number;
          correct_answer: number;
        }
      >
    >;
    section_scores: Record<
      string,
      {
        percentage: number;
        total_points: number;
        earned_points: number;
        section_id: string;
      }
    >;
  };
  rating: number;
  comment: string;
  proctoring_report: {
    id: string;
    candidate_email: string;
    assessment: string;
    device_used: string;
    webcam_enabled: boolean;
    location: string;
    mouse_out_of_window: boolean;
    tab_switched: boolean;
    flags: ProctoringFlag[];
    clean_flags: ProctoringFlag[];
    profile_photo: string;
    photo_id: string;
    was_forced_submission_window_change: boolean;
    was_forced_submission_full_screen: boolean;
    full_screen_tolerance_used: number;
    window_change_tolerance_used: number;

    bulk_proctor_used: number;
    bulk_proctor_option: {
      key: string;
      value: number;
    }[];
    /* video?: null, */
    /* screenshots: [] */
  };
};

export type CandidateSectionAnswer = {
  id: string;
  candidate: string;
  answer: string[];
  is_correct: false;
  ai_comments?: string;
  score: number;
  invite_id: string;
  section_id: string;
  question: string;
  question_body: string;
};

export type AddedLibraryQuestionType = {
  [key: string]: {
    questions: Question[];
    section: number;
  };
};
export type AddedQuestionPackType = {
  [key: string]: {
    questions: Question[];
    section: number;
  };
};

export type ProctoringOptions =
  | 'webcam_snapshot'
  | 'restrict_copying'
  | 'restrict_tab_change'
  | 'track_paste'
  | 'stop_screen_sharing'
  | 'identity_verification'
  | 'invigilate_assessment';

export interface _AssessmentState {
  id: string;
  should_open_first_question_modal: boolean;
  is_automatically_generated: boolean;

  invitations: Invitation[];
  choosen_invitation_type: InvitationTypes;
  selected_file_fields: string[];
  file_details: any;
  delete_invites: string[];

  custom_email_message?: string;
  custom_email_subject?: string;
  cover_image?: Blob;
}

export type AssessmentState = _AssessmentState;

export type TeamMemberRoleTypes = 'editor' | 'viewer';
export type TeamMember = {
  id: string;
  company_email: string;
  role?: TeamMemberRoleTypes;
};

export type InterviewPack = {
  id: string;
  name: string;
  question_set: InterviewPackQuestion[];
  description: string;
  recruiter: string;
};

export type InterviewAssessmentQuestion = {
  id: string;
  question: string;
  section: string | null;
  time: number;
};

export type InterviewAssessment = {
  id: string;
  name: string;
  questions: InterviewAssessmentQuestion[];
  role: string;
  role_level: string;
  creator: string;
  total_questions: number;

  no_candidates: number;
  completed: number;
  paused: number;
  ongoing: number;
  not_started: number;

  is_custom?: boolean;
  description?: string;
  is_published?: boolean;
  commencement_settings?: 'yes' | 'no';
  deadline?: string;
  average_score?: number;
  total_invites?: number;
  candidates?: any[];
};

export type InterviewPackQuestion = {
  id: string;
  section: string; // question category
  question: string;
  time: number;
};

export type InterviewCandidateResultAnswer = {
  id: string;
  question: string;
  time: number;
  question_body: string;
  video_url: string;
  candidate: string;
  transcription: string;
};

export type InterviewCandidateResult = {
  id: string;
  candidate_email: string;
  video_url: any;
  candidate_name: string;
  invite_date: string;
  date_taken: any;
  time_started: any;
  time_ended: any;
  status: CandidateAssessmentStatus;
  has_submitted: boolean;
  /* assessment: any */
  interview: InterviewAssessment;
  verification_docs: {
    photo_id_path: string;
    profile_photo: string;
  } | null;
  interview_answers: InterviewCandidateResultAnswer[];
};

export type FetchCandidateResult = {
  /* pagination: { */
  /*   total_pages: number, */
  /*   total_items: number, */
  /*   current_page: number, */
  /*   has_next: boolean, */
  /*   has_previous: boolean, */
  /* }, */
  candidates: Candidate[];
};

export type FetchCandidateInviteResults = {
  data: CandidateInviteResult[];
};

export type CandidateInviteResult = {
  recipient_name: string;
  invite_id: string;
  recipient: string;
  sender: string;
  sent_at: string;
  status: string;
  subject: string;
};

export type AnalyzedFileType = {
  fields: string[];
  field_details: Record<string, number>;
  names_and_emails: { name: string; email: string }[];
};

export type AnalyzedFileErrorType = {
  error: string;
  count: number;
  details: Record<
    string,
    {
      email: string;
      error: string;
    }
  >;
};

export type ProctoringToleranceOption = {
  id: string;
  title: string;

  description: string;
  modal_description: string;
};

export type BulkProctorOption = {
  key: proctorOption;
  value: string;
};

export enum proctorOption {
  fullScreenExit = 'FULL_SCREEN_EXIT',
  windowChange = 'WINDOW_CHANGE',
  tabChange = 'TAB_CHANGE',
  multiFace = 'MULTIPLE_FACE',
  differentFace = 'DIFFERENT_FACE',
  illegalObject = 'ILLEGAL_OBJECT',
}
