// 'use client';

// import { format } from 'date-fns';
// import DatePicker from 'react-datepicker';
// import 'react-datepicker/dist/react-datepicker.css';
// import { useRouter } from 'next/navigation';
// import { useEffect, useState } from 'react';
// import TableActionButton from '@/app/(website-main)/e/assessments-and-interviews/misc/components/TableActionButton';
// import {
//   ASSESSMENT_PUBLISH_FILTERS,
//   ASSESSMENT_TABLE_OPTIONS,
//   ASSESSMENT_TABLE_OPTIONS_DICTIONARY,
//   TIME_LIMIT_UNITS,
//   YES_OR_NO,
// } from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants';
// import {
//   Assessment,
//   AssessmentDeadlineSettings,
//   AssessmentObject,
//   TimeLimitUnits,
// } from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
// import DropdownMenu from '@/components/DropdownMenu';
// import PlusIcon from '@/components/icons/PlusIcon';
// import Modal from '@/components/Modal';
// import {
//   Button as AltButton,
//   AvatarGroupName,
//   ToolTip,
// } from '@/components/shared';
// import Avatar from '@/components/shared/avatar-alt';
// import Button from '@/components/shared/Button';
// import { Search, Spinner } from '@/components/shared/icons';
// import ListBox from '@/components/shared/listbox';
// import Loader from '@/components/shared/loader';
// import app_fetch from '@/lib/api/appFetch.js';
// import { createNewAssessmentChannel } from '@/lib/utils/event-bus/channels/createNewAssessmentChannel';
// import { secondsToLargestUnit } from '@/lib/utils/functions';
// import { XIcon } from '../../../jobs/misc/icons';
// import { editAssessmentAPI, isValidDateTime } from '../api/Assessment';
// import DeleteAssessmentModal from '../components/DeleteAssessmentModal';
// import InviteCandidatesModal from '../components/InviteCandidatesModal';
// import PreviewAssessmentSectionsModal from '../components/PreviewAssessmentSectionsModal';
// import TimeInput from '../components/TimeInput';
// import { useGetAllAssessments, useGetMyAssessments } from '../api';
// import { InitiateCreateAssessmentModal } from '../components';
// import { useBooleanStateControl } from '@/hooks';

// const MyAssessments = () => {
//   const router = useRouter();
  
//   const [searchTerm, setSearchTerm] = useState<string>('');
//   const [pagesSize, setPageSize] = useState<number>(10);
//   const [pageNumber, setPageNumber] = useState<number>(1);
//   const fetchOptions = {
//     search: searchTerm,
//     size: pagesSize,
//     page: pageNumber,
//   }
//   const { data, isLoading: is_loading_assessments, refetch: loadMyAssessments } = useGetAllAssessments(fetchOptions)

//   const [assessments, setAssessments] = useState(data?.results);
//   const [filtered_assessments, setFilteredAssessments] = useState<
//     AssessmentObject[]
//   >([]);



//   useEffect(() => {
//     loadMyAssessments();
//   }, []);

//   const [search_term, setSearch_Term] = useState<string>('');
//   const [assessment_published_filter, setAssessmentPublishedFilter] =
//     useState<string>('');

//   function clearFilters() {
//     setSearch_Term('');
//     setAssessmentPublishedFilter('');
//   }

//   useEffect(() => {
//     setFilteredAssessments(
//       assessments.filter(assessment => {
//         let should_show = true;
//         if (search_term) {
//           should_show =
//             should_show &&
//             assessment.name.toLowerCase().includes(search_term.toLowerCase());
//         }
//         if (assessment_published_filter == 'is_draft') {
//           should_show = should_show && assessment.is_published == false;
//         }

//         if (assessment_published_filter == 'is_published') {
//           should_show = should_show && assessment.is_published == true;
//         }
//         return should_show;
//       })
//     );
//   }, [assessments, search_term, assessment_published_filter]);

//   const [active_assessment, setActiveAssessment] = useState<
//     Assessment | undefined
//   >(undefined);
//   const [is_delete_assessment_modal_open, setIsDeleteAssessmentModalOpen] =
//     useState(false);

//   function openDeleteAssessmentModal() {
//     setIsDeleteAssessmentModalOpen(true);
//   }

//   function closeDeleteAssessmentModal() {
//     setIsDeleteAssessmentModalOpen(false);
//   }

//   const [is_assessment_preview_modal_open, setIsAssessmentPreviewModalOpen] =
//     useState(false);

//   function openAssessmentPreviewModalOpen() {
//     setIsAssessmentPreviewModalOpen(true);
//   }

//   function closeAssessmentPreviewModalOpen() {
//     setIsAssessmentPreviewModalOpen(false);
//   }

//   const [is_invite_candidates_modal_open, setIsInviteCandidatesModalOpen] =
//     useState(false);

//   function openInviteCandidatesModalOpen() {
//     setIsInviteCandidatesModalOpen(true);
//   }

//   function closeInviteCandidatesModal() {
//     setIsInviteCandidatesModalOpen(false);
//   }

//   const [
//     active_assessment_deadline_settings,
//     setActiveAssessmentDeadlineSettings,
//   ] = useState<
//     | (AssessmentDeadlineSettings & {
//       is_started: boolean;
//     })
//     | undefined
//   >();
//   const [
//     is_loading_assessment_deadline_settings,
//     setIsLoadingAssessmentDeadlineSettings,
//   ] = useState(false);
//   const [
//     is_assessment_deadline_settings_modal_open,
//     setIsAssessmentDeadLineSettingsModalOpen,
//   ] = useState(false);

//   function openAssessmentDeadlineSettingsModal(active_assessment: Assessment) {
//     setIsLoadingAssessmentDeadlineSettings(true);
//     setIsAssessmentDeadLineSettingsModalOpen(true);
//     app_fetch(`assessments/${active_assessment.id}/`)
//       .then(res => res.json())
//       .then((result: AssessmentObject) => {
//         setActiveAssessmentDeadlineSettings({
//           is_started: result.start_time
//             ? new Date() > new Date(result?.start_time)
//             : result.completed.length + result.ongoing.length == 0,
//           // @ts-ignore
//           time_limit_unit: secondsToLargestUnit(result.time_limit).unit,
//           // @ts-ignore
//           time_limit: secondsToLargestUnit(result.time_limit).time,
//           commencement_settings: result.commencement_settings ? 'yes' : 'no',
//           start_time: result.start_time?.split('+')[0],
//           // `${format(value.start_time, 'yyyy-MM-dd')}T${value.start_time.toLocaleTimeString(undefined, { hourCycle: 'h23', hour: '2-digit', minute: '2-digit', second: '2-digit' })}`
//           deadline: `${format(new Date(result.deadline), 'yyyy-MM-dd')}T${new Date(result.deadline || 0).toLocaleTimeString(undefined, { hourCycle: 'h23', hour: '2-digit', minute: '2-digit', second: '2-digit' })}`,
//         });
//       })
//       .finally(() => {
//         setIsLoadingAssessmentDeadlineSettings(false);
//       });
//   }

//   function closeAssessmentDeadlineSettingsModal() {
//     setActiveAssessmentDeadlineSettings(undefined);
//     setIsAssessmentDeadLineSettingsModalOpen(false);
//   }

//   const [
//     is_saving_assessment_deadline_settings,
//     setIsSavingAssessmentDeadlineSettings,
//   ] = useState(false);

//   function saveAssessessmentDeadlineSettings() {
//     let startTime = new Date(active_assessment_deadline_settings?.start_time);
//     let deadline = new Date(active_assessment_deadline_settings?.deadline);

//     // console.log(active_assessment_deadline_settings);
//     // return;
//     // if (!isValidDateTime(active_assessment_deadline_settings?.deadline)) {
//     //   alert('Please enter a valid end date');
//     //   return;
//     // }
//     if (startTime >= deadline) {
//       alert('Assessments cannot end before the start time');
//       return;
//     }

//     if (active_assessment) {
//       setIsSavingAssessmentDeadlineSettings(true);
//       editAssessmentAPI({
//         id: active_assessment.id,
//         ...active_assessment_deadline_settings,
//       })
//         .then(() => {
//           setIsAssessmentDeadLineSettingsModalOpen(false);
//           loadMyAssessments();
//         })
//         .finally(() => {
//           setIsSavingAssessmentDeadlineSettings(false);
//         });
//     }
//   }

//   function handleAssessmentAction(
//     assessment: AssessmentObject,
//     selected_option: (typeof ASSESSMENT_TABLE_OPTIONS_DICTIONARY)[keyof typeof ASSESSMENT_TABLE_OPTIONS_DICTIONARY]
//   ) {
//     setActiveAssessment(assessment);
//     switch (selected_option) {
//       case ASSESSMENT_TABLE_OPTIONS_DICTIONARY['view details']:
//         router.push(
//           `/e/assessments-and-interviews/assessments/${assessment.id}`
//         );
//         break;
//       case ASSESSMENT_TABLE_OPTIONS_DICTIONARY['set deadline']:
//         openAssessmentDeadlineSettingsModal(assessment);
//         break;
//       case ASSESSMENT_TABLE_OPTIONS_DICTIONARY['delete assessment']:
//         openDeleteAssessmentModal();
//         break;
//       case ASSESSMENT_TABLE_OPTIONS_DICTIONARY['preview assessment']:
//         openAssessmentPreviewModalOpen();
//         break;
//       case ASSESSMENT_TABLE_OPTIONS_DICTIONARY['invite candidates']:
//         openInviteCandidatesModalOpen();
//         break;
//     }
//   }

//   const initiateCreateNewAssessment = () => {
//     createNewAssessmentChannel.emit('onCreateButtonClick');
//   };


//   const {
//     state: isCreateAssessmentModalOpen,
//     setTrue: openCreateAssessmentModal,
//     setFalse: closeCreateAssessmentModal
//   } = useBooleanStateControl();



//   return (
//     <>
//       <div className="relative flex h-full flex-1 flex-col">
//         {assessments.length > 0 && (
//           <header className="flex items-center justify-between  gap-4 rounded-md bg-white px-6 py-4 max-md:flex-col">
//             <h2 className="text-base font-normal text-header-text">
//               {search_term || (assessment_published_filter && 'Filtered')}{' '}
//               Assessments :{' '}
//               <span className="font-bold text-body-text">
//                 {filtered_assessments.length}
//               </span>
//             </h2>
//             <div className="flex items-stretch gap-2">
//               <ListBox
//                 options={ASSESSMENT_PUBLISH_FILTERS}
//                 className="border py-2 focus:outline-none"
//                 triggerClass="text-sm"
//                 placeholder="Published status"
//                 active_option={assessment_published_filter}
//                 setActiveOption={_option => {
//                   setAssessmentPublishedFilter(_option);
//                 }}
//                 value_key="id"
//                 readable_text_key="name"
//               />
//               <div className="relative">
//                 <Search className="absolute right-[5%] top-[25%]" />
//                 <input
//                   type="text"
//                   placeholder="Search"
//                   value={search_term}
//                   maxLength={12}
//                   onChange={e => setSearch_Term(e.target.value)}
//                   className="rounded-lg border-[1.5px] border-[#D6D6D6] px-3.5 py-2 text-[0.8rem] transition-all focus:outline-none sm:px-4"
//                 />
//               </div>

//               {(search_term || assessment_published_filter) && (
//                 <AltButton
//                   onClick={clearFilters}
//                   variant="extralight"
//                   icon={<XIcon width={20} height={20} />}
//                   className="border-grey py-1.5"
//                   size="tiny"
//                 >
//                   Clear Filter
//                 </AltButton>
//               )}
//             </div>
//           </header>
//         )}
//         {is_loading_assessments ? (
//           <div className="flex h-full w-full items-center justify-center">
//             <Spinner />
//           </div>
//         ) : filtered_assessments.length === 0 ? (
//           <div className="grid min-h-[80vh] flex-1 place-content-center">
//             <div className="rounded-md border border-[#DADADA]/20 bg-gradient-to-b from-[#755ae20a] via-transparent to-transparent p-12 text-center">
//               <div className="container max-w-xs space-y-2">
//                 <div className="container flex justify-center">
//                   <img
//                     src="/images/create-assessments/no-assessment.png"
//                     alt="writing with pen"
//                   />
//                 </div>
//                 {assessments.length === 0 ? (
//                   <>
//                     <h2>You currently do not have any open assessment</h2>
//                     <p className="text-sm text-[#7D8590]">
//                       Your running assessments would appear here, click button
//                       below to create an assessment
//                     </p>
//                     <button
//                         onClick={openCreateAssessmentModal}
//                       className="btn-primary inline-flex gap-2"
//                       type="button"
//                     >
//                       <PlusIcon />
//                       Create new assessment
//                     </button>
//                   </>
//                 ) : (
//                   <>
//                     <h2>No assessment matches the filter</h2>
//                     <p className="text-sm text-[#7D8590]">
//                       Clear the filters to see more results.
//                     </p>
//                     <button
//                       onClick={clearFilters}
//                       className="btn-primary inline-flex gap-2"
//                       type="button"
//                     >
//                       Clear filters
//                     </button>
//                   </>
//                 )}
//               </div>
//             </div>
//           </div>
//         ) : (
//           <div className="mt-2 max-w-[100vw] space-y-2 overflow-x-hidden">
//             <div className="overflow-x-auto">
//               <table className="w-full divide-y rounded-md bg-white">
//                 <thead>
//                   <tr>
//                     {'Id, Name, Candidates, Progress, Date Created, Deadline, Action'
//                       .split(', ')
//                       .map((heading, index) => (
//                         <th
//                           key={index}
//                           className="p-4 font-normal text-header-text"
//                         >
//                           <p className={index == 3 ? '' : 'text-left'}>
//                             {heading}
//                           </p>
//                         </th>
//                       ))}
//                   </tr>
//                 </thead>
//                 <tbody className="divide-y">
//                   {filtered_assessments.map((assessment, index) => (
//                     <tr
//                       className="cursor-pointer text-sm hover:bg-primary-light"
//                       onClick={() => {
//                         router.push(
//                           `/e/assessments-and-interviews/assessments/${assessment.id}`
//                         );
//                       }}
//                       key={index}
//                     >
//                       <td className="px-4 py-2 font-medium text-header-text">
//                         {index + 1}
//                       </td>
//                       <td className="px-4 py-2 text-[#4A4A68]">
//                         {assessment.name}
//                       </td>
//                       <td className="px-4 py-2 text-[#4A4A68]">
//                         <div className="flex items-center gap-2">
//                           <p> {assessment.no_candidates}</p>
//                           <AvatarGroupName
//                             names={assessment.candidates_data.map(
//                               candidate => candidate.candidate_name
//                             )}
//                             max={5}
//                             size="small"
//                           />
//                         </div>
//                       </td>
//                       <td className="px-4 py-2 text-[#4A4A68]">
//                         <ul className="flex items-center justify-center gap-2 ">
//                           {assessment.is_published ? (
//                             <>
//                               <li className="">
//                                 <ToolTip
//                                   content="completed"
//                                   contentClass="text-xs"
//                                 >
//                                   <p className="rounded-full bg-primary px-6 py-1 text-xs font-semibold text-white">
//                                     {assessment.completed.length}
//                                   </p>
//                                 </ToolTip>
//                               </li>

//                               <li className="">
//                                 <ToolTip
//                                   content="started"
//                                   contentClass="text-xs"
//                                 >
//                                   <p className="rounded-full bg-primary-light-hover px-6 py-1 text-xs font-semibold text-[#3C1356]">
//                                     {assessment.ongoing.length}
//                                   </p>
//                                 </ToolTip>
//                               </li>
//                               <li className="">
//                                 <ToolTip
//                                   content="not started"
//                                   contentClass="text-xs"
//                                 >
//                                   <p className="rounded-full bg-primary-light-hover px-6 py-1 text-xs font-semibold text-[#3C1356]">
//                                     {assessment.not_started.length}
//                                   </p>
//                                 </ToolTip>
//                               </li>
//                             </>
//                           ) : (
//                             <li className="">
//                               <ToolTip
//                                 content="This assessment has not been published yet"
//                                 contentClass="text-xs"
//                               >
//                                 <p className="rounded-full bg-primary-light-hover px-6 py-1 text-xs font-medium text-[#3C1356]">
//                                   draft
//                                 </p>
//                               </ToolTip>
//                             </li>
//                           )}
//                         </ul>
//                       </td>
//                       <td className="px-4 py-2 text-[#4A4A68]">
//                         {format(
//                           new Date(assessment.created_at),
//                           'dd/MM/yyyy, hh:mma'
//                         )}
//                       </td>
//                       <td className="px-4 py-2 text-[#4A4A68]">
//                         {assessment.deadline
//                           ? format(
//                             new Date(assessment.deadline),
//                             'dd/MM/yyyy, hh:mma'
//                           )
//                           : '-'}
//                       </td>
//                       <td className="flex p-4">
//                         <DropdownMenu
//                           options={ASSESSMENT_TABLE_OPTIONS.filter(option => {
//                             return assessment.is_published
//                               ? true
//                               : !['invite candidates'].includes(option.title);
//                           })}
//                           readable_text_key="title"
//                           callback={_selected_option =>
//                             handleAssessmentAction(assessment, _selected_option)
//                           }
//                           button={<TableActionButton />}
//                           itemsClass="text-[13.0px] text-body-text"
//                         />
//                       </td>
//                     </tr>
//                   ))}
//                 </tbody>
//               </table>
//             </div>
//           </div>
//         )}
//       </div>

//       <Modal
//         title="Update Assessment Deadline"
//         is_open={is_assessment_deadline_settings_modal_open}
//         close={closeAssessmentDeadlineSettingsModal}
//       >
//         <form
//           className="w-full max-w-[600px] space-y-4 p-4"
//           onSubmit={e => {
//             e.preventDefault();
//             saveAssessessmentDeadlineSettings();
//           }}
//         >
//           {is_loading_assessment_deadline_settings ? (
//             <Loader />
//           ) : active_assessment_deadline_settings ? (
//             <>
//               <div>
//                 <h2 className="heading-2"> Update Assessment Deadline </h2>
//                 {active_assessment_deadline_settings.is_started ? (
//                   <p className="helper-text text-xs">
//                     Note: Only assessment end date can be edited as candidates
//                     have commenced assessment
//                   </p>
//                 ) : (
//                   <p className="helper-text text-xs">
//                     {' '}
//                     You can modify this assessment deadline and have candidates
//                     take assessment before the set time and date{' '}
//                   </p>
//                 )}
//               </div>
//               <div className="space-y-4">
//                 <div className="space-y-1">
//                   <h3 className="heading-text">Commencement settings</h3>
//                   <p className="text-xs">
//                     Select <span className="font-bold">'YES'</span> if you wish
//                     for candidates to complete the assessment at their
//                     convenience within a designated time frame, or choose{' '}
//                     <span className="font-bold">'NO'</span> if you prefer them
//                     to commence the assessment at the specific time and date you
//                     have specified.
//                   </p>
//                 </div>
//                 <p className="flex gap-2">
//                   {/* @ts-ignore */}
//                   <ListBox
//                     placeholder="Select commencement setting"
//                     className="border"
//                     options={YES_OR_NO}
//                     value_key="value"
//                     readable_text_key="readable_string"
//                     active_option={
//                       active_assessment_deadline_settings.commencement_settings
//                     }
//                     setActiveOption={_option => {
//                       setActiveAssessmentDeadlineSettings({
//                         ...active_assessment_deadline_settings,
//                         commencement_settings: _option == 'yes' ? 'yes' : 'no',
//                       });
//                     }}
//                   />
//                 </p>
//               </div>
//               <div className="space-y-4">
//                 <div className="space-y-1">
//                   <h3 className="heading-text">Set assessment time limit</h3>
//                   <p className="text-xs">
//                     Once started, candidates are required to submit assessment
//                     within this time.
//                   </p>
//                 </div>

//                 <TimeInput
//                   value={active_assessment_deadline_settings.time_limit}
//                   onChange={val => {
//                     setActiveAssessmentDeadlineSettings({
//                       ...active_assessment_deadline_settings,
//                       time_limit: val,
//                     });
//                   }}
//                 />
//               </div>
//               {(!active_assessment_deadline_settings.is_started ||
//                 active_assessment_deadline_settings.commencement_settings ==
//                 'yes') && (
//                   <div className="space-y-4">
//                     <div className="space-y-1">
//                       <h3 className="heading-text">Date and time settings</h3>
//                       <p className="text-xs">
//                         Specify the start and end date and time for candidates to
//                         start and complete this assessment.
//                       </p>
//                     </div>
//                     {!active_assessment_deadline_settings.is_started && (
//                       <label className="flex items-center gap-4">
//                         Start:
//                         <input
//                           className="input-white w-min border"
//                           type="datetime-local"
//                           value={active_assessment_deadline_settings.start_time}
//                           required
//                           name="date"
//                           onChange={event => {
//                             setActiveAssessmentDeadlineSettings({
//                               ...active_assessment_deadline_settings,
//                               start_time: event.target.value,
//                             });
//                           }}
//                         />
//                       </label>
//                     )}
//                     {active_assessment_deadline_settings.commencement_settings ==
//                       'yes' && (
//                         <label className="flex items-center gap-4">
//                           End:
//                           {/* <input
//                         className="input-white w-min border"
//                         type="datetime-local"
//                         value={active_assessment_deadline_settings.deadline}
//                         required
//                         name="date"
//                         onChange={event => {
//                           // console.lg(event.target.valu);
//                           setActiveAssessmentDeadlineSettings({
//                             ...active_assessment_deadline_settings,
//                             deadline: event.target.value,
//                           });
//                         }}
//                       /> */}
//                           <DatePicker
//                             showTimeSelect
//                             value={
//                               active_assessment_deadline_settings.deadline
//                                 ? new Date(
//                                   active_assessment_deadline_settings.deadline
//                                 ).toLocaleString('en-NG', {
//                                   hour12: true,
//                                   weekday: 'short',
//                                   year: 'numeric',
//                                   month: 'short',
//                                   day: 'numeric',
//                                   hour: 'numeric',
//                                   minute: 'numeric',
//                                 })
//                                 : ''
//                             }
//                             placeholderText="Set Deadline"
//                             required
//                             selected={
//                               new Date(active_assessment_deadline_settings.deadline)
//                             }
//                             minDate={
//                               new Date(
//                                 active_assessment_deadline_settings.start_time
//                               )
//                             }
//                             onChange={date => {
//                               console.log(date?.toLocaleTimeString);
//                               setActiveAssessmentDeadlineSettings({
//                                 ...active_assessment_deadline_settings,
//                                 deadline: date ? `${format(date, 'yyyy-MM-dd')}T${date.toLocaleTimeString(undefined, { hourCycle: 'h23', hour: '2-digit', minute: '2-digit', second: '2-digit' })}` : '',
//                               });
//                             }}
//                             className="input-white w-max border"
//                           />
//                         </label>
//                       )}
//                   </div>
//                 )}
//               <div className="flex justify-end">
//                 <Button
//                   className="btn-primary"
//                   type="submit"
//                   is_busy={is_saving_assessment_deadline_settings}
//                 >
//                   Save and Update
//                 </Button>
//               </div>
//             </>
//           ) : (
//             <p>Oops, something went wrong. please try again</p>
//           )}
//         </form>
//       </Modal>

//       <DeleteAssessmentModal
//         is_open={is_delete_assessment_modal_open}
//         close={closeDeleteAssessmentModal}
//         assessment_id={active_assessment?.id}
//         onComplete={() => {
//           setFilteredAssessments(
//             filtered_assessments.filter(
//               assessment => assessment.id !== active_assessment?.id
//             )
//           );
//         }}
//       />

//       <PreviewAssessmentSectionsModal
//         assessment={active_assessment}
//         is_open={is_assessment_preview_modal_open}
//         close={closeAssessmentPreviewModalOpen}
//       />

//       <InviteCandidatesModal
//         close={closeInviteCandidatesModal}
//         assessment_id={active_assessment?.id}
//         is_open={is_invite_candidates_modal_open}
//         onSuccess={loadMyAssessments}
//       />

//       <InitiateCreateAssessmentModal
//         isCreateAssessmentModalOpen={isCreateAssessmentModalOpen}
//         closeCreateAssessmentModal={closeCreateAssessmentModal}
//       />
//     </>
//   );
// };

// export default MyAssessments;
