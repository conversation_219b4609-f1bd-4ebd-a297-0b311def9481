import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem, ConfirmDeleteModalAlt } from '@/components/shared'
import React from 'react'
import { QuestionPack } from '../types/create-assessments'
import { useBooleanStateControl } from '@/hooks'
import { useDeleteQuestionPack } from '../api'
import toast from 'react-hot-toast'
import { More } from 'iconsax-react'
import Link from 'next/link'
import QuestionPackDetailModal from '../components/QuestionPackDetailModal'
import { truncateToLength } from './TestsLibrary'

interface Props {
    question_pack: QuestionPack
    refetchPacks: () => void
}
const QuestionPackCard: React.FC<Props> = ({ question_pack, refetchPacks }) => {
    const {
        state: isCustomPackDetailModalOpen,
        setTrue: openCustomPackDetailModal,
        setFalse: closeCustomPackDetailModal,
    } = useBooleanStateControl()

    const {
        state: isConfirmDeletePackModalOpen,
        setTrue: openConfirmDeletePackModal,
        setFalse: closeConfirmDeletePackModal,
    } = useBooleanStateControl()

    const { mutate: deletePack, isLoading: isDeletingPack } = useDeleteQuestionPack()
    const handleDeleteQuestionPack = () => {
        console.log("deleting pack", question_pack.id)
        deletePack(question_pack.id, {
            onSuccess: () => {
                toast.success("Question pack deleted successfully")
                refetchPacks()
                closeConfirmDeletePackModal()
            }
        })
    }


    return (
        <>
            <li className="space-y-2 relative rounded-md bg-white p-4 overflow-hidden" >
                <aside className="absolute rounded-none btn-primary-light text-xs py-1 px-4 -top-2 left-0">
                    My pack
                </aside>

                <header className="flex items-center justify-between">
                    <h2 className="text-header-text font-medium text-base mt-2">{question_pack.name}</h2>
                    <DropdownMenu>
                        <DropdownMenuTrigger title="view options" >
                            <More className="rotate-90" fill="#D9D9D9" size={18} />
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                            <DropdownMenuItem>
                                <Link href={`/e/assessments-and-interviews/question-pack/${question_pack.id}`} className="text-sm w-full">
                                    View
                                </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                                <button className="text-sm w-full text-left" onClick={openConfirmDeletePackModal}>
                                    Delete
                                </button>
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </header>
                {/* <p
                        className="text-xs text-body-text max-h-[4lh] overflow-hidden"
                        style={{
                          maskImage: "linear-gradient(#fff, transparent)",
                        }}
                      /> */}
                <p className="text-xs">{question_pack.description}</p>
                <p className="heading-text">
                    <span className="helper-text font-normal">
                        Total questions
                    </span>{" "}
                    {question_pack.total_questions}
                </p>
                <ul className="flex items-center gap-2 text-xs">
                    {question_pack.tags?.map(
                        (item) => (
                            <li className="btn-primary-light-pill">
                                <span className="max-w-[5ch] text-ellipsis overflow-hidden whitespace-nowrap">
                                    {truncateToLength(item, 8)}
                                </span>
                            </li>
                        ),
                    )}
                </ul>
                <div className="flex gap-2 text-xs">

                    <QuestionPackDetailModal
                        close={closeCustomPackDetailModal}
                        openDrawer={openCustomPackDetailModal}
                        is_open={isCustomPackDetailModalOpen}
                        questionPack={question_pack}
                    />
                    <Link href={`question-pack/${question_pack.id}`} className="btn-primary-light heading-text font-normal">
                        View Pack
                    </Link>
                </div>
            </li>


            <ConfirmDeleteModalAlt
                closeModal={closeConfirmDeletePackModal}
                isModalOpen={isConfirmDeletePackModalOpen}
                title="Delete question pack"
                isDeleting={isDeletingPack}
                deleteFunction={handleDeleteQuestionPack}
            >
                <div>
                    <p>
                        Are you sure you want to delete this question pack?
                    </p>
                </div>
            </ConfirmDeleteModalAlt>
        </>
    )
}

export default QuestionPackCard