'use client';

import { format } from 'date-fns';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import DropdownMenu from '@/components/DropdownMenu';
import { <PERSON><PERSON>, Loader, ToolTip } from '@/components/shared';
import { Search } from '@/components/shared/icons';
import { useBooleanStateControl } from '@/hooks';
import { createInterviewPackChannel } from '@/lib/utils/event-bus/channels/createInterviewPackChannel';
import { cn } from '@/utils';
import { XIcon } from '../../../jobs/misc/icons';
import DeleteInterviewModal from '../components/DeleteInterviewModal';
import InviteCandidatesModal from '../components/InviteCandidatesModal';
import RoleSelect from '../components/RoleSelect';
import TableActionButton from '../components/TableActionButton';
import {
  INTERVIEW_TABLE_OPTIONS,
  INTERVIEW_TABLE_OPTIONS_DICTIONARY,
} from '../constants/constants';
import {
  fetchMyInterviewQuestionsQuery,
  fetchMyInterviewsQuery,
} from '../queries/interviews';
import {
  InterviewAssessmentObject,
  LibraryAssessment,
  QuestionPack,
} from '../types/create-assessments';
import InterviewsMyQuestion from './InterviewsMyQuestionPacks';

const Interviews = () => {
  const router = useRouter();

  function truncateToLength(str: string, len: number) {
    if (str.length > len) {
      return str.slice(0, len) + '...';
    } else {
      return str;
    }
  }

  const {
    data: my_questions,
    isLoading: is_loading_my_questions,
    error: _my_questions_error,
  } = fetchMyInterviewQuestionsQuery();

  const {
    data: my_interviews,
    isLoading: is_loading_my_interviews,
    error: _my_interviews_error,
    refetch: refetchInterviews,
  } = fetchMyInterviewsQuery();
  const [filtered_interviews, setFilteredInterviews] = useState<
    InterviewAssessmentObject[]
  >([]);

  const findTabFromQueryParams = () => {
    switch (useSearchParams().get('tab')) {
      case 'my_questions':
        return 1;
      default:
        return 0;
    }
  };

  const [current_tab, setCurrentTab] = useState(findTabFromQueryParams());
  const tabs = [
    {
      title: 'My Interviews',
      query_param: undefined,
    },
    {
      title: 'My Questions',
      query_param: 'my_questions',
    },
  ];

  const [search_term, setSearchTerm] = useState<string>('');

  const [assessment_type_filter, setAssessmentTypeFilter] = useState<
    string | undefined
  >(undefined);
  const [question_type_filter, setQuestionTypeFilter] = useState<
    string | undefined
  >(undefined);
  const [job_role_filter, setJobRoleFilter] = useState<string | undefined>(
    undefined
  );

  const [is_delete_interview_modal_open, setIsDeleteInterviewModalOpen] =
    useState(false);
  function openDeleteInterviewModal() {
    setIsDeleteInterviewModalOpen(true);
  }
  function closeDeleteInterviewModal() {
    setIsDeleteInterviewModalOpen(false);
  }

  function clearFilters() {
    setAssessmentTypeFilter(undefined);
    setQuestionTypeFilter(undefined);
    setJobRoleFilter(undefined);
    setSearchTerm('');
  }

  useEffect(() => {
    if (my_interviews) {
      setFilteredInterviews(
        my_interviews.filter(assessment => {
          const search_term_match = assessment.name
            .toLowerCase()
            .includes(search_term.toLowerCase());
          // const assessment_type_match = assessment_type_filter ? assessment.assessment_type == assessment_type_filter : true;
          // const question_type_match = question_type_filter ? assessment.summary == question_type_filter : true;
          const job_role_match = job_role_filter
            ? (assessment.role || assessment.role) == job_role_filter
            : true;
          // return search_term_match && assessment_type_match && question_type_match && job_role_match;
          return search_term_match && job_role_match;
        })
      );
    }
  }, [
    my_interviews,
    my_questions,
    search_term,
    question_type_filter,
    assessment_type_filter,
    job_role_filter,
    is_loading_my_questions,
    my_questions,
  ]);

  const initiateCustomQuestionPackCreation = () => {
    createInterviewPackChannel.emit('onCreateButtonClick');
  };

  const [active_library_assessment, setActiveLibraryAssessment] = useState<
    LibraryAssessment | undefined
  >(undefined);

  /* custom question pack */

  const [active_question_pack, setActiveQuestionPack] = useState<
    QuestionPack | undefined
  >(undefined);

  const {
    state: isInviteCandidatesModalOpen,
    setTrue: openInvitesCandidatesModal,
    setFalse: closeInviteCandidatesModal,
  } = useBooleanStateControl();

  const [active_interview, setActiveInterview] = useState<
    InterviewAssessmentObject | undefined
  >(undefined);
  function handleAssessmentAction(
    assessment: InterviewAssessmentObject,
    selected_option: (typeof INTERVIEW_TABLE_OPTIONS_DICTIONARY)[keyof typeof INTERVIEW_TABLE_OPTIONS_DICTIONARY]
  ) {
    setActiveInterview(assessment);
    switch (selected_option) {
      case INTERVIEW_TABLE_OPTIONS_DICTIONARY['view details']:
        router.push(
          `/e/assessments-and-interviews/interviews/${assessment.id}?tab=interviews`
        );
        break;
      case INTERVIEW_TABLE_OPTIONS_DICTIONARY['delete interview']:
        openDeleteInterviewModal();
        break;
      case INTERVIEW_TABLE_OPTIONS_DICTIONARY['preview assessment']:
        /* openAssessmentPreviewModalOpen() */
        break;
      case INTERVIEW_TABLE_OPTIONS_DICTIONARY['invite candidates']:
        openInvitesCandidatesModal();
        break;
    }
  }

  return (
    <div className="py-4 pt-0">
      <header className="rounded-md bg-white px-2">
        <nav className="">
          <ul className="flex items-center gap-4">
            {tabs.map((tab, index) => (
              <li
                key={index}
                className={cn(
                  'border-b-[3px] border-transparent p-4 pb-2 text-sm transition-all duration-300 hover:border-primary-light',
                  index == current_tab && ' !border-primary text-primary'
                )}
              >
                <label className="cursor-pointer">
                  <input
                    type="radio"
                    name="active tab"
                    className="hidden"
                    value={index}
                    onChange={e => {
                      setCurrentTab(parseInt(e.target.value));
                    }}
                  />
                  {tab.title}
                </label>
              </li>
            ))}
          </ul>
        </nav>
      </header>

      <section className="my-4 flex flex-wrap items-center gap-2 px-4">
        {current_tab == 0 && (
          <RoleSelect
            value={job_role_filter}
            containerClass="!my-0"
            onChange={(_role: string) => {
              setJobRoleFilter(_role);
            }}
            hideLabel
          />
        )}
        {/* <SelectSingleCombo
          name="assessment type"
          options={ASSESSMENT_TYPES}
          className="border-none bg-white focus:!border-none"
          itemClass="text-[0.8125rem]"
          containerClass="!my-0"
          onChange={(_assessment_type) => {
            setAssessmentTypeFilter(_assessment_type);
          }}
          placeholder="Assessment type"
          value={assessment_type_filter}
          valueKey="id"
          labelKey="name"
        /> */}

        {/* < SelectSingleCombo
          name="question type"
          options={QUESITON_TYPES}
          className="border-none bg-white focus:!border-none"
          itemClass="text-[0.8125rem]"
          containerClass="!my-0"
          onChange={(question_type: string) => {
            setQuestionTypeFilter(question_type)
          }}
          placeholder="Question type"
          value={question_type_filter}
          valueKey="value"
          labelKey="name"
        /> */}

        <div className="relative">
          <Search className="absolute right-[5%] top-[25%]" />
          <input
            type="search"
            placeholder="Search"
            value={search_term}
            onChange={e => setSearchTerm(e.target.value)}
            className="rounded-lg border-[1.75px] border-[#D6D6D6] px-3.5 py-2 text-[0.9rem] transition-all focus:border-primary focus:outline-none sm:px-4"
          />
        </div>

        {(search_term ||
          assessment_type_filter ||
          question_type_filter ||
          job_role_filter) && (
          <Button
            onClick={clearFilters}
            variant="outlined"
            type="button"
            size="small"
            className="ml-auto"
            icon={<XIcon width={24} height={24} />}
          >
            Clear filters
          </Button>
        )}
      </section>

      <section className=" px-4">
        {/* my interviews */}
        {current_tab == 0 && (
          <>
            {is_loading_my_interviews ? (
              <Loader />
            ) : filtered_interviews.length == 0 ? (
              <div className="flex min-h-[600px] items-center justify-center">
                <div className="rounded-md border border-[#DADADA]/20 bg-gradient-to-b from-[#755ae20a] via-transparent to-transparent p-12 text-center">
                  <div className="container max-w-xs space-y-2">
                    <div className="container flex justify-center">
                      <img
                        src="/images/create-assessments/no-assessment.png"
                        alt="writing with pen"
                      />
                    </div>
                    {my_interviews?.length === 0 ? (
                      <>
                        <h2>
                          There is currently no test in the get linked library
                        </h2>
                        <p className="text-sm text-[#7D8590]">
                          please check back later.
                        </p>
                      </>
                    ) : (
                      <>
                        <h2>No test matches your filters</h2>
                        <p className="text-sm text-[#7D8590]">
                          clear the filters to get more results
                        </p>
                        <button className="btn-primary" onClick={clearFilters}>
                          Clear filters
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div className="mt-2 max-w-[100vw] space-y-2 overflow-x-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full divide-y rounded-md bg-white">
                    <thead>
                      <tr>
                        {'Id, Name, Candidates, Progress, Date Created, Deadline, Action'
                          .split(', ')
                          .map((heading, index) => (
                            <th
                              key={index}
                              className="p-4 font-normal text-header-text"
                            >
                              <p className={index == 3 ? '' : 'text-left'}>
                                {heading}
                              </p>
                            </th>
                          ))}
                      </tr>
                    </thead>
                    <tbody className="divide-y">
                      {filtered_interviews.map((assessment, index) => (
                        <tr
                          className="cursor-pointer text-sm hover:bg-primary-light"
                          onClick={() => {
                            router.push(
                              `/e/assessments-and-interviews/interviews/${assessment.id}`
                            );
                          }}
                          key={index}
                        >
                          <td className="px-4 py-2 font-medium text-header-text">
                            {index + 1}
                          </td>
                          <td className="px-4 py-2 text-[#4A4A68]">
                            {assessment.name}
                          </td>
                          <td className="px-4 py-2 text-[#4A4A68]">
                            <div className="flex items-center gap-2">
                              <p> {assessment.no_candidates}</p>
                            </div>
                          </td>
                          <td className="px-4 py-2 text-[#4A4A68]">
                            <ul className="flex items-center justify-center gap-2 ">
                              {assessment.is_published ? (
                                <>
                                  <li className="">
                                    <ToolTip
                                      content="completed"
                                      contentClass="text-xs"
                                    >
                                      <p className="rounded-full bg-primary px-6 py-1 text-xs font-semibold text-white">
                                        {assessment.completed.length}
                                      </p>
                                    </ToolTip>
                                  </li>

                                  <li className="">
                                    <ToolTip
                                      content="started"
                                      contentClass="text-xs"
                                    >
                                      <p className="rounded-full bg-primary-light-hover px-6 py-1 text-xs font-semibold text-[#3C1356]">
                                        {assessment.ongoing.length}
                                      </p>
                                    </ToolTip>
                                  </li>
                                  <li className="">
                                    <ToolTip
                                      content="not started"
                                      contentClass="text-xs"
                                    >
                                      <p className="rounded-full bg-primary-light-hover px-6 py-1 text-xs font-semibold text-[#3C1356]">
                                        {assessment.not_started.length}
                                      </p>
                                    </ToolTip>
                                  </li>
                                </>
                              ) : (
                                <li className="">
                                  <ToolTip
                                    content="This assessment has not been published yet"
                                    contentClass="text-xs"
                                  >
                                    <p className="rounded-full bg-primary-light-hover px-6 py-1 text-xs font-medium text-[#3C1356]">
                                      draft
                                    </p>
                                  </ToolTip>
                                </li>
                              )}
                            </ul>
                          </td>
                          <td className="px-4 py-2 text-[#4A4A68]">
                            {/* {format(new Date(assessment.created_at), 'dd/MM/yyyy, hh:mma')} */}
                          </td>
                          <td className="px-4 py-2 text-[#4A4A68]">
                            {assessment.deadline
                              ? format(
                                  new Date(assessment.deadline),
                                  'dd/MM/yyyy, hh:mma'
                                )
                              : '-'}
                          </td>
                          <td className="flex p-4">
                            <DropdownMenu
                              options={INTERVIEW_TABLE_OPTIONS}
                              readable_text_key="title"
                              callback={_selected_option =>
                                handleAssessmentAction(
                                  assessment,
                                  _selected_option
                                )
                              }
                              button={<TableActionButton />}
                              itemsClass="text-[13.0px] text-body-text"
                            />
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </>
        )}

        {/* my questions */}
        {current_tab == 1 && (
          <InterviewsMyQuestion
            clearFilters={clearFilters}
            search_term={search_term}
          />
        )}

        <DeleteInterviewModal
          is_open={is_delete_interview_modal_open}
          close={closeDeleteInterviewModal}
          assessment_id={active_interview?.id}
          onComplete={() => {
            setFilteredInterviews(
              filtered_interviews.filter(
                assessment => assessment.id !== active_interview?.id
              )
            );
          }}
        />
      </section>

      {isInviteCandidatesModalOpen && (
        <InviteCandidatesModal
          assessment_id={active_interview?.id}
          is_open={isInviteCandidatesModalOpen}
          onSuccess={refetchInterviews}
          close={closeInviteCandidatesModal}
          is_interview={true}
        />
      )}
    </div>
  );
};

export default Interviews;
