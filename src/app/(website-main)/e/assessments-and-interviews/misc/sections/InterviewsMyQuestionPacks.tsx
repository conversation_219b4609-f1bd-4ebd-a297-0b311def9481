import { Axios } from '@/lib/api/axios'
import React from 'react'
import { AssessmentObject } from '../types/create-assessments'
import { useQuery } from '@tanstack/react-query'
import Link from 'next/link'
import { Add } from 'iconsax-react'
import { cn } from '@/utils'
import { useBooleanStateControl } from '@/hooks'
import CreateQuestionPackStep2SelectTypeModal from '../components/CreateQuestionPackStep2SelectTypeModal'
import { Button } from '@/components/shared'
import { TInterviewQuestionPack } from '../../create-interview/misc/types'

interface InterviewsMyQuestionPacksProps {
    search_term: string;
    clearFilters: () => void;
}

const InterviewsMyQuestionPacks: React.FC<InterviewsMyQuestionPacksProps> = ({ search_term, clearFilters }) => {

    async function fetchMyInterviews() {
        const res = await Axios.get('assessments/interview-packs')
        return res.data as TInterviewQuestionPack[]
    }
    const useInterviewQuestions = () => {
        return useQuery({
            queryKey: ['my interview packs'],
            queryFn: fetchMyInterviews
        })
    }
    const { data, isLoading } = useInterviewQuestions()
    const [filteredPacks, setFilteredPacks] = React.useState<TInterviewQuestionPack[]>([])
    React.useEffect(() => {
        if (data) {
            const filtered = data.filter(pack => {
                return pack.name.toLowerCase().includes(search_term.toLowerCase())
            })
            setFilteredPacks(filtered)
        }
    }, [data, search_term])

    const {
        state: isCreateNewPackModalOpen,
        setTrue: openCreateNewPackModal,
        setFalse: closeCreateNewPackModal
    } = useBooleanStateControl()

    return (
        <>
            {
                isLoading ?
                    <div></div>
                    :
                    filteredPacks.length === 0 ?
                        <div className="flex min-h-[600px] items-center justify-center">
                            <div className="rounded-md bg-gradient-to-b from-[#755ae20a] to-transparent via-transparent p-12 border border-[#DADADA]/20 text-center">
                                <div className="container max-w-xs space-y-2">
                                    <div className="container flex justify-center">
                                        <img
                                            src="/images/create-assessments/no-assessment.png"
                                            alt="writing with pen"
                                        />
                                    </div>
                                    {
                                        data?.length === 0 ? (
                                            <>
                                                <h2>
                                                    You currently do not have any interview question packs
                                                </h2>
                                                <Link
                                                    className="btn-primary inline-flex gap-2"
                                                    type="button"
                                                    // onClick={initiateCustomQuestionPackCreation}
                                                    href="/e/assessments-and-interviews/interviews-library/create-interview"
                                                >
                                                    <Add />
                                                    Create an interview pack
                                                </Link>
                                            </>
                                        ) : (
                                            <>
                                                <h2>None of your interview question packs match your filters</h2>
                                                <p className="text-sm text-[#7D8590]">clear the filters to get more results</p>
                                                <button className="btn-primary" onClick={clearFilters}>Clear filters</button>
                                            </>
                                        )
                                    }
                                </div>
                            </div>
                        </div>
                        :
                        <ul className={cn("grid gap-4", filteredPacks.length < 4 ? "grid-cols-[repeat(auto-fill,minmax(300px,350fr))]" : "grid-cols-[repeat(auto-fit,minmax(290px,1fr))]")}>
                            {[...filteredPacks].reverse().map((question_pack, index) => (
                                <li
                                    key={index}
                                    className="space-y-2 relative rounded-md bg-white p-4 overflow-hidden"
                                >
                                    <h2 className="text-header-text font-medium text-base mt-2">{question_pack.name}</h2>
                                    <span className="absolute rounded-none btn-primary-light text-xs py-1 px-4 -top-2 right-0">
                                        My pack
                                    </span>
                                    <p className="text-xs">{question_pack.description}</p>
                                    <p className="heading-text">
                                        <span className="helper-text font-normal">
                                            Total questions
                                        </span>{" "}
                                        {question_pack.question_set?.length}
                                    </p>
                                    <div className="flex gap-2 text-xs">




                                        <Link href={`interview-pack/${question_pack.id}`} className="btn-primary-light heading-text font-normal">
                                            View Pack
                                        </Link>
                                    </div>
                                </li>
                            ))}
                            <li
                                className="space-y-2 relative rounded-md bg-white p-4 overflow-hidden grid place-items-center"
                            >
                                <Button
                                    className="inline-flex gap-2"
                                    type="button"
                                    variant="extralight"
                                    onClick={openCreateNewPackModal}
                                >
                                    <Add />
                                    Create an interview pack
                                </Button>
                            </li>
                        </ul>
            }

            <CreateQuestionPackStep2SelectTypeModal
                isCreateQuestionPackModalOpen={isCreateNewPackModalOpen}
                closeCreateQuestionPackModal={closeCreateNewPackModal}
                isInterview={true}
            />
        </>
    )
}

export default InterviewsMyQuestionPacks