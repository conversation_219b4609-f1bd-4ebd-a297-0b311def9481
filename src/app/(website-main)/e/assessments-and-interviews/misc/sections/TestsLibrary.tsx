"use client";

import { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";

import { createQuestionPackChannel } from "@/lib/utils/event-bus/channels/createQuestionPackChannel";
import { LibraryAssessment, Question, QuestionPack } from "../types/create-assessments";
import { ASSESSMENT_TYPES_DICTIONARY, QUESITON_TYPES, } from "../constants/constants";
import LibraryPackDetailModal from "../components/LibraryPackDetailModal";
import QuestionPackDetailModal from "../components/QuestionPackDetailModal";
import PlusIcon from "@/components/icons/PlusIcon";
import { Button, SelectSingleCombo, Loader, } from "@/components/shared";
import { Search } from "@/components/shared/icons";
import { useBooleanStateControl } from "@/hooks";
import { cn } from "@/utils";
import { XIcon } from "../../../jobs/misc/icons";
import CustomQuestionPackCard from "../components/CustomQuestionPackCard";
import RoleSelect from "../components/RoleSelect";
import { fetchLibraryPacksQuery, fetchMyQuestionPacksQuery, fetchMyQuestionsQuery } from "../queries/tests-library";
import CreateNewQuestionPackBtn from "../components/CreateNewQuestionPackBtn";
import { CloseCircle, More } from "iconsax-react";
import { useDeleteQuestionPack } from "../api";
import toast from "react-hot-toast";
import QuestionPackCard from "./QuestionPackCard";
import QuestionCard from "../../question-pack/misc/components/QuestionCard";
import { useGetQuestionsSections } from "../../interview-pack/misc/api";


export function truncateToLength(str: string, len: number) {
  if (str.length > len) {
    return str.slice(0, len) + "...";
  } else {
    return str;
  }
}
const TestsLibrary = () => {


  const [my_filtered_question_packs, setMyFilteredQuestionPacks] = useState<QuestionPack[]>([]);
  const [my_filtered_questions, setMyFilteredQuestions] = useState<Question[]>([]);

  const [filtered_get_linked_questions, setFilteredGetLinkedQuestions] = useState<LibraryAssessment[]>([]);

  const observer = useRef<IntersectionObserver>();

  const { data: my_questions, isLoading: is_loading_my_questions, error: _my_questions_error, } = fetchMyQuestionsQuery()
  const { data: my_question_packs, isLoading: is_loading_custom_packs, error: _my_question_packs_error, refetch: refetchPacks } = fetchMyQuestionPacksQuery()
  const { data: get_linked_questions, isLoading: is_loading_get_linked_packs, error: _get_linked_packs_error, hasNextPage: hasNextGetLinkedPacksPage, fetchNextPage: fetchNextGetLinkedPackPage } = fetchLibraryPacksQuery({ page: 1, per_page: 20 })

  const lastElementRef = useCallback((node: HTMLElement) => {
    if (is_loading_get_linked_packs) return;
    if (observer.current) observer.current.disconnect();
    observer.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasNextGetLinkedPacksPage) fetchNextGetLinkedPackPage()
    })
    if (node) observer.current.observe(node)
  }, [is_loading_get_linked_packs])


  const findTabFromQueryParams = () => { switch (useSearchParams().get("tab")) { case "my_question_packs": return 1; case "my_questions": return 2; default: return 0; } }

  const [current_tab, setCurrentTab] = useState(findTabFromQueryParams());
  const tabs = [
    {
      title: "Getlinked Questions",
      query_param: undefined,
    },
    {
      title: "My Question Packs",
      query_param: "my_question_packs"
    },
    {
      title: "My Questions",
      query_param: "my_questions"
    }
  ];

  const [search_term, setSearchTerm] = useState<string>();

  const [assessment_type_filter, setAssessmentTypeFilter] = useState<string | undefined>(undefined);
  const [question_type_filter, setQuestionTypeFilter] = useState<string | undefined>(undefined);
  const [job_role_filter, setJobRoleFilter] = useState<string | undefined>(undefined);
  const { data: questionSections, isLoading: isGettingSections } =
    useGetQuestionsSections();
  function clearFilters() {
    setAssessmentTypeFilter(undefined)
    setQuestionTypeFilter(undefined)
    setJobRoleFilter(undefined)
    setSearchTerm("")
  }

  useEffect(() => {
    if (get_linked_questions) {
      console.log(get_linked_questions, "GETTER")
      let data: LibraryAssessment[] = []
      get_linked_questions?.pages?.map(page => {
        data = [...data, ...page.results]
      })

      setFilteredGetLinkedQuestions(
        data.filter((assessment) => {
          let should_show = true;
          if (search_term) {
            const is_name_match = assessment.name?.toLowerCase()?.includes(
              search_term.toLowerCase(),
            );
            const is_description_match = assessment.description?.toLowerCase()
              ?.includes(search_term.toLowerCase());
            should_show = should_show && (is_name_match || is_description_match);
          }
          if (question_type_filter) {
            should_show = should_show &&
              assessment.question_types.includes(question_type_filter);
          }
          if (assessment_type_filter) {
            should_show = should_show &&
              assessment_type_filter.includes(assessment.section)
          }
          if (job_role_filter) {
            should_show = should_show && (assessment.relevant_roles.some(role => (role.name == job_role_filter || role.id.toString() == job_role_filter)))
          }
          return should_show;
        }),
      );

    }

    if (my_question_packs) {
      setMyFilteredQuestionPacks(
        my_question_packs.filter((assessment) => {
          let should_show = true;

          if (search_term) {
            const is_name_match = assessment.name?.toLowerCase()?.includes(
              search_term.toLowerCase(),
            );
            const is_description_match = assessment.description?.toLowerCase()
              ?.includes(search_term.toLowerCase());
            should_show = should_show && (is_name_match || is_description_match);
          }

          if (question_type_filter || assessment_type_filter) {

            const condensed_question_pack = assessment.question_set.reduce((acc, question) => {
              acc.question_types.add(question.type)
              acc.assessment_types.add(question.section)
              return acc
            }, {
              assessment_types: new Set([] as string[]),
              question_types: new Set([] as string[]),
            }
            )

            if (question_type_filter) {
              should_show = should_show &&
                condensed_question_pack.question_types.has(question_type_filter);
            }

            if (assessment_type_filter) {
              let _should_show = false

              const assessment_types_array = condensed_question_pack.assessment_types// Array.from() as string[];

              if (assessment_types_array.has(ASSESSMENT_TYPES_DICTIONARY[assessment_type_filter]?.id)) {
                _should_show = true
              }
              should_show = should_show && _should_show;
            }

          }

          return should_show;
        }),
      );
    }

    //@ts-ignore
    if (my_questions && !my_questions.message) {
      console.log(my_questions, "MY QUESTIONS")
      setMyFilteredQuestions(
        my_questions?.results?.filter((question) => {
          let should_show = true;
          if (search_term) {
            const is_question_match = question.question?.toLowerCase()?.includes(
              search_term.toLowerCase(),
            );
            should_show = should_show && is_question_match
          }

          if (question_type_filter) {
            should_show = should_show && question.type == question_type_filter
          }

          if (assessment_type_filter) {
            should_show = should_show &&
              question.section == ASSESSMENT_TYPES_DICTIONARY[assessment_type_filter]?.id
          }

          return should_show;
        }),
      );
    }
  }, [
    get_linked_questions,
    my_question_packs,
    my_questions,
    search_term,
    question_type_filter,
    assessment_type_filter,
    job_role_filter
  ]);

  const initiateCustomQuestionPackCreation = () => {
    createQuestionPackChannel.emit("onCreateButtonClick");
  };

  const [active_library_assessment, setActiveLibraryAssessment] = useState<LibraryAssessment | undefined>(undefined,);

  const {
    state: isLibrarySetDetailModalOpen,
    setTrue: openLibrarySetDetailModal,
    setFalse: closeLibrarySetDetailModal,
  } = useBooleanStateControl()



  /* custom question pack */

  const [active_question_pack, setActiveQuestionPack] = useState<QuestionPack | undefined>(undefined,);

  const [is_question_pack_detail_modal_open, setIsQuestionPackDetailModalOpen] =
    useState(false);
  function openQuestionPackDetailModal() {
    setIsQuestionPackDetailModalOpen(true);
  }
  function closeQuestionPackDetailModal() {
    setIsQuestionPackDetailModalOpen(false);
  }
  return (
    <div className="py-4 pt-0">
      <header className="rounded-md bg-white px-2">
        <nav className="">
          <ul className="flex gap-4 items-center">
            {tabs.map((tab, index) => (
              <li
                key={index}
                className={cn("p-4 pb-2 border-b-[3px] border-transparent text-sm hover:border-primary-light transition-all duration-300",
                  index == current_tab && " !border-primary text-primary")}
              >
                <label className="cursor-pointer">
                  <input
                    type="radio"
                    name="active tab"
                    className="hidden"
                    value={index}
                    onChange={(e) => {
                      setCurrentTab(parseInt(e.target.value))
                    }}
                  />
                  {tab.title}
                </label>
              </li>
            ))}
          </ul>
        </nav>
      </header>

      <section className="flex flex-wrap gap-2 items-center px-4 my-4">
        {current_tab == 0 && (
          <RoleSelect
            value={job_role_filter}
            containerClass="!my-0"
            onChange={(_role: string) => {
              setJobRoleFilter(_role);
            }}
            hideLabel
          />
        )}
        {
          current_tab == 0 &&
          <SelectSingleCombo
            name="assessment type"
            options={questionSections?.results.map(type =>
              ({ id: type.id, name: type.name })
            ) || []}
            className="border-none bg-white focus:!border-none"
            itemClass="text-[0.8125rem] text-left px-1.5"
            containerClass="!my-0 min-w-[200px]"
            onChange={(_assessment_type) => {
              setAssessmentTypeFilter(_assessment_type);
            }}
            placeholder="Assessment type"
            value={assessment_type_filter}
            valueKey="id"
            labelKey="name"
          />
        }

        <SelectSingleCombo
          name="question type"
          options={QUESITON_TYPES}
          className="border-none bg-white focus:!border-none"
          valueKey="value"
          labelKey="name"
          itemClass="text-[0.8125rem] text-left px-1.5"
          containerClass="!my-0 min-w-[200px]"
          onChange={(question_type: string) => {
            setQuestionTypeFilter(question_type)
          }}
          placeholder="Question type"
          value={question_type_filter}
        />

        <div className='relative'>
          <Search className="absolute right-[5%] top-[25%]" />
          <input
            type="search"
            placeholder="Search"
            value={search_term}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="px-3.5 py-2 sm:px-4 border-[1.75px] border-[#D6D6D6] rounded-lg text-[0.9rem] focus:border-primary focus:outline-none transition-all"
          />
        </div>

        {
          (search_term || assessment_type_filter || question_type_filter || job_role_filter) && (
            <Button onClick={clearFilters} variant="outlined" type="button" size="tiny" className="ml-auto" icon={<CloseCircle width={24} height={24} />}>
              Clear filters
            </Button>
          )
        }
      </section>

      <section className=" px-4">
        {/* getlinked questions */}
        {current_tab == 0 && (
          <>
            {is_loading_get_linked_packs ? (<Loader />) :
              filtered_get_linked_questions.length == 0 ? (
                <div className="flex min-h-[600px] items-center justify-center">
                  <div className="rounded-md bg-gradient-to-b from-[#755ae20a] via-transparent to-transparent p-12 border border-[#DADADA]/20 text-center">
                    <div className="container max-w-xs space-y-2">
                      <div className="container flex justify-center">
                        <img
                          src="/images/create-assessments/no-assessment.png"
                          alt="writing with pen"
                        />
                      </div>
                      {
                        get_linked_questions?.pages?.[0]?.results.length === 0 ? (
                          <>
                            <h2>There is currently no test in the get linked library</h2>
                            <p className="text-sm text-[#7D8590]">please check back later.</p>
                          </>
                        ) : (
                          <>
                            <h2>No test matches your filters</h2>
                            <p className="text-sm text-[#7D8590]">clear the filters to get more results</p>
                            <button className="btn-primary" onClick={clearFilters}>Clear filters</button>
                          </>
                        )
                      }
                    </div>
                  </div>
                </div>
              ) : (
                <div className={cn("grid gap-4", filtered_get_linked_questions.length < 4 ? "grid-cols-[repeat(auto-fill,minmax(300px,320fr))]" : "grid-cols-[repeat(auto-fit,minmax(290px,1fr))]")}>
                  {filtered_get_linked_questions.map((question_pack, index) => {
                    const last_element_props = index == filtered_get_linked_questions.length - 1 ? { ref: lastElementRef } : {}
                    return (
                      // @ts-ignore
                      <div key={index} {...last_element_props} className="flex flex-col gap-2 justify-between rounded-[0.625rem] bg-white p-4 overflow-hidden shadow-sm">
                        <h2 className="text-header-text font-medium text-base">{question_pack.name}</h2>
                        <p
                          className="text-xs text-body-text h-[4lh] overflow-hidden"
                          style={{
                            maskImage: "linear-gradient(#fff, transparent)",
                          }}
                        >
                          {question_pack.description}
                        </p>
                        <p className="heading-text text-sm">
                          <span className="helper-text font-normal">
                            Total questions:
                          </span>{" "}
                          {question_pack.total_questions}
                        </p>
                        <ul className="flex items-center gap-2 text-xs">
                          {question_pack.tags.slice(0, 3).map(
                            (item, index) => (
                              <li className="btn-primary-light-pill" key={index}>
                                <span className="max-w-[5ch] text-ellipsis overflow-hidden whitespace-nowrap">
                                  {truncateToLength(item, 8)}
                                </span>
                              </li>
                            ),
                          )}
                        </ul>
                        <div className="flex gap-2 text-xs">

                          <LibraryPackDetailModal
                            close={closeLibrarySetDetailModal}
                            openDrawer={openLibrarySetDetailModal}
                            is_open={isLibrarySetDetailModalOpen}
                            assessment={active_library_assessment}
                            setCurrentQuestionPack={() => setActiveLibraryAssessment(question_pack)}
                          />

                          <Link href={`/e/assessments-and-interviews/getlinked-packs/${question_pack.id}`} className="btn-primary-light heading-text font-normal">
                            View Questions
                          </Link>
                        </div>
                      </div>
                    )
                  })}
                </div>
              )
            }
          </>
        )}

        {/* custom questions */}

        {current_tab == 1 && (
          <>
            {is_loading_custom_packs ? (<Loader />) :
              my_filtered_question_packs.length == 0 ? (
                <div className="flex min-h-[600px] items-center justify-center">
                  <div className="rounded-md bg-gradient-to-b from-[#755ae20a] to-transparent via-transparent p-12 border border-[#DADADA]/20 text-center">
                    <div className="container max-w-xs space-y-2">
                      <div className="container flex justify-center">
                        <img
                          src="/images/create-assessments/no-assessment.png"
                          alt="writing with pen"
                        />
                      </div>
                      {
                        my_question_packs?.length === 0 ? (
                          <>
                            <h2>
                              You currently do not have any custom question pack
                            </h2>
                            <p className="text-sm text-[#7D8590]">
                              Your can create question packs for easy assessment
                              creation
                            </p>
                            <CreateNewQuestionPackBtn />
                          </>
                        ) : (
                          <>
                            <h2>None of your question packs match your filters</h2>
                            <p className="text-sm text-[#7D8590]">clear the filters to get more results</p>
                            <button className="btn-primary" onClick={clearFilters}>Clear filters</button>
                          </>
                        )
                      }
                    </div>
                  </div>
                </div>
              ) : (
                <ul className={cn("grid gap-4", my_filtered_question_packs.length < 4 ? "grid-cols-[repeat(auto-fill,minmax(300px,350fr))]" : "grid-cols-[repeat(auto-fit,minmax(290px,1fr))]")}>
                  {[...my_filtered_question_packs].reverse().map((question_pack, index) => (
                    <QuestionPackCard
                      key={index}
                      question_pack={question_pack}
                      refetchPacks={refetchPacks}
                    />
                  ))}
                  <li>
                    <CreateNewQuestionPackBtn variant="card" />
                  </li>
                </ul>

              )
            }
          </>)
        }

        {/* my questions */}
        {current_tab == 2 && (
          <>
            {is_loading_my_questions ? (<Loader />) :
              my_filtered_questions.length == 0 ? (
                <div className="flex min-h-[600px] items-center justify-center">
                  <div className="rounded-md bg-gradient-to-b from-[#755ae20a] to-transparent via-transparent p-12 border border-[#DADADA]/20 text-center">
                    <div className="container max-w-xs space-y-2">
                      <div className="container flex justify-center">
                        <img
                          src="/images/create-assessments/no-assessment.png"
                          alt="writing with pen"
                        />
                      </div>
                      {
                        //@ts-ignore
                        (my_questions?.length === 0 || my_questions?.message) ? (
                          <>
                            <h2>
                              You currently do not have any custom questions
                            </h2>
                            <p className="text-sm text-[#7D8590]">
                              Your can create questions while creating assessments
                            </p>
                          </>
                        ) : (
                          <>
                            <h2>None of your questions match your filters</h2>
                            <p className="text-sm text-[#7D8590]">clear the filters to get more results</p>
                            <button className="btn-primary" onClick={clearFilters}>Clear filters</button>
                          </>
                        )
                      }
                    </div>
                  </div>
                </div>
              ) : (
                <ul className={cn("grid gap-4", my_filtered_questions.length < 4 ? "grid-cols-[repeat(auto-fill,minmax(300px,350fr))]" : "grid-cols-[repeat(auto-fit,minmax(290px,1fr))]")}>
                  {
                    [...my_filtered_questions].reverse().map((question, index) => (
                      <li key={index} >
                        <QuestionCard
                          allQuestions={my_questions?.results || []}
                          question={question}
                          index={index}
                          refetch={refetchPacks}
                        />

                      </li>
                    ))
                  }
                </ul>
              )}
          </>
        )}
      </section>
    </div>
  );
}

export default TestsLibrary;
