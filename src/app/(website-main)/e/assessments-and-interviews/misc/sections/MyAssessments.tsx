"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useBooleanStateControl } from "@/hooks"
import { ASSESSMENT_TABLE_OPTIONS_DICTIONARY } from "@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants"
import { TAssessmentListItem, useGetAllAssessments } from "../api/getMyAssessments"
import { AssessmentTable } from "./MyAssessmentTable/assessment-table"
import { TableFilters } from "./MyAssessmentTable/table-filters"
import { InitiateCreateAssessmentModal } from "../components"
import DeleteAssessmentModal from "../components/DeleteAssessmentModal"
import PreviewAssessmentSectionsModal from "../components/PreviewAssessmentSectionsModal"
import InviteCandidatesModal from "../components/InviteCandidatesModal"
import DeadlineSettingsModal from "./MyAssessmentTable/deadline-settings-modal"
import { cn } from "@/utils"

export default function MyAssessments() {
  const router = useRouter()

  // Pagination and search state
  const [searchTerm, setSearchTerm] = useState<string>("")
  const [pageSize, setPageSize] = useState<number>(10)
  const [pageNumber, setPageNumber] = useState<number>(1)
  const [publishedFilter, setPublishedFilter] = useState<string>("")

  // Data fetching
  const fetchOptions = {
    search: searchTerm,
    size: pageSize,
    page: pageNumber,
  }

  const { data, isLoading: isLoadingAssessments, refetch: refetchAssessments, isFetching: isFetchingAssessments } = useGetAllAssessments(fetchOptions)

  // Modal states
  const {
    state: isCreateAssessmentModalOpen,
    setTrue: openCreateAssessmentModal,
    setFalse: closeCreateAssessmentModal,
  } = useBooleanStateControl()



  const [activeAssessment, setActiveAssessment] = useState<TAssessmentListItem | null>(null)

  const clearFilters = () => {
    setSearchTerm("")
    setPublishedFilter("")
  }

  useEffect(() => {
    refetchAssessments()
  }, [refetchAssessments])

  return (
    <>
      <div className="relative flex h-full flex-1 flex-col">
        {data?.results && data.results.length > 0 && (
          <TableFilters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            activeFilter={publishedFilter}
            onFilterChange={setPublishedFilter}
            onClearFilters={clearFilters}
            totalResults={data?.results.length || 0}
          />
        )}

        <div
          className={cn(
            'overflow-hidden rounded-full opacity-0 transition-opacity h-1 shrink-0',
            isFetchingAssessments && !isLoadingAssessments && 'opacity-100'
          )}
        >
          <div className="bg-primary/20 h-1 w-full overflow-hidden">
            <div className="h-full w-full origin-[0_50%] animate-indeterminate-progress rounded-full bg-primary "></div>
          </div>
        </div>

        <AssessmentTable
          data={data}
          isLoading={isLoadingAssessments}
          searchTerm={searchTerm}
          pageSize={pageSize}
          pageNumber={pageNumber}
          onPageChange={setPageNumber}
          onPageSizeChange={setPageSize}
          onSearch={setSearchTerm}
          onFilterChange={setPublishedFilter}
          onClearFilters={clearFilters}
          refetchAssessments={refetchAssessments}
          activeFilter={publishedFilter}
          onCreateAssessment={openCreateAssessmentModal}
        />
      </div>

      {/* Modals */}
      <InitiateCreateAssessmentModal
        isCreateAssessmentModalOpen={isCreateAssessmentModalOpen}
        closeCreateAssessmentModal={closeCreateAssessmentModal}
      />


    </>
  )
}

