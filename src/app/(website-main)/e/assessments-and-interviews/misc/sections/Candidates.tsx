'use client';

import { <PERSON><PERSON>, <PERSON>ader } from "@/components/shared";
import { Search } from "@/components/shared/icons";
import app_fetch from "@/lib/api/appFetch";
import { useEffect, useState } from "react"
import { XIcon } from "../../../jobs/misc/icons";
import { cn } from "@/utils";
import { format } from "date-fns";

type CandidateType = {
  id: string,
  name: string,
  email: string,
  assessment_count: number,
  invites: number,
  last_assessment_date: string,
}

const Candidates = () => {

  const [candidates, setCandidates] = useState([] as CandidateType[]);
  const [filtered_candidates, setFilteredCandidates] = useState([] as CandidateType[]);
  const [is_loading_candidates, setIsLoadingCandidates] = useState(true);

  const initial_filters = { search_term: "", is_filtered: false };
  const [filters, setFilters] = useState({ ...initial_filters });

  function clearFilters() {
    setFilters(initial_filters)
  }

  useEffect(() => {
    app_fetch("assessments/mycandidates/").then(async (response) => await response.json()).then((result) => {
      console.log({ result });
      if (result.candidates) {
        setCandidates(result.candidates)
      }
    })
  }, [])

  useEffect(() => {
    setFilteredCandidates(
      candidates.filter(candidate => {
        let should_show = true;
        if (filters.search_term) {
          setFilters({ ...filters, is_filtered: true })
          should_show = should_show && (candidate.name?.toLowerCase().includes(filters.search_term.toLowerCase()) || candidate.email?.toLowerCase().includes(filters.search_term.toLowerCase()))
        }
        return should_show;
      })
    )
    setIsLoadingCandidates(false);
  }, [filters.search_term, candidates])


  return (
    <div>
      <div className="relative flex h-full flex-1 flex-col">
        <div className="space-y-2 p-2">
          <header className="flex items-center justify-between rounded-md bg-white px-4 py-2">
            <h2 className="text-header-text text-base font-normal">
              {filters.is_filtered && ("Filtered ")}Candidates :{' '}

              <span className="text-body-text font-bold">{filtered_candidates.length}</span>
            </h2>
            <section className="flex items-center gap-2">

              <div className='relative'>
                <Search className="absolute right-[5%] top-[25%]" />
                <input
                  type="search"
                  placeholder="Search"
                  value={filters.search_term}
                  onChange={e => setFilters({ ...filters, search_term: e.target.value })}

                  className="px-3.5 py-2 sm:px-4 border-[1.5px] border-[#D6D6D6] rounded-lg text-[0.8rem] focus:outline-none transition-all"
                />
              </div>

              {filters.is_filtered && (
                <Button onClick={clearFilters} variant="extralight" icon={<XIcon width={20} height={20} />} className="border-grey py-1.5" size="tiny">Clear Filter</Button>

              )}
            </section>
          </header>
          <section>
            {
              is_loading_candidates ? (<Loader />) :
                filtered_candidates.length === 0 ? (
                  <div className="grid min-h-[80vh] flex-1 place-content-center">
                    <div className="rounded-md bg-gradient-to-b from-[#755ae20a] via-transparent to-transparent p-12 border border-[#DADADA]/20 text-center">
                      <div className="container max-w-xs space-y-2">
                        <div className="container flex justify-center">
                          <img
                            src="/images/create-assessments/no-assessment.png"
                            alt="writing with pen"
                          />
                        </div>
                        {candidates.length === 0 ? (
                          <>
                            <h2>There are no candidates to show</h2>
                            <p className="text-sm text-[#7D8590]">Candidates that have taken your assessments would be listed here.</p>
                          </>

                        ) : (
                          <>
                            <h2>No candidate fits your filters</h2>
                            <p className="text-sm text-[#7D8590]">Clear the filters to see more results.</p>
                            <button
                              onClick={clearFilters}
                              className="btn-primary inline-flex gap-2"
                              type="button"
                            >
                              Clear filters
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                ) :
                  (
                    <table className="w-full divide-y rounded-md bg-white">
                      <thead>
                        <tr>
                          {'S/N, Candidate, Email, Assessments, Last Assessment'
                            .split(', ')
                            .map((heading, index) => (
                              <th key={index} className={cn("py-4 text-header-text font-normal", index == 0 && "pl-2")}>
                                <p className="text-left ">{heading}</p>
                              </th>
                            ))}
                        </tr>
                      </thead>
                      <tbody className="divide-y">
                        {filtered_candidates.map((candidate, index) => (
                          <tr key={index} className="text-sm hover:bg-primary-light">
                            <td className="p-4 text-header-text font-medium">{index + 1}</td>
                            <td className="px-3 py-1.5 text-[#4A4A68]">{candidate.name}</td>
                            <td className="px-3 py-1.5 text-[#4A4A68]">{candidate.email}</td>
                            <td className="px-3 py-1.5 text-[#4A4A68]">{candidate.assessment_count}</td>
                            <td className="px-3 py-1.5 text-[#4A4A68]">{format(new Date(candidate.last_assessment_date), 'dd/MM/yyyy, hh:mma')}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  )
            }
          </section>
        </div>
      </div>
    </div>
  );
}

export default Candidates