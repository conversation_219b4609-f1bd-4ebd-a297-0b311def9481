import TableActionButton from "@/app/(website-main)/e/assessments-and-interviews/misc/components/TableActionButton"
import { ASSESSMENT_TABLE_OPTIONS, ASSESSMENT_TABLE_OPTIONS_DICTIONARY } from "@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants"
import DropdownMenu from "@/components/DropdownMenu"
import { useBooleanStateControl } from "@/hooks"
import { useRouter } from "next/navigation"
import { TAssessmentListItem } from "../../api/getMyAssessments"
import DeleteAssessmentModal from "../../components/DeleteAssessmentModal"
import DuplicateAssessmentModal from "../../components/DuplicateAssessmentModal"
import InviteCandidatesModal from "../../components/InviteCandidatesModal"
import DeadlineSettingsModal from "./deadline-settings-modal"

interface TableRowActionsProps {
    assessment: TAssessmentListItem
    refetchAssessments: () => void
}

export function TableRowActions({ assessment, refetchAssessments }: TableRowActionsProps) {
    const filteredOptions = ASSESSMENT_TABLE_OPTIONS.filter((option) => {
        return assessment.is_published ? true : !["invite candidates"].includes(option.title)
    })
    const router = useRouter()

    const {
        state: isDeadlineModalOpen,
        setTrue: openDeadlineModal,
        setFalse: closeDeadlineModal,
    } = useBooleanStateControl();

    const {
        state: isDeleteModalOpen,
        setTrue: openDeleteModal,
        setFalse: closeDeleteModal,
    } = useBooleanStateControl();

    const {
        state: isPreviewModalOpen,
        setTrue: openPreviewModal,
        setFalse: closePreviewModal,
    } = useBooleanStateControl();

    const {
        state: isInviteModalOpen,
        setTrue: openInviteModal,
        setFalse: closeInviteModal,
    } = useBooleanStateControl();

    const {
        state: isDuplicateModalOpen,
        setTrue: openDuplicateModal,
        setFalse: closeDuplicateModal,
    } = useBooleanStateControl();

    const handleAssessmentAction = (action: (typeof ASSESSMENT_TABLE_OPTIONS_DICTIONARY)[keyof typeof ASSESSMENT_TABLE_OPTIONS_DICTIONARY]) => {

        switch (action) {
            case ASSESSMENT_TABLE_OPTIONS_DICTIONARY["view details"]:
                router.push(`/e/assessments-and-interviews/assessments/${assessment.id}`)
                break
            case ASSESSMENT_TABLE_OPTIONS_DICTIONARY["set deadline"]:
                openDeadlineModal()
                break
            case ASSESSMENT_TABLE_OPTIONS_DICTIONARY["delete assessment"]:
                openDeleteModal()
                break
            case ASSESSMENT_TABLE_OPTIONS_DICTIONARY["preview assessment"]:
                openPreviewModal()
                break
            case ASSESSMENT_TABLE_OPTIONS_DICTIONARY["invite candidates"]:
                openInviteModal()
                break
            case ASSESSMENT_TABLE_OPTIONS_DICTIONARY["duplicate assessment"]:
                openDuplicateModal()
                break
        }
    }

    return (
        <>
            <DropdownMenu
                options={filteredOptions}
                readable_text_key="title"
                callback={(selectedOption) => handleAssessmentAction(selectedOption)}
                button={<TableActionButton />}
                itemsClass="text-[13.0px] text-body-text"
            />

            <>
                <DeleteAssessmentModal
                    is_open={isDeleteModalOpen}
                    close={closeDeleteModal}
                    assessment_id={assessment.id}
                    onComplete={() => {
                        refetchAssessments()
                        closeDeleteModal()
                    }}
                />
                {/* 
          <PreviewAssessmentSectionsModal
            assessment={activeAssessment}
            is_open={isPreviewModalOpen}
            close={() => setIsPreviewModalOpen(false)}
          /> */}

                <InviteCandidatesModal
                    close={closeInviteModal}
                    assessment_id={assessment.id}
                    is_open={isInviteModalOpen}
                    onSuccess={refetchAssessments}
                />

                <DeadlineSettingsModal
                    assessment={assessment}
                    isOpen={isDeadlineModalOpen}
                    onClose={closeDeadlineModal}
                    onSuccess={refetchAssessments}
                />

                <DuplicateAssessmentModal
                    is_open={isDuplicateModalOpen}
                    close={closeDuplicateModal}
                    assessment={assessment}
                    onSuccess={refetchAssessments}
                />
            </>

        </>
    )
}

