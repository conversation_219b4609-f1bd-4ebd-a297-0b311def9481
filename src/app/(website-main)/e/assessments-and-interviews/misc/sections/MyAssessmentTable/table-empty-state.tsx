import PlusIcon from "@/components/icons/PlusIcon"
import Button from "@/components/shared/Button"

interface TableEmptyStateProps {
    hasAssessments: boolean
    isFiltered: boolean
    onCreateAssessment: () => void
    onClearFilters: () => void
}

export function TableEmptyState({
    hasAssessments,
    isFiltered,
    onCreateAssessment,
    onClearFilters,
}: TableEmptyStateProps) {
    return (
        <div className="grid min-h-[80vh] flex-1 place-content-center">
            <div className="rounded-md border border-[#DADADA]/20 bg-gradient-to-b from-[#755ae20a] via-transparent to-transparent p-12 text-center">
                <div className="container max-w-xs space-y-2">
                    <div className="container flex justify-center">
                        <img src="/images/create-assessments/no-assessment.png" alt="writing with pen" />
                    </div>

                    {!hasAssessments ? (
                        <>
                            <h2>You currently do not have any open assessment</h2>
                            <p className="text-sm text-[#7D8590]">
                                Your running assessments would appear here, click button below to create an assessment
                            </p>
                            <Button onClick={onCreateAssessment} className="btn-primary inline-flex gap-2" type="button">
                                <PlusIcon />
                                Create new assessment
                            </Button>
                        </>
                    ) : (
                        <>
                            <h2>No assessment matches the filter</h2>
                            <p className="text-sm text-[#7D8590]">Clear the filters to see more results.</p>
                            <Button onClick={onClearFilters} className="btn-primary inline-flex gap-2" type="button">
                                Clear filters
                            </Button>
                        </>
                    )}
                </div>
            </div>
        </div>
    )
}

