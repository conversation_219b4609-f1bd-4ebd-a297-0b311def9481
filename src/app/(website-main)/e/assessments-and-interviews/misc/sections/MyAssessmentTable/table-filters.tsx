"use client"

import type React from "react"

import { useState } from "react"
import { Search } from "@/components/shared/icons"
import { Button as AltButton } from "@/components/shared"
import ListBox from "@/components/shared/listbox"
import { ASSESSMENT_PUBLISH_FILTERS } from "@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants"
import { XIcon } from "lucide-react"

interface TableFiltersProps {
    searchTerm: string
    onSearchChange: (term: string) => void
    activeFilter: string
    onFilterChange: (filter: string) => void
    onClearFilters: () => void
    totalResults: number
}

export function TableFilters({
    searchTerm,
    onSearchChange,
    activeFilter,
    onFilterChange,
    onClearFilters,
    totalResults,
}: TableFiltersProps) {
    const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm)

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value
        setLocalSearchTerm(value)
        onSearchChange(value)
    }

    const isFiltered = searchTerm || activeFilter

    return (
        <header className="flex items-center justify-between gap-4 rounded-md bg-white px-6 py-4 max-md:flex-col">
            <h2 className="text-base font-normal text-header-text">
                {isFiltered ? "Filtered" : ""} Assessments : <span className="font-bold text-body-text">{totalResults}</span>
            </h2>

            <div className="flex items-stretch gap-2">
                <ListBox
                    options={ASSESSMENT_PUBLISH_FILTERS}
                    className="border py-2 focus:outline-none"
                    triggerClass="text-sm"
                    placeholder="Published status"
                    active_option={activeFilter}
                    setActiveOption={onFilterChange}
                    value_key="id"
                    readable_text_key="name"
                />

                <div className="relative">
                    <Search className="absolute right-[5%] top-[25%]" />
                    <input
                        type="text"
                        placeholder="Search"
                        value={localSearchTerm}
                        maxLength={12}
                        onChange={handleSearchChange}
                        className="rounded-lg border-[1.5px] border-[#D6D6D6] px-3.5 py-2 text-[0.8rem] transition-all focus:outline-none sm:px-4"
                    />
                </div>

                {isFiltered && (
                    <AltButton
                        onClick={onClearFilters}
                        variant="extralight"
                        icon={<XIcon width={20} height={20} />}
                        className="border-grey py-1.5"
                        size="tiny"
                    >
                        Clear Filter
                    </AltButton>
                )}
            </div>
        </header>
    )
}

