"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { format } from "date-fns"
import DatePicker from "react-datepicker"
import "react-datepicker/dist/react-datepicker.css"
import Modal from "@/components/Modal"
import Button from "@/components/shared/Button"
import ListBox from "@/components/shared/listbox"
import { Spinner } from "@/components/shared/icons"
// import TimeInput from "@/app/(website-main)/e/assessments-and-interviews/components/TimeInput"
import { YES_OR_NO } from "@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants"
// import { editAssessmentAPI } from "@/app/(website-main)/e/assessments-and-interviews/api/Assessment"
import app_fetch from "@/lib/api/appFetch.js"
import { secondsToLargestUnit } from "@/lib/utils/functions"
import { TAssessmentListItem } from "../../api/getMyAssessments"
import TimeInput from "../../components/TimeInput"
import { editAssessmentAPI } from "../../api/Assessment"

interface DeadlineSettingsModalProps {
    assessment: TAssessmentListItem
    isOpen: boolean
    onClose: () => void
    onSuccess: () => void
}

interface DeadlineSettings {
    is_started: boolean
    time_limit_unit: string
    time_limit: number
    commencement_settings: string
    start_time: string
    deadline: string
}

export default function DeadlineSettingsModal({ assessment, isOpen, onClose, onSuccess }: DeadlineSettingsModalProps) {
    const [settings, setSettings] = useState<DeadlineSettings | null>(null)
    const [isLoading, setIsLoading] = useState(false)
    const [isSaving, setIsSaving] = useState(false)

    useEffect(() => {
        if (isOpen && assessment) {
            loadSettings()
        }
    }, [isOpen, assessment])

    const loadSettings = async () => {
        setIsLoading(true)
        try {
            const response = await app_fetch(`assessments/${assessment.id}/`)
            const result = await response.json()

            setSettings({
                is_started: result.start_time
                    ? new Date() > new Date(result?.start_time)
                    : result.completed.length + result.ongoing.length > 0,
                time_limit_unit: secondsToLargestUnit(result.time_limit).unit || "",
                time_limit: secondsToLargestUnit(result.time_limit).time || 0,
                commencement_settings: result.commencement_settings ? "yes" : "no",
                start_time: result.start_time?.split("+")[0],
                deadline: result.deadline
                    ? `${format(new Date(result.deadline), "yyyy-MM-dd")}T${new Date(result.deadline).toLocaleTimeString(undefined, { hourCycle: "h23", hour: "2-digit", minute: "2-digit", second: "2-digit" })}`
                    : "",
            })
        } catch (error) {
            console.error("Error loading settings:", error)
        } finally {
            setIsLoading(false)
        }
    }

    const handleSave = async (e: React.FormEvent) => {
        e.preventDefault()

        if (!settings) return

        const startTime = new Date(settings.start_time)
        const deadline = new Date(settings.deadline)

        if (startTime >= deadline) {
            alert("Assessments cannot end before the start time")
            return
        }

        setIsSaving(true)
        try {
            await editAssessmentAPI({
                id: assessment.id,
                ...settings,
            })

            onSuccess()
            onClose()
        } catch (error) {
            console.error("Error saving settings:", error)
        } finally {
            setIsSaving(false)
        }
    }

    if (!isOpen) return null

    return (
        <Modal title="Update Assessment Deadline" is_open={isOpen} close={onClose}>
            <form className="w-full max-w-[600px] space-y-4 p-4" onSubmit={handleSave}>
                {isLoading ? (
                    <div className="flex justify-center py-8">
                        <Spinner />
                    </div>
                ) : settings ? (
                    <>
                        <div>
                            <h2 className="heading-2">Update Assessment Deadline</h2>
                            {settings.is_started ? (
                                <p className="helper-text text-xs">
                                    Note: Only assessment end date can be edited as candidates have commenced assessment
                                </p>
                            ) : (
                                <p className="helper-text text-xs">
                                    You can modify this assessment deadline and have candidates take assessment before the set time and
                                    date
                                </p>
                            )}
                        </div>

                        <div className="space-y-4">
                            <div className="space-y-1">
                                <h3 className="heading-text">Commencement settings</h3>
                                <p className="text-xs">
                                    Select <span className="font-bold">'YES'</span> if you wish for candidates to complete the assessment
                                    at their convenience within a designated time frame, or choose <span className="font-bold">'NO'</span>{" "}
                                    if you prefer them to commence the assessment at the specific time and date you have specified.
                                </p>
                            </div>
                            <div className="flex gap-2">
                                <ListBox
                                    placeholder="Select commencement setting"
                                    className="border"
                                    options={YES_OR_NO}
                                    value_key="value"
                                    readable_text_key="readable_string"
                                    active_option={settings.commencement_settings}
                                    setActiveOption={(option) => {
                                        setSettings({
                                            ...settings,
                                            commencement_settings: option === "yes" ? "yes" : "no",
                                        })
                                    }}
                                />
                            </div>
                        </div>

                        <div className="space-y-4">
                            <div className="space-y-1">
                                <h3 className="heading-text">Set assessment time limit</h3>
                                <p className="text-xs">Once started, candidates are required to submit assessment within this time.</p>
                            </div>
                            <TimeInput
                                value={settings.time_limit}
                                onChange={(val) => {
                                    setSettings({
                                        ...settings,
                                        time_limit: val,
                                    })
                                }}
                            />
                        </div>

                        {(!settings.is_started || settings.commencement_settings === "yes") && (
                            <div className="space-y-4">
                                <div className="space-y-1">
                                    <h3 className="heading-text">Date and time settings</h3>
                                    <p className="text-xs">
                                        Specify the start and end date and time for candidates to start and complete this assessment.
                                    </p>
                                </div>

                                {!settings.is_started && (
                                    <label className="flex items-center gap-4">
                                        Start:
                                        <input
                                            className="input-white w-min border"
                                            type="datetime-local"
                                            value={settings.start_time}
                                            required
                                            name="date"
                                            onChange={(event) => {
                                                setSettings({
                                                    ...settings,
                                                    start_time: event.target.value,
                                                })
                                            }}
                                        />
                                    </label>
                                )}

                                {settings.commencement_settings === "yes" && (
                                    <label className="flex items-center gap-4">
                                        End:
                                        <DatePicker
                                            showTimeSelect
                                            value={
                                                settings.deadline
                                                    ? new Date(settings.deadline).toLocaleString("en-NG", {
                                                        hour12: true,
                                                        weekday: "short",
                                                        year: "numeric",
                                                        month: "short",
                                                        day: "numeric",
                                                        hour: "numeric",
                                                        minute: "numeric",
                                                    })
                                                    : ""
                                            }
                                            placeholderText="Set Deadline"
                                            required
                                            selected={new Date(settings.deadline)}
                                            minDate={new Date(settings.start_time)}
                                            onChange={(date) => {
                                                setSettings({
                                                    ...settings,
                                                    deadline: date
                                                        ? `${format(date, "yyyy-MM-dd")}T${date.toLocaleTimeString(undefined, { hourCycle: "h23", hour: "2-digit", minute: "2-digit", second: "2-digit" })}`
                                                        : "",
                                                })
                                            }}
                                            className="input-white w-max border"
                                        />
                                    </label>
                                )}
                            </div>
                        )}

                        <div className="flex justify-end">
                            <Button className="btn-primary" type="submit" is_busy={isSaving}>
                                Save and Update
                            </Button>
                        </div>
                    </>
                ) : (
                    <p>Oops, something went wrong. Please try again</p>
                )}
            </form>
        </Modal>
    )
}

