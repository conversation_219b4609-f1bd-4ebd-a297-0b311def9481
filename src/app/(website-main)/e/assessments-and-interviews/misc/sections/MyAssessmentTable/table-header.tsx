import { TableHead, TableRow, TableHeader as RTableHeader } from "@/components/shared/table"

export function TableHeader() {
    const headers = ["Id", "Name", "Candidates", "Progress", "Date Created", "Deadline", "Action"]

    return (
        <RTableHeader>
            <TableRow>
                {headers.map((heading, index) => (
                    <TableHead key={index} className="p-4 font-normal text-header-text">
                        <p className={index === 3 ? "" : "text-left"}>{heading}</p>
                    </TableHead>
                ))}
            </TableRow>
        </RTableHeader>
    )
}

