'use client';

import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import { AvatarGroupName, ToolTip } from '@/components/shared';
import {
  TableBody as RTableBody,
  TableCell,
  TableRow,
} from '@/components/shared/table';
import { TAssessmentListItem } from '../../api/getMyAssessments';
import { TableRowActions } from './table-row-actions';

interface TableBodyProps {
  assessments: TAssessmentListItem[];
  refetchAssessments: () => void;
  currentPage: number;
  pageSize: number;
}

export function TableBody({
  assessments,
  refetchAssessments,
  currentPage,
  pageSize,
}: TableBodyProps) {
  const router = useRouter();

  return (
    <RTableBody className="divide-y">
      {assessments.map((assessment, index) => (
        <TableRow
          className="cursor-pointer text-sm hover:bg-primary-light"
          onClick={() => {
            router.push(
              `/e/assessments-and-interviews/assessments/${assessment.id}`
            );
          }}
          key={assessment.id}
        >
          <TableCell className="px-4 py-2 font-medium text-header-text">
            {(currentPage - 1) * pageSize + index + 1}
          </TableCell>
          <TableCell className="px-4 py-2 text-[#4A4A68]">
            {assessment.name}
          </TableCell>
          <TableCell className="px-4 py-2 text-[#4A4A68]">
            <div className="flex items-center gap-2">
              <p>{assessment.no_candidates}</p>
              <AvatarGroupName
                names={assessment.candidates_data.map(
                  candidate => candidate.candidate_name
                )}
                max={5}
                size="small"
              />
            </div>
          </TableCell>
          <TableCell className="px-4 py-2 text-[#4A4A68]">
            <AssessmentProgress assessment={assessment} />
          </TableCell>
          <TableCell className="px-4 py-2 text-[#4A4A68]">
            {format(new Date(assessment.created_at), 'dd/MM/yyyy, hh:mma')}
          </TableCell>
          <TableCell className="px-4 py-2 text-[#4A4A68]">
            {assessment.deadline
              ? format(new Date(assessment.deadline), 'dd/MM/yyyy, hh:mma')
              : '-'}
          </TableCell>
          <TableCell className="flex p-4" onClick={e => e.stopPropagation()}>
            <TableRowActions
              assessment={assessment}
              refetchAssessments={refetchAssessments}
            />
          </TableCell>
        </TableRow>
      ))}
    </RTableBody>
  );
}

function AssessmentProgress({
  assessment,
}: {
  assessment: TAssessmentListItem;
}) {
  if (!assessment.is_published) {
    return (
      <div className="flex items-center justify-center">
        <ToolTip
          content="This assessment has not been published yet"
          contentClass="text-xs"
        >
          <p className="rounded-full bg-primary-light-hover px-6 py-1 text-xs font-medium text-[#3C1356]">
            draft
          </p>
        </ToolTip>
      </div>
    );
  }

  return (
    <ul className="flex items-center justify-center gap-2">
      <li>
        <ToolTip content="completed" contentClass="text-xs">
          <p className="rounded-full bg-primary px-6 py-1 text-xs font-semibold text-white">
            {assessment.completed.length}
          </p>
        </ToolTip>
      </li>
      <li>
        <ToolTip content="started" contentClass="text-xs">
          <p className="rounded-full bg-primary-light-hover px-6 py-1 text-xs font-semibold text-[#3C1356]">
            {assessment.ongoing.length}
          </p>
        </ToolTip>
      </li>
      <li>
        <ToolTip content="not started" contentClass="text-xs">
          <p className="rounded-full bg-primary-light-hover px-6 py-1 text-xs font-semibold text-[#3C1356]">
            {assessment.not_started.length}
          </p>
        </ToolTip>
      </li>
    </ul>
  );
}
