'use client';

import { useEffect, useState } from 'react';
import { Spinner } from '@/components/shared/icons';
import { Table } from '@/components/shared/table';
import { TAssessmentListItem } from '../../api/getMyAssessments';
import { TableBody } from './table-body';
import { TableEmptyState } from './table-empty-state';
import { TableHeader } from './table-header';
import { TablePagination } from './table-pagination';

interface AssessmentTableProps {
  data:
    | {
        count: number;
        results: TAssessmentListItem[];
      }
    | undefined;
  isLoading: boolean;
  searchTerm: string;
  pageSize: number;
  pageNumber: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
  onSearch: (term: string) => void;
  onFilterChange: (filter: string) => void;
  onClearFilters: () => void;
  refetchAssessments: () => void;
  activeFilter: string;
  onCreateAssessment: () => void;
}

export function AssessmentTable({
  data,
  isLoading,
  searchTerm,
  pageSize,
  pageNumber,
  onPageChange,
  onPageSizeChange,
  onSearch,
  onFilterChange,
  onClearFilters,
  refetchAssessments,
  activeFilter,
  onCreateAssessment,
}: AssessmentTableProps) {
  const [filteredAssessments, setFilteredAssessments] = useState<
    TAssessmentListItem[]
  >([]);

  useEffect(() => {
    if (data?.results) {
      setFilteredAssessments(data.results);
    }
  }, [data]);

  if (isLoading) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <Spinner />
      </div>
    );
  }

  if (!data?.results || data.results.length === 0) {
    return (
      <TableEmptyState
        hasAssessments={false}
        onCreateAssessment={onCreateAssessment}
        onClearFilters={onClearFilters}
        isFiltered={!!searchTerm || !!activeFilter}
      />
    );
  }

  if (filteredAssessments.length === 0 && (searchTerm || activeFilter)) {
    return (
      <TableEmptyState
        hasAssessments={true}
        onCreateAssessment={onCreateAssessment}
        onClearFilters={onClearFilters}
        isFiltered={true}
      />
    );
  }

  return (
    <div className="flex flex-col space-y-4">
      <div className="overflow-x-auto">
        <Table className="w-full divide-y rounded-md bg-white">
          <TableHeader />
          <TableBody
            assessments={filteredAssessments}
            refetchAssessments={refetchAssessments}
            currentPage={pageNumber}
            pageSize={pageSize}
          />
        </Table>
      </div>

      <TablePagination
        totalCount={data.count}
        pageSize={pageSize}
        currentPage={pageNumber}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
      />
    </div>
  );
}
