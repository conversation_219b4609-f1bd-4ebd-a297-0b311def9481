import { Button } from "@/components/shared"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface TablePaginationProps {
    totalCount: number
    pageSize: number
    currentPage: number
    onPageChange: (page: number) => void
    onPageSizeChange: (size: number) => void
}

export function TablePagination({
    totalCount,
    pageSize,
    currentPage,
    onPageChange,
    onPageSizeChange,
}: TablePaginationProps) {
    const totalPages = Math.ceil(totalCount / pageSize)

    if (totalPages <= 1) return null

    return (
        <div className="flex items-center justify-between px-4 py-2 sm:pr-16">
            <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Rows per page:</span>
                <select
                    className="rounded border p-1 text-sm"
                    value={pageSize}
                    onChange={(e) => onPageSizeChange(Number(e.target.value))}
                >
                    {[5, 10, 25, 50].map((size) => (
                        <option key={size} value={size}>
                            {size}
                        </option>
                    ))}
                </select>
            </div>

            <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">
                    {`${(currentPage - 1) * pageSize + 1}-${Math.min(currentPage * pageSize, totalCount)} of ${totalCount}`}
                </span>

                <Button
                    variant="extralight"
                    size="icon"
                    onClick={() => onPageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="h-8 w-8"
                >
                    <ChevronLeft className="h-4 w-4" />
                </Button>

                <Button
                    variant="extralight"
                    size="icon"
                    onClick={() => onPageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="h-8 w-8"
                >
                    <ChevronRight className="h-4 w-4" />
                </Button>
            </div>
        </div>
    )
}

