import { create } from "zustand";
import {
  AssessmentState,
  Invitation,
  InvitationTypes,
} from "@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments";

import app_fetch from "@/lib/api/appFetch.js";
import { blobToBase64 } from "@/lib/utils/functions"
import { CUSTOM_EMAIL_TEMPLATE, CUSTOM_TEMPLATE_EMAIL_AUTH_PARTIAL, CUSTOM_TEMPLATE_FILE_AUTH_PARTIAL, TOKEN_CONSTANTS } from "@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants";
import { uploadToServer } from "@/app/(public-links)/jobs/misc/components/application/upload";

type AssessmentStoreFunctions = {
  resetState: () => void;

  setId: (data: string) => void;
  setCustomEmailMessage: (data: string) => void;
  setCustomEmailSubject: (data: string) => void;
  setCustomEmailCoverImage: (data: Blob) => void;

  setInvitations: (invitations: Invitation[]) => void;

  setIsAutomaticallyGenerated: (is_automatically_generated: boolean) => void;
  setAssessmentShouldOpenFirstQuestionModal: (
    should_open_first_question_modal: boolean,
  ) => void;

  deleteInvitee: (invitee_email: string) => void;

  setChoosenInvitationType: (choice: InvitationTypes) => void;
  sendEmailInvitations: (options?: { type: "interview" | "assessment" }) => Promise<object>;
  sendBulkInvitation: (options?: { type: "interview" | "assessment" }) => Promise<object>;

  setSelectedFileFields: (new_fields: string[]) => void;
  setFileDetails: (file_details: any) => void;
}


const initial_state: AssessmentState = {

  id: "",
  custom_email_message: "",
  custom_email_subject: "",
  cover_image: undefined,

  is_automatically_generated: false,
  should_open_first_question_modal: false,

  selected_file_fields: [],
  choosen_invitation_type: "email",
  file_details: undefined,

  invitations: [
    { name: "", email: "" },
    { name: "", email: "" },
    { name: "", email: "" },
  ],

  delete_invites: [],
};

interface htmlToTokenSProps {
  html_string: string;
  auth_type?: "email" | "file";
  cover_image: string;
}
async function htmlToTokens(data: htmlToTokenSProps) {
  const { html_string, auth_type, cover_image } = data;

  const parser = new DOMParser();
  const dom = parser.parseFromString(html_string, "text/html");

  dom
    .querySelectorAll(`.${TOKEN_CONSTANTS.class_name}`)
    .forEach((token: any) => {
      const {
        dataset: { value },
      } = token;
      token.replaceWith(TOKEN_CONSTANTS.regex.replace("token", value));
    });
  const base64CoverImage = cover_image ?? "https://res.cloudinary.com/dvdnjlxa8/image/upload/v1696419285/Group_40747_thpo8x.png"
  const EMAIL_TEMPLATE_WITH_AUTH = CUSTOM_EMAIL_TEMPLATE(base64CoverImage).replace("{{#auth#}}", (auth_type || "email") == "email" ? CUSTOM_TEMPLATE_EMAIL_AUTH_PARTIAL : CUSTOM_TEMPLATE_FILE_AUTH_PARTIAL)

  return unescape(encodeURIComponent(EMAIL_TEMPLATE_WITH_AUTH.replace("{{#content_goes_here#}}", dom.body.innerHTML)));
}

export const useCreateAssessmentStore = create<
  AssessmentState & AssessmentStoreFunctions
>((set, get) => ({
  ...initial_state,

  setId: (id: string) => set(state => ({ ...state, id: id })),

  setCustomEmailMessage: (custom_email_message) => set((state) => ({ ...state, custom_email_message })),
  setCustomEmailSubject: (custom_email_subject) => set((state) => ({ ...state, custom_email_subject })),
  setCustomEmailCoverImage: (cover_image) => set((state) => ({ ...state, cover_image })),

  setIsAutomaticallyGenerated: (is_automatically_generated) =>
    set((state) => ({ ...state, is_automatically_generated })),

  setAssessmentShouldOpenFirstQuestionModal: (
    should_open_first_question_modal,
  ) => set((state) => ({ ...state, should_open_first_question_modal })),

  resetState: () => {
    set(initial_state);
    get().setInvitations([
      { name: "", email: "" },
      { name: "", email: "" },
      { name: "", email: "" },
    ])
  },

  setInvitations: (invitations) => set((state) => ({ ...state, invitations })),

  setSelectedFileFields: (selected_file_fields) =>
    set((state) => ({ ...state, selected_file_fields })),
  setChoosenInvitationType: (choosen_invitation_type) =>
    set((state) => ({ ...state, choosen_invitation_type })),
  setFileDetails: (file_details) =>
    set((state) => ({ ...state, file_details })),

  deleteInvitee: (invitee_email: string) => {
    switch (get().choosen_invitation_type) {
      case "email":
        set(state => ({ ...state, invitations: get().invitations.filter(invite => invite.email != invitee_email) }))
        break;
      case "file":
        set(state => ({ ...state, delete_invites: [...get().delete_invites, invitee_email], invitations: get().invitations.filter(invite => invite.email != invitee_email) }))
        break;
      case "pool":
        break;
    }
  },

  sendEmailInvitations: (options = { type: "assessment" }) => {
    return new Promise(async (resolve, reject) => {
      const _candidates: Invitation[] = [];
      get().invitations
        .filter((invitation) =>
          invitation.email !== "" && invitation.name !== ""
        )
        .forEach((invitation) => {
          _candidates.push({
            name: invitation.name,
            email: invitation.email,
          });
        });


      const cover_image = get().cover_image;

      let cover_image_base64;
      if (cover_image) {
        const coverImageFile = new File([cover_image], "cover_image.png", { type: cover_image.type });
        const uploadedURL = await uploadToServer(coverImageFile);
        cover_image_base64 = uploadedURL.secure_url;
      }

      const data = {
        assessment: undefined as unknown as string,
        assessment_interview: undefined as unknown as string,
        candidates: _candidates,
        auth_method: [],
        custom_email_subject: get().custom_email_subject ? get().custom_email_subject : undefined,
        custom_email_message: get().custom_email_message ? btoa(await htmlToTokens({
          html_string: get().custom_email_message || "",
          auth_type: "email",
          cover_image: !!cover_image_base64 ? "{{ cover_image }}" : "https://res.cloudinary.com/dvdnjlxa8/image/upload/v1696419285/Group_40747_thpo8x.png"
        })) : undefined,
        cover_image: cover_image_base64,
        is_from_job_post: false
      }

      if (options.type === "assessment") {
        data.assessment = get().id
      } else {
        data.assessment_interview = get().id
      }

      var raw = JSON.stringify(data);

      var requestOptions = {
        method: "POST",
        body: raw,
      };

      const endpoint = options.type === "assessment" ? "assessments/assessment-invites/" : "assessments/interview-invites/"

      app_fetch(endpoint, requestOptions)
        .then((response) => response.json())
        .then(() => {
          resolve({ message: "success" });
        })
        .catch((error) => {
          reject(error);
        });
    });
  },

  sendBulkInvitation: (options = { type: "assessment" }) => {
    return new Promise(async (resolve, reject) => {
      const formdata = new FormData();
      switch (options.type) {
        case "assessment": {
          formdata.append("assessment", get().id);
          break
        }
        case "interview": {
          formdata.append("assessment_interview", get().id);
          break
        }
      }
      formdata.append("assessment", get().id);
      formdata.append("invite_document", get().file_details);

      const cover_image = get().cover_image;

      let cover_image_base64;
      if (cover_image) {
        const coverImageFile = new File([cover_image], "cover_image.png", { type: cover_image.type });
        const uploadedURL = await uploadToServer(coverImageFile);
        cover_image_base64 = uploadedURL.secure_url;
      }


      const email_message = get().custom_email_message;
      if (email_message) {
        formdata.append("custom_email_message", btoa(await htmlToTokens({
          html_string: email_message,
          auth_type: "email",
          cover_image: !!cover_image_base64 ? "{{ cover_image }}" : "https://res.cloudinary.com/dvdnjlxa8/image/upload/v1696419285/Group_40747_thpo8x.png"
        })))
      }

      const email_subject = get().custom_email_subject;
      if (email_subject) {
        formdata.append("custom_email_subject", email_subject)
      }

      get().delete_invites.forEach(email => {
        formdata.append("delete_invites", email)
      })
      get().selected_file_fields.forEach((auth_method: string) => {
        formdata.append("auth_method", auth_method);
      });

      var requestOptions = {
        method: "POST",
        body: formdata,
      };

      const endpoint = options.type === "assessment" ? "assessments/assessment-invites/" : "assessments/interview-invites/"

      app_fetch(endpoint, requestOptions, false)
        .then((response) => response.json())
        .then((result) => {
          resolve({ message: "success" });
        })
        .catch((error) => reject({ error }));
    });
  },
}));
