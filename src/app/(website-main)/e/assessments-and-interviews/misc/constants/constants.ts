import {
  BulkProctorOption,
  CandidateAssessmentStatus,
  ProctoringOptions,
  ProctoringToleranceOption,
  proctorOption,
  QuestionType,
  TeamMemberRoleTypes,
  TimeLimitUnits,
} from '../types/create-assessments';

export const ALPHABETS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');

export const MAX_NUMBER_OF_SECTIONS = 5;

function setDictionary<object_item, Key extends keyof object_item>(
  list_of_items: object_item[],
  key: Key
): Record<
  object_item[Key] extends string | number | symbol ? object_item[Key] : never,
  object_item
> {
  const data = {} as Record<
    object_item[Key] extends string | number | symbol
      ? object_item[Key]
      : never,
    object_item
  >;
  list_of_items.forEach(obj => {
    data[
      obj[key] as object_item[Key] extends string | number | symbol
        ? object_item[Key]
        : never
    ] = obj;
  });

  return data;
}

export const ROLE_LEVELS = [
  {
    id: 'entry_level',
    name: 'entry level',
  },
  {
    id: 'intermediate_level',
    name: 'intermediate level',
  },
  {
    id: 'expert_level',
    name: 'expert level',
  },
];
export const BETA_TESTER_OPTION = [
  {
    id: 'yes',
    name: 'Yes',
  },
  {
    id: 'no',
    name: 'No',
  },
];

export const ROLE_LEVELS_DICTIONARY = setDictionary(ROLE_LEVELS, 'id');

export const TEAM_MEMBER_ROLES: { id: TeamMemberRoleTypes }[] = [
  {
    id: 'editor',
  },
  {
    id: 'viewer',
  },
];

export const TEAM_MEMBER_ROLES_DICTIONARY = setDictionary(
  TEAM_MEMBER_ROLES,
  'id'
);

export const ASSESSMENT_TYPES = [
  {
    id: 'f33bd48d-0d52-4de0-ba0c-bb21c7307fa3',
    name: 'skills & competency',
  },
  {
    id: '5890115d-68f2-4eb4-8121-6f86cfc0c674',
    name: 'cognitive & critical thinking',
  },
  {
    id: '97852241-3808-4961-a597-0b56460c736c',
    name: 'situational judgement',
  },
  { id: 'acfb6aca-bd88-4d6d-9281-407e7199e61e', name: 'Skills & Competency' },
  {
    id: '270ddeab-e351-467a-99f1-7624b977fa10',
    name: 'Cognitive & Critical Thinking',
  },
  { id: '2ec03174-3a93-4e71-a603-28a55c860543', name: 'Personality' },
  {
    id: '3ace1dec-0aa1-427e-929a-398e90483b55',
    name: 'Emotional Intelligence',
  },
  { id: '73737226-54d1-411d-877d-f775451d1e45', name: 'Skills' },
];

export const ASSESSMENT_TYPES_DICTIONARY = setDictionary(
  ASSESSMENT_TYPES,
  'name'
);

export const ASSESSMENT_PUBLISH_FILTERS = [
  {
    id: 'is_published',
    name: 'Published',
  },
  {
    id: 'is_draft',
    name: 'Draft',
  },
];

export const ASSESSMENT_STATUSES_DICTIONARY = {
  not_started: {
    title: 'Not started',
  },
  ongoing: {
    title: 'Not completed',
  },
  completed: {
    title: 'Completed',
  },
};

type QuestionTypeOption = QuestionType & { icon?: () => JSX.Element };

export const QUESITON_TYPES: QuestionTypeOption[] = [
  {
    value: 'mixed-options',
    name: 'mixed options',
    count_name: 'no_mixed',
  },
  {
    value: 'multiple_choice',
    name: 'multiple choice',
    count_name: 'no_multiple_choice',
  },
  {
    value: 'coding',
    name: 'coding',
    count_name: 'no_coding',
  },
  {
    value: 'video',
    name: 'video',
    count_name: 'no_video',
  },
  {
    value: 'sql',
    name: 'SQL',
    count_name: 'no_sql',
  },
  {
    value: 'excel',
    name: 'excel',
    count_name: 'no_excel',
  },
  {
    value: 'file-upload',
    name: 'file upload',
    count_name: 'no_file_upload',
  },
  {
    value: 'essay',
    name: 'essay',
    count_name: 'no_essay',
  },
  {
    value: 'multiple_response',
    name: 'multiple response',
    count_name: 'no_multiple_response',
  },
  {
    value: 'true_false',
    name: 'true or false',
    count_name: 'no_true_false',
  },
  {
    value: 'fill_in_the_blanks',
    name: 'fill in the blanks',
    count_name: 'no_fill_in_the_blanks',
  },
  {
    value: 'drag_and_drop',
    name: 'drag and drop',
    count_name: 'no_drag_and_drop',
  },
];

export const QUESTION_TYPES_DICTIONARY = setDictionary(QUESITON_TYPES, 'value');

type TIME_LIMIT_UNIT_TYPE = {
  value: TimeLimitUnits;
  readable_string: string;
};

export const TRUE_OR_FALSE = [
  {
    value: 'true',
    readable_string: 'true',
  },
  {
    value: 'false',
    readable_string: 'false',
  },
];

export const YES_OR_NO = [
  {
    value: 'yes',
    readable_string: 'yes',
  },
  {
    value: 'no',
    readable_string: 'no',
  },
];

export const YES_TRUE_OR_NO_FALSE = [
  {
    value: 'yes',
    readable_string: true,
  },
  {
    value: 'no',
    readable_string: false,
  },
];

export const TIME_LIMIT_UNITS: TIME_LIMIT_UNIT_TYPE[] = [
  {
    value: 'second(s)',
    readable_string: 'second(s)',
  },
  {
    value: 'minute(s)',
    readable_string: 'minute(s)',
  },
  {
    value: 'hour(s)',
    readable_string: 'hour(s)',
  },
  {
    value: 'day(s)',
    readable_string: 'day(s)',
  },
];

export const TIME_LIMIT_UNITS_DICTIONARY = setDictionary(
  TIME_LIMIT_UNITS,
  'value'
);

export const ASSESSMENT_TABLE_OPTIONS = [
  {
    id: 1,
    icon: 'eye',
    title: 'view details',
  },
  {
    id: 2,
    icon: 'fingerprint',
    title: 'preview assessment',
  },
  {
    id: 3,
    icon: 'note-remove',
    title: 'set deadline',
  },
  // {
  //   id: 4,
  //   icon: 'lock',
  //   title: 'manage access',
  // },
  {
    id: 5,
    icon: 'user-add',
    title: 'invite candidates',
  },
  {
    id: 6,
    icon: 'close-square',
    title: 'duplicate assessment',
  },
  {
    id: 7,
    icon: 'close-square',
    title: 'delete assessment',
  },
];

export const ASSESSMENT_TABLE_OPTIONS_DICTIONARY = setDictionary(
  ASSESSMENT_TABLE_OPTIONS,
  'title'
);

export const INTERVIEW_TABLE_OPTIONS = [
  {
    id: 1,
    icon: 'eye',
    title: 'view details',
  },
  {
    id: 2,
    icon: 'fingerprint',
    title: 'preview interview',
  },
  {
    id: 3,
    icon: 'user-add',
    title: 'invite candidates',
  },
  {
    id: 4,
    icon: 'close-square',
    title: 'delete interview',
  },
];

export const INTERVIEW_TABLE_OPTIONS_DICTIONARY = setDictionary(
  INTERVIEW_TABLE_OPTIONS,
  'title'
);

export const CANDIDATE_TABLE_OPTIONS = [
  {
    id: 1,
    icon: 'eye',
    title: 'view details',
  },
  {
    id: 3,
    icon: 'close-square',
    title: 'remove candidate',
  },
];

export const CANDIDATE_TABLE_OPTIONS_DICTIONARY = setDictionary(
  CANDIDATE_TABLE_OPTIONS,
  'title'
);

export const QUESTION_OPTIONS = [
  {
    icon: 'eye',
    title: 'view',
  },
  {
    icon: 'edit-square',
    title: 'edit',
  },
  {
    icon: 'close-square',
    title: 'delete',
  },
];

export const SECTION_OPTIONS = [
  {
    icon: 'edit-square',
    title: 'edit section',
  },
  {
    icon: 'close-square',
    title: 'delete section',
  },
];

export const PROCTORING_OPTIONS: {
  title: string;
  key: ProctoringOptions;
  description: string;
}[] = [
  {
    title: 'Identity verification',
    key: 'identity_verification',
    description: `Tap toggle to have the assessment onboarding require identify verification for candidates. This would require candidates to provide valid IDs e.g NIN, Voter's card, Int'l passport.`,
  },
  {
    title: 'Webcam snapshot',
    key: 'webcam_snapshot',
    description:
      "Tap toggle to enable capturing periodic snapshots of the candidate throughout the assessment. These snapshots will be accessible in the candidate's post-assessment report.",
  },
  {
    title: 'Restrict candidates from copying assessment content',
    key: 'restrict_copying',
    description:
      'Tap toggle to prevent candidates from copying assessment content.',
  },
  {
    title: 'Restrict tab change',
    key: 'restrict_tab_change',
    description:
      "Tap toggle to prevent candidates from changing tabs during assessment.  You can view tab changing activity in the candidate 's post-assessment report.",
  },
  {
    title: 'External source paste tracking',
    key: 'track_paste',
    description:
      "Tap toggle to activate the option to track pasted content from external sources during candidate assessments. You will find pasted content details in the candidate's post-assessment report",
  },
  {
    title: 'Stop screen sharing',
    key: 'stop_screen_sharing',
    description:
      'Tap toggle to activate the option to restrict candidates from being able to share their screen during the assessment.',
  },
];

export const COMMENT_OPTIONS = [
  {
    id: 1,
    icon: 'edit-square',
    title: 'edit comment',
  },
  {
    id: 2,
    icon: 'close-square',
    title: 'delete comment',
  },
];

export const COMMENT_OPTIONS_DICTIONARY = setDictionary(
  COMMENT_OPTIONS,
  'title'
);

type _CandidateAssessmentStatus =
  | { title: string; value: CandidateAssessmentStatus }
  | undefined;
export const CANDIDATES_ASSESSMENT_STATUSES: _CandidateAssessmentStatus[] = [
  {
    title: 'completed',
    value: 'completed',
  },
  {
    title: 'not started',
    value: 'not_started',
  },
  {
    title: 'started',
    value: 'ongoing',
  },
];

export const TOKEN_CONSTANTS = {
  class_name: 'token',
  tag_name: 'span',
  regex: '{{ token }}',
};

export const CUSTOM_INVITATION_TOKENS = [
  { value: 'assessment_name', title: 'assessment name' },
  { value: 'start_time', title: 'start time' },
  { value: 'invitation_link', title: 'invitation link' },
  // { value: "cover_image", title: "cover image" },
  { value: 'candidate_name', title: 'candidate name' },
  { value: 'candidate_email', title: 'candidate email' },
  { value: 'job_position', title: 'job position' },
  { value: 'company_name', title: 'company name' },
  { value: 'time_limit', title: 'time limit' },
  { value: 'start_date', title: 'start date' },
  { value: 'deadline', title: 'deadline' },
  { value: 'auth_method1', title: 'auth method1' },
  { value: 'auth_method2', title: 'auth method2' },
  { value: 'auth_method1_value', title: 'auth method 1 value' },
  { value: 'auth_method2_value', title: 'auth method 2 value' },
];

export const BULK_PROCTOR_OPTIONS: BulkProctorOption[] = [
  {
    key: proctorOption.fullScreenExit,
    value: 'Full screen exit',
  },
  {
    key: proctorOption.windowChange,
    value: 'Window change',
  },
  {
    key: proctorOption.tabChange,
    value: 'Tab change',
  },
  {
    key: proctorOption.multiFace,
    value: 'Multiple Face',
  },
  {
    key: proctorOption.differentFace,
    value: 'Different Face',
  },
  {
    key: proctorOption.illegalObject,
    value: 'Illegal Object',
  },
];

export const PROCTORING_SECTION_OPTIONS = [
  {
    id: 'individual_tolerance_settings',
    title: 'Individual tolerance settings',
    name: 'Individual setup',
    description:
      'Here you can  configure Proctoring options on an individual basis',
  },
  {
    id: 'bulk_tolerance_settings',
    title: 'Bulk tolerance settings',
    name: 'Bulk tolerance setup',

    description:
      'Here you can combine multiple Proctoring options and set same tolerance count across all Proctoring options. ',
  },
  {
    id: 'other_setup_options',
    title: 'Other assessment settings',
    name: 'Other setup options',

    description:
      'Here, you can tap the toggle below activate or deactivate the default settings for each assessment, allowing you to customize the assessment experience to meet specific requirements.',
  },
];

export const PROCTORING_TOLERANCE_OPTIONS: ProctoringToleranceOption[] = [
  {
    id: 'full_screen_tolerance_level',
    title: 'full screen mode',
    description:
      'All assessment are to be done within the full screen mode, in a case where candidates exit the full screen',
    modal_description:
      'Full screen mode restricts candidates from the ability to change tabs thereby mitigating cheating chances. Determine the tolerance level of how many times you would like to permit candidates on exit full screen mode.',
  },
  {
    id: 'window_change_tolerance_level',
    title: 'Window change',
    description:
      'All assessment are to be done within one window, in a case where candidates attempt to change a window',
    modal_description: `Window change restricts candidates from the ability to open and switch between another window thereby mitigating cheating chances.Determine the tolerance level of how many times you would like to permit candidates on attempt opening or switch window`,
  },

  {
    id: 'tab_change_tolerance_level',
    title: 'Tab change',
    description:
      'All assessments must be completed within a single browser tab. If a candidate switches tabs',
    modal_description: ``,
  },
  {
    id: 'multi_face_tolerance_level',
    title: 'Multiple face',
    description:
      'Only one face should be visible during the assessment. If multiple faces are detected',
    modal_description:
      'Only one face should be visible during the assessment. If multiple faces are detected',
  },
  {
    id: 'different_face_tolerance_level',
    title: 'Difference face',
    description:
      'Assessments require consistent facial recognition. If a different face is detected during ',
    modal_description:
      'Assessments require consistent facial recognition. If a different face is detected during ',
  },
];
export const CUSTOM_EMAIL_TEMPLATE = (cover_image: string) => {
  return `
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<html lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <style>
      @font-face {
        font-family: "DM sans";
        font-style: normal;
        font-weight: 400;
        mso-font-alt: Verdana;
        src: url("https://fonts.googleapis.com/css2?family=DM+Sans:opsz,wght@9..40,100;9..40,200;9..40,300;9..40,400;9..40,500;9..40,600;9..40,700;9..40,800;9..40,900;9..40,1000&amp;family=Montserrat&amp;display=swap");
        /* format(&#x27; woff2&#x27; ); */
      }

      * {
        font-family: "DM sans", Verdana;
      }
    </style>
  </head>

  <body data-id="__react-email-body" style="
      width: max-content;
      height: max-content;
      margin: 0 auto;
      background: #f4f4f4ff;
    ">
    <table align="center" width="100%" data-id="__react-email-container" role="presentation" cellspacing="0" cellpadding="0" border="0" style="
        max-width: 37.5em;
        width: 100vw;
        height: max-content;
        overflow-x: hidden;
        padding-top: 0px;
        box-sizing: border-box;
        background-color: white;
      ">
      <tbody>
        <tr style="width: 100%">
          <td>
            <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation">
              <tbody>
                <tr>
                  <td>
                    <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation" style="
                        width: 100%;
                        height: 20%;
                        display: flex;
                        flex-direction: column;
                        color: rgb(255, 255, 255);
                        row-gap: 0.625rem;
                        justify-content: center;
                        align-items: center;
                        background-color: rgb(132, 39, 189);
                        background: url("${cover_image}");
                      ">
                      <tbody>
                        <tr>
                          <td style="position:relative">
                            <img data-id="react-email-img" src="${cover_image}"  style="
                                display: block;
                                outline: none;
                                border: none;
                                text-decoration: none;
                                width: 100%;
                                max-width: 615px;
                                max-height: 150px;
                                object-fit: cover;
                              ">
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation" style="
                        padding-left: 5rem;
                        padding-right: 5rem;
                        padding-top: 2.5rem;
                        padding-bottom: 2.5rem;
                      ">
                      <tbody>
                        <tr>
                          <td>
                            <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation">
                              <tbody>
                                <tr>
                                  <td>
                                    {{#content_goes_here#}}
                                    <p data-id="react-email-text" style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 0.5rem;
                                        padding-bottom: 0.5rem;
                                      "> <strong> Assessment Details </strong>
                                    </p>
                                    <p data-id="react-email-text" style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 0px;
                                        padding-bottom: 0px;
                                      ">
                                      Assessment Name: <strong>{{ assessment_name }}</strong>
                                    </p>
                                    <p data-id="react-email-text" style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 2px;
                                        padding-bottom: 2px;

                                      ">
                                    {% if start_date %}
                                        Assessment Start Date: <strong>{{ start_date }}</strong><br>
                                    {% endif %}
                                    Duration: <strong>{{ time_limit }}</strong><br>
                                    </p>
                                    <p data-id="react-email-text" style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 0px;
                                        padding-bottom: 0px;
                                      ">
                                    Assessment deadline: This assessment is valid until <strong>{{ deadline }}</strong>.<br>
                                    {% if start_date %}
                                        The 'Take assessment' button provided below will only be active from <strong>{{ start_date }}</strong> until <strong>{{ deadline }}</strong>.<br>
                                    {% else %}
                                        The 'Take assessment' button provided below will only be active during the mentioned time period.
                                    {% endif %}
                                    </p>
                                    <a href="{{ invitation_link }}" data-id="react-email-button" target="_blank" style="
                                        line-height: 100%;
                                        text-decoration: none;
                                        display: inline-block;
                                        max-width: 100%;
                                        padding: 0.75rem;
                                        background-color: rgb(117, 90, 226);
                                        border-radius: 0.375rem;
                                        padding-top: 0.5rem;
                                        padding-bottom: 0.5rem;
                                        margin-top: 0.5rem;
                                        margin-bottom: 0.5rem;
                                      "><span></span><span style="
                                          max-width: 100%;
                                          display: inline-block;
                                          line-height: 120%;
                                          mso-padding-alt: 0px;
                                          mso-text-raise: 0;
                                        "><p data-id="react-email-text" style="
                                            font-size: 14px;
                                            line-height: 24px;
                                            margin: 0px;
                                            color: rgb(255, 255, 255);
                                          ">
                                          Take Assessment
                                        </p></span><span></span></a>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                            <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation">
                              <tbody>
                                <tr>
                                  <td>
                                    <!-- <p
                                      data-id="react-email-text"
                                      style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 0.25rem;
                                        padding-bottom: 0.25rem;
                                      "
                                    >
                                      Use your "{{ auth_method1 }}" and "{{ auth_method2 }}"" details to login to the
                                      assessment,
                                    </p> -->
                                    <p data-id="react-email-text" style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 0.25rem;
                                        padding-bottom: 0.25rem;
                                      ">
                                      Use the following details to log in to the
                                      assessment:<br>
                                      {{#auth#}}
                                    </p>
                                    <p data-id="react-email-text" style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 0px;
                                        padding-bottom: 0px;
                                      ">
                                      Note that you can only take this
                                      assessment once.
                                    </p>
                                    <p data-id="react-email-text" style="
                                        font-size: 14px;
                                        line-height: 24px;
                                        margin: 0px;
                                        padding-top: 0px;
                                        padding-bottom: 0px;
                                      ">
                                      Good luck !
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>

  <style>
    @import url(&quot;https://fonts.googleapis.com/css2?family=DM+Sans:opsz,wght@9..40,100;9..40,200;9..40,300;9..40,400;9..40,500;9..40,600;9..40,700;9..40,800;9..40,900;9..40,1000&amp;family=Montserrat&amp;display=swap&quot;);

    .email-text {
      font-family: DM Sans, sans-serif;
    }
  </style>

</body></html>
`;
};

export const CUSTOM_TEMPLATE_EMAIL_AUTH_PARTIAL = `<b>"email" - {{ candidate_email }}</b><br>
<b>"passcode" - {{ otp }}</b>`;

export const CUSTOM_TEMPLATE_FILE_AUTH_PARTIAL = `<b>"{{ auth_method1 }}" - {{ auth_method1_value }}</b><br>
<b>"{{ auth_method2 }}" - {{ auth_method2_value }}</b> `;

export const MULTIPLE_CHOICE_OPTIONS_LIMIT = 5;
export const MULTIPLE_CHOICE_OPTIONS_MIN_LIMIT = 2;
