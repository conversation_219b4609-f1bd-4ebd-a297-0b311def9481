import app_fetch from "@/lib/api/appFetch";
import { Assessment, Section } from "@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments";
import { DropResult } from "react-beautiful-dnd";
import reorderSections from "../functions/reorderSections";


export function editSectionAPI(edited_section: Section, assessment_id: string) {
  const requestOptions = {
    method: "PATCH",
    body: JSON.stringify(edited_section),
  };
  return app_fetch(`assessments/assessment-section/${assessment_id}/${edited_section.id}`, requestOptions,)
}

export function deleteSectionAPI(section_id: string, assessment_id: string) {
  const requestOptions = {
    method: "DELETE",
  };
  return app_fetch(`assessments/assessment-section/${assessment_id}/${section_id}`, requestOptions)
}

export async function changeOrder({ source_index, destination_index, assessment }: {assessment:Assessment, source_index:number, destination_index:number }){
  const new_sections = reorderSections({source_index, destination_index, assessment})
  return app_fetch(`assessments/${assessment.id}/reorder-sections/`,{
    method: "PUT",
    body: JSON.stringify({
      new_order: new_sections.reduce((acc, curr)=>{acc.push(curr.id);return acc},[] as string[])
    })
  }).then(res=>res.json())
}
