import app_fetch from "@/lib/api/appFetch";
import { Role } from "@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments";

export async function fetchRolesAPI(): Promise<Role[]> {
  return await app_fetch(`assessments/roles`).then(res => res.json()).then(r => r.data);
}

export async function fetchRoleAPI(role_id: string): Promise<Role> {
  return await app_fetch(`assessments/roles/${role_id}`).then(res => res.json())
}

export async function createRoleAPI(role_name: string): Promise<Role> {
  return await app_fetch(`assessments/roles/`, {
    method: "POST",
    body: JSON.stringify({ name: role_name })
  }).then(async (res) => {
    const response: Role = await res.json()
    if (response.id) {
      return response
    }
    else {
      throw response.name || "something went wrong"
    }
  })
}
