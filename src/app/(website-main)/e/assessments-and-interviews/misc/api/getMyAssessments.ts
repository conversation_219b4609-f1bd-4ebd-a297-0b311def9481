import { useQuery } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';

type TFetchOptions = {
  search: string;
  size: number;
  page: number;
};
const getAll = async (options: TFetchOptions) => {
  const params = new URLSearchParams();
  if (options.page) params.append('page', options.page.toString());
  if (options.size) params.append('size', options.size.toString());
  if (options.search) params.append('search', options.search);

  const res = await Axios.get('/assessments/my-assessments', { params });
  return res.data as TWebAPIResponse;
};

export const useGetAllAssessments = (options: TFetchOptions) => {
  return useQuery({
    queryKey: ['my-assessments-list', options],
    queryFn: () => getAll(options),
    keepPreviousData: true,
  });
};

interface TWebAPIResponse {
  count: number;
  next: null;
  previous: null;
  results: TAssessmentListItem[];
}

export interface TAssessmentListItem {
  id: string;
  name: string;
  no_candidates: number;
  completed: string[];
  ongoing: string[];
  not_started: string[];
  created_at: string;
  deadline: null | string;
  is_published: boolean;
  candidates_data: Candidatesdatum[];
}

interface Candidatesdatum {
  candidate_email: string;
  candidate_name: string;
}
