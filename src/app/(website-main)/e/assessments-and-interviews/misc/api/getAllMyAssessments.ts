import { Axios } from "@/lib/api/axios"
import { useQuery } from "@tanstack/react-query"



const getAll = async () => {
    // const params = new URLSearchParams();
    // if (options.page) params.append('page', options.page.toString());
    // if (options.size) params.append('size', options.size.toString());
    // if (options.search) params.append('search', options.search);

    const res = await Axios.get('/assessments/my-assessments-brief',)
    return res.data?.data as TAssessmentListItem[]
}


export const useGetAllMyAssessments = () => {
    return useQuery({
        queryKey: ['my-assessments-list-brief',],
        queryFn: () => getAll(),
        keepPreviousData: true,
    })
}



export interface TAssessmentListItem {
    id: string;
    name: string;
    role_level: string;
    role_name: string;
}