import { Axios } from "@/lib/api/axios"
import { useQuery } from "@tanstack/react-query"
import { TAssessmentQuestionPack } from "../components/CreateQuestionPackStep3NamedPackModal"

const getDetails = async (id: string) => {
    const response = await Axios.get(`assessments/question-packs/${id}`)
    return response.data as TAssessmentQuestionPack
}

export const useGetQuestionPacks = (id?: string) => {
    return useQuery(
        ['get-question-pack-details', id],
        ({ queryKey }) => getDetails(queryKey[1] || ""), {
        enabled: !!id
    })
}
const getDetails2 = async (id: string) => {
    const response = await Axios.get(`assessments/test/${id}/questions`)
    return response.data as TGetQuestionPackDetails[]
}

export const useGetlinkedPackQuestions = (id?: string) => {
    return useQuery(
        ['get-getlinked-question-pack-details', id],
        ({ queryKey }) => getDetails2(queryKey[1] || ""), {
        enabled: !!id
    })
}


export interface TGetQuestionPackDetails {
    id: string;
    type: string;
    question: string;
    answer_options: string[];
    answer: string[];
    role: number;
    experience_level: string;
    category: null;
    points: number;
    label: null;
    tags: any[];
    test: string;
    section: null;
    is_custom: boolean;
    time: number;
    creator: null | number;
    created_at: string;
    updated_at: string;
    instructions: string;
    image_urls: string[] | any[] | null;
    has_image: boolean;
    is_shuffled: boolean;
}