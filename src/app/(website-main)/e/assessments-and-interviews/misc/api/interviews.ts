import app_fetch from "@/lib/api/appFetch";
import { InterviewAssessmentObject, InterviewAssessment, InterviewCandidateResult, InterviewPack, LibraryAssessment, Question, QuestionPack } from "../types/create-assessments";

export async function fetchMyInterviewQuestionsAPI(): Promise<InterviewPack[]> {
  return await app_fetch("assessments/interview-packs/")
    .then((response) => {
      return response.json()
    });
}


export async function fetchMyInterviews(): Promise<InterviewAssessmentObject[]> {
  return app_fetch('assessments/assessment-interview')
    .then((response) => {
      return response.json()
    });
}

export async function fetchInterviewCandidateResult({ candidate_id }: { candidate_id: string }): Promise<InterviewCandidateResult> {
  const options = {
    method: "GET",
  };
  return await app_fetch(`assessments/individual-candidate-result-detail/${candidate_id}/`, options).then((response) =>
    response.json()
  )
}

