import app_fetch from "@/lib/api/appFetch";
import { LibraryAssessment, Question, QuestionPack } from "../types/create-assessments";
import { Axios } from "@/lib/api/axios";
import { useQuery } from "@tanstack/react-query";

export async function fetchMyQuestionsAPI(): Promise<MyQuestionPacksAPIResponse> {
  return await app_fetch("assessments/my-questions/")
    .then((response) => {
      return response.json()
    });
}

export async function fetchMyQuestionPacksAPI(): Promise<QuestionPack[]> {
  return app_fetch("assessments/question-packs/")
    .then((response) => {
      return response.json()
    });
}

type MyQuestionPacksAPIResponse = {
  count: number;
  next?: string;
  previous?: string;
  results: Question[];
}
type FetchLibraryPacksAPIResponse = {
  count: number;
  next?: string;
  previous?: string;
  results: LibraryAssessment[];
}

export async function fetchLibraryPacksAPI({ pageParam = 'assessments/tests' }): Promise<FetchLibraryPacksAPIResponse> {
  return app_fetch(`${pageParam}`)
    .then((response) => {
      return response.json()
    });
}


type FetchOptions = {
  per_page?: number;
  page?: number;
}


export const fetchLibraryPacks = async ({ per_page, page }: FetchOptions) => {
  const res = await Axios.get(`/assessments/tests/?per_page=${per_page}&page=${page}`);
  return res.data as FetchLibraryPacksAPIResponse;
}

export const fetchLibraryPacksAPI2 = async (options: FetchOptions) => {
  return useQuery({
    queryKey: ['library packs', options],
    queryFn: () => fetchLibraryPacks(options),
  })
}