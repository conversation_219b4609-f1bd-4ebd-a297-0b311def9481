import { Axios } from "@/lib/api/axios"
import { useMutation } from "@tanstack/react-query"

const deletePack = async (id: string) => {
    const response = await Axios.delete(`assessments/question-packs/${id}/`)
    return response.data
}

export const useDeleteQuestionPack = () => {
    return useMutation(
        {
            mutationFn: deletePack,
            mutationKey: ['delete-question-pack-details'],
        }

    )
}