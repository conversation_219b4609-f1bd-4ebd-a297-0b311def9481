import {
  Assessment,
  AssessmentObject,
  AssessmentState,
  Candidate<PERSON><PERSON><PERSON>,
  Comment,
  LibraryAssessment,
  QuestionPack,
} from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import app_fetch from '@/lib/api/appFetch';

export async function fetchCandidateComments({
  candidate_email,
  assessment_id,
}: {
  candidate_email: string;
  assessment_id: string;
}): Promise<Comment[]> {
  const options = {
    method: 'POST',
    body: JSON.stringify({
      candidate_email: candidate_email,
      assessment_id: assessment_id,
    }),
  };

  return new Promise(async (resolve, reject) => {
    await app_fetch('assessments/result-overview/', options)
      .then(response => response.json())
      .then(result => {
        /* fetch comments */
        app_fetch(`assessments/result_overview/${result.id}/comments/`)
          .then(response => response.json())
          .then(result => resolve(result));
      })
      .catch(e => {
        throw e;
      })
      .catch(e => {
        reject(e);
      });
  });
}

export async function fetchCandidateResult({
  candidate_email,
  assessment_id,
}: {
  candidate_email: string;
  assessment_id: string;
}): Promise<CandidateResult> {
  const options = {
    method: 'POST',
    body: JSON.stringify({
      candidate_email: candidate_email,
      assessment_id: assessment_id,
    }),
  };
  return await app_fetch('assessments/result-overview/', options).then(
    response => response.json()
  );
}

export async function editAssessmentAPI(
  assessment: Assessment & AssessmentObject & AssessmentState & any
) {
  let body = {
    ...assessment,

    stop_screen_sharing: assessment.is_stop_screen_sharing,
    invigilate_assessment: assessment.is_invigilate_assessment,

    commencement_settings:
      assessment.commencement_settings &&
      assessment.commencement_settings == 'yes',
  };

  delete body.id;

  function blobToBase64(blob: Blob) {
    return new Promise((resolve, _) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result);
      reader.readAsDataURL(blob);
    });
  }

  async function sendRequest() {
    const options = {
      method: 'PATCH',
      body: JSON.stringify(body),
    };

    await app_fetch(`assessments/assessments/${assessment.id}/`, options).then(
      response => response.json()
    );
  }

  return sendRequest().then(() => {
    if (assessment.intro_video) {
      blobToBase64(assessment.intro_video).then(base_64_vid => {
        body = { intro_vid_url: base_64_vid } as any;
        sendRequest();
      });
    }
  });
}

export function deleteSectionAPI(section_id: string, assessment_id: string) {
  const requestOptions = {
    method: 'DELETE',
  };
  return app_fetch(
    `assessments/assessment-section/${assessment_id}/${section_id}`,
    requestOptions
  );
}

export function publishAssessment(assessment_id: string) {
  const requestOptions = {
    method: 'PATCH',
    body: JSON.stringify({
      is_published: true,
      last_page_url: '',
    }),
  };
  return app_fetch(`assessments/assessments/${assessment_id}/`, requestOptions);
}

export function publishInterview(assessment_id: string) {
  const requestOptions = {
    method: 'PATCH',
    body: JSON.stringify({
      is_published: true,
      last_page_url: '',
    }),
  };
  return app_fetch(`assessments/assessment-interview/${assessment_id}/`, requestOptions);
}

export function updateAssessmentLastEditURL(
  assessment_id: string,
  last_page_url: string
) {
  const requestOptions = {
    method: 'PATCH',
    body: JSON.stringify({
      last_page_url,
    }),
  };
  return app_fetch(`assessments/assessments/${assessment_id}/`, requestOptions);
}

export function addLibrarySetToSection(
  assessment: Assessment,
  library_assessment: LibraryAssessment,
  section_index: number,
  is_empty: boolean
) {
  const section = assessment.sections[section_index];
  if (!is_empty) {
    //@ts-ignore
    const test = assessment.sections[section_index].test.reduce((acc, curr) => {
      //@ts-ignore
      acc.push(curr.id);
      return acc;
    }, [] as string[]);
    test.push(library_assessment.id);
    const raw = JSON.stringify({
      section_name: section.section_name,
      test,
    });

    const requestOptions = {
      method: 'PATCH',
      body: raw,
    };

    return app_fetch(
      `assessments/assessment-section/${assessment.id}/${assessment.sections[section_index].id}`,
      requestOptions
    );
  } else {
    //@ts-ignore
    const raw = JSON.stringify({
      section_name: library_assessment?.name,
      test: [library_assessment.id],
    });

    const requestOptions = {
      method: 'POST',
      body: raw,
    };

    return app_fetch(
      `assessments/assessment-section/${assessment.id}/`,
      requestOptions
    );
  }
}

export function removeLibrarySetFromSection(
  assessment: Assessment,
  library_assessment: LibraryAssessment,
  _section_index: number
) {
  const new_sections: any = [...assessment.sections];

  if (new_sections[_section_index]) {
    // remove test from section test list
    //@ts-ignore
    const _tests = new_sections[_section_index].test.reduce((acc, curr) => {
      acc.push(curr.id);
      return acc;
    }, [] as string[]);

    const _test_index = _tests.indexOf(library_assessment.id);
    _tests.splice(_test_index, 1);

    const payload = {
      section_name: new_sections[_section_index].section_name,
      test: _tests,
    };

    // if section already has stuff
    const raw = JSON.stringify(payload);

    const requestOptions = {
      method: 'PATCH',
      body: raw,
    };

    return app_fetch(
      `assessments/assessment-section/${assessment.id}/${new_sections[_section_index].id}`,
      requestOptions
    );
  }
}

export function addQuestionPackToSectionApi(
  assessment: Assessment,
  _question_pack: QuestionPack,
  section_index: number,
  is_empty: boolean
) {
  const section = assessment.sections[section_index];
  if (!is_empty) {
    //@ts-ignore
    const question_pack = assessment.sections[
      section_index
    ].question_pack.reduce((acc, curr) => {
      //@ts-ignore
      acc.push(curr.id);
      return acc;
    }, [] as string[]);
    question_pack.push(_question_pack.id);
    const raw = JSON.stringify({
      section_name: section.section_name,
      question_pack,
    });

    const requestOptions = {
      method: 'PATCH',
      body: raw,
    };

    return app_fetch(
      `assessments/assessment-section/${assessment.id}/${assessment.sections[section_index].id}`,
      requestOptions
    );
  } else {
    const raw = JSON.stringify({
      section_name: _question_pack?.name,
      question_pack: [_question_pack.id],
    });

    const requestOptions = {
      method: 'POST',
      body: raw,
    };

    return app_fetch(
      `assessments/assessment-section/${assessment.id}/`,
      requestOptions
    );
  }
}

export function removeQuestionPackFromSectionApi(
  assessment: Assessment,
  _question_pack: QuestionPack,
  _section_index: number
) {
  const new_sections: any = [...assessment.sections];

  if (new_sections[_section_index]) {
    // remove question_pack from section question_pack list
    //@ts-ignore
    const _question_packs = new_sections[_section_index].question_pack.reduce(
      //@ts-ignore
      (acc, curr) => {
        acc.push(curr.id);
        return acc;
      },
      [] as string[]
    );

    const _question_pack_index = _question_packs.indexOf(_question_pack.id);
    _question_packs.splice(_question_pack_index, 1);

    const payload = {
      section_name: new_sections[_section_index].section_name,
      question_pack: _question_packs,
    };

    // if section already has stuff
    const raw = JSON.stringify(payload);

    const requestOptions = {
      method: 'PATCH',
      body: raw,
    };

    return app_fetch(
      `assessments/assessment-section/${assessment.id}/${new_sections[_section_index].id}`,
      requestOptions
    );
  }
}

export function isValidDateTime(dateTimeString: string) {
  // Regular expressions for different datetime formats
  const dateTimeFormats = [
    // YYYY-MM-DDTHH:mm
    /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/,
    // YYYY-MM-DDTHH:mm:ss
    /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/,
    // ISO 8601 with milliseconds: YYYY-MM-DDTHH:mm:ss.sss
    /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+$/,
    // With optional timezone
    /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}(?::\d{2})?(?:\.\d+)?(?:Z|[+-]\d{2}:\d{2})?$/,
  ];

  // Check if the format matches
  if (!dateTimeFormats.some(format => format.test(dateTimeString))) {
    return false;
  }

  // Create Date object
  const date = new Date(dateTimeString);

  // Check if date is valid
  if (isNaN(date.getTime())) {
    return false;
  }

  // Parse components
  const [datePart, timePart] = dateTimeString.split('T');
  const [year, month, day] = datePart.split('-').map(Number);
  const timeParts = timePart.split(':').map(Number);

  // Add date range validation (e.g., between 1900 and 2100)
  const MIN_YEAR = 1900;
  const MAX_YEAR = 2100;
  if (year < MIN_YEAR || year > MAX_YEAR) {
    return false;
  }

  // Validate month
  if (month < 1 || month > 12) return false;

  // Validate day
  const daysInMonth = new Date(year, month, 0).getDate();
  if (day < 1 || day > daysInMonth) return false;

  // Validate time components
  const [hours, minutes, seconds] =
    timeParts.length === 3 ? timeParts : [...timeParts, 0];

  if (hours < 0 || hours > 23) return false;
  if (minutes < 0 || minutes > 59) return false;
  if (seconds < 0 || seconds > 59) return false;

  return true;
}
