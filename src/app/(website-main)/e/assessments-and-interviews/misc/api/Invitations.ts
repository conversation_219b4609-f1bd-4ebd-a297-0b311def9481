import app_fetch from "@/lib/api/appFetch";


export function additionalInvitesAPI(invites: Record<string,string>[], invite_id: string, is_interview?:boolean) {
  const requestOptions = {
    method: "POST",
    body: JSON.stringify({candidates:invites})
  };
  const url = is_interview ? `assessments/additional-interview-invite/${invite_id}/` : `assessments/invite-additional-candidates/${invite_id}/`
  return app_fetch(url, requestOptions)
}
