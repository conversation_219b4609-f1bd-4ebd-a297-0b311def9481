import app_fetch from "@/lib/api/appFetch";
import { Question } from "@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments";

export function deleteQuestionAPI(question_id: string, section_id: string) {
  const requestOptions = {
    method: "DELETE",
  };
  return app_fetch(`assessments/assessment-sections/${section_id}/remove-question/${question_id}/`, requestOptions,)
}

export function editQuestionAPI(edited_question: Question, section_id: string, assessment_id: string) {

    const _form_data = new FormData()

    const data = {
        question: edited_question.question,
        answer: edited_question.answer,
        answer_options: edited_question.answer_options,
        points: edited_question.points,
        images: edited_question.images,
    }

    Object.entries(data).map(([key, val])=>{
      if (key==="images"){
        const _val = val as unknown as  File[]
        _val.map((val)=>{
          _form_data.append(key, val, `${val.name}.${val.type.split("/")[1]}`)
        })
        return
      }
      //@ts-ignore
      _form_data.set(key, val)
    })

  return app_fetch(`assessments/${assessment_id}/sections/${section_id}/edit-question/${edited_question.id}/`,
    {
      method: "PATCH",
      body: _form_data
    },
    false
  )
}
