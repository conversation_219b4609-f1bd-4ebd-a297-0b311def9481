import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { fetchLibraryPacks, fetchLibraryPacksAPI, fetchMyQuestionPacksAPI, fetchMyQuestionsAPI } from "../api/tests-library";

export const fetchMyQuestionsQuery = () => {
  return useQuery({
    queryKey: ['my questions'],
    queryFn: fetchMyQuestionsAPI
  })
}

export const fetchMyQuestionPacksQuery = () => {
  return useQuery({
    queryKey: ['my questions packs'],
    queryFn: fetchMyQuestionPacksAPI
  })
}

export const fetchLibraryPacksQuery = ({ page = 1, per_page }: { per_page?: number; page?: number }) => {
  //@ts-ignore
  return useInfiniteQuery({
    queryKey: ['library packs', page],
    queryFn: () => fetchLibraryPacks({ page, per_page }),
    initialPageParam: page,
    getNextPageParam: (lastPage, _pages) => lastPage.next,
    getPreviousPageParam: (lastPage, _pages) => lastPage.previous,
  })
}
