import { useQuery } from "@tanstack/react-query";
import { fetchInterviewCandidateResult, fetchMyInterviewQuestionsAPI, fetchMyInterviews } from "../api/interviews";

export const fetchMyInterviewQuestionsQuery = () => {
  return useQuery({
    queryKey: ['my interview packs'],
    queryFn: fetchMyInterviewQuestionsAPI
  })
}


/* export const fetchLibraryPacksQuery = (page="assessments/tests") => { */
/*   //@ts-ignore */
/*   return useInfiniteQuery({ */
/*     queryKey:  ['library packs', page], */
/*     queryFn: fetchLibraryPacksAPI, */
/*     initialPageParam:page, */
/*     getNextPageParam:(lastPage, _pages) => lastPage.next, */
/*     getPreviousPageParam:(lastPage, _pages) => lastPage.previous, */
/*   }) */
/* } */
/**/
export const fetchMyInterviewsQuery = () => {
  return useQuery({
    queryKey: ['my interview'],
    queryFn: fetchMyInterviews
  })
}


export const getCandidateResultQuery = ({ candidate_id }: { candidate_id: string }) => {
  return useQuery({
    queryKey: ['candidate_result', `candidate_id=${candidate_id}`,],
    queryFn: async () => fetchInterviewCandidateResult({ candidate_id }),
    enabled: !!candidate_id,
  })
}
