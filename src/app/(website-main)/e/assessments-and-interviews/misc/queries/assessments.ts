import { useQuery } from "@tanstack/react-query";
import { fetchCandidateComments, fetchCandidateResult } from "../api/Assessment";

export const getCandidateCommentsQuery = ({ candidate_email, assessment_id }: { candidate_email: string, assessment_id: string }) => {
  return useQuery({
    queryKey: ['candidate_comments', `candidate_email=${candidate_email}`, `assessment_id=${assessment_id}`],
    queryFn: async () => fetchCandidateComments({ candidate_email, assessment_id }),
    enabled: !!candidate_email && !!assessment_id,
  })
}


export const getCandidateResultQuery = ({ candidate_email, assessment_id }: { candidate_email: string, assessment_id: string }) => {
  return useQuery({
    queryKey: ['candidate_result', `candidate_email=${candidate_email}`, `assessment_id=${assessment_id}`],
    queryFn: async () => fetchCandidateResult({ candidate_email, assessment_id }),
    enabled: !!candidate_email && !!assessment_id,
  })
}
