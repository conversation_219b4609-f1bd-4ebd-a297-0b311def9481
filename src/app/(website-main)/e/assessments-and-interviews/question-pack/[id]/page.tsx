'use client'
import React from 'react'
import { use<PERSON>ara<PERSON>, useRouter, useSearchParams } from 'next/navigation'
import { useGetQuestionPacks } from '../../misc/api'
import { Button, EmptyCard, LinkButton } from '@/components/shared'
import { Spinner } from '@/components/shared/icons'
import { useBooleanStateControl } from '@/hooks'
import { Edit } from 'iconsax-react'
import { cn } from '@/utils'
import BulkQuestionUploadModal from '../misc/components/BulkQuestionUploadModal'
import CreateQuestionTypeModal from '../misc/components/CreateQuestionSelectTypeModal'
import EditPackDetailsModal from '../misc/components/EditPackDetailsModal'
import QuestionCard from '../misc/components/QuestionCard'

const page = () => {
    const params = useParams()
    const searchParams = useSearchParams()
    const packId = params.id as string
    const isNewPack = searchParams.get("new") == "true"
    const router = useRouter()
    const { data, isLoading: isFetchingPackDetails, refetch } = useGetQuestionPacks(packId)

    React.useEffect(() => {
        if (!packId) {
            router.push("/assessments-and-interviews/tests-library?tab=my_question_packs")
        }
    }, [packId])

    const [mode, setMode] = React.useState<"read" | "write">("read")

    const {
        state: isBulkQuestionUploadModalOpen,
        setTrue: openBulkQuestionUploadModal,
        setFalse: closeBulkQuestionUploadModal
    } = useBooleanStateControl()
    const {
        state: isEditPackDetailsModalOpen,
        setTrue: openPackDetailsModal,
        setFalse: closeEditPackDetailsModal
    } = useBooleanStateControl()
    const {
        state: isSelectQuestionTypeModalOpen,
        setTrue: openCreateQuestionModal,
        setFalse: closeSelectQuestionTypeModal
    } = useBooleanStateControl()


    const savePack = () => {
        setMode("read")
        if (isNewPack) {
            router.push(`/e/assessments-and-interviews/question-pack/${packId}`)
        }
    }

    return (
        <div>
            {
                isFetchingPackDetails ?
                    <div className="flex items-center justify-center w-full h-full">
                        <Spinner />
                    </div>

                    :
                    !isFetchingPackDetails && !data ?
                        <div className="h-full w-full flex items-center justify-center">

                            <EmptyCard
                                title='This pack does not exist'
                                containerClass='!shrink-0 min-w-[350px]'
                                icon={
                                    <img
                                        src="/images/create-assessments/no-assessment.png"
                                        alt="writing with pen"
                                    />
                                }
                                content={
                                    <div className="py-4 space-y-4">
                                        <p className="text-sm text-[#7D8590]">This pack does not exist</p>
                                        <LinkButton
                                            href="/e/assessments-and-interviews/tests-library"
                                            className="gap-2"
                                            size="small"
                                        >
                                            Back to tests library
                                        </LinkButton>
                                    </div>
                                }
                            />
                        </div>
                        :
                        <>
                            <header className="mt-2 flex max-md:flex-col gap-2 items-center justify-between bg-white px-2 py-4">
                                <div className="flex items-center gap-4">
                                    <LinkButton
                                        href="/e/assessments-and-interviews/tests-library?tab=my_question_packs"
                                        className="btn-primary-light-pill"
                                        size="capsule"
                                        variant="extralight"
                                    >
                                        Back
                                    </LinkButton>
                                    <div>
                                        <div className="flex items-center gap-2">
                                            <h2 className="text-header-text font-semibold">{data?.name}</h2>
                                            <button className="flex items-center gap-1 underline text-xs text-body-text"
                                                title="Edit pack name or description"
                                                onClick={openPackDetailsModal}
                                            >
                                                Edit <Edit width={15} height={15} />
                                            </button>
                                        </div>
                                        <p className="max-w-[45ch] text-xs text-body-text truncate overflow-clip" title={data?.description}> {data?.description} </p>
                                    </div>
                                </div>
                                {
                                    (mode == "write" || isNewPack) ? (
                                        <div className="flex gap-2">
                                            <Button
                                                disabled={false}
                                                className=""
                                                onClick={openBulkQuestionUploadModal}
                                                variant="extralight"
                                                size="small"
                                            >
                                                Bulk Upload Question
                                            </Button>
                                            <Button
                                                className=""
                                                onClick={savePack}
                                                size="small"
                                            >
                                                Save pack
                                            </Button>
                                        </div>
                                    ) : (
                                        <div className="flex gap-2">
                                            <Button
                                                disabled={false}
                                                className=""
                                                onClick={() => {
                                                    setMode("write")
                                                }}
                                                size="thin"
                                            >
                                                Edit
                                            </Button>
                                        </div>
                                    )}
                            </header>
                            <p className="px-6 py-2.5 text-sm text-header-text">
                                {
                                    (mode == "write" || isNewPack) ?
                                        "Tap ‘add question’ to add a new question to the pack"
                                        :
                                        "Click on the edit button to make changes"
                                }
                            </p>
                            <section
                                className={cn("grid gap-3 px-5",
                                    ((data?.question_set.length && data?.question_set.length < 4) || !data?.question_set.length) ?
                                        "grid-cols-[repeat(auto-fit,minmax(250px,300px))] lg:grid-cols-[repeat(auto-fill,minmax(250px,300px))]"
                                        :
                                        "grid-cols-[repeat(auto-fit,minmax(250px,1fr))]",
                                    data?.question_set.length == 0 && mode == "read" && !isNewPack && "w-full h-[80%] flex items-center justify-center"
                                )}
                            >
                                {
                                    data?.question_set.length == 0 && mode == "read" && !isNewPack &&
                                    <EmptyCard
                                        title='This pack currently has no questions'
                                        icon={
                                            <img
                                                src="/images/create-assessments/no-assessment.png"
                                                alt="writing with pen"
                                            />
                                        }
                                        content={
                                            <div className="py-4 space-y-4">
                                                <p className="text-sm text-[#7D8590]">Click on the edit button to add new questions</p>
                                                <Button
                                                    onClick={() => setMode("write")}
                                                    className="gap-2"
                                                    type="button"
                                                    size="small"
                                                >
                                                    Edit Pack
                                                </Button>
                                            </div>
                                        }
                                    />
                                }

                                {
                                    (mode == "write" || isNewPack) &&
                                    <article className="flex flex-col items-center justify-center rounded-xl bg-white p-4 min-h-[150px] border-transparent hover:border-primary border-2 !outline-none">
                                        <p className='text-primary'>
                                            Question {(data?.question_set.length || 0) + 1}
                                        </p>
                                        <Button variant="extralight" size="small" onClick={openCreateQuestionModal}>
                                            Add Question
                                        </Button>
                                    </article>
                                }
                                {
                                    data?.question_set.map((question, index) => {
                                        return (
                                            <QuestionCard
                                                index={index}
                                                key={index}
                                                question={question}
                                                allQuestions={data.question_set}
                                                refetch={refetch}
                                                packId={packId}
                                            />
                                        )
                                    })
                                }
                            </section>
                        </>

            }
            <BulkQuestionUploadModal
                closeBulkQuestionUploadModal={closeBulkQuestionUploadModal}
                refetch={refetch}
                isBulkQuestionUploadModalOpen={isBulkQuestionUploadModalOpen}
                packId={packId}

            />
            <CreateQuestionTypeModal
                closeSelectQuestionTypeModal={closeSelectQuestionTypeModal}
                isSelectQuestionTypeModalOpen={isSelectQuestionTypeModalOpen}
                packId={packId}
                refetch={refetch}
            />
            {
                !isFetchingPackDetails && data &&
                <EditPackDetailsModal
                    isEditPackDetailsModalOpen={isEditPackDetailsModalOpen}
                    closeEditPackDetailsModal={closeEditPackDetailsModal}
                    defaultValues={{ name: data.name, description: data.description }}
                    packId={packId}
                    refetch={refetch}
                />
            }
        </div>
    )
}

export default page