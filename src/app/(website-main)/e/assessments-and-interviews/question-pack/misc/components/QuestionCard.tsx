import React from 'react'
import { Question, QuestionType } from '../../../misc/types/create-assessments'
import { convertToTitleCase } from '@/utils/strings';
import { QUESTION_TYPES_DICTIONARY } from '../../../misc/constants/constants';
import { ConfirmDeleteModalAlt, DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/shared';
import { CloseSquare, Edit, Eye, More } from 'iconsax-react';
import { useBooleanStateControl } from '@/hooks';
import QuestionDetailSheet from './QuestionDetailSeet';
import EditQuestionModal from './EditQuestionModal';
import { Axios } from '@/lib/api/axios';
import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/shared/popover-without-portal';

interface QuestionCardProps {
    index: number
    question: Question
    allQuestions: Question[]
    refetch: () => void
    packId?: string
}

const QuestionCard = ({ allQuestions, question, index, refetch, packId }: QuestionCardProps) => {
    const {
        state: isConfirmDeleteQuestionModalOpen,
        setTrue: openConfirmDeleteQuestionModal,
        setFalse: closeConfirmDeleteQuestionModal
    } = useBooleanStateControl()
    const {
        state: isEditQuestionModalOpen,
        setTrue: openEditQuestionModal,
        setFalse: closeEditQuestionModal
    } = useBooleanStateControl()
    const {
        state: isViewQuestionSheetOpen,
        setTrue: openViewQuestionModal,
        setFalse: closeViewQuestionSheet,
        setState: setViewQuestionSheetState
    } = useBooleanStateControl()


    const { mutate: deleteQuestion, isLoading: isDeleting } = useMutation(
        {
            mutationFn: async () => {
                const res = await Axios.delete(`assessments/question-packs/${packId}/delete-question/${question.id}/`,)
                if (packId) {
                    return res.data
                }
                else return
            }
        }
    )
    function handleQuestionOption() {
        deleteQuestion(undefined, {
            onSuccess: () => {
                refetch()
                closeConfirmDeleteQuestionModal()
                toast.success("Question deleted successfully")
            }
        })
    }

    return (
        <article className="rounded-xl bg-white p-4 min-h-[150px] max-h-[200px] border-transparent hover:border-primary border-2 !outline-none">
            <header className="item-center flex justify-between">
                <h2 className="text-base text-header-text font-medium">Question {index + 1}</h2>
                <Popover>
                    <PopoverTrigger title="view options" >
                        <More className="rotate-90" fill="#D9D9D9" size={18} />
                    </PopoverTrigger>
                    <PopoverContent className="min-w-[100px] max-w-max p-2">
                        <button className="w-full flex items-center gap-3 relative cursor-default select-none rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-primary-light hover:text-primary data-[disabled]:pointer-events-none data-[disabled]:opacity-50" onClick={openViewQuestionModal}>
                            <Eye size={18} /> View
                        </button>
                        {
                            !!packId &&
                            <>
                                <button className="w-full flex items-center gap-3 relative cursor-default select-none rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-primary-light hover:text-primary data-[disabled]:pointer-events-none data-[disabled]:opacity-50" onClick={openEditQuestionModal}>
                                    <Edit size={18} /> Edit
                                </button>
                                <button className="w-full flex items-center gap-3 relative cursor-default select-none rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-primary-light hover:text-primary data-[disabled]:pointer-events-none data-[disabled]:opacity-50" onClick={openConfirmDeleteQuestionModal}>
                                    <CloseSquare size={18} /> Delete
                                </button>
                            </>
                        }
                    </PopoverContent>
                </Popover>

            </header>

            <p className="text-sm mt-3 mb-2 font-normal">
                <span className="text-helper-text">
                    Question type:
                </span>{" "}
                <span className="text-header-text font-medium">
                    {convertToTitleCase(QUESTION_TYPES_DICTIONARY[question?.type]?.name || "")}
                </span>
            </p>

            <div className="text-xs text-body-text leading-snug tiptap"
                dangerouslySetInnerHTML={{ __html: `${question.question?.substring(0, 100)} ${question.question?.length > 100 && "..."}` }}
            />


            <QuestionDetailSheet
                allQuestions={allQuestions}
                index={index}
                question={question}
                isViewQuestionSheetOpen={isViewQuestionSheetOpen}
                closeViewQuestionSheet={closeViewQuestionSheet}
                setViewQuestionSheetState={setViewQuestionSheetState}
            />
            <EditQuestionModal
                packId={packId}
                question={question}
                closeEditQuestionModal={closeEditQuestionModal}
                isEditQuestionModalOpen={isEditQuestionModalOpen}
                refetch={refetch}
                questionType={question.type as "multiple_choice" | "multiple_response" | "fill_in_the_blanks" | "essay" | "true_false"}
            />
            <ConfirmDeleteModalAlt
                deleteFunction={handleQuestionOption}
                isModalOpen={isConfirmDeleteQuestionModalOpen}
                closeModal={closeConfirmDeleteQuestionModal}
                title="Delete question"
                isDeleting={isDeleting}
            >
                <p className="text-sm text-body-text font-normal">
                    You are about to delete this question. This action cannot be undone.
                </p>
            </ConfirmDeleteModalAlt>
        </article>
    )
}

export default QuestionCard