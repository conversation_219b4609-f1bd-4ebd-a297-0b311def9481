import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { DocumentUpload } from 'iconsax-react';
import Link from 'next/link';
import React from 'react';
import { useDropzone } from 'react-dropzone';
import {
  AltButton,
  AltModal,
  Button,
  ErrorModal,
  LoaderBtn,
} from '@/components/shared';
import { SmallSpinner } from '@/components/shared/icons';
import { useErrorModalState } from '@/hooks';
import { Axios } from '@/lib/api/axios';
import { formatBytes } from '@/lib/utils/functions';
import { formatAxiosErrorMessage } from '@/utils/errors';

interface BulkQuestionUploadModalProps {
  closeBulkQuestionUploadModal: () => void;
  isBulkQuestionUploadModalOpen: boolean;
  refetch: () => void;
  packId: string;
}
const BulkQuestionUploadModal: React.FC<BulkQuestionUploadModalProps> = ({
  packId,
  closeBulkQuestionUploadModal,
  isBulkQuestionUploadModalOpen,
  refetch,
}) => {
  const [file_details, setFileDetails] = React.useState(undefined as any);
  function clearFile() {
    setUploadError('');
    setFileDetails(undefined);
  }
  const [upload_error, setUploadError] = React.useState('');
  const onDrop = React.useCallback((acceptedFiles: any) => {
    setFileDetails(acceptedFiles[0]);
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [],
      'application/vnd.ms-excel': [],
    },
    maxSize: 10 * 1024 * 1024, //10mb
  });

  const uploadFn = async (formdata: any) => {
    const response = await Axios.post(
      `assessments/question-packs/${packId}/upload-questions/`,
      formdata
    );
    return response.data;
  };
  const { mutate, isLoading } = useMutation({
    mutationFn: uploadFn,
    mutationKey: ['upload-bulk-questions'],
  });

  function submitBulkUpload() {
    const formdata = new FormData();
    formdata.append('file', file_details);

    mutate(formdata, {
      onSuccess() {
        closeBulkQuestionUploadModal();
        setFileDetails(undefined);
        refetch();
      },
      onError(error) {
        const errorMessage = formatAxiosErrorMessage(error as AxiosError);
        openErrorModalWithMessage(errorMessage);
      },
    });
  }

  const {
    openErrorModalWithMessage,
    closeErrorModal,
    isErrorModalOpen,
    openErrorModal,
    setErrorModalState,
    errorModalMessage,
  } = useErrorModalState();

  return (
    <>
      <AltModal
        is_open={isBulkQuestionUploadModalOpen}
        title="Upload questions"
        close={closeBulkQuestionUploadModal}
      >
        <div className="w-[490px] space-y-4 p-4 text-sm">
          <h2 className="text-lg text-header-text">
            Bulk upload custom questions
          </h2>
          <p className="helper-text ">
            Please download the{' '}
            <span className="font-bold text-black "> 'sample file' </span>
            provided below and populate the{' '}
            <span className="font-bold text-black ">EXCEL</span> file by
            entering the necessary information in the required fields. Once
            completed, upload the file to have your custom questions on
            getlinked.
          </p>
          <div className="flex gap-2">
            <a
              className="btn-primary-light"
              href="/files/assessment-and-interviews/getlinked_sample_questions_template.xlsx"
              target="_blank"
            >
              Download sample file
            </a>
            {/* <Link href="#" className="btn-primary-transparent">
                            Learn more
                        </Link> */}
          </div>

          {!file_details && (
            <div
              className={`${
                false ? 'border border-red-600' : ''
              } mt-3 flex h-[5.9375rem] max-w-[90%] cursor-pointer items-center gap-4 rounded-[0.875rem] border-[0.3px] border-dashed border-[rgb(117,90,226)] p-6`}
              {...getRootProps()}
            >
              <div className="">
                <DocumentUpload className="text-primary" />
              </div>
              <div className="">
                <p className="font-sans text-xs font-semibold text-[#755AE2] sm:text-sm">
                  Tap to upload bulk questions
                </p>
                <span className="font-sans text-xs font-semibold text-[#755AE2] opacity-75">
                  Files types: excel, Max size: 10MB
                </span>
              </div>
              <input hidden {...getInputProps()} />
            </div>
          )}

          {file_details && (
            <>
              <div className="bg-light-accent-bg w-full rounded-md p-4 text-xs">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <div>
                      <img src="/images/icons/excel.png" alt="excel icon" />
                    </div>
                    <div>
                      <p className="font-bold">{file_details?.name}</p>
                      <p className="text-[#4E4E4E]">
                        {formatBytes(file_details?.size)}
                      </p>
                    </div>
                  </div>
                  <button onClick={clearFile}>
                    <span className="flex aspect-square w-7 items-center justify-center rounded-full bg-white text-lg">
                      &times;
                    </span>
                  </button>
                </div>
              </div>
              {upload_error && <p className="text-red-500">{upload_error}</p>}
            </>
          )}
          <Button
            disabled={!file_details}
            onClick={submitBulkUpload}
            className="mt-12 w-full"
            type="button"
          >
            Upload file
            {isLoading && <LoaderBtn />}
          </Button>
        </div>
      </AltModal>

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={errorModalMessage}
        heading="Something went wrong"
      >
        <Button variant="red" onClick={closeErrorModal}>
          Okay
        </Button>
      </ErrorModal>
    </>
  );
};

export default BulkQuestionUploadModal;
