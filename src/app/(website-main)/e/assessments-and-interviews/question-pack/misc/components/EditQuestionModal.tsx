"use client"

import React, { use<PERSON><PERSON>back, useEffect, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { z } from 'zod'
import { FieldErrors, useForm, UseFormRegister, UseFormSetValue, UseFormWatch } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import ListBox from "@/components/shared/listbox"
import ListBoxStrings from "@/components/shared/listbox-strings"
import {
    MULTIPLE_CHOICE_OPTIONS_LIMIT,
    MULTIPLE_CHOICE_OPTIONS_MIN_LIMIT,
    ALPHABETS,
    TRUE_OR_FALSE
} from '../../../misc/constants/constants'
import { CloseCircle, DocumentUpload } from 'iconsax-react'
import { AltModal, Button, Checkbox2, ErrorModal, Input, LoaderBtn, RichTextEditor } from '@/components/shared'
import { useMutation } from '@tanstack/react-query'
import { Axios } from '@/lib/api/axios'
import { Question } from '../../../misc/types/create-assessments'
import { questionTypeEnums } from '@/app/(assessments)/company-test/dummydata'
import toast from 'react-hot-toast'
import { useErrorModalState } from '@/hooks'
import { formatAxiosErrorMessage } from '@/utils/errors'
import { TAssessmentQuestionset } from '../../../create-assessment/misc/types'

const baseQuestionSchema = z.object({
    section: z.string().optional().nullable(),
    question: z.string().min(1, "Question is required"),
    points: z.number().min(0, "Points must be a positive number"),
    images: z.array(z.instanceof(Blob)).optional(),
    is_shuffled: z.boolean().optional(),
})

const multipleChoiceSchema = baseQuestionSchema.extend({
    type: z.literal('multiple_choice'),
    answer_options: z.array(z.string().min(1, "Enter answer option")).min(2).max(MULTIPLE_CHOICE_OPTIONS_LIMIT),
    answer: z.array(z.string()).length(1),
})
const multipleResponseSchema = baseQuestionSchema.extend({
    type: z.literal('multiple_response'),
    answer_options: z.array(z.string().min(1, "Enter answer option")).min(2).max(MULTIPLE_CHOICE_OPTIONS_LIMIT),
    answer: z.array(z.string()).min(1),
})
const fillInTheBlanksSchema = baseQuestionSchema.extend({
    type: z.literal('fill_in_the_blanks'),
    answer_options: z.array(z.string().min(1, "Enter answer option")).min(1),
    answer: z.array(z.string()).length(1),
})
const essaySchema = baseQuestionSchema.extend({
    type: z.literal('essay'),
    instructions: z.string(),
})
const trueFalseSchema = baseQuestionSchema.extend({
    type: z.literal('true_false'),
    answer: z.array(z.enum(['true', 'false'])).length(1),
})
const nullSchema = baseQuestionSchema.extend({
    type: z.literal(null),
})
type QuestionType = z.infer<typeof multipleChoiceSchema> | z.infer<typeof multipleResponseSchema> | z.infer<typeof fillInTheBlanksSchema> | z.infer<typeof essaySchema> | z.infer<typeof trueFalseSchema>

const HOW_CANDIDATES_WILL_ANSWER = {
    multiple_choice: 'selecting one option',
    multiple_response: 'selecting multiple options',
    fill_in_the_blanks: 'selecting an answer from a provided list of choices',
    essay: 'typing in an input field',
    true_false: 'Selecting true or false'
}


interface EditQuestionModalProps {
    questionType: "multiple_choice" | "multiple_response" | "fill_in_the_blanks" | "essay" | "true_false"
    isEditQuestionModalOpen: boolean
    closeEditQuestionModal: () => void
    packId?: string,
    assessmentId?: string,
    sectionId?: string,
    refetch?: () => void;
    question: Question | TAssessmentQuestionset
    onSave?: (question: Question) => void
}

export default function EditQuestionModal({ question, packId, assessmentId, questionType, isEditQuestionModalOpen, closeEditQuestionModal, refetch, onSave, sectionId }: EditQuestionModalProps) {
    const [imageUrls, setImageUrls] = useState<string[]>(question.image_urls || question.images || [])
    const [imageBlobs, setImageBlobs] = useState<Blob[]>(
        (question.image_urls || question.images)?.map((url) => new Blob([url], { type: url.split("/")[1] })) || [],
    )
    const [imageFiles, setImageFiles] = useState<File[]>(
        (question.image_urls || question.images)?.map((url) => new File([url], `${url.split("/")[1]}`)) || [],
    )

    useEffect(() => {
        setImageUrls(question.image_urls || question.images || [])
        setImageBlobs((question.image_urls || question.images)?.map((url) => new Blob([url], { type: url.split("/")[1] })) || [],)
        setImageFiles((question.image_urls || question.images)?.map((url) => new File([url], `${url.split("/")[1]}`)) || [])
    }, [question.image_urls, question.images])


    const schema = (() => {
        switch (questionType || null) {
            case 'multiple_choice':
                return multipleChoiceSchema
            case 'multiple_response':
                return multipleResponseSchema
            case 'fill_in_the_blanks':
                return fillInTheBlanksSchema
            case 'essay':
                return essaySchema
            case 'true_false':
                return trueFalseSchema
            case null:
                return nullSchema
            default:
                throw new Error(`Unsupported question type: ${questionType}`)
        }
    })()

    React.useEffect(() => {
        setValue('type', questionType)
    }, [questionType])

    const { register, handleSubmit, watch, setValue, formState: { errors }, getValues, trigger, reset } = useForm<QuestionType>({
        resolver: zodResolver(schema),
        defaultValues: {
            section: question.section,
            question: question.question,
            points: question.points,
            images: question.image_urls
                ? question.images?.map((url) => new Blob([url], { type: url.split("/")[1] }))
                : undefined,
            answer_options: question.answer_options || [],
            answer:
                question.type === "multiple_response"
                    ? question?.answer
                    // ?.map((ans) => {
                    //     const index = question?.answer_options?.indexOf(ans)
                    //     return index !== -1 ? index.toString() : ""
                    // })
                    // .filter((ans) => ans !== "")
                    : question.type === "essay"
                        ? undefined
                        : question?.answer,
            is_shuffled: question.is_custom || question.is_shuffled || false,
            instructions: questionType === "essay" ? question.instructions : undefined,
        },
        mode: 'onChange'
    })
    const closeAndReset = () => {
        closeEditQuestionModal()

    }
    const blobToBase64 = (blob: Blob): Promise<string> => {
        return new Promise((resolve) => {
            const reader = new FileReader()
            reader.onloadend = () => resolve(reader.result as string)
            reader.readAsDataURL(blob)
        })
    }

    const onDrop = useCallback(async (acceptedFiles: File[]) => {
        const newImageUrls = [...imageUrls]
        const newImageBlobs = [...imageBlobs]
        const newImageFiles = [...imageFiles]

        for (const file of acceptedFiles) {
            newImageFiles.push(file)
            const blob = new Blob([file], { type: file.type })
            const imageBase64 = await blobToBase64(blob)
            newImageUrls.push(imageBase64)
            newImageBlobs.push(blob)
        }

        setImageUrls(newImageUrls)
        setImageBlobs(newImageBlobs)
        setImageFiles(newImageFiles)
    }, [imageUrls, imageBlobs, imageFiles])

    const { getRootProps, getInputProps } = useDropzone({
        onDrop,
        multiple: true,
        accept: {
            'image/*': []
        },
        maxSize: 10 * 1024 * 1024 // 10MB
    })

    const deleteImage = (index: number) => {
        setImageUrls(prev => prev.filter((_, i) => i !== index))
        setImageBlobs(prev => prev.filter((_, i) => i !== index))
        setImageFiles(prev => prev.filter((_, i) => i !== index))
    }


    const { mutate: createQuestion, isLoading: isCreatingQuestion } = useMutation(async (data: QuestionType) => {
        const formData = new FormData()
        Object.entries(data).forEach(([key, val]) => {
            if (key !== "section" && key !== "images") {
                if (key === "answer_options") {
                    (val as string[]).forEach((option, index) => {
                        formData.append(`${key}[${index}]`, option)
                    })
                } else if (key === "answer") {
                    if (questionType === "multiple_response") {
                        // Just append the selected answers directly
                        (val as string[]).forEach((selectedAnswer, index) => {
                            formData.append(`${key}[${index}]`, selectedAnswer)
                        })
                    } else {
                        formData.append(key, val as string)
                    }
                } else if (key == "is_shuffled") {
                    formData.append(key, val ? "True" : "False")
                } else {
                    formData.append(key, val as string)
                }
            }
        })

        if (imageFiles.length > 0) {
            imageFiles.forEach((file, index) => {
                if (!!file) {
                    formData.append(`images[${index}]`, file)
                }
            })
        } else {
            formData.delete('images')
        }


        const endpoint = assessmentId
            ? `assessments/${assessmentId}/sections/${sectionId || data.section}/edit-question/${question.id}/`
            : `assessments/question-packs/${packId}/edit-question/${question.id}/`
        const response = await Axios.patch(endpoint, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
        return response.data
    })

    useEffect(() => {
        if (isEditQuestionModalOpen) {
            reset({
                section: question.section,
                question: question.question,
                points: question.points,
                images: question.image_urls
                    ? question.images?.map((url) => new Blob([url], { type: url.split("/")[1] }))
                    : undefined,
                answer_options: question.answer_options || [],
                answer:
                    question.type === "multiple_response"
                        ? question?.answer
                        // ?.map((ans) => {
                        //     const index = question?.answer_options?.indexOf(ans)
                        //     return index !== -1 ? index.toString() : ""
                        // })
                        // .filter((ans) => ans !== "")
                        : question.type === "essay"
                            ? undefined
                            : question?.answer,
                is_shuffled: question.is_custom || question.is_shuffled || false,
                instructions: questionType === "essay" ? question.instructions : undefined,
            })
        }
    }, [question, isEditQuestionModalOpen])


    const {
        closeErrorModal,
        openErrorModalWithMessage,
        isErrorModalOpen,
        errorModalMessage,
        setErrorModalState,

    } = useErrorModalState()

    const onSubmit = async (data: QuestionType) => {
        console.log("submission data", data)
    }


    const handleEditQuestion = async () => {
        console.log("handleEditQuestion called")
        const data = getValues() as QuestionType
        const isValid = await trigger()
        createQuestion(data, {
            onSuccess(data: any) {
                if (!!refetch) {
                    refetch()
                }
                else if (onSave) {
                    onSave(data)
                }
                reset({
                    section: data.section,
                    question: data.question,
                    points: data.points,
                    images: data.image_urls
                        ? data.images?.map((url: any) => new Blob([url], { type: url.split("/")[1] }))
                        : undefined,
                    answer_options: data.answer_options || [],
                    answer:
                        data.type === "multiple_response"
                            ? data?.answer
                            // ?.map((ans) => {
                            //     const index = question?.answer_options?.indexOf(ans)
                            //     return index !== -1 ? index.toString() : ""
                            // })
                            // .filter((ans) => ans !== "")
                            : data.type === "essay"
                                ? undefined
                                : data?.answer,
                    is_shuffled: data.is_custom || data.is_shuffled || false,
                    instructions: questionType === "essay" ? data.instructions : undefined,
                })
                closeEditQuestionModal()
                toast.success("Question updated successfully")
            },
            onError(error: any) {
                const errorMessage = formatAxiosErrorMessage(error) || error.response?.data?.detail || "An error occurred"
                openErrorModalWithMessage(errorMessage)
            }
        })
    }


    return (
        <>
            <AltModal
                title="Edit Question"
                is_open={isEditQuestionModalOpen}
                close={closeAndReset}
                portalClass='w-[90%] max-w-[1050px]'
                is_close_by_button_only={true}

            >
                <form
                    onSubmit={handleSubmit(onSubmit)} className="space-y-6 p-4 lg:p-8"
                    id="edit-assessment-question-form"
                >
                    {/* Image Upload Section */}
                    <div className=" space-y-2">
                        <h2 className="text-lg font-medium text-primary">Images</h2>
                        <div className="flex gap-2 flex-wrap">
                            {imageUrls.map((url, index) => (
                                <div key={index} className="relative">
                                    <img
                                        src={url}
                                        alt={`Question image ${index + 1}`}
                                        className="w-24 h-24 object-cover rounded-lg"
                                    />
                                    <button
                                        type="button"
                                        onClick={() => deleteImage(index)}
                                        className="absolute -top-2 -right-2 p-1 bg-white rounded-full shadow-lg"
                                    >
                                        <CloseCircle className="text-red-300" />
                                    </button>
                                </div>
                            ))}

                            <div
                                {...getRootProps()}
                                className="border-2 border-dashed border-primary/20 rounded-lg p-4 cursor-pointer hover:border-primary/40 transition-colors"
                            >
                                <input {...getInputProps()} />
                                <div className="flex items-center gap-2">
                                    <DocumentUpload />
                                    <div>
                                        <p className="text-sm font-medium">Upload images</p>
                                        <p className="text-xs text-muted-foreground">Drag & drop or click to select</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Question Form */}
                    <div className="grid md:grid-cols-2 gap-6 lg:gap-10">
                        <div className="space-y-4">
                            <h2 className="text-lg font-medium text-primary">
                                Edit {questionType?.replace('_', ' ')} question
                            </h2>

                            <div>
                                <p className="text-xs p-4">
                                    Candidates will be asked to respond to a question in by {HOW_CANDIDATES_WILL_ANSWER[questionType!]} {"\n"}
                                    {
                                        questionType === 'fill_in_the_blanks' &&
                                        "You can create the blanks by typing [blank] in the question and underlining it"
                                    }
                                </p>
                                <RichTextEditor
                                    placeholder="Enter your question here"
                                    className="min-h-[200px] bg-[#FAFAFA] "
                                    hasError={!!errors.question}
                                    errorMessage={errors.question?.message}
                                    initialContent={question.question}
                                    onChange={(content) => setValue('question', content)}
                                />
                            </div>
                        </div>

                        <div className="space-y-4">
                            {(questionType === 'multiple_choice') && (
                                <MultipleChoiceAnswers
                                    register={register}
                                    watch={watch}
                                    setValue={setValue}
                                    errors={errors}
                                    question={question}
                                />
                            )}
                            {(questionType === 'multiple_response') && (
                                <MultipleResponseAnswers
                                    register={register}
                                    watch={watch}
                                    setValue={setValue}
                                    errors={errors}
                                    question={question}
                                />
                            )}
                            {(questionType === 'essay') && (
                                <EssayAnswers
                                    register={register}
                                    watch={watch}
                                    setValue={setValue}
                                    errors={errors}
                                    question={question}
                                />
                            )}
                            {(questionType === 'fill_in_the_blanks') && (
                                <FillInTheBlanksAnswer
                                    register={register}
                                    watch={watch}
                                    setValue={setValue}
                                    errors={errors}
                                />
                            )}
                            {(questionType === 'true_false') && (
                                <TrueOrFalseAnswers
                                    register={register}
                                    watch={watch}
                                    setValue={setValue}
                                    errors={errors}
                                />
                            )}



                            <div>
                                <label className="text-sm font-medium">How do you want to score this question?</label>
                                <Input
                                    type="number"
                                    {...register('points', { valueAsNumber: true })}
                                    className="mt-1"
                                    variant="showcase"
                                    hasError={!!errors.points}
                                    errorMessage={errors.points?.message}
                                />
                            </div>
                            <Button
                                // type="submit"
                                disabled={isCreatingQuestion}
                                onClick={handleEditQuestion}
                                className="w-full"
                                form="edit-assessment-question-form"
                            >
                                Save Question
                                {isCreatingQuestion && <LoaderBtn />}
                            </Button>

                        </div>
                    </div>
                </form>
            </AltModal>

            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={errorModalMessage}
                heading="Something went wrong"
            >
                <div className="w-full p-6 flex items-center justify-center bg-danger-light-active rounded-xl ">
                    <Button variant="red" className="w-full" onClick={closeErrorModal}>
                        Okay
                    </Button>
                </div>
            </ErrorModal>
        </>
    )
}

interface AnswersProps {
    register: UseFormRegister<QuestionType>
    watch: UseFormWatch<QuestionType>
    setValue: UseFormSetValue<QuestionType>
    errors: FieldErrors<QuestionType>
    question?: Question | TAssessmentQuestionset

}
function MultipleChoiceAnswers({ register, watch, setValue, errors, question }: AnswersProps) {
    const answerOptions = watch('answer_options') || []

    return (
        <div className="space-y-4">
            <h3 className="text-lg font-medium">Answer Options</h3>
            {
                answerOptions.map((_, index) => (
                    <div key={index} className="flex items-center gap-2">
                        <span>{ALPHABETS[index]})</span>
                        <Input
                            {...register(`answer_options.${index}`)}
                            placeholder={`Option ${index + 1}`}
                            variant="showcase"
                            hasError={!!(errors as any).answer_options?.[index]}
                            errorMessage={(errors as any).answer_options?.[index]?.message}
                            containerClassName='grow'
                        />
                        {answerOptions.length > MULTIPLE_CHOICE_OPTIONS_MIN_LIMIT && (
                            <button
                                type="button"
                                onClick={() => {
                                    const newOptions = answerOptions.filter((_, i) => i !== index)
                                    setValue('answer_options', newOptions)
                                }}
                            >
                                <CloseCircle />
                            </button>
                        )}
                    </div>
                ))}

            {answerOptions.length < MULTIPLE_CHOICE_OPTIONS_LIMIT && (
                <Button
                    type="button"
                    variant="outlined"
                    onClick={() => setValue('answer_options', [...answerOptions, ''])}
                >
                    Add Option
                </Button>
            )}
            <div>
                <label className="text-sm font-medium">Correct Answer</label>
                <ListBoxStrings
                    options={answerOptions.filter((option) => option.trim() !== "")}
                    active_option={question?.answer[0] || watch('answer')[0]}
                    setActiveOption={(value) => setValue('answer', [value])}
                    className="mt-1 bg-[#FAFAFA] h-[2.875rem] py-[0.8125rem] px-4 text-[#818181] text-sm !outline-none border-[1.8px] border-transparent focus:border-primary"
                />
            </div>
            <div className="flex items-center gap-2">
                <Checkbox2
                    {...register('is_shuffled')}
                    checked={watch('is_shuffled')}
                    onCheckedChange={() => setValue('is_shuffled', !watch('is_shuffled'))}
                    id="shuffle"
                />
                <label htmlFor="shuffle" className="text-sm">
                    Shuffle options
                </label>
            </div>
        </div>
    )
}

function FillInTheBlanksAnswer({ register, watch, setValue, errors }: AnswersProps) {
    const answerOptions = watch('answer_options') || []

    return (
        <div className="space-y-4">
            <h3 className="text-lg font-medium">Answer Options</h3>
            {
                answerOptions.map((_, index) => (
                    <div key={index} className="flex items-center gap-2">
                        <span>{ALPHABETS[index]})</span>
                        <Input
                            {...register(`answer_options.${index}`)}
                            placeholder={`Option ${index + 1}`}
                            variant="showcase"
                            hasError={!!(errors as any).answer_options?.[index]}
                            errorMessage={(errors as any).answer_options?.[index]?.message}
                            containerClassName='grow'
                        />
                        {answerOptions.length > MULTIPLE_CHOICE_OPTIONS_MIN_LIMIT && (
                            <button
                                type="button"
                                onClick={() => {
                                    const newOptions = answerOptions.filter((_, i) => i !== index)
                                    setValue('answer_options', newOptions)
                                }}
                            >
                                <CloseCircle />
                            </button>
                        )}
                    </div>
                ))}

            {answerOptions.length < MULTIPLE_CHOICE_OPTIONS_LIMIT && (
                <Button
                    type="button"
                    variant="outlined"
                    onClick={() => setValue('answer_options', [...answerOptions, ''])}
                >
                    Add Option
                </Button>
            )}
            <div>
                <label className="text-sm font-medium">Correct Answer</label>
                <ListBoxStrings
                    options={answerOptions.filter((option) => option.trim() !== "")}
                    active_option={watch('answer')[0]}
                    setActiveOption={(value) => setValue('answer', [value])}
                    className="mt-1 bg-[#FAFAFA] h-[2.875rem] py-[0.8125rem] px-4 text-[#818181] text-sm !outline-none border-[1.8px] border-transparent focus:border-primary"
                />
            </div>
            <div className="flex items-center gap-2">
                <Checkbox2
                    {...register('is_shuffled')}
                    checked={watch('is_shuffled')}
                    onCheckedChange={() => setValue('is_shuffled', !watch('is_shuffled'))}
                    id="shuffle"
                />
                <label htmlFor="shuffle" className="text-sm">
                    Shuffle options
                </label>
            </div>
        </div>
    )
}

function MultipleResponseAnswers({ register, watch, setValue, errors, question }: AnswersProps) {
    const answerOptions = watch('answer_options') || []
    const selectedAnswers = watch('answer') as string[] || question?.answer as string[]

    const removeOptionAndUpdateAnswer = (index: number) => {
        const optionToRemove = answerOptions[index]
        const newOptions = answerOptions.filter((_, i) => i !== index)
        setValue('answer_options', newOptions)

        // Remove the actual option value from selected answers
        setValue('answer', selectedAnswers.filter(answer => answer !== optionToRemove))
    }

    return (
        <div className="space-y-4">
            <div>
                <h3 className="text-lg font-medium">Answer Options</h3>
                <p className="text-xs py-4">Check the correct options(s)</p>
            </div>
            {
                answerOptions.map((option, index) => (
                    <div key={index} className="flex items-center gap-2">
                        <span>{ALPHABETS[index]})</span>
                        <Input
                            {...register(`answer_options.${index}`)}
                            placeholder={`Option ${index + 1}`}
                            variant="showcase"
                            hasError={!!(errors as any).answer_options?.[index]}
                            errorMessage={(errors as any).answer_options?.[index]?.message}
                            containerClassName='grow'
                        />
                        {answerOptions.length > MULTIPLE_CHOICE_OPTIONS_MIN_LIMIT && (
                            <button
                                type="button"
                                onClick={() => removeOptionAndUpdateAnswer(index)}
                            >
                                <CloseCircle />
                            </button>
                        )}
                        <Checkbox2
                            checked={selectedAnswers.includes(option)}
                            onCheckedChange={(checked) => {
                                const currentAnswers = [...selectedAnswers];
                                if (checked) {
                                    // Add the actual option value
                                    currentAnswers.push(option);
                                } else {
                                    // Remove the actual option value
                                    const indexToRemove = currentAnswers.indexOf(option);
                                    if (indexToRemove > -1) {
                                        currentAnswers.splice(indexToRemove, 1);
                                    }
                                }
                                setValue('answer', currentAnswers);
                            }}
                            id={`answer-${index}`}
                        />
                    </div>
                ))
            }
            <section className="flex items-center justify-between">
                {answerOptions.length < MULTIPLE_CHOICE_OPTIONS_LIMIT && (
                    <Button
                        type="button"
                        variant="outlined"
                        onClick={() => setValue('answer_options', [...answerOptions, ''])}
                    >
                        Add Option
                    </Button>
                )}
                <div className="flex items-center gap-2">
                    <Checkbox2
                        {...register('is_shuffled')}
                        checked={watch('is_shuffled')}
                        onCheckedChange={() => setValue('is_shuffled', !watch('is_shuffled'))}
                        id="shuffle"
                    />
                    <label htmlFor="shuffle" className="text-sm">
                        Shuffle options
                    </label>
                </div>
            </section>
        </div>
    )
}

function TrueOrFalseAnswers({ register, watch, setValue, errors }: AnswersProps) {
    const answerOptions = watch('answer_options') || []

    return (
        <div className="space-y-4">
            <h3 className="text-lg font-medium">Answer Options</h3>
            {
                TRUE_OR_FALSE.map((option, index) => (
                    <div key={index} className="flex items-center gap-2">
                        <span>{ALPHABETS[index]})</span>
                        <Input
                            {...register(`answer_options.${index}`)}
                            value={option.value}
                            placeholder={`Option ${index + 1}`}
                            variant="showcase"
                            hasError={!!(errors as any).answer_options?.[index]}
                            errorMessage={(errors as any).answer_options?.[index]?.message}
                            containerClassName='grow'
                            readOnly
                        />
                    </div>
                ))
            }


            <div>
                <label className="text-sm font-medium">Correct Answer</label>
                <ListBoxStrings
                    options={TRUE_OR_FALSE.filter((option) => option.value?.trim() !== "").map(option => option.value)}
                    active_option={!!watch('answer') ? watch('answer')[0] : undefined}
                    setActiveOption={(value) => setValue('answer', [value])}
                    className="mt-1 bg-[#FAFAFA] h-[2.875rem] py-[0.8125rem] px-4 text-[#818181] text-sm !outline-none border-[1.8px] border-transparent focus:border-primary"
                />
            </div>
        </div>
    )
}



function EssayAnswers({ setValue, errors, question, watch }: AnswersProps) {

    return (
        <div className="space-y-4">
            <h3 className="text-base font-medium">Instructions</h3>
            <RichTextEditor
                className="min-h-[200px] bg-[#FAFAFA] "
                onChange={(content) => setValue('instructions', content)}
                placeholder={`Enter grading instructions e.g Please focus on the following points while grading:\n - Clarity of the answer\n - Use of relevant examples\n - Logical flow and structure\n - Grammar and spelling `}
                hasError={!!(errors as any).instructions}
                errorMessage={(errors as any).instructions?.message}
                initialContent={question?.instructions}
            />
        </div>
    )
}