import { Axios } from "@/lib/api/axios"
import { useQuery } from "@tanstack/react-query"

const queryFn = async () => {
    const res = await Axios.get("assessments/question-sections/?size=70")
    return res.data as RootObject
}

export const useGetQuestionsSections = () => {
    return useQuery({
        queryKey: ['question-sections'],
        queryFn
    })
}



interface RootObject {
  count: number;
  next: string;
  previous: null;
  results: Result[];
}

interface Result {
  id: string;
  name: string;
}