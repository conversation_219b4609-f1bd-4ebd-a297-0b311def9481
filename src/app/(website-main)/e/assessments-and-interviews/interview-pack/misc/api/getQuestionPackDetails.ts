import { Axios } from "@/lib/api/axios"
import { useQuery } from "@tanstack/react-query"
import { TInterviewQuestion, TInterviewQuestionPack } from "../../../create-interview/misc/types"

const getDetails = async (id: string) => {
    const response = await Axios.get(`assessments/interview-packs/${id}`)
    return response.data as TInterviewQuestionPack
}

export const useGetInterviewPacks = (id?: string) => {
    return useQuery(
        ['get-questinterviewion-pack-details', id],
        ({ queryKey }) => getDetails(queryKey[1] || ""), {
        enabled: !!id
    })
}

