import React from 'react'
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import toast from 'react-hot-toast';

import { <PERSON><PERSON>, <PERSON><PERSON>, SelectSingleCombo, Textarea } from '@/components/shared';
import { zodResolver } from '@hookform/resolvers/zod';
import { Axios } from '@/lib/api/axios';
import { formatAxiosErrorMessage } from '@/utils/errors';

import { TimeLimitInput } from '../../../create-assessment/misc/components/TimeLimitInput';
import { TInterviewQuestion } from '../../../create-interview/misc/types';
import { useGetQuestionsSections } from '../api';


interface CreateInterviewQuestionModalProps {
    isModalOpen: boolean
    closeModal: () => void
    refetch?: () => void
    initial_question?: TInterviewQuestion
    packId: string
}

const schema = z.object({
    question: z.string({ message: "Enter question" }).min(3, { message: "Enter at least 3 characters" }),
    section: z.string({ message: "Select a section" }),
    time: z.number({ message: "Enter time" }).min(1, { message: "Enter at least 1 secs" })
})
type formType = z.infer<typeof schema>

const CreateInterviewQuestionModal: React.FC<CreateInterviewQuestionModalProps> = ({ isModalOpen, closeModal, refetch, packId, initial_question }) => {
    const { register, formState: { errors }, handleSubmit, setValue, watch, reset } = useForm<formType>({
        resolver: zodResolver(schema),
        defaultValues: {
            question: initial_question ? initial_question.question : '',
            time: initial_question ? (initial_question.time_limit || initial_question.time) : 300
        },
        mode: 'onChange'
    })
    const { data: questionSections, isLoading: isGettingSections } = useGetQuestionsSections()


    const submitForm = async (data: any) => {
        if (!initial_question) {
            try {
                await Axios.post(
                    `/assessments/interview-packs/${packId}/create-question/`,
                    data
                );
                toast.success('Question added successfully')
                closeModal()
                reset()
                if (refetch) {
                    refetch()
                }
            } catch (error) {
                const errorMessage = formatAxiosErrorMessage(error as unknown as any)
                toast.error(errorMessage)
            }

        }
        else {
            try {
                await Axios.patch(
                    // `/assessments/interview-packs/${packId}/update-question/${initial_question.id}/`,
                    `assessments/interview-packs/${packId}/edit-question/${initial_question.id}/`,
                    data
                );
                toast.success('Question updated successfully')
                closeModal()
                reset()
                setValue('time', 300)
                setValue('question', '')

                if (refetch) {
                    refetch()
                }
            } catch (error) {
                const errorMessage = formatAxiosErrorMessage(error as unknown as any)
                toast.error(errorMessage)
            }
        }
    }
    console.log(errors)


    return (
        <Modal isModalOpen={isModalOpen} closeModal={closeModal} heading={`${initial_question ? 'Edit' : 'Add a new'} question`}>
            <form className="default-text-styles" onSubmit={handleSubmit(submitForm)}>
                <div className="w-full p-4 text-sm">
                    <div className="space-y-6">
                        <SelectSingleCombo
                            name='section'
                            placeholder="Select a category"
                            label="Question category"
                            options={questionSections?.results}
                            value={watch('section')}
                            labelKey="name"
                            valueKey="id"
                            variant="showcase"
                            isLoadingOptions={isGettingSections}
                            onChange={(val) => setValue('section', val)}
                        />


                        <Textarea
                            label='Question'
                            id="question"
                            placeholder="Enter question"
                            {...register('question')}
                            hasError={!!errors.question}
                            minRows={10}
                            variant="showcase"
                            errorMessage={errors.question?.message}
                        />
                    </div>

                    <div className='mt-4'>
                        <label htmlFor="">
                            Time
                        </label>
                        <TimeLimitInput
                            value={watch('time')}
                            onChange={(val) => setValue('time', val)}
                            hasError={!!errors.time}
                            errorMessage={errors.time?.message}
                        />

                    </div>

                    <Button className="w-full mt-8" type="submit">
                        Save question
                    </Button>
                </div>
            </form>
        </Modal>
    )
}

export default CreateInterviewQuestionModal