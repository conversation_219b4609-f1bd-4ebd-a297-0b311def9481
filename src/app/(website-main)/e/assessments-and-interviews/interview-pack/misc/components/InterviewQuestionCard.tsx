import React from 'react'
import { Question, QuestionType } from '../../../misc/types/create-assessments'
import { convertToTitleCase } from '@/utils/strings';
import { QUESTION_TYPES_DICTIONARY } from '../../../misc/constants/constants';
import { ConfirmDeleteModalAlt, DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/shared';
import { CloseSquare, Edit, Eye, More } from 'iconsax-react';
import { useBooleanStateControl } from '@/hooks';
import { Axios } from '@/lib/api/axios';
import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { TInterviewQuestion } from '../../../create-interview/misc/types';
import CreateInterviewQuestionModal from './CreateInterviewQuestionModal';

interface InterviewQuestionCardProps {
    index: number
    question: TInterviewQuestion
    allQuestions: TInterviewQuestion[]
    refetch: () => void
    packId?: string
}

const InterviewQuestionCard = ({ allQuestions, question, index, refetch, packId }: InterviewQuestionCardProps) => {
    const {
        state: isConfirmDeleteQuestionModalOpen,
        setTrue: openConfirmDeleteQuestionModal,
        setFalse: closeConfirmDeleteQuestionModal
    } = useBooleanStateControl()
    const {
        state: isEditQuestionModalOpen,
        setTrue: openEditQuestionModal,
        setFalse: closeEditQuestionModal
    } = useBooleanStateControl()
    const {
        state: isViewQuestionSheetOpen,
        setTrue: openViewQuestionModal,
        setFalse: closeViewQuestionSheet,
        setState: setViewQuestionSheetState
    } = useBooleanStateControl()


    const { mutate: deleteQuestion, isLoading: isDeleting } = useMutation(
        {
            mutationFn: async () => {
                "assessments/interview-packs/5f2a3b6a-1834-4109-8ab8-7dbf554c3af0/delete-question/3b44c808-7d58-441e-9434-1d94e455b19e/"
                const res = await Axios.delete(`assessments/interview-packs/${packId}/delete-question/${question.id}/`,)
                if (packId) {
                    return res.data
                }
                else return
            }
        }
    )
    function handleQuestionOption() {
        deleteQuestion(undefined, {
            onSuccess: () => {
                refetch()
                closeConfirmDeleteQuestionModal()
                toast.success("Question deleted successfully")
            }
        })
    }

    return (
        <article className="rounded-xl bg-white p-4 min-h-[150px] max-h-[200px] border-transparent hover:border-primary border-2 !outline-none">
            <header className="item-center flex justify-between">
                <h2 className="text-base text-header-text font-medium">Question {index + 1}</h2>
                <DropdownMenu>
                    <DropdownMenuTrigger title="view options" >
                        <More className="rotate-90" fill="#D9D9D9" size={18} />
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                        {/* <DropdownMenuItem className="flex items-center gap-3" onClick={openViewQuestionModal}>
                            <Eye size={18} /> View
                        </DropdownMenuItem> */}
                        {
                            !!packId &&
                            <>
                                <DropdownMenuItem className="flex items-center gap-3" onClick={openEditQuestionModal}>
                                    <Edit size={18} /> Edit
                                </DropdownMenuItem>
                                <DropdownMenuItem className="flex items-center gap-3" onClick={openConfirmDeleteQuestionModal}>
                                    <CloseSquare size={18} /> Delete
                                </DropdownMenuItem>
                            </>
                        }
                    </DropdownMenuContent>
                </DropdownMenu>

            </header>

            <p className="text-sm mt-3 mb-2 font-normal">
                <span className="text-helper-text">
                    Question type:
                </span>{" "}
                <span className="text-header-text font-medium">
                    {convertToTitleCase(QUESTION_TYPES_DICTIONARY[question?.type]?.name || "")}
                </span>
            </p>

            <div className="text-xs text-body-text leading-snug tiptap"
                dangerouslySetInnerHTML={{ __html: `${question.question?.substring(0, 100)} ${question.question?.length > 100 && "..."}` }}
            />


            {/* <QuestionDetailSheet
                allQuestions={allQuestions}
                index={index}
                question={question}
                isViewQuestionSheetOpen={isViewQuestionSheetOpen}
                closeViewQuestionSheet={closeViewQuestionSheet}
                setViewQuestionSheetState={setViewQuestionSheetState}
            /> */}
            <CreateInterviewQuestionModal
                closeModal={closeEditQuestionModal}
                isModalOpen={isEditQuestionModalOpen}
                packId={packId!}
                initial_question={question!}

            />
            <ConfirmDeleteModalAlt
                deleteFunction={handleQuestionOption}
                isModalOpen={isConfirmDeleteQuestionModalOpen}
                closeModal={closeConfirmDeleteQuestionModal}
                title="Delete question"
                isDeleting={isDeleting}
            >
                <p className="text-sm text-body-text font-normal">
                    You are about to delete this question. This action cannot be undone.
                </p>
            </ConfirmDeleteModalAlt>
        </article>
    )
}

export default InterviewQuestionCard