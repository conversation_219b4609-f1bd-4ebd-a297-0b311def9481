import { AltModal, <PERSON><PERSON>, <PERSON>down<PERSON>enu<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/components/shared';
import React, { useState } from 'react';
import { QUESTION_TYPES_DICTIONARY } from '../../../misc/constants/constants';
import { Question } from '../../../misc/types/create-assessments';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Edit } from 'iconsax-react';

interface QuestionDetailSheetProps {
    question: Question;
    allQuestions: Question[];
    index: number;
    closeViewQuestionSheet: () => void;
    isViewQuestionSheetOpen: boolean;
    setViewQuestionSheetState: React.Dispatch<React.SetStateAction<boolean>>
}

const QuestionDetailSheet: React.FC<QuestionDetailSheetProps> = ({
    question,
    allQuestions,
    index,
    closeViewQuestionSheet,
    isViewQuestionSheetOpen,
    setViewQuestionSheetState
}) => {
    const [currentIndex, setCurrentIndex] = useState(index);
    const currentQuestion = allQuestions[currentIndex];

    const handleNavigation = (direction: 'next' | 'previous') => {
        setCurrentIndex(current => {
            if (direction === 'next') {
                return (current + 1) % allQuestions.length;
            }
            return current === 0 ? allQuestions.length - 1 : current - 1;
        });
    };

    return (
        <Sheet open={isViewQuestionSheetOpen} modal={true} onOpenChange={closeViewQuestionSheet}>
            <SheetContent className="!w-[90%] !max-w-[575px] p-5">
                <SheetHeader>
                    <SheetTitle>Question Details</SheetTitle>
                </SheetHeader>
                {
                    currentQuestion &&

                    <div className="h-[calc(100%-3lh)] flex flex-col gap-y-5 text-sm mt-6">
                        <div className="grid grid-cols-3 gap-4 items-center font-medium">
                            <div>
                                <h3 className="text-body-text">Type:</h3>
                                <p className="heading-text capitalize">
                                    {QUESTION_TYPES_DICTIONARY[currentQuestion?.type!]?.name}
                                </p>
                            </div>
                            <div>
                                <h3 className="text-body-text">No:</h3>
                                <p className="heading-text">Question {currentIndex + 1} of {allQuestions.length}</p>
                            </div>
                        </div>

                        <div>
                            <h3 className="heading-text">Question</h3>
                            <div dangerouslySetInnerHTML={{ __html: currentQuestion.question }} />
                        </div>

                        {currentQuestion.answer_options?.length > 1 && (
                            <div>
                                <h3 className="heading-text">Answer options</h3>
                                <ul className="list-disc pl-4">
                                    {currentQuestion.answer_options.map((option, idx) => (
                                        <li key={idx}>{option}</li>
                                    ))}
                                </ul>
                            </div>
                        )}

                        {Array.isArray(currentQuestion.answer) && currentQuestion.answer.length > 1 ? (
                            <div>
                                <h3 className="heading-text">Answers</h3>
                                <ul className="list-disc pl-4">
                                    {currentQuestion.answer.map((answer: string, idx: number) => (
                                        <li key={idx}>{answer}</li>
                                    ))}
                                </ul>
                            </div>
                        ) : (
                            <div>
                                <h3 className="heading-text">Answer</h3>
                                <p>{currentQuestion.answer || "-"}</p>
                            </div>
                        )}

                        <footer className="flex justify-between items-center mt-auto  rounded-t-xl">
                            <Button
                                onClick={() => handleNavigation('previous')}
                                className="flex items-center gap-2"
                                variant="extralight"
                                size="small"
                            >
                                <ChevronLeft className="w-4 h-4" />
                                Previous
                            </Button>
                            <Button
                                onClick={() => handleNavigation('next')}
                                className="flex items-center gap-2"
                                variant="extralight"
                                size="small"
                            >
                                Next
                                <ChevronRight className="w-4 h-4" />
                            </Button>
                        </footer>
                    </div>
                }
            </SheetContent>
        </Sheet>
    );
};

export default QuestionDetailSheet;