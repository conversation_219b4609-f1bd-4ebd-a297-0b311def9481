import React from 'react'
import { z } from 'zod';
import { useForm } from 'react-hook-form';

import { AltButton, AltModal, Input, LoaderBtn, Textarea } from '@/components/shared';
import { Axios } from '@/lib/api/axios';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';

const schema = z.object({
    name: z.string({ message: "Enter pack name" }).min(3, { message: "Enter at least 3 characters" }),
    description: z.string({ message: "Enter pack description" }).min(10, { message: "Enter at least 10 characters" })
})

type formType = z.infer<typeof schema>

interface EditPackDetailsModalProps {
    isEditPackDetailsModalOpen: boolean,
    closeEditPackDetailsModal: () => void,
    defaultValues: {
        name: string,
        description: string
    }
    packId: string
    refetch: () => void
}



const EditPackDetailsModal = ({ isEditPackDetailsModalOpen, closeEditPackDetailsModal, defaultValues, packId, refetch }: EditPackDetailsModalProps) => {
    const { handleSubmit, register, formState: { errors }, trigger, getValues } = useForm<formType>({
        resolver: zodResolver(schema),
        defaultValues
    })
    const fn = async (data: formType) => {
        const response = await Axios.patch(`assessments/question-packs/${packId}/`, {
            ...data
        })
        return response.data;
    }
    const { mutate: createPack, isLoading: iscCreatingPack } = useMutation({
        mutationFn: fn
    })

    const submitForm = async (data: formType) => {
        const isValid = await trigger()
        if (!isValid) return
        createPack(data, {
            onSuccess(data) {
                toast.success("Pack details updated successfully")
                refetch()
                closeEditPackDetailsModal()
            },
        })
    }

    return (
        <AltModal
            is_open={isEditPackDetailsModalOpen}
            close={closeEditPackDetailsModal}
            title="options to continue"
        >
            <form
                className="md:w-[547px] py-4  bg-[#F5F3FF] text-center text-[#675E8B] bg-light-accent-bg max-w-[450px] space-y-4 p-5 text-sm"
                onSubmit={handleSubmit(submitForm)}
            >
                <div>
                    <label className="space-y-1 text-left">
                        <p className="heading-text font-medium">Name of pack</p>
                        <Input
                            variant="showcase"
                            type="text"
                            placeholder="Enter pack name"
                            hasError={!!errors.name}
                            errorMessage={errors.name?.message}
                            {...register("name")}
                        />
                    </label>
                </div>
                <div>
                    <label className="space-y-1 text-left">
                        <p className="heading-text font-medium">Description</p>
                        <Textarea
                            variant="showcase"
                            {...register("description")}
                            className="input-white min-h-[150px] resize-none"
                            placeholder="Enter pack description"
                            hasError={!!errors.description}
                            errorMessage={errors.description?.message}
                        />
                    </label>
                </div>
                <div className="flex items-center gap-2 mt-4">
                    <AltButton type="button" onClick={closeEditPackDetailsModal} className="h-10 btn-primary-bordered w-full">
                        Cancel
                    </AltButton>
                    <button type="submit" className="h-10 flex items-center justify-center gap-4 btn-primary w-full" onClick={(() => submitForm(getValues()))}>
                        Proceed
                        {
                            iscCreatingPack && <LoaderBtn />
                        }
                    </button>
                </div>
            </form>

        </AltModal>

    )
}

export default EditPackDetailsModal