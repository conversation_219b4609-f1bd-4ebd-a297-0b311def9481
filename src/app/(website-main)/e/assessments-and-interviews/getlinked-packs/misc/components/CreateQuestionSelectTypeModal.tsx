import React from 'react';

import { QUESITON_TYPES } from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants';
import { useBooleanStateControl } from '@/hooks';

import QuestionTypeIcon from '../../../../../../../components/icons/jsx/QuestionTypeIcon';
import Modal from '../../../../../../../components/Modal';
import CreateQuestionModal from './CreateQuestionModal';

interface ModalProps {
    isSelectQuestionTypeModalOpen: boolean;
    closeSelectQuestionTypeModal: () => void;
    packId?: string
    assessment_id?: string
    section_id?: string
    refetch: () => void
}

const CreateQuestionSelectTypeModal: React.FC<ModalProps> = ({
    isSelectQuestionTypeModalOpen,
    closeSelectQuestionTypeModal,
    packId,
    section_id,
    assessment_id,
    refetch
}) => {
    const [selectedQuestionType, setSelectedQuestionType] = React.useState<"multiple_choice" | "multiple_response" | "fill_in_the_blanks" | "essay" | "true_false">("multiple_choice")
    const handleSelectQuestionType = (selected: string) => {
        setSelectedQuestionType(selected as "multiple_choice" | "multiple_response" | "fill_in_the_blanks" | "essay" | "true_false")
        openCreateQuestionModal()
    }

    const memoizedSelectedType = React.useMemo(() => selectedQuestionType, [selectedQuestionType])

    const {
        state: isCreateQuestionModalOpen,
        setTrue: openCreateQuestionModal,
        setFalse: closeCreateQuestionModal
    } = useBooleanStateControl()


    
    return (
        <>
            <Modal is_open={isSelectQuestionTypeModalOpen} close={closeSelectQuestionTypeModal} title="Questions types">
                <div className="grid grid-cols-2  md:grid-cols-4 justify-center gap-2 p-4 ">
                    {QUESITON_TYPES.map((item, index) =>
                        "multiple_response, multiple_choice, essay, fill_in_the_blanks, true_false".split(", ").includes(item.value) ? (
                            <button
                                key={index}
                                type="button"
                                className="btn-modal-choice text-center"
                                onClick={() => handleSelectQuestionType(item.value)}
                            >
                                <div className="flex flex-col items-center space-y-1">
                                    <div className="flex aspect-square w-16 items-center justify-center rounded-full bg-white">
                                        <QuestionTypeIcon question_type={item.value} is_large={true} />
                                    </div>
                                    <p className="heading-text text-sm font-normal capitalize">
                                        {item.name}
                                    </p>
                                </div>
                            </button>
                        )
                            : (
                                <div
                                    key={index}
                                    className="btn-modal-choice hover:outline-none cursor-auto text-center relative group/btn"
                                >
                                    <div className="flex flex-col items-center space-y-1">
                                        <div className="flex aspect-square w-16 items-center justify-center rounded-full bg-white">
                                            <QuestionTypeIcon question_type={item.value} is_large={true} />
                                        </div>
                                        <p className="heading-text text-sm font-normal capitalize">
                                            {item.name}
                                        </p>
                                    </div>
                                    <p className='absolute inset-0 group-hover/btn:grid place-items-center hidden'>
                                        <span className="btn-base bg-[#F1EFFC] cursor-auto drop-shadow-md">Coming soon</span>
                                    </p>
                                </div>
                            )
                    )}
                </div>
            </Modal>

            {
                isCreateQuestionModalOpen &&
                <CreateQuestionModal
                    closeCreateQuestionModal={closeCreateQuestionModal}
                    isCreateQuestionModalOpen={isCreateQuestionModalOpen}
                    questionType={memoizedSelectedType}
                    section_id={section_id}
                    assessment_id={assessment_id}
                    packId={packId!}
                    refetch={refetch}
                />
            }
        </>
    );
};
export default CreateQuestionSelectTypeModal;
