import React from 'react'
import { convertToTitleCase } from '@/utils/strings';
import { QUESTION_TYPES_DICTIONARY } from '../../../misc/constants/constants';
import { Axios } from '@/lib/api/axios';
import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/shared/popover-without-portal';
import { TGetQuestionPackDetails } from '../../../misc/api/getQuestionPackDetails';

interface QuestionCardProps {
    index: number
    question: TGetQuestionPackDetails
    allQuestions: TGetQuestionPackDetails[]
    refetch: () => void
    packId?: string
}

const QuestionCard = ({ allQuestions, question, index, refetch, packId }: QuestionCardProps) => {
    const { mutate: deleteQuestion, isLoading: isDeleting } = useMutation(
        {
            mutationFn: async () => {
                const res = await Axios.delete(`assessments/question-packs/${packId}/delete-question/${question.id}/`,)
                if (packId) {
                    return res.data
                }
                else return
            }
        }
    )


    return (
        <article className="rounded-xl bg-white p-4 min-h-[150px] max-h-[200px] border-transparent hover:border-primary border-2 !outline-none">
            <header className="item-center flex justify-between">
                <h2 className="text-base text-header-text font-medium">Question {index + 1}</h2>
                

            </header>

            <p className="text-sm mt-3 mb-2 font-normal">
                <span className="text-helper-text">
                    Question type:
                </span>{" "}
                <span className="text-header-text font-medium">
                    {convertToTitleCase(QUESTION_TYPES_DICTIONARY[question?.type]?.name || "")}
                </span>
            </p>

            <div className="text-xs text-body-text leading-snug tiptap"
                dangerouslySetInnerHTML={{ __html: `${question.question?.substring(0, 100)} ${question.question?.length > 100 && "..."}` }}
            />

{/* 
            <QuestionDetailSheet
                allQuestions={allQuestions}
                index={index}
                question={question}
                isViewQuestionSheetOpen={isViewQuestionSheetOpen}
                closeViewQuestionSheet={closeViewQuestionSheet}
                setViewQuestionSheetState={setViewQuestionSheetState}
            /> */}
            
        </article>
    )
}

export default QuestionCard