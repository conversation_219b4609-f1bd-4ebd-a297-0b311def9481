'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { CloseCircle, DocumentUpload } from 'iconsax-react';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import {
  FieldErrors,
  useForm,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from 'react-hook-form';
import toast from 'react-hot-toast';
import { z } from 'zod';
import {
  AltModal,
  Button,
  Checkbox2,
  Input,
  LoaderBtn,
  RichTextEditor,
  Select,
  SelectSingleCombo,
} from '@/components/shared';
import FormError from '@/components/shared/form-error';
import { Axios } from '@/lib/api/axios';
import { convertKebabAndSnakeToTitleCase } from '@/utils/strings';
import { UseAddQuestionsToSection } from '../../../create-assessment/misc/api';
import { useGetQuestionsSections } from '../../../interview-pack/misc/api';
import { validateImageDimensions } from '../../../misc/components/AddCustomQuestionModal';
import {
  ALPHABETS,
  MULTIPLE_CHOICE_OPTIONS_LIMIT,
  MULTIPLE_CHOICE_OPTIONS_MIN_LIMIT,
  TRUE_OR_FALSE,
} from '../../../misc/constants/constants';

const baseQuestionSchema = z.object({
  section: z.string(),
  question: z.string().min(1, 'Question is required'),
  points: z.number().min(1, 'Points must be a at least 1'),
  images: z.array(z.instanceof(Blob)).optional(),
  is_shuffled: z.boolean().optional(),
});

const multipleChoiceSchema = baseQuestionSchema.extend({
  type: z.literal('multiple_choice'),
  answer_options: z
    .array(z.string().min(1, 'Enter answer option'))
    .min(2)
    .max(MULTIPLE_CHOICE_OPTIONS_LIMIT)
    .refine(options => new Set(options).size === options.length, {
      message: 'Answer options must be unique',
    }),
  answer: z.array(z.string()).length(1),
});
const multipleResponseSchema = baseQuestionSchema.extend({
  type: z.literal('multiple_response'),
  answer_options: z
    .array(z.string().min(1, 'Enter answer option'))
    .min(2)
    .max(MULTIPLE_CHOICE_OPTIONS_LIMIT),
  answer: z
    .array(z.string())
    .min(1, { message: 'Select at least one correct answer' }),
});
const fillInTheBlanksSchema = baseQuestionSchema.extend({
  type: z.literal('fill_in_the_blanks'),
  answer_options: z.array(z.string().min(1, 'Enter answer option')).min(1),
  answer: z.array(z.string({ message: 'Select correct answer' })).length(1),
});
const essaySchema = baseQuestionSchema.extend({
  type: z.literal('essay'),
  instructions: z.string(),
});
const trueFalseSchema = baseQuestionSchema.extend({
  type: z.literal('true_false'),
  answer: z.array(z.enum(['true', 'false'])).length(1, 'Select correct answer'),
});
type QuestionType =
  | z.infer<typeof multipleChoiceSchema>
  | z.infer<typeof multipleResponseSchema>
  | z.infer<typeof fillInTheBlanksSchema>
  | z.infer<typeof essaySchema>
  | z.infer<typeof trueFalseSchema>;

const HOW_CANDIDATES_WILL_ANSWER = {
  multiple_choice: 'selecting one option',
  multiple_response: 'selecting multiple options',
  fill_in_the_blanks: 'selecting an answer from a provided list of choices',
  essay: 'typing in an input field',
  true_false: 'Selecting true or false',
};

interface CreateQuestionModalProps {
  questionType:
    | 'multiple_choice'
    | 'multiple_response'
    | 'fill_in_the_blanks'
    | 'essay'
    | 'true_false';
  isCreateQuestionModalOpen: boolean;
  closeCreateQuestionModal: () => void;
  packId: string;
  assessment_id?: string;
  section_id?: string;
  refetch: () => void;
}

export default function CreateQuestionModal({
  packId,
  assessment_id,
  section_id,
  questionType,
  isCreateQuestionModalOpen,
  closeCreateQuestionModal,
  refetch,
}: CreateQuestionModalProps) {
  const question_type = questionType;

  const [imageUrls, setImageUrls] = useState<string[]>([]);
  const [imageBlobs, setImageBlobs] = useState<Blob[]>([]);
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const schema = (() => {
    switch (questionType) {
      case 'multiple_choice':
        return multipleChoiceSchema;
      case 'multiple_response':
        return multipleResponseSchema;
      case 'fill_in_the_blanks':
        return fillInTheBlanksSchema;
      case 'essay':
        return essaySchema;
      case 'true_false':
        return trueFalseSchema;
      default:
        throw new Error(`Unsupported question type: ${questionType}`);
    }
  })();
  const { data: questionSections, isLoading: isGettingSections } =
    useGetQuestionsSections();

  React.useEffect(() => {
    if (questionType === 'true_false') {
      setValue(
        'answer_options',
        TRUE_OR_FALSE.map(item => item.value)
      );
    }
  }, []);

  React.useEffect(() => {
    setValue('type', questionType);
  }, [questionType]);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
    getValues,
    trigger,
    reset,
  } = useForm<QuestionType>({
    resolver: zodResolver(schema),
    defaultValues: {
      type: question_type,
      // section: packId,
      section: !!assessment_id ? undefined : questionSections?.results?.[0].id,
      question: '',
      answer_options: questionType !== 'essay' ? ['', '', '', ''] : undefined,
      answer: [],
      is_shuffled: false,
    },
    mode: 'onChange',
  });

  const closeAndReset = () => {
    closeCreateQuestionModal();
    reset();
    setImageUrls([]);
    setImageBlobs([]);
    setImageFiles([]);
  };

  const blobToBase64 = (blob: Blob): Promise<string> => {
    return new Promise(resolve => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.readAsDataURL(blob);
    });
  };

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      const newImageUrls = [...imageUrls];
      const newImageBlobs = [...imageBlobs];
      const newImageFiles = [...imageFiles];

      for (const file of acceptedFiles) {
        const blob = new Blob([file], { type: file.type });
        const image_base64 = await blobToBase64(blob);

        newImageFiles.push(file);
        const imageBase64 = await blobToBase64(blob);
        newImageUrls.push(imageBase64);
        newImageBlobs.push(blob);
      }

      setImageUrls(newImageUrls);
      setImageBlobs(newImageBlobs);
      setImageFiles(newImageFiles);
    },
    [imageUrls, imageBlobs, imageFiles]
  );

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    multiple: true,
    accept: {
      'image/*': [],
    },
    maxSize: 10 * 1024 * 1024,
  });

  const deleteImage = (index: number) => {
    setImageUrls(prev => prev.filter((_, i) => i !== index));
    setImageBlobs(prev => prev.filter((_, i) => i !== index));
    setImageFiles(prev => prev.filter((_, i) => i !== index));
  };

  const { mutate: createQuestion, isLoading: isCreatingQuestion } = useMutation(
    async (data: QuestionType) => {
      const formData = new FormData();

      Object.entries(data).forEach(([key, val]) => {
        if (key === 'answer_options') {
          (val as string[]).forEach((option, index) => {
            formData.append(`${key}[${index}]`, option);
          });
        } else if (key === 'section') {
          if (assessment_id) {
          } else {
            formData.append(key, val as string);
          }
        } else if (key === 'answer') {
          if (questionType === 'multiple_response') {
            (val as string[]).forEach((option, index) => {
              formData.append(
                `${key}[${index}]`,
                watch('answer_options')[index]
              );
            });
          } else {
            formData.append(key, val as string);
          }
        } else if (key == 'is_shuffled') {
          formData.append(key, val as string);
        } else {
          formData.append(key, val as string);
        }

        imageFiles.forEach((file, index) => {
          formData.append(`images[${index}]`, file);
        });
        console.log(formData);
      });
      if (assessment_id && section_id) {
        const response = await Axios.post(
          `/assessments/${assessment_id}/sections/${section_id}/add_question/`,
          formData
        );
        return response.data;
      } else {
        const response = await Axios.post(
          `/assessments/question-packs/${packId}/create-question/`,
          formData
        );
        return response.data;
      }
    }
  );

  const onSubmit = async (data: QuestionType) => {
    createQuestion(data, {
      onSuccess() {
        closeAndReset();
        closeCreateQuestionModal();
        refetch();
        toast.success('Question created successfully');
      },
      onError(err) {
        console.log(err);
      },
    });
  };
  // console.log(errors, getValues());

  return (
    <AltModal
      title="New Question"
      is_open={isCreateQuestionModalOpen}
      close={closeAndReset}
      portalClass="w-[90%] max-w-[1050px]"
      is_close_by_button_only={true}
    >
      <form
        onSubmit={handleSubmit(onSubmit)}
        id="new-question-form"
        className="space-y-6 p-4 lg:p-8"
      >
        {/* Image Upload Section */}
        <div className=" space-y-2">
          <h2 className="text-lg font-medium text-primary">Images</h2>
          <div className="flex flex-wrap gap-2">
            {imageUrls.map((url, index) => (
              <div key={index} className="relative">
                <img
                  src={url}
                  alt={`Question image ${index + 1}`}
                  className="h-24 w-24 rounded-lg object-cover"
                />
                <button
                  type="button"
                  onClick={() => deleteImage(index)}
                  className="absolute -right-2 -top-2 rounded-full bg-white p-1 shadow-lg"
                >
                  <CloseCircle />
                </button>
              </div>
            ))}

            <div
              {...getRootProps()}
              className="cursor-pointer rounded-lg border-2 border-dashed border-primary/20 p-4 transition-colors hover:border-primary/40"
            >
              <input {...getInputProps()} />
              <div className="flex items-center gap-2">
                <DocumentUpload />
                <div>
                  <p className="text-sm font-medium">Upload images</p>
                  <p className="text-xs text-muted-foreground">
                    Drag & drop or click to select
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Question Form */}
        <div className="grid gap-6 md:grid-cols-2 lg:gap-10">
          <div className="space-y-4">
            <h2 className="text-lg font-medium text-primary">
              Create {convertKebabAndSnakeToTitleCase(questionType)} question
            </h2>

            <div>
              <Select
                options={questionSections?.results || []}
                valueKey="id"
                labelKey="name"
                placeholder="Select category"
                label="Question Category"
                {...register('section')}
                onChange={value => setValue('section', value)}
                variant="showcase"
                hasError={!!errors.section}
                errorMessage={errors.section?.message}
              />
            </div>

            <div>
              <p className="p-4 text-xs">
                Candidates will be asked to respond to a question in by{' '}
                {HOW_CANDIDATES_WILL_ANSWER[questionType]} {'\n'}
                {questionType === 'fill_in_the_blanks' &&
                  'You can create the blanks by typing [blank] in the question and underlining it'}
              </p>
              <RichTextEditor
                placeholder="Enter your question here"
                className="min-h-[200px] bg-[#FAFAFA] "
                hasError={!!errors.question}
                errorMessage={errors.question?.message}
                onChange={content => setValue('question', content)}
                // initialContent={watch('question')}
              />
            </div>
          </div>

          <div className="space-y-4">
            {questionType === 'multiple_choice' && (
              <MultipleChoiceAnswers
                register={register}
                watch={watch}
                setValue={setValue}
                errors={errors}
              />
            )}
            {questionType === 'multiple_response' && (
              <MultipleResponseAnswers
                register={register}
                watch={watch}
                setValue={setValue}
                errors={errors}
              />
            )}
            {questionType === 'essay' && (
              <EssayAnswers
                register={register}
                watch={watch}
                setValue={setValue}
                errors={errors}
              />
            )}
            {questionType === 'fill_in_the_blanks' && (
              <FillInTheBlanksAnswer
                register={register}
                watch={watch}
                setValue={setValue}
                errors={errors}
              />
            )}
            {questionType === 'true_false' && (
              <TrueOrFalseAnswers
                register={register}
                watch={watch}
                setValue={setValue}
                errors={errors}
              />
            )}

            <div>
              <label className="text-sm font-medium">
                How do you want to score this question?
              </label>
              <Input
                type="number"
                {...register('points', { valueAsNumber: true })}
                className="mt-1"
                placeholder="Enter points"
                variant="showcase"
                hasError={!!errors.points}
                errorMessage={errors.points?.message}
              />
            </div>

            <Button
              type="submit"
              form="new-question-form"
              disabled={isCreatingQuestion}
              className="w-full"
            >
              Save Question
              {isCreatingQuestion && <LoaderBtn />}
            </Button>
          </div>
        </div>
      </form>
    </AltModal>
  );
}

///////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////
/////////////////////                 QUESTION TYPES            ///////////////////////
///////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////
interface AnswersProps {
  register: UseFormRegister<QuestionType>;
  watch: UseFormWatch<QuestionType>;
  setValue: UseFormSetValue<QuestionType>;
  errors: FieldErrors<QuestionType>;
}

function MultipleChoiceAnswers({
  register,
  watch,
  setValue,
  errors,
}: AnswersProps) {
  const answerOptions = watch('answer_options') || [];

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Answer Options</h3>
      {answerOptions.map((_, index) => (
        <div key={index} className="flex items-center gap-2">
          <span>{ALPHABETS[index]})</span>
          <Input
            {...register(`answer_options.${index}`)}
            placeholder={`Option ${index + 1}`}
            variant="showcase"
            hasError={!!(errors as any).answer_options?.[index]}
            errorMessage={(errors as any).answer_options?.[index]?.message}
            containerClassName="grow"
          />
          {answerOptions.length > MULTIPLE_CHOICE_OPTIONS_MIN_LIMIT && (
            <button
              type="button"
              onClick={() => {
                const newOptions = answerOptions.filter((_, i) => i !== index);
                setValue('answer_options', newOptions);
              }}
            >
              <CloseCircle />
            </button>
          )}
        </div>
      ))}
      {(errors as any).answer_options?.root.message && (
        <p className="mt-1.5 rounded-md bg-red-100 px-5 py-1.5 text-xs text-red-600 animate-in fade-in-40">
          {(errors as any).answer_options?.root.message}
        </p>
      )}
      {answerOptions.length < MULTIPLE_CHOICE_OPTIONS_LIMIT && (
        <Button
          type="button"
          variant="outlined"
          onClick={() => setValue('answer_options', [...answerOptions, ''])}
        >
          Add Option
        </Button>
      )}
      <div>
        <label className="text-sm font-medium">Correct Answer</label>
        <SelectSingleCombo
          name="correctAnswer"
          placeholder="Select correct answer"
          options={answerOptions
            .filter(option => option.trim() !== '')
            .map((option, index) => ({ value: option, label: option }))}
          value={watch('answer')[0]}
          onChange={value => setValue('answer', [value])}
          hasError={!!(errors as any).answer}
          errorMessage={(errors as any).answer?.message}
          valueKey="value"
          labelKey="label"
          itemClass="text-left"
        />
      </div>
      <div className="flex items-center gap-2">
        <Checkbox2 {...register('is_shuffled')} id="shuffle" />
        <label htmlFor="shuffle" className="text-sm">
          Shuffle options
        </label>
      </div>
    </div>
  );
}

function FillInTheBlanksAnswer({
  register,
  watch,
  setValue,
  errors,
}: AnswersProps) {
  const answerOptions = watch('answer_options') || [];

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Answer Options</h3>
      {answerOptions.map((_, index) => (
        <div key={index} className="flex items-center gap-2">
          <span>{ALPHABETS[index]})</span>
          <Input
            {...register(`answer_options.${index}`)}
            placeholder={`Option ${index + 1}`}
            variant="showcase"
            hasError={!!(errors as any).answer_options?.[index]}
            errorMessage={(errors as any).answer_options?.[index]?.message}
            containerClassName="grow"
          />
          {answerOptions.length > MULTIPLE_CHOICE_OPTIONS_MIN_LIMIT && (
            <button
              type="button"
              onClick={() => {
                const newOptions = answerOptions.filter((_, i) => i !== index);
                setValue('answer_options', newOptions);
              }}
            >
              <CloseCircle />
            </button>
          )}
        </div>
      ))}

      {answerOptions.length < MULTIPLE_CHOICE_OPTIONS_LIMIT && (
        <Button
          type="button"
          variant="outlined"
          onClick={() => setValue('answer_options', [...answerOptions, ''])}
        >
          Add Option
        </Button>
      )}
      <div>
        <label className="text-sm font-medium">Correct Answer</label>
        <SelectSingleCombo
          name="correctAnswer"
          placeholder="Select correct answer"
          options={answerOptions
            .filter(option => option.trim() !== '')
            .map((option, index) => ({ value: option, label: option }))}
          value={watch('answer')[0]}
          onChange={value => setValue('answer', [value])}
          hasError={!!(errors as any).answer}
          errorMessage={(errors as any).answer?.message}
          valueKey="value"
          labelKey="label"
          variant="showcase"
          itemClass="text-left"
        />
      </div>
      <div className="flex items-center gap-2">
        <Checkbox2 {...register('is_shuffled')} id="shuffle" />
        <label htmlFor="shuffle" className="text-sm">
          Shuffle options
        </label>
      </div>
    </div>
  );
}

function MultipleResponseAnswers({
  register,
  watch,
  setValue,
  errors,
}: AnswersProps) {
  const answerOptions = watch('answer_options') || [];
  const removeOptionAndUpdateAnswer = (index: number) => {
    const newOptions = answerOptions.filter((_, i) => i !== index);
    setValue('answer_options', newOptions);
    setValue(
      'answer',
      watch('answer').filter((_, i) => _ !== index.toString())
    );
  };

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-medium">Answer Options</h3>
        <p className="py-4 text-xs">Check the correct options(s)</p>
      </div>
      {(errors as any).answer && (errors as any).answer?.message && (
        <FormError errorMessage={(errors as any).answer.message} />
      )}
      {answerOptions.map((_, index) => (
        <div key={index} className="flex items-center gap-2">
          <span>{ALPHABETS[index]})</span>
          <Input
            {...register(`answer_options.${index}`)}
            placeholder={`Option ${index + 1}`}
            variant="showcase"
            hasError={!!(errors as any).answer_options?.[index]}
            errorMessage={(errors as any).answer_options?.[index]?.message}
            containerClassName="grow"
          />
          {answerOptions.length > MULTIPLE_CHOICE_OPTIONS_MIN_LIMIT && (
            <button
              type="button"
              onClick={() => removeOptionAndUpdateAnswer(index)}
            >
              <CloseCircle />
            </button>
          )}
          <Checkbox2
            defaultChecked={false}
            onCheckedChange={checked => {
              const currentAnswers = [...watch('answer')];
              if (checked) {
                currentAnswers.push(index.toString());
              } else {
                currentAnswers.splice(
                  currentAnswers.indexOf(index.toString()),
                  1
                );
              }
              setValue('answer', currentAnswers);
            }}
            id={`answer-${index}`}
          />
        </div>
      ))}
      <section className="flex items-center justify-between">
        {answerOptions.length < MULTIPLE_CHOICE_OPTIONS_LIMIT && (
          <Button
            type="button"
            variant="outlined"
            onClick={() => setValue('answer_options', [...answerOptions, ''])}
          >
            Add Option
          </Button>
        )}

        <div className="flex items-center gap-2">
          <Checkbox2
            {...register('is_shuffled')}
            checked={watch('is_shuffled')}
            onCheckedChange={() =>
              setValue('is_shuffled', !watch('is_shuffled'))
            }
            id="shuffle"
          />
          <label htmlFor="shuffle" className="text-sm">
            Shuffle options
          </label>
        </div>
      </section>
    </div>
  );
}

function TrueOrFalseAnswers({
  register,
  watch,
  setValue,
  errors,
}: AnswersProps) {
  const answerOptions = watch('answer_options') || [];

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Answer Options</h3>
      {TRUE_OR_FALSE.map((_, index) => (
        <div key={index} className="flex items-center gap-2">
          <span>{ALPHABETS[index]})</span>
          <Input
            {...register(`answer_options.${index}`)}
            value={_.value}
            placeholder={`Option ${index + 1}`}
            variant="showcase"
            hasError={!!(errors as any).answer_options?.[index]}
            errorMessage={(errors as any).answer_options?.[index]?.message}
            containerClassName="grow"
            readOnly
          />
        </div>
      ))}

      <div>
        <label className="text-sm font-medium">Correct Answer</label>
        <SelectSingleCombo
          name="correctAnswer"
          placeholder="Select correct answer"
          options={TRUE_OR_FALSE}
          value={watch('answer')[0]}
          onChange={value => setValue('answer', [value])}
          hasError={!!(errors as any).answer}
          errorMessage={(errors as any).answer?.message}
          valueKey="value"
          labelKey="readable_string"
          itemClass="text-left"
          variant="showcase"
        />
      </div>
    </div>
  );
}

function EssayAnswers({ setValue, errors }: AnswersProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-base font-medium">Instructions</h3>
      <RichTextEditor
        className="min-h-[200px] bg-[#FAFAFA] "
        onChange={content => setValue('instructions', content)}
        placeholder={`Enter grading instructions e.g Please focus on the following points while grading:\n - Clarity of the answer\n - Use of relevant examples\n - Logical flow and structure\n - Grammar and spelling `}
        hasError={!!(errors as any).instructions}
        errorMessage={(errors as any).instructions?.message}
      />
    </div>
  );
}
