'use client'
import React from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from 'next/navigation'
import { useGetlinkedPackQuestions, useGetQuestionPacks } from '../../misc/api'
import { Button, EmptyCard, LinkButton } from '@/components/shared'
import { Spinner } from '@/components/shared/icons'
import { useBooleanStateControl } from '@/hooks'
import { Edit } from 'iconsax-react'
import { cn } from '@/utils'
import QuestionCard from '../misc/components/QuestionCard'

const page = () => {
    const params = useParams()
    const searchParams = useSearchParams()
    const packId = params.id as string
    const isNewPack = searchParams.get("new") == "true"
    const router = useRouter()
    const { data, isLoading: isFetchingPackDetails, refetch } = useGetlinkedPackQuestions(packId)

    React.useEffect(() => {
        if (!packId) {
            router.push("/assessments-and-interviews/tests-library")
        }
    }, [packId])

    const [mode, setMode] = React.useState<"read" | "write">("read")

    const savePack = () => {
        setMode("read")
        if (isNewPack) {
            router.push(`/e/assessments-and-interviews/question-pack/${packId}`)
        }
    }

    return (
        <div>
            {
                isFetchingPackDetails ?
                    <div className="flex items-center justify-center w-full h-full">
                        <Spinner />
                    </div>

                    :
                    !isFetchingPackDetails && !data ?
                        <div className="h-full w-full flex items-center justify-center">

                            <EmptyCard
                                title='This pack does not exist'
                                containerClass='!shrink-0 min-w-[350px]'
                                icon={
                                    <img
                                        src="/images/create-assessments/no-assessment.png"
                                        alt="writing with pen"
                                    />
                                }
                                content={
                                    <div className="py-4 space-y-4">
                                        <p className="text-sm text-[#7D8590]">This pack does not exist</p>
                                        <LinkButton
                                            href="/e/assessments-and-interviews/tests-library"
                                            className="gap-2"
                                            size="small"
                                        >
                                            Back to tests library
                                        </LinkButton>
                                    </div>
                                }
                            />
                        </div>
                        :
                        <>
                            <header className="mt-2 flex max-md:flex-col gap-2 items-center justify-between bg-white px-2 py-4">
                                <div className="flex items-center gap-4">
                                    <LinkButton
                                        href="/e/assessments-and-interviews/tests-library?tab=my_question_packs"
                                        className="btn-primary-light-pill"
                                        size="capsule"
                                        variant="extralight"
                                    >
                                        Back
                                    </LinkButton>
                                    <div>
                                        
                                        <p className="max-w-[45ch] text-xs text-body-text truncate overflow-clip" title={""}> {""} </p>
                                    </div>
                                </div>
                                {
                                    (mode == "write" || isNewPack) ? (
                                        <div className="flex gap-2">
                                           
                                            <Button
                                                className=""
                                                onClick={savePack}
                                                size="small"
                                            >
                                                Save pack
                                            </Button>
                                        </div>
                                    ) : (
                                        <div className="flex gap-2">
                                            {/* <Button
                                                disabled={false}
                                                className=""
                                                onClick={() => {
                                                    setMode("write")
                                                }}
                                                size="thin"
                                            >
                                                Edit
                                            </Button> */}
                                        </div>
                                    )}
                            </header>
                            
                            <section
                                className={cn("grid gap-3 px-5",
                                    ((data?.length && data?.length < 4) || !data?.length) ?
                                        "grid-cols-[repeat(auto-fit,minmax(250px,300px))] lg:grid-cols-[repeat(auto-fill,minmax(250px,300px))]"
                                        :
                                        "grid-cols-[repeat(auto-fit,minmax(250px,1fr))]",
                                    data?.length == 0 && mode == "read" && !isNewPack && "w-full h-[80%] flex items-center justify-center"
                                )}
                            >
                                {
                                    data?.length == 0 && mode == "read" && !isNewPack &&
                                    <EmptyCard
                                        title='This pack currently has no questions'
                                        icon={
                                            <img
                                                src="/images/create-assessments/no-assessment.png"
                                                alt="writing with pen"
                                            />
                                        }
                                        content={
                                            <div className="py-4 space-y-4">
                                                <p className="text-sm text-[#7D8590]">Click on the edit button to add new questions</p>
                                            </div>
                                        }
                                    />
                                }

                               
                                {
                                    data?.map((question, index) => {
                                        return (
                                            <QuestionCard
                                                index={index}
                                                key={index}
                                                question={question}
                                                allQuestions={data}
                                                refetch={refetch}
                                                packId={packId}
                                            />
                                        )
                                    })
                                }
                            </section>
                        </>

            }

        </div>
    )
}

export default page