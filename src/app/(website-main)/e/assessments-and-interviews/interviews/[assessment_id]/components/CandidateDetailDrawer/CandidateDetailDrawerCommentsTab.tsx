import { getCandidateCommentsQuery} from "@/app/(website-main)/e/assessments-and-interviews/misc/queries/assessments"
import CommentItem from "../CommentItem"
import { Comment, CommentActions } from "@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments"
import app_fetch from "@/lib/api/appFetch"

type props = {
  candidate_email:string, 
  assessment_id:string
}

export default function CandidateDetailDrawerCommentsTab({candidate_email, assessment_id}:props){

  const {data, isLoading, isError, error, refetch:refetchCandidates} = getCandidateCommentsQuery({candidate_email, assessment_id})

  function handleCommentAction(action:CommentActions, comment:Comment){
    switch (action){
      case "edit comment":
        break
      case "delete comment":
        app_fetch(`assessments/comments/${comment.id}/`, {
          method: "DELETE",
        }).then(()=>{
            refetchCandidates()
          })
        break;
    }
  }

  if (isLoading){
    return <p>Loading comments...</p>
  }

  if (isError){
    return <p>Error loading comments...</p>
  }

  return (
    <div className="p-4 space-y-4">
      <h2 className="heading-text">Comments:</h2>
      <ul className="space-y-4 text-sm flex-1 h-full">
        {
          data.length > 0 ?
            data.map((comment,index)=>(
              <li key={index} className="border-b">
                <CommentItem comment={comment} onAction={(action, comment)=>handleCommentAction(action, comment)} />
              </li>
            )) : (
              <p className="helper-text">All comments will appear here...</p>
            )
        }
      </ul>
    </div>
  )
}
