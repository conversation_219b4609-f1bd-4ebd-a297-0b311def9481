'use client'

import * as React from "react"
import toast from "react-hot-toast"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>lTip, LoaderBtn, } from "@/components/shared"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, Ta<PERSON> } from "@/components/shared/tabs"
import { Eye, Info, Spinner, StrokeCircleCheck, DoubleForward } from "@/components/shared/icons"
import { cn } from "@/utils"
import { getInitials } from "@/utils/strings"
import { useWindowWidth } from "@/hooks"

import CandidateDetailsAboutTabs from "./CandidateDetailDrawerAboutTab";
import CandidateDetailsDocumentsTab from "./CandidateDetailsDrawerDocumentsTab"
import CandidateDetailDrawerCommentsTab from "./CandidateDetailDrawerCommentsTab"
import { JobApplicationData, CandidateAboutResult  } from "../../../../misc/types/create-assessments"

import app_fetch from "@/lib/api/appFetch"
import { useQuery } from "@tanstack/react-query"
import { useParams } from "next/navigation"

type _props = {

    candidate_data?: JobApplicationData;
    isDrawerOpen: boolean;
    closeDrawer: () => void;
    openDrawer: () => void;
}

type _optional_props = {
    show_pagination: false;
    moveToPrevious?:never;
    moveToNext?:never;
} | {
    show_pagination?: true;
    moveToNext: () => void;
    moveToPrevious: () => void;
}

type props = _optional_props & _props;


const CandidateDetailsDrawer: React.FC<props> = ({  candidate_data, isDrawerOpen, show_pagination=true, closeDrawer, moveToNext, moveToPrevious }) => {
    const windowWidth = useWindowWidth()
    const params = useParams()

    let [isLoadingDetails, setIsLoadingDetails] = React.useState(false)


    async function getCandidateAbout():Promise<CandidateAboutResult>{
        return app_fetch(`recruiter/candidate_applied_job_detail/${candidate_data?.id}`).then(res=>res.json())
    }

    const {data:candidate_application_details, isLoading: is_loading_candidate_application_details, error: candidate_application_details_error, isError: is_candidate_application_details_error}  = useQuery({
        queryKey: [],
        queryFn: getCandidateAbout,
        enabled: !!candidate_data?.id
    })

    const tabListRef = React.useRef<HTMLDivElement>(null);
    const [currentTab, setCurrentTab] = React.useState("about");
    const categoryArray = [
        {
            id: 1,
            title: "About",
        },
        {
            id: 2,
            title: "Documents",
        },
        {
            id: 3,
            title: "Assessments",
        },
        {
            id: 4,
            title: "Comments",
        },
        {
            id: 5,
            title: "Journey",
        },
    ];


    const close = () => {
        closeDrawer()
        setCurrentTab("about")
    }

    React.useEffect(()=>{
        if(!candidate_data){
            close()
        }
    }, [candidate_data])

    if (!candidate_data){
        return (
        <div></div>
        )
    }

    return (
        <Drawer onClose={() => close()} open={isDrawerOpen} dismissible direction={windowWidth < 720 ? "bottom" : "right"} >

            <DrawerContent className="w-full md:w-[60%] md:right-0 md:left-auto md:max-w-[750px] h-[90vh] md:h-screen !m-0 !p-0 bg-white border-none rounded-l-2xl overflow-hidden">
                <div className="grow flex flex-col relative max-h-full overflow-y-hidden">
                    <div className="w-full flex items-center justify-center bg-primary md:hidden">
                        <div className="mx-auto mt-3 mb-1 h-1 w-[100px] rounded-full bg-white/60" />
                    </div>

                    <DrawerHeader className="sticky top-0 flex items-center justify-between bg-primary w-full text-white max-md:pt-1 px-5 md:px-8">
                        <h3>Candidate Details</h3>
                        <DrawerClose onClick={close} className="text-sm bg-white/30 px-6 py-2 rounded-lg">Close</DrawerClose>
                    </DrawerHeader>


                    <section className="grow flex flex-col overflow-y-scroll">
                        <header className="flex items-start gap-4 p-6 pb-4">
                            {
                                isLoadingDetails ?
                                    <Skeleton className="shrink-0 w-14 h-14 rounded-full" />
                                    :
                                    <div className="flex items-center justify-center shrink-0 w-14 h-14 text-xl font-bold text-white bg-primary rounded-full">
                                        {getInitials(candidate_data?.name || "First Last")}
                                    </div>
                            }
                            <div>
                                {
                                    isLoadingDetails ?
                                        <>
                                            <Skeleton className="shrink-0 w-28 md:w-60 h-4 rounded-md" />
                                            <div className="flex items-center gap-2 mt-2.5">
                                                <Skeleton className="shrink-0 w-16 md:w-40 h-2.5 rounded-md" />
                                                <Skeleton className="shrink-0 w-6 md:w-32 h-8 rounded-md" />
                                                <Skeleton className="shrink-0 w-6 md:w-32 h-8 rounded-md" />

                                            </div>
                                        </>
                                        :
                                        <>
                                            <h4 className="text-primary text-lg md:text-xl font-medium">{candidate_data?.name}</h4>
                                            <section className="flex items-center gap-3 flex-wrap">
                                                <p className="text-sm">{candidate_data?.email}</p>
                                            </section>
                                        </>
                                }
                            </div>
                        </header>




                        <Tabs defaultValue={"about"} className="grow !grid grid-rows-[max-content,1fr] overflow-x-hidden">
                            <TabsList
                                ref={tabListRef}
                                className={cn("flex items-center gap-1.5 justify-start overflow-x-auto bg-[#F1EFFC] p-[0.25rem] px-6 [scrollbar-width:none] md:justify-start ")}
                            >
                                {
                                    categoryArray?.map((cat: { id: number, title: string }) => {
                                        const { title, id } = cat
                                        return (

                                            <TabsTrigger
                                                className={
                                                    cn(
                                                        'taboption relative min-w-[6rem] flex-1 transition-all duration-500 md:min-w-[5rem] md:max-w-max',
                                                        'rounded-full py-1.5 md:rounded-[0.55rem] md:px-4 lg:px-6',
                                                        'text-xs font-normal leading-5 xs:text-sm',
                                                        'focus:outline-none active:outline-none',
                                                        title.toLowerCase() === currentTab.toLowerCase() ? 'active' : 'taboption text-body-text '
                                                    )
                                                }
                                                onClick={() => setCurrentTab(title.toLowerCase())}
                                                value={title.toLowerCase()}
                                                key={id}
                                            >
                                                <span className='flex items-center gap-3'>
                                                    {title}
                                                </span>
                                            </TabsTrigger>
                                        );
                                    })
                                }
                            </TabsList>

                            {
                                (is_loading_candidate_application_details && is_candidate_application_details_error) ?

                                    <div className="flex items-center justify-center w-full h-full">
                                        <Spinner />
                                    </div>
                                    :
                                    <TabsContent className="mt-2 max-w-full overflow-y-scroll" value={currentTab.toLowerCase()}>

                                        {/* /////////////////////////////////////////////////////////////////////////// */}
                                        {/* /////////////////////////////////////////////////////////////////////////// */}
                                        {
                                            currentTab.toLowerCase() === "about" &&
                                                <CandidateDetailsAboutTabs
                                                    data={candidate_application_details!}
                                                />
                                        }


                                        {/* /////////////////////////////////////////////////////////////////////////// */}
                                        {/* /////////////////////////////////////////////////////////////////////////// */}
                                        {
                                            currentTab.toLowerCase() === "documents" &&
                                                <CandidateDetailsDocumentsTab
                                                    isLoadingCVFile={false}
                                                    cvFileName={candidate_application_details?.job_application.cv || ""}
                                                    cvFile={candidate_application_details?.job_application.cv_data || ""}
                                                />
                                        }


                                        {/* /////////////////////////////////////////////////////////////////////////// */}
                                        {/* /////////////////////////////////////////////////////////////////////////// */}
                                        {
                                            currentTab.toLowerCase() === "comments" &&
                                                <CandidateDetailDrawerCommentsTab assessment_id={params.assessment_id as string} candidate_email={candidate_data.email} />
                                        }
                                        {/* /////////////////////////////////////////////////////////////////////////// */}
                                        {/* /////////////////////////////////////////////////////////////////////////// */}
                                        {
                                            currentTab.toLowerCase() === "journey" && candidate_data?.trail && (
                                                <p>Journey tab</p>
                                                //<CandidateDetailsTrailTab data={basicDetails?.trail} />
                                            )
                                        }
                                    </TabsContent>
                            }

                        </Tabs>
                    </section>

                    {show_pagination && (
                        <DrawerFooter className="sticky bottom-0 flex flex-row items-center justify-between flex-wrap bg-primary-light w-full text-white md:px-8 rounded-t-xl ">
                            <Button size='tiny' variant='outlined' onClick={moveToPrevious} icon={<DoubleForward className="rotate-180" />}>
                                Prev
                            </Button>
                            <Button size='tiny' variant='outlined' onClick={moveToNext}>
                                Next
                                <DoubleForward />
                            </Button>
                        </DrawerFooter>
                    )}
                </div>
            </DrawerContent>
        </Drawer >
    )
    }


export default CandidateDetailsDrawer
