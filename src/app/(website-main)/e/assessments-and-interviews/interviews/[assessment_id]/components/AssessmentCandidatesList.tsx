'use client'

import { use<PERSON>ara<PERSON>, useRouter, } from "next/navigation"
import { FetchCandidateResult } from "../../../misc/types/create-assessments"
import Avatar from "@/components/shared/avatar-alt";
import TableActionButton from "@/app/(website-main)/e/assessments-and-interviews/misc/components/TableActionButton";
import DropdownMenu from "@/components/DropdownMenu";
import {
  ASSESSMENT_STATUSES_DICTIONARY,
  CANDIDATE_TABLE_OPTIONS_DICTIONARY,
  CANDIDATE_TABLE_OPTIONS,
} from "@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants";
import { format } from "date-fns";
import CandidateDetailsDrawer from "./CandidateDetailDrawer"
import { useState } from "react"
import { useBooleanStateControl } from "@/hooks";

type props = {
  isSuccess: boolean;
  data?: FetchCandidateResult;
  isLoading: boolean;
  isError: boolean;
  error: unknown;
  refetch: () => void;

  gotoCandidateNextPage?: () => void,
  gotoCandidatePrevPage?: () => void,
}

export default function AssessmentCandidatesList({ isSuccess, isError, isLoading, data, error, refetch, gotoCandidateNextPage, gotoCandidatePrevPage }: props) {

  const router = useRouter();
  const params = useParams();
  const assessment_id = params.assessment_id

  const {
    state: is_candidate_details_drawer_open,
    setTrue: openCandidateDetailsDrawer,
    setFalse: closeCandidateDetailsDrawer,
  } = useBooleanStateControl(false)


  const [active_candidate_index, setActiveCandidateIndex] = useState(0)
  function moveToNextCandidate() {
    setActiveCandidateIndex(
      (
        (active_candidate_index + 1) + (data?.candidates.length || 0)
      ) % (data?.candidates.length || 1)
    )
  }

  function moveToPreviousCandidate() {
    setActiveCandidateIndex(
      (
        (active_candidate_index - 1) + (data?.candidates.length || 0)
      ) % (data?.candidates.length || 1)
    )

  }

  function handleAssessmentAction(candidate: any, selected_option: any) {
    if (
      selected_option == CANDIDATE_TABLE_OPTIONS_DICTIONARY["view details"]
    ) {
      router.push(
        `/e/assessments-and-interviews/interviews/${assessment_id}/candidates/${candidate.id}/`,
      );
    }

    else if (
      selected_option == CANDIDATE_TABLE_OPTIONS_DICTIONARY["quick view"]
    ) {
      openCandidateDetailsDrawer()
    }
  }



  return (
    <>
      <table className="w-full divide-y border-b">
        <thead>
          <tr>
            {"rank, candidate, invite date, date taken, status, action"
              .split(", ")
              .map((heading, index) => (
                <th
                  className="pb-4 text-left font-normal capitalize"
                  key={index}
                >
                  {heading}
                </th>
              ))}
          </tr>
        </thead>
        <tbody className="divide-y  ">
          {
            isLoading && (
              <div className="p-4">
                <p>Loading...</p>
              </div>
            )
          }
          {
            isError && (
              <div className="p-4 text-red-500">
                <p>Error loading candidates</p>
              </div>
            )
          }

          {isSuccess && data?.candidates.map((candidate, index) => (
            <tr
              className="cursor-pointer hover:bg-primary-light"
              onClick={() => {
                router.push(
                  `/e/assessments-and-interviews/interviews/${params.assessment_id}/candidates/${candidate.id}/`,
                );
              }
              }
              key={index}>
              <td className="p-4">{index + 1}</td>
              <td className="p-4" >
                <div className="flex items-center gap-2">
                  <Avatar name={candidate?.candidate_name} />
                  {candidate.candidate_name}
                </div>
              </td>
              <td className="p-4">{format(new Date(candidate.invite_date), 'dd/MM/yyyy, hh:mma')}
              </td>
              <td className="p-4">
                {candidate.date_taken
                  ? format(new Date(candidate.date_taken), 'dd/MM/yyyy, hh:mma')
                  : "-"}
              </td>
              <td className="p-4">
                <p
                  className={` ${candidate.status === "ongoing" ? "text-yellow-500" : candidate.status === "completed" ? "text-green-500" : "text-red-500"} } `}
                >
                  {ASSESSMENT_STATUSES_DICTIONARY[candidate.status]?.title}
                </p>
              </td>
              <td className="p-4">
                <div className="inline-flex justify-start">
                  <DropdownMenu
                    options={CANDIDATE_TABLE_OPTIONS}
                    readable_text_key="title"
                    callback={(_selected_option) => {
                      setActiveCandidateIndex(index);
                      handleAssessmentAction(candidate, _selected_option)
                    }
                    }
                    button={<TableActionButton />}
                  />
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      <div className="flex gap-4 items-center justify-between mt-2"></div>


      <CandidateDetailsDrawer
        candidate_data={data?.candidates[active_candidate_index]?.job_application}
        isDrawerOpen={is_candidate_details_drawer_open}
        closeDrawer={closeCandidateDetailsDrawer}
        openDrawer={openCandidateDetailsDrawer}
        moveToNext={moveToNextCandidate}
        moveToPrevious={moveToPreviousCandidate}
      />

    </>


  )
}
