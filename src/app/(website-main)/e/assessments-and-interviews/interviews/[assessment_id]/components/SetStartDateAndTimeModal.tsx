import { UseGetInterviewDetails } from '@/app/(website-main)/e/assessments-and-interviews/create-interview/misc/api/getInterviewDetails'
import { Button, DateTimePicker, Modal } from '@/components/shared'
import { Axios } from '@/lib/api/axios'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import { useParams } from 'next/navigation'
import React, { useEffect } from 'react'
import { Controller, useForm } from 'react-hook-form'
import toast from 'react-hot-toast'
import { z } from 'zod'


const SetStartDateAndTimeModalSchema = z.object({
    start_time: z.date(),
    deadline: z.date().optional(),
}).refine(data => {
    // Allow no deadline; when provided, ensure start_time precedes deadline
    if (!data.deadline) return true
    return data.start_time < data.deadline
}, {
    message: "Start time must be before deadline",
    path: ["start_time"]
})

export type SetStartDateAndTimeModalFormData = z.infer<typeof SetStartDateAndTimeModalSchema>
interface SetStartDateAndTimeModalProps {
    isModalOpen: boolean;
    closeModal: () => void;
}
const SetStartDateAndTimeModal: React.FC<SetStartDateAndTimeModalProps> = ({ isModalOpen, closeModal }) => {
    const params = useParams()

    // Load existing values when editing
    const { data: interview } = UseGetInterviewDetails(String(params.assessment_id || ''))

    const { control, handleSubmit, watch, reset, formState: { errors }, } = useForm<SetStartDateAndTimeModalFormData>({
        resolver: zodResolver(SetStartDateAndTimeModalSchema),
        defaultValues: {
            start_time: new Date(),
            deadline: new Date(),
        },
        mode: 'onChange'
    })

    useEffect(() => {
        if (interview) {
            const start = interview.start_time ? new Date(interview.start_time as unknown as string) : new Date()
            const end = interview.deadline ? new Date(interview.deadline as unknown as string) : new Date()
            reset({ start_time: start, deadline: end })
        }
    }, [interview, reset])
    const { mutate: setTimes } = useMutation({
        mutationFn: async () => {
            await Axios.patch(`assessments/assessment-interview/${params.assessment_id}/`, {
                start_time: watch('start_time'),
                deadline: watch('deadline')
            })
        },
    })
    const submitForm = () => {
        setTimes(undefined, {
            onSuccess() {
                closeModal()
                toast.success('Start date and time set successfully')
            },
        })
    }

    return (
        <Modal
            heading='Set Start Date and Time'
            isModalOpen={isModalOpen}
            closeModal={closeModal}
            allowDismiss={false}
        >
            <form className="space-y-4" onSubmit={handleSubmit(submitForm)}>
                <div className="space-y-1">
                    <h3 className="text-header-text text-sm font-semibold">Date and time settings</h3>
                    <p className="text-xxs md:text-[0.735rem] text-body-text">
                        Specify the start and end date and time for candidates to start and
                        complete this assessment.
                    </p>
                </div>
                <Controller
                    name="start_time"
                    control={control}
                    render={({ field }) => (
                        <DateTimePicker
                            label="Start date and time"
                            labelClassName='!font-normal text-[0.82rem]'
                            className="max-w-[350px] text-sm"
                            value={field.value || new Date()}
                            onChange={field.onChange}
                            hasError={!!errors.start_time}
                            errorMessage={errors.start_time?.message}
                            variant="showcase"
                        />
                    )}
                />

                <Controller
                    name="deadline"
                    control={control}
                    render={({ field }) => (
                        <DateTimePicker
                            label="End date and time"
                            labelClassName='!font-normal text-[0.82rem]'
                            className="max-w-[350px] text-sm "
                            value={field.value || new Date()}
                            onChange={field.onChange}
                            hasError={!!errors.deadline}
                            errorMessage={errors.deadline?.message}
                            variant="showcase"
                        />
                    )}
                />

                <div className="flex justify-center items-center p-5">
                    <Button
                        className="w-full"
                        type="submit"
                    >
                        Set date and time
                    </Button>
                </div>

            </form>

        </Modal>
    )
}

export default SetStartDateAndTimeModal