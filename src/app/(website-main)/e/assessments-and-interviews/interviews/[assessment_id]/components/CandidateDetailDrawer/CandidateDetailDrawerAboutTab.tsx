
import React from 'react'
import { CandidateAboutResult } from '../../../../misc/types/create-assessments'

interface Props {
    data: CandidateAboutResult;
}


const CandidateDetailsAboutTabs: React.FC<Props> = ({data}) => {


    return (
        <section className="max-w-full p-4 md:py-6 md:px-8">
            <div className="md:grid grid-cols-2 gap-5 overflow-scroll h-full">
                <p className="flex flex-col">
                    <span className="text-body-text text-xs">Name</span>
                    <span className="text-header-text text-sm font-semibold">{data?.job_application?.name}</span>
                </p>
                <p className="flex flex-col">
                    <span className="text-body-text text-xs">Email</span>
                    <span className="text-header-text text-sm font-semibold">{data?.job_application?.email}</span>
                </p>
                <p className="flex flex-col">
                    <span className="text-body-text text-xs">Current Location</span>
                    <span className="text-header-text text-sm font-semibold">{data?.job_application?.current_location}</span>
                </p>
                <p className="flex flex-col">
                    <span className="text-body-text text-xs">Years of experience</span>
                    <span className="text-header-text text-sm font-semibold">{data?.job_application?.years_of_experience}</span>
                </p>
                <p className="flex flex-col">
                    <span className="text-body-text text-xs">Phone number</span>
                    <span className="text-header-text text-sm font-semibold">{data?.job_application?.phone_number}</span>
                </p>

                {data?.job_application?.linkedin_link && (
                    <p className="flex flex-col">
                        <span className="text-body-text text-xs">Linkendin link</span>
                        <a className="text-header-text text-sm font-semibold" href={data?.job_application?.linkedin_link}>
                            {data?.job_application?.linkedin_link}
                        </a>
                    </p>
                )}
                {data?.job_application?.portfolio_link && (
                    <p className="flex flex-col">
                        <span className="text-body-text text-xs">Portfolio link</span>
                        <a className="text-header-text text-sm font-semibold" href={data?.job_application?.portfolio_link}>
                            {data?.job_application?.linkedin_link}
                        </a>
                    </p>
                )}

                {data &&
                    <>
                        {data.skills?.length > 0 && (
                            <div className="flex flex-col mt-4 col-span-2">
                                <p className="text-header-text text-sm font-semibold">Skills:</p>
                                <div className="flex items-center gap-3 flex-wrap">
                                    {data.skills.map((skill, index) => {
                                        return (
                                            <article key={index} className="bg-primary-light px-4 py-1.5 rounded-full text-primary text-sm w-max">
                                                {skill}
                                            </article>
                                        );
                                    })}
                                </div>
                            </div>
                        )}

                        {data.job_application?.matching_reason && (
                            <div className="flex flex-col col-span-2">
                                <p className="text-header-text text-sm font-semibold">Match: {data.job_application?.percentage_match}% - Reason:</p>
                                <span className="text-header-text text-sm">{data.job_application?.matching_reason}</span>
                            </div>
                        )}
                    </>
                }
            </div>
        </section>
    )
}

export default CandidateDetailsAboutTabs
