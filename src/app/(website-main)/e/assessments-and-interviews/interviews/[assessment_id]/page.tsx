//@ts-nocheck
"use client";

import Link from "next/link";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import EllipsesVerticalIcon from "@/components/icons/jsx/EllipsesVerticalIcon";
import {
  CANDIDATES_ASSESSMENT_STATUSES,
  QUESITON_TYPES,
  ROLE_LEVELS_DICTIONARY,
  SECTION_OPTIONS,
} from "@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants";
import { FormEvent, useState } from "react";
import app_fetch from "@/lib/api/appFetch.js";
import Button from "@/components/shared/Button";
import { CandidateAssessmentStatus, Question, Section, InterviewAssessment } from "@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments";
import Loader from "@/components/shared/loader";
import InviteCandidatesModal from "../../misc/components/InviteCandidatesModal";
import QuestionsListItem from "../../misc/components/QuestionsListItem";
import EditQuestionModal from "../../misc/components/EditQuestionModal";
import DeleteQuestionModal from "../../misc/components/DeleteQuestionModal";
import { deleteQuestionAPI, editQuestionAPI } from "../../misc/api/Questions";
import { deleteSectionAPI, editSectionAPI } from "../../misc/api/Sections";
import ListBox from "@/components/shared/listbox";
import { editAssessmentAPI } from "../../misc/api/Assessment";
import { downloadBlob } from "@/lib/utils/functions";
import { useQuery } from "@tanstack/react-query";
import { fetchRoleAPI } from "../../misc/api/Roles";
import AssessmentCandidatesList from "./components/AssessmentCandidatesList";
import { useDebouncedCallback } from 'use-debounce'
import { SearchNormal1 } from "iconsax-react";
import { LinkButton } from "@/components/shared";

export default function Dashboard(
  { params, searchParams: search_params }: {
    params: { assessment_id: string, }, searchParams?: {
      page?: string,
      search_filter?: string,
      status_filter?: string,
    }
  },
) {
  const path_name = usePathname()
  const searchParams = useSearchParams()
  const { replace } = useRouter()

  const tabs = [
    {
      title: "Candidates",
      query_param: undefined,
    },
    {
      title: "Interview Details",
      query_param: "assessment_details"
    },
    /* { */
    /*   title: "More Details", */
    /*   query_param: "more_details" */
    /* }, */
    /* { */
    /*   title: "Team Details", */
    /*   query_param: "team_details" */
    /* }, */
  ];

  /* const [is_loading_assessment, setIsLoadingAssessment] = useState(true); */
  const { isLoading: is_loading_assessment, data: assessment, isError: is_loading_assessment_error, isSuccess: is_loading_assessment_success, error: load_assessment_error, refetch: refetchAssessment } = useQuery({
    queryFn: fetchAssessment,
  })

  async function fetchAssessment(): Promise<InterviewAssessment> {
    return app_fetch(`assessments/assessment-interview/${params.assessment_id}`)
      .then((response) => response.json())
  }

  const page = Number(search_params?.page || 1)
  const status_filter = search_params?.status_filter
  const search_filter = search_params?.search_filter
  const assessment_id = params.assessment_id

  function filterData(filter_term: string | undefined, filter_key: "search_filter" | "status_filter" | "page") {
    const params = new URLSearchParams(searchParams)
    params.set("page", "1")
    if (filter_term) {
      params.set(filter_key, filter_term)
    }
    else {
      params.delete(filter_key)
    }
    replace(`${path_name}?${params.toString()}`)
  }

  const setSearchFilter = useDebouncedCallback((term) => {
    filterData(term, "search_filter")
  }, 300)

  function setStatusFilter(status: string | undefined) {
    filterData(status, "status_filter")
  }

  function gotoCandidatePrevPage() {
    filterData(String(page - 1), "page")
  }

  function gotoCandidateNextPage() {
    filterData(String(page + 1), "page")
  }


  function clearFilters() {
    replace(`${path_name}`)
  }


  const findTabFromQueryParams = () => { switch (useSearchParams().get("tab")) { case "assessment_details": return 1; case "team_details": return 2; case "more_details": return 2; default: return 0; } }
  const [current_tab, setCurrentTab] = useState(findTabFromQueryParams());


  const [is_assessment_time_form_busy, setIsAssessmentTimeFormBusy] = useState(false);

  const [is_assessment_details_form_busy, setIsAssessmentDetailsFormBusy] = useState(false);

  function editAssessmentDetailsSettings(e: FormEvent) {
    e.preventDefault();
    setIsAssessmentDetailsFormBusy(true)
    editAssessment(['name', 'description']).finally(() => {
      setIsAssessmentDetailsFormBusy(false);
      setIsEditAssessmentDetails(false);
    })
  }

  type keyofAssessment = keyof InterviewAssessment
  function editAssessment(parts: keyofAssessment[]) {

    const request_body = {} as typeof assessment;

    parts.map((field) => {
      // @ts-ignore
      request_body[field] = assessment[field]
    })
    request_body!.id = assessment!.id

    return editAssessmentAPI(request_body);
  }



  const [active_question_index, setActiveQuestionIndex] = useState(0);
  const [active_question, setActiveQuestion] = useState(
    assessment?.sections?.[active_question_index],
  );

  /* useEffect(() => { */
  /*   setActiveSection(assessment?.sections?.[active_section_index]); */
  /* }, [active_section_index, assessment.sections]); */

  const [is_edit_section_modal_open, setIsEditSectionModalOpen] = useState(
    false,
  );
  function closeEditSectionModal() {
    setIsEditSectionModalOpen(false);
    setActiveQuestionIndex(0);
  }
  function openEditSectionModal() {
    setIsEditSectionModalOpen(true);
  }

  function editSection(section: Section, section_index: number) {
    return new Promise((resolve, reject) => {
      const question_types_counts = {} as Record<string, number>;
      QUESITON_TYPES.map((_question_type) => {
        // @ts-ignore
        question_types_counts[_question_type.count_name] = section[_question_type.count_name] || 0;
      });

      editSectionAPI(section, assessment!.id)
        .then((response) => response.json())
        .then(() => {
          const new_sections = [...assessment!.sections];
          // @ts-ignore
          new_sections[section_index] = section;
          refetchAssessment()
          resolve({ message: "success" });
        })
        .catch((error) => {
          reject(error);
        });

    })
  }

  const [is_edit_timing_settings, setIsEditTimingSettings] = useState(false);
  const [is_edit_assessment_details, setIsEditAssessmentDetails] = useState(false);
  const [is_edit_proctoring_settings, setIsEditProctoringSettings] = useState(false);

  const [is_editing_section, setIsEditingSection] = useState(false);

  function handleEditSection(section: Section) {
    setIsEditingSection(true);
    editSection(section, active_question_index)
      .finally(() => {
        setIsEditingSection(false);
        closeEditSectionModal();
      });
  }

  const [is_delete_section_modal_open, setIsDeleteSectionModalOpen] = useState(
    false,
  );
  function openDeleteSectionModal() {
    setIsDeleteSectionModalOpen(true);
  }
  function closeDeleteSectionModal() {
    setIsDeleteSectionModalOpen(false);
  }

  function deleteSection(section_index: number) {
    return new Promise((resolve, reject) => {
      const section = assessment!.sections[section_index];
      /* send a delete request */
      deleteSectionAPI(section.id, assessment!.id)
        .then((_result) => {
          /* if success, delete the section entry from the assessment */
          const new_sections = assessment!.sections.splice(section_index, 1);
          refetchAssessment()
          closeDeleteSectionModal()
          resolve({ message: "success" })
        })
        .catch(e => reject(e))
        .finally(() => {
          closeDeleteSectionModal
        })
    })
  }

  function handleSectionOption(_selected_option: typeof SECTION_OPTIONS[0]) {
    if (_selected_option.title == "edit section") {
      openEditSectionModal();
    }
    if (_selected_option.title == "delete section") {
      openDeleteSectionModal();
    }
  }


  const [is_edit_question_modal_open, setIsEditQuestionModalOpen] = useState(false,);
  function closeEditQuestionModal() {
    setIsEditQuestionModalOpen(false);
  }
  function openEditQuestionModal() {
    setIsEditQuestionModalOpen(true);
  }

  const [is_editing_question, setIsEditingQuestion] = useState(false);

  function handleEditQuestion(edited_question: Question) {
    setIsEditingQuestion(true);
    editQuestionAPI(edited_question, active_question.id, assessment!.id)
      .then(_result => {
        refetchAssessment()
      })
      .finally(() => { setIsEditingQuestion(false); closeEditQuestionModal() });
  }

  function deleteQuestion(question_index: number, section_index: number) {
    return new Promise((resolve, reject) => {
      const active_question = assessment!.questions[question_index]
      deleteQuestionAPI(active_question.id, active_question.id)
        .then(() => {
          refetchAssessment()
          resolve({ message: "success" })
        })
        .catch(e => reject({ error: e }))
    })
  }

  const [is_delete_question_modal_open, setIsDeleteQuestionModalOpen] =
    useState(
      false,
    );
  function openDeleteQuestionModal() {
    setIsDeleteQuestionModalOpen(true);
  }
  function closeDeleteQuestionModal() {
    setIsDeleteQuestionModalOpen(false);
  }

  const [is_invite_candidates_modal_open, setIsInviteCandidatesModalOpen] = useState(false);
  function openInviteCandidatesModal() {
    setIsInviteCandidatesModalOpen(true)
  }
  function closeInviteCandidatesModal() {
    setIsInviteCandidatesModalOpen(false)
  }

  const [is_sending_report, setIsSendingReport] = useState(false);
  async function sendResult() {
    setIsSendingReport(true);
    const options = {
      method: "POST",
      body: JSON.stringify({
        "get_questions": "true",
      }),
    };
    app_fetch(
      `assessments/export-assessment-report/${params.assessment_id}/`,
      options,
    )
      .then((response) => response.blob()).then(result => {
        downloadBlob(result, `getLinked_${params.assessment_id}.xlsx`)
        setIsSendingReport(false);
      });
  }

  const roles_query = useQuery({
    queryKey: ["roles", assessment?.role],
    queryFn: async () => await fetchRoleAPI(assessment!.role),
    enabled: assessment?.role !== undefined
  })

  return (
    <div className="-mt-3 space-y-4">
      <section className="flex max-md:flex-wrap gap-2 items-end justify-between bg-white px-2 py-4">
        <div className="flex items-start gap-4">
          <LinkButton
            href="/e/assessments-and-interviews/interviews-library"
            variant="extralight"
            size="capsule"
          >
            Back
          </LinkButton>
          <div className="space-y-1">
            <h1 className={`${is_loading_assessment ? "skeleton-loading" : ''} heading-2`}>{assessment?.name}</h1>
            <ul className="heading-text flex gap-2 text-sm">

              {roles_query.isSuccess && (
                <li className="font-medium">
                  <span className="helper-text max-md:block font-normal">Role: </span>
                  {roles_query.data.name}
                </li>
              )}
              <li className="font-medium">
                <span className="helper-text max-md:block font-normal">Role level: </span>
                <span className={`${is_loading_assessment ? "skeleton-loading" : ''} capitalize`}> {ROLE_LEVELS_DICTIONARY[assessment?.role_level || ""]?.name} </span>
              </li>
              <li className="font-medium">
                <span className="helper-text max-md:block font-normal">
                  Total questions:{" "}
                </span>
                <span className={`${is_loading_assessment ? "skeleton-loading" : ''}`}> {assessment?.total_questions} </span>
              </li>
            </ul>
          </div>
        </div>
        <div className="flex gap-2 max-md:ml-auto">
          <p>{assessment?.is_published}</p>
          {assessment?.is_published !== undefined && (
            <>
              {assessment?.is_published ? (
                <>
                  <div className="relative group/invite-btn">
                    <button className="btn-primary-light" onClick={openInviteCandidatesModal} disabled={assessment.commencement_settings == "yes" && (new Date(assessment.deadline!) < new Date())}>
                      Invite candidates
                    </button>
                    {
                      (assessment.commencement_settings == "yes" && (new Date(assessment.deadline || Date.now()) < new Date()) && (
                        <span className="hidden group-hover/invite-btn:flex w-max absolute shadow-lg -bottom-10 p-2 -translate-x-[25%] rounded-xl bg-grey text-center items-center justify-center">
                          You cannot invite after the deadline.
                        </span>
                      ))
                    }
                  </div>
                </>
              ) : (
                <>
                  <Link href={`${path_name}/${'edit'}`} className="btn-primary-light">Continue editing</Link>
                </>
              )}
            </>

          )}
          {/* <button className="btn-primary-light" title="options">
            <EllipsesVerticalIcon />
          </button> */}
        </div>
      </section>

      <div className="space-y-1 text-sm">
        <section className="flex max-md:flex-wrap max-md:justify-center gap-4 rounded-xl bg-white p-2 md:p-8">
          <ul className="flex items-center divide-x rounded-xl border p-2 text-center">
            <li className="px-4">
              <div className="space-y-1">
                <p className={`${is_loading_assessment ? "skeleton-loading" : ''} heading-1`}>{assessment?.total_invites || 0}</p>
                <p className="text-xs">Invites</p>
              </div>
            </li>
            <li className="px-4">
              <div>
                <p className={`${is_loading_assessment ? "skeleton-loading" : ''} heading-1`}>
                  {assessment?.completed}
                  <span className={`text-xxs font-normal ${is_loading_assessment ? '' : 'text-green-500'}`}>
                    /{assessment?.no_candidates ? Math.round((assessment.completed / assessment?.no_candidates) * 100) : 100}%
                  </span>
                </p>
                <p className="text-xs">Completed</p>
              </div>
            </li>
            <li className="px-4">
              <div>
                <p className={`${is_loading_assessment ? "skeleton-loading" : ''} heading-1`}>
                  {assessment?.ongoing}
                  <span className={`text-xxs font-normal ${is_loading_assessment ? '' : 'text-yellow-500'}`}>
                    /{assessment?.no_candidates ? Math.round((assessment.ongoing / assessment?.no_candidates) * 100) : 100}%
                  </span>
                </p>
                <p className="text-xs">Started</p>
              </div>
            </li>
            <li className="px-4">
              <div>
                <p className={`${is_loading_assessment ? "skeleton-loading" : ''} heading-1`}>
                  {assessment?.not_started}
                  <span className={`text-xxs font-normal ${is_loading_assessment ? '' : 'text-red-500'}`}>
                    /{assessment?.no_candidates ? Math.round((assessment.not_started / assessment?.no_candidates) * 100) : 100}%
                  </span>
                </p>
                <p className="text-xs">Not started</p>
              </div>
            </li>
            <li className="px-4">
              <div className="space-y-1">
                <p className={`${is_loading_assessment ? "skeleton-loading" : ''} heading-1`}>
                  {Math.floor((assessment?.average_score || 0) * 100) / 100}
                </p>
                <p className="text-xs">Average score</p>
              </div>
            </li>
          </ul>

        </section>
        {
          !is_loading_assessment && (
            <section className="flex items-center justify-between rounded-xl bg-white px-4">
              <div className="rounded-md pt-2 bg-white px-4">
                <nav className="">
                  <ul className="flex gap-4 items-center">
                    {tabs.map((tab, index) => (
                      <li
                        key={index}
                        className={index == current_tab
                          ? "border-b-4 border-primary text-primary p-4 pb-2"
                          : ""}
                      >
                        <label className="cursor-pointer">
                          <input
                            type="radio"
                            name="active tab"
                            className="hidden"
                            value={index}
                            onChange={(e) => {
                              setCurrentTab(parseInt(e.target.value))
                            }}
                          />
                          {tab.title}
                        </label>
                      </li>
                    ))}
                  </ul>
                </nav>
              </div>
              {(current_tab == 0 && assessment?.candidates?.length) && (
                <div className="flex items-center gap-12">
                  <div className="flex gap-2">
                    <div>
                      <ListBox
                        options={CANDIDATES_ASSESSMENT_STATUSES}
                        className="border"
                        placeholder="Status"
                        // @ts-ignore
                        active_option={status_filter} setActiveOption={(status) => { setStatusFilter(status as CandidateAssessmentStatus) }} value_key="value" readable_text_key="title"
                      />
                    </div>
                    <div className="btn-bordered flex items-center gap-2 py-0">
                      <SearchNormal1 />
                      <input
                        type="text"
                        name="search assessments"
                        placeholder="Search"
                        className="input-base"
                        defaultValue={searchParams.get("search_filter")?.toString()}
                        onChange={(e) => setSearchFilter(e.target.value)}
                      />
                    </div>
                    {/* //todo */}
                    {(status_filter || search_filter) && (
                      <button onClick={clearFilters} className="btn-bordered">Clear filters</button>
                    )}
                    <Button
                      is_busy={is_sending_report}
                      className="btn-bordered"
                      onClick={sendResult}
                    >
                      Export
                    </Button>
                  </div>
                </div>
              )}
            </section>
          )
        }

        {/* candidates */}
        {
          current_tab == 0 && (
            <section className="rounded-xl bg-white p-4">
              {
                is_loading_assessment ? (<Loader />) :
                  assessment?.candidates?.length == 0 ? (
                    <div className="flex  items-center justify-center">
                      <div className="rounded-md bg-gradient-to-b from-[#755ae20a] via-transparent to-transparent p-12 border border-[#DADADA]/20 text-center">
                        <div className="container max-w-xs space-y-2">
                          <div className="container flex justify-center">
                            <img
                              src="/images/create-assessments/no-assessment.png"
                              alt="writing with pen"
                            />
                          </div>
                          {
                            (!assessment.candidates.length) ? (
                              <>
                                <h2>This assessment has no candidates</h2>
                              </>
                            ) : (
                              <>
                                <h2>No candidate matches your filters</h2>
                                <p className="text-sm text-[#7D8590]">clear the filters to get more results</p>
                                <button className="btn-primary" onClick={clearFilters}>Clear filters</button>
                              </>
                            )
                          }
                        </div>
                      </div>
                    </div>
                  ) : (
                    <AssessmentCandidatesList gotoCandidatePrevPage={gotoCandidatePrevPage} gotoCandidateNextPage={gotoCandidateNextPage} isSuccess={is_loading_assessment_success} refetch={refetchAssessment} data={{ candidates: assessment?.candidates || [] }} isLoading={is_loading_assessment} isError={is_loading_assessment_error} error={load_assessment_error} />
                  )
              }
            </section>
          )
        }
        {
          current_tab == 1 && (
            <section>
              {is_loading_assessment ? (<Loader />) : (
                <div className="grid md:grid-cols-2 gap-2 text-base items-start">
                  <section className="bg-white p-4 rounded-xl space-y-4">
                    <div>
                      <h2 className="heading-text">Questions</h2>
                      <p className="helper-text text-sm">Preview and reposition interview questions</p>
                    </div>
                    <div>
                      {
                        assessment?.questions.length == 0 ? (
                          <div className="grid py-20 flex-1 place-content-center">
                            <div className="rounded-md bg-gradient-to-b from-[#755ae20a] via-transparent to-transparent p-12 border border-[#DADADA]/20 text-center">
                              <div className="container max-w-xs space-y-2">
                                <div className="container flex justify-center">
                                  <img
                                    src="/images/create-assessments/no-assessment.png"
                                    alt="writing with pen"
                                  />
                                </div>
                                <h2>This Interview has no questions</h2>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <ul className="space-y-1">
                            {assessment?.questions
                              .map((question, question_index) => (

                                <li key={question_index}>
                                  <QuestionsListItem
                                    question={question}
                                    deleteQuestion={() => {
                                      setActiveQuestionIndex(question_index);
                                      openDeleteQuestionModal();
                                    }}
                                    index={question_index}
                                    editQuestion={() => {
                                      setActiveQuestionIndex(question_index);
                                      openEditQuestionModal();
                                    }}
                                  />
                                </li>
                              ))}
                          </ul>
                        )
                      }

                    </div>
                  </section>
                  <section className="bg-white p-4 rounded-xl space-y-4 text-sm">
                    <div className="flex items-start justify-between">
                      <div>
                        <h2 className="heading-text">Welcome details and description</h2>
                        <p className="header-text text-sm">Assessment intro and description</p>
                      </div>
                      {!is_edit_assessment_details && (
                        <button type="button" className="btn-primary-bordered" onClick={e => { e.preventDefault(); setIsEditAssessmentDetails(true) }}>
                          Edit
                        </button>
                      )}
                    </div>
                    <form className="space-y-6" onSubmit={editAssessmentDetailsSettings}>
                      <div>
                        <label className="space-y-2">
                          <p className="">Assessment name</p>
                          <input
                            type="text"
                            disabled={!is_edit_assessment_details}
                            placeholder="Assessment name"
                            className="input-grey"
                            value={assessment?.name}
                            onChange={(e) => {
                              // TODO
                              /* setAssessment({ ...assessment, name: e.target.value }) */
                            }}
                          />
                        </label>
                      </div>
                      <div>
                        <label className="space-y-2">
                          <p className="">Assessment Intro message / description</p>
                          <textarea
                            required
                            disabled={!is_edit_assessment_details}
                            value={assessment?.description}
                            onChange={(e) => {
                              // TODO
                              /* setAssessment({ ...assessment, description: e.target.value }) */
                            }}
                            placeholder="Enter details of this assessment to guide candidates"
                            className="min-h-[100px] input-grey"
                            maxLength={400}
                          />
                        </label>
                        {is_edit_assessment_details && (
                          <p className="text-right text-xxs">
                            {assessment?.description?.length || 0}/400 characters remaining
                          </p>
                        )}
                      </div>
                      {is_edit_assessment_details && (
                        <Button className="btn-primary" type="submit" is_busy={is_assessment_details_form_busy}>Save and Update</Button>
                      )}
                    </form>
                  </section>
                </div>
              )}
            </section>
          )
        }

        {
          current_tab == 3 && (
            <p>Team  Details</p>
          )
        }

      </div>


      <EditQuestionModal
        is_busy={is_editing_question}
        is_open={is_edit_question_modal_open}
        close={closeEditQuestionModal}
        question={assessment?.questions?.[active_question_index]!}
        onSave={handleEditQuestion}
      />

      <DeleteQuestionModal
        is_open={is_delete_question_modal_open}
        close={closeDeleteQuestionModal}
        section_index={active_question_index}
        question_index={active_question_index}
        onDelete={(question_index, section_index) => deleteQuestion(question_index, section_index)}
      />

      <InviteCandidatesModal
        assessment_id={assessment?.id}
        is_open={is_invite_candidates_modal_open}
        onSuccess={refetchAssessment}
        close={closeInviteCandidatesModal}
        is_interview={true}
      />
    </div>
  );
}
