"use client";

import Link from "next/link";
import { FormEvent, useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import React, { useCallback } from "react";
import { useDropzone } from "react-dropzone";

import Button from "@/components/shared/Button";
import { useCreateAssessmentStore } from "../../../../misc/store/createAssessmentStore";
import app_fetch from "@/lib/api/appFetch.js";
import UploadIcon from '@/app/(website-main)/t/showcase/misc/components/cv-upload/icons/UploadIcon';
import Checkbox from "@/components/shared/Checkbox/Checkbox";
import { formatBytes } from "@/lib/utils/functions";
import { AnalyzedFileErrorType } from "../../../../misc/types/create-assessments";
import SetStartDateAndTimeModal from "../../components/SetStartDateAndTimeModal";
import { useBooleanStateControl } from "@/hooks";



export default function Invitations({ searchParams }: { searchParams: { back?: string } }) {
  const router = useRouter();
  const assessment = useCreateAssessmentStore((state) => state);
  const INVITATION_OPTIONS = [
    {
      id: 0,
      title: "input invite",
    },
    {
      id: 1,
      title: "bulk invite",
    },
    {
      id: 2,
      title: "invite from candidate pool",
    },
  ];

  const {
    state: isDateAndTimeMoalOpen,
    setTrue: openDateAndTimeModal,
    setFalse: closeDateAndTimeModal,
  } = useBooleanStateControl(true)
  React.useEffect(() => {
    // if (assessment.start_time && assessment.end_time) {
      openDateAndTimeModal()
    // }
  }, [])

  const path_name = usePathname();
  const [active_invitation_option_id, setActiveInvitationOptionID] = useState(0);
  const [is_changing_page, setIsChangingPage] = useState(true)

  useEffect(() => {
    setIsChangingPage(false)
  }, [])

  function handleProceed() {
    setIsChangingPage(true)
    router.push(`${path_name.replace("edit/invitations", "edit/summary")}`);
  }

  function submitEmailInvite(e: FormEvent) {
    e.preventDefault();
    assessment.setChoosenInvitationType("email");
    handleProceed();
  }

  // file analysis result
  type AnalyzedFileType = {
    fields: string[];
    field_details: Record<string, number>;
    names_and_emails: { name: string, email: string }[];
  }

  const [analyzed_file_details, setAnalyzedFileDetails] = useState({} as AnalyzedFileType);
  const [is_analyzing_file, setIsAnalyzingFile] = useState(false)
  const [analysis_errors, setAnalysisErrors] = useState<AnalyzedFileErrorType | undefined>(undefined)

  function clearFile() {
    assessment.setFileDetails(undefined);
    setIsAnalyzingFile(false)
    setAnalysisErrors(undefined)
    setAnalyzedFileDetails({} as AnalyzedFileType);
    assessment.setSelectedFileFields([]);
  }

  function uploadFileForAnalysis(acceptedFiles: any) {
    setIsAnalyzingFile(true)
    setAnalysisErrors(undefined)
    const formdata = new FormData();
    formdata.append("file", acceptedFiles[0]);

    assessment.setFileDetails(acceptedFiles[0]);

    const requestOptions = {
      method: "POST",
      body: formdata,
    };

    app_fetch("assessments/analyze-excel/", requestOptions, false)
      .then((response) => response.json())
      .then((result) => {
        if (result.error) {
          throw result
        }
        setAnalyzedFileDetails(result);
        assessment.setInvitations(result.names_and_emails);
      })
      .catch((error) => {
        setAnalysisErrors(error)
      }).finally(() => {
        setIsAnalyzingFile(false)
      });
  }


  function submitBulkInvite(e: FormEvent) {
    e.preventDefault();
    assessment.setChoosenInvitationType("file");

    if (assessment.selected_file_fields.length < 2) {
      return;
    }
    handleProceed();
  }

  const onDrop = useCallback((acceptedFiles: any) => {
    uploadFileForAnalysis(acceptedFiles);
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [],
      "application/vnd.ms-excel": [],
    },
    maxSize: 10 * 1024 * 1024, //10mb
  });

  function submitPoolInvite(e: FormEvent) {
    e.preventDefault();
    assessment.setChoosenInvitationType("pool");
    handleProceed();
  }
  function getBackLink() {

    if (searchParams.back) {
      return searchParams.back
    }
    return path_name.replace("/invitations", "/")

  }

  return (
    <div className="flex max-md:flex-col gap-4 pb-4 pr-4">
      <p>
        <Link
          href={getBackLink()}
          className="btn-primary-light-pill bg-white"
        >
          Back
        </Link>
      </p>
      <div className="flex-1">
        <div className="mx-auto max-w-4xl">
          <div className="space-y-8 rounded-t-xl bg-white p-4 px-12">
            <section className="space-y-4">
              <div>
                <h1 className="heading-1">Invite Candidates</h1>
                <p>Invite and manage interview invitations</p>
              </div>
            </section>
            <nav className="tab-nav max-md:flex-col w-full flex-1">
              <ul className="tab-list capitalize space-x-2">
                {INVITATION_OPTIONS.map((item, index) => (
                  <li
                    key={index}
                    className="!px-0"
                  >
                    <button
                      onClick={() => {
                        setActiveInvitationOptionID(item.id);
                      }}
                      className={`capitalize !px-12  ${active_invitation_option_id == item.id
                        ? "active-tab-item"
                        : "mx-2 text-primary"
                        }`}
                    >
                      {item.title}
                    </button>
                  </li>
                ))}
              </ul>
            </nav>
            <section className="space-y-4">
              {/* input invite */}
              {active_invitation_option_id == 0 && (
                <>
                  <p className="helper-text">
                    Enter candidates name and email to invite them to your
                    interview.
                  </p>
                  <form className="space-y-4">
                    {assessment.invitations.map((invitation, index) => (
                      <div className="grid md:grid-cols-2 max-md:outline outline-gray-50 max-md:p-2 max-md:rounded-md gap-4" key={index}>
                        <label className="space-y-2">
                          <p className="">Name</p>
                          <input
                            type="text"
                            value={invitation.name}
                            onChange={(e) => {
                              const _invitations = [...assessment.invitations];
                              _invitations[index].name = e.target.value;
                              assessment.setInvitations(_invitations);
                            }}
                            placeholder="Enter name"
                            className="input-grey"
                          />
                        </label>
                        <label className="space-y-2">
                          <p className="">Email</p>
                          <input
                            type="text"
                            value={invitation.email}
                            onChange={(e) => {
                              const _invitations = [...assessment.invitations];
                              _invitations[index].email = e.target.value;
                              assessment.setInvitations(_invitations);
                            }}
                            placeholder="Enter email"
                            className="input-grey"
                          />
                        </label>
                      </div>
                    ))}
                    <button
                      className="btn-primary-light"
                      type="button"
                      onClick={() =>
                        assessment.setInvitations([
                          ...assessment.invitations,
                          { name: "", email: "" },
                        ])}
                    >
                      + Add more
                    </button>
                    <p className="flex justify-end gap-2">
                      <button
                        className="btn-primary px-20"
                        type="button"
                        onClick={submitEmailInvite}
                      >
                        Add candidates and continue
                      </button>
                    </p>
                  </form>
                </>
              )}

              {/* file invite */}
              {active_invitation_option_id == 1 && (
                <>
                  <p className="helper-text">
                    Here you can invite candidates in bulk by uploading an excel
                    file containing candidate email and other desired details
                  </p>
                  <div className="flex gap-2">
                    <a
                      className="btn-primary-light"
                      href="/files/assessment-and-interviews/getlinked_sample_invitations_template.xlsx"
                      target="_blank"
                    >
                      Download sample file
                    </a>
                    <Link href="#" className="btn-primary-transparent">
                      Learn more
                    </Link>
                  </div>

                  {!assessment.file_details && (
                    <div
                      className={`${false ? "border border-red-600" : ""
                        } mt-3 flex h-[5.9375rem] max-w-[60%] cursor-pointer items-center gap-4 rounded-[0.875rem] border-[0.3px] border-dashed border-[rgb(117,90,226)] p-6`}
                      {...getRootProps()}
                    >
                      <div className="">
                        <UploadIcon />
                      </div>
                      <div className="">
                        <p className="font-sans text-xs font-semibold text-[#755AE2] sm:text-sm">
                          Tap to upload invite document
                        </p>
                        <span className="font-sans text-xs font-semibold text-[#755AE2] opacity-75">
                          Files types: excel, Max size: 10MB
                        </span>
                      </div>
                      <input hidden {...getInputProps()} />
                    </div>
                  )}

                  {assessment.file_details && (
                    <>
                      <div className="p-4 max-w-[60%] rounded-md bg-light-accent-bg text-xs">
                        <div className="flex items-start justify-between">
                          <div className="flex gap-2 items-center">
                            <div>
                              <img
                                src="/images/icons/excel.png"
                                alt="excel icon"
                              />
                            </div>
                            <div>
                              <p className="font-bold">
                                {assessment.file_details?.name}
                              </p>
                              <p className="text-[#4E4E4E]">
                                {formatBytes(assessment.file_details?.size)}
                              </p>
                            </div>
                          </div>
                          <button onClick={clearFile}>
                            <span className="bg-white rounded-full w-7 aspect-square flex items-center justify-center text-lg">
                              &times;
                            </span>
                          </button>
                        </div>
                      </div>
                      {Object.keys(analyzed_file_details).length > 0 && (
                        <>
                          <ul className="flex max-w-[60%] gap-3 items-center capitalize bg-light-accent-bg p-4 rounded-md">
                            {analyzed_file_details.fields.map((
                              field,
                              index,
                            ) => (
                              <li key={index}>
                                <p className="text-[#3E3873]">
                                  {field}: {" "}
                                  <span className="text-primary">
                                    {analyzed_file_details.field_details[field]}
                                  </span>
                                </p>
                              </li>
                            ))}
                          </ul>
                          <p className="helper-text">
                            Determine how candidates get to access the
                            interviews by selecting from the options below.The
                            default option is ‘Passcode’ as this is mandatory to
                            access interview.
                          </p>

                          <div className="flex items-center gap-4">
                            {analyzed_file_details.fields.map((
                              field,
                              index,
                            ) => (
                              <label
                                key={index}
                                className="text-sm p-4 flex items-center gap-2 bg-light-accent-bg rounded-md"
                              >

                                <Checkbox
                                  checked={assessment.selected_file_fields
                                    .includes(field)}
                                  disabled={assessment.selected_file_fields
                                    .length > 1 &&
                                    !assessment.selected_file_fields.includes(
                                      field,
                                    )}
                                  onChange={(e) => {
                                    if (
                                      assessment.selected_file_fields.includes(
                                        e.target.value,
                                      )
                                    ) {
                                      const _selected = assessment
                                        .selected_file_fields.filter(
                                          (field) => field != e.target.value,
                                        );
                                      assessment.setSelectedFileFields(
                                        _selected,
                                      );
                                    } else {
                                      assessment.setSelectedFileFields([
                                        ...assessment.selected_file_fields,
                                        e.target.value,
                                      ]);
                                    }
                                  }}
                                  name="auth fields"
                                  value={field}
                                />
                                {field}
                              </label>
                            ))}
                          </div>
                        </>
                      )}
                    </>
                  )}

                  {analysis_errors && (
                    <div className="p-2 text-sm bg-red-200/20 rounded-md text-red-500">
                      <p className="font-bold">{analysis_errors.error}</p>
                      <ul>
                        {Object.keys(analysis_errors.details).map((row, index) => {
                          const error = analysis_errors.details[row]
                          return (
                            <div className="pl-2" key={index}>
                              <p> {row}: {error.email || "?"} - {error.error}</p>
                            </div>
                          )
                        })}
                      </ul>
                    </div>
                  )}


                  <Button
                    is_busy={is_changing_page || is_analyzing_file}
                    onClick={submitBulkInvite}
                    disabled={((assessment.selected_file_fields.length > 0 && assessment.selected_file_fields.length !== 2) || !!analysis_errors || is_analyzing_file)}
                    className={`px-20 ${((assessment.selected_file_fields.length > 0 && assessment.selected_file_fields.length !== 2) || !!analysis_errors || is_analyzing_file) ? "btn-primary-bordered"
                      : "btn-primary"
                      }`}
                    type="button"
                  >
                    {is_analyzing_file ? "Analyzing file" : "Add candidates and continue"}
                  </Button>
                </>
              )}

              {/* candidate pool */}
              {active_invitation_option_id == 2 && (
                <>
                  <p className="helper-text">
                    Here you can invite candidates grouped in your talent pool
                  </p>
                  <div>
                    <label className="space-y-2">
                      <p className="">Select candidate pool</p>
                      <input
                        type="text"
                        placeholder="Backend developer"
                        className="bg-light-accent-bg rounded-xl px-4 py-3"
                      />
                    </label>
                  </div>
                  <button
                    className="btn-primary"
                    onClick={submitPoolInvite}
                  >
                    Add candidates
                  </button>
                </>
              )}
            </section>
          </div>
        </div>
      </div>

      <SetStartDateAndTimeModal
        isModalOpen={isDateAndTimeMoalOpen}
        closeModal={closeDateAndTimeModal}
      />
    </div>
  );
}
