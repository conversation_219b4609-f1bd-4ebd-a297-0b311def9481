'use client';

import { ROLE_LEVELS_DICTIONARY } from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants';
import { useCreateAssessmentStore } from '@/app/(website-main)/e/assessments-and-interviews/misc/store/createAssessmentStore';
import { InterviewAssessment } from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import AssessmentSuccessModalIcon from '@/components/icons/AssessmentSuccessModalIcon';
import Modal from '@/components/Modal';
import Button from '@/components/shared/Button';
import app_fetch from '@/lib/api/appFetch';
import { secondsToLargestUnit } from '@/lib/utils/functions';
import Link from 'next/link';
import { useParams, usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import { publishInterview } from '../../../../misc/api/Assessment';
import CustomInvitationModal from '../../../../misc/components/CustomInvitationModal';
import InviteCandidatesModal from '../../../../misc/components/InviteCandidatesModal';

export default function Dashboard() {
  const params = useParams();

  const [assessment, setAssessment] = useState({} as InterviewAssessment);
  const assessment_store = useCreateAssessmentStore(state => state);
  const [questions_count, setQuestionsCount] = useState(0);
  const setAssessmentID = useCreateAssessmentStore(state => state.setId);

  useEffect(() => {
    app_fetch(`assessments/assessment-interview/${params.assessment_id}/`)
      .then(res => res.json())
      .then(res => {
        setAssessmentID(res.id);
        setQuestionsCount(res.total_questions);
        setAssessment(res);
      });
  }, []);

  const [time, setTime] = useState(0);
  useEffect(() => {
    const _time = assessment?.questions?.reduce((acc, curr) => {
      acc += curr.time;
      return acc;
    }, 0);

    setTime(_time || 0);
  }, [assessment]);

  const [is_success_modal_open, setIsSuccessModalOpen] = useState(false);
  function openSuccessModal() {
    setIsSuccessModalOpen(true);
  }

  const [is_invitees_modal_open, setIsInviteesModalOpen] = useState(false);
  function openInviteesModal() {
    setIsInviteesModalOpen(true);
  }
  function closeInviteesModal() {
    setIsInviteesModalOpen(false);
  }

  const [is_invite_candidates_modal_open, setIsInviteCandidatesModalOpen] =
    useState(false);
  function openInviteCandidatesModal() {
    setIsInviteCandidatesModalOpen(true);
  }
  function closeInviteCandidatesModal() {
    setIsInviteCandidatesModalOpen(false);
  }

  const [is_sending_invites, setIsSendingInvites] = useState(false);
  function submitAssessment() {
    if (
      assessment_store.invitations.filter(invitation => invitation.name !== '')
        .length === 0
    ) {
      openSuccessModal();
      return;
    }

    setIsSendingInvites(true);
    switch (assessment_store.choosen_invitation_type) {
      case 'file':
        assessment_store
          .sendBulkInvitation({ type: 'interview' })
          .then(() => {
            publishInterview(assessment.id);
            openSuccessModal();
          })
          .catch(() => {
            return;
          })
          .finally(() => {
            setIsSendingInvites(false);
          });
        break;
      case 'email':
        assessment_store
          .sendEmailInvitations({ type: 'interview' })
          .then(() => {
            publishInterview(assessment.id);
            openSuccessModal();
          })
          .catch(() => {
            return;
          })
          .finally(() => {
            setIsSendingInvites(false);
          });
        break;
      case 'pool':
        break;
    }
  }

  const [is_custom_invitation_modal_open, setIsCustomInvitationModalOpen] =
    useState(false);

  function openCustomInvitationModal() {
    setIsCustomInvitationModalOpen(true);
  }

  function closeCustomInvitationModal() {
    setIsCustomInvitationModalOpen(false);
  }

  function updateCustomInvitation(form_data: any) {
    assessment_store.setCustomEmailMessage(form_data.message);
    assessment_store.setCustomEmailSubject(form_data.subject);
    assessment_store.setCustomEmailCoverImage(form_data.cover_image);
  }

  const path_name = usePathname();

  return (
    <div className="flex gap-4 pb-4 pr-4 max-md:flex-col">
      <p>
        <Link
          href={`${path_name.replace('edit/summary', 'edit/invitations')}`}
          className="btn-primary-light-pill bg-white"
        >
          Back
        </Link>
      </p>
      <div className="flex-1">
        <div className="mx-auto max-w-3xl">
          <div className="space-y-8 rounded-t-xl bg-white p-4 px-12">
            <section className="space-y-4">
              <h1 className="heading-1">Interview summary</h1>
              <div className="grid gap-2 md:grid-cols-3">
                <div>
                  <p className="heading-2 capitalize">{assessment.name}</p>
                  <p className="helper-text text-sm">Name of interview</p>
                </div>
                <div>
                  <p className="heading-2 capitalize">
                    {ROLE_LEVELS_DICTIONARY[assessment.role_level]?.name}
                  </p>
                  <p className="helper-text text-sm">Role level</p>
                </div>
                <div>
                  <p className="heading-2">
                    {`${secondsToLargestUnit(time).time || 0} ${
                      secondsToLargestUnit(time).unit || 'seconds'
                    }`}
                  </p>
                  <p className="helper-text text-sm">Interview time</p>
                </div>
                <div>
                  <p className="heading-2">
                    {
                      assessment_store.invitations.filter(
                        invitation => invitation.email !== ''
                      ).length
                    }
                  </p>
                  <p className="helper-text text-sm">No of invites</p>
                </div>
                <div>
                  <p className="heading-2">{questions_count}</p>
                  <p className="helper-text text-sm">No of Questions</p>
                </div>
              </div>
            </section>
            <section className="space-y-4">
              <h2 className="heading-2">Candidates to invite:</h2>
              <ul className="space-y-1">
                {assessment_store.invitations
                  .slice(0, 10)
                  .filter(invitaion => invitaion.name !== '')
                  .map((invitation, index) => (
                    <li
                      key={index}
                      className="relative flex items-center justify-between"
                    >
                      <button
                        onClick={() => {
                          assessment_store.deleteInvitee(invitation.email);
                        }}
                        className="peer absolute right-0 m-4"
                        type="button"
                      >
                        <span
                          title="delete invitee"
                          className="flex aspect-square w-7 items-center justify-center rounded-full bg-white text-lg"
                        >
                          &times;
                        </span>
                      </button>
                      <div className="btn-base heading-text grid flex-1 grid-cols-2 items-center bg-grey py-4 font-normal peer-hover:bg-red-100">
                        <p>
                          <span className="helper-text">Name:</span>{' '}
                          {invitation.name}
                        </p>
                        <p>
                          <span className="helper-text">Email:</span>{' '}
                          {invitation.email}
                        </p>
                      </div>
                    </li>
                  ))}
              </ul>
              <div className="flex gap-2">
                <button
                  className="btn-primary-light"
                  type="button"
                  onClick={openInviteesModal}
                >
                  View all
                </button>
                <Link
                  className="btn-primary-light"
                  href={`${path_name.replace(
                    'edit/summary',
                    'edit/invitations'
                  )}`}
                >
                  + Invite more candidates
                </Link>
              </div>
            </section>
          </div>
          <div className="flex items-center justify-end gap-4 rounded-b-xl bg-white p-4 shadow-[-20px_-20px_40px_0px_#7D85900D]">
            <button
              className="btn-primary-light px-20"
              type="button"
              onClick={openCustomInvitationModal}
            >
              {assessment_store.custom_email_message ? 'Edit' : 'Customize'}{' '}
              invitation email
            </button>
            <Button
              className="btn-primary"
              type="button"
              onClick={submitAssessment}
              is_busy={is_sending_invites}
            >
              Publish Interview
            </Button>
          </div>
        </div>
      </div>
      <Modal
        is_open={is_invitees_modal_open}
        title="Invitees"
        close={closeInviteesModal}
      >
        <div className="min-w-[628px] p-4">
          <ul className="space-y-1">
            {assessment_store.invitations
              .filter(invitaion => invitaion.name !== '')
              .map((invitation, index) => (
                <li
                  key={index}
                  className="relative flex items-center justify-between"
                >
                  <button
                    onClick={() => {
                      assessment_store.deleteInvitee(invitation.email);
                    }}
                    className="peer absolute right-0 m-4"
                    type="button"
                  >
                    <span
                      title="delete invitee"
                      className="flex aspect-square w-7 items-center justify-center rounded-full bg-white text-lg"
                    >
                      &times;
                    </span>
                  </button>
                  <div className="btn-base heading-text grid flex-1 grid-cols-2 items-center bg-grey py-4 font-normal peer-hover:bg-red-100">
                    <p>
                      <span className="helper-text">Name:</span>{' '}
                      {invitation.name}
                    </p>
                    <p>
                      <span className="helper-text">Email:</span>{' '}
                      {invitation.email}
                    </p>
                  </div>
                </li>
              ))}
          </ul>
        </div>
      </Modal>
      <Modal
        is_open={is_success_modal_open}
        title="Success"
        close={() => {}}
        is_close_by_button_only={true}
        hide_close_btn={true}
      >
        <div className="space-y-2 bg-[#F5F3FF] p-4 text-center text-sm text-[#675E8B] md:w-[547px]">
          <div className="flex flex-col items-center gap-2 px-12 py-4">
            <AssessmentSuccessModalIcon />
            <h2 className="heading-2 text-primary">
              Interview has been created successfully
            </h2>
            <p>
              Your '{assessment.name}' interview has been successfully created,
              and invitations have been sent to{' '}
              {
                assessment_store.invitations.filter(
                  invitation => invitation.name
                ).length
              }{' '}
              candidates.
            </p>
          </div>
        </div>
        <div className="rounded-xl bg-white p-4">
          <div className="flex items-center justify-end gap-2">
            <Link
              className="btn-primary"
              href={`/e/assessments-and-interviews/interviews/${assessment.id}`}
            >
              View interview details
            </Link>
          </div>
        </div>
      </Modal>

      <CustomInvitationModal
        initial_data={{
          message: assessment_store.custom_email_message || '',
          subject: assessment_store.custom_email_subject || '',
          cover_image: assessment_store.cover_image,
        }}
        is_open={is_custom_invitation_modal_open}
        close={closeCustomInvitationModal}
        handleSubmit={form_data => {
          updateCustomInvitation(form_data);
          closeCustomInvitationModal();
        }}
      />

      <InviteCandidatesModal
        assessment_id={assessment.id}
        invitation_type={assessment_store.choosen_invitation_type}
        is_open={is_invite_candidates_modal_open}
        close={closeInviteCandidatesModal}
      />
    </div>
  );
}
