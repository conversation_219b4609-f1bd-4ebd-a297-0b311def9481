"use client";

import { usePathname, useRouter, } from "next/navigation";
import { useEffect, useState } from "react";
import AssessmentDetailModal from "../../../misc/components/AssessmentDetailModal";
import { useParams } from "next/navigation";
import CreateAssessmentHeader from "../../../misc/components/CreateAssessmentHeader";
import ListBox from "@/components/shared/listbox";
import Modal from "@/components/Modal";
import {
  ASSESSMENT_TYPES,
  QUESTION_TYPES_DICTIONARY,
} from "@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants";
import app_fetch from "@/lib/api/appFetch.js";
import {
  InterviewAssessment,
  InterviewAssessmentQuestion,
  LibraryAssessment,
  QuestionPack,
  Section,
} from "@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments";
import Button from "@/components/shared/Button";
import { Loader } from "@/components/shared";
import InterviewPackDetailModal from "../../../misc/components/InterviewPackDetailModal";
import { updateAssessmentLastEditURL } from "../../../misc/api/Assessment";
import { useMutation } from "@tanstack/react-query";
import Checkbox from "@/components/shared/Checkbox/Checkbox";
import CreateNewInterviewPackBtn from "../../../misc/components/CreateNewInterviewPackBtn";
import IconElement from "@/components/icons/jsx/IconElement";
import CreateInterviewHeader from "../../../misc/components/CreateInterviewHeader";
import { SearchNormal1 } from "iconsax-react";
import { useGetQuestionsSections } from "../../../interview-pack/misc/api";

function getQuestionTypes(section: Section) {
  const section_question_types = [] as any
  Object.entries(QUESTION_TYPES_DICTIONARY).map(([key, value], index) => {
    //@ts-ignore
    const count = section[value.count_name]
    if (count) {
      section_question_types.push(value.value)
    }
  })
  return section_question_types
}

export default function Dashboard() {
  const path_name = usePathname();
  const router = useRouter();

  /* get linked library interviews */
  const [recommended_tests, setRecommendedTests] = useState<LibraryAssessment[]>([]);
  const [is_fetching_recommended_tests, setIsFetchingRecommendedTests] = useState(true);

  const [added_recommendations, setAddedRecommendations] = useState({} as Record<string, string | undefined>)

  const [active_library_assessment, setActiveLibraryAssessment] = useState<LibraryAssessment>();

  /* custom question packs */
  const [my_interviews, setMyInterviews] = useState<QuestionPack[]>([]);

  const [active_question_pack, setActiveQuestionPack] = useState<QuestionPack>();


  const [is_question_pack_detail_modal_open, setIsQuestionPackDetailModalOpen] =
    useState(false);
  function openQuestionPackDetailModal() {
    setIsQuestionPackDetailModalOpen(true);
  }
  function closeQuestionPackDetailModal() {
    setIsQuestionPackDetailModalOpen(false);
  }

  const [is_fetching_my_interviews, setIsFetchingMyInterviews] = useState(false);
  const [used_questions, setUsedQuestions] = useState<Record<string, boolean>>({}) //_used_questions.question = section_id || undefined 

  const [assessment, setAssessment] = useState({} as InterviewAssessment)
  const params = useParams()

  useEffect(() => {
    app_fetch(`assessments/assessment-interview/${params.assessment_id}/`)
      .then((res) => res.json())
      .then(res => {
        const _assessment = res as typeof assessment
        _assessment.is_custom = true;

        const _used_questions = {} as typeof used_questions;

        _assessment.questions?.map(question => {

          _used_questions[question.id] = true

          /* setAssessment(_assessment) */

          /* setAddedRecommendations(_added_recommendations) */
        })

        setUsedQuestions(_used_questions)
        setAssessment(_assessment)

        if (!_assessment.is_custom) {
          if (_assessment.role && _assessment.role_level) {
            const raw = JSON.stringify({
              role_id: _assessment.role,
              role_level: _assessment.role_level,
            });

            const requestOptions = {
              method: "POST",
              body: raw,
            };

            setIsFetchingRecommendedTests(true);
            app_fetch("assessments/test-recommendations/", requestOptions)
              .then((response) => response.json())
              .then((result) => {
                setRecommendedTests(result);
              })
              .catch((error) => console.log("error", error))
              .finally(() => {
                setIsFetchingRecommendedTests(false);
              });
          }
        } else {
          app_fetch("assessments/interview-packs/")
            .then((response) => response.json())
            .then((result) => {
              setMyInterviews(result);
            })
            .finally(() => {
              setIsFetchingMyInterviews(false);
            });
        }
      }
      )
  }, [params.assessment_id]);


  const [active_question_index, setActiveQuestionIndex] = useState(0);
  const [active_question, setActiveQuestion] = useState<InterviewAssessmentQuestion | undefined>(undefined);

  useEffect(() => {
    setActiveQuestion(assessment?.questions?.[active_question_index]);
  }, [active_question_index, assessment?.questions]);


  const [selected_questions, setSelectedQuestions] = useState<string[]>([])
  const [is_select_questions_modal_open, setIsSelectQuestionsModalOpen] = useState(false)
  function openSelectQuestionsModal() {
    setSelectedQuestions([])
    setIsSelectQuestionsModalOpen(true)
  }
  function closeSelectQuestionsModal() {
    setIsSelectQuestionsModalOpen(false)
  }


  async function addQuestionsToSectionAPI({ question_ids }: { question_ids: string[] }) {
    return await app_fetch(`assessments/assessment-interview/${params.assessment_id}/`, {
      method: "PATCH",
      body: JSON.stringify({
        add_questions: question_ids
      })
    }).then(res => res.json())
  }

  async function removeQuestionsFromSectionAPI({ question_ids }: { question_ids: string[] }) {
    return await app_fetch(`assessments/assessment-interview/${params.assessment_id}/`, {
      method: "PATCH",
      body: JSON.stringify({
        remove_questions: question_ids
      })
    }).then(res => res.json())
  }

  async function addQuestionsToSectionHelperFn(_selected_questions: string[] | undefined = undefined) {
    addQuestionsToSectionMutation.mutate({ question_ids: _selected_questions || selected_questions })
  }

  const addQuestionsToSectionMutation = useMutation({
    mutationFn: addQuestionsToSectionAPI,
    onSuccess: (result: InterviewAssessment, variables) => {
      const _used_questions = {} as typeof used_questions
      result.questions.map(
        question => _used_questions[question.id] = true
      )

      setUsedQuestions(_used_questions)

      result.is_custom = true
      setAssessment(result)

      closeSelectQuestionsModal()
    }
  })

  function addSelectedQuestions() {
    addQuestionsToSectionHelperFn()
  }

  function addAllQuestions() {
    if (active_question_pack) {
      const _questions = active_question_pack?.question_set.reduce((acc, curr) => { acc.push(curr.id); return acc }, [] as string[])
      setSelectedQuestions(_questions)
      addQuestionsToSectionHelperFn(_questions);
    }
  }

  async function removeQuestionFromSectionAPI({ question_id }: { question_id: string }) {
    return await app_fetch(`assessments/interview-packs/${assessment.id}/delete-question/${question_id}/`, { method: "DELETE" }).then(res => res.json())
  }

  const removeQuestionFromSectionMutation = useMutation({
    mutationFn: removeQuestionFromSectionAPI,
    onMutate: (variables) => {
      setUsedQuestions({ ...used_questions, [variables.question_id]: false })
    },
    onSuccess: (result: { message: string, section: Section }, variables) => {
      const _used_questions = used_questions
      delete _used_questions[variables.question_id]
      setUsedQuestions({ ..._used_questions })
      let new_questions = assessment.questions
      new_questions.splice(active_question_index, 1)
      setAssessment({ ...assessment, questions: new_questions, total_questions: new_questions.length })
    },
    onError: (error, variables) => {
      setUsedQuestions({ ...used_questions, [variables.question_id]: true })
    }
  })

  function removeUsedQuestion(question_id: string) {
    removeQuestionFromSectionMutation.mutate({ question_id })
  }

  const [is_adding_library_set_to_section, setIsAddingLibrarySetToSection] = useState({} as Record<string, boolean>);

  function addAssessmentQuestionsToSection(_assessment: LibraryAssessment) {
    setIsAddingLibrarySetToSection({
      ...is_adding_library_set_to_section,
      [_assessment.id]: true,
    });
  }

  const [is_removing_library_set_from_section, setIsRemovingLibrarySetFromSection,] = useState({} as Record<string, boolean>);
  function removeAssessmentQuestionsFromSection(_assessment: LibraryAssessment,) {
    setIsRemovingLibrarySetFromSection({
      ...is_removing_library_set_from_section,
      [_assessment.id]: true,
    });
  }

  const [is_question_detail_modal_open, setIsQuestionDetailModalOpen] =
    useState(false);
  function closeQuestionDetailModal() {
    setIsQuestionDetailModalOpen(false);
  }
  function openQuestionDetailModal() {
    setIsQuestionDetailModalOpen(true);
  }


  // filters
  const [assessment_type_filter, setAssessmentTypeFilter] = useState<string | undefined>();
  const [search_term, setSearchTerm] = useState<string | undefined>(undefined);
  const [my_filtered_question_packs, setMyFilteredQuestionPacks] = useState<QuestionPack[]>([]);
  const [filtered_recommended_tests, setFilteredRecommendedTests] = useState<LibraryAssessment[]>([]);

  function clearFilters() {
    setAssessmentTypeFilter(undefined);
    setSearchTerm(undefined);
  }

  useEffect(() => {
    /* get linked library */
    setFilteredRecommendedTests(
      recommended_tests.filter((test) => {
        let should_show = true;

        if (assessment_type_filter) {
          const is_assessment_type_match = assessment_type_filter.toLowerCase()
            .includes(test.label?.toLowerCase());
          should_show = should_show && is_assessment_type_match;
        }

        if (search_term) {
          const is_name_match = test.name.toLowerCase().includes(
            search_term.toLowerCase(),
          );
          const is_description_match = test.description.toLowerCase().includes(
            search_term.toLowerCase(),
          );

          should_show = should_show && (is_name_match || is_description_match);
        }

        return should_show;
      }),
    );

    setMyFilteredQuestionPacks(
      my_interviews.filter((test) => {
        let should_show = true;

        // do not show empty question sets
        if (test.question_set.length == 0) {
          should_show = false
        }

        if (assessment_type_filter) {
          if (test.label) {
            const is_assessment_type_match = assessment_type_filter.toLowerCase().includes(test.label.toLowerCase());
            should_show = should_show && is_assessment_type_match;
          }
        }

        if (search_term) {
          const is_name_match = test.name.toLowerCase().includes(
            search_term.toLowerCase(),
          );
          const is_description_match = test.description?.toLowerCase().includes(
            search_term.toLowerCase(),
          );

          should_show = should_show && (is_name_match || is_description_match);
        }

        return should_show;
      }),
    );
  }, [
    search_term,
    assessment_type_filter,
    recommended_tests,
    my_interviews,
  ]);

  function truncateToLength(str: string, len: number) {
    if (str.length > len) {
      return str.slice(0, len) + "...";
    } else {
      return str;
    }
  }


  const [is_proceeding_to_next_step, setIsProceedingToNextStep] = useState(
    false,
  );
  function handleProceed() {
    /* validate proceed */
    if (assessment.questions?.filter(section => section !== undefined).length) {
      setIsProceedingToNextStep(true);
      updateAssessmentLastEditURL(assessment.id, "edit")
      router.push(`${path_name.replace("edit", "edit/invitations")}`);
    }
    else {
      alert("You need to add a question to continue")
    }
  }

  const number_of_questions = Math.max(6, assessment?.total_questions || 0)
  const { data: questionSections, isLoading: isGettingSections } =
    useGetQuestionsSections();

  const ASSESSMENT_TYPES = questionSections?.results.map(section => ({ name: section.name })) || []




  return (
    <div>

      <CreateInterviewHeader
        assessment={assessment}
        back_page_url={`/e/assessments-and-interviews/interviews/${params.assessment_id}`}
        handleProceed={handleProceed}
        is_proceeding={is_proceeding_to_next_step}
      />

      <div className="space-y-4 py-4">
        <section className="section-container">
          <h2 className="heading-2">Questions</h2>
          <div className="grid grid-cols-2 md:grid-cols-7 gap-2">
            {[...Array(number_of_questions)].map((_, index) => (
              <div
                key={index}
                role="button"
                className={`flex min-h-[150px] items-center justify-center rounded-xl bg-white p-1 ${active_question_index == index ? "outline !outline-[1px] outline-primary focus:outline focus:outline-primary" : ""}`}
                onClick={() => {
                  setActiveQuestionIndex(index);
                }}
              >
                {
                  // if section is not undefined
                  assessment?.questions?.[index]
                    ? (
                      <div className="bg-light-accent-bg h-full w-full rounded-xl">
                        <div className="flex items-center justify-between rounded-xl bg-[#ECE9FF] p-2 text-left capitalize text-primary">
                          <h2>
                            Question {index + 1}
                          </h2>
                          <div className="flex items-center gap-2">
                            <div className="btn-icon-primary" title="delete question" >
                              <Button className="" onClick={() => removeUsedQuestion(assessment.questions[index].id)} is_busy={used_questions[assessment.questions[index].id] == false}>
                                <IconElement icon="close-square" />
                              </Button>
                            </div>
                          </div>
                        </div>
                        <div className="space-y-4 p-4 text-left capitalize text-primary">
                          <p className="max-h-[2lh] max-w-[120ch] text-ellipses overflow-hidden">{assessment.questions[index].question}</p>
                        </div>
                      </div>
                    )
                    : (
                      <div className="p-3">
                        <p className="btn-primary-light flex-1">
                          Question {index + 1}
                        </p>
                      </div>
                    )
                }
              </div>
            ))}
            <div
              role="button"
              className={`flex min-h-[150px] items-center justify-center rounded-xl bg-white p-1 ${active_question_index == number_of_questions + 1 ? "outline !outline-[1px] outline-primary focus:outline focus:outline-primary" : ""}`}
              onClick={() => {
                setActiveQuestionIndex(number_of_questions + 1);
              }}
            >
              <div className="p-3">
                <p className="btn-primary-light flex-1">
                  Question {number_of_questions + 1}
                </p>
              </div>
            </div>
          </div>
        </section>
        <section>
          <div className="flex max-md:flex-wrap gap-2">
            <ListBox
              options={ASSESSMENT_TYPES}
              className="border"
              placeholder="Assessment type"
              // @ts-ignore
              active_option={assessment_type_filter}
              setActiveOption={(_assessment_type) => {
                setAssessmentTypeFilter(_assessment_type);
              }}
              value_key="name"
              readable_text_key="name"
            />
            <div className="btn-bordered flex items-center gap-2 py-0">
              <SearchNormal1 />
              <input
                type="text"
                name="search assessments"
                placeholder="Search"
                className="input-base"
                value={search_term}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            {(search_term || assessment_type_filter) && (
              <button className="btn-bordered" onClick={clearFilters}>
                Clear filters
              </button>
            )}
          </div>
        </section>

        {/* library questions */}
        {!assessment.is_custom && (
          <>
            <section className="section-container">
              <p className="md:max-w-[60vw] text-sm">
                These assessments evaluate a candidate's specific job-related
                skills, technical knowledge, and competencies required for the
                role. They focus on the candidate's ability to perform the tasks
                associated with the job role.
              </p>
            </section>

            <p>
              {is_fetching_recommended_tests}
            </p>

            <section>
              {is_fetching_recommended_tests
                ? (<Loader />)
                : filtered_recommended_tests.length === 0
                  ? (
                    <div className="flex min-h-[600px] items-center justify-center">
                      <div className="rounded-md bg-gradient-to-b from-[#755ae20a] via-transparent to-transparent p-12 border border-[#DADADA]/20 text-center">
                        <div className="container max-w-xs space-y-2">
                          <div className="container flex justify-center">
                            <img
                              src="/images/create-assessments/no-assessment.png"
                              alt="writing with pen"
                            />
                          </div>
                          {recommended_tests.length == 0 ? (
                            <>
                              <h2>There are currently no recommended tests available for the selected options</h2>
                              <p className="text-sm text-[#7D8590]">You could try again later.</p>
                            </>
                          ) : (
                            <>
                              <h2>No recommended pack matches your filters</h2>
                              <p className="text-sm text-[#7D8590]">Clear the filters to see more results.</p>
                              <button
                                onClick={clearFilters}
                                className="btn-primary inline-flex gap-2"
                                type="button"
                              >
                                Clear filters
                              </button>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                  : (
                    <>
                      <div className="grid md:grid-cols-4 gap-4">
                        {filtered_recommended_tests.map((_assessment, index) => (
                          <div
                            key={index}
                            className={`space-y-2 rounded-md transition ease-in-out overflow-hidden ${added_recommendations[_assessment.id] ? "border-l-8 border-[#755AE280]" : ""} bg-white p-4`}
                          >
                            <h2 className="heading-2">{_assessment.name}</h2>
                            <p
                              className="text-xs max-h-[4lh] overflow-hidden"
                              style={{
                                maskImage: "linear-gradient(#fff, transparent)",
                              }}
                            >
                              {_assessment.description}
                            </p>
                            <p className="heading-text text-sm">
                              <span className="helper-text font-normal">
                                Total questions:
                              </span>{" "}
                              {_assessment.question_set?.length}
                            </p>
                            <ul className="flex items-center gap-2 text-xs">
                              {_assessment.tags?.slice(0, 3).map((tag) => (
                                <li className="btn-primary-light-pill" key={tag} title={tag}>
                                  <span className="max-w-[5ch] text-ellipsis overflow-hidden whitespace-nowrap">
                                    {truncateToLength(tag, 8)}
                                  </span>
                                </li>
                              ))}
                            </ul>
                            <div className="flex justify-end gap-2 text-xs">
                              <button
                                className="btn-primary-light heading-text font-normal"
                                type="button"
                                onClick={() => {
                                  setActiveLibraryAssessment(_assessment);
                                  openQuestionDetailModal();
                                }}
                              >
                                Details
                              </button>
                              {added_recommendations[_assessment.id] && (
                                <Button
                                  is_busy={is_removing_library_set_from_section[
                                    _assessment.id
                                  ]}
                                  className="btn-primary-light heading-text font-normal"
                                  onClick={() =>
                                    removeAssessmentQuestionsFromSection(
                                      _assessment,
                                    )}
                                >
                                  Remove
                                </Button>
                              )}

                              <Button
                                type="button"
                                is_busy={is_adding_library_set_to_section[
                                  _assessment.id
                                ]}
                                className="btn-primary"
                                onClick={() => {

                                  setActiveQuestionPack(_assessment);
                                  openSelectQuestionsModal()
                                  return

                                }
                                }
                              >
                                Add
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </>
                  )}
            </section>
          </>
        )}

        {/* custom questions */}
        {assessment.is_custom && (
          <>
            <h2 className="heading-text">Your created interview packs</h2>
            <section>
              {is_fetching_my_interviews
                ? (<Loader />)
                : my_filtered_question_packs.length === 0
                  ? (
                    <div className="flex min-h-[600px] items-center justify-center">
                      <div className="rounded-md bg-gradient-to-b from-[#755ae20a] via-transparent to-transparent p-12 border border-[#DADADA]/20 text-center">
                        <div className="container max-w-xs space-y-2">
                          <div className="container flex justify-center">
                            <img
                              src="/images/create-assessments/no-assessment.png"
                              alt="writing with pen"
                            />
                          </div>
                          {my_interviews.length == 0 ? (
                            <>
                              <h2>You do not have any question packs</h2>
                              <p className="text-sm text-[#7D8590]">You can create one with the button</p>
                              <CreateNewInterviewPackBtn redirect_link={path_name} />
                            </>
                          ) : (
                            <>
                              <h2>No recommended pack matches your filters</h2>
                              <p className="text-sm text-[#7D8590]">Clear the filters to see more results.</p>
                              <button
                                onClick={clearFilters}
                                className="btn-primary inline-flex gap-2"
                                type="button"
                              >
                                Clear filters
                              </button>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                  : (
                    <ul className="grid md:grid-cols-4 gap-4">
                      {[...my_filtered_question_packs].reverse().map((
                        question_pack,
                        index,
                      ) => (
                        <li key={index}>
                          <div className="space-y-2 relative rounded-md bg-white p-4 overflow-hidden">
                            <h2 className="heading-2 mt-2">{question_pack.name}</h2>
                            <span className="absolute rounded-none btn-primary-light text-xs py-1 px-4 -top-2 right-0">
                              My pack
                            </span>
                            <p className="text-xs">{question_pack.description}</p>
                            <p className="heading-text">
                              <span className="helper-text font-normal">
                                Total questions:
                              </span>
                              {" "}
                              {question_pack.question_set?.length || 0}
                            </p>
                            <ul className="flex items-center gap-2 text-xs">
                              {question_pack.tags?.map(
                                (item) => (
                                  <li className="btn-primary-light-pill" key={item}>
                                    <span className="max-w-[5ch] text-ellipsis overflow-hidden whitespace-nowrap">
                                      {truncateToLength(item, 8)}
                                    </span>
                                  </li>
                                ),
                              )}

                            </ul>
                            <div className="flex gap-2 justify-end text-xs">

                              <InterviewPackDetailModal
                                close={closeQuestionPackDetailModal}
                                is_open={is_question_pack_detail_modal_open}
                                interviewPack={active_question_pack}
                                setCurrentInterviewPack={() => setActiveQuestionPack(question_pack)}
                                openDrawer={openQuestionPackDetailModal}
                              />

                              {added_recommendations[question_pack.id]
                                ? (
                                  <Button
                                    is_busy={is_removing_library_set_from_section[
                                      question_pack.id
                                    ]}
                                    className="btn-primary-light heading-text font-normal"
                                    onClick={() => { }
                                    }
                                  >
                                    Remove
                                  </Button>
                                )
                                : (
                                  <Button
                                    type="button"
                                    is_busy={is_adding_library_set_to_section[question_pack.id]}
                                    className="btn-primary"
                                    onClick={() => {
                                      setActiveQuestionPack(question_pack);
                                      openSelectQuestionsModal()
                                      return
                                    }}
                                  >
                                    Add
                                  </Button>
                                )}
                            </div>
                          </div>
                        </li>
                      ))}

                      <li>
                        <CreateNewInterviewPackBtn redirect_link={path_name} variant="card" />
                      </li>
                    </ul>
                  )}
            </section>
          </>
        )}
      </div>


      <AssessmentDetailModal
        close={closeQuestionDetailModal}
        is_open={is_question_detail_modal_open}
        is_adding={active_library_assessment ? is_adding_library_set_to_section[active_library_assessment.id] : false}
        is_removing={active_library_assessment ? is_removing_library_set_from_section[active_library_assessment.id] : false}
        assessment={active_library_assessment}
        //@ts-ignore
        is_added={active_library_assessment ? added_recommendations[active_library_assessment.id] : false}
        handleAdd={() => {
          if (assessment.questions[active_question_index]) {
            if (active_library_assessment) addAssessmentQuestionsToSection(active_library_assessment)
          }
          else {
            setActiveLibraryAssessment(active_library_assessment);
            closeQuestionDetailModal()
          }
        }}
        handleRemove={() => {
          if (active_library_assessment)
            removeAssessmentQuestionsFromSection(active_library_assessment)
        }}
      />

      <Modal
        is_open={is_select_questions_modal_open}
        close={closeSelectQuestionsModal}
        title="Questions"
      >
        <div className="p-4 pb-0 space-y-4 w-full max-w-[800px] max-h-[80vh] overflow-y-auto">
          <div className="bg-grey rounded-xl p-2">
            <h2 className="heading-2">{active_question_pack?.name}</h2>
            <p className="helper-text">{active_question_pack?.description}</p>
          </div>
          <div className="">
            <ul className="space-y-2">
              {active_question_pack?.question_set.map((question, index) => (
                <li key={index}>
                  {
                    used_questions[question.id] !== undefined ? (
                      <div className="p-4 rounded-md border block" >
                        <div className="flex justify-between items-center">
                          <h2 className="text-lg"> Question {index + 1} </h2>
                          <Button className="text-xs btn-danger-bordered" onClick={() => removeUsedQuestion(question.id)} is_busy={used_questions[question.id] == false}>
                            remove
                          </Button >
                        </div>
                        <p>
                          {question.question}
                        </p>
                      </div>
                    ) : (
                      <label className="p-4 rounded-md border block" >
                        <div className="flex justify-between items-center">
                          <h2 className="text-lg"> Question {index + 1} </h2>

                          <Checkbox
                            checked={selected_questions.includes(question.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedQuestions(selected_questions.concat([e.target.value]))
                              }
                              else {
                                setSelectedQuestions(selected_questions.toSpliced(selected_questions.indexOf(question.id), 1))
                              }
                            }}
                            name="select question"
                            value={question.id}
                          />

                        </div>
                        <p>
                          {question.question}
                        </p>
                      </label>
                    )
                  }
                </li>
              ))}
            </ul>
          </div>
          <div className="sticky shadow-[rgba(17,_17,_26,_0.1)_0px_-12px_12px] bottom-0 mt-1 bg-white p-4 border-t transition-all -mx-4">
            <div className="flex items-center gap-2">
              <Button is_busy={addQuestionsToSectionMutation.isLoading} type="button" disabled={!active_question_pack?.question_set.length} onClick={addAllQuestions} className="btn-primary-bordered w-full">
                Add all ({active_question_pack?.question_set.length}) question(s)
              </Button>
              <Button disabled={!selected_questions.length} onClick={addSelectedQuestions} is_busy={addQuestionsToSectionMutation.isLoading} className="btn-primary w-full">
                Add ({selected_questions.length}) selected question(s)
              </Button>
            </div>
          </div>
        </div>
      </Modal>
    </div>

  );
}
