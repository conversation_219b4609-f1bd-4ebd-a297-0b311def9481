'use client';

import { ASSESSMENT_STATUSES_DICTIONARY } from '@/app/(website-main)/e/assessments-and-interviews/misc/constants/constants';
import { ProctoringFlag } from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';
import Disclosure from '@/components/Disclosure';
import ChevronDown from '@/components/icons/ChevronDown';
import Modal from '@/components/Modal';
import Button from '@/components/shared/Button';
import Loader from '@/components/shared/loader';
import app_fetch from '@/lib/api/appFetch.js';
import { downloadBlob, secondsToLargestUnit } from '@/lib/utils/functions';
import '@smastrom/react-rating/style.css';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import Link from 'next/link';
import { useState } from 'react';
import { getCandidateResultQuery } from '../../../../misc/queries/interviews';

const StarIcon = (
  <path
    d="M398.799,141.794c-43.394-3.977-86.776-6.52-130.158-8.418C258.835,99.302,242.633-4.751,193.173,0.169
      c-39.659,3.944-61.012,90.515-73.08,130.306c-32.333,0.283-64.692,1.062-97.09,2.416c-14.735,0.615-27.908,17.9-18.207,31.732
      c19.157,27.316,44.198,49.389,70.487,70.103c-11.83,38.196-21.665,77.499-29.759,116.53c-3.504,16.91-5.31,32.212,3.881,44.82
      c2.411,9.987,12.018,18.494,22.429,18.029c51.805-2.313,93.872-44.738,133.991-77.119c33.156,26.317,66.309,52.64,99.475,78.951
      c12.835,10.183,37.057,5.178,35.798-14.828c-3.039-48.158-15.477-96.473-30.599-144.041c32.951-25.229,65.899-50.459,99.11-75.353
      C426.818,168.817,420.858,143.814,398.799,141.794z"
  />
);

export default function Dashboard({
  params,
}: {
  params: { assessment_id: string; candidate_id: string };
}) {
  const [CANDIDATE_ID, setCandidateID] = useState(params.candidate_id);

  const [flag_dict, setFlagDict] = useState(
    {} as Record<string, ProctoringFlag[]>
  );

  const [is_view_video_modal_open, setIsViewVideoModalOpen] = useState(false);
  const [active_video_index, setVideoIndex] = useState(0);

  function goToNextVideo() {
    setVideoIndex(
      (active_video_index + 1 + candidate?.interview_answers?.length!) %
        (candidate?.interview_answers?.length! || 1)
    );
  }

  function goToPreviousVideo() {
    setVideoIndex(
      (active_video_index - 1 + candidate?.interview_answers?.length!) %
        (candidate?.interview_answers.length! || 1)
    );
  }

  function viewVideo(video_index: number) {
    setVideoIndex(video_index);
    setIsViewVideoModalOpen(true);
  }

  const {
    data: candidate,
    isSuccess: is_loading_candidate_success,
    isLoading: is_loading_candidate,
    isError: is_loading_candidate_error,
    error: load_candidate_error,
  } = getCandidateResultQuery({ candidate_id: CANDIDATE_ID });

  const [is_sending_report, setIsSendingReport] = useState(false);
  function sendResult() {
    setIsSendingReport(true);
    const options = {
      method: 'POST',
      body: JSON.stringify({
        candidate_email: CANDIDATE_ID,
        assessment_id: params.assessment_id,
      }),
    };
    app_fetch('assessments/export-report/', options)
      .then(response => response.blob())
      .then(result => {
        downloadBlob(result, `getLinked_${params.assessment_id}.xlsx`);
        setIsSendingReport(false);
      });
  }

  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');

  async function submitComment() {
    const options = {
      method: 'POST',
      body: JSON.stringify({
        rating,
        text: comment,
      }),
    };
    return app_fetch(
      `assessments/result_overview/${candidate?.id}/comments/`,
      options
    ).then(response => response.json());
  }

  function submitRating() {
    makeCommentMutation.mutate();
  }

  const query_client = useQueryClient();

  const makeCommentMutation = useMutation({
    mutationFn: submitComment,
    onSuccess: () => {
      setRating(0);
      setComment('');
      query_client.invalidateQueries({
        queryKey: [
          'candidate_comments',
          `candidate_email=${CANDIDATE_ID}`,
          `assessment_id=${params.assessment_id}`,
        ],
      });
    },
  });

  const [current_tab, setCurrentTab] = useState(0);
  const tabs = ['Assessment Overview'];

  if (is_loading_candidate) {
    return <Loader />;
  }
  if (is_loading_candidate_error) {
    return <p>error loading candidate</p>;
  }

  return (
    <div className="m-4 ml-0 space-y-2">
      <div className="flex items-start gap-4 bg-white p-4 text-sm">
        <Link
          className="btn-primary-light-pill"
          href={`/e/assessments-and-interviews/interviews/${params.assessment_id}`}
        >
          Back
        </Link>
        <div className="flex flex-1 items-start gap-4">
          <div className="bg-light-accent-bg relative z-0 flex aspect-square w-24 items-center justify-center rounded-full bg-primary-light">
            {false ? ( //candidate.proctoring_report?.profile_photo ? (
              <img
                src={undefined} //candidate?.proctoring_report?.profile_photo}
                className="aspect-square  w-24 rounded-full"
              />
            ) : (
              <h2 className="text-2xl font-bold text-primary">
                {' '}
                {candidate.candidate_name
                  ?.split(' ')
                  .slice(0, 2)
                  .map(name => name.slice(0, 1)) || '-'}{' '}
              </h2>
            )}
            <span className="absolute right-0 top-0 flex aspect-square w-8 items-center justify-center rounded-full bg-primary text-xs text-white shadow-md">
              {/* {candidate.result?.status == "completed" ? Math.round(candidate.result?.overall_percentage) : "--"}% */}
            </span>
          </div>
          <div className="flex-1 space-y-2">
            <div>
              <h1 className="heading-2 text-base text-primary">
                {candidate.candidate_name}
              </h1>
              <div className="flex items-center justify-between">
                <div className="flex divide-x">
                  <p className="pr-2">{candidate?.candidate_email}</p>
                  <p className="pl-2">
                    {/* {candidate.proctoring_report?.location} */}
                  </p>
                </div>
                {/* <div className="flex items-center gap-2"> */}
                {/*   <Button */}
                {/*     className="btn-primary-light" */}
                {/*     onClick={openCandidateDetailsDrawer} */}
                {/*     disabled={!candidate?.result?.job_application} */}
                {/*   > */}
                {/*     View details */}
                {/*   </Button> */}
                {/*   <Button */}
                {/*     is_busy={is_sending_report} */}
                {/*     className="btn-primary-light" */}
                {/*     onClick={sendResult} */}
                {/*     disabled={candidate?.status !== "completed"} */}
                {/*   > */}
                {/*     Send result */}
                {/*   </Button> */}
                {/* </div> */}
              </div>
            </div>
            <hr />
            <div className="flex gap-6">
              <div>
                <p className="heading-text">
                  {candidate?.interview?.name || '-'}
                </p>
                <p className="text-xs">Assessment</p>
              </div>
              <div>
                <p
                  className={` ${
                    candidate.status === 'ongoing'
                      ? 'text-yellow-500'
                      : candidate.status === 'completed'
                      ? 'text-green-500'
                      : 'text-red-500'
                  } } `}
                >
                  {ASSESSMENT_STATUSES_DICTIONARY[candidate.status]?.title}
                </p>
                <p className="text-xs">Status</p>
              </div>
              <div>
                <p>{new Date(candidate.invite_date).toLocaleString()}</p>
                <p className="text-xs">Invited date</p>
              </div>
              <div>
                <p>
                  {candidate.date_taken
                    ? new Date(candidate.date_taken).toLocaleString()
                    : '-'}
                </p>
                <p className="text-xs">Completed date</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <nav className="tab-nav w-full">
        <ul className="tab-list">
          {tabs.map((tab, index) => (
            <li
              key={index}
              className={index == current_tab ? 'active-tab-item' : ''}
            >
              <label className="cursor-pointer">
                <input
                  type="radio"
                  name="active tab"
                  className="hidden"
                  value={index}
                  onChange={e => setCurrentTab(parseInt(e.target.value))}
                />
                {tab}
              </label>
            </li>
          ))}
        </ul>
      </nav>
      {is_loading_candidate ? (
        <Loader />
      ) : (
        <div className="">
          {/* assessment overview */}
          {tabs[current_tab].toLowerCase() == 'assessment overview' && (
            <>
              <div className="grid grid-cols-2 gap-2 ">
                <div className="space-y-4 rounded-xl bg-white p-4 text-sm">
                  <div className="space-y-2">
                    <div>
                      <h2 className="heading-2">Onboarding Images</h2>
                      <p className="text-helper-text">
                        Profile image and identity card images taken on
                        onboarding
                      </p>
                    </div>
                    {candidate.verification_docs?.profile_photo ? (
                      <div className="grid grid-cols-2 gap-4">
                        <div className="flex flex-col items-center gap-2">
                          <div className="w-full overflow-hidden rounded-xl border border-[#E0E0E0] bg-[#F8F9FB]">
                            <img
                              src={candidate.verification_docs?.profile_photo}
                              alt="candidate's profile image"
                              className="h-[300px] w-full object-cover"
                            />
                          </div>
                          <p className="rounded-full bg-light-background p-2 text-primary">
                            Profile image
                          </p>
                        </div>
                        <div className="flex flex-col items-center gap-2">
                          <div className="w-full overflow-hidden rounded-xl border border-[#E0E0E0] bg-[#F8F9FB]">
                            <img
                              src={candidate.verification_docs?.photo_id_path}
                              alt="candidate's identity image"
                              className="h-[300px] w-full object-cover"
                            />
                          </div>
                          <p className="rounded-full bg-light-background p-2 text-primary">
                            Identity card image
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="grid min-h-[300px] place-items-center rounded-xl border border-[#E0E0E0] bg-[#F8F9FB]">
                        <div className="flex flex-col items-center gap-2 text-center">
                          <img src="/image icon.png" alt="image placeholder" />
                          <p className="text-xs text-helper-text">
                            Web cam recording will appear here
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="space-y-2">
                    <div>
                      <h2 className="heading-2">Videos</h2>
                      <p className="text-helper-text">
                        Tap to watch video and see transacript
                      </p>
                    </div>
                    {candidate.interview_answers?.length ? (
                      <div className="grid grid-cols-3 gap-2">
                        {candidate.interview_answers?.map((answer, index) => (
                          <Button
                            onClick={() => viewVideo(index)}
                            key={index}
                            className="grid min-h-[100px] place-items-center rounded-xl border border-[#E0E0E0] bg-[#F8F9FB]"
                          >
                            <div className="flex flex-col items-center gap-2 text-center">
                              <video
                                className="cover rounded-xl"
                                src={answer.video_url}
                              />
                            </div>
                          </Button>
                        ))}
                      </div>
                    ) : (
                      <p className="italic text-helper-text">
                        No videos to show.
                      </p>
                    )}
                  </div>
                </div>
                <div className="space-y-2 rounded-xl bg-white p-4">
                  <div className="space-y-2">
                    <div>
                      <h2 className="heading-2">Questions and Answer</h2>
                    </div>
                    {candidate.interview_answers.length ? (
                      <div className="space-y-2">
                        {candidate.interview_answers.map((answer, index) => (
                          <Disclosure
                            title={
                              <div className="flex items-center justify-between">
                                <h3>Question {index + 1}</h3>
                                <div className="flex items-center gap-2">
                                  <p>
                                    {secondsToLargestUnit(answer.time).time}{' '}
                                    {secondsToLargestUnit(answer.time).unit}
                                  </p>
                                  <ChevronDown />
                                </div>
                              </div>
                            }
                          >
                            <div className="space-y-2">
                              <div>
                                <p className="font-bold">Question :</p>
                                <p>{answer.question_body}</p>
                              </div>

                              <div>
                                <p className="font-bold">Answer :</p>
                                <video
                                  src={answer.video_url}
                                  controls
                                  className="w-full"
                                />
                              </div>

                              {answer.transcription && (
                                <div>
                                  <p className="font-bold">Transcription :</p>
                                  <div className="rounded-lg border bg-gray-50 p-3">
                                    <p className="whitespace-pre-wrap text-sm text-gray-700">
                                      {answer.transcription}
                                    </p>
                                  </div>
                                </div>
                              )}
                            </div>
                          </Disclosure>
                        ))}
                      </div>
                    ) : (
                      <div>No answers to show</div>
                    )}
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      )}

      <Modal
        title={`Answer ${active_video_index + 1}/${candidate.interview_answers
          ?.length}`}
        is_open={is_view_video_modal_open}
        close={() => setIsViewVideoModalOpen(false)}
      >
        <div className="min-w-[600px] space-y-4 p-4">
          <div>
            <h2 className="heading-2">Interview Videos</h2>
            <p className="text-helper-text">Video answers</p>
          </div>

          <div className="relative grid min-h-[300px] place-items-center overflow-hidden rounded-xl bg-[#F8F9FB]">
            <video
              src={candidate.interview_answers?.[active_video_index]?.video_url}
              controls
              className="image-cover w-full"
            />
            <div className="input-grey">
              <div>
                <p className="font-bold">Question :</p>
                <p>
                  {
                    candidate.interview_answers?.[active_video_index]
                      ?.question_body
                  }
                </p>
              </div>

              {candidate.interview_answers?.[active_video_index]
                ?.transcription && (
                <div className="mt-3">
                  <p className="font-bold">Transcription :</p>
                  <div className="mt-2 rounded-lg border bg-gray-50 p-3">
                    <p className="whitespace-pre-wrap text-sm text-gray-700">
                      {
                        candidate.interview_answers?.[active_video_index]
                          ?.transcription
                      }
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
          <div className="flex justify-between">
            <Button
              className="btn-primary-bordered"
              onClick={goToPreviousVideo}
            >
              Previous Video
            </Button>

            {/* hide download image button */}
            {true ? (
              <span></span>
            ) : (
              <Button className="btn-primary-light hidden items-center gap-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 12 12"
                  fill="none"
                >
                  <path
                    d="M7.5 11.375H4.5C1.785 11.375 0.625 10.215 0.625 7.5V4.5C0.625 1.785 1.785 0.625 4.5 0.625H7.5C10.215 0.625 11.375 1.785 11.375 4.5V7.5C11.375 10.215 10.215 11.375 7.5 11.375ZM4.5 1.375C2.195 1.375 1.375 2.195 1.375 4.5V7.5C1.375 9.805 2.195 10.625 4.5 10.625H7.5C9.805 10.625 10.625 9.805 10.625 7.5V4.5C10.625 2.195 9.805 1.375 7.5 1.375H4.5Z"
                    fill="#755AE2"
                  />
                  <path
                    d="M6.0007 7.6301C5.9057 7.6301 5.8107 7.5951 5.7357 7.5201L4.2357 6.0201C4.0907 5.8751 4.0907 5.6351 4.2357 5.4901C4.3807 5.3451 4.6207 5.3451 4.7657 5.4901L6.0007 6.7251L7.2357 5.4901C7.3807 5.3451 7.6207 5.3451 7.7657 5.4901C7.9107 5.6351 7.9107 5.8751 7.7657 6.0201L6.2657 7.5201C6.1907 7.5951 6.0957 7.6301 6.0007 7.6301Z"
                    fill="#755AE2"
                  />
                  <path
                    d="M6 7.62988C5.795 7.62988 5.625 7.45988 5.625 7.25488V3.25488C5.625 3.04988 5.795 2.87988 6 2.87988C6.205 2.87988 6.375 3.04988 6.375 3.25488V7.25488C6.375 7.46488 6.205 7.62988 6 7.62988Z"
                    fill="#755AE2"
                  />
                  <path
                    d="M5.99924 9.11508C4.94424 9.11508 3.88424 8.94508 2.87924 8.61008C2.68424 8.54508 2.57924 8.33008 2.64424 8.13508C2.70924 7.94008 2.91924 7.83008 3.11924 7.90008C4.97924 8.52008 7.02424 8.52008 8.88424 7.90008C9.07924 7.83508 9.29424 7.94008 9.35924 8.13508C9.42424 8.33008 9.31924 8.54508 9.12424 8.61008C8.11424 8.95008 7.05424 9.11508 5.99924 9.11508Z"
                    fill="#755AE2"
                  />
                </svg>

                <span> Download Video </span>
              </Button>
            )}

            <Button className="btn-primary-bordered" onClick={goToNextVideo}>
              Next Video
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
}
