import { CandidateSectionAnswer } from "@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments";
import app_fetch from "@/lib/api/appFetch";
import { useQuery } from "@tanstack/react-query";

const fetchCandidateSectionAnswers = async ( candidate_email:string, assessment_id:string, section_id:string,): Promise<CandidateSectionAnswer[]> => {
  return await app_fetch(`assessments/candidate-dashboard-answers?candidate_email=${candidate_email}&assessment_id=${assessment_id}&section_id=${section_id}`).then(async (res)=>res.json());
};


export const useFetchCandidateSectionAnswers = ( candidate_email:string, assessment_id:string, section_id:string,) => {
    return useQuery(['candidate-answers', assessment_id, candidate_email, section_id ], () => fetchCandidateSectionAnswers(candidate_email, assessment_id,  section_id), {
        staleTime: 500,
        enabled: !!candidate_email && !!assessment_id && !!section_id,
   })
}
