'use client';

import Link from 'next/link';
import * as React from 'react';
import io, { type Socket } from 'socket.io-client';
import { assessments } from '@/app/misc/mock/assessments';
import EllipsesVerticalIcon from '@/components/icons/jsx/EllipsesVerticalIcon';
import { cn } from '@/utils';


export interface Image {
  // image_data: string;
  // user_id: string;
  // room: string;
  // id: number;
  imageData: string;
  userID: string;
}

export interface Events {
  // image_data: string;
  // user_id: string;
  // room: string;
  // id: number;
  eventType: string;
  inviteId: string;
}

export interface ImageObj {
  // image_data: string;
  // user_id: string;
  // room: string;
  // id: number;
  [userId: string]: { imageData: string; userID: string };
}

const assessment = assessments[0];

function InvigilationDashboard() {
  const [imagesObject, setImagesObject] = React.useState<Image[]>([]);
  const [events, setEvents] = React.useState<Events[]>([]);
  const socket = React.useRef<Socket>();

  const [showBorder, setShowBorder] = React.useState(false);
  const [indexInFocus, setIndexInFocus] = React.useState(0);

  React.useEffect(() => {
    socket.current = io('https://socket.getlinked.ai');

    // Emit a 'joinRoom' event with the room name 'room1'
    socket.current.emit('joinRoom', 'room1');

    // Listen for 'currentImages' event to receive image data
    socket.current?.on('currentImages', data => {
      setImagesObject(prev => ({ ...prev, [data.userID]: data }));
    });

    socket.current?.on('currentEvents', data => {
      // setImagesObject(prev => ({ ...prev, [data.userID]: data }));
      setEvents(prev => ({ ...prev, [data.inviteId]: data }));
    });

    return () => {
      socket.current?.disconnect();
    };
  }, []);


  // Effect to track changes in myState
  React.useEffect(() => {
    // This code runs whenever myState changes
    console.log('myState changed:', events);

    // You can perform additional actions based on the state change here

    // Cleanup function (optional)
    return () => {
      // This runs on component unmount or when myState is about to change
      console.log('Cleanup or before next myState change');
    };
  }, [events]); // The second argument is the dependency array

  // console.log(events, "adds")
  // console.log(imagesObject, "dsd")

  // Effect to handle changes in events and show border
  React.useEffect(() => {
    if (events && Object.keys(events).length > 0) {
      // Set border style when events change
      setShowBorder(true);

      // Clear the border after 5 seconds
      const timeout = setTimeout(() => {
        setShowBorder(false);
      }, 5000);

      // Cleanup function
      return () => {
        clearTimeout(timeout); // Clear timeout on component unmount or before the next events change
      };
    }
  }, [events]);

  return (
    <>
      <section className="mb-4 flex items-end justify-between bg-white px-2 py-4">
        <div className="flex items-start gap-4">
          <Link
            href="/e/assessments-and-interviews/"
            className="btn-primary-light-pill"
          >
            back
          </Link>
          <div className="space-y-1">
            <h1 className="heading-2">{assessment.name}</h1>
            <ul className="heading-text flex gap-2 text-sm">
              <li className="font-medium">
                <span className="helper-text font-normal">Role: </span>
                {assessment.role}
              </li>
              <li className="font-medium">
                <span className="helper-text font-normal">Role level: </span>
                {assessment.role_level}
              </li>
              <li className="font-medium">
                <span className="helper-text font-normal">
                  Total questions:{' '}
                </span>
                {assessment.questions?.length}
              </li>
            </ul>
          </div>
        </div>
        <div className="flex gap-2">
          <button className="btn-primary-light">Invite candidates</button>
          <button className="btn-primary-light">1</button>
          <button className="btn-primary-light" title="options">
            <EllipsesVerticalIcon />
          </button>
        </div>
      </section>

      <div className="px-4 lg:px-6">
        <ul className="grid grid-cols-2 justify-center gap-[.625rem] rounded-2xl bg-white p-4 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
          {Object.values(imagesObject).map((image, index) => (
            <li
              key={index}
              className={`relative transition-all duration-500 ease-in-out ${index === indexInFocus ? 'col-span-2 row-span-2' : ''
                } ${showBorder ? 'border-[10px] rounded-[.625rem] border-red-500' : ''}`}
            >
              {/* Assuming image.image_data contains base64 image data */}
              <img
                src={image.imageData}
                className="aspect-[1.38/1] w-full rounded-[.625rem] object-cover transition-all duration-500 ease-in-out"
                alt={`Image ${index}`}
              />

              <div className="">
                <p
                  className={cn(
                    'absolute left-2 bottom-1 w-max truncate rounded-[.3125rem]  px-2.5 py-1 text-xxs text-white backdrop-blur-xl bg-[#755AE2]',
                    events.length === 0 ? 'hidden' : 'block'
                  )}>
                  {/* {events.eventType} */}

                  {Object.values(events).map((event_type, index) => (
                    <li
                      key={index}
                      className={`relative transition-all duration-500 ease-in-out ${index === indexInFocus ? 'col-span-2 row-span-2' : ''
                        }`}
                    >
                      {/* Assuming image.image_data contains base64 image data */}
                      <p
                        className=" w-full rounded-[.625rem] object-cover transition-all duration-500 ease-in-out text-white">{event_type.eventType}</p>

                    </li>
                  ))}
                </p>
              </div>


              <button
                onClick={() => {
                  setIndexInFocus(index);
                }}
                className={`absolute right-2 top-1 w-max max-w-[6.25rem] truncate rounded-[.3125rem] bg-white/5 px-2.5 py-1 text-xxs text-white backdrop-blur-xl ${index === indexInFocus ? 'hidden' : ''
                  }`}
              >
                Focus
              </button>
            </li>
          ))}
        </ul>



      </div>
    </>
  );
}

export default InvigilationDashboard;