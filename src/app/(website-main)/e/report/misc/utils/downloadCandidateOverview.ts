import { op } from '@tensorflow/tfjs';
import { CandidateDetails } from '../../../jobs/misc/types/PipelineType';
import { ReportResponse } from '../types/CandidateReportType';
import {
  prepareCombinedPDFContent,
  preparePDFContentForCandidate,
} from './prepareBulkPDFContent';

// Download single candidate PDF
export const downloadCandidateOverview = async (
  candidateName: string,
  companyName: string
): Promise<void> => {
  try {
    // Get current candidate data from DOM or state
    const printContent = document.querySelector('.container')?.innerHTML || '';

    const response = await fetch('/api/generate-pdf', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        htmlContent: `
          <!DOCTYPE html>
          <html>
            <head>
              <meta charset="UTF-8">
              <title>Candidate Report</title>
              <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .container { max-width: 800px; margin: 0 auto; }
              </style>
            </head>
            <body>
              <div class="container">${printContent}</div>
            </body>
          </html>
        `,
        candidateName,
        companyName,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(errorData?.error || 'Failed to generate PDF');
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${candidateName.replace(
      /\s+/g,
      '_'
    )}_${companyName.replace(/\s+/g, '_')}_Report.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading PDF:', error);
    throw error;
  }
};

// Download combined PDF for all candidates
export const downloadCombinedCandidateReports = async (
  candidatesData: CandidateDetails[],
  reportsData: ReportResponse[],
  companyName: string,
  options: {
    colorTheme?: string;
    fontFamily?: string;
    logoPreview?: string;
  }
): Promise<void> => {
  try {
    if (!candidatesData.length || !reportsData.length) {
      throw new Error('No candidate data available');
    }

    // Generate combined HTML content
    const htmlContent = prepareCombinedPDFContent(candidatesData, reportsData, {
      companyName,
      colorTheme: options.colorTheme || 'purple',
      fontFamily: options.fontFamily || 'Inter',
    });

    const response = await fetch('/api/generate-combined-pdf', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        htmlContent,
        companyName,
        options: {
          format: 'A4',
          printBackground: true,
          margin: {
            top: '20mm',
            right: '10mm',
            bottom: '20mm',
            left: '10mm',
          },
        },
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(errorData?.error || `Server error: ${response.status}`);
    }

    const blob = await response.blob();

    if (blob.size === 0) {
      throw new Error('Generated PDF is empty');
    }

    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;

    const timestamp = new Date().toISOString().split('T')[0];
    link.download = `${companyName.replace(
      /\s+/g,
      '_'
    )}_Combined_Reports_${timestamp}.pdf`;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading combined PDF:', error);
    throw error;
  }
};

// Download all PDFs as individual files in a ZIP
export const downloadAllCandidateReports = async (
  candidatesData: CandidateDetails[],
  reportsData: ReportResponse[],
  companyName: string
): Promise<void> => {
  try {
    if (!candidatesData.length || !reportsData.length) {
      throw new Error('No candidate data available');
    }

    // Prepare candidate reports for bulk download
    const candidateReports = candidatesData.map((candidate, index) => {
      const reportData = reportsData[index];

      return {
        candidateId: candidate.id,
        candidateName: candidate.name,
        htmlContent: preparePDFContentForCandidate(candidate, reportData, {
          companyName,
          colorTheme: 'blue',
          fontFamily: 'Inter',
        }),
      };
    });

    const response = await fetch('/api/generate-bulk-pdf', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        candidateReports,
        companyName,
        options: {
          format: 'A4',
          printBackground: true,
        },
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(errorData?.error || `Server error: ${response.status}`);
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;

    const timestamp = new Date().toISOString().split('T')[0];
    link.download = `${companyName.replace(
      /\s+/g,
      '_'
    )}_All_Reports_${timestamp}.zip`;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading bulk PDFs:', error);
    throw error;
  }
};

// Download all PDFs individually (one by one)
export const downloadAllCandidateReportsIndividually = async (
  candidatesData: CandidateDetails[],
  companyName: string,
  onProgress?: (current: number, total: number) => void
): Promise<void> => {
  try {
    const total = candidatesData.length;

    for (let i = 0; i < candidatesData.length; i++) {
      const candidate = candidatesData[i];
      onProgress?.(i + 1, total);

      await downloadCandidateOverview(candidate.name, companyName);

      // Add small delay to prevent overwhelming the browser
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  } catch (error) {
    console.error('Error downloading individual PDFs:', error);
    throw error;
  }
};
