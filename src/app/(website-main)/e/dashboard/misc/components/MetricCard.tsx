import React from 'react';

interface MetricCardProps {
  title: string;
  value: string | number;
  percentageChange?: number;
  comparisonText?: string;
  icon?: React.ReactNode;
  showComparison?: boolean;
}

export const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  percentageChange,
  comparisonText = 'vs Last month',
  icon,
  showComparison = true,
}) => {
  const getPercentageColor = (change?: number) => {
    if (!change) return 'text-gray-600';
    return change > 0
      ? 'text-green-600'
      : change < 0
      ? 'text-red-600'
      : 'text-gray-600';
  };

  return (
    <div className="flex w-full flex-col gap-2 rounded-md border bg-white p-4 shadow-sm">
      <h3 className="text-sm text-gray-500">{title}</h3>
      <div className="flex w-full items-center justify-between gap-2">
        <h1 className="text-2xl font-bold text-gray-800">{value}</h1>
        {icon ? (
          icon
        ) : (
          <div className="h-10 w-10 rounded-md bg-primary-light"></div>
        )}
      </div>
      {showComparison && (
        <div className="mt-2 flex w-full items-center gap-2">
          <span
            className={`flex text-sm font-medium ${getPercentageColor(
              percentageChange
            )}`}
          >
            {percentageChange !== undefined ? `${percentageChange}%` : ''}
          </span>
          <span className="text-gray-400">{comparisonText}</span>
        </div>
      )}
    </div>
  );
};

export default MetricCard;
