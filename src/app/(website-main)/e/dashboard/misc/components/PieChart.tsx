import React from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer } from 'recharts';

interface GenderMetric {
  label: string;
  percentage: number;
  count: number;
  growth: number;
  color?: string;
}

interface Props {
  data: GenderMetric[];
  innerRadius?: number;
  outerRadius?: number;
  total?: number;
}

const PieChartSection: React.FC<Props> = ({
  data,
  innerRadius = 60,
  outerRadius = 96,
  total = 0,
}) => {
  // fallback colors if not provided
  const defaultColors = ['#6E53D8', '#FF8C00', '#6E53D8'];

  const renderCustomizedLabel = (props: any) => {
    const {
      cx,
      cy,
      midAngle,
      innerRadius: ir,
      outerRadius: or,
      percent,
      index,
      payload,
    } = props;

    // place label slightly outside the outerRadius
    const radius = ir + (or - ir) * 1.12;
    const rad = (-midAngle * Math.PI) / 180;
    const x = cx + radius * Math.cos(rad);
    const y = cy + radius * Math.sin(rad);

    const text = `${Math.round(percent * 100)}%`;
    const dotColor =
      payload.color ?? defaultColors[index % defaultColors.length];

    // width / height choices approximate the pill in the screenshot
    const pillWidth = 56;
    const pillHeight = 28;
    const rx = 8;

    return (
      <g
        key={`label-${index}`}
        transform={`translate(${x - pillWidth / 2}, ${y - pillHeight / 2})`}
      >
        {/* pill background */}
        <rect
          x={0}
          y={0}
          width={pillWidth}
          height={pillHeight}
          rx={rx}
          ry={rx}
          fill="#ffffff"
          stroke="rgba(0,0,0,0.04)"
        />
        {/* small colored dot */}
        <circle cx={12} cy={pillHeight / 2} r={5} fill={dotColor} />
        {/* percent text */}
        <text
          x={24}
          y={pillHeight / 2 + 4}
          textAnchor="start"
          fontSize={12}
          fontFamily="Inter, Arial, sans-serif"
          fill="#0F172A"
          style={{ fontWeight: 600 }}
        >
          {text}
        </text>
      </g>
    );
  };

  return (
    <div
      style={{ width: '100%', height: 260, position: 'relative' }}
      aria-label="Gender diversity chart"
    >
      <ResponsiveContainer>
        <PieChart>
          <Pie
            data={data}
            dataKey="percentage"
            nameKey="label"
            cx="50%"
            cy="50%"
            innerRadius={innerRadius}
            outerRadius={outerRadius}
            paddingAngle={4}
            cornerRadius={10}
            stroke="#fff"
            strokeWidth={6}
            label={renderCustomizedLabel}
            isAnimationActive={false}
          >
            {data.map((entry, index) => (
              <Cell
                key={`slice-${entry.label}`}
                fill={
                  entry.color ?? defaultColors[index % defaultColors.length]
                }
              />
            ))}
          </Pie>
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};

export default PieChartSection;
