import React from 'react';
import { Assessment } from '../types/dashboard';

export const OngoingAssessments = ({ items }: { items: Assessment[] }) => {
  return (
    <div className="h-full rounded bg-white p-4 shadow">
      <h3 className="mb-3 text-lg font-medium">Ongoing Assessments</h3>
      <div className="space-y-4">
        {items.map((a, i) => (
          <div key={i}>
            <div className="mb-1 flex justify-between">
              <div className="text-sm font-medium">{a.name}</div>
              <div className="text-sm text-gray-600">{a.in_progress}%</div>
            </div>
            <div className="h-2 w-full rounded bg-gray-100">
              <div
                className="h-2 rounded bg-primary"
                style={{ width: `${a.in_progress}%` }}
              />
            </div>
            <div className="mt-1 text-xs text-gray-500">
              Invited: {a.invited} • Completed: {a.completed}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
export default OngoingAssessments;
