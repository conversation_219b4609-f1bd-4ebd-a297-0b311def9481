import React from 'react';
import { TopRole } from '../types/dashboard';

export const TopRoles = ({ items }: { items: TopRole[] }) => {
  return (
    <div className="h-full w-full rounded-xl bg-white p-4 shadow">
      <h3 className="mb-3 text-lg font-medium">Top Roles in Demand</h3>
      <div className="space-y-3">
        {items.map(r => (
          <div key={r.role}>
            <div className="mb-1 flex justify-between">
              <div className="text-sm">{r.role}</div>
              <div className="text-sm font-medium">{r.percentage}%</div>
            </div>
            <div className="h-2 w-full rounded bg-gray-100">
              <div
                className="h-2 rounded bg-primary"
                style={{ width: `${r.percentage}%` }}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
export default TopRoles;
