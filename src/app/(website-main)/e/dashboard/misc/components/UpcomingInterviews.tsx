import React from 'react';
import { Spinner } from '@/components/shared/icons';
import { Interview } from '../types/dashboard';

export const UpcomingInterviews: React.FC<{
  items?: Interview[];
  loading?: boolean;
  error?: Error | null;
}> = ({ items = [], loading, error }) => {
  if (loading)
    return (
      <div className="rounded bg-white p-4 shadow">
        <Spinner />
      </div>
    );
  if (error)
    return (
      <div className="p-4 text-red-600">
        Failed to load interviews: {error.message}
      </div>
    );

  return (
    <div className="h-full rounded-lg bg-white p-4 shadow">
      <h3 className="mb-3 text-lg font-medium">Upcoming Interviews</h3>
      <table className="w-full border-collapse text-sm">
        <thead className="text-left text-gray-500">
          <tr>
            <th className="pb-2 font-medium">Candidate</th>
            <th className="pb-2 font-medium">Interviewer</th>
            <th className="pb-2 font-medium">Date & Time</th>
          </tr>
        </thead>
        <tbody>
          {items.map((it, i) => (
            <tr key={i} className="border-t">
              {/* Candidate column */}
              <td className="py-3">
                <div className="flex items-center space-x-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-light text-sm font-medium text-primary">
                    {(() => {
                      const parts = (it.assessment_interview_name || '')
                        .trim()
                        .split(/\s+/);
                      if (parts.length === 0) return '?';
                      if (parts.length === 1) return parts[0][0].toUpperCase();
                      return (
                        parts[0][0] + parts[parts.length - 1][0]
                      ).toUpperCase();
                    })()}
                  </div>

                  <div className="flex flex-col">
                    <span className="font-semibold">
                      {it.assessment_interview_name}
                    </span>
                    <span className="text-sm text-gray-500">{it.role}</span>
                  </div>
                </div>
              </td>

              {/* Interviewer column */}
              <td className="py-3 align-top">
                <div className="flex flex-col">
                  <span className="font-semibold">{it.interviewer}</span>
                  <span className="text-sm text-gray-500">Interviewer</span>
                </div>
              </td>

              {/* Date/Time column */}
              <td className="py-3 align-top">
                {(() => {
                  const dt = new Date(it.interview_date);
                  return (
                    <div className="flex flex-col">
                      <span className="font-semibold">
                        {dt.toLocaleDateString()}
                      </span>
                      <span className="text-sm text-gray-500">
                        {dt.toLocaleTimeString([], {
                          hour: '2-digit',
                          minute: '2-digit',
                        })}
                      </span>
                    </div>
                  );
                })()}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default UpcomingInterviews;
