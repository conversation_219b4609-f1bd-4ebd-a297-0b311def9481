import React from 'react';
import { Spinner } from '@/components/shared/icons';
import useGetDashboard from '../api/getDashboard';
import GenderDiversity from './GenderDiversity';
import JobPerformanceChart from './JobPerformanceChart';
import MetricsCards from './MetricsCards';
import OngoingAssessments from './OngoingAssessments';
import TopCandidates from './TopCandidates';
import TopRoles from './TopRoles';
import UpcomingInterviews from './UpcomingInterviews';

const DashboardPage: React.FC = () => {
  const { data } = useGetDashboard();

  if (!data) {
    return <Spinner />;
  }

  return (
    <>
      <main className="min-h-screen overflow-auto  bg-gray-50 p-6">
        <div className="mx-auto max-w-7xl">
          <section className="mb-6">
            <MetricsCards items={data.stats} />
          </section>

          <div className="h-[600px] overflow-y-scroll pb-24 2xl:h-[700px]">
            <div className="grid grid-cols-1 gap-4 lg:grid-cols-5">
              <div className="lg:col-span-3">
                <JobPerformanceChart data={data.job_performance} />
              </div>

              <div className=" lg:col-span-2">
                <GenderDiversity data={data.applicant_demographics} />
              </div>

              <div className=" lg:col-span-3">
                <UpcomingInterviews items={data.upcoming_interviews} />
              </div>

              <div className=" lg:col-span-2">
                <TopRoles items={data.top_roles} />
              </div>

              <div className=" lg:col-span-3">
                <OngoingAssessments items={data.ongoing_assessments} />
              </div>

              <div className=" lg:col-span-2">
                <TopCandidates items={data.recommended_candidates} />
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  );
};

export default DashboardPage;
