import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YA<PERSON>s,
} from 'recharts';
import { MonthlyData } from '../types/dashboard';

interface Props {
  data: MonthlyData[];
  appliedKey?: keyof MonthlyData;
  hiredKey?: keyof MonthlyData;
  height?: number;
}

export const BarChartSection: React.FC<Props> = ({
  data,
  appliedKey = 'applied',
  hiredKey = 'hired',
  height = 250,
}) => {
  return (
    <div style={{ width: '100%', height }} aria-label="Job performance chart">
      <ResponsiveContainer>
        <BarChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Bar dataKey={appliedKey as string} name="Applied" fill="#60A5FA" />
          <Bar dataKey={hiredKey as string} name="Hired" fill="#34D399" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};
export default BarChartSection;
