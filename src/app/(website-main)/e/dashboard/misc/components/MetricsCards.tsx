import React from 'react';
import { Stats } from '../types/dashboard';
import MetricCard from './MetricCard';

export const MetricsCards = ({ items }: { items: Stats }) => {
  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-3 lg:grid-cols-5">
      <MetricCard
        title="Active Jobs"
        value={items.active_jobs}
        percentageChange={items.jobs_percentage_change}
      />

      <MetricCard
        title="Total Applicants"
        value={items.total_applicants}
        percentageChange={items.jobs_percentage_change}
      />

      <MetricCard
        title="Hires"
        value={items.hires}
        percentageChange={items.hires_percentage_change}
      />

      <MetricCard
        title="Avg. Time-to-Hire"
        value={items.avg_time_to_hire}
        percentageChange={items.time_to_hire_improvement}
      />

      <MetricCard
        title="Clients"
        value={items.clients}
        showComparison={false}
      />
    </div>
  );
};

export default MetricsCards;
