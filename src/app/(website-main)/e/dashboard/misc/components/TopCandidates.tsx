import { Star } from 'lucide-react';
import React from 'react';
import { RecommendedCandidate } from '../types/dashboard';

export interface TopCandidatesProps {
  items?: RecommendedCandidate[];
  limit?: number;
  title?: string;
  borderColorClass?: string;
  starColorClass?: string;
  scoreColorClass?: string;
  avatarBgClass?: string;
  avatarTextClass?: string;
  overallScoreLabel?: string;
  showSkeletonRows?: boolean;
  className?: string;
  'data-testid'?: string;
}

const computeInitials = (name?: string) => {
  if (!name) return '??';
  const parts = name.trim().split(/\s+/).slice(0, 2);
  return parts.map(p => p[0]?.toUpperCase() ?? '').join('');
};

const SkeletonRow: React.FC = () => (
  <li className="flex items-center justify-between py-3">
    <div className="flex items-center gap-4">
      <div className="h-12 w-12 animate-pulse rounded-full bg-gray-100" />
      <div className="space-y-2">
        <div className="h-3 w-40 animate-pulse rounded bg-gray-100" />
        <div className="h-3 w-28 animate-pulse rounded bg-gray-100" />
      </div>
    </div>
    <div className="flex flex-col items-end gap-1">
      <div className="h-4 w-10 animate-pulse rounded bg-gray-100" />
      <div className="h-3 w-16 animate-pulse rounded bg-gray-100" />
    </div>
  </li>
);

export const TopCandidates: React.FC<TopCandidatesProps> = ({
  items = [],
  limit = 5,
  title,
  borderColorClass = 'border-[#1D6FD8]',
  starColorClass = 'text-[#F8A629]',
  scoreColorClass = 'text-[#12B669]',
  avatarBgClass = 'bg-[#DBD6F766]',
  avatarTextClass = 'text-[#5C3EF4]',
  overallScoreLabel = 'Overall score',
  showSkeletonRows = false,
  className = '',
  'data-testid': testId = 'top-candidates',
}) => {
  const list = items.slice(0, limit);
  const finalTitle = title || `Top ${limit} Recommended Candidates`;

  return (
    <div
      className={`h-full rounded-xl  bg-white p-6 shadow ${className}`}
      data-testid={testId}
    >
      {/* Header */}
      <div className="mb-6 flex items-center gap-3">
        <Star className={`${starColorClass}`} size={22} strokeWidth={2} />
        <h3 className="text-lg font-semibold leading-none tracking-tight text-black">
          {finalTitle}
        </h3>
      </div>

      {/* List */}
      {list.length > 0 && (
        <ul className="divide-y divide-transparent">
          {list.map((c, i) => {
            const initials = computeInitials(c.name);
            return (
              <li
                key={i}
                className="flex items-center justify-between py-3"
                data-testid={`${testId}-row-${i}`}
              >
                <button
                  type="button"
                  className={`group flex flex-1 items-center gap-4 text-left `}
                >
                  <span className={`text-sm font-semibold ${avatarTextClass}`}>
                    {initials}
                  </span>

                  {/* Name & Role */}
                  <div className="flex min-w-0 flex-col">
                    <span className="text-sm font-semibold leading-snug text-gray-900">
                      {c.name}
                    </span>
                    <span className="truncate text-sm leading-snug text-gray-500">
                      {c.role || '—'}
                    </span>
                  </div>
                </button>

                {/* Right metrics */}
                <div className="ml-4 flex flex-col items-end">
                  <div className="flex items-center gap-2">
                    {typeof c.overall_score === 'number' && (
                      <span
                        className={`text-base font-semibold tabular-nums ${scoreColorClass}`}
                        aria-label={`${c.overall_score}% ${overallScoreLabel}`}
                      >
                        {c.overall_score}%
                      </span>
                    )}
                    <Star
                      size={18}
                      className={`${starColorClass}`}
                      fill="#F8A629"
                    />
                  </div>
                  <span className="mt-1 text-xs text-gray-500">
                    {overallScoreLabel}
                  </span>
                </div>
              </li>
            );
          })}
        </ul>
      )}
    </div>
  );
};

export default TopCandidates;
