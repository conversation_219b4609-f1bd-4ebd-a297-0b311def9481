import React from 'react';
import {
  Bar,
  <PERSON><PERSON>hart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  TooltipProps,
  XAxis,
  YAxis,
} from 'recharts';
import { JobPerformance } from '../types/dashboard';

const JobPerformanceChart = ({ data }: { data: JobPerformance }) => {
  const CustomTooltip = ({
    active,
    payload,
    label,
  }: TooltipProps<any, any>) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded-lg border border-gray-200 bg-white p-3 shadow-lg">
          <p className="mb-2 text-sm font-medium text-gray-900">{label}</p>
          {payload.map((entry, index) => (
            <div key={index} className="mb-1 flex items-center gap-2">
              <div
                className="h-3 w-3 rounded-sm"
                style={{ backgroundColor: entry.color }}
              ></div>
              <span className="text-sm capitalize text-gray-600">
                {entry.dataKey}:
              </span>
              <span className="text-sm font-semibold text-gray-900">
                {entry.value}
              </span>
            </div>
          ))}
        </div>
      );
    }
    return null;
  };
  return (
    <div
      className="rounded-lg bg-white p-6 shadow"
      style={{ width: '100%', minHeight: '400px' }}
    >
      {/* Header */}
      <div className="mb-8 flex flex-col items-center justify-between md:flex-row">
        <h2 className="text-xl font-medium text-gray-900">Job performance</h2>
        <div className="flex items-center gap-6">
          <div className="flex items-center gap-2">
            <div
              className="h-3 w-3 rounded-sm"
              style={{ backgroundColor: '#C4B5FD' }}
            ></div>
            <span className="text-sm text-gray-600">Applied</span>
          </div>
          <div className="flex items-center gap-2">
            <div
              className="h-3 w-3 rounded-sm"
              style={{ backgroundColor: '#7C3AED' }}
            ></div>
            <span className="text-sm text-gray-600">Hired</span>
          </div>
        </div>
        <div className="text-sm text-gray-500">This year</div>
      </div>

      {/* Chart */}
      <div
        style={{ width: '100%', height: '300px' }}
        className="overflow-x-auto"
      >
        <ResponsiveContainer>
          <BarChart
            data={data.monthly_data}
            margin={{
              top: 20,
              right: 20,
              left: 10,
              bottom: 5,
            }}
            barCategoryGap="20%"
          >
            <CartesianGrid
              strokeDasharray="3 3"
              stroke="#F3F4F6"
              horizontal={true}
              vertical={true}
            />
            <XAxis
              dataKey="month"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#9CA3AF' }}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#9CA3AF' }}
              domain={[0, 900]}
              tickCount={6}
            />
            <Tooltip
              content={<CustomTooltip />}
              cursor={{ fill: 'rgba(0, 0, 0, 0.05)' }}
            />
            <Bar
              dataKey="applied"
              fill="#DBD6F7"
              radius={[2, 2, 0, 0]}
              barSize={32}
            />
            <Bar
              dataKey="hired"
              fill="#755AE2"
              radius={[2, 2, 0, 0]}
              barSize={32}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default JobPerformanceChart;
