import React from 'react';
import { ApplicantDemographics } from '../types/dashboard';
import PieChartSection from './PieChart';

export const GenderDiversity = ({ data }: { data: ApplicantDemographics }) => {
  const items = [
    {
      label: 'Male',
      percentage: data.male_percentage,
      count: data.male_count,
      growth: data.male_percentage,
      color: '#FF8A00',
    },
    {
      label: 'Female',
      percentage: data.female_percentage,
      count: data.female_count,
      growth: data.female_percentage,
      color: '#6E53D8',
    },
    {
      label: 'Not identified',
      percentage: data.not_identified_percentage,
      count: data.not_identified_count,
      growth: data.not_identified_percentage,
      color: '#00A37D',
    },
  ];

  return (
    <div className="h-full w-full rounded-xl bg-white p-4 shadow">
      <div className="mb-6">
        <h3 className="text-xl font-medium text-slate-900">
          Gender diversity metrics
        </h3>
      </div>

      <div className="flex items-center gap-4">
        {/* Left: donut chart */}
        <div className="relative w-3/5">
          <PieChartSection
            data={items}
            innerRadius={74}
            outerRadius={110}
            total={data.total_applicants}
          />

          {/* center overlay */}
          <div
            className="absolute left-1/2 top-1/2 flex -translate-x-1/2 -translate-y-1/2 flex-col items-center"
            style={{ pointerEvents: 'none' }}
          >
            <div
              className="flex items-center justify-center rounded-full bg-white"
              style={{
                width: 132,
                height: 132,
                boxShadow: '0 6px 24px rgba(15, 23, 42, 0.06)',
              }}
            >
              <div className="text-center">
                <div className="text-3xl font-semibold text-indigo-700">
                  {data.total_applicants.toLocaleString()}
                </div>
                <div className="text-sm text-slate-400">Applicants</div>
              </div>
            </div>
          </div>
        </div>

        {/* Right: legend */}
        <div className="w-2/5">
          <ul className="space-y-6">
            {items.map(it => (
              <li key={it.label} className="flex items-center gap-6">
                <div className="flex w-40 items-center gap-4">
                  <span
                    className="h-3 w-3 rounded-full"
                    style={{ background: it.color ?? '#6C5CE7' }}
                  />
                  <div className="flex-1">
                    <div className="flex items-baseline gap-4">
                      <div className="text-sm font-semibold text-slate-900">
                        {Math.round(it.percentage)}%
                      </div>
                      <div className="text-sm font-medium text-slate-700">
                        {it.count.toLocaleString()}
                      </div>
                    </div>
                    <div className="mt-1 text-sm text-slate-400">
                      {it.label}
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default GenderDiversity;
