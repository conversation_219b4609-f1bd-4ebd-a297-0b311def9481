export interface DashboardData {
  stats: Stats;
  job_performance: JobPerformance;
  applicant_demographics: ApplicantDemographics;
  top_roles: TopRole[];
  recommended_candidates: RecommendedCandidate[];
  ongoing_assessments: Assessment[];
  upcoming_interviews: Interview[];
}

/* ---------- STATS ---------- */
export interface Stats {
  active_jobs: number;
  jobs_percentage_change: number;
  total_applicants: number;
  applicants_percentage_change: number;
  hires: number;
  hires_percentage_change: number;
  avg_time_to_hire: number;
  time_to_hire_improvement: number;
  clients: number;
}

/* ---------- JOB PERFORMANCE ---------- */
export interface JobPerformance {
  monthly_data: MonthlyData[];
}

export interface MonthlyData {
  month: string;
  applied: number;
  hired: number;
}

/* ---------- APPLICANT DEMOGRAPHICS ---------- */
export interface ApplicantDemographics {
  total_applicants: number;
  male_percentage: number;
  male_count: number;
  female_percentage: number;
  female_count: number;
  not_identified_percentage: number;
  not_identified_count: number;
}

/* ---------- TOP ROLES ---------- */
export interface TopRole {
  role: string;
  percentage: number;
  candidate: Candidate | null;
}

export interface Candidate {
  name: string;
  title: string;
}

/* ---------- RECOMMENDED CANDIDATES ---------- */
export interface RecommendedCandidate {
  name: string;
  role: string;
  overall_score: number;
}

/* ---------- ONGOING ASSESSMENTS ---------- */
export interface Assessment {
  name: string;
  invited: number;
  completed: number;
  in_progress: number;
  pending: number;
}

/* ---------- UPCOMING INTERVIEWS ---------- */
export interface Interview {
  name: string;
  role: string;
  interviewer: string;
  interview_date: string; // could be Date if parsed
  interview_time: string; // could be Date if parsed
  assessment_interview_name: string;
}
