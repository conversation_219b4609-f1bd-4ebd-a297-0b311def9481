import { useQuery, UseQueryResult } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { Axios } from '@/lib/api/axios';
import { DashboardData } from '../types/dashboard';

const getDashboard = async (): Promise<DashboardData> => {
  const response = await Axios.get<DashboardData>('/recruiter/dashboard/');
  return response.data;
};

const useGetDashboard = (): UseQueryResult<DashboardData, AxiosError> => {
  return useQuery<DashboardData, AxiosError>(
    ['recruiter-dashboard'],
    getDashboard,
    {
      enabled: true,
      keepPreviousData: true,
    }
  );
};

export default useGetDashboard;
