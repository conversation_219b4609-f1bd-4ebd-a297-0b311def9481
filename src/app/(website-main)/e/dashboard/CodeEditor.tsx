import React, { useRef } from 'react';
import MonacoEditor from '@monaco-editor/react';

const CodeEditor = () => {
const editorRef = useRef<any>(null);

function handleEditorDidMount(editor: any, monaco: any) {
    // You can customize the editor here
    editorRef.current = editor;
}

function handleEditorChange(newValue: string | undefined, event: any) {
    // Handle editor value changes here
    console.log('Editor value:', newValue);
    console.log('EVent:', event);
}

  return (
    <MonacoEditor
      height="500px"
      language="javascript"
    //   theme=""
      onMount={handleEditorDidMount}
      onChange={handleEditorChange}
    />
  );
};

export default CodeEditor;