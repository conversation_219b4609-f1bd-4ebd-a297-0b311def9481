import { Axios } from '@/lib/api/axios';
import { useQuery } from '@tanstack/react-query';

const getPublicJobs = async (slug: string) => {
  const res = await Axios.get(`/recruiter/public/jobs/${slug}/`);
  return res.data;
};

export const usePublicJobs = (slug: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: ['career-page-public-jobs', slug],
    queryFn: () => getPublicJobs(slug),
    enabled: (options?.enabled ?? true) && !!slug,
    keepPreviousData: true,
  });
};

export default usePublicJobs;

