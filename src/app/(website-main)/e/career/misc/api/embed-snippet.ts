import { useQuery } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import type { ApiResponse, EmbedSnippet } from './types';

const getEmbedSnippet = async () => {
  const res = await Axios.get<ApiResponse<EmbedSnippet>>('/recruiter/career-page/embed-snippet/');
  return res.data;
};

export const useEmbedSnippet = (options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: ['career-page-embed-snippet'],
    queryFn: getEmbedSnippet,
    enabled: options?.enabled ?? true,
  });
};

export default useEmbedSnippet;

