import { Axios } from '@/lib/api/axios';
import { useQuery } from '@tanstack/react-query';
import type { ApiResponse, CareerPageStatus } from './types';

const getCareerPageStatus = async () => {
  const res = await Axios.get<ApiResponse<CareerPageStatus>>('/recruiter/career-page/status/');
  return res.data;
};

export const useCareerPageStatus = () => {
  return useQuery({
    queryKey: ['career-page-status'],
    queryFn: getCareerPageStatus,
    staleTime: 60 * 1000,
  });
};

export default useCareerPageStatus;

