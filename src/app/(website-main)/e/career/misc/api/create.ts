import { Axios } from '@/lib/api/axios';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import type { ApiResponse, CareerPageDetails, CreateCareerPagePayload } from './types';

const createOrUpdateCareerPage = async (payload: CreateCareerPagePayload) => {
  // If any file is present, send multipart/form-data; otherwise send JSON
  const hasFiles = !!(payload.logo_file || payload.banner_file);
  if (hasFiles) {
    const form = new FormData();
    form.append('setup_type', payload.setup_type);
    form.append('slug', payload.slug);
    form.append('company_description', payload.company_description);
    if (payload.custom_domain) form.append('custom_domain', payload.custom_domain);
    form.append('branding_config', JSON.stringify(payload.branding_config));
    if (payload.logo_file) form.append('logo_file', payload.logo_file);
    if (payload.banner_file) form.append('banner_file', payload.banner_file);

    const res = await Axios.post<ApiResponse<CareerPageDetails>>(
      '/recruiter/career-page/create/',
      form,
      { headers: { 'Content-Type': 'multipart/form-data' } }
    );
    return res.data;
  }

  const jsonPayload = {
    setup_type: payload.setup_type,
    slug: payload.slug,
    company_description: payload.company_description,
    custom_domain: payload.custom_domain,
    branding_config: payload.branding_config,
  };
  const res = await Axios.post<ApiResponse<CareerPageDetails>>('/recruiter/career-page/create/', jsonPayload);
  return res.data;
};

export const useCreateCareerPage = () => {
  const queryClient = useQueryClient();
  return useMutation(createOrUpdateCareerPage, {
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['career-page-status'] });
      queryClient.invalidateQueries({ queryKey: ['career-page-embed-snippet'] });
    },
  });
};

export default useCreateCareerPage;

