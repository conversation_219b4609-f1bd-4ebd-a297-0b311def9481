export type SetupType = 'HOSTED' | 'EMBED';

export type ApiResponse<T> = {
  success: boolean;
  status_code: number;
  message: string;
  data: T;
};

export type CareerPageStatus = {
  has_career_page: boolean;
  setup_type?: SetupType;
  slug?: string;
  url?: string; // final public URL
  custom_domain?: string | null;
};

// Branding types
export type ColorPalette = {
  primary: string;
  // secondary: string;
  // accent: string;
  // background: string;
  // surface: string;
  // text: string;
  // muted_text: string;
  // success: string;
  // warning: string;
  // error: string;
};

// export type TypographySettings = {
//   heading_font: string;
//   body_font: string;
//   weight_heading: number;
//   weight_body: number;
//   base_size: number; // px
//   scale: number; // typographic scale multiplier
// };

export type BrandingConfig = {
  brand: {
    colors: ColorPalette;
    // border_radius: number;
    // typography: TypographySettings;
    logo_url?: string;
  };
  banner?: {
    image_url?: string;
  };
};

export type CreateCareerPageDto = {
  setup_type: Extract<SetupType, 'HOSTED'>; // hosted flow
  slug: string;
  company_description: string;
  custom_domain?: string;
  branding_config: BrandingConfig;
};

// File attachments for multipart submissions
export type CareerPageFiles = {
  logo_file?: File | null;
  banner_file?: File | null;
};

export type CreateCareerPagePayload = CreateCareerPageDto & CareerPageFiles;

export type CareerPageDetails = {
  id?: string;
  setup_type: SetupType;
  slug: string;
  url?: string; // resolved public URL
  custom_domain?: string | null;
  company_description?: string;
  branding_config?: BrandingConfig;
};

export type EmbedSnippet = {
  snippet: string;
  width?: number | string;
  height?: number | string;
};

export type SlugAvailability = {
  available: boolean;
  suggestions?: string[];
};

export type PublicJob = {
  id: string | number;
  title: string;
  location?: string;
  employment_type?: string;
  created_at?: string;
};

export type PublicJobsResponse = {
  count?: number;
  results: PublicJob[];
};

