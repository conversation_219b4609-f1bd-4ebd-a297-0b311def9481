import { useQuery } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import type { ApiResponse, SlugAvailability } from './types';

const checkSlug = async (slug: string) => {
  const res = await Axios.get<ApiResponse<SlugAvailability>>('/recruiter/career-page/check-slug/', { params: { slug } });
  return res.data;
};

export const useCheckSlug = (slug: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: ['career-page-check-slug', slug],
    queryFn: () => checkSlug(slug),
    enabled: (options?.enabled ?? true) && !!slug,
  });
};

export default useCheckSlug;

