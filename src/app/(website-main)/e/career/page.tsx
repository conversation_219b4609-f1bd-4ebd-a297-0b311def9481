'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, CardTitle, InfoModal, Input, Label } from '@/components/shared';
import { CopyIcon } from 'lucide-react';
import React, { useEffect, useMemo, useState } from 'react';
import toast from 'react-hot-toast';
import CareerPageConfigForm from './components/CareerPageConfigForm';
import {
  useCareerPageStatus,
  useCheckSlug,
  useCreateCareerPage,
  useEmbedSnippet,
} from './misc/api';

// Types
export type TSetupType = 'HOSTED' | 'EMBED';


const RecruiterCareerPage: React.FC = () => {
  const [mode, setMode] = useState<'VIEW' | 'CREATE'>('VIEW');
  const [slug, setSlug] = useState('');
  const [copied, setCopied] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [step, setStep] = useState<'SLUG' | 'CONFIG'>('SLUG');

  // Success URL for modal
  const [successUrl, setSuccessUrl] = useState('');


  const { data: statusData, isLoading: isLoadingStatus, refetch: refetchStatus } = useCareerPageStatus();
  const hasCareerPage = !!statusData?.data?.has_career_page;


  const existingType = statusData?.data?.setup_type as TSetupType | undefined;
  const existingSlug = statusData?.data?.slug || '';

  useEffect(() => {
    if (hasCareerPage && existingSlug) setSlug(existingSlug);
  }, [hasCareerPage, existingSlug]);

  // Debounced slug check
  const [debounced, setDebounced] = useState('');
  useEffect(() => {
    const t = setTimeout(() => setDebounced(slug.trim()), 400);
    return () => clearTimeout(t);
  }, [slug]);

  const {
    data: slugCheck,
    refetch: refetchSlug,
    isFetching: checkingSlug,
  } = useCheckSlug(debounced, { enabled: false });

  useEffect(() => {
    if (debounced) refetchSlug();
  }, [debounced, refetchSlug]);

  const { data: snippetData } = useEmbedSnippet({ enabled: hasCareerPage && existingType === 'EMBED' });

  const { mutate: createCareerPage, isLoading: creating } = useCreateCareerPage();

  const hostedUrl = useMemo(() => {
    const s = slug || existingSlug;
    if (!s) return '';
    // Prefer subdomain pattern as per requirement
    return `https://${s}.app.getlinked.ai`;
  }, [slug, existingSlug]);

  const onContinueToConfig = (e: React.FormEvent) => {
    e.preventDefault();
    if (!slug) return toast.error('Please enter a slug');
    if (slugCheck && slugCheck?.data && slugCheck.data.available === false && slug !== existingSlug) {
      return toast.error('Slug is not available');
    }
    setStep('CONFIG');
  };

  const handleSubmitConfig = (payload: any) => {
    createCareerPage(payload, {
      onSuccess: (res: any) => {
        toast.success('Career page saved');
        setMode('VIEW');
        refetchStatus();
        const d = res?.data as { url?: string; custom_domain?: string } | undefined;
        const url = d?.url || (d?.custom_domain ? (/^https?:\/\//i.test(d.custom_domain) ? d.custom_domain : `https://${d.custom_domain}`) : hostedUrl);
        setSuccessUrl(url || hostedUrl);
        setShowSuccess(true);
      },
      onError: (err: any) => {
        const msg = err?.response?.data?.message || err?.response?.data?.error || 'Failed to save career page';
        toast.error(msg);
      },
    });
  };

  const copy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      toast.success('Copied');
      setTimeout(() => setCopied(false), 1500);
    } catch (e) {
      toast.error('Failed to copy');
    }
  };

  return (
    <main className="main">
      <div className="maincontentwithheader grid grid-rows-[max-content_1fr] flex-col bg-[#F8F9FB] h-full overflow-y-hidden ">
        <AppHeaderRecruiter title="Career page" className="bg-white md:!pt-2" />

        <section className="p-4 md:p-6 overflow-y-auto">
          {/* Loading State */}
          {isLoadingStatus ? (
            <div className="grid min-h-[60vh] place-content-center text-[#7D8590]">Loading...</div>
          ) : (
            <>
              {mode === 'CREATE' || !hasCareerPage ? (
                <div className="grid gap-6 md:grid-cols-2">
                  {/* EMBED card */}
                  <Card className="bg-white">
                    <CardHeader>
                      <CardTitle className="text-base">I already have a career page</CardTitle>
                      <p className="text-xs text-[#7D8590]">Embed your Getlinked job listings into your existing career page.</p>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <Label className="text-xs">EMBED JOB LISTINGS</Label>
                      <div className="rounded-lg border p-3 bg-[#F8F9FB] text-xs text-[#7D8590] whitespace-pre-wrap break-all">
                        {snippetData?.data?.snippet || '<iframe src="https://{yourcompany}.app.getlinked.ai" width="100%" height="800px" style="border:none;"></iframe>'}
                      </div>
                      <Button size="small" onClick={() => copy(snippetData?.data?.snippet || '')} disabled={!snippetData?.data?.snippet}>
                        {copied ? 'Copied!' : 'Copy snippet'}
                      </Button>
                      <p className="text-[11px] text-[#7D8590]">Paste this into your site’s career page code where you want job listings to appear.</p>
                    </CardContent>
                  </Card>

                  {/* HOSTED setup card */}
                  <Card className="bg-white">
                    <CardHeader>
                      <CardTitle className="text-base">I don’t have a career page</CardTitle>
                      <p className="text-xs text-[#7D8590]">Get a hosted career page from Getlinked where your jobs will appear automatically.</p>
                    </CardHeader>
                    <CardContent>
                      {step === 'SLUG' ? (
                        <form onSubmit={onContinueToConfig} className="space-y-4">
                          <div className="space-y-1">
                            <Label htmlFor="slug">Choose your page slug</Label>
                            <Input
                              id="slug"
                              placeholder="yourcompany"
                              value={slug}
                              onChange={(e) => setSlug(e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, ''))}
                            />
                            {debounced && (
                              <p className="text-[11px] text-[#7D8590]">
                                {checkingSlug
                                  ? 'Checking availability…'
                                  : slugCheck?.data?.available === false && slug !== existingSlug
                                  ? 'Slug not available'
                                  : 'Slug available'}
                              </p>
                            )}
                          </div>

                          <div className="space-y-1">
                            <Label>Generated URL</Label>
                            <div className="flex items-center gap-2">
                              <Input readOnly value={hostedUrl} />
                              <Button type="button" size="small" onClick={() => copy(hostedUrl)} disabled={!hostedUrl}>
                                <CopyIcon size={16} className="mr-1" /> Copy link
                              </Button>
                            </div>
                            <p className="text-[11px] text-[#7D8590]">Direct link to your career page that you can share on your website</p>
                          </div>

                          <div className="flex items-center gap-3 pt-2">
                            <Button type="submit" disabled={!slug} className="">Setup hosted page</Button>
                          </div>
                        </form>
                      ) : (
                        <CareerPageConfigForm slug={slug} loading={creating} onBack={() => setStep('SLUG')} onSubmit={handleSubmitConfig} />
                      )}
                    </CardContent>
                  </Card>
                </div>
              ) : (
                // Existing career page state
                <div className="grid gap-6 md:grid-cols-2">
                  {existingType === 'EMBED' ? (
                    <Card className="bg-white">
                      <CardHeader>
                        <CardTitle className="text-base">Embed job listings</CardTitle>
                        <p className="text-xs text-[#7D8590]">Use the snippet below to display your live job listings on your site.</p>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="rounded-lg border p-3 bg-[#F8F9FB] text-xs text-[#7D8590] whitespace-pre-wrap break-all">
                          {snippetData?.data?.snippet}
                        </div>
                        <Button size="small" onClick={() => copy(snippetData?.data?.snippet || '')}>
                          {copied ? 'Copied!' : 'Copy snippet'}
                        </Button>
                        <p className="text-[11px] text-[#7D8590]">Paste this into your site’s career page code.</p>
                      </CardContent>
                    </Card>
                  ) : (
                    <Card className="bg-white">
                      <CardHeader>
                        <CardTitle className="text-base">Your career page is ready</CardTitle>
                        <p className="text-xs text-[#7D8590]">Your custom career page is live and ready to use</p>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Input readOnly value={hostedUrl} />
                          <Button type="button" size="small" onClick={() => copy(hostedUrl)}>
                            {copied ? 'Copied!' : 'Copy link'}
                          </Button>
                        </div>
                        <p className="text-[11px] text-[#7D8590]">Direct link to your career page that you can share on your website</p>
                        <div className="pt-3">
                          <Button variant="secondary" size="small" onClick={() => setMode('CREATE')}>Modify setup</Button>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              )}
            </>
          )}
        </section>

        <InfoModal
          isModalOpen={showSuccess}
          closeModal={() => setShowSuccess(false)}
          title="Your career page is ready"
          heading="Career page created"
        >
          <div className="mt-4 space-y-2">
            <Input readOnly value={successUrl || hostedUrl} />
            <div className="flex justify-start">
              <Button size="small" onClick={() => copy(successUrl || hostedUrl)}>
                {copied ? 'Copied!' : 'Copy link'}
              </Button>
            </div>
            <p className="text-[12px] text-[#7D8590]">Direct link to your career page that you can share on your website</p>
          </div>
        </InfoModal>

      </div>
    </main>
  );
};

export default RecruiterCareerPage;
