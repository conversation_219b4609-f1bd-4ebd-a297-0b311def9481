"use client";

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Input, Label } from "@/components/shared";
import GLFileUpload from "@/components/shared/file-upload";
import { Textarea as GLTextarea } from "@/components/shared/textarea";
import React from "react";
import { SketchPicker } from "react-color";
import type { CreateCareerPagePayload } from "../misc/api/types";

export type CareerPageConfigFormProps = {
  slug: string;
  loading?: boolean;
  onBack: () => void;
  onSubmit: (payload: CreateCareerPagePayload) => void;
};

// Local helpers
const hexOk = (v: string) => /^#([0-9a-f]{3}|[0-9a-f]{6})$/i.test(v.trim());
const clamp = (n: number, min = 0, max = 999) => Math.min(Math.max(n, min), max);

const defaultColors = {
  primary: "#755AE2",
  // secondary: "#6C63FF",
  // accent: "#EAB308",
  // background: "#FFFFFF",
  // surface: "#F8F9FB",
  // text: "#1F2937",
  // muted_text: "#6B7280",
  // success: "#22C55E",
  // warning: "#F59E0B",
  // error: "#EF4444",
};

const SectionHeader: React.FC<{ title: string; className?: string }> = ({ title, className }) => (
  <CardHeader className={`pb-4 pt-5 ${className || ""}`}>
    <CardTitle className="text-[13px] font-semibold tracking-wide text-[#0B0B2A] uppercase">{title}</CardTitle>
    <div className="mt-4 h-px w-full bg-[#ECEAFB]" />
  </CardHeader>
);

const CareerPageConfigForm: React.FC<CareerPageConfigFormProps> = ({ slug, loading, onBack, onSubmit }) => {
  // Core fields
  const [companyDescription, setCompanyDescription] = React.useState("");
  const [customDomain, setCustomDomain] = React.useState("");

  // Files
  const [logoFile, setLogoFile] = React.useState<File | null>(null);
  const [bannerFile, setBannerFile] = React.useState<File | null>(null);

  // Branding
  const [colors, setColors] = React.useState({ ...defaultColors });
  // const [borderRadius, setBorderRadius] = React.useState<number>(12);
  // const [headingFont, setHeadingFont] = React.useState("Inter");
  // const [bodyFont, setBodyFont] = React.useState("Inter");
  // const [weightHeading, setWeightHeading] = React.useState<number>(600);
  // const [weightBody, setWeightBody] = React.useState<number>(400);
  // const [baseSize, setBaseSize] = React.useState<number>(16);
  // const [scale, setScale] = React.useState<number>(1.2);

  // Validation state
  const [errors, setErrors] = React.useState<Record<string, string>>({});

  const validate = React.useCallback(() => {
    const e: Record<string, string> = {};
    if (!slug.trim()) e.slug = "Missing slug";
    if (!companyDescription.trim()) e.companyDescription = "Company description is required";
    // Validate colors
    (Object.keys(colors) as Array<keyof typeof colors>).forEach((k) => {
      if (!hexOk(colors[k])) e[`color_${k}`] = "Use a valid hex color";
    });
    // if (borderRadius < 0) e.borderRadius = "Border radius cannot be negative";
    // if (baseSize <= 0) e.baseSize = "Base size must be greater than 0";
    // if (scale <= 0) e.scale = "Scale must be greater than 0";
    setErrors(e);
    return Object.keys(e).length === 0;
  // }, [slug, companyDescription, colors, borderRadius, baseSize, scale]);
  }, [slug, companyDescription, colors]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!validate()) return;

    const payload: CreateCareerPagePayload = {
      setup_type: "HOSTED",
      slug,
      company_description: companyDescription.trim(),
      custom_domain: customDomain.trim() || undefined,
      branding_config: {
        brand: {
          colors: { ...colors },
          // border_radius: clamp(borderRadius, 0, 48),
          // typography: {
          //   heading_font: headingFont,
          //   body_font: bodyFont,
          //   weight_heading: clamp(Number(weightHeading), 100, 900),
          //   weight_body: clamp(Number(weightBody), 100, 900),
          //   base_size: clamp(Number(baseSize), 8, 32),
          //   scale: Number(scale) || 1.2,
          // },
        },
        banner: {},
      },
      logo_file: logoFile || undefined,
      banner_file: bannerFile || undefined,
    };

    onSubmit(payload);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 1. Company Information */}
      <Card className="rounded-2xl">
        <SectionHeader title="Company Information" />
        <CardContent className="space-y-4 pt-6">
          {/* Company description */}
          <div className="space-y-1">
            <Label htmlFor="company_description" className="text-sm text-[#0B0B2A]">Company description</Label>
            <GLTextarea
              id="company_description"
              placeholder="We are a leading technology company in Nigeria, specializing in innovative software solutions that transform businesses across Africa."
              value={companyDescription}
              onChange={(e) => setCompanyDescription(e.target.value)}
            />
            {errors.companyDescription && (
              <p className="text-[11px] text-red-500">{errors.companyDescription}</p>
            )}
            <div className="rounded-xl bg-[#EFEAFF] px-4 py-3 text-[12px] text-[#6C63FF]">
              This will appear at the top of your career page
            </div>
          </div>

          {/* Custom domain */}
          <div className="space-y-1">
            <Label htmlFor="custom_domain" className="text-sm text-[#0B0B2A]">Custom domain (optional)</Label>
            <Input
              id="custom_domain"
              placeholder="careers.yourcompany.com"
              value={customDomain}
              onChange={(e) => setCustomDomain(e.target.value)}
            />
            <p className="text-[11px] text-[#7D8590]">If provided, your final URL will use your custom domain.</p>
          </div>
        </CardContent>
      </Card>

      {/* 2. Company Logo */}
      <Card className="rounded-2xl">
        <SectionHeader title="Company Logo" />
        <CardContent className="space-y-6 pt-6">
          <GLFileUpload
            value={logoFile}
            onFileSelect={setLogoFile}
            enableCrop
            cropAspectRatio={1}
            accept={{ 'image/*': ['.png', '.jpg', '.jpeg', '.svg'] }}
            className="h-[14rem]"
            customPlaceholder={
              <div className="flex h-full w-full flex-col items-center justify-center gap-2 text-center">
                <div className="rounded-full bg-[#F4F2FF] p-2.5">
                  {/* icon handled by component default styles if needed */}
                </div>
                <p className="text-lg font-semibold text-[#0B0B2A]">Drag & drop your logo here or click to browse</p>
                <p className="text-sm text-[#667085]">(PNG, JPG up to 2MB)</p>
                <div className="mt-2 rounded-xl bg-[#EFEAFF] px-4 py-2 text-[12px] text-[#6C63FF]">
                  If no logo is uploaded, we'll use your company profile image
                </div>
              </div>
            }
          />
        </CardContent>
      </Card>

      {/* 3. Banner Image */}
      <Card className="rounded-2xl">
        <SectionHeader title="Banner Image" />
        <CardContent className="space-y-6 pt-6">
          <GLFileUpload
            value={bannerFile}
            onFileSelect={setBannerFile}
            enableCrop
            cropAspectRatio={16 / 9}
            accept={{ 'image/*': ['.png', '.jpg', '.jpeg'] }}
            className="h-[16rem]"
            customPlaceholder={
              <div className="flex h-full w-full flex-col items-center justify-center gap-2 text-center">
                <div className="rounded-full bg-[#F4F2FF] p-2.5" />
                <p className="text-lg font-semibold text-[#0B0B2A]">Drag & drop your banner here or click to browse</p>
                <p className="text-sm text-[#667085]">(PNG, JPG up to 5MB)</p>
                <div className="mt-2 rounded-xl bg-[#EFEAFF] px-4 py-2 text-[12px] text-[#6C63FF]">
                  Recommended size: 1200x400px for best results
                </div>
              </div>
            }
            previewClassName="h-[12rem]"
          />
        </CardContent>
      </Card>

      {/* 4. Brand Colors */}
      <Card className="rounded-2xl">
        <SectionHeader title="Brand Colors" />
        <CardContent className="space-y-6 pt-6">
          {/* Preset themes */}
          <div className="space-y-4">
            <div className="flex flex-wrap gap-4">
              {([
                {
                  name: "Professional Blue",
                  dots: ["#2C2E66", "#6C63FF", "#EDEBFF"],
                  value: "#1708FF",
                },
                {
                  name: "Modern Purple",
                  dots: ["#5B38E3", "#B9A7FF", "#F1EFFC"],
                  value: "#755AE2",
                },
                {
                  name: "Minimal Gray",
                  dots: ["#374151", "#9CA3AF", "#E5E7EB"],
                  value: "#6B7280",
                },
                {
                  name: "Vibrant Red",
                  dots: ["#F59E0B", "#EF4444", "#FECACA"],
                  value: "#EF4444",
                },
              ] as const).map((p) => {
                const selected = colors.primary.toLowerCase() === p.value.toLowerCase();
                return (
                  <button
                    key={p.name}
                    type="button"
                    onClick={() => setColors((c) => ({ ...c, primary: p.value }))}
                    className={`flex items-center gap-3 rounded-xl px-5 py-3 shadow-sm transition-colors ${
                      selected ? "bg-[#F5F3FF] ring-1 ring-[#755AE2]" : "bg-[#F6F7FB]"
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      {p.dots.map((d, i) => (
                        <span key={i} className="h-3 w-3 rounded-full border border-white shadow" style={{ backgroundColor: d }} />
                      ))}
                    </div>
                    <span className="text-[15px] font-medium text-[#6B6E7C]">{p.name}</span>
                  </button>
                );
              })}
            </div>
          </div>

          <div className="mt-2 h-px w-full bg-[#ECEAFB]" />

          {/* Custom colour input */}
          <div className="space-y-3">
            <div className="text-[12px] font-semibold uppercase text-[#5B38E3]">
              Custom colour <span className="ml-1 font-normal normal-case text-[#8F8BA3]">(Input colour code)</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-md border border-[#E5E7EB]" style={{ backgroundColor: colors.primary }} />
              <Input
                value={colors.primary}
                onChange={(e) => setColors((c) => ({ ...c, primary: e.target.value }))}
                hasError={!!errors["color_primary"]}
                className="h-10 flex-1 bg-[#F5F6FA]"
              />
            </div>

            {/* Color picker */}
            <div className="max-w-[500px]">
              <SketchPicker
                color={colors.primary}
                onChange={(col) => setColors((c) => ({ ...c, primary: col.hex }))}
                presetColors={[]}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex items-center gap-3 pt-2">
        <Button type="button" variant="secondary" onClick={onBack}>Back</Button>
        <Button type="submit" disabled={!!loading}>{loading ? "Saving…" : "Generate career page"}</Button>
      </div>
    </form>
  );
};

export default CareerPageConfigForm;
