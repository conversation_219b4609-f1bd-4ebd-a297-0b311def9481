"use client"

import React, { } from 'react'
import { useSearchParams } from 'next/navigation';

import { AppHeaderRecruiter } from '@/components/shared'

import { CreateJobForm, JobCreator, Step1, Step2 } from '../misc/components/job/create';
import { useGetJobDetails } from '../misc/api';



const RecruiterCreateJobPage = () => {

    const searchParams = useSearchParams()
    const edit = searchParams.get('edit')
    const { data: jobDetail, isLoading: isFetchingJobData, } = useGetJobDetails(edit!!)

    return (
        <main className="main relative grow max-h-full grid grid-rows-[max-content,1fr,max-content] overflow-hidden">
            <AppHeaderRecruiter title='Jobs' className='!sticky top-0' />
            {
                edit && !isFetchingJobData && !jobDetail &&
                <div>
                    job doesn't exist or has been deleted, create new job <a href="./create">here</a>
                </div>
            }
            {
                edit && !isFetchingJobData && jobDetail && (jobDetail.job_status === 'OPEN' || jobDetail.job_status === 'CLOSED') &&
                <div>
                    Cannot edit open or closed jobs, Can only edit draft or queued jobs
                </div>
            }
            <JobCreator />
        </main>
    )
}

export default RecruiterCreateJobPage