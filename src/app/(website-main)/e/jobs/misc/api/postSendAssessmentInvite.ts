import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';

interface sendInvite {
    assessment?: string,
    interview?:string
    candidates: { name: string | null, email: string, job_application_id: number }[]
    recruiter: number | string;
    assessment_duration?: number
    is_from_job_post: boolean
    is_assessment: boolean
    is_interview: boolean
}

const sendAssessmentInvite = async (sendInvite: sendInvite) => {
    const response = await Axios.post(
        '/assessments/invite-job-candidates/',
        sendInvite,
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
    return response.data;
};

const useSendAssessmentInvite = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: sendAssessmentInvite,
        onSuccess: () => {
            // queryClient.invalidateQueries({
            //     queryKey: [`fetch-single-job-pipeline-detail-${job_id}`],
            // });
        },
    });
};

export default useSendAssessmentInvite