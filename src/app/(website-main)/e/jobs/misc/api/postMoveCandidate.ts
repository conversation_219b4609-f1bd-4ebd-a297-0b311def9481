import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';

interface moveCandidateDto {
    job_application_ids: (number | string)[],
    stage_id: string | number,
    job_unique_id: string
}

const progressCandidate = async (moveCandidateDto: moveCandidateDto) => {
       const response = await Axios.post(
        '/recruiter/candidate_progression/',
        moveCandidateDto,
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
    return response.data;
};

const useMoveCandidate = (job_id:number) => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: progressCandidate,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: [`fetch-single-job-pipeline-detail-${job_id}`],
            });
        },
    });
};

export default useMoveCandidate