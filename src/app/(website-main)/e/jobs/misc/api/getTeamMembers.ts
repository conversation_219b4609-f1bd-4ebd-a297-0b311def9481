import { useQuery } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';

// export interface TeamMember {
//   id: number;
//   member: {
//     id: number;
//     company_email: string;
//     verified: boolean;
//     type_of_recruiter: string;
//     profile_picture: string | null;
//     created_at: string;
//     updated_at: string;
//     user: {
//       id: string;
//       first_name: string;
//       middle_name: string | null;
//       last_name: string;
//       email: string;
//       phone_number: string | null;
//       date_of_birth: string | null;
//       gender: string;
//       user_type: string;
//     };
//     company: {
//       id: number;
//       name: string;
//       address: string;
//       website: string;
//       industry: string;
//       size: string;
//       description: string;
//       logo: string | null;
//       type_of_company: string;
//       company_domain: string;
//       created_at: string;
//     };
//   };
//   role: string;
//   role_name: string;
//   role_id: number;
//   permission_names: string[];
//   permission_ids: number[];
// }

export interface TeamMembersResponse {
  success: boolean;
  status_code: number;
  message: string;
  data: TeamMembersData;
}

export interface TeamMembersData {
  count: number;
  total_pages: number;
  current_page: number;
  next: string | null;
  previous: string | null;
  members: TeamMember[];
  filtered_applied: {
    role: string | null;
    status: string | null;
  };
  total_count: number;
  company: string;
}

export interface TeamMember {
  id: number;
  user_email: string;
  user_first_name: string;
  user_last_name: string;
  role_name: string;
  role_description: string;
  company_name: string;
  status: string;
  date_added: string;
  phone_number: string | null;
  role_id: number;
  user: TeamUser;
}

export interface TeamUser {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  middle_name: string | null;
  email_verified: boolean;
  phone_number: string | null;
  date_of_birth: string | null;
  gender: string | null;
  user_type: string;
  address: string;
}

export const fetchTeamMembers = async () => {
  const response = await Axios.get(`teams/members/`);
  return response.data.data.members as TeamMember[];
};

const useGetTeamMembers = (enabled: boolean | undefined = true) => {
  return useQuery(['fetch-job-team-members'], () => fetchTeamMembers(), {
    keepPreviousData: true,
    enabled,
  });
};

export default useGetTeamMembers;
