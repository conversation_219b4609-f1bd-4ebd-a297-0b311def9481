import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { CreatePipelineStages } from '../types';


const PutEditStages = async (PutEditStagesDto: CreatePipelineStages) => {
    const {stages, id} = PutEditStagesDto
    const response = await Axios.put(
        `/recruiter/pipelines/${id}/stages/bulk_update/`,
        stages,
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
    return response.data;
};

const usePutEditStages = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: PutEditStages,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['fetch-created-pipelines'],
            });
        },
    });
};

export default usePutEditStages