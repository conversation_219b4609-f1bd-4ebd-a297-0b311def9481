import { Axios } from '@/lib/api/axios';
import { MutationFunction, useMutation, useQueryClient } from '@tanstack/react-query';
import { CreateJob } from '../types';

const editDraftJob = async (id: string, data: CreateJob) => {
    const response = await Axios.patch(`/recruiter/job/${id}/`, data);
    return response.data;
};

const useEditDraftJob = () => {
    const queryClient = useQueryClient();

    const mutationFn: MutationFunction<any, [string, CreateJob]> = async (variables: [string, CreateJob]) => {
        const [id, data] = variables;
        return await editDraftJob(id, data);
    };

    return useMutation(mutationFn, {
        onSuccess: () => {
            queryClient.invalidateQueries(['fetch-created-jobs']);
        },
    });
};

export default useEditDraftJob;
