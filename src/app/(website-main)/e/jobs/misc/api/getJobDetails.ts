import { useQuery } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { ActiveJob } from '../types';



const getJobDetails = async (id: string) => {
    const response = await Axios.get(`/recruiter/jobs/${id}`);
    return response?.data as ActiveJob;
};

const useGetJobDetails = (id: string) => {

    return useQuery([`recruiter-fetch-job-detail-${id}`], () => getJobDetails(id), {
        enabled: id !== null && id.trim() !== "",
        keepPreviousData: true,
    });
};

export default useGetJobDetails;