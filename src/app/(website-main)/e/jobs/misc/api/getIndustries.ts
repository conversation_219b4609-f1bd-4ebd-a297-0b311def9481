import { useQuery } from '@tanstack/react-query';

import { Axios } from '@/lib/api/axios';


export const fetchIndustries = async () => {
    const response = await Axios.get(`recruiter/industries/`);
    return response.data as tiNDUSTRY[];
};

const useGetIndustries = (enabled?: boolean) => {
    return useQuery(['fetch-industries-for-job-creation'], fetchIndustries, {
        keepPreviousData: true,
        enabled: enabled,
        staleTime: 60 * 1000,
    });
};

export default useGetIndustries


interface tiNDUSTRY {
  id: number;
  name: string;
}