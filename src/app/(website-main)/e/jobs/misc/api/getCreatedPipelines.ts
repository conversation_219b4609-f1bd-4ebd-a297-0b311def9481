import { useInfiniteQuery, useQuery, useQueryClient } from '@tanstack/react-query';
import { ActiveJob } from '../types';
import { Axios } from '@/lib/api/axios';
import { getAccessToken } from '@/utils';



interface PaginatedJobTypes {
    page: number;
    count: number;
    results: ActiveJob[];
}
interface queryKeyTypes {
    pageParam: number;
    filterUrl?: string;
    search?: string
}

const fetchCreatedPipelines = async ({ pageParam = 1 }: queryKeyTypes) => {
            
    const response = await Axios.get(`recruiter/my_pipelines/?page=${pageParam}`)
    return response?.data ;
};


const useGetPipelines = () => {
    const queryClient = useQueryClient();

    return useInfiniteQuery(["fetch-created-pipelines", ], ({ pageParam = 1 }) => fetchCreatedPipelines({ pageParam }), {
        staleTime: 6000,
        keepPreviousData: true,
        getNextPageParam: (lastPage, pages) => {
            if (lastPage?.results?.length > 0 && (lastPage?.next !== null)) {
                return pages.length + 1;
            } else {
                return undefined;
            }
        },
    });
};

export default useGetPipelines;
