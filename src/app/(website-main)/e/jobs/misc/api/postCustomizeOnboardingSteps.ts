import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import {
  ReportResponse,
  UpdateSectionOrderPayload,
} from '../../../report/misc/types/CandidateReportType';
import { OnboardingStep } from '../types/PipelineType';

type CustomizeOnboardingStepsPayload = {
  title: string;
  content: string;
  order: number;
  attachments: string[];
};
const customizeOnboardingSteps = async (
  stageId: number,
  payload: CustomizeOnboardingStepsPayload
): Promise<any> => {
  const res = await Axios.post(
    `/recruiter/pipelines/stages/${stageId}/configure-onboarding-steps/`,
    payload
  );
  return res.data;
};

export const useCustomizeOnboardingSteps = (stageId: number) => {
  const queryClient = useQueryClient();

  return useMutation<any, Error, CustomizeOnboardingStepsPayload>({
    mutationFn: payload => customizeOnboardingSteps(stageId, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['fetch-stage-details'],
      });
    },
  });
};
