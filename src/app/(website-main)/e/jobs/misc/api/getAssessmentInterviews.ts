import { useQuery } from '@tanstack/react-query';
import { ActiveJob } from '../types';
import { Axios } from '@/lib/api/axios';






export const fetchInterviews = async () => {
    const response = await Axios.get(`assessments/assessment-interview/`);
    return response.data
};

const useGetInterviewAssessments = (enabled?: boolean) => {
    return useQuery(['fetch-my-interviews-for-job-creation'], () => fetchInterviews(), {
        keepPreviousData: true,
        enabled: enabled,
    });
};

export default useGetInterviewAssessments