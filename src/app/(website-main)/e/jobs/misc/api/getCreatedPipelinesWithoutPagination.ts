import { useQuery } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';




export const fetchPipelines = async () => {
    const response = await Axios.get(`recruiter/recruiter_pipelines/`);
    return response.data
};

const useGetPipelinesWithoutPagination = () => {
    return useQuery(['fetch-all-recruiter-pipelines-without-pagination'], () => fetchPipelines(), {
        keepPreviousData: true,
    });
};

export default useGetPipelinesWithoutPagination