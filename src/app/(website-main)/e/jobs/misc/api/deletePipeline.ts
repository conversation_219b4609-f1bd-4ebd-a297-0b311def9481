import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';


const deletePipeline = async (id: number) => {
    const response = await Axios.delete(
        `/recruiter/pipelines/${id}/`,
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
    return response.data;
};

const useDeletePipeline = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: deletePipeline,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['fetch-created-pipelines'],
            });
        },
    });
};
export default useDeletePipeline