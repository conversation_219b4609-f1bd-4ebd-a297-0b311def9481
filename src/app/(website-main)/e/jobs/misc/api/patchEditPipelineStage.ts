import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { EditSinglePipelineStageType } from '../types/PipelineType';
import { Stage } from '../types';

type editPipelineStage = {
    pipelineId: string | number
    stageId: string | number
    newStage: Stage
}
const EditPipelineStage = async ({ pipelineId, stageId, newStage }: editPipelineStage) => {
    const response = await Axios.patch(
        `/recruiter/pipelines/${pipelineId}/stages/${stageId}/`, newStage
    );
    return response.data;
};

const useEditPipelineStage = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: EditPipelineStage,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['fetch-created-pipelines'],
            });
        },
    });
};

export default useEditPipelineStage