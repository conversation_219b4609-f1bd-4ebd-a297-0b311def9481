import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { Axios } from '@/lib/api/axios';


export const fetchIndustries = async (data: { name: string }) => {
    const response = await Axios.post(`recruiter/industries/`, data);
    return response.data as tiNDUSTRY[];
};

const useCreateIndustry = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationKey: ['create-industry'],
        mutationFn: fetchIndustries,
        onSuccess(data, variables, context) {
            queryClient.invalidateQueries({
                queryKey: ['fetch-industries-for-job-creation']
            });
        },
        

    })
};

export default useCreateIndustry


interface tiNDUSTRY {
    id: number;
    name: string;
}