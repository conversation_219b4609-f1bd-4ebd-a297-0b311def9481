import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { getAccessToken } from '@/utils';
import { DraftJob } from '../types';



const saveDraft = async (saveDraftDto: DraftJob) => {
    const response = await Axios.post(
        '/recruiter/job/',
        saveDraftDto,
        {
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${getAccessToken()}`,
            },
        }
    );
    return response.data;
    
};

export const usesaveDraft = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: saveDraft,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['fetch-created-jobs'],
            });
        },
    });
};