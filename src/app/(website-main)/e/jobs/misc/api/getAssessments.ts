import { useInfiniteQuery } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';

interface TFetchAssessmentsAPIResponse {
    count: number;
    total_pages: number;
    current_page: number;
    next: string | null;
    previous: null;
    results: Assessment[];
}

export interface Assessment {
    id: string;
    name: string;
    no_candidates: number;
    completed: string[];
    ongoing: string[];
    not_started: string[];
    created_at: Date | string;
    deadline: Date | string | null;
    is_published: boolean;
    candidates_data: any[];
}

export const fetchAssessments = async ({ pageParam = 1 }) => {

    const response = await Axios.get<TFetchAssessmentsAPIResponse>(
        `assessments/my-assessments/?page=${pageParam}`
    );
    return response.data;
};

const useGetAssessments = (enabled?: boolean) => {
    return useInfiniteQuery(
        ['fetch-my-assessments-for-job-creation'],
        ({ pageParam = 1 }) => fetchAssessments({ pageParam }),
        {
            // getNextPageParam: (lastPage) => {
            //     console.log(lastPage, "LAST APAGE")
            //     if (lastPage.next) {
            //         const url = new URL(lastPage.next);
            //         const nextPage = url.searchParams.get('page');
            //         return nextPage ? parseInt(nextPage) : undefined;
            //     }
            //     // Return undefined if there are no more pages
            //     return undefined;
            // },
            getNextPageParam: (lastPage) => {
                const { current_page, total_pages } = lastPage;
                return current_page < total_pages ? current_page + 1 : undefined;
            },

            enabled: enabled,
        }
    );
};

export default useGetAssessments;