import {
  InfiniteQueryObserverResult,
  useInfiniteQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { ActiveJob } from '../types';

export interface PaginatedJobTypes {
  next: number | null;
  previous: number | null;
  count: number;
  results: ActiveJob[];
}

interface QueryKeyTypes {
  pageParam: number;
  filterUrl?: string;
  search?: string;
}

// Enhanced return type to include total count
interface EnhancedInfiniteQueryResult {
  data:
    | {
        pages: PaginatedJobTypes[];
        pageParams: number[];
        totalCount: number;
      }
    | undefined;
  error: unknown;
  fetchNextPage: () => Promise<
    InfiniteQueryObserverResult<PaginatedJobTypes, unknown>
  >;
  hasNextPage: boolean;
  isFetching: boolean;
  isFetchingNextPage: boolean;
  isLoading: boolean;
  refetch: () => Promise<void>;
}

const fetchCreatedJobs = async ({
  filterUrl,
  pageParam = 1,
  search,
}: QueryKeyTypes) => {
  const endpoint = search?.trim()
    ? `recruiter/fetch_created_jobs/?search=${search}${filterUrl}`
    : `recruiter/fetch_created_jobs/?page=${pageParam}${filterUrl}`;

  const response = await Axios.get(endpoint);
  return response?.data as PaginatedJobTypes;
};

const useGetJobs = (
  filterUrl?: string,
  search?: string
): EnhancedInfiniteQueryResult => {
  const queryClient = useQueryClient();

  const query = useInfiniteQuery(
    ['fetch-created-jobs', { filterUrl, search }],
    ({ pageParam = 1 }) => fetchCreatedJobs({ filterUrl, pageParam, search }),
    {
      staleTime: 6000,
      keepPreviousData: true,
      getNextPageParam: (lastPage, pages) => {
        if (lastPage?.results?.length > 0 && lastPage?.next !== null) {
          return lastPage.next;
        }
        return undefined;
      },
    }
  );

  // Enhanced data object with total count
  const enhancedData = query.data
    ? {
        ...query.data,
        totalCount: query.data.pages[0]?.count ?? 0,
      }
    : undefined;

  return {
    ...query,
    data: enhancedData,
  } as unknown as EnhancedInfiniteQueryResult;
};

export default useGetJobs;
