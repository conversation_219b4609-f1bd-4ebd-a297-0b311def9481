import { Axios } from '@/lib/api/axios';
import { useMutation } from '@tanstack/react-query';


export const createGroup = async (data: { email: string; name: string }) => {
  const response = await Axios.post('/services/cv_parser_group/', data);
  return response.data;
};
const useCreateGroup = () => {
  return useMutation({
    mutationKey: ['createGroup'],
    mutationFn: createGroup,
  });
};


export default useCreateGroup;
