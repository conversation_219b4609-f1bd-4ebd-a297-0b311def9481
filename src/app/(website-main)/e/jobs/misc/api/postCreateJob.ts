import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { TJobCreatorData } from '../components/job/create/JobCreator';


const createJob = async (createJobDto: TJobCreatorData) => {
    const response = await Axios.post(
        '/recruiter/job/',
        createJobDto,
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
    return response.data;
};

export const useCreateJob = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: createJob,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['fetch-created-jobs'],
            });
        },
    });
};