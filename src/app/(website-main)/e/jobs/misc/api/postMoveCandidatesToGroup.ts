import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';

interface moveCandidateDto {
    application_ids: (string  | number)[];
    group_id: number;
}

const moveToGroup = async (moveCandidateDto: moveCandidateDto) => {
       const response = await Axios.post(
        '/recruiter/move-applicant-to-mycandidates/',
        moveCandidateDto,
    );
    return response.data;
};

const useMoveCandidatesToGroup = (job_id:number, email: string) => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: moveToGroup,
        // onSuccess: () => {
        //     queryClient.invalidateQueries({
        //         queryKey: [`fetch-single-job-pipeline-detail-${job_id}`],
        //     });
        //     queryClient.invalidateQueries({
        //         queryKey: [`recruiter-candidate-groups-${email}`],
        //     });
        // },
        onSuccess: () => {
            queryClient.invalidateQueries({
                predicate: (query) =>
                    query.queryKey.includes(`fetch-single-job-pipeline-detail-${job_id}`) ||
                    query.queryKey.includes(`recruiter-candidate-groups-${email}`),
            });
        },
    });
};

export default useMoveCandidatesToGroup