import { useQuery } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { ActiveJob } from '../types';



const getCandidateDetails = async (id:number) => {
    const response = await Axios.get(
        `/recruiter/candidate_applied_job_detail/${id}`,
        {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        }
    );
    return response?.data?.job_application;
};

const useGetCandidateDetails = (candidate_id: number) => {

    return useQuery([`fetch-candiate-detail-${candidate_id}`], () => getCandidateDetails(candidate_id), {
        keepPreviousData: true,
    });
};

export default useGetCandidateDetails;