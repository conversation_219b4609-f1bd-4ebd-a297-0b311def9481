// services/cv_parser_group/?email=

import { useQuery } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';


  
  export interface candidatesFilteredGroup {
    id: number;
    name: string;
    role: string | null;
    keywords: string[] | null;
    email: string;
    filter_group: boolean;
    created_at: string; 
    updated_at: string; 
    cv_count: number;
    unique_id: string;
  };
  
const getCandidatesGroup = async (email: string) => {
    const response = await Axios.get(
        `/services/filtered_group/?email=${email}`,
       
    );
    return response?.data as candidatesFilteredGroup[];
};

const useGetCandidatesGroup = (email: string) => {

    return useQuery([`recruiter-candidate-groups-${email}`], () => getCandidatesGroup(email), {
        enabled: email !== null && email.trim() !== "",
        keepPreviousData: true,
    });
};

export default useGetCandidatesGroup;