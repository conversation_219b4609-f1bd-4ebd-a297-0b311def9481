import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';

interface DTO {
    id: number;
    is_seen: boolean;
}

const useEditIsSeen = (job_id: number) => {
    const queryClient = useQueryClient();
    return useMutation(
        async (data: DTO) => {
            const { id, is_seen } = data;
            const response = await Axios.patch(`/recruiter/job-applications/${id}/`, data);
            return response.data;
        },
        {
            onSuccess: (data) => {
                queryClient.invalidateQueries([`candidate-basic-details-${data.id}`, `fetch-single-job-pipeline-detail-${job_id}`]);
            },
        }
    );
};

export default useEditIsSeen;
