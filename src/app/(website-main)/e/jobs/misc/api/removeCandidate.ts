import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';


const removeCandidate = async (id: number) => {
    const response = await Axios.delete(
        `/recruiter/remove_job_application/${id}/`,
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
    return response.data;
};

const useRemoveCandidate = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: removeCandidate,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['delete-candidate'],
            });
        },
    });
};
export default useRemoveCandidate