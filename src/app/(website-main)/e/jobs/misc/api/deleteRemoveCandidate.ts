import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';

interface moveCandidateDto {
    job_application_ids: (string | number)[],
}

const deleteCandidate = async ({ job_application_ids }: moveCandidateDto) => {
    const response = await Axios.delete(`/recruiter/delete_job_applications/`, {
            data: { job_application_ids }
        })
    return response.data;
};

const useDeleteCandidates = (job_id: number) => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: deleteCandidate,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: [`fetch-single-job-pipeline-detail-${job_id}`],
            });
        },
    });
};

export default useDeleteCandidates