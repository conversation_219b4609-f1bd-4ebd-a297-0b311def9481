import { useQuery } from '@tanstack/react-query';
import {  CTableCandidate } from '../types';
import { Axios } from '@/lib/api/axios';


export interface Assessment {
    candidates_data: any[]
    completed: any[]
    created_at: Date | string | null;
    deadline: Date | string | null
    id: string
    name: string
    no_candidates: number
    not_started: any[]
    ongoing: any[]
}

interface Options {
    unique_id: string
    pageIndex: number
    pageSize: number
}
interface Result {
    count: number
    next: string | null
    previous: string| null
    results: CTableCandidate[]
}


export const fetchJobCandidates = async ({unique_id, pageIndex, pageSize}:Options) => {
    const response = await Axios.get(`recruiter/fetch_job_applications_in_job/${unique_id}/?page=${pageIndex + 1}&pageSize=${pageSize}`);
    return response.data as Result
};

const useGetJobCandidates = (DTO:Options) => {
    return useQuery([`fetch-job-candidates-${DTO.unique_id}`], () => fetchJobCandidates(DTO), {
        keepPreviousData: true,
    });
};

export default useGetJobCandidates