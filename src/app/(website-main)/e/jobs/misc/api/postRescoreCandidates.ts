import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';

interface DTO {
    job_id: number | string
    job_applications?: (string |number)[],
    calculate_all: boolean
}

const rescoreCandidate = async (moveCandidateDto: DTO) => {
    const response = await Axios.post(
        '/recruiter/v2/recalculate_matching_reason/',
        moveCandidateDto,
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
    return response.data;
};

const useRescoreCandidates = (job_id: number) => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: rescoreCandidate,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: [`fetch-single-job-pipeline-detail-${job_id}`],
            });
        },
    });
};

export default useRescoreCandidates