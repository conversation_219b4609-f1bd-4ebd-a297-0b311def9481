import { useMutation } from '@tanstack/react-query';

interface ShareJobRequest {
  jobId: string;
  emails: string[];
  shareType: 'talent' | 'client';
  viewAccess?: 'full' | 'limited';
}

interface ShareJobResponse {
  success: boolean;
  message: string;
}

const shareJob = async (data: ShareJobRequest): Promise<ShareJobResponse> => {
  const response = await fetch('/api/jobs/share', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error('Failed to share job');
  }

  return response.json();
};

export const useShareJob = () => {
  return useMutation({
    mutationFn: shareJob,
    onSuccess: data => {
      console.log('Job shared successfully:', data);
    },
    onError: error => {
      console.error('Error sharing job:', error);
    },
  });
};
