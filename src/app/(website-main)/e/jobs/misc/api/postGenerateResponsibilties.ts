import { useMutation, useQueryClient } from '@tanstack/react-query';
import { z } from 'zod';
import { Axios } from '@/lib/api/axios';


interface data {
    job_title: string,
    industry: string

}

type GenerateResponsibilitiesResponseSchema = {
    job_description: string
    job_responsibilities: string[]
    job_requirements: string[]
}


const generateResponsibilities = async (formData: data) => {
    const response = await Axios.post(
        '/recruiter/generate_role_responsibilities/',
        formData,
        {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        }
    );
    return response.data as GenerateResponsibilitiesResponseSchema
};

const useGenerateResponsibilities = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: generateResponsibilities,
    });
};

export default useGenerateResponsibilities