import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';

interface DTO {
    id: number;
    percentage_match: number;
}

const useEditPercentageMatch = (job_id: number) => {
    const queryClient = useQueryClient();
    return useMutation(
        async (data: DTO) => {
            const { id, percentage_match } = data;
            const response = await Axios.patch(`/recruiter/job-applications/${id}/update-percentage-match/`, data);
            return response.data;
        },
        {
            onSuccess: (data) => {
                queryClient.invalidateQueries([`candidate-basic-details-${data.id}`, `fetch-single-job-pipeline-detail-${job_id}`]);
            },
        }
    );
};

export default useEditPercentageMatch;
