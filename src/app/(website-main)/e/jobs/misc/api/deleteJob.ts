import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';

const deleteJob = async (id: string) => {
    const response = await Axios.delete(`/recruiter/job/?id=${id}`,);
    return response.data;
};

 const useDeleteJob = () => {
    const queryClient = useQueryClient();

    return useMutation(deleteJob, {
        onSuccess: () => {
            queryClient.invalidateQueries(['fetch-created-jobs']);
        },
    });
};

export default useDeleteJob