import { useQuery } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { PipelineDataResult } from '../types';

interface queryProps {
    id: number;
    filterUrl?: string;
}
export const fetchJobPipelineDetails = async (DTO: queryProps) => {
    const {id, filterUrl} = DTO
    const response = await Axios.get(`recruiter/fetch_job_pipeline_details/${id}/${filterUrl}`);
    return response.data as PipelineDataResult
};

const useGetJobPipelineDetails = (DTO: queryProps) => {
    
    return useQuery([`fetch-single-job-pipeline-detail-${DTO.id}`], () => fetchJobPipelineDetails(DTO), {
        keepPreviousData: true,
        // refetchInterval(data, query) {
            
        // },
        refetchInterval: 30000,
    });
};

export default useGetJobPipelineDetails