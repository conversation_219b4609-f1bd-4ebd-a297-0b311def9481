import { useMutation, useQueryClient, MutationFunction } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { DraftJob } from '../types';

const editDraftJob = async (id: string, data: DraftJob) => {
    const response = await Axios.patch(`/recruiter/job/?id=${id}`, data);
    return response.data;
};

const patchEditJobDetails = () => {
    const queryClient = useQueryClient();

    const mutationFn: MutationFunction<any, [string, DraftJob]> = async (variables: [string, DraftJob]) => {
        const [id, data] = variables;
        return await editDraftJob(id, data);
    };

    return useMutation(mutationFn, {
        onSuccess: (data) => {
            queryClient.invalidateQueries(['fetch-created-jobs', `recruiter-fetch-job-detail-${data.id}`]);
        },
    });
};

export default patchEditJobDetails;
