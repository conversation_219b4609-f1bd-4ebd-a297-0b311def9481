import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';

// Types for AI job configuration
export type ConductMode = 'AI_ONLY' | 'HUMAN_RECRUITER' | 'HYBRID';
export type SchedulingOption =
  | 'after_job_posting'
  | 'within_a_duration_span'
  | 'base_on_candidate_dates';

// Team member availability interface
export interface TeamMemberAvailability {
  date: string; // YYYY-MM-DD format
  start_time: string; // HH:MM format
  end_time: string; // HH:MM format
  team_member_email: string;
}

// Interview setup stage interface
export interface InterviewSetupStage {
  title: string;
  conduct_mode: ConductMode;
  interview_duration: string; // HH:MM format
  team_members: number[]; // team member ids as numbers
  scheduling_options: SchedulingOption;
  description: string;
  custom_questions: string[];
  team_members_availability: TeamMemberAvailability[];
}

export interface JobConfiguration {
  require_resume: boolean;
  require_personal_details: boolean;
  // require_cover_letter is optional in the new format
  require_cover_letter: boolean;
  conduct_mode: ConductMode;
  interview_setup_stages: InterviewSetupStage[];
  post_as_anonymous: boolean;
}

const createJobConfiguration = async (
  jobId: string,
  configuration: JobConfiguration
) => {
  const response = await Axios.post(
    `/recruiter/job/${jobId}/configure`,
    configuration,
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
  return response.data;
};

const updateJobConfiguration = async (
  jobId: string,
  configuration: Partial<JobConfiguration>
) => {
  console.log('configuration', configuration);
  const response = await Axios.patch(
    `/recruiter/job/${jobId}/configure`,
    configuration,
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
  return response.data;
};

const getJobConfiguration = async (
  jobId: string
): Promise<JobConfiguration> => {
  const response = await Axios.get(`/recruiter/job/${jobId}/configure`);
  // The API response is wrapped in a data object
  return response.data.data;
};

export const useCreateJobConfiguration = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      jobId,
      configuration,
    }: {
      jobId: string;
      configuration: JobConfiguration;
    }) => createJobConfiguration(jobId, configuration),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['job-configurations'],
      });
    },
  });
};

export const useUpdateJobConfiguration = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      jobId,
      configuration,
    }: {
      jobId: string;
      configuration: Partial<JobConfiguration>;
    }) => updateJobConfiguration(jobId, configuration),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['job-configurations'],
      });
    },
  });
};

export const useGetJobConfiguration = (jobId: string) => {
  return useQuery(
    ['job-configuration', jobId],
    () => getJobConfiguration(jobId),
    {
      enabled: !!jobId,
      retry: (failureCount: number, error: any) => {
        // Don't retry on 404 errors (configuration not found)
        if (error?.response?.status === 404) {
          return false;
        }
        // Retry other errors up to 3 times
        return failureCount < 3;
      },
      useErrorBoundary: (error: any) => {
        // Don't throw on 404 errors, let the component handle them gracefully
        return error?.response?.status !== 404;
      },
    }
  );
};
