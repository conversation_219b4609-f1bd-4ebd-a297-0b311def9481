import { Axios } from "@/lib/api/axios";
import { useQuery } from "@tanstack/react-query";
import { ApplicantAssessmentResult } from "../types";

interface data {
    candidate: number,
    enabled: boolean
}
const fetchAssessmentsResults = async (candidate: number) => {
    const response = await Axios.get(`/assessments/job_application/${candidate}/result/`);
    return response.data as ApplicantAssessmentResult;
}

const useGetApplicantAssessmentResult = (data: data) => {
    return useQuery([`fetch-applicants-results-${data.candidate}`], () => fetchAssessmentsResults(data.candidate), {
        keepPreviousData: true,
        enabled: data.enabled,
    });
};

export default useGetApplicantAssessmentResult