import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import app_fetch from '@/lib/api/appFetch';

interface ExportData {
    job_id: number,
    pipeline_id: number,
    stage_id: number
}

const exportStageData = async (exportData: ExportData) => {
   return await app_fetch(`recruiter/export-job-pipeline/?job_id=${exportData.job_id}&pipeline_id=${exportData.pipeline_id}&stage_id=${exportData.stage_id}`).then(res=>res.blob());
};


const useExportStageData = (exportData: ExportData) => {
    return useQuery({
        queryKey: ['exportStageData', exportData],
        queryFn: () => exportStageData(exportData),
        enabled: false,
    });
};



export default useExportStageData;
