import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { type CreatePipelineStages } from '../types';


const CreatePipelineStages = async (CreatePipelineStagesDto: CreatePipelineStages) => {
    const {stages, id} = CreatePipelineStagesDto
    const response = await Axios.post(
        `/recruiter/pipelines/${id}/stages/`,
        stages,
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
    return response.data;
};

const useCreatePipelineStages = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: CreatePipelineStages,
        onSuccess: () => {
            queryClient.invalidateQueries(['fetch-created-pipelines']);
            queryClient.invalidateQueries(['fetch-all-recruiter-pipelines-without-pagination']);
        },
    });
};

export default useCreatePipelineStages