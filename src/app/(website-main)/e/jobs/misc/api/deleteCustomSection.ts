import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';

const deleteCustomSection = async ({ sectionId }: { sectionId: string }) => {
  const res = await Axios.delete(`/candidate-reports/sections/${sectionId}/`);
  return res.data;
};

export const useDeleteCustomSection = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteCustomSection,
    onSuccess: () => {
      // Invalidate queries or perform any necessary updates after deletion
      queryClient.invalidateQueries({
        queryKey: ['fetch-custom-sections'],
      });
    },
  });
};
