import { Axios } from '@/lib/api/axios';
import { useMutation } from '@tanstack/react-query';

export interface CheckAvailabilityRequest {
    date: string; // YYYY-MM-DD format
    start_time: string; // HH:MM:SS format
    end_time: string; // HH:MM:SS format
    team_member_email: string;
}

export interface CheckAvailabilityResponse {
    available: boolean;
    message?: string;
    conflicts?: Array<{
        start_time: string;
        end_time: string;
        title?: string;
    }>;
}

export interface CheckAvailabilityApiResponse {
    success: boolean;
    status_code: number;
    message: string;
    data: CheckAvailabilityResponse;
}

const checkMemberAvailability = async (data: CheckAvailabilityRequest): Promise<CheckAvailabilityResponse> => {
    const response = await Axios.post('/recruiter/check-member-availability/', data);
    return response.data.data;
};

export const useCheckMemberAvailability = () => {
    return useMutation({
        mutationFn: checkMemberAvailability,
    });
};

export default useCheckMemberAvailability;
