import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import {
  ReportResponse,
  UpdateSectionOrderPayload,
} from '../../../report/misc/types/CandidateReportType';

const updateSectionOrder = async (
  reportId: string,
  payload: UpdateSectionOrderPayload
): Promise<ReportResponse> => {
  const res = await Axios.patch(
    `/candidate-reports/reports${reportId}/reorder-sections/`,
    payload
  );
  return res.data;
};

export const useUpdateSectionOrder = (reportId: string) => {
  const queryClient = useQueryClient();

  return useMutation<ReportResponse, Error, UpdateSectionOrderPayload>({
    mutationFn: payload => updateSectionOrder(reportId, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['fetch-reordered-report'],
      });
    },
  });
};
