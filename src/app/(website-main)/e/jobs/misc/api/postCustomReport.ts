import {
  QueryClient,
  useMutation,
  useQueryClient,
} from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { CreateCustomReportSection } from '../../../report/misc/types/CandidateReportType';

const createCustomSection = async ({
  reportId,
  payload,
}: {
  reportId: string;
  payload: CreateCustomReportSection;
}) => {
  const res = await Axios.post(
    `/candidate-reports/reports/${reportId}/sections/`,
    payload
  );
  return res.data;
};

export const useCreateCustomSection = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createCustomSection,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['fetch-created-sections'],
      });
    },
  });
};
