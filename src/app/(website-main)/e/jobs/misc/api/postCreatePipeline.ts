import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { CreatePipeline } from '../types';

// CHANGED NAME OF THIS. CONFLICTING WITH LOCAL VALUE
const CreatePipelineRequirements = async (
  CreatePipelineDto: CreatePipeline
) => {
  const response = await Axios.post(
    '/recruiter/pipelines/',
    CreatePipelineDto,
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
  return response.data;
};

const useCreatePipeline = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: CreatePipelineRequirements,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['fetch-created-pipelines'],
      });
    },
  });
};

export default useCreatePipeline;
