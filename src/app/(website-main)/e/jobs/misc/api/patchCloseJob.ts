import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';

const closeJob = async (id: string) => {
    const response = await Axios.patch(`/recruiter/job/?id=${id}`, { job_status: "CLOSED" });
    return response.data;
};

const useCloseJob = () => {
    const queryClient = useQueryClient();

    return useMutation(closeJob, {
        onSuccess: () => {
            queryClient.invalidateQueries(['fetch-created-jobs']);
        },
    });
};
export default useCloseJob