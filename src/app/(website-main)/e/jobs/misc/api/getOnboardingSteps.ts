import { useQuery } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';

const getReportStatus = async (
  stageId: number
): Promise<any> => {
  const res = await Axios.get(`/recruiter/pipelines/stages/${stageId}/configure-onboarding-steps/`);
  return res.data;
};

export const useGetOnboardingSteps = (
  stageId: number,
 ) =>
  useQuery<any, Error>({
    queryKey: ['report-status',   stageId],
    queryFn: () => getReportStatus(stageId),
     
  });
