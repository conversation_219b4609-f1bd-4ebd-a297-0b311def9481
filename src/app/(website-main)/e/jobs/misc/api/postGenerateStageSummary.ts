import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';

interface GenerateStageSummaryData {
  jobUniqueId: string;
  stage: string;
}

type GenerateStageSummaryResponseSchema = {
  success: boolean;
  status_code: number;
  message: string;
  data: {
    summary: string;
  };
};

interface GenerateStageQuestionsData {
  jobUniqueId: string;
  stage: string;
}

type GenerateStageQuestionsResponseSchema = {
  success: boolean;
  status_code: number;
  message: string;
  data: {
    stage: string;
    suggestions: string[];
  };
};

const generateStageSummary = async (formData: GenerateStageSummaryData) => {
  const response = await Axios.get(
    `/recruiter/job/${formData.jobUniqueId}/stage-summary/?stage=${formData.stage}`,
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
  return response.data as GenerateStageSummaryResponseSchema;
};

const generateStageQuestions = async (formData: GenerateStageQuestionsData) => {
  const response = await Axios.get(
    `/recruiter/jobs/${formData.jobUniqueId}/stage-question-suggestions/?stage=${formData.stage}`,
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
  return response.data as GenerateStageQuestionsResponseSchema;
};

const useGenerateStageSummary = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: generateStageSummary,
  });
};

const useGenerateStageQuestions = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: generateStageQuestions,
  });
};

export default useGenerateStageSummary;
export { useGenerateStageQuestions };
