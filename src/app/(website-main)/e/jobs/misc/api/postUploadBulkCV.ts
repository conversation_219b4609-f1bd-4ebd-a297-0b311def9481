import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';

interface DTO {
    job: string;
    cvs: File[]
}

const uploadBulkCv = async (data: DTO) => {
    const formData = new FormData();
    formData.append('job', data.job);
    data.cvs.forEach((cv: any) => {
        formData.append('cv_file', cv);
    });


    const response = await Axios.post(
        `/recruiter/v2/bulk-upload-cv/`,
        formData,
    );

    return response.data;
};

const useUploadBulkCVsToPipeline = () => {
    const query = useQueryClient()
    return useMutation({
        mutationFn: uploadBulkCv,
        onSuccess(data, variables, context) {
            query.invalidateQueries({
                queryKey: [`fetch-single-job-pipeline-detail-${variables.job}`],
            })
        },
    });
};

export default useUploadBulkCVsToPipeline
