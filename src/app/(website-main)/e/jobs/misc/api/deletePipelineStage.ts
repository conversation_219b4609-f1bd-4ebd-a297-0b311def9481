import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { Stage } from '../types';

type deletePipelineStage = {
    pipelineId: number
    stageId: number
}
const DeletePipelineStage = async ({ pipelineId, stageId }: deletePipelineStage) => {
    const response = await Axios.delete(
        `/recruiter/pipelines/${pipelineId}/stages/${stageId}/`
    );
    return response.data;
};

const useDeletePipelineStage = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: DeletePipelineStage,
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: ['fetch-created-pipelines'],
            });
        },
    });
};

export default useDeletePipelineStage