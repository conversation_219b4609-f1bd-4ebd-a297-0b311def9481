import * as React from "react";
import { SVGProps } from "react";
const DocIcon = (props: SVGProps<SVGSVGElement>) => (
    <svg
        clipRule="evenodd"
        fillRule="evenodd"
        strokeLinejoin="round"
        strokeMiterlimit={1.41421}
        viewBox="0 0 560 400"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <g fillRule="nonzero" transform="matrix(3.125 0 0 3.125 180 62.5)">
            <path
                d="m58 88h-52c-3.3 0-6-2.7-6-6v-76c0-3.3 2.7-6 6-6h36l22 22v60c0 3.3-2.7 6-6 6z"
                fill="#3086f6"
            />
            <path d="m42 0 22 22h-22z" fill="#0c67d6" />
            <path
                d="m50 39h-36v-5h36zm0 7h-36v5h36zm-10 12h-26v5h26z"
                fill="#fdffff"
            />
        </g>
    </svg>
);
export default DocIcon;
