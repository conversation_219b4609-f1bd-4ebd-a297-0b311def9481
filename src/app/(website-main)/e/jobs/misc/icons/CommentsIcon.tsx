import * as React from "react";
import { SVGProps } from "react";
const CommentsIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={20}
    height={23}
    viewBox="0 0 20 23"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M9.09934 20.7582L9.10143 20.7609C9.3157 21.0425 9.62749 21.2457 9.99935 21.2457C10.3712 21.2457 10.683 21.0425 10.8973 20.7609L10.8993 20.7582L12.1493 19.0915C12.3299 18.8507 12.6151 18.7082 12.916 18.7082H13.3327C14.8927 18.7082 16.1406 18.3449 16.9925 17.493C17.8444 16.641 18.2077 15.3932 18.2077 13.8332V11.3332C18.2077 11.303 18.22 11.2717 18.2456 11.2461C18.2713 11.2205 18.3025 11.2082 18.3327 11.2082C18.3628 11.2082 18.3941 11.2205 18.4198 11.2461C18.4454 11.2717 18.4577 11.303 18.4577 11.3332V13.8332C18.4577 15.5911 17.9953 16.851 17.1729 17.6734C16.3505 18.4958 15.0906 18.9582 13.3327 18.9582H12.916C12.6726 18.9582 12.4821 19.0812 12.3652 19.2214L12.357 19.2313L12.3493 19.2415L11.1001 20.9071C11.1001 20.9072 11.1 20.9073 11.0999 20.9075C10.7978 21.3078 10.3966 21.5082 9.99935 21.5082C9.60212 21.5082 9.20087 21.3078 8.89882 20.9075C8.89873 20.9073 8.89865 20.9072 8.89856 20.9071L7.64935 19.2415L7.24935 19.5415L7.64935 19.2415C7.59411 19.1678 7.53002 19.1218 7.50125 19.1021C7.46371 19.0763 7.42509 19.0548 7.38936 19.0374C7.34061 19.0138 7.21777 18.9582 7.07435 18.9582H6.66602C4.94685 18.9582 3.70008 18.7185 2.87301 18.0199C2.06568 17.3381 1.54102 16.1131 1.54102 13.8332V9.6665C1.54102 7.90861 2.00341 6.64872 2.82582 5.82631C3.64823 5.00389 4.90813 4.5415 6.66602 4.5415H11.666C11.6962 4.5415 11.7274 4.55379 11.7531 4.57943C11.7787 4.60508 11.791 4.63636 11.791 4.6665C11.791 4.69665 11.7787 4.72793 11.7531 4.75358C11.7274 4.77922 11.6962 4.7915 11.666 4.7915H6.66602C5.10602 4.7915 3.85815 5.15477 3.00621 6.0067C2.15428 6.85863 1.79102 8.10651 1.79102 9.6665V13.8332C1.79102 15.7386 2.10958 17.0326 2.99208 17.8086C3.85718 18.5694 5.13516 18.7082 6.66602 18.7082H7.08268C7.34927 18.7082 7.68512 18.8749 7.85001 19.0924C7.85018 19.0926 7.85035 19.0928 7.85052 19.0931L9.09934 20.7582Z"
      fill="#12B669"
      stroke="#8C8CA1"
    />
    <path
      d="M9.99935 12.5002C9.81208 12.5002 9.66602 12.3523 9.66602 12.1668C9.66602 11.9846 9.81716 11.8335 9.99935 11.8335C10.1815 11.8335 10.3327 11.9846 10.3327 12.1668C10.3327 12.3523 10.1866 12.5002 9.99935 12.5002Z"
      fill="#12B669"
      stroke="#8C8CA1"
    />
    <path
      d="M13.3333 12.5002C13.1461 12.5002 13 12.3523 13 12.1668C13 11.9846 13.1511 11.8335 13.3333 11.8335C13.5155 11.8335 13.6667 11.9846 13.6667 12.1668C13.6667 12.3523 13.5206 12.5002 13.3333 12.5002Z"
      fill="#12B669"
      stroke="#8C8CA1"
    />
    <path
      d="M6.66732 12.5002C6.48005 12.5002 6.33398 12.3523 6.33398 12.1668C6.33398 11.9846 6.48513 11.8335 6.66732 11.8335C6.84951 11.8335 7.00065 11.9846 7.00065 12.1668C7.00065 12.3523 6.85459 12.5002 6.66732 12.5002Z"
      fill="#12B669"
      stroke="#8C8CA1"
    />
    <path
      d="M13.5362 8V6.7358L16.1889 4.41335C16.3878 4.23343 16.5571 4.06889 16.6967 3.91974C16.8364 3.76823 16.9429 3.61671 17.0163 3.4652C17.0897 3.31132 17.1264 3.14441 17.1264 2.96449C17.1264 2.76326 17.0826 2.59162 16.995 2.44957C16.9074 2.30516 16.7867 2.19389 16.6328 2.11577C16.4789 2.03764 16.3026 1.99858 16.1037 1.99858C15.9025 1.99858 15.7261 2.04001 15.5746 2.12287C15.4231 2.20336 15.3047 2.32055 15.2195 2.47443C15.1366 2.62831 15.0952 2.81534 15.0952 3.03551H13.4297C13.4297 2.54072 13.541 2.1134 13.7635 1.75355C13.986 1.3937 14.2985 1.11671 14.701 0.922585C15.1058 0.726089 15.5758 0.627841 16.1108 0.627841C16.6624 0.627841 17.1418 0.72017 17.549 0.904829C17.9562 1.08949 18.2711 1.34754 18.4936 1.67898C18.7185 2.00805 18.831 2.39039 18.831 2.82599C18.831 3.10298 18.7753 3.3776 18.6641 3.64986C18.5528 3.92211 18.3527 4.22277 18.0639 4.55185C17.7775 4.88092 17.3703 5.27509 16.8423 5.73438L15.9723 6.53693V6.5831H18.9197V8H13.5362Z"
      fill="#12B669"
    />
  </svg>
);
export default CommentsIcon;
