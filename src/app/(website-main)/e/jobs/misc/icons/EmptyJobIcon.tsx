import * as React from "react";
import { SVGProps } from "react";
const EmptyJobIcon = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width={111}
        height={111}
        viewBox="0 0 111 111"
        fill="none"
        {...props}
    >
        <circle cx={55.5} cy={55.5} r={55.5} fill="white" />
        <path
            d="M70.5422 69.7643H41.0598C39.6476 69.7643 38.4988 68.6155 38.4988 67.2038V47.2642C38.4988 45.8526 39.6476 44.7037 41.0598 44.7037H70.5422C71.9544 44.7037 73.1032 45.8526 73.1032 47.2642V67.2038C73.1032 68.6155 71.9544 69.7643 70.5422 69.7643ZM41.0598 45.9078C40.3114 45.9078 39.7029 46.5163 39.7029 47.2642V67.2038C39.7029 67.9517 40.3114 68.5602 41.0598 68.5602H70.5422C71.2906 68.5602 71.8991 67.9517 71.8991 67.2038V47.2642C71.8991 46.5163 71.2906 45.9078 70.5422 45.9078H41.0598Z"
            fill="#7D8590"
        />
        <path
            d="M61.3677 45.9078C61.0349 45.9078 60.7656 45.6385 60.7656 45.3057V42.5866C60.7656 41.8387 60.1571 41.2302 59.4093 41.2302H52.193C51.4452 41.2302 50.8367 41.8387 50.8367 42.5866V45.3057C50.8367 45.6385 50.5674 45.9078 50.2346 45.9078C49.9018 45.9078 49.6326 45.6385 49.6326 45.3057V42.5866C49.6326 41.1749 50.7814 40.0261 52.193 40.0261H59.4093C60.8209 40.0261 61.9697 41.1749 61.9697 42.5866V45.3057C61.9697 45.6385 61.7004 45.9078 61.3677 45.9078Z"
            fill="#7D8590"
        />
        <path
            d="M54.1737 56.9521H48.5484C44.664 56.9521 40.9688 55.0971 38.653 51.9876C38.5577 51.8812 38.4995 51.7412 38.4995 51.5884C38.4995 51.2556 38.7635 50.9863 39.0963 50.9863H39.1063C39.2973 50.9863 39.4772 51.0769 39.5907 51.2309C41.6785 54.0594 45.0273 55.748 48.5484 55.748H54.1737C54.5065 55.748 54.7758 56.0172 54.7758 56.35C54.7758 56.6828 54.5065 56.9521 54.1737 56.9521Z"
            fill="#7D8590"
        />
        <path
            d="M63.0538 56.952H57.4385C57.1057 56.952 56.8364 56.6828 56.8364 56.35C56.8364 56.0172 57.1057 55.748 57.4385 55.748H63.0538C66.5749 55.748 69.9238 54.0594 72.0115 51.2309C72.3219 50.8076 73.1033 51.0645 73.1033 51.5884C73.1033 51.7536 73.0374 51.9035 72.931 52.0123C70.614 55.1071 66.9283 56.952 63.0538 56.952Z"
            fill="#7D8590"
        />
        <path
            d="M57.4331 59.0709H54.1689C53.8362 59.0709 53.5669 58.8017 53.5669 58.4689V54.2411C53.5669 53.9083 53.8362 53.639 54.1689 53.639H57.4331C57.7659 53.639 58.0352 53.9083 58.0352 54.2411V58.4689C58.0352 58.8017 57.7659 59.0709 57.4331 59.0709ZM54.771 57.8668H56.8311V54.8431H54.771V57.8668Z"
            fill="#7D8590"
        />
        <path
            d="M55.4999 85C49.0991 85 43.0081 82.9811 37.8855 79.1613C37.6192 78.9626 37.5639 78.5851 37.7626 78.3188C37.9613 78.0519 38.3382 77.9972 38.6051 78.1959C43.5185 81.8593 49.3607 83.796 55.4999 83.796C61.6391 83.796 67.4813 81.8593 72.3947 78.1959C72.6604 77.996 73.0385 78.0519 73.2372 78.3188C73.4359 78.5851 73.3806 78.9626 73.1143 79.1613C67.9917 82.9811 61.9007 85 55.4999 85Z"
            fill="#7D8590"
        />
        <path
            d="M72.754 32.9235C72.6293 32.9235 72.5035 32.8847 72.3953 32.8047C67.465 29.1407 61.6227 27.2041 55.5 27.2041C49.3773 27.2041 43.535 29.1407 38.6046 32.8047C38.3354 33.0023 37.9603 32.947 37.7621 32.6807C37.564 32.4137 37.6193 32.0363 37.8862 31.8382C43.0265 28.019 49.1168 26 55.5 26C61.8831 26 67.9735 28.019 73.1138 31.8382C73.3807 32.0363 73.436 32.4137 73.2379 32.6807C73.1197 32.8394 72.938 32.9235 72.754 32.9235Z"
            fill="#7D8590"
        />
        <path
            d="M78.6778 73.3567C78.5526 73.3567 78.4268 73.3179 78.3186 73.2374C78.0523 73.0386 77.997 72.6618 78.1957 72.3948C81.8591 67.4774 83.7958 61.6351 83.7958 55.5001C83.7958 49.3627 81.8632 43.5251 78.2075 38.6177C78.0088 38.3507 78.064 37.9739 78.3304 37.7751C78.5973 37.5752 78.9742 37.6311 79.1729 37.898C82.985 43.0148 84.9999 49.1016 84.9999 55.5001C84.9999 61.8962 82.9809 67.9871 79.1611 73.1145C79.0429 73.2732 78.8619 73.3567 78.6778 73.3567Z"
            fill="#7D8590"
        />
        <path
            d="M32.322 73.3566C32.138 73.3566 31.9569 73.2731 31.8387 73.1144C28.019 67.9871 26 61.8961 26 55.5C26 49.101 28.0148 43.0141 31.8264 37.8979C32.0257 37.6316 32.4026 37.5769 32.6689 37.7751C32.9352 37.9738 32.9905 38.3507 32.7918 38.6176C29.136 43.5244 27.2041 49.362 27.2041 55.5C27.2041 61.6351 29.1407 67.4773 32.8041 72.3948C33.0028 72.6617 32.9476 73.0386 32.6812 73.2373C32.5731 73.3178 32.4472 73.3566 32.322 73.3566Z"
            fill="#7D8590"
        />
        <path
            d="M35.0658 39.8846C33.8329 39.8846 32.5995 39.4148 31.6605 38.4759C30.7504 37.5658 30.2495 36.3564 30.2495 35.07C30.2495 33.7836 30.7504 32.5742 31.6605 31.6647C32.5701 30.7546 33.7794 30.2537 35.0658 30.2537C36.3528 30.2537 37.5622 30.7546 38.4717 31.6647C40.3496 33.542 40.3496 36.5974 38.4717 38.4759C37.5328 39.4148 36.2993 39.8846 35.0658 39.8846ZM35.0658 31.4577C34.101 31.4577 33.1945 31.8334 32.5119 32.516C31.8293 33.1986 31.4536 34.1052 31.4536 35.07C31.4536 36.0348 31.8293 36.942 32.5119 37.6245C33.9205 39.0332 36.2117 39.0326 37.6204 37.6245C39.0285 36.2159 39.0285 33.9241 37.6204 32.516C36.9378 31.8334 36.0312 31.4577 35.0658 31.4577Z"
            fill="#7D8590"
        />
        <path
            d="M35.0664 80.7521C33.8329 80.7521 32.5995 80.2824 31.6605 79.3435C30.7504 78.4339 30.2495 77.2246 30.2495 75.9382C30.2495 74.6512 30.7504 73.4418 31.6605 72.5323C33.5378 70.6544 36.5933 70.6544 38.4717 72.5323C40.3496 74.4101 40.3496 77.4656 38.4717 79.3435C37.5328 80.2824 36.2993 80.7521 35.0664 80.7521ZM35.0658 72.3271C34.141 72.3271 33.2162 72.6793 32.5119 73.3836C31.8293 74.0662 31.4536 74.9728 31.4536 75.9382C31.4536 76.903 31.8293 77.8096 32.5119 78.4921C33.9194 79.8996 36.2105 79.9008 37.6204 78.4921C39.0285 77.0835 39.0285 74.7923 37.6204 73.3836C36.9161 72.6793 35.9912 72.3271 35.0658 72.3271Z"
            fill="#7D8590"
        />
        <path
            d="M75.9341 80.7523C74.7007 80.7523 73.4672 80.2825 72.5283 79.3436C70.6504 77.4658 70.6504 74.4103 72.5283 72.5324C74.4073 70.6546 77.4628 70.6552 79.3394 72.5324C80.2496 73.442 80.7505 74.6513 80.7505 75.9383C80.7505 77.2247 80.2496 78.4341 79.3394 79.3436C78.4005 80.2825 77.167 80.7523 75.9341 80.7523ZM75.9336 72.3272C75.0087 72.3272 74.0839 72.6794 73.3796 73.3838C71.9715 74.7924 71.9715 77.0836 73.3796 78.4923C74.7871 79.8998 77.0783 79.901 78.4881 78.4923C79.1707 77.8097 79.5464 76.9031 79.5464 75.9383C79.5464 74.9729 79.1707 74.0663 78.4881 73.3838C77.7838 72.6794 76.859 72.3272 75.9336 72.3272Z"
            fill="#7D8590"
        />
        <path
            d="M75.9336 39.8846C74.7007 39.8846 73.4672 39.4148 72.5283 38.4759C70.6504 36.5975 70.6504 33.542 72.5283 31.6647C74.3467 29.8451 77.5198 29.8439 79.3394 31.6647C80.2496 32.5743 80.7505 33.7836 80.7505 35.07C80.7505 36.3564 80.2496 37.5658 79.3394 38.4759C78.4005 39.4148 77.167 39.8846 75.9336 39.8846ZM75.9341 31.4578C74.9688 31.4578 74.0622 31.8335 73.3796 32.5161C71.9715 33.9242 71.9715 36.2159 73.3796 37.6246C74.7894 39.0344 77.0806 39.0333 78.4881 37.6246C79.1707 36.942 79.5464 36.0348 79.5464 35.07C79.5464 34.1052 79.1707 33.1986 78.4881 32.5161C77.8055 31.8335 76.8989 31.4578 75.9341 31.4578Z"
            fill="#7D8590"
        />
    </svg>
);
export default EmptyJobIcon;
