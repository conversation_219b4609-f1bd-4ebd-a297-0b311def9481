import * as React from "react";
import { SVGProps } from "react";
const XIcon = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width={30}
        height={30}
        viewBox="0 0 22 22"
        fill="none"
        {...props}
    >
        <path
            d="M8.30518 13.3145L13.8788 8.29597"
            stroke={props.stroke || "#0E0E2C"}
            strokeOpacity={0.5}
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M13.6015 13.5919L8.58301 8.01831"
            stroke={props.stroke || "#0E0E2C"}
            strokeOpacity={0.5}
            strokeWidth={1.5}
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);
export default XIcon;
