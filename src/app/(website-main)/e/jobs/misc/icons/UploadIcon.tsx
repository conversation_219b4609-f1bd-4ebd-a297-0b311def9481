import * as React from "react";
import { SVGProps } from "react";
const UploadIcon = (props: SVGProps<SVGSVGElement>) => (
    <svg
        width={68}
        height={68}
        viewBox="0 0 68 68"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <circle cx={34} cy={34} r={34} fill="#F5F3FF" />
        <path
            d="M29.75 42.1463C29.1691 42.1463 28.6875 41.6647 28.6875 41.0838V35.148L27.6675 36.168C27.2566 36.5788 26.5766 36.5788 26.1658 36.168C25.755 35.7572 25.755 35.0772 26.1658 34.6663L28.9991 31.833C29.2966 31.5355 29.7641 31.4363 30.1608 31.6063C30.5575 31.7622 30.8125 32.1588 30.8125 32.5838V41.0838C30.8125 41.6647 30.3308 42.1463 29.75 42.1463Z"
            fill="#755AE2"
        />
        <path
            d="M32.5835 36.4782C32.3143 36.4782 32.0451 36.3791 31.8326 36.1666L28.9993 33.3332C28.5885 32.9224 28.5885 32.2424 28.9993 31.8316C29.4101 31.4207 30.0901 31.4207 30.501 31.8316L33.3343 34.6649C33.7451 35.0757 33.7451 35.7557 33.3343 36.1666C33.1218 36.3791 32.8526 36.4782 32.5835 36.4782Z"
            fill="#755AE2"
        />
        <path
            d="M38.2499 49.2298H29.7499C22.0574 49.2298 18.7708 45.9432 18.7708 38.2507V29.7507C18.7708 22.0582 22.0574 18.7715 29.7499 18.7715H36.8333C37.4141 18.7715 37.8958 19.2532 37.8958 19.834C37.8958 20.4148 37.4141 20.8965 36.8333 20.8965H29.7499C23.2191 20.8965 20.8958 23.2198 20.8958 29.7507V38.2507C20.8958 44.7815 23.2191 47.1048 29.7499 47.1048H38.2499C44.7808 47.1048 47.1041 44.7815 47.1041 38.2507V31.1673C47.1041 30.5865 47.5858 30.1048 48.1666 30.1048C48.7474 30.1048 49.2291 30.5865 49.2291 31.1673V38.2507C49.2291 45.9432 45.9424 49.2298 38.2499 49.2298Z"
            fill="#755AE2"
        />
        <path
            d="M48.1666 32.2297H42.4999C37.6549 32.2297 35.7708 30.3455 35.7708 25.5005V19.8338C35.7708 19.4088 36.0258 19.0122 36.4224 18.8563C36.8191 18.6863 37.2724 18.7855 37.5841 19.083L48.9174 30.4163C49.2149 30.7138 49.3141 31.1813 49.1441 31.578C48.9741 31.9747 48.5916 32.2297 48.1666 32.2297ZM37.8958 22.398V25.5005C37.8958 29.1555 38.8449 30.1047 42.4999 30.1047H45.6024L37.8958 22.398Z"
            fill="#755AE2"
        />
    </svg>
);
export default UploadIcon;
