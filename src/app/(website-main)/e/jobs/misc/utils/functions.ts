import { PipelineColumn, Stage } from "../types";

/**
 * Converts pipeline stage data from source format to target format
 * @param {Record<string, PipelineColumn>} sourceData - The source data object with string keys mapping to PipelineColumn objects
 * @returns {Record<string, Stage>} - The converted data in target format
 */
export function convertPipelineStageFormat(sourceData: Record<string, PipelineColumn>): Record<string, Stage> {
  const result: Record<string, Stage> = {};

  // Iterate through each key in the source object
  for (const key in sourceData) {
    if (Object.hasOwnProperty.call(sourceData, key)) {
      const sourceStage = sourceData[key];

      // Create the target stage object with the mapped properties
      result[key] = {
        id: sourceStage.stage_id,
        name: sourceStage.stage_name,
        move_criteria: sourceStage.move_criteria,
        is_new_candidate: false,
        is_assessment: sourceStage.is_assessment,
        is_interview: sourceStage.is_interview,
        is_offer_stage: sourceStage.is_offer_stage,
        offer_letter_send_automatic: sourceStage.offer_letter_send_automatic,
        offer_letter_manual_review: sourceStage.offer_letter_manual_review,
        offer_setup_customization: null,
        offer_email_subject: sourceStage.offer_email_subject || null,
        offer_email_body: sourceStage.offer_email_body || null,
        offer_cover_image: sourceStage.offer_cover_image || null,
        reminders: null,
        enable_automated_reminders: false,
        enable_onboarding_setup: false,
        rejection_setup_customization: null,
        customize_rejection_message: false,
        applicant_count: sourceStage.count || null,
        offer_attachment_url: sourceStage.offer_attachment_url || null,
        assessment: sourceStage.assessment,
        interview: sourceStage.interview,
        duration: sourceStage.duration,
        order: sourceStage.order,
        email_notification_enabled: sourceStage.email_notification_enabled,
        email_subject: sourceStage.email_subject || null,
        email_body: sourceStage.email_body || null,
        email_text: sourceStage.email_text || null,
        automation_enabled: sourceStage.automation_enabled,
      };
    }
  }

  return result;
}


/**
 * Converts a single pipeline column to a stage
 * @param {PipelineColumn} sourceData - The source pipeline column
 * @returns {Stage} - The converted stage
 */
export function convertPipelineColumnToStage(sourceData: PipelineColumn): Stage {
  return {
    id: sourceData.stage_id,
    name: sourceData.stage_name,
    move_criteria: sourceData.move_criteria,
    is_new_candidate: false, // Default value as it's not in source
    is_assessment: sourceData.is_assessment,
    is_interview: sourceData.is_interview,
    is_offer_stage: sourceData.is_offer_stage,
    offer_letter_send_automatic: sourceData.offer_letter_send_automatic,
    offer_letter_manual_review: sourceData.offer_letter_manual_review,
    offer_setup_customization: sourceData.offer_setup_customization,
    offer_email_subject: sourceData.offer_email_subject || null,
    offer_email_body: sourceData.offer_email_body || null,
    offer_cover_image: sourceData.offer_cover_image || null,
    offer_attachment_url: sourceData.offer_attachment_url || null,
    assessment: sourceData.assessment,
    interview: sourceData.interview,
    duration: sourceData.duration,
    order: sourceData.order,
    email_subject: sourceData.email_subject || null,
    email_body: sourceData.email_body || null,
    email_notification_enabled: sourceData.email_notification_enabled,
    email_text: sourceData.email_text || null,
    automation_enabled: sourceData.automation_enabled,
    enable_automated_reminders: sourceData.enable_automated_reminders,
    enable_onboarding_setup: sourceData.enable_onboarding_setup,
    customize_rejection_message: sourceData.customize_rejection_message,
    reminders: sourceData.reminders,
    rejection_setup_customization: sourceData.rejection_setup_customization,
    applicant_count: sourceData.count || null,
  };
}


export function addContentToTemplate(newContent: string, headerImageUrl?: string): string {
  const contentRegex = /(<td[^>]*class="esd-block-text es-p10t es-p10b es-p25r"[^>]*>)([\s\S]*?)(<\/td>)/i;
  const headerImageRegex = /<img[^>]*id="header-image"[^>]*>/i;

  let modifiedTemplate = originalTemplate.replace(contentRegex, (match, openingTag, content, closingTag) => {
    const newParagraph = `<div style="color:#333333">${newContent}</div>`;
    return `${openingTag}${content}${newParagraph}${closingTag}`;
  });

  if (headerImageUrl) {
    modifiedTemplate = modifiedTemplate.replace(headerImageRegex, `<img src="${headerImageUrl}" alt="Header" width="100%" style="display:block;width:100%;max-width:600px;height:auto;" id="header-image">`);
  }

  return modifiedTemplate;
}

export function extractContentFromTemplate(htmlTemplate: string): string {
  if (!htmlTemplate || typeof htmlTemplate !== 'string') {
    return '';
  }
  const contentRegex = /<td[^>]*class="esd-block-text es-p10t es-p10b es-p25r"[^>]*>([\s\S]*?)<\/td>/i;
  const match = htmlTemplate.match(contentRegex);
  if (match && match[1]) {
    return match[1].trim(); // Return the inner HTML of the <td> as-is
  }

  return '';
}

export const originalTemplate = `
<html dir="ltr" xmlns="http://www.w3.org/1999/xhtml" xmlns:o="urn:schemas-microsoft-com:office:office">
  <head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1" name="viewport">
    <meta name="x-apple-disable-message-reformatting">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="telephone=no" name="format-detection">
    <title></title>
    <!--[if (mso 16)]>
    <style type="text/css">
    a {text-decoration: none;}
    .link-button{
      padding: 15px 30px 15px 30px;
      border-radius: 50px;
    }
    </style>
    <![endif]-->
    <!--[if gte mso 9]><style>sup { font-size: 100% !important; }</style><![endif]-->
    <!--[if gte mso 9]>
<noscript>
         <xml>
           <o:OfficeDocumentSettings>
           <o:AllowPNG></o:AllowPNG>
           <o:PixelsPerInch>96</o:PixelsPerInch>
           </o:OfficeDocumentSettings>
         </xml>
      </noscript>
<![endif]-->
    <!--[if mso]><xml>
    <w:WordDocument xmlns:w="urn:schemas-microsoft-com:office:word">
      <w:DontUseAdvancedTypographyReadingMail/>
    </w:WordDocument>
    </xml><![endif]-->
  </head>
  <body class="body">
    <div dir="ltr" class="es-wrapper-color">
      <!--[if gte mso 9]>
			<v:background xmlns:v="urn:schemas-microsoft-com:vml" fill="t">
				<v:fill type="tile" color="#f6f6f6"></v:fill>
			</v:background>
		<![endif]-->
      <table cellspacing="0" cellpadding="0" width="100%" class="es-wrapper">
        <tbody>
          <tr>
            <td valign="top" class="esd-email-paddings">
              <table cellspacing="0" cellpadding="0" align="center" class="es-header">
                <tbody>
                  <tr>
                    <td align="center" class="esd-stripe">
                      <table cellspacing="0" cellpadding="0" width="100% max-width="600" bgcolor="#ffffff" align="center" class="es-header-body" style="background-color:#ffffff">
                        <tbody>
                          <tr>
                            <td bgcolor="#ffffff" align="left" class="esd-structure es-p10" style="background-color:#ffffff">
                              <table cellspacing="0" cellpadding="0" width="100%">
                                <tbody>
                                  <tr>
                                    <td width="580" valign="top" align="center" class="esd-container-frame">
                                      <table cellspacing="0" cellpadding="0" width="100%">
                                        <tbody>
                                          <tr>
                                            <td align="center" class="esd-block-image" style="font-size:0">
                                              <div style="width:100%; max-height:150px; overflow:hidden; position:relative;">
                                                <img src="https://res.cloudinary.com/dvdnjlxa8/image/upload/v1696419285/Group_40747_thpo8x.png" alt="Header" width="100%" style="display:block;width:100%;max-width:600px;height:auto;object-fit:cover;" id="header-image">
                                              </div>
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </td>
                  </tr>
                </tbody>
              </table>
              <table cellspacing="0" cellpadding="0" align="center" class="es-content">
                <tbody>
                  <tr>
                    <td align="center" class="esd-stripe">
                      <table cellspacing="0" cellpadding="0" width="100% max-width="600" bgcolor="#ffffff" align="center" class="es-content-body">
                        <tbody>
                          <tr>
                            <td align="left" class="esd-structure es-p40">
                              <table cellspacing="0" cellpadding="0" width="100%">
                                <tbody>
                                  <tr>
                                    <td width="520" align="left" class="esd-container-frame">
                                      <table cellspacing="0" cellpadding="0" width="100%">
                                        <tbody>
                                          <tr>
                                            <td align="left" class="esd-block-text es-p10t es-p10b es-p25r" style="padding:0px 20px">
                                              
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </td>
                  </tr>
                </tbody>
              </table>
              <table cellspacing="0" cellpadding="0" align="center" class="es-content" style="min-height:200px;">
                <tbody>
                  <tr>
                    <td align="center" class="esd-stripe">
                      <table cellspacing="0" cellpadding="0" width="100% max-width="600" bgcolor="#ffffff" align="center" class="es-content-body">
                        <tbody>
                          <tr>
                            <td align="left" class="esd-structure es-p20">
                              <!--[if mso]><table width="560" cellpadding="0" cellspacing="0"><tr><td width="193" valign="top"><![endif]-->
                              <table cellspacing="0" cellpadding="0" align="left" class="es-left">
                                <tbody>
                                  <tr>
                                    <td width="173" align="left" class="esd-container-frame">
                                      <table cellpadding="0" cellspacing="0" width="100%" role="presentation">
                                        <tr>
                                          <td align="center" class="esd-block-button es-p5t es-p10r">
                                              <a class="link-button" href="{{ accept_url }}" target="_blank" class="es-button" style="font-size: 13px; text-decoration: none; background: #755ae2; color: white; mso-border-alt: 10px solid #755ae2; padding:10px 12px; border-radius:50px;">
                                                ACCEPT
                                              </a>
                                          </td>
                                        </tr>
                                      </table>
                                    </td>
                                    <td width="20" class="es-hidden"></td>
                                  </tr>
                                  <tr>
                                    <td width="173" align="left" class="esd-container-frame">
                                      <table cellpadding="0" cellspacing="0" width="100%" role="presentation"></table>
                                    </td>
                                    <td width="20" class="es-hidden"></td>
                                  </tr>
                                </tbody>
                              </table>
                              <!--[if mso]></td><td width="173" valign="top"><![endif]-->
                              <table cellspacing="0" cellpadding="0" align="left" class="es-left">
                                <tbody>
                                  <tr>
                                    <td width="173" align="left" class="esd-container-frame">
                                      <table cellspacing="0" cellpadding="0" width="100%">
                                        <tbody>
                                          <tr>
                                            <td align="center" class="esd-block-button es-p5t es-p10r">
                                                <a class="link-button" href="{{ negotiate_url }}" target="_blank" class="es-button" style="font-size: 13px; text-decoration: none; background: #755ae2; color: white; mso-border-alt: 10px solid #755ae2; padding:10px 12px; border-radius:50px;">
                                                  NEGOTIATE
                                                </a>
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                              <!--[if mso]></td><td width="20"></td><td width="174" valign="top"><![endif]-->
                              <table cellpadding="0" cellspacing="0" align="right" class="es-right">
                                <tbody>
                                  <tr>
                                    <td width="174" align="left" class="esd-container-frame">
                                      <table cellpadding="0" cellspacing="0" width="100%" role="presentation">
                                        <tbody>
                                          <tr>
                                            <td align="center" class="esd-block-button es-p5t es-p10r">
                                                <a class="link-button" href="{{ decline_url }}" target="_blank" class="es-button" style="font-size: 13px; text-decoration: none; background: #755ae2; color: white; mso-border-alt: 10px solid #755ae2; padding:10px 12px; border-radius:50px;">
                                                  REJECT
                                                </a>
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                              <!--[if mso]></td></tr></table><![endif]-->
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </td>
                  </tr>
                </tbody>
              </table>
             
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </body>
</html>
`;
