import { z, Zod<PERSON>rror } from 'zod';

export const customFieldSchema = z.object({
  fieldTitle: z
    .string({ required_error: 'Please enter field title' })
    .min(1, { message: 'Please enter field title' }),
  fieldValue: z
    .string({ required_error: 'Please enter custom info here' })
    .min(1, { message: 'Please enter custom info' }),
});

export const customRequirementSchema = z.object({
  fieldTitle: z
    .string({ required_error: 'Please enter requirement name' })
    .min(1, { message: 'Please enter field title' }),
  fieldType: z.string({ required_error: 'Please select requirement type' }),
});

const default_paragraph_class = `<p class="whitespace-pre-wrap" style="min-height: 1lh;"></p>`;
export const CreateJobFormSchema1 = z
  .object({
    name: z.string({ required_error: 'Please enter job title' }).min(3, {
      message: "Please enter job title at least 3 character's long",
    }),
    industry: z
      .string({ required_error: 'Please select an industry' })
      .min(3, { message: 'Please select an industry' }),
    description: z
      .string({ required_error: 'Please enter job description' })
      .min(3, { message: 'Please enter job description' }),
    compulsory_requirements: z.string().optional(),
    requirements: z
      .string({ required_error: 'Please enter job requirements' })
      .min(3, { message: 'Please enter job requirements' }),
    responsibilities: z.string().optional(),
    added_advantage: z.string().optional(),
    company_overview: z
      .string({ required_error: 'Please enter company overview' })
      .min(3, { message: 'Please enter company overview' }),
    job_type: z
      .string({ required_error: 'Please select an employment type' })
      .min(3, { message: 'Please select an employment type' }),
    work_experience: z
      .string({ required_error: 'Please select an experience level' })
      .min(1, { message: 'Please select an experience level' }),
    working_option: z
      .string({ required_error: 'Please select working option' })
      .min(1, { message: 'Please select working option' }),
    proficiency_level: z
      .string({ required_error: 'Please select an proficiency level' })
      .min(1, { message: 'Please select an proficiency level' }),
    location: z
      .string({ required_error: 'Please enter location' })
      .min(2, { message: 'Please enter location' }),
    salary_type: z
      .string({ required_error: 'Please select salary type' })
      .min(1, { message: 'Please select salary type' }),
    salary_currency: z.enum(['NGN', 'EUR', 'GBP', 'USD']).optional(),
    min_salary: z.number().optional(),
    max_salary: z.number().optional(),
    fixed_salary: z.number().optional(),
    salary_negotiable: z.boolean(),
    application_start_date: z.date({
      required_error: 'Please select job post date',
    }),
    application_deadline: z.date({
      required_error: 'Please select application deadline',
    }),
    show_in_career_page: z.boolean(),
    post_as_anonymous: z.boolean(),
    client: z.string().optional(),
  })
  .refine(data => {
    if (data.company_overview == default_paragraph_class) {
      throw ZodError.create([
        {
          path: ['company_overview'],
          message: 'Please enter a company overview.',
          code: 'custom',
        },
      ]);
    }
    if (data.requirements == default_paragraph_class) {
      throw ZodError.create([
        {
          path: ['requirements'],
          message: 'Please enter a requirements.',
          code: 'custom',
        },
      ]);
    }
    if (data.responsibilities == default_paragraph_class) {
      throw ZodError.create([
        {
          path: ['responsibilities'],
          message: 'Please enter a responsibilities.',
          code: 'custom',
        },
      ]);
    }
    if (
      data.salary_type === 'RANGE' &&
      (Number.isNaN(data.min_salary) || !data.min_salary)
    ) {
      throw ZodError.create([
        {
          path: ['min_salary'],
          message: 'Please enter a minimum salary amount.',
          code: 'custom',
        },
      ]);
    }
    if (
      data.salary_type === 'RANGE' &&
      (Number.isNaN(data.max_salary) || !data.max_salary)
    ) {
      throw ZodError.create([
        {
          path: ['max_salary'],
          message: 'Please enter a maximum salary amount.',
          code: 'custom',
        },
      ]);
    }
    if (
      data.salary_type === 'FIXED' &&
      (Number.isNaN(data.fixed_salary) || !data.fixed_salary)
    ) {
      throw ZodError.create([
        {
          path: ['fixed_salary'],
          message: 'Please enter an amount.',
          code: 'custom',
        },
      ]);
    }
    if (data.application_start_date) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const application_start_date = new Date(data.application_start_date);
      application_start_date.setHours(0, 0, 0, 0);
      if (application_start_date < today) {
        throw ZodError.create([
          {
            path: ['application_start_date'],
            message: "Start date can't be before today",
            code: 'custom',
          },
        ]);
      }
    }
    if (data.application_deadline) {
      const application_start_date = new Date(data.application_start_date);
      application_start_date.setHours(0, 0, 0, 0);
      const application_deadline = new Date(data.application_deadline);
      application_deadline.setHours(0, 0, 0, 0);

      if (application_deadline < application_start_date) {
        throw ZodError.create([
          {
            path: ['application_deadline'],
            message: "End date can't be before start date",
            code: 'custom',
          },
        ]);
      }
    }
    return true;
  });

// export const CreateJobFormSchema2 = z.object({
//   accept_personal_details: z.boolean(),
//   accept_resume: z.boolean(),
//   accept_cover_letter: z.boolean(),
//   job_custom_fields: z.array(customFieldSchema).optional(),
//   job_requirement_custom_fields: z.array(customRequirementSchema).optional(),
//   pipeline_type: z
//     .string({ required_error: 'Please select a job pipeline type' })
//     .min(1, { message: 'Please select a job pipeline type' }),
//   pipeline: z.string().optional(),
//   team_member: z.array(z.string()).optional(),
//   // Default pipeline configuration fields
//   default_pipeline_automation_enabled: z.boolean().optional(),
//   default_pipeline_move_criteria: z.preprocess((val) => {
//      if (val === "") return undefined;
//     if (typeof val === "string") return Number(val);
//     return val;
//   },
//     z.number({
//       required_error: "Please enter a percentage."
//     })
//       .min(20, { message: "Please input a minimum of 20% percentage match." })
//       .max(100, { message: "Percentage cannot exceed 100%." })
//   )
// });

export const CreateJobFormSchema2 = z
  .object({
    accept_personal_details: z.boolean(),
    accept_resume: z.boolean(),
    accept_cover_letter: z.boolean(),
    job_custom_fields: z.array(customFieldSchema).optional(),
    job_requirement_custom_fields: z.array(customRequirementSchema).optional(),
    pipeline_type: z
      .string({ required_error: 'Please select a job pipeline type' })
      .min(1, { message: 'Please select a job pipeline type' }),
    pipeline: z.string().optional(),
    team_member: z.array(z.string()).optional(),

    default_pipeline_automation_enabled: z.boolean().optional(),
    default_pipeline_move_criteria: z.preprocess(
      (val) => {
        if (val === '') return undefined;
        if (typeof val === 'string') return Number(val);
        return val;
      },
      z
        .number({
          required_error: 'Please enter a percentage.',
        })
        .min(20, { message: 'Please input a minimum of 20% percentage match.' })
        .max(100, { message: 'Percentage cannot exceed 100%.' })
        .optional() // start as optional, will be conditionally required
    ),
  })
  .superRefine((data, ctx) => {
    if (data.default_pipeline_automation_enabled) {
      if (
        data.default_pipeline_move_criteria === undefined ||
        data.default_pipeline_move_criteria === null
      ) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Please enter a percentage.',
          path: ['default_pipeline_move_criteria'],
        });
      }
    }
  });

