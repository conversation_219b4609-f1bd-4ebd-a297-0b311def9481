import { z } from 'zod';

// Enums for AI job configuration
export type ConductMode = 'AI_ONLY' | 'HUMAN_RECRUITER' | 'HYBRID';
export type SchedulingOption = 'after_job_posting' | 'within_a_duration_span' | 'base_on_candidate_dates';
export type AssessmentType = 'Generic' | 'Custom';

// Team member availability type for API
export interface TeamMemberAvailability {
    date: string; // YYYY-MM-DD format
    start_time: string; // HH:MM format
    end_time: string; // HH:MM format
    team_member_email: string;
}

// Team member availability schema for API
export const teamMemberAvailabilitySchema = z.object({
    date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format'),
    start_time: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Start time must be in HH:MM format'),
    end_time: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'End time must be in HH:MM format'),
    team_member_email: z.string().email('Must be a valid email'),
});

// Interview setup stage schema for new API format
export const interviewSetupStageSchema = z.object({
    title: z.string().min(1, 'Stage title is required'),
    conduct_mode: z.enum(['AI_ONLY', 'HUMAN_RECRUITER', 'HYBRID']).default('AI_ONLY'),
    interview_duration: z.string()
        .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Please enter valid time format (HH:MM)')
        .default('01:30'),
    team_members: z.array(z.union([z.string(), z.number()])).default([]), // Allow both string and number for form compatibility
    scheduling_options: z.enum(['after_job_posting', 'within_a_duration_span', 'base_on_candidate_dates'])
        .default('after_job_posting'),
    description: z.string().default(''),
    custom_questions: z.array(z.string()).default([]),
    team_members_availability: z.array(teamMemberAvailabilitySchema).default([]),
});

// Legacy stage configuration schema for backward compatibility
export const stageConfigurationSchema = z.object({
    conduct_mode: z.enum(['AI_ONLY', 'HUMAN_RECRUITER', 'HYBRID']).default('AI_ONLY'),
    interview_duration: z.string()
        .regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Please enter valid time format (HH:MM)')
        .default('01:30'),
    team_member_ids: z.array(z.string()).default([]),
    team_members_availability: z.array(teamMemberAvailabilitySchema).default([]),
    scheduling_option: z.enum(['after_job_posting', 'within_a_duration_span', 'base_on_candidate_dates'])
        .default('after_job_posting'),
    description: z.string().default(''),
    custom_questions: z.array(z.string()).default([]),
    assessment_type: z.enum(['Generic', 'Custom']).default('Generic'),
    assessment_id: z.string().uuid().optional(),
    assessment_notification_mode: z.boolean().default(false),
    anonymous_posting: z.boolean().default(false),
});

// New AI job configuration schema matching the API format
export const aiJobConfigurationSchema = z.object({
    require_resume: z.boolean().default(true),
    require_personal_details: z.boolean().default(true),
    require_cover_letter: z.boolean().default(false),
    // require_cover_letter is optional in new format
    conduct_mode: z.enum(['AI_ONLY', 'HUMAN_RECRUITER', 'HYBRID']).default('AI_ONLY'),
    pre_assessment_questions: z.array(z.string()).default([]),
    interview_setup_stages: z.array(interviewSetupStageSchema).min(1, 'At least one interview stage is required'),
    post_as_anonymous: z.boolean().default(false),
});

// Legacy schema for backward compatibility
export const legacyAIJobConfigurationSchema = z.object({
    require_resume: z.boolean().default(true),
    require_personal_details: z.boolean().default(true),
    require_cover_letter: z.boolean().default(false),
    conduct_mode: z.enum(['AI_ONLY', 'HUMAN_RECRUITER', 'HYBRID']).default('AI_ONLY'),
    post_as_anonymous: z.boolean().default(false),
    initial_screening_stage_config: stageConfigurationSchema,
    technical_screening_stage_config: stageConfigurationSchema,
    final_interview_stage_config: stageConfigurationSchema,
});

// Type exports
export type InterviewSetupStage = z.infer<typeof interviewSetupStageSchema>;
export type StageConfiguration = z.infer<typeof stageConfigurationSchema>;
export type AIJobConfiguration = z.infer<typeof aiJobConfigurationSchema>;
export type LegacyAIJobConfiguration = z.infer<typeof legacyAIJobConfigurationSchema>;

// Default values for new interview setup stages
export const defaultInterviewStage: InterviewSetupStage = {
    title: '',
    conduct_mode: 'AI_ONLY',
    interview_duration: '01:30',
    team_members: [] as (string | number)[], // Explicitly type as union array
    scheduling_options: 'after_job_posting',
    description: '',
    custom_questions: [],
    team_members_availability: [],
};

// Default values for new configurations
export const defaultAIJobConfiguration: AIJobConfiguration = {
    require_resume: true,
    require_personal_details: true,
    require_cover_letter: false,
    conduct_mode: 'AI_ONLY',
    pre_assessment_questions: [],
    interview_setup_stages: [
        { ...defaultInterviewStage, title: 'Initial' },
        { ...defaultInterviewStage, title: 'Technical' },
        { ...defaultInterviewStage, title: 'Final' },
    ],
    post_as_anonymous: false,
};

// Legacy default values for backward compatibility
export const defaultStageConfig: StageConfiguration = {
    conduct_mode: 'AI_ONLY',
    interview_duration: '01:30',
    team_member_ids: [],
    team_members_availability: [],
    scheduling_option: 'after_job_posting',
    description: '',
    custom_questions: [],
    assessment_type: 'Generic',
    assessment_notification_mode: false,
    anonymous_posting: false,
};

// Form validation schema with custom refinements
export const aiJobConfigurationFormSchema = aiJobConfigurationSchema.refine((data) => {
    // Validate that if conduct_mode is HYBRID, at least one stage should have team members
    if (data.conduct_mode === 'HYBRID') {
        const hasTeamMembers = data.interview_setup_stages.some(stage =>
            stage.team_members.length > 0
        );

        if (!hasTeamMembers) {
            return false;
        }
    }
    return true;
}, {
    message: "When using HYBRID mode, at least one stage must have team members assigned",
    path: ["conduct_mode"]
});

// Legacy API request/response types for backward compatibility
export interface LegacyJobConfigurationRequest {
    require_resume: boolean;
    require_personal_details: boolean;
    require_cover_letter: boolean;
    overall_conduct_mode: ConductMode;
    initial_screening_stage_config: {
        conduct_mode: ConductMode;
        interview_duration: string;
        team_member_ids: string[];
        team_members_availability: TeamMemberAvailability[];
        scheduling_option: SchedulingOption;
        description: string;
        custom_questions: string[];
        assessment_type: AssessmentType;
        assessment_id?: string;
        assessment_notification_mode: boolean;
        anonymous_posting: boolean;
    };
    technical_screening_stage_config: {
        conduct_mode: ConductMode;
        interview_duration: string;
        team_member_ids: string[];
        team_members_availability: TeamMemberAvailability[];
        scheduling_option: SchedulingOption;
        description: string;
        custom_questions: string[];
        assessment_type: AssessmentType;
        assessment_id?: string;
        assessment_notification_mode: boolean;
        anonymous_posting: boolean;
    };
    final_interview_stage_config: {
        conduct_mode: ConductMode;
        interview_duration: string;
        team_member_ids: string[];
        team_members_availability: TeamMemberAvailability[];
        scheduling_option: SchedulingOption;
        description: string;
        custom_questions: string[];
        assessment_type: AssessmentType;
        assessment_id?: string;
        assessment_notification_mode: boolean;
        anonymous_posting: boolean;
    };
}

// Helper function to convert HH:MM format to minutes
const HHMMToMinutes = (timeString: string): number => {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
};

// Helper function to transform form data to new API request format
export const transformToApiRequest = (formData: AIJobConfiguration): any => {
    return {
        require_resume: formData.require_resume,
        require_personal_details: formData.require_personal_details,
        require_cover_letter: formData.require_cover_letter,
        conduct_mode: formData.conduct_mode,
        pre_assessment_questions: formData.pre_assessment_questions,
        interview_setup_stages: formData.interview_setup_stages.map(stage => ({
            title: stage.title,
            conduct_mode: stage.conduct_mode,
            // Convert HH:MM format to minutes for API
            interview_duration: HHMMToMinutes(stage.interview_duration),
            // Convert string[] to number[] for API compatibility
            team_members: Array.isArray(stage.team_members)
                ? stage.team_members.map(id => typeof id === 'string' ? parseInt(id, 10) : id)
                : [],
            scheduling_options: stage.scheduling_options,
            description: stage.description,
            custom_questions: stage.custom_questions,
            // Format time values to HH:MM format (remove seconds if present)
            team_members_availability: stage.team_members_availability.map(availability => ({
                ...availability,
                start_time: availability.start_time.includes(':')
                    ? availability.start_time.split(':').slice(0, 2).join(':')
                    : availability.start_time,
                end_time: availability.end_time.includes(':')
                    ? availability.end_time.split(':').slice(0, 2).join(':')
                    : availability.end_time,
            })),
        })),
        post_as_anonymous: formData.post_as_anonymous,
    };
};

// Helper function to convert minutes to HH:MM format
const minutesToHHMM = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
};

// Helper function to transform new API response to form data format
export const transformFromApiResponse = (apiData: any): AIJobConfiguration => {
    // Handle case where apiData might be null or undefined
    if (!apiData) {
        console.warn('transformFromApiResponse: apiData is null or undefined, using defaults');
        return defaultAIJobConfiguration;
    }

    // Ensure interview_setup_stages exists and is an array
    const stages = Array.isArray(apiData.interview_setup_stages)
        ? apiData.interview_setup_stages
        : [];

    return {
        require_resume: apiData.require_resume ?? true,
        require_personal_details: apiData.require_personal_details ?? true,
        require_cover_letter: apiData.require_cover_letter ?? false,
        conduct_mode: apiData.conduct_mode ?? 'AI_ONLY',
        pre_assessment_questions: apiData.pre_assessment_questions ?? [],
        interview_setup_stages: stages.map((stage: any) => ({
            title: stage.title || '',
            conduct_mode: stage.conduct_mode || 'AI_ONLY',
            // Convert minutes to HH:MM format if it's a number
            interview_duration: typeof stage.interview_duration === 'number'
                ? minutesToHHMM(stage.interview_duration)
                : stage.interview_duration || '01:30',
            // Convert number[] to string[] for form compatibility
            team_members: Array.isArray(stage.team_members)
                ? stage.team_members.map((id: any) => String(id))
                : [],
            scheduling_options: stage.scheduling_options || 'after_job_posting',
            description: stage.description || '',
            custom_questions: stage.custom_questions || [],
            team_members_availability: stage.team_members_availability || [],
        })),
        post_as_anonymous: apiData.post_as_anonymous ?? false,
    };
};
