import React, { createContext, useCallback, useContext, useRef } from 'react';

type ValidationFunction = () => Promise<boolean>;
type ValidationWithErrorsFunction = () => Promise<{ isValid: boolean; errors?: any }>;

const ValidationContext = createContext<{
    registerValidation: (step: number, validate: ValidationFunction) => void;
    registerValidationWithErrors: (step: number, validate: ValidationWithErrorsFunction) => void;
    validateStep: (step: number) => Promise<boolean>;
    validateStepWithErrors: (step: number) => Promise<{ isValid: boolean; errors?: any }>;
} | null>(null);

 const JobCreatorValidationProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
    const validationFunctions = useRef<Record<number, ValidationFunction>>({});
    const validationWithErrorsFunctions = useRef<Record<number, ValidationWithErrorsFunction>>({});

    const registerValidation = useCallback(
        (step: number, validate: ValidationFunction) => {
            validationFunctions.current[step] = validate;
        }, [],
    );

    const registerValidationWithErrors = useCallback(
        (step: number, validate: ValidationWithErrorsFunction) => {
            validationWithErrorsFunctions.current[step] = validate;
        }, [],
    );

    const validateStep = async (step: number) => {
        const validate = validationFunctions.current[step];
        if (validate) {
            const result = await validate();
            if (!result) {
                console.log(`Validation failed for step ${step}. The validation function returned false.`);
                console.log('This means the form validation (trigger()) failed. Check the form errors in the component.');
            }
            return result;
        }
        return true;
    };

    const validateStepWithErrors = async (step: number) => {
        const validateWithErrors = validationWithErrorsFunctions.current[step];
        if (validateWithErrors) {
            const result = await validateWithErrors();
            if (!result.isValid) {
                console.log(`Validation failed for step ${step}. Errors:`, result.errors);
            }
            return result;
        }
        return { isValid: true };
    };

    return (
        <ValidationContext.Provider value={{
            registerValidation,
            registerValidationWithErrors,
            validateStep,
            validateStepWithErrors
        }}>
            {children}
        </ValidationContext.Provider>
    );
};

export default JobCreatorValidationProvider;

export const useJobCreatorValidation = () => {
    const context = useContext(ValidationContext);
    if (!context) {
        throw new Error('useValidation must be used within a ValidationProvider');
    }
    return context;
};