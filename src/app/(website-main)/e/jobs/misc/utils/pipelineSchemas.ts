import { z, ZodError } from 'zod';

export const singleStageSchema = z.object({
  id: z.union([z.string(), z.number()]).optional(),
  order: z.number().optional(),
  name: z
    .string({ required_error: 'Please enter a stage name' })
    .min(1, { message: 'Please enter stage name' }),
  is_interview: z.string(),
  interview: z.string().optional(),
  is_assessment: z.string(),
  is_offer_stage: z.string(),
  assessment: z.string().optional(),
  automation_enabled: z.string(),
  email_notification_enabled: z.string(),
  offer_letter_send_automatic: z.string(),
  offer_letter_manual_review: z.string(),
  offer_email_subject: z.string().optional(),
  offer_email_body: z.string().optional(),
  offer_cover_image: z.string().optional(),
  offer_attachment_url: z.string().optional(),
  email_subject: z.string().optional(),
  email_body: z.string().optional(),
  email_text: z.string().optional(),
  duration: z.union([z.string(), z.number()]).optional(),
  move_criteria: z.union([z.string(), z.number()]).optional(),
}).refine((data) => {
  if (data.is_assessment === "true" && data.is_interview === "true") {
    throw ZodError.create([{
      path: ['is_assessment', 'is_interview'],
      message: 'A stage cannot be both an assessment and interview stage.',
      code: 'custom',
    }]);
  }
  if (data.is_interview === "true" && (data.interview?.trim() === "" || !data.interview)) {
    throw ZodError.create([{
      path: ['interview'],
      message: 'Please select an assessment for this stage.',
      code: 'custom',
    }]);
  }
  if (data.is_assessment === "true" && (data.assessment?.trim() === "" || !data.assessment)) {
    throw ZodError.create([{
      path: ['assessment'],
      message: 'Please select an assessment for this stage.',
      code: 'custom',
    }]);
  }
  if ((data.is_assessment === "true" || data.is_interview === "true") && (data.duration == '0' || !data.duration)) {
    throw ZodError.create([{
      path: ['duration'],
      message: 'Please input an assessment duration longer than 0 days.',
      code: 'custom',
    }]);
  }
  if (data.automation_enabled === "true" && (data.move_criteria === undefined || Number(data.move_criteria) <= 19)) {
    throw ZodError.create([{
      path: ['move_criteria'],
      message: 'Please input a minimum of 20% percentage match.',
      code: 'custom',
    }]);
  }
  if (data.email_notification_enabled === "true" && (!data.email_subject || data.email_subject?.trim() === "" || !data.email_text || data.email_text?.trim() === "")) {
    throw ZodError.create([{
      path: ['email_text', 'email_subject'],
      message: 'Please input an assessment duration longer than 0 days.',
      code: 'custom',
    }]);
  }
  return true
});


export const stageSchema = z.object({
  id: z.union([z.string(), z.number()]).optional(),
  order: z.number().optional(),
  name: z
    .string({ required_error: 'Please enter a stage name' })
    .min(1, { message: 'Please enter stage name' }),
  is_interview: z.boolean(),
  is_assessment: z.boolean(),
  is_offer_stage: z.boolean(),
  automation_enabled: z.boolean(),
  assessment: z.string().optional().nullable(),
  interview: z.string().optional().nullable(),
  email_notification_enabled: z.boolean(),
  email_subject: z.string().optional(),
  email_body: z.string().optional(),
  email_text: z.string().optional(),
  duration: z.number().optional(),
  move_criteria: z.union([z.string(), z.number()]).optional(),
  offer_letter_send_automatic: z.boolean().optional(),
  offer_letter_manual_review: z.boolean().optional(),
  offer_email_subject: z.string().optional().nullable(),
  offer_email_body: z.string().optional().nullable(),
  offer_cover_image: z.string().optional().nullable(),
  offer_attachment_url: z.string().optional().nullable(),
})



export const editPipelineStagesSchema = z.object({
  stages: z.array(stageSchema),
  currentEdit: z.number(),
}).refine((data) => {
  const { stages } = data;

  stages.forEach((stage, index) => {
    if (stage.is_assessment === true && (stage.assessment?.trim() === "" || !stage.assessment)) {
      throw ZodError.create([{
        path: ['stages', index, 'assessment'],
        message: 'Please select an assessment for this stage.',
        code: 'custom',
      }]);
    }
    if (stage.is_interview === true && (stage.interview?.trim() === "" || !stage.interview)) {
      throw ZodError.create([{
        path: ['stages', index, 'interview'],
        message: 'Please select an interview for this stage.',
        code: 'custom',
      }]);
    }

    // TODO: confirm the purpose of the duration
    // if ((stage.is_assessment === true || stage.is_interview === true) && (stage.duration == 0 || !stage.duration)) {
    //   throw ZodError.create([{
    //     path: ['stages', index, 'duration'],
    //     message: 'Please input an assessment duration longer than 0 days.',
    //     code: 'custom',
    //   }]);
    // }

    if (stage.automation_enabled === true && (stage.move_criteria === undefined || Number(stage.move_criteria) <= 19)) {
      throw ZodError.create([{
        path: ['stages', index, 'move_criteria'],
        message: 'Please input a minimum of 20% percentage match.',
        code: 'custom',
      }]);
    }

    if (stage.email_notification_enabled === true && (!stage.email_subject || stage.email_subject?.trim() === "" || stage.email_subject?.trim() === "<p><br></p>" || !stage.email_text || stage.email_text?.trim() === "" || stage.email_text?.trim() === "<p><br></p>")) {
      throw ZodError.create([{
        path: ['stages', index, 'email_text'],
        message: 'Please input an email body.',
        code: 'custom',
      }, {
        path: ['stages', index, 'email_subject'],
        message: 'Please input an email subject.',
        code: 'custom',
      }]);
    }
  });

  return true;
});

