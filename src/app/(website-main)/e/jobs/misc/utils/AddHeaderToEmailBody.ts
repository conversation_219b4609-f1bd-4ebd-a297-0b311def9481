

export const AddHeaderToEmailBody = (body: string, include_triggers?: boolean) => {
  const getTriggers = () => {
    if (include_triggers) {
      return `
      <div class="links-container" style="margin-top: 3rem; display: flex; gap:0.5rem; flex-wrap: wrap;">
        <a href="{{ confirm_url }}" class="btn btn-accept" style="color:white; margin-right:0.5rem;">Accept Invite</a>
        <a href="{{ reschedule_url }}" class="btn btn-reschedule" style="color:#741fa7; margin-right:0.5rem;">Request Reschedule</a>
        <a href="{{ cancel_url }}" class="btn btn-reschedule" style="color:#741fa7;">Reject Invite</a>
      </div>
    `
    } else {
      return ``
    }
  }

  const triggerLinks = '' //getTriggers()

  return `
      <!DOCTYPE html>
    <html lang="en">

    <head>
      <meta charset="UTF-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <title>Email Notification from Getlinked</title>
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
      <link
        href="https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap"
        rel="stylesheet" />
      <style>
        * {
          font-family: "DM sans", Verdana;
          font-size: 0.975rem;
        }

        body {
          background-color: #f4f4f4;
        }

        .container {
          max-width: 600px;
          margin: 0 auto;
          background-color: #fff;
          border-radius: 10px;
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .inner-container {
          padding: 5% 8.5%;
          margin-left:auto;
          margin-right:auto;
        }

        h2 {
          color: #333;
        }

        p {
          color: #666;
        }


        .btn {
          padding: 9px 18px;
          border-radius: 0.75rem;
          text-decoration: none;
          border: solid 2px transparent;
          transition: all ease-in-out 250ms;
          font-size: 0.875rem;
        }


        .btn:hover {
          transform: scale(0.925);
          text-decoration: none;
        }

        .btn-accept {
          background-color: #741fa7;
          color: white;
        }

        .btn-reschedule {
          border-color: #741fa7;
          background: white;
          color: #741fa7;
        }

        .btn-rejected {
          background-color: #ce4f5a;
          color: white !important;
        }
      </style>
    </head>

    <body clas>
      <div class="container" style="flex-direction: column !important;">
        <header>
          <img data-id="react-email-img"
            src="https://res.cloudinary.com/dvdnjlxa8/image/upload/v1696419285/Group_40747_thpo8x.png"
            style="display: block; outline: none; border: none; text-decoration: none; width: 100%; " />
        </header>
        <div class="inner-container">


          <section data-id="react-email-inserted-text" style=" font-size: 14px; line-height: 24px; margin: 0px; padding-top: 0.5rem; padding-bottom: 0.5rem;">
            ${body}
          </section>

          ${triggerLinks}

          <footer style="margin-top: 1.125rem;">
            <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0"
              role="presentation">
              <tbody>
                <tr>
                  <td>
                    <p data-id="react-email-text"
                      style="                                        font-size: 15px;                                        font-weight: 500;                                        line-height: 24px;                                        margin: 0px;                                        padding-top: 0.5rem;                                        padding-bottom: 0.5rem;                                      ">
                      Follow Us: </p>
                    <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0"
                      cellspacing="0" role="presentation">
                      <tbody>
                        <tr>
                          <td>
                            <table align="center" width="100%" data-id="react-email-row" role="presentation" cellspacing="0"
                              cellpadding="0" border="0" style="width: 50%; float: left">
                              <tbody style="width: 100%">
                                <tr style="width: 100%">
                                  <td data-id="__react-email-column"> <a data-id="react-email-link" target="_blank"
                                      href="https://www.facebook.com/getLinkedai"
                                      style="                                                        color: #067df7;                                                        text-decoration: none;                                                      "><img
                                        data-id="react-email-img"
                                        src="https://res.cloudinary.com/dvdnjlxa8/image/upload/v1696421029/facebook_1_s65uhg.png"
                                        style="                                                          display: block;                                                          outline: none;                                                          border: none;                                                          text-decoration: none;                                                          width: 2.25rem;                                                          height: 2.25rem;                                                        " /></a>
                                  </td>
                                  <td data-id="__react-email-column"> <a data-id="react-email-link" target="_blank"
                                      href="https://www.instagram.com/getlinked.ai/"
                                      style="                                                        color: #067df7;                                                        text-decoration: none;                                                      "><img
                                        data-id="react-email-img"
                                        src="https://res.cloudinary.com/dvdnjlxa8/image/upload/v1696421695/instagram_1_hnq7sw.png"
                                        style="                                                          display: block;                                                          outline: none;                                                          border: none;                                                          text-decoration: none;                                                          width: 2.25rem;                                                          height: 2.25rem;                                                        " /></a>
                                  </td>
                                  <td data-id="__react-email-column"> <a data-id="react-email-link" target="_blank"
                                      style="                                                        color: #067df7;                                                        text-decoration: none;                                                      "><img
                                        data-id="react-email-img"
                                        src="https://res.cloudinary.com/dvdnjlxa8/image/upload/v1696421823/mail_1_tnqajd.png"
                                        style="                                                          display: block;                                                          outline: none;                                                          border: none;                                                          text-decoration: none;                                                          width: 2.25rem;                                                          height: 2.25rem;                                                        " /></a>
                                  </td>
                                  <td data-id="__react-email-column"> <a data-id="react-email-link" target="_blank"
                                      href="https://twitter.com/getLinkedai"
                                      style="                                                        color: #067df7;                                                        text-decoration: none;                                                      "><img
                                        data-id="react-email-img"
                                        src="https://res.cloudinary.com/dvdnjlxa8/image/upload/v1696421834/twitter_1_fbgedg.png"
                                        style="                                                          display: block;                                                          outline: none;                                                          border: none;                                                          text-decoration: none;                                                          width: 2.25rem;                                                          height: 2.25rem;                                                        " /></a>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
            <section style="display:flex; align-items:center; justify-content:space-between; font-size:0.875rem !important; margin-top:0.75rem;">
              <span data-id="react-email-text" style="font-size: 0.875rem"> © 2023, Getlinked All rights reserved. </span>
              <a href="https:getlinked.ai/terms-and-conditions" data-id="react-email-link" target="_blank" 
              style="float:right; font-size: 0.825rem"> 
                Terms | Privacy
              </a>
            </section>
          </footer>


        </div>
      </div>
    </body>

    </html>
    `
}




export const ExtractOriginalBodyString = (html: string) => {
  // const regex = new RegExp(`<div\\s+data-id="react-email-inserted-text"[^>]*>(.*?)<\\/div>`);
  // const match = regex.exec(html);
  // if (match && match.length > 1) {
  //   return match[1].trim();
  // } else {
  //   return ""; // Return null if data-id is not found
  // }
  const startTag = '<section data-id="react-email-inserted-text"';
  const endTag = '</section>';

  const startIndex = html.indexOf(startTag);
  if (startIndex === -1) return ''; // Not found

  const endIndex = html.indexOf(endTag, startIndex);
  if (endIndex === -1) return ''; // Not found

  const divContentStartIndex = html.indexOf('>', startIndex) + 1;

  return html.substring(divContentStartIndex, endIndex).trim();

  // Your HTML content here
  // const htmll = `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
  // <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  // <html lang="en">
  //   <!-- Your HTML content here -->
  // </html>`;

  // const insertedTextContent = extractInsertedTextFromHTML(html);

};
