"use client";
import React, { useState } from "react";
import useInfiniteScroll from "react-infinite-scroll-hook";

import { Search } from "@/components/shared/icons";
import { Button, EmptyCard, LinkButton, Popover, PopoverContent, PopoverTrigger } from "@/components/shared";


import JobMatchLoader from "./cards/JobLoader";
import JobCard from "./cards/JobCard";
import Filter from "./Filter";
import useGetJobs, { PaginatedJobTypes } from "../api/getCreatedJobs";
import { EmptyJobIcon, PlusIcon } from "../icons";
import { ActiveJob } from "../types";


interface props {
  filterUrl?: string;
  handleFilterJobs?: () => void
}


const Drafts = ({ }: props) => {
  const [searchParams, setSearchParams] = useState<string>("")
  const [filterUrl, setFilterUrl] = useState("&job_status=DRAFT")
  const [openFilter, setOpenFilter] = useState(false)


  const {
    data,
    error,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isFetchingNextPage,
    isLoading: loading,
    refetch: refetchJobs
  } = useGetJobs(filterUrl, searchParams)

  const nextPage = hasNextPage !== undefined && data?.pages !== null;

  const [sentryRef] = useInfiniteScroll({
    loading: isFetchingNextPage || loading,
    hasNextPage: nextPage,
    onLoadMore: fetchNextPage,
    disabled: !!error,
    rootMargin: "0px 0px 400px 0px",
  });




  return (
    <div className="relative overflow-x-auto md:mt-2 h-full">
      <section className="sticky top-0 flex flex-wrap items-center justify-between bg-white shadow-sm md:mx-4 px-4 py-2" >
        <h2 className="max-w-max">
          Drafts:{" "}
          <span className="text-header-text font-medium">
            {data?.pages && data.pages[0].results.length}
          </span>
        </h2>


        <div className="flex items-center gap-2 ml-auto">

          <div className='relative ml-auto'>
            <Search className="absolute right-[5%] top-[25%]" />
            <input
              type="search"
              placeholder="Search"
              value={searchParams}
              onChange={(e) => setSearchParams(e.target.value)}
              className="px-2.5 py-2 sm:px-4 border-[1.75px] border-[#D6D6D6] rounded-lg text-xs md:text-[0.9rem] focus:border-primary focus:outline-none transition-all"
            />
          </div>


          <Popover>
            <PopoverTrigger>
              <span className='px-2.5 sm:px-4 py-2 border-[#D6D6D6] border-[1.75px] rounded-[0.35rem] text-[#556575] text-xs md:text-[0.875rem]'>
                Filter
              </span>
            </PopoverTrigger>
            <PopoverContent className="max-h-96" align='end'>
              <Filter filterUrl={filterUrl} setFilterUrl={setFilterUrl} draft />
            </PopoverContent>
          </Popover>
        </div>
      </section>



      <div className="w-full bg-[#F8F9FB] rounded-[1.25rem]  overflow-y-auto p-6 pt-4 max-md:pb-16">
        {
          loading && (
            <div className="flex flex-col gap-6 w-full">
              <JobMatchLoader match={10} />
            </div>
          )
        }




        <div className="flex flex-col gap-6">
          {
            data?.pages?.map((jobs: PaginatedJobTypes, index) =>
              jobs?.results.length < 1 ?
                <EmptyCard
                  title="No job found in your draft"
                  icon={<EmptyJobIcon />}
                  contentClass="flex flex-col items-center gap-6"
                  titleClass="mt-8"
                  key={index}
                  content={
                    <>
                      <div className='text-center mt-2 '>
                        <p className='text-[0.875rem] font-normal'>Do you have a new position opened up? Click button to start creating your job post.</p>
                      </div>
                      <LinkButton href='./create' variant={'default'} icon={<PlusIcon width={17} height={17} />}>Create Job</LinkButton>
                    </>
                  }
                />
                :
                jobs?.results?.map((job: ActiveJob) => (
                  <>
                    {
                      !loading && (
                        <JobCard job={job} key={job?.id} refetch={refetchJobs} />
                      )
                    }
                  </>
                ))
            )}

          {(isFetchingNextPage || hasNextPage) && (
            <div
              className=" w-full flex flex-col gap-6"
              ref={sentryRef}
            >
              {isFetchingNextPage && <JobMatchLoader match={3} />}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Drafts;
