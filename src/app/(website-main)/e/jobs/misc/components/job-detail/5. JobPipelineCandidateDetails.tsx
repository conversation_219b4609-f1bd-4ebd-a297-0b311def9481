'use client';

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@radix-ui/react-popover';
import { useQuery } from '@tanstack/react-query';
import * as React from 'react';
import toast from 'react-hot-toast';
import {
  <PERSON><PERSON>,
  Drawer,
  DrawerClose,
  Drawer<PERSON>ontent,
  Drawer<PERSON>ooter,
  Drawer<PERSON>eader,
  DrawerTrigger,
  LoaderBtn,
  Skeleton,
  ToolTip,
} from '@/components/shared';
import {
  DoubleForward,
  Eye,
  Info,
  Spinner,
  StrokeCircleCheck,
} from '@/components/shared/icons';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/shared/tabs';
import { useWindowWidth } from '@/hooks';
import { Axios } from '@/lib/api/axios';
import { cn } from '@/utils';
import { getInitials } from '@/utils/strings';
import { CandidateReportModal } from '../../../../report/misc/components/CandidateReport';
import { useEditIsSeen, useEditPercentageMatch } from '../../api';
import { CandidateDetails, PipelineColumn } from '../../types/PipelineType';
import CandidateDetailsAboutTabs from './5.1 JobPipelineCandidateDetailsAboutTab';
import CandidateDetailsAssessmentsTab from './5.2 JobPipelineCandidateDetailsAssessmentsTab';
import CandidateDetailsDocumentsTab from './5.3 JobPipelineCandidateDetailsDocumentsTab';
import CandidateDetailsTrailTab from './5.4 JobPipelineCandidateDetailsTrailTab';
import CandidateDetailsChatTab from './5.5 JobPipelineCandidateDetailsChatTab';
import { useHasChatData } from './5.5 JobPipelineCandidateDetailsChatTab/api';
import CandidateDetailsCommentsTab from './JobPipelineCandidateDetailsCommentsTab';

interface props {
  applicant_id: number;
  allApplicantsId: number[];
  isDrawerOpen: boolean;
  refetchJobData: () => void;
  closeDrawer: () => void;
  openDrawer: () => void;
  currentStage: PipelineColumn;
  viewOnly?: boolean;
  progressCandidate?: () => void;
  sendAssessmentInvite?: () => void;
}

const CandidateDetailsDrawer: React.FC<props> = ({
  applicant_id,
  currentStage,
  allApplicantsId,
  isDrawerOpen,
  openDrawer,
  closeDrawer,
  refetchJobData,
  viewOnly = false,
  progressCandidate,
  sendAssessmentInvite,
}) => {
  const windowWidth = useWindowWidth();
  const [currentCandidate, setCurrentCandidate] =
    React.useState<number>(applicant_id);
  const currentCandidateIndex = allApplicantsId.findIndex(
    candidate => candidate === currentCandidate
  );
  const nextCandidate =
    currentCandidateIndex + 1 < allApplicantsId.length
      ? allApplicantsId[currentCandidateIndex + 1]
      : allApplicantsId[0];
  const previousCandidate =
    currentCandidateIndex > 0
      ? allApplicantsId[currentCandidateIndex - 1]
      : allApplicantsId[allApplicantsId.length - 1];

  const moveToNext = () => {
    setCurrentCandidate(nextCandidate);
    setCurrentTab(prev => prev);
  };

  const moveToPrevious = () => {
    setCurrentCandidate(previousCandidate);
  };

  React.useEffect(() => {
    setCurrentCandidate(applicant_id);
  }, [applicant_id]);

  const [skills, setSkills] = React.useState<string[]>([]);
  const {
    data: basicDetails,
    isLoading: isLoadingDetails,
    refetch: refetchBasicDetails,
  } = useQuery(
    [`candidate-basic-details-${currentCandidate}`, currentCandidate],
    async () => {
      const response = await Axios.get(
        `/recruiter/candidate_applied_job_detail/${currentCandidate}/`
      );
      setSkills(response.data?.skills);
      return response.data?.job_application as CandidateDetails;
    },
    {
      enabled: isDrawerOpen,
    }
  );

  // Check if chat data exists for this candidate
  const { data: hasChatData } = useHasChatData({
    unique_id: basicDetails?.job?.unique_id as string,
    applicant_email: basicDetails?.email || '',
  });

  const [currentMatch, setCurrentMatch] = React.useState(
    basicDetails?.percentage_match
  );
  const percentageInputRef = React.useRef<HTMLInputElement | null>(null);

  const tabListRef = React.useRef<HTMLDivElement>(null);
  const [currentTab, setCurrentTab] = React.useState('about');
  const [openReport, setOpenReport] = React.useState(false);
  // Dynamic category array that conditionally includes Chat tab
  const catgoryArray = React.useMemo(() => {
    const baseTabs = [
      {
        id: 1,
        title: 'About',
      },
      {
        id: 2,
        title: 'Documents',
      },
      {
        id: 3,
        title: 'Assessments',
      },
      {
        id: 4,
        title: 'Comments',
      },
    ];

    // Add Chat tab only if chat data exists
    if (hasChatData) {
      baseTabs.push({
        id: 5,
        title: 'Chat Details',
      });
    }

    // Always add Journey tab at the end
    baseTabs.push({
      id: hasChatData ? 6 : 5,
      title: 'Journey',
    });

    return baseTabs;
  }, [hasChatData]);

  const { mutate: updateMatch } = useEditPercentageMatch(
    basicDetails?.job.id || 0
  );
  const changePercentageMatch = () => {
    if (currentMatch && isNaN(currentMatch)) {
      toast.error('Enter a valid number');
    }
    updateMatch(
      { id: currentCandidate, percentage_match: currentMatch || 0 },
      {
        onSuccess() {
          toast.success('Update match score successfully');
          refetchJobData();
          refetchBasicDetails();
        },
        onError() {
          toast.error('Encountered error updating match score');
        },
      }
    );
  };
  const { mutate: markASeen, isLoading: isMarkingAsSeen } = useEditIsSeen(
    basicDetails?.job.id || 0
  );
  const markCandidateAsSeen = () => {
    if (currentMatch && isNaN(currentMatch)) {
      toast.error('Enter a valid number');
    }
    markASeen(
      { id: currentCandidate, is_seen: true },
      {
        onSuccess() {
          toast.success('Marked as seen');
          refetchJobData();
          refetchBasicDetails();
        },
        onError() {
          toast.error('Encountered marking candidate as seeen');
        },
      }
    );
  };

  const markAsSeenAndMoveToNext = () => {
    markASeen(
      { id: currentCandidate, is_seen: true },
      {
        onSuccess() {
          refetchJobData();
          refetchBasicDetails();
          moveToNext();
        },
        onError() {
          toast.error('Encountered marking candidate as seeen');
        },
      }
    );
  };

  React.useEffect(() => {
    if (!isLoadingDetails && basicDetails) {
      setCurrentMatch(basicDetails.percentage_match);
    }
  }, [basicDetails]);

  React.useEffect(() => {
    if (isDrawerOpen) {
      refetchBasicDetails();
    }
  }, [currentCandidate]);

  const close = () => {
    closeDrawer();
    setCurrentTab('about');
    setCurrentCandidate(applicant_id);
  };

  return (
    <Drawer
      onClose={() => close()}
      dismissible
      direction={windowWidth < 720 ? 'bottom' : 'right'}
    >
      <DrawerTrigger asChild className="max-w-max">
        <Button
          variant="extralight"
          size="tiny"
          className="max-w-max text-[0.7rem]"
          onClick={() => openDrawer()}
        >
          View Details
        </Button>
      </DrawerTrigger>

      <DrawerContent className="!m-0 h-[90vh] w-full overflow-hidden rounded-l-2xl border-none bg-white !p-0 md:left-auto md:right-0 md:h-screen md:w-[60%] md:max-w-[750px]">
        <div className="relative flex max-h-full grow flex-col overflow-y-hidden">
          <div className="flex w-full items-center justify-center bg-primary md:hidden">
            <div className="mx-auto mb-1 mt-3 h-1 w-[100px] rounded-full bg-white/60" />
          </div>

          <DrawerHeader className="sticky top-0 flex w-full items-center justify-between bg-primary px-5 text-white md:px-8 max-md:pt-1">
            <h3>Candidate Details</h3>
            <DrawerClose className="rounded-lg bg-white/30 px-6 py-2 text-sm">
              Close
            </DrawerClose>
          </DrawerHeader>

          <section className="flex grow flex-col overflow-y-scroll">
            <header className="flex items-start gap-4 p-6 pb-4">
              {isLoadingDetails ? (
                <Skeleton className="h-14 w-14 shrink-0 rounded-full" />
              ) : (
                <div className="flex h-14 w-14 shrink-0 items-center justify-center rounded-full bg-primary text-xl font-bold text-white">
                  {getInitials(basicDetails?.name || 'First Last')}
                </div>
              )}
              <div>
                {isLoadingDetails ? (
                  <>
                    <Skeleton className="h-4 w-28 shrink-0 rounded-md md:w-60" />
                    <div className="mt-2.5 flex items-center gap-2">
                      <Skeleton className="h-2.5 w-16 shrink-0 rounded-md md:w-40" />
                      <Skeleton className="h-8 w-6 shrink-0 rounded-md md:w-32" />
                      <Skeleton className="h-8 w-6 shrink-0 rounded-md md:w-32" />
                    </div>
                  </>
                ) : (
                  <>
                    <h4 className="text-lg font-medium text-primary md:text-xl">
                      {basicDetails?.name}
                    </h4>
                    <section className="flex flex-wrap items-center gap-3">
                      <p className="text-sm">{basicDetails?.email}</p>
                      {!basicDetails?.matching_reason && (
                        <ToolTip
                          content="The candidate's data has been submitted, please remain patient while the AI scores them."
                          contentClass="text-sm !max-w-xs"
                        >
                          <p className="text-sm text-orange-400">scoring...</p>
                        </ToolTip>
                      )}
                      {basicDetails?.percentage_match !== undefined &&
                        currentMatch !== undefined &&
                        basicDetails?.percentage_match !== null &&
                        basicDetails?.matching_reason !== null && (
                          <>
                            <p
                              className={cn(
                                'flex items-center gap-1 rounded-lg px-4 py-2 text-xs',
                                currentMatch <= 20 && 'bg-red-100 text-red-500',
                                currentMatch > 20 &&
                                  currentMatch <= 40 &&
                                  'bg-orange-100 text-orange-500',
                                currentMatch > 40 &&
                                  currentMatch <= 60 &&
                                  'bg-yellow-100 text-yellow-500',
                                currentMatch > 60 &&
                                  currentMatch < 70 &&
                                  'bg-blue-100 text-blue-500',
                                currentMatch >= 70 &&
                                  'bg-primary-light text-primary'
                              )}
                            >
                              <span>
                                CV Score:
                                <input
                                  ref={percentageInputRef}
                                  type="number"
                                  value={currentMatch}
                                  max={100}
                                  className="max-w-4 ml-1 w-4 bg-transparent focus:outline-none"
                                  onChange={e => {
                                    const value = Number(e.target.value);
                                    if (value <= 100) {
                                      setCurrentMatch(value);
                                    } else {
                                      setCurrentMatch(100);
                                    }
                                  }}
                                />
                                %
                              </span>
                              <ToolTip content="You can edit this score as you see fit.">
                                <Info />
                              </ToolTip>
                            </p>
                            <span
                              className={`flex items-center transition-opacity duration-300 ${
                                percentageInputRef.current !== null &&
                                (
                                  percentageInputRef.current as HTMLInputElement
                                ).matches(':focus')
                                  ? 'inline-block opacity-100'
                                  : 'hidden opacity-0'
                              }`}
                            >
                              <ToolTip content="Save changes to percentage match">
                                <StrokeCircleCheck
                                  stroke="black"
                                  width={20}
                                  height={20}
                                  strokeWidth={3}
                                  onClick={changePercentageMatch}
                                />
                              </ToolTip>
                            </span>
                          </>
                        )}

                      {/* Display interview overall score if available */}
                      {basicDetails?.interview_overall_score && (
                        <p
                          className={cn(
                            'flex items-center gap-1 rounded-lg px-4 py-2 text-xs',
                            basicDetails.interview_overall_score <= 20 &&
                              'bg-red-100 text-red-500',
                            basicDetails.interview_overall_score > 20 &&
                              basicDetails.interview_overall_score <= 40 &&
                              'bg-orange-100 text-orange-500',
                            basicDetails.interview_overall_score > 40 &&
                              basicDetails.interview_overall_score <= 60 &&
                              'bg-yellow-100 text-yellow-500',
                            basicDetails.interview_overall_score > 60 &&
                              basicDetails.interview_overall_score < 70 &&
                              'bg-blue-100 text-blue-500',
                            basicDetails.interview_overall_score >= 70 &&
                              'bg-primary-light text-primary'
                          )}
                        >
                          <span>
                            Interview Score:{' '}
                            {basicDetails.interview_overall_score.toFixed(1)}%
                          </span>
                        </p>
                      )}

                      {/* Display combined score if available */}
                      {basicDetails?.combined_score ? (
                        <p
                          className={cn(
                            'flex items-center gap-1 rounded-lg px-4 py-2 text-xs',
                            basicDetails.combined_score <= 20 &&
                              'bg-red-100 text-red-500',
                            basicDetails.combined_score > 20 &&
                              basicDetails.combined_score <= 40 &&
                              'bg-orange-100 text-orange-500',
                            basicDetails.combined_score > 40 &&
                              basicDetails.combined_score <= 60 &&
                              'bg-yellow-100 text-yellow-500',
                            basicDetails.combined_score > 60 &&
                              basicDetails.combined_score < 70 &&
                              'bg-blue-100 text-blue-500',
                            basicDetails.combined_score >= 70 &&
                              'bg-primary-light text-primary'
                          )}
                        >
                          <span>
                            Overall Score: {basicDetails.combined_score}%
                          </span>
                        </p>
                      ) : null}

                      {basicDetails?.is_seen ? (
                        <Button
                          className="flex cursor-none items-center gap-1 rounded-lg !border-transparent bg-primary-light px-4 py-1.5 text-xs"
                          variant="outlined"
                          size="tiny"
                        >
                          <Eye width={20} height={20} />
                          Seen
                        </Button>
                      ) : (
                        <Button
                          className="flex items-center gap-1 rounded-lg border-[1.05px] px-4 py-2 text-xs"
                          variant="outlined"
                          size="tiny"
                          onClick={markCandidateAsSeen}
                          icon={
                            isMarkingAsSeen && (
                              <LoaderBtn width={13} height={13} />
                            )
                          }
                          iconPosition="after"
                          // title="Mark "
                        >
                          Mark as seen
                        </Button>
                      )}
                      <Button size="tiny" onClick={() => setOpenReport(true)}>
                        View Report
                      </Button>
                      <CandidateReportModal
                        candidateData={basicDetails}
                        isOpen={openReport}
                        onClose={() => setOpenReport(false)}
                      />
                    </section>
                  </>
                )}
              </div>
            </header>

            <Tabs
              defaultValue={'about'}
              className="!grid grow grid-rows-[max-content,1fr] overflow-x-hidden"
            >
              <TabsList
                ref={tabListRef}
                className={cn(
                  'flex items-center justify-start gap-1.5 overflow-x-auto bg-[#F1EFFC] p-[0.25rem] px-6 [scrollbar-width:none] md:justify-start '
                )}
              >
                {catgoryArray?.map((cat: { id: number; title: string }) => {
                  const { title, id } = cat;
                  return (
                    <TabsTrigger
                      className={cn(
                        'taboption relative min-w-[6rem] flex-1 transition-all duration-500 md:min-w-[5rem] md:max-w-max',
                        'rounded-full py-1.5 md:rounded-[0.55rem] md:px-4 lg:px-6',
                        'text-xs font-normal leading-5 xs:text-sm',
                        'focus:outline-none active:outline-none',
                        title.toLowerCase() === currentTab.toLowerCase()
                          ? 'active'
                          : 'taboption text-body-text '
                      )}
                      onClick={() => setCurrentTab(title.toLowerCase())}
                      value={title.toLowerCase()}
                      key={id}
                    >
                      <span className="flex items-center gap-3">{title}</span>
                    </TabsTrigger>
                  );
                })}
              </TabsList>

              {isLoadingDetails ? (
                <div className="flex h-full w-full items-center justify-center">
                  <Spinner />
                </div>
              ) : (
                <TabsContent
                  className="mt-2 max-w-full overflow-y-scroll"
                  value={currentTab.toLowerCase()}
                >
                  {/* /////////////////////////////////////////////////////////////////////////// */}
                  {/* /////////////////////////////////////////////////////////////////////////// */}
                  {currentTab.toLowerCase() === 'about' && (
                    <CandidateDetailsAboutTabs
                      basicDetails={basicDetails!}
                      skills={skills}
                    />
                  )}

                  {/* /////////////////////////////////////////////////////////////////////////// */}
                  {/* /////////////////////////////////////////////////////////////////////////// */}
                  {currentTab.toLowerCase() === 'documents' && (
                    <CandidateDetailsDocumentsTab
                      isLoadingCVFile={isLoadingDetails}
                      cvFileName={basicDetails?.cv || ''}
                      cvFile={basicDetails?.cv_file_path}
                    />
                  )}

                  {/* /////////////////////////////////////////////////////////////////////////// */}
                  {/* /////////////////////////////////////////////////////////////////////////// */}
                  {currentTab.toLowerCase() === 'assessments' &&
                    basicDetails && (
                      <CandidateDetailsAssessmentsTab
                        enabled={currentTab.toLowerCase() === 'assessments'}
                        id={basicDetails.id}
                      />
                    )}

                  {/* /////////////////////////////////////////////////////////////////////////// */}
                  {/* /////////////////////////////////////////////////////////////////////////// */}
                  {currentTab.toLowerCase() === 'comments' && (
                    <CandidateDetailsCommentsTab candidate={basicDetails!} />
                  )}

                  {/* /////////////////////////////////////////////////////////////////////////// */}
                  {/* /////////////////////////////////////////////////////////////////////////// */}
                  {currentTab.toLowerCase() === 'chat details' &&
                    basicDetails &&
                    hasChatData && (
                      <CandidateDetailsChatTab
                        unique_id={basicDetails.job.unique_id as string}
                        applicant_email={basicDetails.email}
                        enabled={currentTab.toLowerCase() === 'chat details'}
                      />
                    )}

                  {/* /////////////////////////////////////////////////////////////////////////// */}
                  {/* /////////////////////////////////////////////////////////////////////////// */}
                  {currentTab.toLowerCase() === 'journey' &&
                    basicDetails?.trail && (
                      <CandidateDetailsTrailTab data={basicDetails?.trail} />
                    )}
                </TabsContent>
              )}
            </Tabs>
          </section>

          <DrawerFooter className="sticky bottom-0 flex w-full flex-row flex-wrap items-center justify-between rounded-t-xl bg-primary-light text-white md:px-8 ">
            <Button
              size="tiny"
              variant="outlined"
              onClick={moveToPrevious}
              icon={<DoubleForward className="rotate-180" />}
            >
              Prev
            </Button>

            <div className="flex flex-wrap items-center gap-4">
              {currentStage &&
                currentStage.is_assessment &&
                sendAssessmentInvite && (
                  <Button
                    size="tiny"
                    variant="white"
                    onClick={sendAssessmentInvite}
                  >
                    Send assessment invite
                  </Button>
                )}

              {progressCandidate && !viewOnly && (
                <>
                  <Button
                    size="tiny"
                    variant="outlined"
                    onClick={progressCandidate}
                  >
                    Move to next stage
                  </Button>
                  <Button
                    size="tiny"
                    variant="unstyled"
                    className={cn('text-primary')}
                  >
                    Remove
                  </Button>
                </>
              )}
            </div>

            {basicDetails?.is_seen ? (
              <Button size="tiny" variant="outlined" onClick={moveToNext}>
                Next
                <DoubleForward />
              </Button>
            ) : (
              <Popover>
                <PopoverTrigger asChild>
                  <Button size="tiny" variant="outlined">
                    Next
                    <DoubleForward />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="rounded-md bg-white p-1.5 text-sm shadow-md">
                  <div className="mb-2 border-b border-b-helper-text p-1.5 pr-6 pt-0 text-header-text">
                    Mark as seen?
                  </div>
                  <div
                    className="cursor-pointer rounded-md py-1.5 pl-2 pr-4 text-[0.8125rem] text-body-text hover:bg-primary-light"
                    onClick={markAsSeenAndMoveToNext}
                  >
                    Yes
                  </div>
                  <div
                    className="cursor-pointer rounded-md py-1.5 pl-2 pr-4 text-[0.8125rem] text-body-text hover:bg-primary-light"
                    onClick={moveToNext}
                  >
                    No
                  </div>
                </PopoverContent>
              </Popover>
            )}
          </DrawerFooter>
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export default CandidateDetailsDrawer;
