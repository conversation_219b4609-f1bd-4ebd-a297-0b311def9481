import React, { useEffect, useState } from 'react';
import { SmallSpinner, Spinner } from '@/components/shared/icons';

interface props {
    cvFile: string
}
const ViewDOCX: React.FC<props> = ({ cvFile }) => {
    const [isLoading, setLoading] = useState(true);
    const [file, setFile] = useState<string>('');
    useEffect(() => {
        function removeQueryParameters(url: string) {
            const indexPdf = url.indexOf(".docx")
            if (indexPdf !== -1) {
                return url.substring(0, indexPdf + 5);
            } else {
                return url;
            }
        }

        const cleanedUrl = removeQueryParameters(cvFile);
        setFile(cleanedUrl);
        setLoading(false);

    }, [cvFile])


    return (
        <>
            {
                isLoading ?
                    <div className='w-full h-full flex items-center justify-center'>
                        <SmallSpinner color="#755AE2" />
                    </div>

                    :
                    <>
                        {/* <iframe className="w-full h-full min-h-[400px]" src={`https://view.officeapps.live.com/op/embed.aspx?src=${file}`} ></iframe> */}
                        <iframe src={`https://view.officeapps.live.com/op/embed.aspx?src=${file}`} width='100%' height='100%'>
                            This is an embedded <a target='_blank' href='http://office.com'>Microsoft Office</a> document, powered by <a target='_blank' href='http://office.com/webapps'>Office Online</a>.
                        </iframe>
                    </>

            }

        </>
    );
}
export default ViewDOCX