'use client';

import React from 'react';
import { ChatMessage } from '../api';

interface MessageTypesDemoProps {
  onMessageClick?: (message: ChatMessage) => void;
}

const MessageTypesDemo: React.FC<MessageTypesDemoProps> = ({
  onMessageClick,
}) => {
  // Sample messages demonstrating different user types and message types
  const sampleMessages: ChatMessage[] = [
    {
      content:
        "Hello! I'm here to help you with your application. Let's start by uploading your CV.",
      user_type: 'ai',
      message_type: 'text',
      timestamp: new Date().toISOString(),
    },
    {
      content:
        "Thank you! I've uploaded my CV and I'm excited about this opportunity.",
      user_type: 'applicant',
      message_type: 'text',
      timestamp: new Date(Date.now() + 60000).toISOString(),
    },
    {
      content: 'CV uploaded successfully',
      user_type: 'applicant',
      message_type: 'cv_upload',
      timestamp: new Date(Date.now() + 120000).toISOString(),
    },
    {
      content:
        'Great! I can see your experience in software development. Can you tell me more about your recent projects?',
      user_type: 'ai',
      message_type: 'text',
      timestamp: new Date(Date.now() + 180000).toISOString(),
    },
    {
      content: '<PERSON> has taken over the conversation',
      user_type: 'human_recruiter',
      message_type: 'takeover_start',
      timestamp: new Date(Date.now() + 240000).toISOString(),
      recruiter_name: 'Sarah Johnson',
    },
    {
      content:
        "Hi there! I'm <PERSON>, a senior recruiter at the company. I've reviewed your CV and I'm impressed with your background. I'd like to discuss the role in more detail.",
      user_type: 'human_recruiter',
      message_type: 'recruiter_message',
      timestamp: new Date(Date.now() + 300000).toISOString(),
      recruiter_name: 'Sarah Johnson',
    },
    {
      content:
        "Thank you Sarah! I'm very interested in learning more about the position and the team.",
      user_type: 'applicant',
      message_type: 'text',
      timestamp: new Date(Date.now() + 360000).toISOString(),
    },
    {
      content:
        "Perfect! Let me tell you about our tech stack and the exciting projects you'd be working on...",
      user_type: 'human_recruiter',
      message_type: 'recruiter_message',
      timestamp: new Date(Date.now() + 420000).toISOString(),
      recruiter_name: 'Sarah Johnson',
    },
  ];

  const formatTimestamp = (timestamp: string) => {
    try {
      return new Date(timestamp).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return timestamp;
    }
  };

  const renderMessage = (message: ChatMessage, index: number) => {
    const isAI = message.user_type === 'ai';
    const isRecruiter =
      message.user_type === 'recruiter' ||
      message.user_type === 'human_recruiter';
    const isApplicant = message.user_type === 'applicant';
    const isCVUpload = message.message_type === 'cv_upload';
    const isTakeoverMessage =
      message.message_type === 'takeover_status' ||
      message.message_type === 'takeover_start';

    // Special rendering for takeover status messages
    if (isTakeoverMessage) {
      return (
        <div key={index} className="mb-4 flex w-full justify-center">
          <div className="max-w-[80%] rounded-lg border border-blue-200 bg-blue-50 px-4 py-2 text-center">
            <div className="flex items-center justify-center gap-2 text-sm text-blue-800">
              <div className="flex h-4 w-4 items-center justify-center">
                <span className="text-xs">🔄</span>
              </div>
              <span className="font-medium">{message.content}</span>
            </div>
            <div className="mt-1 text-xs text-blue-600">
              {formatTimestamp(message.timestamp)}
            </div>
          </div>
        </div>
      );
    }

    return (
      <div
        key={index}
        className={`mb-4 flex w-full cursor-pointer transition-opacity hover:opacity-80 ${
          isAI || isApplicant ? 'justify-start' : 'justify-end'
        }`}
        onClick={() => onMessageClick?.(message)}
      >
        <div
          className={`max-w-[80%] rounded-lg px-4 py-3 shadow-sm ${
            isAI
              ? 'border border-primary/20 bg-primary-light text-primary'
              : isRecruiter
              ? 'border border-purple-200 bg-purple-100 text-purple-900'
              : 'border border-gray-200 bg-gray-100 text-header-text'
          }`}
        >
          {/* Message content */}
          <div className="mb-2">
            {isCVUpload ? (
              <div className="flex items-center gap-2">
                <div className="flex h-4 w-4 items-center justify-center rounded-full bg-green-500">
                  <span className="text-xs text-white">✓</span>
                </div>
                <span className="text-sm font-medium">
                  CV uploaded successfully
                </span>
              </div>
            ) : (
              <p className="whitespace-pre-wrap text-sm">{message.content}</p>
            )}
          </div>

          {/* Timestamp and user type */}
          <div className="flex items-center justify-between text-xs opacity-70">
            <div className="flex items-center gap-1">
              {/* User type icon */}
              <div className="flex h-4 w-4 items-center justify-center">
                {isAI ? (
                  <span className="text-xs">🤖</span>
                ) : isRecruiter ? (
                  <span className="text-xs">👨‍💼</span>
                ) : (
                  <span className="text-xs">👤</span>
                )}
              </div>
              <span className="font-medium">
                {isAI
                  ? 'AI Assistant'
                  : isRecruiter
                  ? message.recruiter_name || 'Human Recruiter'
                  : 'Applicant'}
              </span>
              {/* Message type indicator for recruiter messages */}
              {isRecruiter &&
                message.message_type &&
                message.message_type !== 'text' && (
                  <span className="ml-1 rounded bg-black/10 px-1 py-0.5 text-[10px]">
                    {message.message_type.replace('_', ' ')}
                  </span>
                )}
            </div>
            <span>{formatTimestamp(message.timestamp)}</span>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="rounded-lg border border-gray-200 bg-white p-6">
      <h3 className="mb-4 text-lg font-semibold text-gray-900">
        Message Types Visual Demo
      </h3>

      <div className="mb-4 rounded-lg bg-yellow-50 p-4">
        <h4 className="mb-2 font-medium text-yellow-800">
          Visual Styling Guide
        </h4>
        <div className="space-y-2 text-sm text-yellow-700">
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded border border-primary/20 bg-primary-light"></div>
            <span>
              <strong>AI Messages:</strong> Blue theme, left-aligned, 🤖 icon
            </span>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded border border-purple-200 bg-purple-100"></div>
            <span>
              <strong>Human Recruiter Messages:</strong> Purple theme,
              right-aligned, 👨‍💼 icon
            </span>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded border border-gray-200 bg-gray-100"></div>
            <span>
              <strong>Applicant Messages:</strong> Gray theme, left-aligned, 👤
              icon
            </span>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded border border-blue-200 bg-blue-50"></div>
            <span>
              <strong>Takeover Messages:</strong> Blue theme, center-aligned, 🔄
              icon
            </span>
          </div>
        </div>
        <div className="mt-3 rounded bg-green-100 p-2 text-xs text-green-800">
          <strong>✅ Unified Styling:</strong> Both WebSocket (real-time) and
          HTTP (historical) messages now use identical visual styling for
          consistency.
        </div>
      </div>

      <div className="space-y-4">
        {sampleMessages.map((message, index) => renderMessage(message, index))}
      </div>
    </div>
  );
};

export default MessageTypesDemo;
