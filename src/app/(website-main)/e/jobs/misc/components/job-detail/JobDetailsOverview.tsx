import { format } from 'date-fns'
import Image from 'next/image'
import React from 'react'

import { Avatar } from '@/components/shared'
import { Check } from '@/components/shared/icons'
import { useBooleanStateControl } from '@/hooks'
import { cn } from '@/utils'
import { addCommasToNumber } from '@/utils/numbers'
import { convertKebabAndSnakeToTitleCase, formatLineBreaks } from '@/utils/strings'

import { EditIcon } from '../../icons'
import { ActiveJob } from '../../types'
import { EditJobBasicInfoModal, EditJobCompanyInfoModal, EditJobRequirementsModal } from './modals'

interface jobDetailsProps {
    job: ActiveJob
    refetch: () => void
}
const JobDetails: React.FC<jobDetailsProps> = ({ job, refetch }) => {
    const {
        state: isBasicInfoModalOpen,
        setTrue: openBasicInfoModal,
        setFalse: closeBasicInfoModal
    } = useBooleanStateControl()
    const {
        state: isCompanyInfoModalOpen,
        setTrue: openCompanyInfoModal,
        setFalse: closeCompanyInfoModal
    } = useBooleanStateControl()
    const {
        state: isConfigModalOpen,
        setTrue: openConfigModal,
        setFalse: closeConfigModal
    } = useBooleanStateControl()

    const { job_title, industry, company_overview, proficiency_level, description, job_type, working_option, location, job_status, work_experience, salary_currency, min_salary, max_salary, compulsory_requirements, requirements, responsibilities,
        application_deadline, created_at, team_member, requirement_custom_fields, stage_data, salary_range, salary_type, salary_negotiable, fixed_salary, added_advantage } = job || {}




    return (
        <>
            <div className='max-md:hidden w-full rounded-full bg-[#F8F9FB] h-[0.3rem] mb-1'></div>
            <div className='grow lg:grid grid-cols-[repeat(auto-fit,minmax(300px,1fr))] bg-[#F8F9FB] gap-6 p-4 h-full overflow-y-scroll max-lg:mb-8'>


                <article className='flex flex-col gap-5 bg-white p-4 md:p-6 !pt-0 rounded-xl xl:h-full overflow-y-auto'>
                    <header className='sticky top-0 bg-white flex items-center gap-4 border-b pb-3  pt-6'>
                        <Image src={job.company.logo} alt={job.company.name} width={33} height={33} className='rounded-full' />
                        <h3 className='font-medium text-base text-header-text truncate '>{job_title}</h3>

                        <EditIcon className='ml-auto cursor-pointer' fill='#292D32' width={17} height={17} onClick={openBasicInfoModal} />
                    </header>


                    <div className='grid lg:grid-cols-2 gap-4 md:gap-x-[3.5vw]'>
                        <section className='grid grid-cols-[subgrid] lg:col-span-2 gap-4'>
                            <p className='flex flex-col'>
                                <span className='text-header-text text-[0.875rem] font-medium max-h-[2lh] text-ellipsis overflow-hidden'>
                                    {job_title}
                                </span>
                                <span className='text-helper-text text-[0.75rem]'>
                                    Job Title
                                </span>
                            </p>
                            <p className='flex flex-col'>
                                <span className='text-header-text text-[0.875rem] font-medium'>
                                    {location}
                                </span>
                                <span className='text-helper-text text-[0.75rem]'>
                                    Location
                                </span>
                            </p>
                            <p className='flex flex-col'>
                                <span className='text-header-text text-[0.875rem] font-medium max-h-[2lh] text-ellipsis overflow-hidden'>
                                    {industry}
                                </span>
                                <span className='text-helper-text text-[0.75rem]'>
                                    Industry
                                </span>
                            </p>

                            {
                                salary_type === "RANGE" ?
                                    <p className='flex flex-col'>
                                        <span className='text-header-text text-[0.875rem] font-medium'>
                                            {salary_currency}{addCommasToNumber(Number(min_salary))} - {salary_currency}{addCommasToNumber(Number(max_salary))}
                                        </span>
                                        <span className='text-helper-text text-[0.75rem]'>
                                            Salary range
                                        </span>
                                    </p>
                                    :
                                    salary_type === "FIXED" ?
                                        <p className='flex flex-col'>
                                            <span className='text-header-text text-[0.875rem] font-medium'>
                                                {salary_currency}{addCommasToNumber(Number(fixed_salary))}
                                            </span>
                                            <span className='text-helper-text text-[0.75rem]'>
                                                Fixed salary
                                            </span>
                                        </p>
                                        :
                                        <p className='flex flex-col'>
                                            <span className='text-header-text text-[0.875rem] font-medium'>
                                                Undisclosed
                                            </span>
                                            <span className='text-helper-text text-[0.75rem]'>
                                                Salary
                                            </span>
                                        </p>
                            }

                            <p className='flex flex-col'>
                                <span className='text-header-text text-[0.875rem] font-medium'>
                                    {convertKebabAndSnakeToTitleCase(proficiency_level)}
                                </span>
                                <span className='text-helper-text text-[0.75rem]'>
                                    Proficiency level
                                </span>
                            </p>

                            <p className='flex flex-col'>
                                <span className='text-header-text text-[0.875rem] font-medium'>
                                    {work_experience}
                                </span>
                                <span className='text-helper-text text-[0.75rem]'>
                                    Experience
                                </span>
                            </p>

                            <p className='flex flex-col'>
                                <span className='text-header-text text-[0.875rem] font-medium'>
                                    {convertKebabAndSnakeToTitleCase(job_type)}
                                </span>
                                <span className='text-helper-text text-[0.75rem]'>
                                    Employment type
                                </span>
                            </p>

                            <p className='flex flex-col'>
                                <span className='text-header-text text-[0.875rem] font-medium'>
                                    {convertKebabAndSnakeToTitleCase(working_option)}
                                </span>
                                <span className='text-helper-text text-[0.75rem]'>
                                    Work option
                                </span>
                            </p>

                            <p className='flex flex-col'>
                                <span className='text-header-text text-[0.875rem] font-medium'>
                                    {format(new Date(created_at), "dd MMMM yyyy")}
                                </span>
                                <span className='text-helper-text text-[0.75rem]'>
                                    Application start date
                                </span>
                            </p>

                            <p className='flex flex-col'>
                                <span className='text-header-text text-[0.875rem] font-medium'>
                                    {format(new Date(application_deadline), "dd MMMM yyyy")}
                                </span>
                                <span className='text-helper-text text-[0.75rem]'>
                                    Application close date
                                </span>
                            </p>

                        </section>
                    </div>
                </article>


                <article className='flex flex-col gap-5 bg-white max-lg:mt-5 p-4 md:p-6 !pt-0 rounded-xl xl:h-full overflow-y-auto'>
                    <header className='sticky top-0 bg-white flex items-center gap-4 border-b pb-3 pt-6'>
                        <h3 className='font-medium text-base text-header-text'>Job Details</h3>
                        <EditIcon className='ml-auto cursor-pointer' fill='#292D32' width={17} height={17} onClick={openCompanyInfoModal} />
                    </header>


                    <div>
                        <h3 className='text-header-text text-[0.875rem] font-medium'>Company Overview</h3>
                        <p className='text-helper-text text-[0.825rem] font-normal'
                            dangerouslySetInnerHTML={{ __html: company_overview }}
                        />
                    </div>

                    {
                        description &&
                        <div>
                            <h3 className='text-header-text text-[0.875rem] font-medium'>Job Description</h3>
                            <p className='text-helper-text text-[0.825rem] font-normal tiptap' dangerouslySetInnerHTML={{ __html: formatLineBreaks(description) }}></p>
                        </div>
                    }
                    {
                        responsibilities &&
                        <div>
                            <h3 className='text-header-text text-[0.875rem] font-medium'>Responsibilities</h3>
                            <p className='text-helper-text text-[0.825rem] font-normal tiptap' dangerouslySetInnerHTML={{ __html: formatLineBreaks(responsibilities) }}></p>
                        </div>
                    }
                    {
                        compulsory_requirements &&
                        <div>
                            <h3 className='text-header-text text-[0.875rem] font-medium'>Compulsory Requirements</h3>
                            <p className='text-helper-text text-[0.825rem] font-normal tiptap' dangerouslySetInnerHTML={{ __html: formatLineBreaks(compulsory_requirements) }}></p>
                        </div>
                    }
                    {
                        requirements &&
                        <div>
                            <h3 className='text-header-text text-[0.875rem] font-medium'>{compulsory_requirements ? "Additional" : ""} Requirements</h3>
                            <p className='text-helper-text text-[0.825rem] font-normal tiptap' dangerouslySetInnerHTML={{ __html: formatLineBreaks(requirements) }}></p>
                        </div>
                    }
                    {
                        added_advantage &&
                        <div>
                            <h3 className='text-header-text text-[0.875rem] font-medium'>Added Advantage</h3>
                            <p className='text-helper-text text-[0.825rem] font-normal tiptap' dangerouslySetInnerHTML={{ __html: formatLineBreaks(added_advantage) }}></p>
                        </div>
                    }
                </article>



                <article className='flex flex-col gap-5 bg-white max-lg:mt-5 p-4 md:p-6 !pt-0 rounded-xl xl:h-full overflow-y-auto'>
                    <header className='sticky top-0 bg-white flex items-center gap-4 border-b pb-3  pt-6'>
                        <h3 className='font-medium text-base text-header-text'>Job post configuration</h3>
                        <EditIcon className='ml-auto cursor-pointer' fill='#292D32' width={17} height={17} onClick={openConfigModal} />
                    </header>


                    <div className='mb-2'>
                        <h3 className='font-medium text-header-text text-[0.9rem]'>Application Requirements</h3>

                        <div className='flex items-center gap-4 flex-wrap'>
                            {
                                requirement_custom_fields?.accept_personal_details && (
                                    <div
                                        className="flex items-center min-w-max peer bg-primary-light p-2 rounded-[0.35rem]"
                                    >
                                        <div
                                            className={cn(
                                                'grid place-content-center h-5 w-5 shrink-0 rounded-[0.35rem] cursor-pointer overflow-hidden border-[1.75px] border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground',
                                            )}
                                        >
                                            <span
                                                className={cn('grid place-content-center h-5 w-5 text-current ')}
                                            >
                                                <Check width={18} height={18} className='md:translate-x-[1%]' />
                                            </span>
                                        </div>
                                        <span className="ml-2 !text-[0.825rem] text-header-text">Accept Personal Details</span>
                                    </div>
                                )
                            }
                            {
                                requirement_custom_fields?.accept_resume && (
                                    <div
                                        className="flex items-center min-w-max peer bg-primary-light p-2 rounded-[0.35rem]"
                                    >
                                        <div
                                            className={cn(
                                                'grid place-content-center h-5 w-5 shrink-0 rounded-[0.35rem] cursor-pointer overflow-hidden border-[1.75px] border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground',
                                            )}
                                        >
                                            <span
                                                className={cn('grid place-content-center h-5 w-5 text-current ')}
                                            >
                                                <Check width={18} height={18} className='md:translate-x-[1%]' />
                                            </span>
                                        </div>
                                        <span className="ml-2 !text-[0.825rem] text-header-text">Accept Resume</span>
                                    </div>
                                )
                            }
                            {
                                requirement_custom_fields?.accept_cover_letter && (
                                    <div
                                        className="flex items-center min-w-max peer bg-primary-light p-2 rounded-[0.35rem]"
                                    >
                                        <div
                                            className={cn(
                                                'grid place-content-center h-5 w-5 shrink-0 rounded-[0.35rem] cursor-pointer overflow-hidden border-[1.75px] border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground',
                                            )}
                                        >
                                            <span
                                                className={cn('grid place-content-center h-5 w-5 text-current ')}
                                            >
                                                <Check width={18} height={18} className='md:translate-x-[1%]' />
                                            </span>
                                        </div>
                                        <span className="ml-2 !text-[0.825rem] text-header-text">Accept Cover Letter</span>
                                    </div>
                                )
                            }
                        </div>


                        {
                            requirement_custom_fields?.custom_fields.map((field, index) => {
                                const { fieldTitle, fieldValue, fieldType } = field
                                return (
                                    <div className='mt-8'>
                                        <h3 className='font-medium text-header-text text-[0.9rem]'>Custom Requirements</h3>
                                        <div key={`${index}${fieldTitle}${fieldValue}`} className='my-1'>
                                            <h3 className='text-header-text text-[0.85rem]'>{index + 1}. {fieldTitle}</h3>
                                            <p className='text-body-text text-[0.75rem] font-normal ml-2.5' dangerouslySetInnerHTML={{ __html: fieldValue || convertKebabAndSnakeToTitleCase(fieldType) }}></p>
                                        </div>
                                    </div>
                                )
                            })
                        }


                        {
                            team_member.length > 0 &&
                            <div className='mt-8'>
                                <h3 className='font-medium text-header-text text-[0.9rem] mb-1.5'>Team</h3>
                                <div className='flex flex-wrap gap-y-2 gap-x-4'>
                                    {
                                        team_member.map((member, index) => {
                                            const { company_email, profile_picture } = member
                                            return (
                                                <div className='flex items-center gap-1.5' key={`${index}${company_email}${profile_picture}`} >
                                                    <Avatar
                                                        src={profile_picture}
                                                        fallback={company_email}
                                                        alt={`${company_email} profile picture`}
                                                        size='small'
                                                    />
                                                    <p className='text-[0.8125rem]'>{company_email}</p>
                                                </div>
                                            )
                                        })
                                    }
                                </div>

                            </div>
                        }


                    </div>
                </article >




                {/* /////////////////////////////////////// */}
                {/* ///////////     MODALS     //////////// */}
                {/* /////////////////////////////////////// */}
                <EditJobBasicInfoModal
                    isOpen={isBasicInfoModalOpen}
                    close={closeBasicInfoModal}
                    job={job}
                    refetch={refetch}
                />
                <EditJobCompanyInfoModal
                    isOpen={isCompanyInfoModalOpen}
                    close={closeCompanyInfoModal}
                    job={job}
                    refetch={refetch}
                />
                <EditJobRequirementsModal
                    isOpen={isConfigModalOpen}
                    close={closeConfigModal}
                    job={job}
                    refetch={refetch}
                />
            </div >
        </>

    )
}

export default JobDetails