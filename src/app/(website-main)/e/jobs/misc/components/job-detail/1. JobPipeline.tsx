import React, { Suspense, useEffect, useRef, useState } from 'react';
import { Spinner } from '@/components/shared/icons';
import { cn } from '@/utils';
import { useGetJobPipelineDetails, useMoveCandidate } from '../../api';
import { MoveLeft } from '../../icons';
import { customRequirement, PipelineCandidate } from '../../types';
import JobPipelineStage from './2. JobPipelineStage';

interface PipelineProps {
  id: number;
  pipeline_id: number;
  unique_id: string;
  job_custom_requirements: customRequirement[];
}

const JobPipeline: React.FC<PipelineProps> = ({
  id,
  unique_id,
  pipeline_id,
  job_custom_requirements,
}) => {
  const [filterUrl, setFilterUrl] = useState('');
  const {
    data: pipelineData,
    isLoading: isLoadingPipelineData,
    refetch: refetchPipelineData,
    isFetching: isFetchingPipelineData,
  } = useGetJobPipelineDetails({ id, filterUrl });
  const { mutate: moveCandidate } = useMoveCandidate(id);


  const [allApplicants, setAllApplicants] = useState<PipelineCandidate[]>(
    pipelineData?.result_data
      .map(stage => stage.data.map(cand => ({ ...cand, current_stage: stage })))
      .flat() || ([] as PipelineCandidate[])
  );

  const calculateApplicants = React.useCallback(() => {
    if (pipelineData && !isLoadingPipelineData && !isFetchingPipelineData) {
      setAllApplicants(
        pipelineData?.result_data
          .map(stage =>
            stage.data.map(cand => ({ ...cand, current_stage: stage }))
          )
          .flat() || ([] as PipelineCandidate[])
      );
    }
  }, [pipelineData, isLoadingPipelineData, isFetchingPipelineData]);

  React.useEffect(() => {
    calculateApplicants();
  }, [pipelineData, isLoadingPipelineData, isFetchingPipelineData]);

  // console.log(allApplicants, "all applicants")

  const [cvRoles, setCVRoles] = useState<string[]>(
    pipelineData?.unique_roles || []
  );
  const [uniqueSkills, setUniqueSkills] = useState<string[]>(
    pipelineData?.unique_skills || []
  );
  const [uniqueTools, setUniqueTools] = useState<string[]>(
    pipelineData?.unique_tools || []
  );
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [showMoveLeft, setShowMoveLeft] = useState(false);
  const [showMoveRight, setShowMoveRight] = useState(false);

  useEffect(() => {
    if (filterUrl === '') {
      refetchPipelineData();
    } else {
      refetchPipelineData();
    }
  }, [filterUrl, refetchPipelineData]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const showLeft = container.scrollLeft > 0;
      const showRight =
        container.scrollLeft + container.clientWidth < container.scrollWidth;
      setShowMoveLeft(showLeft);
      setShowMoveRight(showRight);
    };

    container.addEventListener('scroll', handleScroll);
    handleScroll();
    return () => {
      container.removeEventListener('scroll', handleScroll);
    };
  }, [containerRef.current?.scrollWidth, containerRef.current?.clientWidth]);

  const MoveToLeft = () => {
    if (containerRef.current) {
      containerRef.current.scrollLeft -= 500;
    }
  };

  const MoveToRight = () => {
    if (containerRef.current) {
      containerRef.current.scrollLeft += 500;
    }
  };

  React.useEffect(() => {
    if (pipelineData && !isLoadingPipelineData) {
      setCVRoles(pipelineData.unique_roles);
      setUniqueSkills(pipelineData.unique_skills);
      setUniqueTools(pipelineData.unique_tools);
    }
  }, [pipelineData, isLoadingPipelineData]);

  return (
    <div className="relative h-full max-h-full w-full overflow-hidden">
      {isLoadingPipelineData && (
        <div className="relative flex h-full w-full items-center justify-center">
          <Spinner />
        </div>
      )}

      {allApplicants && (
        <Suspense fallback={<Spinner />} key={'JOB_PIPELINE_SUSPENSE_BOUNDARY'}>
          <div
            className="relative flex h-full w-full gap-3 overflow-x-scroll"
            ref={containerRef}
          >
            <div className={cn('overflow-hidden rounded-full')}>
              <div
                className={cn(
                  'absolute top-0 h-1 w-full overflow-hidden bg-[#F8F9FB]',
                  isFetchingPipelineData &&
                    !isLoadingPipelineData &&
                    'bg-main-solid/20'
                )}
              >
                <div
                  className={cn(
                    'bg-main-solid h-full w-full origin-[0_50%] animate-indeterminate-progress rounded-full opacity-0 transition-opacity',
                    isFetchingPipelineData &&
                      !isLoadingPipelineData &&
                      'opacity-100'
                  )}
                ></div>
              </div>
            </div>

            {pipelineData?.result_data.map(stage => {
              return (
                <JobPipelineStage
                  allStages={pipelineData.result_data}
                  column={stage.stage_id}
                  thisStage={stage}
                  key={stage.stage_id}
                  applicants={allApplicants}
                  pipelineId={pipeline_id}
                  refetchPipelineData={refetchPipelineData}
                  setApplicants={setAllApplicants}
                  jobUniqueId={unique_id}
                  jobId={id}
                  cvRoles={cvRoles}
                  uniqueSkills={uniqueSkills}
                  uniqueTools={uniqueTools}
                  jobCustomRequirements={job_custom_requirements}
                  filterUrl=""
                  setFilterUrl={setFilterUrl}
                />
              );
            })}
          </div>
        </Suspense>
      )}

      <aside
        className={cn(
          'absolute bottom-0 right-0 flex items-center gap-4 p-6',
          !showMoveLeft && !showMoveRight && ''
        )}
      >
        <button
          className="hover:bg-main-bg border-main-solid block aspect-square rounded-full border bg-white p-2.5 disabled:opacity-30"
          disabled={!showMoveLeft}
          onClick={MoveToLeft}
        >
          <MoveLeft className=" scale-90" />
        </button>
        <button
          className="hover:bg-main-bg border-main-solid block aspect-square rounded-full border bg-white p-2.5 disabled:opacity-30"
          disabled={!showMoveRight}
          onClick={MoveToRight}
        >
          <MoveLeft className="rotate-180 scale-90" />
        </button>
      </aside>
    </div>
  );
};

export default JobPipeline;
