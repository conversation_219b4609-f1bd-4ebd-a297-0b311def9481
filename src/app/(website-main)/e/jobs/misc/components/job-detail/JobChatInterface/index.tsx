'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';
import { SmallSpinner } from '@/components/shared/icons';
import { cn } from '@/utils';
import { useGetJobCandidates } from '../../../api';
import CandidateDetailsChatTab from '../5.5 JobPipelineCandidateDetailsChatTab';
import {
  ApplicantWithChat,
  useFetchApplicantsWithChat,
} from '../5.5 JobPipelineCandidateDetailsChatTab/api';

interface Props {
  unique_id: string;
}

const JobChatInterface: React.FC<Props> = ({ unique_id }) => {
  const [selectedApplicant, setSelectedApplicant] =
    useState<ApplicantWithChat | null>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const currentApplicantRef = useRef<ApplicantWithChat | null>(null);

  // Fetch job candidates first
  const {
    data: candidatesData,
    isLoading: isLoadingCandidates,
    isError: candidatesError,
  } = useGetJobCandidates({
    unique_id,
    pageIndex: 0,
    pageSize: 100, // Get a large number to check all candidates
  });

  // Fetch applicants with chat data
  const {
    data: applicantsWithChat,
    isLoading: isLoadingChats,
    isError: chatError,
  } = useFetchApplicantsWithChat({
    unique_id,
    candidates: candidatesData?.results,
    enabled: !!unique_id && !!candidatesData?.results,
  });

  // Auto-select first applicant if available
  useEffect(() => {
    if (
      applicantsWithChat &&
      applicantsWithChat.length > 0 &&
      !selectedApplicant
    ) {
      setSelectedApplicant(applicantsWithChat[0]);
      currentApplicantRef.current = applicantsWithChat[0];
    }
  }, [applicantsWithChat, selectedApplicant]);

  // Handle applicant switching with proper WebSocket cleanup
  const handleApplicantSwitch = useCallback(
    async (newApplicant: ApplicantWithChat) => {
      // Don't switch if it's the same applicant
      if (selectedApplicant?.id === newApplicant.id) {
        return;
      }

      setIsTransitioning(true);

      try {
        // If there's a current applicant, we need to clean up the WebSocket connection
        if (selectedApplicant) {
          console.log(
            'Switching from applicant:',
            selectedApplicant.email,
            'to:',
            newApplicant.email
          );

          // Clear the selected applicant first to trigger cleanup in CandidateDetailsChatTab
          setSelectedApplicant(null);

          // Wait a short time for the WebSocket cleanup to complete
          // The CandidateDetailsChatTab component's useEffect cleanup will handle disconnection
          await new Promise(resolve => setTimeout(resolve, 300));
        }

        // Now set the new applicant
        setSelectedApplicant(newApplicant);
        currentApplicantRef.current = newApplicant;
      } catch (error) {
        console.error('Error during applicant switch:', error);
        // Fallback: still set the new applicant even if cleanup had issues
        setSelectedApplicant(newApplicant);
        currentApplicantRef.current = newApplicant;
      } finally {
        setIsTransitioning(false);
      }
    },
    [selectedApplicant]
  );

  // Handle clearing selected applicant with proper cleanup (for mobile back button)
  const handleClearSelection = useCallback(async () => {
    if (!selectedApplicant) return;

    setIsTransitioning(true);

    try {
      console.log('Clearing selected applicant:', selectedApplicant.email);

      // Clear the selected applicant to trigger cleanup in CandidateDetailsChatTab
      setSelectedApplicant(null);
      currentApplicantRef.current = null;

      // Wait a short time for the WebSocket cleanup to complete
      await new Promise(resolve => setTimeout(resolve, 300));
    } catch (error) {
      console.error('Error during applicant clear:', error);
      // Fallback: still clear the selection
      setSelectedApplicant(null);
      currentApplicantRef.current = null;
    } finally {
      setIsTransitioning(false);
    }
  }, [selectedApplicant]);

  if (isLoadingCandidates || isLoadingChats) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <div className="flex items-center gap-2">
          <SmallSpinner />
          <span className="text-sm text-gray-600">
            {isLoadingCandidates
              ? 'Loading candidates...'
              : 'Loading chat conversations...'}
          </span>
        </div>
      </div>
    );
  }

  if (candidatesError || chatError) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <p className="text-sm text-red-500">
          Error loading data. Please try again.
        </p>
      </div>
    );
  }

  if (applicantsWithChat.length === 0) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <div className="text-center">
          <div className="mb-4 text-4xl">💬</div>
          <h3 className="mb-2 text-lg font-medium text-gray-900">
            No Chat Conversations
          </h3>
          <p className="text-sm text-gray-600">
            No applicants have started chat conversations for this job yet.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full w-full overflow-hidden bg-white">
      {/* Conversation List Sidebar */}
      <div
        className={cn(
          'w-80 border-r border-gray-200 bg-gray-50 max-md:w-64 max-lg:w-72',
          'sm:block', // Always show on desktop
          selectedApplicant ? 'max-sm:hidden' : 'max-sm:block' // Hide on mobile when chat is selected
        )}
      >
        <div className="border-b border-gray-200 bg-white p-4">
          <h2 className="text-lg font-semibold text-gray-900">
            Chat Conversations ({applicantsWithChat.length})
          </h2>
        </div>

        <div className="h-[90%] overflow-y-auto pb-2">
          {applicantsWithChat.map(applicant => (
            <div
              key={applicant.id}
              onClick={() => handleApplicantSwitch(applicant)}
              className={cn(
                'relative cursor-pointer border-b border-gray-100 p-4 transition-colors hover:bg-white',
                selectedApplicant?.id === applicant.id
                  ? 'border-blue-200 bg-blue-50'
                  : 'bg-gray-50',
                isTransitioning && 'pointer-events-none opacity-75'
              )}
            >
              {/* Transition loading overlay */}
              {isTransitioning && (
                <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75">
                  <SmallSpinner />
                </div>
              )}
              <div className="flex items-start justify-between">
                <div className="min-w-0 flex-1">
                  <div className="flex items-center gap-2">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-500 text-sm font-medium text-white">
                      {applicant.name?.charAt(0)?.toUpperCase()}
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="truncate text-sm font-medium text-gray-900">
                        {applicant.name}
                      </p>
                      <p className="truncate text-xs text-gray-500">
                        {applicant.email}
                      </p>
                    </div>
                  </div>

                  {applicant.lastMessage && (
                    <p className="mt-2 line-clamp-2 text-xs text-gray-600">
                      {applicant.lastMessage}
                    </p>
                  )}

                  <div className="mt-1 flex items-center justify-between">
                    {applicant.lastMessageTime && (
                      <p className="text-xs text-gray-400">
                        {new Date(
                          applicant.lastMessageTime
                        ).toLocaleDateString()}
                      </p>
                    )}
                    <p className="text-xs text-gray-500">
                      {applicant.totalMessages} messages
                    </p>
                  </div>
                </div>

                <div className="ml-2 flex flex-col items-end gap-1">
                  {applicant.cvProcessed && (
                    <div className="text-xs font-medium text-green-600">
                      CV ✓
                    </div>
                  )}
                  {applicant.cvScore && (
                    <div className="text-xs text-blue-600">
                      {Math.round(applicant.cvScore)}%
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
          {applicantsWithChat.map(applicant => (
            <div
              key={applicant.id}
              onClick={() => handleApplicantSwitch(applicant)}
              className={cn(
                'relative cursor-pointer border-b border-gray-100 p-4 transition-colors hover:bg-white',
                selectedApplicant?.id === applicant.id
                  ? 'border-blue-200 bg-blue-50'
                  : 'bg-gray-50',
                isTransitioning && 'pointer-events-none opacity-75'
              )}
            >
              {/* Transition loading overlay */}
              {isTransitioning && (
                <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75">
                  <SmallSpinner />
                </div>
              )}
              <div className="flex items-start justify-between">
                <div className="min-w-0 flex-1">
                  <div className="flex items-center gap-2">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-500 text-sm font-medium text-white">
                      {applicant.name?.charAt(0)?.toUpperCase()}
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="truncate text-sm font-medium text-gray-900">
                        {applicant.name}
                      </p>
                      <p className="truncate text-xs text-gray-500">
                        {applicant.email}
                      </p>
                    </div>
                  </div>

                  {applicant.lastMessage && (
                    <p className="mt-2 line-clamp-2 text-xs text-gray-600">
                      {applicant.lastMessage}
                    </p>
                  )}

                  <div className="mt-1 flex items-center justify-between">
                    {applicant.lastMessageTime && (
                      <p className="text-xs text-gray-400">
                        {new Date(
                          applicant.lastMessageTime
                        ).toLocaleDateString()}
                      </p>
                    )}
                    <p className="text-xs text-gray-500">
                      {applicant.totalMessages} messages
                    </p>
                  </div>
                </div>

                <div className="ml-2 flex flex-col items-end gap-1">
                  {applicant.cvProcessed && (
                    <div className="text-xs font-medium text-green-600">
                      CV ✓
                    </div>
                  )}
                  {applicant.cvScore && (
                    <div className="text-xs text-blue-600">
                      {Math.round(applicant.cvScore)}%
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Chat Interface */}
      <div
        className={cn(
          'flex flex-1 flex-col',
          selectedApplicant ? 'max-sm:block' : 'max-sm:hidden'
        )}
      >
        {selectedApplicant ? (
          <>
            {/* Chat Header */}
            <div className="border-b border-gray-200 bg-white p-4">
              <div className="flex items-center gap-3">
                {/* Back button for mobile */}
                <button
                  onClick={handleClearSelection}
                  className="-ml-2 rounded-lg p-2 hover:bg-gray-100 sm:hidden"
                >
                  <svg
                    className="h-5 w-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                </button>

                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-500 font-medium text-white">
                  {selectedApplicant.name?.charAt(0)?.toUpperCase()}
                </div>
                <div className="min-w-0 flex-1">
                  <h3 className="truncate text-lg font-semibold text-gray-900">
                    {selectedApplicant.name}
                  </h3>
                  <p className="truncate text-sm text-gray-600">
                    {selectedApplicant.email}
                  </p>
                </div>

                {/* Chat status indicators */}
                <div className="flex flex-col items-end gap-1 text-xs max-sm:hidden">
                  {selectedApplicant.cvProcessed && (
                    <span className="font-medium text-green-600">CV ✓</span>
                  )}
                  {selectedApplicant.cvScore && (
                    <span className="text-blue-600">
                      {Math.round(selectedApplicant.cvScore)}%
                    </span>
                  )}
                  <span className="text-gray-500">
                    {selectedApplicant.totalMessages} msgs
                  </span>
                </div>
              </div>
            </div>

            {/* Chat Messages */}
            <div className="flex-1 overflow-hidden">
              <CandidateDetailsChatTab
                unique_id={unique_id}
                applicant_email={selectedApplicant.email}
                enabled={true}
              />
            </div>
          </>
        ) : (
          <div className="flex flex-1 items-center justify-center">
            <div className="text-center">
              <div className="mb-4 text-4xl">💬</div>
              <h3 className="mb-2 text-lg font-medium text-gray-900">
                Select a Conversation
              </h3>
              <p className="text-sm text-gray-600">
                Choose an applicant from the list to view their chat
                conversation.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default JobChatInterface;
