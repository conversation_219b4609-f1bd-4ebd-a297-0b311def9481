'use client'
import Image from 'next/image'
import { AxiosError } from "axios";
import { useForm } from "react-hook-form";
import { useReducer, useEffect, useCallback, useState } from 'react'

import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Underline from '@tiptap/extension-underline'
import TextAlign from '@tiptap/extension-text-align'
import BulletList from '@tiptap/extension-bullet-list'
import { default as TipTapLink } from '@tiptap/extension-link'
import ListItem from '@tiptap/extension-list-item'
import OrderedList from '@tiptap/extension-ordered-list'
import { Bold, Italic, Underline as UnderlineIcon, List, AlignLeft, AlignCenter, AlignRight, Upload, Strikethrough, ListOrdered, LinkIcon, Unlink, UploadIcon, Eye, } from 'lucide-react'
import { Badge, Button, CardHeader, FileUpload, Input, LoaderBtn, Separator } from "@/components/shared"
import { Card, CardContent } from "@/components/shared"
import { Label } from "@/components/shared"
import { ToggleGroup, ToggleGroupItem } from "@/components/shared"
// import { Separator } from "@/components/ui/separator"
import { cn } from '@/utils'
import { useCloudinary, useErrorModalState } from "@/hooks";
import { zodResolver } from "@hookform/resolvers/zod";
import { singleStageSchema } from "../../../utils/pipelineSchemas";
import { formatAxiosErrorMessage } from "@/utils/errors";
import FormError from '@/components/shared/form-error'
import { useLoading } from '@/lib/contexts/LoadingContext'

import { useEditPipelineStage } from "../../../api";
import { EditSinglePipelineStageType, PipelineColumn, Stage } from "../../../types/PipelineType";
import { addContentToTemplate, convertPipelineColumnToStage, extractContentFromTemplate } from '../../../utils/functions'
import { DocumentUpload } from 'iconsax-react';
import { z } from 'zod';
import toast from 'react-hot-toast';

interface Props {
    stage: PipelineColumn;
    pipelineId: string | number;
    closeStagesModal: () => void

}

const defaultContent = `
                 <div contenteditable="true" translate="no" class="tiptap ProseMirror" role="textbox" aria-label="Rich-Text Editor" tabindex="0">
                <p>Hello {{ candidate_firstname }},</p>
                <p> I am pleased to inform you that after a thorough review of your qualifications and interview performance, we are delighted to extend an offer for you to join {{ company_name }} in the role of {{ job_title }}. </p>
                <p>As part of our design team, you will play a crucial role in shaping our product strategy and contributing to the ongoing success of our organization. Below, you will find the key terms of your offer: </p>
                <ul>
                <li><p>Position: {{ job_title }} </p></li>
                <li><p> Salary: {{ salary }}</p></li>
                    <li>
                        <p>
                            Employment Type: {{ employment_type }}
                        </p>
                    </li>
                    <li>
                        <p>
                            Benefits: {{ benefits }}
                        </p>
                    </li>
                </ul>
                <p>Your formal offer letter detailing the full terms and conditions of your employment is attached to this email. We kindly ask that you review the document carefully. Should you have any questions or require clarification, please feel free to contact us. We would appreciate receiving your response by {Insert Response Deadline}.
                </p>
                <p>
                    <br class="ProseMirror-trailingBreak">
                </p>
                <p>To confirm your acceptance of the offer, please sign and return the attached offer letter at your earliest convenience. We believe that your talent and creativity will be a valuable addition to our team, and we look forward to the contributions you will make at {Company}. Thank you once again for your interest in joining us. We are excited about the possibility of welcoming you to our company and working together toward future successes.
                </p>
                <p>
                    <br class="ProseMirror-trailingBreak">
                </p>
                <p>
                    Yours sincerely,
                </p>
                <p>{{ recruiter_full_name }}</p><p>{{ job_title }}
                </p>
                <p>
                    {{ company_name }}
                </p>
                <p>
                    {{ company_support_email }}
                </p>
                <p>
                    {{ company_address }}
                </p>
            </div>
            `

export const offerSchema = z.object({
    id: z.union([z.string(), z.number()]).optional(),

    offer_email_subject: z.string().optional(),
    offer_email_body: z.string().optional(),
    offer_cover_image: z.string().optional(),
    offer_attachment_url: z.string().optional(),
})
export type OfferShemaType = z.infer<typeof offerSchema>;


export default function Component({ stage, pipelineId, closeStagesModal }: Props) {
    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();
    const { stage_id, stage_name, offer_letter_send_automatic, offer_email_subject, offer_attachment_url, offer_cover_image, offer_email_body } = stage
    const { register, formState: { errors }, setValue, handleSubmit, reset, watch, control, clearErrors } = useForm({
        defaultValues: {
            id: stage_id,
            offer_email_subject: offer_email_subject || "",
            offer_email_body: !!offer_email_body ? extractContentFromTemplate(offer_email_body) : defaultContent
        },
        resolver: zodResolver(offerSchema),
    });
    const closeModal = () => {
        reset({
            id: stage_id,
            offer_email_subject: offer_email_subject || "",
            offer_email_body: !!offer_email_body ? extractContentFromTemplate(offer_email_body) : defaultContent
        })
        closeStagesModal()
    }



    const getLinkName = (link: string) => {
        const url = new URL(link)
        return url.pathname.split('/').pop()
    }

    const { mutate: editStage, isLoading: isStageEditing } = useEditPipelineStage();
    const { uploadToServer } = useCloudinary()
    const { isUploading } = useLoading()

    const submitForm = async (data: OfferShemaType) => {
        // let offer_cover_image: string | undefined;
        // let attachment_url: string | undefined;

        const prevStage = convertPipelineColumnToStage(stage)

        const newStage: Stage = {
            ...prevStage,
            offer_email_subject: data.offer_email_subject,
            offer_email_body: addContentToTemplate(data.offer_email_body || "<p></p>", offer_cover_image),
        }

        console.log("SUBMITTING")
        if (selectedcoverImage) {
            console.log("BEGIN UPLOADING")
            const res = await uploadToServer(selectedcoverImage)

          console.log(res, "SELECTED IMAGE RES")
            newStage.offer_cover_image = res.secure_url
        }
        if (selectedAttachment) {
            const res = await uploadToServer(selectedAttachment)
            newStage.offer_attachment_url = res.secure_url
        }

        console.log(newStage, "NEW STAGE BEFORE SUBMISSIPN")
        editStage(
            { pipelineId, stageId: Number(stage_id), newStage },
            {
                onSuccess() {
                    toast.success("Offer letter updated");
                    closeModal()
                },
                onError(error: any) {
                    const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                    openErrorModalWithMessage(error?.response?.data || errorMessage || "Something went wrong");
                },
            }
        )

    }

    const [selectedcoverImage, setSelectedCoverImage] = useState<File | null>(null)
    const [selectedAttachment, setSelectedAttachment] = useState<File | null>(null);



    const editor = useEditor({
        extensions: [
            StarterKit.configure({
                bulletList: false,
                orderedList: false,
                listItem: false,
            }),
            Underline,
            TextAlign.configure({
                types: ['heading', 'paragraph'],
            }),
            BulletList,
            OrderedList,
            ListItem,
            TipTapLink.configure({
                openOnClick: false,
                autolink: true,
                defaultProtocol: 'https',
            }),
        ],

        content: watch('offer_email_body'),
        onUpdate: ({ editor }) => {
            setValue('offer_email_body', editor.getHTML())
        },
    })

    const tokens = [
        { title: 'First name', value: 'candidate_firstname' },
        { title: 'Last name', value: 'candidate_lastname' },
        { title: 'Position', value: 'position' },
        { title: 'Salary', value: 'salary' },
        { title: 'Employment Type', value: 'employment_type' },
        { title: 'Recruiter\'s Job Title', value: 'recruiter_job_title' },
        // { title: 'Start date', value: 'start_date' },
        // { title: 'Start date', value: 'start_date' },
    ]

    const insertToken = (token: string) => {
        if (editor) {
            editor.commands.insertContent(`{{ ${token} }}`)
        }
    }

    const [, forceUpdate] = useReducer((x) => x + 1, 0)

    useEffect(() => {
        if (editor) {
            editor.on('selectionUpdate', () => {
                // Force a re-render to update the toggle states
                forceUpdate()
            })
        }
    }, [editor])



    const setLink = useCallback(() => {
        if (editor) {
            const previousUrl = editor.getAttributes('link').href
            const url = window.prompt('URL', previousUrl)

            if (url === null) {
                return
            }

            if (url === '') {
                editor.chain().focus().extendMarkRange('link').unsetLink()
                    .run()
                return
            }

            editor.chain().focus().extendMarkRange('link').setLink({ href: url })
                .run()
        }
    }, [editor])


    if (!editor) {
        return null
    }
    const offerEmailSubject = watch('offer_email_subject')

    return (
        <>
            <div className="flex flex-col relative w-[90vw] max-w-[1200px] p- h-full overflow-hidden bg-[#F8F9FB] ">
                <form id="offer-letter-form" className="grid grid-cols-2 w-full p-4 gap-4 grow overflow-y-scroll" onSubmit={handleSubmit(submitForm)}>

                    <Card className="border-none">
                        <CardHeader className="text-lg 2xl:text-xl">
                            Edit Offer Letter
                        </CardHeader>
                        <CardContent className="">
                            <div className="space-y-6">
                                <Input
                                    className="border rounded-xl p-4 border-[helper-text] w-full"
                                    id="email-subject" type="text"
                                    label="Email Subject"
                                    labelClass="text-black"
                                    placeholder="Enter email subject here"
                                    // onChange={(e) => setSubject(e.target.value)}
                                    required
                                    {...register('offer_email_subject')}
                                    hasError={!!errors?.offer_email_subject}
                                    errorMessage={errors?.offer_email_subject?.message}
                                />


                                <FileUpload
                                    label="COVER IMAGE"
                                    accept={{ 'image/*': [] }}
                                    maxSizeMB={15}
                                    dragActiveClassName="bg-green-50 border-green-500"
                                    containerClassName="w-full max-w-2xl"
                                    hint="Files types: PNG, JPG, Max size: 10MB"
                                    value={selectedcoverImage}
                                    onFileSelect={setSelectedCoverImage}
                                    onFileRemove={() => setSelectedCoverImage(null)}
                                />



                                {/* Token Buttons */}
                                <div className="text-body-text font-normal text-[0.85rem]">
                                    <Label className="mt-4 mb-3 text-body-text">To customize this email, drag the placeholder options displayed below
                                        and place them where you will like to have them.
                                    </Label>
                                    <div className="flex flex-wrap gap-2 border border-[#E4E4E4] p-4 rounded-xl">
                                        {tokens.map((token) => (
                                            <Badge
                                                key={token.value}
                                                variant="light"
                                                shape="rounded"
                                                className='cursor-pointer border-none font-normal'
                                                size="lg"
                                                onClick={() => insertToken(token.value)}
                                            >
                                                {token.title}
                                            </Badge>
                                        ))}
                                    </div>
                                </div>


                                <article className="border-[0.7px] border-[#E4E4E4] rounded-xl">
                                    <header>
                                        <ToggleGroup type="multiple" className="justify-start flex-wrap border-b">
                                            <ToggleGroupItem
                                                value="bold"
                                                aria-label="Toggle bold"
                                                onClick={() => editor.chain().focus().toggleBold().run()}
                                                data-state={editor.isActive('bold') ? 'on' : 'off'}
                                            >
                                                <Bold className="h-4 w-4" />
                                            </ToggleGroupItem>
                                            <ToggleGroupItem
                                                value="italic"
                                                aria-label="Toggle italic"
                                                onClick={() => editor.chain().focus().toggleItalic().run()}
                                                data-state={editor.isActive('italic') ? 'on' : 'off'}
                                            >
                                                <Italic className="h-4 w-4" />
                                            </ToggleGroupItem>
                                            <ToggleGroupItem
                                                value="italic"
                                                aria-label="Toggle italic"
                                                onClick={() => editor.chain().focus().toggleStrike().run()}
                                                data-state={editor.isActive('italic') ? 'on' : 'off'}
                                            >
                                                <Strikethrough className="h-4 w-4" />
                                            </ToggleGroupItem>
                                            <ToggleGroupItem
                                                value="underline"
                                                aria-label="Toggle underline"
                                                onClick={() => editor.chain().focus().toggleUnderline().run()}
                                                data-state={editor.isActive('underline') ? 'on' : 'off'}
                                            >
                                                <UnderlineIcon className="h-4 w-4" />
                                            </ToggleGroupItem>

                                            <Separator orientation="vertical" className="mx-1 h-8" />

                                            <ToggleGroupItem
                                                value="left"
                                                aria-label="Align left"
                                                onClick={() => editor.chain().focus().setTextAlign('left').run()}
                                                data-state={editor.isActive({ textAlign: 'left' }) ? 'on' : 'off'}
                                            >
                                                <AlignLeft className="h-4 w-4" />
                                            </ToggleGroupItem>
                                            <ToggleGroupItem
                                                value="center"
                                                aria-label="Align center"
                                                onClick={() => editor.chain().focus().setTextAlign('center').run()}
                                                data-state={editor.isActive({ textAlign: 'center' }) ? 'on' : 'off'}
                                            >
                                                <AlignCenter className="h-4 w-4" />
                                            </ToggleGroupItem>
                                            <ToggleGroupItem
                                                value="right"
                                                aria-label="Align right"
                                                onClick={() => editor.chain().focus().setTextAlign('right').run()}
                                                data-state={editor.isActive({ textAlign: 'right' }) ? 'on' : 'off'}
                                            >
                                                <AlignRight className="h-4 w-4" />
                                            </ToggleGroupItem>

                                            <Separator orientation="vertical" className="mx-1 h-8" />

                                            <ToggleGroupItem
                                                value="bulletList"
                                                aria-label="Toggle bullet list"
                                                onClick={() => editor.chain().focus().toggleBulletList().run()}
                                                data-state={editor.isActive('bulletList') ? 'on' : 'off'}
                                            >
                                                <List className="h-4 w-4" />
                                            </ToggleGroupItem>
                                            <ToggleGroupItem
                                                value="orderedList"
                                                aria-label="Toggle ordered list"
                                                onClick={() => editor.chain().focus().toggleOrderedList().run()}
                                                data-state={editor.isActive('orderedList') ? 'on' : 'off'}
                                            >
                                                <ListOrdered className="h-4 w-4" />
                                            </ToggleGroupItem>

                                            <Separator orientation="vertical" className="mx-1 h-8" />

                                            <ToggleGroupItem
                                                value="bulletList"
                                                aria-label="Toggle bullet list"
                                                onClick={setLink}
                                                data-state={editor.isActive('link') ? 'on' : 'off'}
                                            >
                                                <LinkIcon className="h-4 w-4" />
                                            </ToggleGroupItem>
                                            <ToggleGroupItem
                                                value="orderedList"
                                                aria-label="Toggle ordered list"
                                                onClick={() => editor.chain().focus().unsetLink().run()}
                                                disabled={!editor.isActive('link')}
                                            >
                                                <Unlink className="h-4 w-4" />
                                            </ToggleGroupItem>
                                        </ToggleGroup>
                                    </header>


                                    <section className="min-h-[500px]  p-4">
                                        <EditorContent editor={editor} className="prose max-w-none" />
                                    </section>
                                    {
                                        !!errors?.offer_email_body && <FormError errorMessage={errors?.offer_email_body.message} />
                                    }
                                </article>

                                <FileUpload
                                    label="Attachment: Offer Letter (PDF)"
                                    accept={{ 'application/pdf': [] }}
                                    maxSizeMB={15}
                                    dragActiveClassName="bg-green-50 border-green-500"
                                    containerClassName="w-full max-w-2xl"
                                    value={selectedAttachment}
                                    onFileSelect={setSelectedAttachment}
                                    onFileRemove={() => setSelectedAttachment(null)}
                                />

                            </div>
                        </CardContent>
                    </Card>


                    <Card>
                        <CardHeader className="text-lg 2xl:text-xl">
                            Preview
                        </CardHeader>
                        <CardContent className="p-6 pt-2">
                            {offerEmailSubject.trim() != "" &&
                                <div className="text-body-text font-normal text-[0.85rem]  mb-5 gap-2">
                                    <p className="text-body-text">EMAIL SUBJECT
                                    </p>
                                    <p className=" font-medium text-lg text-black">{offerEmailSubject}</p>
                                </div>
                            }
                            {
                                (selectedcoverImage || offer_cover_image != null) &&
                                <div className="relative inline-block rounded-[0.975rem] h-[7.2rem] w-full">
                                    <Image
                                        src={selectedcoverImage ? URL.createObjectURL(selectedcoverImage) : offer_cover_image || ""}
                                        alt="Cover Image Preview"
                                        fill
                                        objectFit='cover'
                                        className={cn("rounded-lg border border-gray-200")}
                                    />
                                </div>

                            }
                            <div className="tiptap max-w-none">
                                <div
                                    dangerouslySetInnerHTML={{ __html: editor.getHTML() }}
                                    className="tiptap"
                                />
                            </div>
                            {
                                (selectedAttachment || offer_attachment_url != null) && <>
                                    <hr className="my-4" />
                                    <div className="relative inline-block rounded-[0.975rem] h-[7.2rem] w-full ">
                                        <label className="text-sm font-medium text-upppercase mb-3">
                                            Attachment: Offer Letter (PDF)
                                        </label>
                                        <div className="rounded-lg bg-[#F1EFFC] p-2 px-3 flex items-center gap-2 flex justify-between">
                                            <div className="flex items-center gap-2">
                                                <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center">
                                                    <DocumentUpload className='text-primary' />
                                                </div>
                                                <p className=" text-md font-medium mb-0 ">
                                                    {selectedAttachment ? selectedAttachment.name : getLinkName(offer_attachment_url || "")}</p>
                                            </div>
                                            {/* {offer_attachment_url && <a href={offer_attachment_url} target="_blank" rel="noopener noreferrer" className=' bg-primary text-white rounded-lg hover:bg-primary/80 p-2 py-1'>
                                                <Eye />
                                            </a>
                                            } */}
                                        </div>
                                    </div>
                                </>
                            }
                        </CardContent>
                    </Card>

                </form>

            </div>
            <div className="sticky bottom-0 flex gap-4 items-center justify-end p-4 bg-white">
                <button type="button" onClick={close} className="btn-primary-light"> Cancel </button>
                <button form="offer-letter-form" type="submit" className="btn-primary items-center gap-2" > Save

                    {
                        (isUploading || isStageEditing) && <LoaderBtn color="white" />
                    }
                </button>
            </div>
        </>

    )
}