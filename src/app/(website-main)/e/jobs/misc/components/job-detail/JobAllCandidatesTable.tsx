import {
  ColumnDef,
  PaginationState,
  SortingFn,
  SortingState,
} from '@tanstack/react-table';
import React, { ReactNode, useEffect, useState } from 'react';
import {
  Avatar,
  Button,
  DataTable,
  LoaderBtn,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/shared';
import { Elipsis } from '@/components/shared/icons';
import { useBooleanStateControl } from '@/hooks';
import { cn } from '@/utils';
import { addCommasToNumber } from '@/utils/numbers';
import { useGetJobCandidates } from '../../api';
import { CTableCandidate, PipelineCandidate } from '../../types';
import CandidateDetailsDrawer from './5. JobPipelineCandidateDetails';
import useRemoveCandidate from '../../api/removeCandidate';
import toast from 'react-hot-toast';
import { formatAxiosErrorMessage } from '@/utils/errors';

interface Props {
  unique_id: string;
}
interface CandidateTable {
  id: number;
  name: string | null;
  email: string;
  phone_number: string | null;
  percentage_match: number | null;
  stage: string | null;
  action: CTableCandidate;
}

const JobCandidatesTable: React.FC<Props> = ({ unique_id }) => {
  const {
    state: isDrawerOpen,
    setTrue: openDrawer,
    setFalse: closeDrawer,
  } = useBooleanStateControl(false);

  const [{ pageIndex, pageSize }, setPagination] =
    React.useState<PaginationState>({
      pageIndex: 0,
      pageSize: 10,
    });
  const fetchOptions = { unique_id, pageIndex, pageSize };
  const {
    data: allCandidates,
    isLoading: isLoadingCandidates,
    isFetching: isFetchingCandidates,
    refetch,
  } = useGetJobCandidates(fetchOptions);
  useEffect(() => {
    refetch();
  }, [pageIndex, pageSize]);

  const { count } = allCandidates || {};

  const pageCount = React.useMemo(
    () => (!!count ? Math.ceil(count / pageSize) : -1),
    [count, pageSize]
  );

  const {
    isLoading: isRemovingCandidate,
    mutate: removeCandidate,
  } = useRemoveCandidate();

  const [isDeletingUser, setIsDeletingUser] = useState<number>(0)

  const handleApplicationRemoval = (applicantId: number) => {
    console.log(applicantId, "APPLICANT ID")
    if (applicantId) {
      setIsDeletingUser(applicantId)
      removeCandidate(applicantId, {
        onSuccess: (response) => {
          console.log(response, "DELETE RESPONSE")
          toast.success('Candidate removed successfully')
          refetch();
        },
        onError: (err: any) => {
          const errMessage = formatAxiosErrorMessage(err)
          toast.error(errMessage || 'Failed to remove candidate')
        }
      })
    }
  }

  const columns: ColumnDef<CandidateTable>[] = [
    {
      header: 'S/N',
      cell: ({ row }) => (
        <span className="my-1 hidden text-sm text-header-text md:visible md:block">
          {pageIndex > 0 && isFetchingCandidates
            ? 1 + 10 * (pageIndex - 1) + row.index
            : pageIndex > 0 && !isFetchingCandidates
              ? 1 + 10 * pageIndex + row.index
              : 1 + row.index}
        </span>
      ),
    },
    {
      header: 'Name',
      accessorKey: 'name',
      cell: ({ getValue, row }) => {
        const memberName = getValue() as string;
        return (
          <span className="my-1 flex min-w-max max-w-max items-center gap-1.5 truncate text-sm">
            <Avatar
              fallback={memberName}
              size="small"
              alt={`${memberName} avatar`}
            />
            <span>{memberName}</span>
          </span>
        );
      },
      sortUndefined: 1, //force undefined values to the end
      sortDescFirst: false,
    },
    {
      header: 'Email',
      accessorKey: 'email',
      cell: ({ getValue }) => {
        return (
          <span className="my-1 inline-block max-w-max truncate text-sm">
            {getValue() as string}
          </span>
        );
      },
    },
    {
      header: 'Phone Number',
      accessorKey: 'phone_number',
      cell: ({ getValue }) => {
        const phone = getValue() as string;
        return (
          <span className="my-1 inline-block min-w-max max-w-full truncate text-sm">
            {phone}
          </span>
        );
      },
    },
    {
      header: 'Match %',
      accessorKey: 'percentage_match',
      cell: ({ getValue }) => {
        const match = getValue() as number;

        return (
          <span
            className={cn(
              'my-1 text-center text-sm',
              match <= 20 && 'text-red-500',
              match > 20 && match <= 40 && 'text-orange-500',
              match > 40 && match <= 60 && 'text-yellow-500',
              match > 60 && match < 70 && 'text-blue-500',
              match >= 70 && 'text-green-500'
            )}
          >
            {match}%
          </span>
        );
      },
    },

    {
      header: 'Current Stage',
      enableGlobalFilter: true,
      // sortingFn
      accessorKey: 'stage',
      cell: ({ getValue }) => {
        return (
          <span
            className={cn(
              'my-1 text-sm',
              !(getValue() as string) && 'text-[0.8125rem] italic text-red-500'
            )}
          >
            {(getValue() as string) || 'Removed from pipeline'}
          </span>
        );
      },
      sortUndefined: 1, //force undefined values to the end
    },

    {
      header: 'Action',
      accessorKey: 'action',
      accessorFn: row => {
        return row;
      },
      cell: ({ row }) => {
        const data = row.original;
        // cons customerId = data.ben_phone;
        // const amount = data.amount;
        // const packageSlug = data.biller;
        // const bills_type = data.bills_type;

        return (
          <Popover>
            <PopoverTrigger className="items-cente flex gap-5 rounded-md border bg-grey px-2.5 py-1.5 text-xs">
              Action <Elipsis />
            </PopoverTrigger>

            <PopoverContent
              className="flex w-max flex-col gap-1.5 !p-1 pr-4"
              align="end"
            >
              {/* <CandidateDetailsDrawer
                                applicant_id={data.id}
                                allApplicantsId={allCandidates?.results.map(candidate => candidate.id)!}
                                isDrawerOpen={isDrawerOpen}
                                openDrawer={openDrawer}
                                closeDrawer={closeDrawer}
                                refetchJobData={refetch}
                                viewOnly
                                currentStage={data.stage}
                            /> */}
              <Button
                variant="unstyled"
                size="tiny"
                justify="start"
                onClick={() => handleApplicationRemoval(data.id)}
                className="max-w-full rounded-sm py-[0.275rem] text-left text-[0.8125rem] hover:bg-primary-light"
              >
{
  isDeletingUser === data.id && isRemovingCandidate && (
    <LoaderBtn />
  ) 
}

                Delete
              </Button>
            </PopoverContent>
          </Popover>
        );
      },
    },
  ];

  const formattedData = allCandidates?.results?.map((candidate, index) => {
    const { name, email, id, percentage_match, job_stage, phone_number } =
      candidate;
    return {
      id,
      name,
      email,
      phone_number,
      percentage_match,
      action: candidate,
      stage: job_stage,
      // role: member.role_name
    };
  });

  return (
    <div className="flex max-h-full min-h-full w-full flex-col overflow-y-hidden overflow-x-scroll">
      <DataTable
        columns={columns}
        isFetching={isFetchingCandidates}
        isLoading={isLoadingCandidates}
        pageCount={pageCount}
        count={count}
        pageIndex={pageIndex}
        pageSize={pageSize}
        rows={formattedData}
        setPagination={setPagination}
        cellClass="!truncate !max-w-[16rem]"
        tableContainerClassName="overflow-hidden max-h-full grid grid-rows-[1fr_max-content] !pb-2 grow"
      // emptyDisplay={emptyDisplay}
      />
    </div>
  );
};

export default JobCandidatesTable;
function formatAxiosError(error: unknown) {
  throw new Error('Function not implemented.');
}

