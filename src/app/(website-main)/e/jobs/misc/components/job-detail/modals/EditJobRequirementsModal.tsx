import React from 'react'
import { AxiosError } from 'axios';
import { Controller, useFieldArray, useForm } from 'react-hook-form'
import toast from 'react-hot-toast';

import { cn } from '@/utils';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { useErrorModalState } from '@/hooks';
import { Button, Checkbox, ErrorModal, LoaderBtn, LoadingOverlay, Modal, Select } from '@/components/shared'
import { Plus } from '@/components/shared/icons';

import { ActiveJob, CreateJob } from '../../../types'
import { usePatchEditJobDetails } from '../../../api'
import { XIcon } from '../../../icons';


interface ModalProps {
  job: ActiveJob
  isOpen: boolean
  close: () => void
  refetch: () => void
}

interface customRequirementOptions {
  name: string, value: "TEXT_INPUT" | "FILE_UPLOAD"
}


interface CustomFormErrors {
  job_requirement_custom_fields?: Array<{ fieldTitle?: { message?: string }, fieldType?: { message: string }, }>;
}


const file_types: customRequirementOptions[] = [
  { name: 'Text Input', value: 'TEXT_INPUT' },
  { name: 'File Upload', value: 'FILE_UPLOAD' },
];



const EditJobRequirementsModal: React.FC<ModalProps> = ({ job, isOpen, close, refetch }) => {
  const { accept_personal_details, accept_resume, accept_cover_letter, post_as_anonymous, show_in_career_page, id, job_title, industry, is_multi_location, preferred_locations,
    job_status, job_type, company_overview, location, application_start_date, application_deadline, salary_currency, max_salary, min_salary, salary_type, fixed_salary, salary_negotiable,
    proficiency_level, description, responsibilities, requirements, compulsory_requirements, work_experience, working_option, pipeline_type, pipeline, team_member, job_custom_fields, requirement_custom_fields, job_requirement_custom_fields } = job
  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();
  const { handleSubmit, formState: { errors }, register, watch, getValues, trigger, setValue, setError, control, } = useForm({
    defaultValues: {
      show_in_career_page: show_in_career_page || false,
      accept_personal_details: accept_personal_details || true,
      accept_resume: accept_resume || true,
      accept_cover_letter: accept_cover_letter || false,
      post_as_anonymous: post_as_anonymous || true,
      application_start_date: new Date(application_start_date!!) || new Date(),
      application_deadline: new Date(application_deadline!!) || new Date(),
      industry: industry || undefined,
      name: job_title || undefined,
      is_multi_location,
      preferred_locations,
      job_type: job_type || undefined,
      company_overview: company_overview || undefined,
      location: location || undefined,
      salary_type: salary_type || undefined,
      salary_currency: salary_currency || undefined,
      max_salary: max_salary || undefined,
      min_salary: min_salary || undefined,
      fixed_salary: fixed_salary || undefined,
      salary_negotiable: salary_negotiable || false,
      proficiency_level: proficiency_level || undefined,
      description: description || undefined,
      responsibilities: responsibilities || undefined,
      requirements: requirements || undefined,
      compulsory_requirements: compulsory_requirements || undefined,
      work_experience: work_experience || undefined,
      working_option: working_option || undefined,
      pipeline_type: pipeline_type || undefined,
      pipeline: pipeline?.id.toString() || undefined,
      team_member: team_member.map(member => member.id) || [],
      job_custom_fields: job_custom_fields || [],
      // job_requirement_custom_fields: job_requirement_custom_fields || [],
      job_requirement_custom_fields: requirement_custom_fields?.custom_fields || [],

    },
  });



  const { fields, append, remove } = useFieldArray({
    name: 'job_requirement_custom_fields',
    control,
  });
  const customErrors = useForm().formState.errors as CustomFormErrors;






  const { mutate: editJobDetails, isLoading: isSavingEditedJob } = usePatchEditJobDetails();

  const onSubmit = async (data: CreateJob) => {
    const isValid = await trigger();

    if (isValid) {
      const dataToSubmit = {
        accept_cover_letter: data.accept_cover_letter,
        accept_personal_details: data.accept_personal_details,
        accept_resume: data.accept_resume,
        job_requirement_custom_fields: data.job_requirement_custom_fields
      }

      editJobDetails([String(id)!!, dataToSubmit], {
        onSuccess: () => {
          toast.success('Job details updated successfully');
          close()
          refetch()
        },
        onError(error) {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage);
        },
      })
    }
  };






  return (
    <Modal isModalOpen={isOpen} closeModal={close} heading='Edit Application Requirements Details'
      body={
        <form onSubmit={handleSubmit(onSubmit)} className='bg-white px-3 rounded-xl overflow-y-scroll ' id="edit-job-requirements-form">
          <div className='flex flex-col gap-10 max-w-[900px]'>
            <fieldset>
              <legend>
                <h3 className='text-header-text text-sm font-medium'>Basic requirements</h3>
              </legend>

              <div className='flex gap-y-2 gap-x-4 item-center flex-wrap '>
                <Controller
                  control={control}
                  name="accept_personal_details"
                  render={({ field }) => (
                    <div className="flex flex-row items-center space-x-2 space-y-0 bg-[#F5F3FF] py-2 px-4 rounded-[0.45rem] max-w-max">
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        label="Personal details"
                        disabled
                      />
                    </div>
                  )}
                />
                <Controller
                  control={control}
                  name="accept_resume"
                  render={({ field }) => (
                    <div className="flex flex-row items-center space-x-2 space-y-0 bg-[#F5F3FF] py-2 px-4 rounded-[0.45rem] max-w-max">
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        label="Attach resume"
                        disabled
                      />
                    </div>
                  )}
                />
                <Controller
                  control={control}
                  name="accept_cover_letter"
                  render={({ field }) => (
                    <div className="flex flex-row items-center space-x-2 space-y-0 bg-[#F5F3FF] py-2 px-4 rounded-[0.45rem] max-w-max">
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        label="Attach cover letter"

                      />
                    </div>
                  )}
                />
              </div>

            </fieldset>


            <fieldset>
              <legend>
                <h3 className='text-header-text text-sm font-medium'>Additional requirements</h3>
              </legend>

              <div className='flex flex-col gap-y-1.5'>
                {
                  fields.map((field, index) => {
                    return (
                      <fieldset key={field.id} className='flex items-center !space-y-1 gap-2'>
                        <div className='inputdiv !m-0 !mt-1 '>
                          <input
                            placeholder="Type requirement"
                            type="text"
                            className={cn(
                              '!bg-[#F5F7F9] text-secondary-text text-[15px] font-[500] !py-2',
                              customErrors.job_requirement_custom_fields &&
                              customErrors.job_requirement_custom_fields[index]?.fieldTitle && 'error'
                            )}

                            {...register(`job_requirement_custom_fields.${index}.fieldTitle`, { required: true })}
                          />

                          {customErrors.job_requirement_custom_fields && customErrors.job_requirement_custom_fields[index]?.fieldTitle && <p className='formerror'>{customErrors?.job_requirement_custom_fields[index]?.fieldTitle?.message}</p>}

                        </div>

                        <Select
                          {...register(`job_requirement_custom_fields.${index}.fieldType`)}
                          className='!py-2'
                          onChange={(value) => setValue(`job_requirement_custom_fields.${index}.fieldType`, value as "FILE_UPLOAD" | "TEXT_INPUT")}
                          options={file_types}
                          labelKey='name'
                          valueKey='value'
                          placeholder="Select requirement type"
                          hasError={!!job_requirement_custom_fields && !!errors.job_requirement_custom_fields?.[0]?.fieldType}
                          errorMessage={!!job_requirement_custom_fields && errors.job_requirement_custom_fields?.[0]?.fieldType?.message as string}

                        />
                        {customErrors.job_requirement_custom_fields && customErrors.job_requirement_custom_fields[index]?.fieldType && <p className='formerror'>{customErrors?.job_requirement_custom_fields[index]?.fieldType?.message}</p>}


                        <div title='Delete Field'>
                          <XIcon className='bg-white p-1 rounded-full cursor-pointer hover:bg-red-300' onClick={() => remove(index)} />
                        </div>
                      </fieldset>
                    )
                  })
                }
              </div>

              <Button icon={<Plus />} variant='extralight' type='button' size='tiny' className='w-max self-start mt-4 mb-5'
                onClick={(e) => {
                  append({ fieldTitle: "Custom Field", fieldType: "TEXT_INPUT" }); e.preventDefault()
                }}
              >
                Add Additional Requirement
              </Button>
            </fieldset>
          </div >





          <div className='flex items-center justify-between mt-10'>
            <Button variant='outlined' type="button" disabled={isSavingEditedJob} icon={isSavingEditedJob && <LoaderBtn />} onClick={() => close()}>
              Cancel
            </Button>
            <Button className='ml-auto' type="submit" form="edit-job-requirements-form" disabled={isSavingEditedJob} icon={isSavingEditedJob && <LoaderBtn />}>
              Save
            </Button>
          </div>

          <LoadingOverlay isOpen={isSavingEditedJob} />
          <ErrorModal
            isErrorModalOpen={isErrorModalOpen}
            setErrorModalState={setErrorModalState}

            subheading={
              errorModalMessage || 'Please check your inputs and try again.'
            }
          >
            <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
              <Button
                className="grow bg-red-950 hover:border-red-950 hover:text-red-950 px-1.5 sm:text-sm md:px-6"
                type="button"
                onClick={closeErrorModal}
              >
                Okay
              </Button>
            </div>
          </ErrorModal>
        </form>
      }
    />
  )
}

export default EditJobRequirementsModal