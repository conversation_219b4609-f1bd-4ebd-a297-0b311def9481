# Recruiter Takeover Feature for Job Application Chat

This implementation adds recruiter takeover functionality to the existing job
application chat system, allowing recruiters to seamlessly take over AI
conversations with candidates and hand control back to the AI.

## 🚀 Features

- **Real-time Takeover**: Recruiters can join active AI-candidate conversations
- **Seamless Messaging**: Direct messaging between recruiter and candidate
  during takeover
- **Smart Handover**: Recruiters can leave conversations, returning control to
  AI
- **Automatic State Detection**: Monitors AI response messages for takeover
  status changes
- **Message History**: Combined view of historical and real-time messages
- **Connection Management**: Robust WebSocket connection with auto-reconnection
- **Error Handling**: Comprehensive error handling and user feedback

## 📁 File Structure

```
5.5 JobPipelineCandidateDetailsChatTab/
├── index.tsx                          # Main component with integrated takeover UI
├── api/
│   └── index.tsx                      # Enhanced API types for recruiter messages
├── hooks/
│   └── useRecruiterChat.ts           # WebSocket hook for recruiter functionality
├── components/
│   ├── TakeoverControls.tsx          # UI controls for takeover/handover
│   ├── MessageInput.tsx              # Message input component for recruiters
│   └── RealtimeMessage.tsx           # Real-time message display component
├── types/
│   └── recruiterChat.ts              # TypeScript types and validation
├── test/
│   ├── RecruiterChatTest.tsx         # Test component for validation
│   └── WebSocketTakeoverTest.tsx     # Test component for WebSocket takeover logic
└── README.md                         # This documentation
```

## 🔧 Implementation Details

### WebSocket Connection

- **URL**: `ws://localhost:8000/ws/chat/{job_id}` (configurable via
  `NEXT_PUBLIC_AI_BACKEND_URL`)
- **Protocol**: Follows existing AI application WebSocket patterns
- **Reconnection**: Exponential backoff with configurable retry limits

### Message Types

#### Outgoing Messages (Recruiter → Server)

```typescript
// Join conversation
{
  type: 'recruiter_join',
  data: {
    recruiter_id: string,
    recruiter_name: string,
    applicant_email: string,
    reason?: string
  }
}

// Send message
{
  type: 'recruiter_message',
  data: {
    message: string
  }
}

// Leave conversation
{
  type: 'recruiter_leave',
  data: {
    handover_message?: string
  }
}
```

#### Incoming Messages (Server → Recruiter)

- `recruiter_join`: Another recruiter joined
- `recruiter_message`: Message from recruiter
- `recruiter_leave`: Recruiter left conversation
- `ai_response`: AI response with takeover status (includes
  progress.human_takeover flag)
- `chat_message`: Applicant message

#### AI Response with Takeover Status

The `ai_response` messages now include a `progress` object that indicates the
current chat mode:

```typescript
{
  type: "ai_response",
  data: {
    message: "AI will continue with your application now.",
    status: "collecting_info",
    progress: {
      human_takeover: false,        // true = human recruiter mode, false = AI mode
      message_from: "ai"            // "ai" | "human_recruiter"
    },
    next_step: null
  },
  timestamp: "2025-07-26T20:14:49.814727"
}
```

The `useRecruiterChat` hook automatically monitors these messages and updates
the `TakeoverStatus` accordingly:

- When `human_takeover: false` → Sets `isRecruiterActive: false` (AI mode)
- When `human_takeover: true` → Sets `isRecruiterActive: true` (Human recruiter
  mode)

### UI Components

#### TakeoverControls

- Connection status indicator
- Takeover/handover buttons
- Modal dialogs for confirmation
- Status display (AI active vs. recruiter active)

#### MessageInput

- Disabled when not in takeover mode
- Real-time typing indicators
- Keyboard shortcuts (Enter to send, Shift+Enter for new line)

#### RealtimeMessage

- Distinguishes between AI, recruiter, and applicant messages
- Live message indicators
- Timestamp formatting
- Special styling for takeover status messages

## 🎯 Usage

### Basic Integration

```tsx
import CandidateDetailsChatTab from './5.5 JobPipelineCandidateDetailsChatTab';

<CandidateDetailsChatTab
  unique_id="job-uuid-123"
  applicant_email="<EMAIL>"
  enabled={true}
/>
```

### Testing

```tsx
import RecruiterChatTest from './test/RecruiterChatTest';

<RecruiterChatTest
  jobUuid="job-uuid-123"
  applicantEmail="<EMAIL>"
/>
```

## 🔄 Workflow

1. **Initial State**: AI is active, recruiter sees historical messages
2. **Takeover**: Recruiter clicks "Take Over Conversation"
   - WebSocket sends `recruiter_join` message
   - UI updates to show recruiter is active
   - Message input becomes enabled
3. **Messaging**: Recruiter can send messages directly to candidate
   - Messages appear in real-time for both parties
   - Clear sender indicators (AI, recruiter, candidate)
4. **Handover**: Recruiter clicks "Hand Back to AI"
   - WebSocket sends `recruiter_leave` message
   - AI resumes conversation automatically
   - Message input becomes disabled

## ⚙️ Configuration

### Environment Variables

```env
NEXT_PUBLIC_AI_BACKEND_URL=https://apply.getlinked.live
```

### Customization

- Recruiter name/ID: Currently hardcoded, should be retrieved from user context
- Reconnection settings: Configurable in `useRecruiterChat.ts`
- UI styling: Uses existing design system classes

## 🧪 Testing

### Manual Testing

1. Use the `RecruiterChatTest` component
2. Run connection tests
3. Verify takeover/handover flow
4. Check message sending/receiving

### Expected Behavior

- ✅ WebSocket connects successfully
- ✅ Takeover changes conversation status
- ✅ Messages are sent and received in real-time
- ✅ Handover returns control to AI
- ✅ Connection errors are handled gracefully
- ✅ Reconnection works after network issues

## 🚨 Error Handling

### Connection Errors

- Network connectivity issues
- Server unavailability
- WebSocket protocol errors
- Authentication failures

### User Feedback

- Connection status indicators
- Error messages in UI
- Retry mechanisms
- Graceful degradation

## 🔮 Future Enhancements

1. **User Context Integration**: Get recruiter info from authentication
2. **Typing Indicators**: Show when candidate/AI is typing
3. **Message Reactions**: Add emoji reactions to messages
4. **File Sharing**: Support file attachments during takeover
5. **Multiple Recruiters**: Handle multiple recruiters in same conversation
6. **Analytics**: Track takeover usage and effectiveness
7. **Mobile Optimization**: Responsive design improvements

## 🐛 Known Issues

1. Recruiter name/ID are currently hardcoded
2. No persistence of takeover state across page refreshes
3. Limited error recovery for malformed WebSocket messages
4. No rate limiting on message sending

## 📝 Notes

- This implementation follows the existing codebase patterns
- WebSocket URL uses the same backend as AI applications
- UI components maintain design system consistency
- Error handling is comprehensive but can be enhanced
- Testing component provides good validation coverage
