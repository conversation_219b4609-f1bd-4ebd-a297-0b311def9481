import { format } from 'date-fns';
import React from 'react';
import { Mail } from '@/app/(auth)/misc/icons';
import { EditPenToSquare, Plane2 } from '@/components/shared/icons';

export interface TimelineEvent {
  phase: string;
  score: number;
  action: string;
  source: string;
  timestamp: string;
  overall_interview_score: number;
  event?: string;
  stage_name?: string;
  applicant_name?: string;
  status?: string;
  recipient?: string;
  moved_by?: string;
}

interface TimelineProps {
  data: TimelineEvent[];
}

const Timeline: React.FC<TimelineProps> = ({ data }) => {
  const getEventIcon = (event: TimelineEvent) => {
    // Handle new interview scoring events
    if (event.action === 'interview_ai_scoring_completed') {
      return <EditPenToSquare width={20} height={20} fill="#755ae2" />;
    }

    // Handle legacy events
    switch (event.event) {
      case 'email':
        return <Mail stroke="#755ae2" />;
      case 'progression':
        return <Plane2 width={20} height={20} fill="#755ae2" />;
      case 'percentage_match_update':
        return <EditPenToSquare width={20} height={20} fill="#755ae2" />;
      default:
        return <div className="h-3 w-3 rounded-full bg-primary"></div>;
    }
  };

  const getEventName = (event: TimelineEvent) => {
    // Handle new interview scoring events
    if (event.action === 'interview_ai_scoring_completed') {
      return `${event.phase} Interview Completed`;
    }

    // Handle legacy events
    switch (event.event) {
      case 'email':
        return 'Sent an email';
      case 'progression':
        return data.findIndex(item => item === event) > 0
          ? 'Moved to a new stage'
          : 'Applied for job';
      case 'percentage_match_update':
        return 'Match score changed';
      default:
        return 'Event';
    }
  };

  const getEventDetails = (event: TimelineEvent) => {
    // Handle new interview scoring events
    if (event.action === 'interview_ai_scoring_completed') {
      return (
        <>
          <p className="text-sm text-helper-text">
            <span className="text-header-text">Phase: </span> {event.phase}
          </p>
          <p className="text-sm text-helper-text">
            <span className="text-header-text">Score: </span> {event.score}/100
          </p>
          <p className="text-sm text-helper-text">
            <span className="text-header-text">Overall Score: </span>{' '}
            {event.overall_interview_score.toFixed(1)}/100
          </p>
          <p className="text-sm text-helper-text">
            <span className="text-header-text">Source: </span> {event.source}
          </p>
        </>
      );
    }

    // Handle legacy events
    return (
      <>
        {event.stage_name && getEventName(event) !== 'Applied for job' && (
          <p className="text-sm text-helper-text">
            <span className="text-header-text">New Stage: </span>{' '}
            {event.stage_name}
          </p>
        )}
        {event.status && (
          <p className="text-sm text-helper-text">
            <span className="text-header-text">Status: </span> {event.status}
          </p>
        )}
        {event.moved_by && (
          <p className="text-sm text-helper-text">
            <span className="text-header-text">Moved by: </span>{' '}
            {event.moved_by}
          </p>
        )}
        {event.recipient && (
          <p className="text-sm text-helper-text">
            <span className="text-header-text">Recipient: </span>{' '}
            {event.recipient}
          </p>
        )}
      </>
    );
  };

  return (
    <div className="relative">
      <div className="mx-4 border-l-4 border-primary">
        {data.map((event, index) => (
          <div key={index} className="relative mb-8">
            <div className="absolute left-[-19px] mt-1 rounded-full border-2 border-primary bg-white p-1.5">
              {getEventIcon(event)}
            </div>
            <div className="ml-8">
              <p className="font-medium text-header-text">
                {getEventName(event)}
              </p>
              <p className="text-sm text-helper-text">
                <span className="text-header-text">Date: </span>{' '}
                {format(new Date(event.timestamp), 'MMM d, yyyy h:mm aaaa')}
              </p>
              {getEventDetails(event)}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Timeline;
