'use client';

import { Loader2, MessageCircle, UserCheck, UserX } from 'lucide-react';
import React, { useState } from 'react';
import { cn } from '@/utils';
import { TakeoverStatus } from '../hooks/useRecruiterChat';

interface TakeoverControlsProps {
  takeoverStatus: TakeoverStatus;
  isConnected: boolean;
  isConnecting: boolean;
  onJoinConversation: (reason?: string) => void;
  onLeaveConversation: (handoverMessage?: string) => void;
  disabled?: boolean;
}

const TakeoverControls: React.FC<TakeoverControlsProps> = ({
  takeoverStatus,
  isConnected,
  isConnecting,
  onJoinConversation,
  onLeaveConversation,
  disabled = false,
}) => {
  const [showJoinDialog, setShowJoinDialog] = useState(false);
  const [showLeaveDialog, setShowLeaveDialog] = useState(false);
  const [joinReason, setJoinReason] = useState('');
  const [handoverMessage, setHandoverMessage] = useState('');

  const handleJoinClick = () => {
    if (takeoverStatus.isRecruiterActive) return;
    setShowJoinDialog(true);
  };

  const handleLeaveClick = () => {
    if (!takeoverStatus.isRecruiterActive) return;
    setShowLeaveDialog(true);
  };

  const confirmJoin = () => {
    onJoinConversation(joinReason || undefined);
    setShowJoinDialog(false);
    setJoinReason('');
  };

  const confirmLeave = () => {
    onLeaveConversation(handoverMessage || undefined);
    setShowLeaveDialog(false);
    setHandoverMessage('');
  };

  const cancelDialog = () => {
    setShowJoinDialog(false);
    setShowLeaveDialog(false);
    setJoinReason('');
    setHandoverMessage('');
  };

  return (
    <div className="border-b border-gray-200 bg-white px-3 py-2">
      {/* Compact Header Row */}
      <div className="flex items-center justify-between gap-3">
        {/* Connection Status & Conversation Status Combined */}
        <div className="flex items-center gap-3 text-xs">
          {/* Connection Indicator */}
          <div className="flex items-center gap-1.5">
            <div
              className={cn(
                'h-1.5 w-1.5 rounded-full',
                isConnected
                  ? 'bg-green-500'
                  : isConnecting
                  ? 'bg-yellow-500'
                  : 'bg-red-500'
              )}
            />
            <span className="text-gray-500">
              {isConnecting
                ? 'Connecting'
                : isConnected
                ? 'Connected'
                : 'Offline'}
            </span>
          </div>

          {/* Separator */}
          <div className="h-3 w-px bg-gray-300" />

          {/* Conversation Status */}
          <div className="flex items-center gap-1.5">
            <MessageCircle className="h-3 w-3 text-gray-400" />
            <span
              className={cn(
                'rounded-full px-1.5 py-0.5 text-xs font-medium',
                takeoverStatus.isRecruiterActive
                  ? 'bg-blue-100 text-blue-700'
                  : 'bg-green-100 text-green-700'
              )}
            >
              {takeoverStatus.isRecruiterActive
                ? `${takeoverStatus.recruiterName} Active`
                : 'AI Active'}
            </span>
            {takeoverStatus.joinedAt && (
              <span className="text-gray-400">
                •{' '}
                {takeoverStatus.joinedAt.toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </span>
            )}
          </div>
        </div>

        {/* Control Button */}
        <div className="flex-shrink-0">
          {!takeoverStatus.isRecruiterActive ? (
            <button
              onClick={handleJoinClick}
              disabled={disabled || !isConnected || isConnecting}
              className={cn(
                'flex items-center gap-1.5 rounded-md px-3 py-1.5 text-xs font-medium transition-colors',
                'bg-primary text-white hover:bg-primary/90',
                'disabled:cursor-not-allowed disabled:opacity-50'
              )}
            >
              {isConnecting ? (
                <Loader2 className="h-3 w-3 animate-spin" />
              ) : (
                <UserCheck className="h-3 w-3" />
              )}
              Take Over
            </button>
          ) : (
            <button
              onClick={handleLeaveClick}
              disabled={disabled || !isConnected}
              className={cn(
                'flex items-center gap-1.5 rounded-md px-3 py-1.5 text-xs font-medium transition-colors',
                'bg-gray-600 text-white hover:bg-gray-700',
                'disabled:cursor-not-allowed disabled:opacity-50'
              )}
            >
              <UserX className="h-3 w-3" />
              Hand Back
            </button>
          )}
        </div>
      </div>

      {/* Join Dialog */}
      {showJoinDialog && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-xl">
            <h3 className="mb-4 text-lg font-semibold text-gray-900">
              Take Over Conversation
            </h3>
            <p className="mb-4 text-sm text-gray-600">
              You're about to take over this AI conversation. The candidate will
              be notified that a recruiter has joined.
            </p>
            <div className="mb-4">
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Reason (optional)
              </label>
              <textarea
                value={joinReason}
                onChange={e => setJoinReason(e.target.value)}
                placeholder="e.g., Personal discussion needed, Technical clarification required..."
                className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                rows={3}
              />
            </div>
            <div className="flex gap-2">
              <button
                onClick={confirmJoin}
                className="flex-1 rounded-lg bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary/90"
              >
                Take Over
              </button>
              <button
                onClick={cancelDialog}
                className="flex-1 rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Leave Dialog */}
      {showLeaveDialog && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-xl">
            <h3 className="mb-4 text-lg font-semibold text-gray-900">
              Hand Back to AI
            </h3>
            <p className="mb-4 text-sm text-gray-600">
              You're about to hand the conversation back to the AI assistant.
              The candidate will be notified.
            </p>
            <div className="mb-4">
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Handover Message (optional)
              </label>
              <textarea
                value={handoverMessage}
                onChange={e => setHandoverMessage(e.target.value)}
                placeholder="e.g., AI will continue with your application now. Good luck!"
                className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                rows={3}
              />
            </div>
            <div className="flex gap-2">
              <button
                onClick={confirmLeave}
                className="flex-1 rounded-lg bg-gray-600 px-4 py-2 text-sm font-medium text-white hover:bg-gray-700"
              >
                Hand Back to AI
              </button>
              <button
                onClick={cancelDialog}
                className="flex-1 rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TakeoverControls;
