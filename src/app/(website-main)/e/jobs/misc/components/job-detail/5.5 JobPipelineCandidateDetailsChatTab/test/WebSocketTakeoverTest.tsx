'use client';

import React, { useState } from 'react';
import { AIResponseData, AIResponseProgress } from '../hooks/useRecruiterChat';

interface WebSocketTakeoverTestProps {
  jobUuid: string;
  applicantEmail: string;
}

/**
 * Test component to simulate WebSocket messages with human_takeover flag
 * This helps verify that the TakeoverControls component properly responds to
 * ai_response messages containing progress.human_takeover boolean values.
 */
const WebSocketTakeoverTest: React.FC<WebSocketTakeoverTestProps> = ({
  jobUuid,
  applicantEmail,
}) => {
  const [testLogs, setTestLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  // Simulate ai_response message with human_takeover: false (AI mode)
  const simulateAIMode = () => {
    const aiResponseData: AIResponseData = {
      message: "AI will continue with your application now.",
      status: "collecting_info",
      progress: {
        human_takeover: false,
        message_from: "ai"
      },
      next_step: null
    };

    const mockWebSocketMessage = {
      type: "ai_response",
      data: aiResponseData,
      timestamp: new Date().toISOString()
    };

    addLog(`Simulating AI mode message: ${JSON.stringify(mockWebSocketMessage, null, 2)}`);
    addLog(`Expected: TakeoverControls should show "AI Active" status`);
  };

  // Simulate ai_response message with human_takeover: true (Human recruiter mode)
  const simulateHumanMode = () => {
    const aiResponseData: AIResponseData = {
      message: "A recruiter has taken over the conversation.",
      status: "human_takeover",
      progress: {
        human_takeover: true,
        message_from: "human_recruiter"
      },
      next_step: null
    };

    const mockWebSocketMessage = {
      type: "ai_response",
      data: aiResponseData,
      timestamp: new Date().toISOString()
    };

    addLog(`Simulating Human Recruiter mode message: ${JSON.stringify(mockWebSocketMessage, null, 2)}`);
    addLog(`Expected: TakeoverControls should show "Recruiter Active (Human Recruiter)" status`);
  };

  const clearLogs = () => {
    setTestLogs([]);
  };

  return (
    <div className="p-6 bg-gray-50 rounded-lg">
      <h3 className="text-lg font-semibold mb-4 text-gray-900">
        WebSocket Takeover Test
      </h3>
      
      <div className="mb-4 text-sm text-gray-600">
        <p><strong>Job UUID:</strong> {jobUuid}</p>
        <p><strong>Applicant Email:</strong> {applicantEmail}</p>
      </div>

      <div className="flex gap-2 mb-4">
        <button
          onClick={simulateAIMode}
          className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 text-sm"
        >
          Simulate AI Mode
        </button>
        <button
          onClick={simulateHumanMode}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm"
        >
          Simulate Human Mode
        </button>
        <button
          onClick={clearLogs}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 text-sm"
        >
          Clear Logs
        </button>
      </div>

      <div className="bg-white rounded-lg p-4 max-h-96 overflow-y-auto">
        <h4 className="font-medium mb-2 text-gray-900">Test Logs:</h4>
        {testLogs.length === 0 ? (
          <p className="text-gray-500 text-sm">No test logs yet. Click the buttons above to simulate WebSocket messages.</p>
        ) : (
          <div className="space-y-1">
            {testLogs.map((log, index) => (
              <div key={index} className="text-xs font-mono text-gray-700 whitespace-pre-wrap">
                {log}
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="mt-4 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-medium mb-2 text-blue-900">Implementation Notes:</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• The useRecruiterChat hook now listens for ai_response messages</li>
          <li>• When data.progress.human_takeover is false → AI mode (green "AI Active" status)</li>
          <li>• When data.progress.human_takeover is true → Human mode (blue "Recruiter Active" status)</li>
          <li>• The TakeoverControls component automatically updates based on the takeoverStatus state</li>
          <li>• Console logs will show the state transitions for debugging</li>
        </ul>
      </div>
    </div>
  );
};

export default WebSocketTakeoverTest;
