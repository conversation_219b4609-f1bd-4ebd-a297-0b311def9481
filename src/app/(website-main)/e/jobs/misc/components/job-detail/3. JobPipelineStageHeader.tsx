import React, { useEffect, useRef } from 'react';
import toast from 'react-hot-toast';
import {
  Button,
  ConfirmActionModal,
  ConfirmDeleteModal,
  InfoModal,
  LoadingOverlay,
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarSeparator,
  MenubarSub,
  MenubarSubContent,
  MenubarSubTrigger,
  MenubarTrigger,
  Select,
  ToolTip,
} from '@/components/shared';
import {
  Elipsis,
  Filter,
  Info,
  MagnifyingLens,
} from '@/components/shared/icons';
import { useBooleanStateControl } from '@/hooks';
import { useUser } from '@/lib/contexts/UserContext';
import { downloadBlob } from '@/lib/utils/functions';
import { MultiCandidateReportModal } from '../../../../report/misc/components/MultiCandidateReportModal';
import {
  useDeleteCandidates,
  useExportStageData,
  useMoveCandidate,
  useRescoreCandidates,
  useSendAssessmentInvite,
} from '../../api';
import { PipelineCandidate, PipelineColumn } from '../../types';
import EditOfferLetterModal from './modals/EditOfferLetterModal';
import EditOnboardingModal from './modals/EditOnboardingModal';
import EditRejectionLetterModal from './modals/EditRejectionLetterModal';

interface JobPipelineStageHeaderProps {
  stage: PipelineColumn;
  pipelineId: string | number;
  qualifiedApplicants: number;
  selectedApplicants: string[];
  searchText: string;
  toggleSearchBox: () => void;
  filterUrl: string;
  openPreviewModal: () => void;
  setSelectedApplicants: React.Dispatch<React.SetStateAction<string[]>>;
  stageHasNewApplicant: () => boolean | undefined;
  applicantsToDisplay: PipelineCandidate[];
  data: PipelineCandidate[];
  initialApplicants: number[];
  setInitialApplicants: React.Dispatch<React.SetStateAction<number[]>>;
  job_unique_id: string;
  job_id: number;
  confirmMoveToGroup: () => void;
  openEditStageModal: () => void;
  openOfferConfigurationModal: () => void;
  isOpenOfferConfigurationModal: boolean;
  closeOfferConfigurationModal: () => void;

  openRejectionConfigurationModal: () => void;
  isOpenRejectionConfigurationModal: boolean;
  closeRejectionConfigurationModal: () => void;

  openOnboardingConfigurationModal: () => void;
  isOpenOnboardingConfigurationModal: boolean;
  closeOnboardingConfigurationModal: () => void;
}

const JobPipelineStageHeader: React.FC<JobPipelineStageHeaderProps> = ({
  stage,
  pipelineId,
  qualifiedApplicants,
  selectedApplicants,
  searchText,
  toggleSearchBox,
  filterUrl,
  openPreviewModal,
  setSelectedApplicants,
  stageHasNewApplicant,
  applicantsToDisplay,
  data,
  initialApplicants,
  job_unique_id,
  job_id,
  confirmMoveToGroup,
  setInitialApplicants,
  openEditStageModal,
  openOfferConfigurationModal,
  isOpenOfferConfigurationModal,
  closeOfferConfigurationModal,

  openRejectionConfigurationModal,
  isOpenRejectionConfigurationModal,
  closeRejectionConfigurationModal,

  openOnboardingConfigurationModal,
  isOpenOnboardingConfigurationModal,
  closeOnboardingConfigurationModal,
}) => {
  const { is_assessment, is_interview } = stage;

  /////////////////////////////////////////////////////////////////////////////////////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////
  //////////////////                          API CALL HOOKS                     //////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////

  const { mutate: moveCandidate, isLoading: isMovingCandidates } =
    useMoveCandidate(job_id);
  const { mutate: removeCandidates, isLoading: isRemovingCandidates } =
    useDeleteCandidates(job_id);

  /////////////////////////////////////////////////////////////////////////////////////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////
  //////////////////                          NUMBERS STATE                      //////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////

  const [startMove, setStartMove] = React.useState(0);
  const [endMove, setEndMove] = React.useState(0);
  const [startRemove, setStartRemove] = React.useState(0);
  const [endRemove, setEndRemove] = React.useState(0);
  const [startRescore, setStartRescore] = React.useState(0);
  const [endRescore, setEndRescore] = React.useState(0);
  const [startAssessmentInvite, setStartAssessmentInvite] = React.useState(0);
  const [endAssessmentInvite, setEndAssessmentInvite] = React.useState(0);

  /////////////////////////////////////////////////////////////////////////////////////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////
  //////////////////                          BOOLEAN STATES                     //////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////
  const {
    state: isConfirmExportStageDataMOdalOpen,
    setTrue: openConfirmExportStageDataModal,
    setFalse: closeConfirmExportStageDataModal,
  } = useBooleanStateControl();
  const {
    state: isConfirmSendAssessmentToSelectedInvite,
    setTrue: openConfirmSendAssessmentToSelectedModal,
    setFalse: closeConfirmSendAssessmentToSelectedModal,
  } = useBooleanStateControl();
  const {
    state: isConfirmSendAssessmentInviteToRange,
    setTrue: openConfirmSendAssessmentToRangeModal,
    setFalse: closeConfirmSendAssessmentToRangeModal,
  } = useBooleanStateControl();
  const {
    state: isConfirmSendAssessmentInviteToRecentCandidates,
    setTrue: openConfirmSendAssessmentToRecentCandidatesModal,
    setFalse: closeConfirmSendAssessmentToRecentCandidatesModal,
  } = useBooleanStateControl();
  const {
    state: isConfirmDeleteSelectedCandidatesModalOpen,
    setTrue: openConfirmDeleteSelectedCandidateModal,
    setFalse: closeConfirmDeleteSelectedCandidateModal,
  } = useBooleanStateControl();
  const {
    state: isConfirmDeleteRangeCandidateModalOpen,
    setTrue: openConfirmDeleteRangeCandidatesModal,
    setFalse: closeConfirmDeleteRangeCandidateModal,
  } = useBooleanStateControl();
  const {
    state: isConfirmProgressSelectedCandidateModalOpen,
    setTrue: openConfirmProgressSelectedCandidateModal,
    setFalse: closeConfirmProgressSelectedCandidateModal,
  } = useBooleanStateControl();
  const {
    state: isConfirmProgressRangeCandidateModalOpen,
    setTrue: openConfirmProgressRangeCandidatesModal,
    setFalse: closeConfirmProgressRangeCandidateModal,
  } = useBooleanStateControl();
  const {
    state: isConfirmRescoreSelectedCandidateModalOpen,
    setTrue: openConfirmRescoreSelectedCandidateModal,
    setFalse: closeConfirmRescoreSelectedCandidateModal,
  } = useBooleanStateControl();
  const {
    state: isConfirmRescoreRangeCandidateModalOpen,
    setTrue: openConfirmRescoreRangeCandidatesModal,
    setFalse: closeConfirmRescoreRangeCandidateModal,
  } = useBooleanStateControl();

  const {
    state: isInformEmailNotificationModalOpen,
    setTrue: openInformEmailNotificationModal,
    setFalse: closeInformEmailNotificationModal,
  } = useBooleanStateControl();
  const {
    state: isAssessmentNotificationModalOpen,
    setTrue: openAssessmentNotificationModal,
    setFalse: closeAssessmentNotificationModal,
  } = useBooleanStateControl();

  const {
    state: isGenerateReportModalOpen,
    setTrue: openGenerateReportModal,
    setFalse: closeGenerateReportModal,
  } = useBooleanStateControl();

  /////////////////////////////////////////////////////////////////////////////////////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////
  //////////////////                          SEARCH AND FILTERS                     //////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////

  const searchTextBoxRef = useRef<HTMLInputElement | null>(null);
  const {
    stage_id,
    stage_name,
    move_criteria,
    order,
    assessment,
    interview,
    count,
    duration,
    offer_letter_send_automatic,
    customize_rejection_message,
    enable_onboarding_setup,
  } = stage;

  // useEffect(() => {
  //     if (searchText && searchText.trim() !== "") {
  //         if (searchBy === "name" || searchBy === "cv_role" || searchBy === "years_of_experience" || searchBy === "email") {
  //             setapplicantsToDisplay(data.filter(candidate => {
  //                 const lowerSearchText = searchText.toLowerCase();
  //                 return (candidate as any)[searchBy]?.toString().toLowerCase().includes(lowerSearchText);
  //             }));
  //         }
  //         else {
  //             setapplicantsToDisplay(
  //                 data.filter(candidate => {
  //                     const lowerSearchText = searchText.toLowerCase();
  //                     const filteredData = candidate.custom_job_requirements.filter(req =>
  //                         req.custom_field_name === searchBy && req.custom_text?.toLowerCase().includes(lowerSearchText)
  //                     );
  //                     return filteredData.length > 0;
  //                 })
  //             );
  //         }

  //     } else {
  //         setapplicantsToDisplay(data);
  //     }
  // }, [searchText, searchBy, data, col]);

  // const {
  //     state: isEditOfferLetterModalOpen,
  //     setTrue: openEditOfferLetterModal,
  //     setFalse: closeEditOfferLetterModal
  // } = useBooleanStateControl()

  /////////////////////////////////////////////////////////////////////////////////////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////
  /////////////////                          ACTIONS ON CANDIDATES                    /////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////
  const checkStartAndEnd = (start?: number, end?: number) => {
    if (!start || !end) {
      toast.error('Start or end number cannot be 0');
      return false;
    } else if (start == 0 || end == 0) {
      toast.error('Start or end number cannot be 0');
      return false;
    } else if (start > end) {
      toast.error('Start number cannot be greater than end number');
      return false;
    } else if (
      start > applicantsToDisplay.length ||
      end > applicantsToDisplay.length
    ) {
      toast.error(
        'Start or end number cannot be greater than the number of candidates'
      );
      return false;
    } else return true;
  };

  const progressSelectedCandidates = () => {
    if (selectedApplicants.length > 0) {
      moveCandidate(
        {
          job_application_ids: selectedApplicants,
          stage_id: stage_id + 1,
          job_unique_id,
        },
        {
          onSuccess() {
            toast.success('Candidates successfully moved to next stage');
            closeConfirmProgressSelectedCandidateModal();
            setSelectedApplicants([]);
          },
          onError(errors: any) {
            if (errors?.response?.data?.message === 'Invalid stage ID') {
              toast.error('This is the last stage of the pipeline');
            } else {
              toast.error('Something went wrong');
            }
            closeConfirmProgressSelectedCandidateModal();
          },
        }
      );
    } else {
      toast.error('Select candidates to move');
    }
  };

  const progressCandidatesRange = () => {
    const valid = checkStartAndEnd(startMove, endMove);
    if (valid) {
      const candidates = applicantsToDisplay
        .slice(startMove - 1, endMove)
        .map(object => object['id']);
      moveCandidate(
        {
          job_application_ids: candidates,
          stage_id: stage_id + 1,
          job_unique_id,
        },
        {
          onSuccess() {
            toast.success('Candidates successfully moved to next stage');
            closeConfirmProgressRangeCandidateModal();
            setSelectedApplicants([]);
          },
          onError(errors: any) {
            if (errors?.response?.data?.message === 'Invalid stage ID') {
              toast.error('This is the last stage of the pipeline');
            } else {
              toast.error('Something went wrong');
            }
            closeConfirmProgressRangeCandidateModal();
          },
        }
      );
    }
  };

  const removeSelectedCandidates = () => {
    if (selectedApplicants.length > 0) {
      removeCandidates(
        { job_application_ids: selectedApplicants },
        {
          onSuccess(data, variables, context) {
            toast.success('Candidates were removed successfully');
            closeConfirmDeleteSelectedCandidateModal();
            return;
          },
        }
      );
    } else {
      toast.error('Select candidates to remove');
    }
  };

  const removeCandidatesRange = () => {
    const valid = checkStartAndEnd(startRemove, endRemove);
    if (valid) {
      const candidates = applicantsToDisplay
        .slice(startRemove - 1, endRemove)
        .map(object => object['id']);
      removeCandidates(
        { job_application_ids: candidates },
        {
          onSuccess(data, variables, context) {
            toast.success('Candidates removed successfully');
            closeConfirmProgressRangeCandidateModal();
            setSelectedApplicants([]);
            return;
          },
        }
      );
    }
  };

  const { mutate: rescore, isLoading: isRescoring } = useRescoreCandidates(
    Number(job_id)
  );
  const rescoreSelectedCandidates = () => {
    if (selectedApplicants.length > 0) {
      rescore(
        {
          job_id: Number(job_id),
          job_applications: selectedApplicants,
          calculate_all: false,
        },
        {
          onSuccess() {
            toast.success(
              "Candidate's match score is being recalculated, It may take up to 15 minutes for the new score to reflect in the pipeline"
            );
            closeConfirmRescoreSelectedCandidateModal();
          },
          onError(error) {
            toast.error('Something went wrong');
          },
        }
      );
    } else {
      toast.error('Select candidates to move');
    }
  };

  const rescoreCandidatesRange = () => {
    const valid = checkStartAndEnd(startRescore, endRescore);
    if (valid) {
      const candidates = applicantsToDisplay
        .slice(startRescore - 1, endRescore)
        .map(object => object['id']);
      if (candidates.length > 0) {
        rescore(
          {
            job_id: Number(job_id),
            job_applications: candidates,
            calculate_all: false,
          },
          {
            onSuccess() {
              toast.success(
                "Candidate's match score is being recalculated, It may take up to 15 minutes for the new score to reflect in the pipeline"
              );
              closeConfirmRescoreRangeCandidateModal();
            },
            onError(error) {
              toast.error('Something went wrong');
            },
          }
        );
      } else {
        toast.error('Select candidates to move');
      }
    }
  };

  const { userInfo } = useUser();
  const { mutate: sendInvite, isLoading: isSendingInvite } =
    useSendAssessmentInvite();
  const sendAssessmentInviteToSelectedCandidates = () => {
    const selectedApplicantsData = data
      ?.filter(candidate =>
        selectedApplicants.includes(candidate.id.toString())
      )
      .map(candidate => ({
        name: candidate.name,
        email: candidate.email,
        job_application_id: candidate.id,
      }));

    const dto = {
      assessment: assessment!,
      interview: interview!,
      candidates: selectedApplicantsData,
      recruiter: userInfo?.id || 0,
      duration: duration || 4,
      is_from_job_post: true,
      is_interview,
      is_assessment,
    };

    if (assessment || interview) {
      sendInvite(dto, {
        onSuccess() {
          closeConfirmSendAssessmentToSelectedModal();
          toast.success('Invites sent successfully');
          setSelectedApplicants([]);
        },
      });
    } else {
      const assessmentType = is_interview ? `interview` : 'assessment';
      toast.error(
        `There is no ${assessmentType} attached to this  Add an ${assessmentType} and try again`
      );
    }
  };

  const sendAssessmentInviteToCandidatesRange = () => {
    const valid = checkStartAndEnd(startAssessmentInvite, endAssessmentInvite);
    if (valid) {
      const candidates = applicantsToDisplay
        .slice(startAssessmentInvite - 1, endAssessmentInvite)
        .map(candidate => {
          return {
            name: candidate.name,
            email: candidate.email,
            job_application_id: candidate.id,
          };
        });
      const dto = {
        assessment: assessment!!,
        interview: interview!!,
        candidates: candidates,
        recruiter: userInfo?.id || 0,
        duration: duration || 4,
        is_from_job_post: true,
        is_interview,
        is_assessment,
      };
      sendInvite(dto, {
        onSuccess() {
          toast.success('Invites sent successfully');
          setStartAssessmentInvite(0);
          setEndAssessmentInvite(0);
          closeConfirmSendAssessmentToRangeModal();
        },
      });
    }
  };

  const sendAssessmentInviteToRecentCandidates = () => {
    let currentPool = data.map(candidate => candidate.id);
    const difference = currentPool.filter(
      candidate => !initialApplicants.includes(candidate)
    );
    if (difference.length && difference.length > 0) {
      const candidates = data
        .filter(candidato => difference.includes(candidato.id))
        .map(candidate => {
          return {
            name: candidate.name || 'Unknown',
            email: candidate.email,
            job_application_id: candidate.id,
          };
        });
      const dto = {
        assessment: assessment!!,
        interview: interview!!,
        candidates: candidates,
        recruiter: userInfo?.id || 0,
        duration: duration || 4,
        is_from_job_post: true,
        is_assessment,
        is_interview,
      };
      sendInvite(dto, {
        onSuccess() {
          toast.success('Invites sent successfully');
          closeConfirmSendAssessmentToRecentCandidatesModal();
          setInitialApplicants(currentPool);
        },
      });
    } else {
      false;
    }
  };

  const {
    data: exportedData,
    error,
    refetch: exportData,
    isRefetching: isExporting,
  } = useExportStageData({ job_id, pipeline_id: Number(pipelineId), stage_id });
  const xlsxRef = useRef<HTMLAnchorElement | null>(null);
  const exportStageData = async () => {
    const result = await exportData();

    if (result.isSuccess && result.data) {
      try {
        downloadBlob(result.data, `${stage_name}.xlsx`);
        closeConfirmExportStageDataModal();
      } catch (error) {
        console.error('Error processing file data', error);
      }
    }
  };

  return (
    <header className="sticky top-0 flex w-full items-center justify-between gap-4 rounded-t-xl bg-[#F8F9FB] px-4 py-2 font-medium text-primary">
      <a href="" ref={xlsxRef} className="hidden"></a>

      <section className="flex items-center gap-4 text-sm">
        <h2 className="flex items-center gap-1.5 truncate text-sm">
          {stage_name === 'New candidate' ? 'New' : stage_name}
          <span className="flex items-center justify-center rounded-md bg-white px-2 py-1 text-xs font-bold">
            {count}
          </span>
        </h2>
        <div>
          {(stage_name === 'New candidate' || order == 0) && (
            <h2 className="flex items-center gap-1.5 text-sm ">
              <span>Qualified</span>
              <span className="flex items-center justify-center rounded-md bg-white px-2 py-1 text-xs font-bold">
                {qualifiedApplicants}
              </span>
            </h2>
          )}
        </div>
      </section>

      <section className="flex items-center gap-3">
        <ToolTip content="Search stage" contentClass="text-xs font-normal">
          <div
            className="relative flex h-7 w-7 items-center justify-center"
            onClick={toggleSearchBox}
          >
            <MagnifyingLens
              className=""
              fill="#292D32"
              strokeWidth={0.25}
              stroke="#292D32"
            />
            {searchText && searchText.trim() !== '' && (
              <span className="absolute -right-0 -top-0 h-[0.45rem] w-[0.45rem] rounded-full bg-danger"></span>
            )}
          </div>
        </ToolTip>

        <ToolTip content="Filter applicants" contentClass="text-xs font-normal">
          <div
            className="relative flex h-7 w-7 items-center justify-center !rounded-full"
            onClick={openPreviewModal}
          >
            <Filter className="!scale-75" />
            {filterUrl && filterUrl.trim() !== '' && (
              <span className="absolute -right-0 -top-0 h-[0.45rem] w-[0.45rem] rounded-full bg-danger"></span>
            )}
          </div>
        </ToolTip>

        {(assessment || interview) &&
          (is_assessment || is_interview) &&
          selectedApplicants.length > 0 && (
            <ToolTip
              content="Click this button to send assessment invite to selected candidates"
              contentClass="text-xs max-w-sm"
            >
              <Button
                size="tiny"
                onClick={openConfirmSendAssessmentToSelectedModal}
                className="min-w-max "
              >
                Send Invite
              </Button>
            </ToolTip>
          )}

        {stageHasNewApplicant() &&
          (assessment || interview) &&
          (is_assessment || is_interview) &&
          selectedApplicants.length < 1 && (
            <ToolTip
              content="Send invite to the recently progressed candidates(s)"
              contentClass="text-xs max-w-sm"
            >
              <Button
                size="tiny"
                onClick={openConfirmSendAssessmentToRecentCandidatesModal}
                className="min-w-max "
              >
                Send Invite
              </Button>
            </ToolTip>
          )}

        <Menubar className="ml-auto">
          <MenubarMenu>
            <ToolTip content="More" contentClass="text-xs font-normal" asChild>
              <MenubarTrigger className="!ml-auto flex h-7 w-7 items-center justify-center rounded-full bg-white">
                <Elipsis />
              </MenubarTrigger>
            </ToolTip>

            <MenubarContent align="end">
              <MenubarItem onClick={openEditStageModal}>Edit stage</MenubarItem>
              <MenubarItem onClick={openConfirmExportStageDataModal}>
                Export stage data
              </MenubarItem>
              {offer_letter_send_automatic && (
                <MenubarItem onClick={openOfferConfigurationModal}>
                  Edit offer letter
                </MenubarItem>
              )}
              {customize_rejection_message && (
                <MenubarItem onClick={openRejectionConfigurationModal}>
                  Edit rejection message
                </MenubarItem>
              )}
              {enable_onboarding_setup && (
                <MenubarItem onClick={openOnboardingConfigurationModal}>
                  Update onboarding steps
                </MenubarItem>
              )}

              <MenubarSub>
                <MenubarSubTrigger>Rescore candidates</MenubarSubTrigger>
                <MenubarSubContent>
                  <div className="flex flex-col gap-2 p-4">
                    <h3 className="flex items-center gap-2 text-xs font-medium hover:bg-white hover:text-header-text">
                      Rescore selected candidates
                      <span>({selectedApplicants.length})</span>
                      <ToolTip
                        content="Select the candidates you want to move by checking the checkbox on their cards."
                        contentClass="text-[0.78rem] leading-normal p-3 !max-w-[250px]"
                      >
                        <Info />
                      </ToolTip>
                    </h3>
                    <div className="flex items-center justify-end gap-2">
                      <Button
                        size="tiny"
                        className=""
                        disabled={selectedApplicants.length < 1}
                        onClick={openConfirmRescoreSelectedCandidateModal}
                      >
                        Rescore {selectedApplicants.length} candidates
                      </Button>
                    </div>
                  </div>
                  <MenubarSeparator className="border-b-2" />

                  <div className="p-4">
                    <h3 className="flex items-center gap-2 text-xs font-medium hover:bg-white hover:text-header-text">
                      Rescore candidates range
                      <ToolTip
                        content="Input the range of candidates you want to progress to the next stage. e.g. 1-5 to progress the first 5 candidates to the next stage."
                        contentClass="text-[0.78rem] leading-normal p-3 !max-w-[250px]"
                      >
                        <Info />
                      </ToolTip>
                    </h3>
                    <section>
                      <div className="grid grid-cols-2">
                        <div className="inputdiv my-1">
                          <label htmlFor="start_move" className="!text-xs">
                            From
                          </label>
                          <input
                            type="number"
                            name="start_move"
                            id="end_move"
                            value={startRescore}
                            onChange={e =>
                              setStartRescore(parseInt(e.target.value))
                            }
                            className="!w-[5rem] !rounded-md !border-[0.75px] !bg-[#F5F7F9] !p-[0.3rem] !text-sm !font-medium focus:!border-[0.75px]"
                          />
                        </div>
                        <div className="inputdiv my-1">
                          <label htmlFor="start_move" className="!text-xs">
                            To
                          </label>
                          <input
                            type="number"
                            name="start_move"
                            id="end_move"
                            value={endRescore}
                            onChange={e =>
                              setEndRescore(parseInt(e.target.value))
                            }
                            className="!w-[5rem] !rounded-md !border-[0.75px] !bg-[#F5F7F9] !p-[0.3rem] !text-sm !font-medium focus:!border-[0.75px]"
                          />
                        </div>
                      </div>

                      <div className="mt-4 flex items-center justify-end gap-2">
                        <Button
                          size="tiny"
                          className="mt-2"
                          onClick={openConfirmRescoreRangeCandidatesModal}
                          disabled={startRescore < 1 || endRescore < 1}
                        >
                          Rescore candidates
                        </Button>
                      </div>
                    </section>
                  </div>
                </MenubarSubContent>
              </MenubarSub>

              <MenubarSub>
                <MenubarSubTrigger>Move candidates</MenubarSubTrigger>
                <MenubarSubContent>
                  <div className="flex flex-col gap-2 p-4">
                    <h3 className="flex items-center gap-2 text-xs font-medium hover:bg-white hover:text-header-text">
                      Move selected candidates
                      <span>({selectedApplicants.length})</span>
                      <ToolTip
                        content="Select the candidates you want to move by checking the checkbox on their cards."
                        contentClass="text-[0.78rem] leading-normal p-3 !max-w-[250px]"
                      >
                        <Info />
                      </ToolTip>
                    </h3>
                    <div className="flex items-center justify-end gap-2">
                      <Button
                        size="tiny"
                        className=""
                        disabled={selectedApplicants.length < 1}
                        onClick={openConfirmProgressSelectedCandidateModal}
                      >
                        Move to next stage
                      </Button>
                      <Button
                        size="tiny"
                        className=""
                        disabled={selectedApplicants.length < 1}
                        onClick={confirmMoveToGroup}
                      >
                        Move to group
                      </Button>
                    </div>
                  </div>
                  <MenubarSeparator className="border-b-2" />

                  <div className="p-4">
                    <h3 className="flex items-center gap-2 text-xs font-medium hover:bg-white hover:text-header-text">
                      Move candidates range
                      <ToolTip
                        content="Input the range of candidates you want to progress to the next stage. e.g. 1-5 to progress the first 5 candidates to the next stage."
                        contentClass="text-[0.78rem] leading-normal p-3 !max-w-[250px]"
                      >
                        <Info />
                      </ToolTip>
                    </h3>
                    <section>
                      <div className="grid grid-cols-2">
                        <div className="inputdiv my-1">
                          <label htmlFor="start_move" className="!text-xs">
                            From
                          </label>
                          <input
                            type="number"
                            name="start_move"
                            id="end_move"
                            value={startMove}
                            onChange={e =>
                              setStartMove(parseInt(e.target.value))
                            }
                            className="!w-[5rem] !rounded-md !border-[0.75px] !bg-[#F5F7F9] !p-[0.3rem] !text-sm !font-medium focus:!border-[0.75px]"
                          />
                        </div>
                        <div className="inputdiv my-1">
                          <label htmlFor="start_move" className="!text-xs">
                            To
                          </label>
                          <input
                            type="number"
                            name="start_move"
                            id="end_move"
                            value={endMove}
                            onChange={e => setEndMove(parseInt(e.target.value))}
                            className="!w-[5rem] !rounded-md !border-[0.75px] !bg-[#F5F7F9] !p-[0.3rem] !text-sm !font-medium focus:!border-[0.75px]"
                          />
                        </div>
                      </div>

                      <div className="mt-4 flex items-center justify-end gap-2">
                        <Button
                          size="tiny"
                          className="mt-2"
                          onClick={openConfirmProgressRangeCandidatesModal}
                          disabled={startMove < 1 || endMove < 1}
                        >
                          Move to next stage
                        </Button>
                        <Button
                          size="tiny"
                          className="mt-2"
                          onClick={confirmMoveToGroup}
                          disabled={startMove < 1 || endMove < 1}
                        >
                          Move to group
                        </Button>
                      </div>
                    </section>
                  </div>
                </MenubarSubContent>
              </MenubarSub>

              <MenubarSub>
                <MenubarSubTrigger>Remove candidates</MenubarSubTrigger>
                <MenubarSubContent>
                  <div className="flex flex-col gap-2 p-4">
                    <h3 className="flex items-center gap-2 text-xs font-medium hover:bg-white hover:text-header-text">
                      Remove selected candidate
                      <ToolTip
                        content="Select the candidates you want to remove from pipeline."
                        contentClass="text-[0.78rem] leading-normal p-3 !max-w-[250px]"
                      >
                        <Info />
                      </ToolTip>
                    </h3>
                    <Button
                      size="tiny"
                      className=""
                      disabled={selectedApplicants.length < 1}
                      onClick={openConfirmDeleteSelectedCandidateModal}
                    >
                      Remove {selectedApplicants.length} candidates
                    </Button>
                  </div>

                  <MenubarSeparator className="border-b-2" />

                  <div className="p-4">
                    <h3 className="flex items-center gap-2 text-xs font-medium hover:bg-white hover:text-header-text">
                      Remove range
                      <ToolTip
                        content="Input the range of candidates you want to remove from pipeline. e.g. 1-5 to remove the first 5 candidates."
                        contentClass="text-[0.78rem] leading-normal p-3 !max-w-[250px]"
                      >
                        <Info />
                      </ToolTip>
                    </h3>
                    <section>
                      <div className="grid grid-cols-2">
                        <div className="inputdiv my-1">
                          <label htmlFor="start_move" className="!text-xs">
                            From
                          </label>
                          <input
                            type="number"
                            name="start_move"
                            id="end_move"
                            placeholder={'0'}
                            value={startRemove}
                            onChange={e =>
                              setStartRemove(parseInt(e.target.value))
                            }
                            className="!w-[5rem] !rounded-md !border-[0.75px] !bg-[#F5F7F9] !p-[0.3rem] !text-sm !font-medium focus:!border-[0.75px]"
                          />
                        </div>
                        <div className="inputdiv my-1">
                          <label htmlFor="start_move" className="!text-xs">
                            To
                          </label>
                          <input
                            type="number"
                            name="start_move"
                            id="end_move"
                            placeholder={'0'}
                            value={endRemove}
                            onChange={e =>
                              setEndRemove(parseInt(e.target.value))
                            }
                            className="!w-[5rem] !rounded-md !border-[0.75px] !bg-[#F5F7F9] !p-[0.3rem] !text-sm !font-medium focus:!border-[0.75px]"
                          />
                        </div>
                      </div>

                      <div className="mt-4 flex items-center justify-end">
                        <Button
                          size="tiny"
                          variant="outlined"
                          className=" ml-2 mt-2 border-[1.5px] !px-5"
                          onClick={close}
                        >
                          Cancel
                        </Button>
                        <Button
                          size="tiny"
                          className=" ml-2 mt-2 !px-5"
                          onClick={openConfirmDeleteRangeCandidatesModal}
                          disabled={startRemove == 0 || endRemove == 0}
                        >
                          Remove
                        </Button>
                      </div>
                    </section>
                  </div>
                </MenubarSubContent>
              </MenubarSub>

              <MenubarSub>
                <MenubarSubTrigger>Generate reports</MenubarSubTrigger>
                <MenubarSubContent>
                  <div className="flex flex-col gap-2 p-4">
                    <h3 className="flex items-center gap-2 text-xs font-medium hover:bg-white hover:text-header-text">
                      Generate report for selected candidates
                      <ToolTip
                        content="Select the candidates you want to generate reports for."
                        contentClass="text-[0.78rem] leading-normal p-3 !max-w-[250px]"
                      >
                        <Info />
                      </ToolTip>
                    </h3>
                    <Button
                      size="tiny"
                      className=""
                      disabled={selectedApplicants.length < 1}
                      onClick={openGenerateReportModal}
                    >
                      Generate report for {selectedApplicants.length} candidates
                    </Button>
                  </div>
                </MenubarSubContent>
              </MenubarSub>

              {(is_assessment || is_interview) && (
                <MenubarSub>
                  <MenubarSubTrigger>
                    Send {is_interview ? 'Interview' : 'Asessment'} Invite
                  </MenubarSubTrigger>
                  <MenubarSubContent>
                    <div className="flex flex-col gap-2 p-4">
                      <h3 className="flex items-center gap-2 text-xs font-medium hover:bg-white hover:text-header-text">
                        Send invite to selected candidates
                        <ToolTip
                          content="Select the candidates you want to send invites to."
                          contentClass="text-[0.78rem] leading-normal p-3 !max-w-[250px]"
                        >
                          <Info />
                        </ToolTip>
                      </h3>
                      <Button
                        size="tiny"
                        className=""
                        disabled={selectedApplicants.length < 1}
                        onClick={openConfirmSendAssessmentToSelectedModal}
                      >
                        Send invite to {selectedApplicants.length} candidates
                      </Button>
                    </div>

                    <MenubarSeparator className="border-b-2" />

                    <div className="p-4">
                      <h3 className="flex items-center gap-2 text-xs font-medium hover:bg-white hover:text-header-text">
                        Send invite to candidates in range
                        <ToolTip
                          content="Input the range of candidates you want to send assessment invites to. e.g. 1-15 to send invite to the first 15 candidates."
                          contentClass="text-[0.78rem] leading-normal p-3 !max-w-[250px]"
                        >
                          <Info />
                        </ToolTip>
                      </h3>
                      <section>
                        <div className="grid grid-cols-2">
                          <div className="inputdiv my-1">
                            <label
                              htmlFor="start_assessment_invite"
                              className="!text-xs"
                            >
                              From
                            </label>
                            <input
                              type="number"
                              name="start_assessment_invite"
                              id="end_move"
                              placeholder={'0'}
                              value={startAssessmentInvite}
                              onChange={e =>
                                setStartAssessmentInvite(
                                  parseInt(e.target.value)
                                )
                              }
                              className="!w-[5rem] !rounded-md !border-[0.75px] !bg-[#F5F7F9] !p-[0.3rem] !text-sm !font-medium focus:!border-[0.75px]"
                            />
                          </div>
                          <div className="inputdiv my-1">
                            <label
                              htmlFor="start_assessment_invite"
                              className="!text-xs"
                            >
                              To
                            </label>
                            <input
                              type="number"
                              name="start_assessment_invite"
                              id="end_move"
                              placeholder={'0'}
                              value={endAssessmentInvite}
                              onChange={e =>
                                setEndAssessmentInvite(parseInt(e.target.value))
                              }
                              className="!w-[5rem] !rounded-md !border-[0.75px] !bg-[#F5F7F9] !p-[0.3rem] !text-sm !font-medium focus:!border-[0.75px]"
                            />
                          </div>
                        </div>

                        <div className="mt-4 flex items-center justify-end">
                          <Button
                            size="tiny"
                            variant="outlined"
                            className=" ml-2 mt-2 border-[1.5px] !px-5"
                            onClick={close}
                          >
                            Cancel
                          </Button>
                          <Button
                            size="tiny"
                            className=" ml-2 mt-2 !px-5"
                            onClick={openConfirmSendAssessmentToRangeModal}
                          >
                            Send
                          </Button>
                        </div>
                      </section>
                    </div>
                  </MenubarSubContent>
                </MenubarSub>
              )}
            </MenubarContent>
          </MenubarMenu>
        </Menubar>
      </section>

      {/* ////////////////////////////////////////////////////////////////////////////////////// */}
      {/* ////////////////////////////////////////////////////////////////////////////////////// */}
      {/* ////////////////////////////////////////////////////////////////////////////////////// */}
      {/* //////////////                           MODALS                           //////////// */}
      {/* ////////////////////////////////////////////////////////////////////////////////////// */}
      {/* ////////////////////////////////////////////////////////////////////////////////////// */}
      {/* ////////////////////////////////////////////////////////////////////////////////////// */}

      <ConfirmDeleteModal
        isModalOpen={isConfirmDeleteRangeCandidateModalOpen}
        closeModal={closeConfirmDeleteRangeCandidateModal}
        title="Delete Candidate(s)"
        deleteFunction={removeCandidatesRange}
      >
        <p className="text-sm font-normal text-[#8C8CA1]">
          You are about to delete candidates{' '}
          <span className="ml-1 font-bold text-header-text">{startRemove}</span>{' '}
          - <span className="mr-1 font-bold text-header-text">{endRemove}</span>
          from the pipeline. Please be aware that all candidates in the range
          will be deleted from the pipeline but can still be seen in the
          candidates tab.
        </p>
      </ConfirmDeleteModal>

      <ConfirmDeleteModal
        isModalOpen={isConfirmDeleteSelectedCandidatesModalOpen}
        closeModal={closeConfirmDeleteSelectedCandidateModal}
        title="Delete Candidate(s)"
        deleteFunction={removeSelectedCandidates}
      >
        <p className="text-sm font-normal text-[#8C8CA1]">
          You are about to delete{' '}
          <span className="mr-1 font-bold text-header-text">
            {selectedApplicants.length}
          </span>
          {selectedApplicants.length < 2 ? 'candidate' : 'candidates'} from the
          pipeline, Please be aware that all candidates selected will be deleted
          from the pipeline but can still be seen in the candidates tab.
        </p>
      </ConfirmDeleteModal>

      <ConfirmActionModal
        isModalOpen={isConfirmProgressRangeCandidateModalOpen}
        closeModal={closeConfirmProgressRangeCandidateModal}
        title="Progress candidates to next stage"
        confirmFunction={progressCandidatesRange}
      >
        <p className="text-sm font-normal text-[#8C8CA1]">
          You are about to progress candidates{' '}
          <span className="ml-1 font-bold text-header-text">{startMove}</span> -{' '}
          <span className="mr-1 font-bold text-header-text">{endMove}</span> to
          the next stage, Please be aware that any candidates progressed to a
          stage with email notification enabled will{' '}
          <span className="mr-1 font-medium text-header-text">
            receive an email
          </span>{' '}
          from us even if you move them back later.
        </p>
      </ConfirmActionModal>

      <ConfirmActionModal
        isModalOpen={isConfirmProgressSelectedCandidateModalOpen}
        closeModal={closeConfirmProgressSelectedCandidateModal}
        title="Progress candidates to next stage"
        confirmFunction={progressSelectedCandidates}
      >
        <p className="text-sm font-normal text-[#8C8CA1]">
          You are about to progress{' '}
          <span className="mr-1 font-bold text-header-text">
            {selectedApplicants.length}
          </span>
          {selectedApplicants.length < 2 ? 'candidate' : 'candidates'} to the
          next stage, Please be aware that any candidates progressed to a stage
          with email notification enabled will{' '}
          <span className="mr-1 font-medium text-header-text">
            receive an email
          </span>{' '}
          from us even if you move them back later.
        </p>
      </ConfirmActionModal>

      <ConfirmActionModal
        isModalOpen={isConfirmSendAssessmentToSelectedInvite}
        closeModal={closeConfirmSendAssessmentToSelectedModal}
        title="Send assessment invite to candidates"
        confirmFunction={sendAssessmentInviteToSelectedCandidates}
      >
        <p className="text-sm font-normal text-[#8C8CA1]">
          You are about to send assessment invites to{' '}
          <span className="mr-1 font-bold text-header-text">
            {selectedApplicants.length}
          </span>
          {selectedApplicants.length < 2 ? 'candidate' : 'candidates'}, Please
          be aware that this is an irreversible action. The selected candidates
          will{' '}
          <span className="mr-1 font-medium text-header-text">
            receive an email
          </span>{' '}
          within 10 minutes of confirmation.
        </p>
      </ConfirmActionModal>

      <ConfirmActionModal
        isModalOpen={isConfirmSendAssessmentInviteToRange}
        closeModal={closeConfirmSendAssessmentToRangeModal}
        title="Send assessment invite to candidates"
        confirmFunction={sendAssessmentInviteToCandidatesRange}
      >
        <p className="text-sm font-normal text-[#8C8CA1]">
          You are about to send assessment invites to candidates{' '}
          <span className="ml-1 font-bold text-header-text">
            {startAssessmentInvite}
          </span>{' '}
          -{' '}
          <span className="mr-1 font-bold text-header-text">
            {endAssessmentInvite}
          </span>
          . Please be aware that this is an irreversible action. The selected
          candidates will{' '}
          <span className="mr-1 font-medium text-header-text">
            receive an email
          </span>{' '}
          within 10 minutes of confirmation.
        </p>
      </ConfirmActionModal>

      <ConfirmActionModal
        isModalOpen={isConfirmRescoreSelectedCandidateModalOpen}
        closeModal={closeConfirmRescoreSelectedCandidateModal}
        title="Rescore Selected Candidates"
        confirmFunction={rescoreSelectedCandidates}
        isConfirmingAction={isRescoring}
      >
        <p className="text-sm font-normal text-[#8C8CA1]">
          You are about to recalculate the match score for{' '}
          <span className="mr-1 font-bold text-header-text">
            {selectedApplicants.length}
          </span>
          {selectedApplicants.length < 2 ? 'candidate' : 'candidates'}, Please
          be aware that this is an irreversible action. The new score will take
          about{' '}
          <span className="mr-1 font-medium text-header-text">
            1 -3 minutes
          </span>{' '}
          to reflect on the pipeline.
        </p>
      </ConfirmActionModal>

      <ConfirmActionModal
        isModalOpen={isConfirmRescoreRangeCandidateModalOpen}
        closeModal={closeConfirmRescoreRangeCandidateModal}
        title="Rescore Candidates"
        confirmFunction={rescoreCandidatesRange}
        isConfirmingAction={isRescoring}
      >
        <p className="text-sm font-normal text-[#8C8CA1]">
          You are about to recalculate the match score for{' '}
          <span className="mr-1 font-bold text-header-text">
            {startRescore}
          </span>{' '}
          -{' '}
          <span className="mr-1 font-bold text-header-text">{endRescore}</span>
          {selectedApplicants.length < 2 ? 'candidate' : 'candidates'}, Please
          be aware that this is an irreversible action. The new score will take
          about{' '}
          <span className="mr-1 font-medium text-header-text">
            1 - 2 minutes
          </span>{' '}
          to reflect on the pipeline.
        </p>
      </ConfirmActionModal>

      <ConfirmActionModal
        isModalOpen={
          isConfirmSendAssessmentInviteToRecentCandidates ||
          isAssessmentNotificationModalOpen
        }
        closeModal={() => {
          closeConfirmSendAssessmentToRecentCandidatesModal();
          closeAssessmentNotificationModal();
          setInitialApplicants(data.map(candidate => candidate.id));
        }}
        title="Send assessment invite to candidates"
        confirmFunction={sendAssessmentInviteToRecentCandidates}
      >
        <p className="text-sm font-normal text-[#8C8CA1]">
          You are about to send assessment invites to{' '}
          <span className="ml-1 font-bold text-header-text">
            {
              data
                .map(candidate => candidate.id)
                .filter(candidate => !initialApplicants.includes(candidate))
                .length
            }
          </span>{' '}
          candidates. Please be aware that this is an irreversible action. The
          selected candidates will{' '}
          <span className="mr-1 font-medium text-header-text">
            receive an email
          </span>{' '}
          within 10 minutes of confirmation.
        </p>
      </ConfirmActionModal>
      <ConfirmActionModal
        isModalOpen={isConfirmExportStageDataMOdalOpen}
        closeModal={closeConfirmExportStageDataModal}
        title="Export Stage Data"
        confirmFunction={exportStageData}
        isConfirmingAction={isExporting}
      >
        <p className="text-sm font-normal text-[#8C8CA1]">
          You are about to export the data and candidates on this stage to CSV
          or XLSX file. Please be aware that this action is irreversible.
        </p>
      </ConfirmActionModal>

      <InfoModal
        isModalOpen={isInformEmailNotificationModalOpen}
        closeModal={closeInformEmailNotificationModal}
        title="Email notification notice"
        confirmFunction={closeInformEmailNotificationModal}
      >
        <p className="text-sm font-normal text-[#8C8CA1]">
          We just sent an email to all the candidates you just progressed to
          this stage{' '}
          <span className="mr-1 font-medium text-header-text">
            {stage_name}
          </span>
          . If you want to stop this behaviour, turn off{' '}
          <span className="mr-1 font-medium text-header-text">
            email notification
          </span>{' '}
          in the stage configuration.
        </p>
      </InfoModal>

      <EditOfferLetterModal
        is_open={isOpenOfferConfigurationModal}
        close={closeOfferConfigurationModal}
        stage={stage}
        pipelineId={pipelineId}
      />

      <EditRejectionLetterModal
        is_open={isOpenRejectionConfigurationModal}
        close={closeRejectionConfigurationModal}
        stage={stage}
        pipelineId={pipelineId}
      />

      <EditOnboardingModal
        is_open={isOpenOnboardingConfigurationModal}
        close={closeOnboardingConfigurationModal}
        stage={stage}
        pipelineId={pipelineId}
      />

      <MultiCandidateReportModal
        open={isGenerateReportModalOpen}
        onOpenChange={closeGenerateReportModal}
        candidateIds={selectedApplicants}
        job_id={job_unique_id}
      />

      <LoadingOverlay
        isOpen={isMovingCandidates || isRemovingCandidates || isSendingInvite}
      />
    </header>
  );
};

export default JobPipelineStageHeader;
