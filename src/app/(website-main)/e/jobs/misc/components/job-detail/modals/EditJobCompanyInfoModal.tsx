import { AxiosError } from 'axios';
import React, { MouseEvent } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import TextareaAutosize from 'react-textarea-autosize';
import { z } from 'zod';

import { Button, ErrorModal, LoaderBtn, LoadingOverlay, Modal, RichTextEditor, ToolTip } from '@/components/shared';
import { Info, Plus } from '@/components/shared/icons';
import { useErrorModalState } from '@/hooks';
import { cn } from '@/utils';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { zodResolver } from '@hookform/resolvers/zod';

import { useGenerateResponsibilities, usePatchEditJobDetails } from '../../../api';
import { XIcon } from '../../../icons';
import { ActiveJob, } from '../../../types';
import { customFieldSchema } from '../../../utils/jobSchemas';

interface ModalProps {
  job: ActiveJob
  isOpen: boolean
  close: () => void
  refetch: () => void
}



interface CustomFormErrors {
  job_custom_fields?: Array<{ fieldTitle?: { message?: string }, fieldValue?: { message: string }, }>;
}
const EditJobCompanyInfoSchema = z.object({
  description: z
    .string({ required_error: 'Please enter job description' })
    .min(1, { message: 'Please enter job description' }),
  compulsory_requirements: z.string().optional(),
  requirements: z
    .string({ required_error: 'Please enter job requirements' })
    .min(1, { message: 'Please enter job requirements' }),
  responsibilities: z.string().optional(),
  added_advantage: z.string().optional(),
  company_overview: z
    .string({ required_error: 'Please enter company overview' })
    .min(1, { message: 'Please enter company overview' }),
  job_custom_fields: z.array(customFieldSchema).optional(),
});
type EditJobCompanyInfoFormType = z.infer<typeof EditJobCompanyInfoSchema>;


const EditJobCompanyInfoModal: React.FC<ModalProps> = ({ job, isOpen, close, refetch }) => {
  const { id, job_title, industry, company_overview, description, responsibilities, requirements, compulsory_requirements, job_custom_fields, added_advantage, job_requirement_custom_fields } = job
  const { handleSubmit, formState: { errors }, register, watch, clearErrors, getValues, trigger, setValue, setError, control, } = useForm<EditJobCompanyInfoFormType>({
    defaultValues: {
      description: description || undefined,
      compulsory_requirements: compulsory_requirements || undefined,
      requirements: requirements || undefined,
      responsibilities: responsibilities || undefined,
      added_advantage: added_advantage || undefined,
      company_overview: company_overview || undefined,
      job_custom_fields: job_custom_fields || [],
    },
    resolver: zodResolver(EditJobCompanyInfoSchema),
  });

  const { fields, append, remove } = useFieldArray({
    name: 'job_custom_fields',
    control,
  });
  const customErrors = useForm().formState.errors as CustomFormErrors;






  const { mutate: generateResponsibilities, isLoading: isGeneratingResponsibilities } = useGenerateResponsibilities();
  const generateJobDescription = async (e: MouseEvent<HTMLButtonElement, globalThis.MouseEvent>) => {
    e.preventDefault()
    if (job_title === undefined || job_title === "" || industry === undefined || industry === "") {
      if (job_title == undefined || job_title == "") {
        // setError('name', { message: 'Please enter job title to generate job description' })
      }
      if (industry == undefined || industry == "") {
        // setError('industry', { message: 'Please select industry to generate job description' })
      }
      setError('description', { message: 'Please select industry and enter job title to generate job description' })
      return
    }

    const data = { job_title, industry: industry! }
    clearErrors(["requirements", "responsibilities"])
    generateResponsibilities(data, {
      onSuccess: ({ job_description, job_requirements, job_responsibilities }) => {
        setValue('description', job_description);
        setValue('requirements', job_requirements.join("\n"));
        setValue('responsibilities', job_responsibilities.join("\n"));
      }
    })
  }



  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const { mutate: editJobDetails, isLoading: isSavingEditedJob } = usePatchEditJobDetails();
  const onSubmit = async (data: EditJobCompanyInfoFormType) => {
    const isValid = await trigger();
    if (isValid) {
      const dataToSubmit = {
        company_overview: data.company_overview,
        description: data.description,
        requirements: data.requirements,
        added_advantage: data.added_advantage,
        compulsory_requirements: data.compulsory_requirements,
        responsibilities: data.responsibilities,
        job_custom_fields: data.job_custom_fields,
      }

      editJobDetails([String(id)!!, dataToSubmit], {
        onSuccess: (data) => {
          toast.success('Job details updated successfully');
          close()
          refetch()
        },
        onError(error, variables, context) {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage);
        },
      })
    }
  };






  return (
    <Modal isModalOpen={isOpen} closeModal={close} heading='Edit Job Details'
      body={
        <form onSubmit={handleSubmit(onSubmit)} className='bg-white px-3 py-4 rounded-xl overflow-y-scroll ' id="create-job-form">
          <div className='max-w-[1000px] space-y-4'>

            <RichTextEditor
              label="Company Overview"
              value={watch('company_overview')}
              onChange={(value) => setValue('company_overview', value)}
              // initialContent={getCompanyData()}
              hasError={!!errors.company_overview}
              errorMessage={errors.company_overview?.message as string}
              className='text-body-text text-[0.8rem]'
            />
            <RichTextEditor
              label={
                <div className='flex items-center justify-between flex-wrap'>
                  <p>Job Description</p>
                  <Button variant='light' size='capsule' type='button'
                    onClick={(e) => generateJobDescription(e)}
                    icon={isGeneratingResponsibilities && <LoaderBtn />}
                  >
                    Auto-generate
                  </Button>
                </div>
              }
              onChange={(value) => setValue('description', value)}
              initialContent={watch('description')}
              value={watch('description')}
              placeholder='Enter job description'
              hasError={!!errors.description}
              errorMessage={errors.description?.message as string}
              className='text-body-text text-[0.8rem]'
            />

            <RichTextEditor
              label="Requirements"
              value={watch('requirements')}
              initialContent={watch('requirements')}
              onChange={(value) => setValue('requirements', value)}
              placeholder='Enter requirements'
              hasError={!!errors.requirements}
              errorMessage={errors.requirements?.message as string}
              className='text-body-text text-[0.8rem]'
            />
            <RichTextEditor
              label={
                <p className='flex items-center'>
                  Additional Requirements {" "}<span className='text-helper-text'>(optional)</span>
                  <ToolTip contentClass='text-body-text font-normal' content='This field should ONLY contain what the candidate MUST have, our system will automatically disregard and score any candidates without the requirements listed here ZERO.'>
                    <Info />
                  </ToolTip>
                </p>
              }
              placeholder='This field should ONLY contain what the candidate MUST have, our system will automatically disregard and score any candidates without the requirements listed here ZERO.'
              value={watch('compulsory_requirements')}
              initialContent={watch('compulsory_requirements')}
              onChange={(value) => setValue('compulsory_requirements', value)}
              hasError={!!errors.compulsory_requirements}
              errorMessage={errors.compulsory_requirements?.message as string}
              className='text-body-text text-[0.8rem]'
            />

            <RichTextEditor
              label={
                <>
                  Added Advantage <span className='text-helper-text'>(optional)</span>
                </>
              }
              placeholder='Enter added advantage'
              value={watch('added_advantage')}
              initialContent={watch('added_advantage')}
              onChange={(value) => setValue('added_advantage', value)}
              hasError={!!errors.added_advantage}
              errorMessage={errors.added_advantage?.message as string}
              className='text-body-text text-[0.8rem]'
            />

            <RichTextEditor
              label={
                <>
                  Responsibilities <span className='text-helper-text'>(optional)</span>
                </>
              }
              value={watch('responsibilities')}
              placeholder='Enter responsibilities'
              initialContent={watch('responsibilities')}
              onChange={(value) => setValue('responsibilities', value)}
              hasError={!!errors.responsibilities}
              errorMessage={errors.responsibilities?.message as string}
              className='text-body-text text-[0.8rem]'
            />



            <div className='flex flex-col gap-y-2 my-2'>
              {
                fields.map((field, index) => {
                  return (
                    <fieldset key={field.id}>
                      <div className='inputdiv relative'>
                        <input
                          placeholder="Type field title"
                          type="text"
                          className={cn(
                            '!bg-[#F5F7F9] text-secondary-text text-[15px] font-[500]',
                            customErrors.job_custom_fields &&
                            customErrors.job_custom_fields[index]?.fieldTitle && 'error'
                          )}

                          {...register(`job_custom_fields.${index}.fieldTitle`, { required: true })}
                        />
                        <div className='absolute top-[5%] right-[2%]' title='Delete Field'>
                          <XIcon className='bg-white p-1 rounded-full cursor-pointer hover:bg-red-300' onClick={() => remove(index)} />
                        </div>
                        {customErrors.job_custom_fields && customErrors.job_custom_fields[index]?.fieldTitle && <p className='formerror'>{customErrors?.job_custom_fields[index]?.fieldTitle?.message}</p>}


                        <TextareaAutosize
                          className={cn('!bg-[#F5F7F9] resize-none', customErrors.job_custom_fields &&
                            customErrors.job_custom_fields[index]?.fieldValue && 'error')}
                          minRows={5}
                          maxRows={10}
                          {...register(`job_custom_fields.${index}.fieldValue`, { required: true })}
                          placeholder='Type here'
                        />
                        {customErrors.job_custom_fields && customErrors.job_custom_fields[index]?.fieldValue && <p className='formerror'>{customErrors?.job_custom_fields[index]?.fieldValue?.message}</p>}
                      </div>
                    </fieldset>
                  )
                })
              }

              <Button icon={<Plus />} variant='light' type='button' className='w-full mb-8'
                onClick={(e) => {
                  append({ fieldTitle: "", fieldValue: "" }); e.preventDefault()
                }}
              >
                Add Custom Field
              </Button>
            </div>
          </div >




          <div className='flex items-center justify-between mt-10'>
            <Button variant='outlined' type="button" disabled={isSavingEditedJob} icon={isSavingEditedJob && <LoaderBtn />} onClick={() => close()}>
              Cancel
            </Button>
            <Button className='ml-auto' type="submit" form="create-job-form" disabled={isSavingEditedJob} icon={isSavingEditedJob && <LoaderBtn />}>
              Save
            </Button>
          </div>

          <LoadingOverlay isOpen={isSavingEditedJob} />
          <ErrorModal
            isErrorModalOpen={isErrorModalOpen}
            setErrorModalState={setErrorModalState}

            subheading={
              errorModalMessage || 'Please check your inputs and try again.'
            }
          >
            <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
              <Button
                className="grow bg-red-950 hover:border-red-950 hover:text-red-950 px-1.5 sm:text-sm md:px-6"
                type="button"
                onClick={closeErrorModal}
              >
                Okay
              </Button>
            </div>
          </ErrorModal>
        </form>
      }
    />
  )
}

export default EditJobCompanyInfoModal