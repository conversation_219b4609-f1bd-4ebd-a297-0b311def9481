'use client';

import { useCallback, useEffect, useRef, useState } from 'react';

// Real-time message types for recruiter chat
export interface RealtimeChatMessage {
  id: string;
  type: 'ai' | 'applicant' | 'recruiter';
  content: string;
  timestamp: Date;
  messageType?:
    | 'recruiter_join'
    | 'recruiter_message'
    | 'recruiter_leave'
    | 'ai_response'
    | 'chat_message'
    | 'takeover_status'
    | 'takeover_start';
  recruiterName?: string;
  data?: any;
}

export interface TakeoverStatus {
  isRecruiterActive: boolean;
  recruiterName?: string;
  recruiterId?: string;
  joinedAt?: Date;
}

// WebSocket message progress interface for ai_response messages
export interface AIResponseProgress {
  human_takeover: boolean;
  message_from: 'ai' | 'human_recruiter';
}

// WebSocket message data interface for ai_response messages
export interface AIResponseData {
  message: string;
  status: string;
  progress?: AIResponseProgress;
  next_step?: any;
}

interface UseRecruiterChatProps {
  jobUuid: string;
  applicantEmail: string;
  recruiterName: string;
  recruiterId: string;
  onError?: (error: string) => void;
  onTakeoverStatusChange?: (status: TakeoverStatus) => void;
}

export const useRecruiterChat = ({
  jobUuid,
  applicantEmail,
  recruiterName,
  recruiterId,
  onError,
  onTakeoverStatusChange,
}: UseRecruiterChatProps) => {
  const [realtimeMessages, setRealtimeMessages] = useState<
    RealtimeChatMessage[]
  >([]);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [takeoverStatus, setTakeoverStatus] = useState<TakeoverStatus>({
    isRecruiterActive: false,
  });
  const [isTyping, setIsTyping] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [lastConnectionAttempt, setLastConnectionAttempt] =
    useState<Date | null>(null);

  const socketRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const maxReconnectAttempts = 5;
  const reconnectBaseDelay = 1000; // 1 second
  const maxReconnectDelay = 30000; // 30 seconds

  // Get WebSocket URL
  const getWebSocketUrl = useCallback(() => {
    const aiBackendUrl = process.env.NEXT_PUBLIC_AI_BACKEND_URL;
    const protocol = aiBackendUrl?.startsWith('https://') ? 'wss://' : 'ws://';
    const baseUrl = aiBackendUrl?.replace(/^(https?:\/\/)/, '');
    return `${protocol}${baseUrl}/ws/chat/${jobUuid}`;
  }, [jobUuid]);

  // Add message to realtime chat
  const addRealtimeMessage = useCallback(
    (
      type: 'ai' | 'applicant' | 'recruiter',
      content: string,
      messageType?: string,
      data?: any
    ) => {
      console.log('Adding realtime message:', type, content, messageType, data);
      const newMessage: RealtimeChatMessage = {
        id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
        type,
        content,
        timestamp: new Date(),
        messageType: messageType as any,
        recruiterName: type === 'recruiter' ? recruiterName : undefined,
        data,
      };
      setRealtimeMessages(prev => [...prev, newMessage]);
      return newMessage;
    },
    [recruiterName]
  );

  // Handle incoming WebSocket messages
  const handleMessage = useCallback(
    (event: MessageEvent) => {
      try {
        const data = JSON.parse(event.data);
        console.log('Received recruiter WebSocket message:', data);

        // Handle error responses first
        if (data.data.code === 'APPLICATION_ERROR' && data.data.message) {
          // Check if this is a "Application already under human control" error
          if (
            data.data.message.includes(
              'Application already under human control'
            )
          ) {
            console.log(
              'Application already under human control - enabling active chat state'
            );

            // Extract recruiter name from the error message if available
            const recruiterNameMatch = data.data.message.match(/by (.+)$/);
            const controllingRecruiterName = recruiterNameMatch
              ? recruiterNameMatch[1]
              : 'another recruiter';

            // Automatically enable active chat state for seamless collaboration
            const activeStatus: TakeoverStatus = {
              isRecruiterActive: true,
              recruiterName: controllingRecruiterName,
              recruiterId: recruiterId, // Use current recruiter's ID
              joinedAt: new Date(),
            };
            setTakeoverStatus(activeStatus);
            onTakeoverStatusChange?.(activeStatus);

            // Add informational message to chat
            // addRealtimeMessage(
            //   'recruiter',
            //   `Chat is already active with ${controllingRecruiterName}. You can now participate in the conversation.`,
            //   'takeover_status',
            //   { recruiter_name: controllingRecruiterName }
            // );
            return;
          } else {
            // Handle other application errors normally
            console.log('data.data.message', data.data.message);
            console.error('Application error:', data.data.message);
            onError?.(data.data.message);
            return;
          }
        }

        if (!data.type || !data.data) {
          console.warn('Invalid message structure:', data);
          return;
        }

        switch (data.type) {
          case 'ai_response':
            const aiResponseData = data.data as AIResponseData;
            console.log('aiResponseData', aiResponseData);
            addRealtimeMessage(
              'ai',
              aiResponseData.message,
              'ai_response',
              aiResponseData
            );
            setIsTyping(false);

            // Check for human_takeover status in ai_response messages
            if (
              aiResponseData.progress &&
              typeof aiResponseData.progress.human_takeover === 'boolean'
            ) {
              const currentTakeoverState =
                aiResponseData.progress.human_takeover;

              console.log('Takeover state check:', {
                currentTakeoverState,
                currentIsRecruiterActive: takeoverStatus.isRecruiterActive,
                shouldUpdate:
                  currentTakeoverState !== takeoverStatus.isRecruiterActive,
                messageFrom: aiResponseData.progress.message_from,
              });

              // Always update takeover status to ensure UI consistency
              // This handles cases where the state might be stale or inconsistent
              const newStatus: TakeoverStatus = {
                isRecruiterActive: currentTakeoverState,
                recruiterName: currentTakeoverState
                  ? aiResponseData.progress.message_from === 'human_recruiter'
                    ? 'Human Recruiter'
                    : takeoverStatus.recruiterName || 'Human Recruiter'
                  : undefined,
                recruiterId: currentTakeoverState
                  ? takeoverStatus.recruiterId
                  : undefined,
                joinedAt: currentTakeoverState
                  ? takeoverStatus.joinedAt || new Date()
                  : undefined,
              };

              console.log('Updating takeover status from ai_response:', {
                from: takeoverStatus.isRecruiterActive,
                to: currentTakeoverState,
                newStatus,
                messageFrom: aiResponseData.progress.message_from,
                forceUpdate: true,
              });

              setTakeoverStatus(newStatus);
              onTakeoverStatusChange?.(newStatus);
            }
            break;

          case 'chat_message':
            if (data.data.user_type === 'applicant') {
              addRealtimeMessage(
                'applicant',
                data.data.message,
                'chat_message',
                data.data
              );
            }
            break;

          case 'recruiter_join':
            const joinStatus: TakeoverStatus = {
              isRecruiterActive: true,
              recruiterName: data.data.recruiter_name,
              recruiterId: data.data.recruiter_id,
              joinedAt: new Date(),
            };
            setTakeoverStatus({ ...joinStatus });
            onTakeoverStatusChange?.(joinStatus);
            addRealtimeMessage(
              'recruiter',
              `${data.data.recruiter_name} has joined the conversation`,
              'takeover_status',
              data.data
            );
            break;

          case 'recruiter_leave':
            const leaveStatus: TakeoverStatus = {
              isRecruiterActive: false,
            };
            setTakeoverStatus(leaveStatus);
            onTakeoverStatusChange?.(leaveStatus);
            addRealtimeMessage(
              'recruiter',
              data.data.handover_message ||
                'Recruiter has left the conversation. AI will continue.',
              'takeover_status',
              data.data
            );
            break;

          case 'recruiter_message':
            addRealtimeMessage(
              'recruiter',
              data.data.message,
              'recruiter_message',
              data.data
            );
            break;

          case 'welcome':
            console.log('Received welcome message:', data.data);
            // Welcome message indicates successful connection
            // No need to add to chat messages as it's a system message
            break;

          default:
            console.log('Unhandled message type:', data.type);
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
        onError?.('Error processing incoming message');
      }
    },
    [addRealtimeMessage, onTakeoverStatusChange, onError, recruiterId]
  );

  // Enhanced connection with better error handling
  const connect = useCallback(() => {
    if (isConnecting || isConnected) {
      console.log('Connection already in progress or established');
      return;
    }

    const wsUrl = getWebSocketUrl();
    if (!wsUrl) {
      const errorMsg =
        'WebSocket URL not available - check AI_BACKEND_URL configuration';
      setConnectionError(errorMsg);
      onError?.(errorMsg);
      return;
    }

    setIsConnecting(true);
    setConnectionError(null);
    setLastConnectionAttempt(new Date());

    try {
      console.log(`Attempting to connect to: ${wsUrl}`);
      const ws = new WebSocket(wsUrl);
      socketRef.current = ws;

      // Connection timeout
      const connectionTimeout = setTimeout(() => {
        if (ws.readyState === WebSocket.CONNECTING) {
          ws.close();
          const errorMsg = 'Connection timeout - server may be unavailable';
          setConnectionError(errorMsg);
          setIsConnecting(false);
          onError?.(errorMsg);
        }
      }, 10000); // 10 second timeout

      ws.onopen = () => {
        clearTimeout(connectionTimeout);
        console.log('Recruiter WebSocket connected successfully');
        setIsConnected(true);
        setIsConnecting(false);
        setConnectionError(null);
        reconnectAttemptsRef.current = 0;
      };

      ws.onmessage = handleMessage;

      ws.onclose = event => {
        clearTimeout(connectionTimeout);
        console.log(
          'Recruiter WebSocket disconnected:',
          event.code,
          event.reason
        );
        setIsConnected(false);
        setIsConnecting(false);

        // Handle different close codes
        let shouldReconnect = false;
        let errorMessage = '';

        switch (event.code) {
          case 1000: // Normal closure
            console.log('WebSocket closed normally');
            break;
          case 1001: // Going away
            errorMessage = 'Server is going away';
            shouldReconnect = true;
            break;
          case 1006: // Abnormal closure
            errorMessage = 'Connection lost unexpectedly';
            shouldReconnect = true;
            break;
          default:
            errorMessage = `Connection closed with code ${event.code}: ${event.reason}`;
            shouldReconnect = true;
        }

        if (errorMessage) {
          setConnectionError(errorMessage);
        }

        // Attempt reconnection with exponential backoff
        if (
          shouldReconnect &&
          reconnectAttemptsRef.current < maxReconnectAttempts
        ) {
          const delay = Math.min(
            reconnectBaseDelay * Math.pow(2, reconnectAttemptsRef.current),
            maxReconnectDelay
          );

          console.log(
            `Attempting reconnection ${
              reconnectAttemptsRef.current + 1
            }/${maxReconnectAttempts} in ${delay}ms...`
          );

          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttemptsRef.current++;
            connect();
          }, delay);
        } else if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
          const errorMsg = `Failed to reconnect after ${maxReconnectAttempts} attempts`;
          setConnectionError(errorMsg);
          onError?.(errorMsg);
        }
      };

      ws.onerror = error => {
        clearTimeout(connectionTimeout);
        console.error('Recruiter WebSocket error:', error);
        const errorMsg =
          'WebSocket connection error - check network connectivity';
        // setConnectionError(errorMsg);
        setIsConnecting(false);
        // onError?.(errorMsg);
      };
    } catch (error) {
      console.error('Failed to create recruiter WebSocket:', error);
      const errorMsg = `Failed to create WebSocket connection: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`;
      setConnectionError(errorMsg);
      setIsConnecting(false);
      onError?.(errorMsg);
    }
  }, [isConnecting, isConnected, getWebSocketUrl, handleMessage, onError]);

  // Disconnect WebSocket
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (socketRef.current) {
      socketRef.current.close(1000, 'Recruiter disconnected');
      socketRef.current = null;
    }

    setIsConnected(false);
    setIsConnecting(false);
    reconnectAttemptsRef.current = 0;
  }, []);

  // Join conversation (takeover)
  const joinConversation = useCallback(
    (reason?: string) => {
      if (
        !socketRef.current ||
        socketRef.current.readyState !== WebSocket.OPEN
      ) {
        onError?.('Not connected to chat service');
        return;
      }

      const payload = {
        type: 'recruiter_join',
        data: {
          recruiter_id: recruiterId,
          recruiter_name: recruiterName,
          applicant_email: applicantEmail,
          reason: reason || 'Personal discussion needed',
        },
      };

      console.log('Sending recruiter_join:', payload);
      socketRef.current.send(JSON.stringify(payload));
    },
    [recruiterId, recruiterName, applicantEmail, onError]
  );

  // Send recruiter message
  const sendMessage = useCallback(
    (message: string) => {
      if (
        !socketRef.current ||
        socketRef.current.readyState !== WebSocket.OPEN
      ) {
        onError?.('Not connected to chat service');
        return;
      }

      if (!message.trim()) return;

      // Add recruiter message to chat
      // addRealtimeMessage('recruiter', message, 'recruiter_message');

      // Send to server
      const payload = {
        type: 'recruiter_message',
        data: {
          message: message.trim(),
        },
      };

      console.log('Sending recruiter message:', payload);
      socketRef.current.send(JSON.stringify(payload));
    },
    [addRealtimeMessage, onError]
  );

  // Leave conversation (handover back to AI)
  const leaveConversation = useCallback(
    (handoverMessage?: string) => {
      if (
        !socketRef.current ||
        socketRef.current.readyState !== WebSocket.OPEN
      ) {
        onError?.('Not connected to chat service');
        return;
      }

      const payload = {
        type: 'recruiter_leave',
        data: {
          handover_message:
            handoverMessage || 'AI will continue with your application now.',
        },
      };

      console.log('Sending recruiter_leave:', payload);
      socketRef.current.send(JSON.stringify(payload));
    },
    [onError]
  );

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    realtimeMessages,
    isConnected,
    isConnecting,
    takeoverStatus,
    isTyping,
    connectionError,
    lastConnectionAttempt,
    connect,
    disconnect,
    joinConversation,
    leaveConversation,
    sendMessage,
  };
};
