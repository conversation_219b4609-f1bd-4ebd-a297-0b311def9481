import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { Comment } from '@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments';

type CandidateCommentsProps = {
    id: string,
    job_application_id:string,
    comment:string,
    rating:number,
}
type Callbacks = {
onSuccess?:()=>void,
onError?:()=>void,
}

async function fetchCandidateComments({job_application_id}:Pick<CandidateCommentsProps, "job_application_id">):Promise<Comment[]>{
    const response = await Axios.get(`/recruiter/job_application/${job_application_id}/comments`,);
    return response.data;
};

export function useFetchCandidateCommentsQuery({job_application_id}:Pick<CandidateCommentsProps, "job_application_id">){
    return useQuery({
        queryFn: async ()=> await fetchCandidateComments({job_application_id}),
        queryKey: ['job-candidate-comments', `job_application_id=${job_application_id}`]
    })
};

async function createCandidateComment({job_application_id, comment, rating}:Omit<CandidateCommentsProps, "id">){
    const response = await Axios.post(`/recruiter/job_application/${job_application_id}/comments/`,
        {
            text: comment,
            rating
        }
    );
    return response.data;
};

export function useCreateCandidateCommentMutation({job_application_id, onSuccess}:Pick<CandidateCommentsProps, "job_application_id"> & Callbacks){
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: createCandidateComment,
        onSuccess: () => {
            queryClient.refetchQueries(['job-candidate-comments', `job_application_id=${job_application_id}`]);
            onSuccess && onSuccess()
        },
    });
};


async function editCandidateComment({id, comment, rating}:CandidateCommentsProps):Promise<Comment>{
    const response = await Axios.patch(`/recruiter/comments/${id}/`,{
        text: comment,
        rating
    });
    return response.data;
};


export function useEditCandidateCommentMutation({job_application_id, onSuccess}:Pick<CandidateCommentsProps, "job_application_id"> & Callbacks){
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: editCandidateComment,
        onSuccess: () => {
            queryClient.refetchQueries(['job-candidate-comments', `job_application_id=${job_application_id}`]);
             onSuccess && onSuccess()
        },
    });
};


async function deleteCandidateComment({id}:Pick<CandidateCommentsProps, "id">){
    const response = await Axios.delete(`/recruiter/comments/${id}/`);
    return response.data;
};

export function useDeleteCandidateCommentMutation({job_application_id, onSuccess}:Pick<CandidateCommentsProps, "job_application_id"> & Callbacks){
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: deleteCandidateComment,
        onSuccess: () => {
            queryClient.refetchQueries(['job-candidate-comments', `job_application_id=${job_application_id}`]);
             onSuccess && onSuccess()
        },
    });
};
