import React from 'react';
import { CandidateDetails } from '../../types/PipelineType';

interface Props {
  basicDetails: CandidateDetails;
  skills: string[];
}

const CandidateDetailsAboutTabs: React.FC<Props> = ({
  basicDetails,
  skills,
}) => {
  return (
    <section className="max-w-full p-4 md:px-8 md:py-6">
      <div className="grid h-full gap-5 overflow-scroll md:grid-cols-2">
        <p className="flex flex-col">
          <span className="text-xs text-body-text">Name</span>
          <span className="text-sm font-semibold text-header-text">
            {basicDetails?.name}
          </span>
        </p>
        <p className="flex flex-col">
          <span className="text-xs text-body-text">Email</span>
          <span className="text-sm font-semibold text-header-text">
            {basicDetails?.email}
          </span>
        </p>
        {basicDetails?.current_location && (
          <p className="flex flex-col">
            <span className="text-xs text-body-text">Current Location</span>
            <span className="text-sm font-semibold text-header-text">
              {basicDetails?.current_location}
            </span>
          </p>
        )}
        {basicDetails?.years_of_experience && (
          <p className="flex flex-col">
            <span className="text-xs text-body-text">Years of experience</span>
            <span className="text-sm font-semibold text-header-text">
              {basicDetails?.years_of_experience}
            </span>
          </p>
        )}
        <p className="flex flex-col">
          <span className="text-xs text-body-text">Phone number</span>
          <span className="text-sm font-semibold text-header-text">
            {basicDetails?.phone_number}
          </span>
        </p>

        {basicDetails?.custom_job_requirements &&
          basicDetails?.custom_job_requirements.map((requirement, index) => {
            return (
              <React.Fragment key={index}>
                {requirement.custom_text && (
                  <p className="flex flex-col">
                    <span className="text-xs text-body-text">
                      {requirement.custom_field_name}
                    </span>
                    <span className="text-sm font-semibold text-header-text">
                      {requirement.custom_text}
                    </span>
                  </p>
                )}
              </React.Fragment>
            );
          })}

        {basicDetails.linkedin_link && (
          <p className="flex flex-col">
            <span className="text-xs text-body-text">Linkendin link</span>
            <a
              className="text-sm font-semibold text-header-text"
              href={basicDetails.linkedin_link}
            >
              {basicDetails?.linkedin_link}
            </a>
          </p>
        )}
        {basicDetails.portfolio_link && (
          <p className="flex flex-col">
            <span className="text-xs text-body-text">Portfolio link</span>
            <a
              className="text-sm font-semibold text-header-text"
              href={basicDetails.portfolio_link}
            >
              {basicDetails?.linkedin_link}
            </a>
          </p>
        )}

        {skills?.length > 0 && (
          <div className="col-span-2 mt-4 flex flex-col">
            <p className="text-sm font-semibold text-header-text">Skills:</p>
            <div className="flex flex-wrap items-center gap-3">
              {skills.map((skill, index) => {
                return (
                  <article
                    key={index}
                    className="w-max rounded-full bg-primary-light px-4 py-1.5 text-sm text-primary"
                  >
                    {skill}
                  </article>
                );
              })}
            </div>
          </div>
        )}
        {basicDetails?.matching_reason && (
          <div className="col-span-2 flex flex-col md:mt-4">
            <p className="text-sm font-semibold text-header-text">
              Match: {basicDetails?.percentage_match}% - Reason:
            </p>
            <span className="text-sm text-header-text">
              {basicDetails?.matching_reason}
            </span>
          </div>
        )}
        {basicDetails?.strength && (
          <div className="col-span-2 flex flex-col">
            <p className="text-sm font-semibold text-header-text">Strengths</p>
            <span className="text-sm text-header-text">
              {basicDetails?.strength}
            </span>
          </div>
        )}
        {basicDetails?.weakness && (
          <div className="col-span-2 flex flex-col">
            <p className="text-sm font-semibold text-header-text">Weaknesses</p>
            <span className="text-sm text-header-text">
              {basicDetails?.weakness}
            </span>
          </div>
        )}

        {/* Interview Scores Section */}
        {basicDetails?.interview_scores_data && (
          <div className="col-span-2 mt-6 border-t pt-6">
            <h3 className="mb-4 text-lg font-semibold text-header-text">
              Interview Scores
            </h3>
            <div className="space-y-4">
              {/* Overall Score */}
              <div className="flex flex-col">
                <span className="text-xs text-body-text">Overall Score</span>
                <span className="text-lg font-bold text-primary">
                  {basicDetails.interview_scores_data.overall_score}%
                </span>
              </div>

              {/* Phase Scores */}
              {Object.entries(basicDetails.interview_scores_data.phases).map(
                ([phaseName, phaseData]) => (
                  <div
                    key={phaseName}
                    className="rounded-lg border border-gray-200 p-4"
                  >
                    <div className="mb-3 flex items-center justify-between">
                      <h4 className="text-sm font-semibold capitalize text-header-text">
                        {phaseName} Phase
                      </h4>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-body-text">Score:</span>
                        <span className="text-lg font-bold text-primary">
                          {phaseData.score}%
                        </span>
                      </div>
                    </div>

                    <div className="mb-2">
                      <span className="text-xs text-body-text">Completed:</span>
                      <span className="ml-2 text-sm text-header-text">
                        {new Date(phaseData.completed_at).toLocaleDateString()}
                      </span>
                    </div>

                    <div className="mb-2">
                      <span className="text-xs text-body-text">Responses:</span>
                      <span className="ml-2 text-sm text-header-text">
                        {phaseData.responses_count}
                      </span>
                    </div>

                    <div>
                      <span className="text-xs text-body-text">
                        Score Reason:
                      </span>
                      <p className="mt-1 text-sm text-header-text">
                        {phaseData.score_reason}
                      </p>
                    </div>
                  </div>
                )
              )}

              {/* Additional Info */}
              <div className="text-xs text-body-text">
                <span>Scoring Source: </span>
                <span className="capitalize text-header-text">
                  {basicDetails.interview_scores_data.scoring_source.replace(
                    '_',
                    ' '
                  )}
                </span>
                <span className="ml-4">Phases Completed: </span>
                <span className="text-header-text">
                  {basicDetails.interview_scores_data.total_phases_completed}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default CandidateDetailsAboutTabs;
