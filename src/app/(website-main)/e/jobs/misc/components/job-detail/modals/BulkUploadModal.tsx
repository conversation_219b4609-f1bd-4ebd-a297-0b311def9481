import { filesize } from 'filesize';
import React, { useCallback, useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import toast from 'react-hot-toast';
import { Button, LoadingOverlay, Modal } from '@/components/shared';
import { useUploadBulkCVsToPipeline } from '../../../api';
import { DOCXIcon, PDFIcon, RemoveIcon, UploadIcon } from '../../../icons';

interface BulkUploadModalProps {
  isOpen: boolean;
  closeModal: () => void;
  job_id: number;
}

const BulkUploadModal: React.FC<BulkUploadModalProps> = ({
  isOpen,
  closeModal,
  job_id,
}) => {
  const [file, setFiles] = useState<Array<File> | undefined>();
  const [showUploadProgress, setshowUploadProgress] = useState(false);
  const [fileError, setFilesError] = useState(false);
  const [progress, setProgress] = useState(0);

  const onDrop = useCallback((acceptedFiles: any) => {
    setFiles(acceptedFiles);
    setshowUploadProgress(true);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc', '.docx'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        ['.docx'],
      'application/zip': ['.zip'],
    },
    maxSize: 10 * 1024 * 1024,
    multiple: true,
  });
  const docxTypes = [
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword',
    'application/docx',
  ];

  useEffect(() => {
    setProgress(50);
    setInterval(() => {
      setProgress(100);
    }, 4000);
  }, []);

  const removeFile = () => {
    setshowUploadProgress(false);
    setFiles(undefined);
    setFilesError(false);
  };

  const { mutate: uploadBulkCV, isLoading: uploadBulkLoading } =
    useUploadBulkCVsToPipeline();

  const submitFiles = () => {
    if (file === undefined) {
      setFilesError(true);
      return;
    }
    uploadBulkCV(
      {
        job: job_id.toString(),
        cvs: file!,
      },
      {
        onSuccess: data => {
          closeModal();
        },
        onError: (err: any) => {
          toast.error(err.response.data.error);
        },
      }
    );
  };

  console.log(file);
  return (
    <Modal
      isModalOpen={isOpen}
      closeModal={closeModal}
      heading="Add Candidates"
    >
      <div className="h-full overflow-y-scroll p-5">
        <div className="flex flex-col items-center">
          <p className="text-lg font-semibold text-header-text">
            Bulk upload candidates
          </p>
          <span className="text-sm text-helper-text">
            Upload resumes of candidates you want to add to this pipeline.
          </span>
        </div>
        <div>
          {showUploadProgress ? (
            <>
              <div className="flex items-center gap-[0.56rem]">
                {file && file[0] && docxTypes.includes(file[0].type) ? (
                  <DOCXIcon height={45} />
                ) : (
                  <PDFIcon />
                )}

                <div className="">
                  <p className="text-[0.75rem] text-[#646464]">
                    {file && file[0] && file[0]?.name}
                    {file &&
                      file.length > 1 &&
                      ` and ${file.length - 1} others`}
                  </p>
                  <span className="text-[0.625rem] text-[#4E4E4E] ">
                    {filesize(Number(file && file[0].size), {
                      base: 2,
                      standard: 'jedec',
                    })}
                  </span>
                </div>

                <div className="cursor-pointer">
                  <RemoveIcon onClick={removeFile} />
                </div>
              </div>

              <div className="relative mt-1 h-[0.3125rem] rounded-[0.625rem] bg-white">
                <div
                  className={`absolute inset-0 w-[${progress}%] rounded-[0.625rem] bg-[#755AE2]`}
                />
              </div>
            </>
          ) : (
            <div
              className={`${
                fileError ? 'border border-red-600' : ''
              } mt-3 flex h-[5.9375rem] w-full cursor-pointer items-center gap-4 rounded-[0.875rem] border-[0.3px] border-dashed border-[rgb(117,90,226)] p-6`}
              {...getRootProps()}
            >
              <div className="">
                <UploadIcon />
              </div>
              <div className="">
                <p className="font-sans text-xs font-semibold text-[#755AE2] sm:text-sm">
                  Tap to upload document
                </p>
                <span className="font-sans text-xs font-semibold text-[#755AE2] opacity-75">
                  Files types: pdf, docx and zip files
                </span>
              </div>
              <input hidden {...getInputProps()} />
            </div>
          )}
          <div className="">
            {fileError && file === undefined && (
              <p className="py-1 text-xs text-red-600">choose a file</p>
            )}
          </div>

          <Button className="mt-12 w-full md:text-base" onClick={submitFiles}>
            Upload Resumé
          </Button>
        </div>
      </div>

      <LoadingOverlay isOpen={uploadBulkLoading} />
    </Modal>
  );
};

export default BulkUploadModal;
