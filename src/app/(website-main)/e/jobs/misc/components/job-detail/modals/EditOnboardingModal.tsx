import React, { FormEvent, useCallback, useRef, useEffect, useState, useReducer } from "react";

import Modal from "@/components/Modal";
import "@/styles/quill.css"
import Image from 'next/image'

import { useDropzone } from "react-dropzone";
import { blobToBase64 } from "@/lib/utils/functions";
import { Badge, Separator, Card, CardContent, CardHeader, FileUpload, Input, Label, ToggleGroup, ToggleGroupItem, LoaderBtn, Button } from "@/components/shared";

import EditOfferLetterModalRTE from "./EditOfferLetterModalRTE";
import { PipelineColumn } from "../../../types";
import { useCloudinary, useErrorModalState } from "@/hooks";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { singleStageSchema } from "../../../utils/pipelineSchemas";
import { EditSinglePipelineStageType, OnboardingStep, Stage } from "../../../types/PipelineType";
import { useEditPipelineStage } from "../../../api";
import { formatAxiosErrorMessage } from "@/utils/errors";
import { AxiosError } from "axios";
import { useLoading } from "@/lib/contexts/LoadingContext";
import z from "zod";
import { addContentToTemplate, convertPipelineColumnToStage, extractContentFromTemplate } from "../../../utils/functions";
import toast from "react-hot-toast";

import { Bold, Italic, Underline as UnderlineIcon, List, AlignLeft, AlignCenter, AlignRight, Upload, Strikethrough, ListOrdered, LinkIcon, Unlink, UploadIcon, Eye, Plus, } from 'lucide-react'

import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Underline from '@tiptap/extension-underline'
import TextAlign from '@tiptap/extension-text-align'
import BulletList from '@tiptap/extension-bullet-list'
import { default as TipTapLink } from '@tiptap/extension-link'
import ListItem from '@tiptap/extension-list-item'
import OrderedList from '@tiptap/extension-ordered-list'
import { Add, CloseCircle, DocumentUpload } from 'iconsax-react';
import FormError from "@/components/shared/form-error";
import { cn } from "@/utils";
import { EmptyTeamMembers } from "@/app/(website-main)/e/teams/misc/icons";
import { div } from "@tensorflow/tfjs";
import { useCustomizeOnboardingSteps } from "../../../api/postCustomizeOnboardingSteps";
import { useGetOnboardingSteps } from "../../../api/getOnboardingSteps";

type FormDataType = {
  subject: string,
  message: string,
  cover_image: Blob | undefined,
}



interface Props {
  is_open: boolean;
  close: () => void;
  stage: PipelineColumn;
  pipelineId: string | number;

}


const defaultContent = `
                 <div contenteditable="true" translate="no" class="tiptap ProseMirror" role="textbox" aria-label="Rich-Text Editor" tabindex="0">
                <p>Hello {{ candidate_firstname }},</p>
                <p>Thank you for your interest in the [Role] position at [Company Name] and for taking the time to go through our recruitment process. </p>
                <p>After careful consideration, we regret to inform you that we have decided to move forward with another candidate for this role. Please know that this decision in no way diminishes the impressive qualifications and skills you demonstrated throughout the application process. </p>
                <p>We want to sincerely thank you for the effort and dedication you put into your application. We will keep your profile on file and may reach out if there are other positions that align with your skills and experience in the future.</p>
<p>We encourage you to apply for other roles at [Company Name] as they become available. We value the potential you bring, and we would love to keep in touch regarding future opportunities.
</p>
<p>Thank you again for your time and interest in joining our team. We wish you all the best in your career endeavors.</>

                 
                <p>
                    <br class="ProseMirror-trailingBreak">
                </p>
                <p>
                    Yours sincerely,
                </p>
                <p>{{ recruiter_full_name }}</p><p>{{ job_title }}
                </p>
                <p>
                    {{ company_name }}
                </p>
                <p>
                    {{ company_support_email }}
                </p>
                <p>
                    {{ company_address }}
                </p>
            </div>
            `



type onboardkingKey = keyof OnboardingStep;
type onboardingError = Record<onboardkingKey, string>

const tokens = [
  { title: 'First name', value: 'candidate_firstname' },
  { title: 'Last name', value: 'candidate_lastname' },
  { title: 'Position', value: 'position' },
  { title: 'Company Name', value: 'company_name' },
  { title: 'Salary', value: 'salary' },
  { title: 'Employment Type', value: 'employment_type' },
  { title: 'Recruiter\'s Job Title', value: 'recruiter_job_title' },
  // { title: 'Start date', value: 'start_date' },
  // { title: 'Start date', value: 'start_date' },
]



const EditRejectionLetterModal: React.FC<Props> = ({
  is_open,
  close,
  stage,
  pipelineId

}) => {
  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();
  const { stage_id, stage_name, rejection_setup_customization, } = stage

  const { mutateAsync: customizeOnboardingSteps, isLoading: isCustomizingOnboardingSteps } = useCustomizeOnboardingSteps(stage_id)
  const { data: onboardingStepsData, isLoading: isOnboardingStepsLoading } = useGetOnboardingSteps(stage_id)

  const [errors, setErrors] = useState<onboardingError[]>([])
  const [onboardingSteps, setOnboardingSteps] = useState<OnboardingStep[]>([])

  useEffect(() => {
    if (onboardingStepsData && onboardingStepsData.data && Array.isArray(onboardingStepsData.data)) {
      const parsedData = onboardingStepsData.data.map((step: any) => ({
          ...step,
          attachments: step.attachments[0]
        }))
      setOnboardingSteps(parsedData)
    }
  }, [onboardingStepsData])
  const handleErrors = (index: number, key: onboardkingKey, value: string) => {
    const updatedErrors = [...errors]
    updatedErrors[index][key] = value
    setErrors(updatedErrors)
  }

  const handleOnboardingStepUpdate = (key: onboardkingKey, index: number, value: string) => {
    const updatedSteps = [...onboardingSteps]
    if (key != "order") {
      updatedSteps[index][key] = value
    }
    setOnboardingSteps(updatedSteps)
  }


  const closeModal = () => {

    close()
  }





  const { isUploading } = useLoading()


  const validateOnboardingSteps = () => {
    let hasErrors = false;
    onboardingSteps.forEach((step, index) => {
       if (step.title.trim() === "") {
        handleErrors(index, "title", "Title is required");
        hasErrors = true;
      }
      if (step.content.trim() === "") {
        handleErrors(index, "content", "Content is required");
        hasErrors = true;
      }
    })
     return hasErrors;
  }

  const submitForm = async () => {

    if (onboardingSteps.length === 0) {
      toast.error("Please add at least one onboarding step");
      return
    }

    if (validateOnboardingSteps()) return;

    const prepOnboardingSteps = onboardingSteps.map((step, index) => {
      return {
        ...step,
        content: step.content,
        attachments: [step.attachments]
      }
    })

     const successes = []
    const failures = []
    for (let i = 0; i < prepOnboardingSteps.length; i++) {
      const step = prepOnboardingSteps[i]
      try {
        const res = await customizeOnboardingSteps(step)
         successes.push(step)
      } catch (error: any) {
      
        failures.push(error.response.data.error)
      }
    }

 
    if (successes.length > 0) {
      toast.success("Onboarding steps updated");
      closeModal()
    }

    if (failures.length > 0) {
      toast.error(failures.join(", "));
    }

  }


  const addSteps = () => {

    setOnboardingSteps([...onboardingSteps, { title: "", content: "", order: onboardingSteps.length + 1, attachments: "" }])
    setActiveOnboardingIndex(onboardingSteps.length)
  }

  const removeStep = (index: number) => {
    const updatedSteps = [...onboardingSteps];
    updatedSteps.splice(index, 1);
    setOnboardingSteps(updatedSteps);
  };

  const [activeOnboardingIndex, setActiveOnboardingIndex] = useState<number>(0);






  return (
    <Modal title="Customize onboarding" close={close} is_open={is_open} portalClass="grid grid-rows-[max-content,1fr,max-content] h-[calc(100vh_-_2rem)] overflow-y-hidden">


      <div className="flex flex-col relative w-[90vw] max-w-[2200px] p-4 h-full overflow-hidden bg-[#F8F9FB] ">
        <div className="bg-white p-4 rounded-lg">
          <div className="header  p-4">
            <h2 className="text-lg 2xl:text-xl font-bold">
              Content Configuration
            </h2>
            <p className="text-body-text">
              Keep your new hires excited and informed by sharing company information, culture insights, and <br /> onboarding materials. This will help ensure a smooth transition into their new role.
            </p>
          </div>

          {onboardingSteps.length > 0 && (

            <div className="flex overflow-x-scroll gap-2 mb-4 px-4">
              {onboardingSteps?.map((step, index) => (

                <Button
                  variant="unstyled"
                  key={index}
                  className={`group relative border border-primary bg-white !p-1 lg:px-4 ${index === activeOnboardingIndex ? 'border-solid' : 'border-0'}`}
                  onClick={() => setActiveOnboardingIndex(index)}
                >
                  <div className="flex items-center rounded-lg bg-[#F1EFFC] p-1 px-2">
                    <span className="flex items-center gap-2 text-[12px] text-primary">
                      Step {step.order}
                    </span>

                    {/* Close icon shown only on hover */}
                    <CloseCircle
                      size={16}
                      variant="Bold"
                      className="ml-2 hidden text-primary group-hover:inline cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation(); // prevent button click
                        removeStep(index); // your custom remove handler
                      }}
                    />
                  </div>
                </Button>

              ))}
              <Button
                variant="unstyled"
                className="border border-dashed border-primary bg-white p-1 px-2 lg:px-4"
                onClick={addSteps}
              >
                <div className="flex items-center rounded-lg bg-[#F1EFFC] p-1 px-2">
                  <span className="flex items-center gap-2 text-[12px] text-primary">
                    <Add /> Add Step
                  </span>
                </div>
              </Button>
            </div>
          )}
          {onboardingSteps.length === 0 && (
            <div className="align-center flex min-h-full w-full grow justify-center py-6 lg:[@media(min-height:700px)]:min-h-[400px]">
              <article className="via-primary-[#D9D9D9] flex min-h-[90%] w-[90%] max-w-[390px] flex-col items-center  justify-center gap-6 rounded-xl  bg-gradient-to-tr from-primary-light to-transparent p-8 sm:p-12">
                <EmptyTeamMembers />
                <div className="text-center">
                  <h3 className="text-base font-medium text-header-text">
                    No Onboarding Content Configured Yet
                  </h3>

                  <p className="text-sm font-normal text-[#7D8590]">
                    You haven’t set up any content for engaging candidates after the offer is made.
                  </p>

                </div>
                <Button
                  variant={'default'}
                  icon={<Plus fill="white" width={17} height={17} />}
                  onClick={addSteps}
                >
                  Add Steps
                </Button>

              </article>
            </div>
          )}


        </div>

        {onboardingSteps.length > 0 && (
          <div className="bg-white p-4 rounded-lg mt-4">
            {
              onboardingSteps.map((step: OnboardingStep, index: number) => (
                <div className={index != activeOnboardingIndex ? "hidden" : ""}>
                  <OnboardingStepComponent
                    key={index}
                    onboardingStep={step}
                    handleUpdate={(key: keyof OnboardingStep, value: string) => handleOnboardingStepUpdate(key, index, value)}
                    errors={errors[index]}
                  />
                </div>
              ))
            }

          </div>
        )}
      </div>
      {onboardingSteps.length > 0 && (
        <div className="sticky bottom-0 flex gap-4 items-center justify-end p-4 bg-white">
          <button type="button" onClick={close} className="btn-primary-light"> Cancel </button>
          <button onClick={submitForm} className="btn-primary items-center gap-2" > Save

            {
              (isUploading || isCustomizingOnboardingSteps) && <LoaderBtn color="white" />
            }
          </button>
        </div>)}

    </Modal>
  );
};


interface OnboardingStepComponentProps {
  onboardingStep: OnboardingStep;
  handleUpdate: (key: keyof OnboardingStep, value: string) => void;
  errors: onboardingError;
}

const OnboardingStepComponent = ({
  onboardingStep,
  handleUpdate,
  errors,
}: OnboardingStepComponentProps) => {


  const [selectedAttachment, setSelectedAttachment] = useState<File | null>(null);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: false,
        orderedList: false,
        listItem: false,
      }),
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      BulletList,
      OrderedList,
      ListItem,
      TipTapLink.configure({
        openOnClick: false,
        autolink: true,
        defaultProtocol: 'https',
      }),
    ],

    content: onboardingStep.content,
    onUpdate: ({ editor }) => {
      handleUpdate('content', editor.getHTML())
    },
  })


  const getLinkName = (link: string) => {
    const url = new URL(link)
    return url.pathname.split('/').pop()
  }

  const setLink = useCallback(() => {
    if (editor) {
      const previousUrl = editor.getAttributes('link').href
      const url = window.prompt('URL', previousUrl)

      if (url === null) {
        return
      }

      if (url === '') {
        editor.chain().focus().extendMarkRange('link').unsetLink()
          .run()
        return
      }

      editor.chain().focus().extendMarkRange('link').setLink({ href: url })
        .run()
    }
  }, [editor])



 
  if (!editor) {
    return null
  }
  const insertToken = (token: string) => {
    if (editor) {
      editor.commands.insertContent(`{{ ${token} }}`)
    }
  }

  // const [, forceUpdate] = useReducer((x) => x + 1, 0)

  // useEffect(() => {
  //   if (editor) {
  //     editor.on('selectionUpdate', () => {
  //       // Force a re-render to update the toggle states
  //       forceUpdate()
  //     })
  //   }
  // }, [editor])


  return (
    <div className="grid grid-cols-2 w-full p-4 gap-4 grow overflow-y-scroll">


      <Card className="">

        <CardContent className="">
          <div className="py-6">
            <Input
              className="border rounded-xl p-4 border-[helper-text] w-full"
              id="email-subject" type="text"
              label="Title"
              labelClass="text-black"
              placeholder="Enter email subject here"
              value={onboardingStep.title}
              onChange={(e) => handleUpdate('title', e.target.value)}
              required
              hasError={!!errors?.title}
              errorMessage={errors?.title}
            />


            {/* Token Buttons */}
            <div className="text-body-text font-normal text-[0.85rem] flex flex-col">
              <Label className="my-4 mb-3 text-body-text">To customize this email, drag the placeholder options displayed below
                and place them where you will like to have them.
              </Label>
              <div className="flex flex-wrap gap-2 border border-[#E4E4E4] p-4 rounded-xl">
                {tokens.map((token) => (
                  <Badge
                    key={token.value}
                    variant="light"
                    shape="rounded"
                    className='cursor-pointer border-none font-normal'
                    size="lg"
                    onClick={() => insertToken(token.value)}
                  >
                    {token.title}
                  </Badge>
                ))}
              </div>
            </div>


            <article className="border-[0.7px] border-[#E4E4E4] rounded-xl">
              <header>
                <ToggleGroup type="multiple" className="justify-start flex-wrap border-b">
                  <ToggleGroupItem
                    value="bold"
                    aria-label="Toggle bold"
                    onClick={() => editor.chain().focus().toggleBold().run()}
                    data-state={editor.isActive('bold') ? 'on' : 'off'}
                  >
                    <Bold className="h-4 w-4" />
                  </ToggleGroupItem>
                  <ToggleGroupItem
                    value="italic"
                    aria-label="Toggle italic"
                    onClick={() => editor.chain().focus().toggleItalic().run()}
                    data-state={editor.isActive('italic') ? 'on' : 'off'}
                  >
                    <Italic className="h-4 w-4" />
                  </ToggleGroupItem>
                  <ToggleGroupItem
                    value="italic"
                    aria-label="Toggle italic"
                    onClick={() => editor.chain().focus().toggleStrike().run()}
                    data-state={editor.isActive('italic') ? 'on' : 'off'}
                  >
                    <Strikethrough className="h-4 w-4" />
                  </ToggleGroupItem>
                  <ToggleGroupItem
                    value="underline"
                    aria-label="Toggle underline"
                    onClick={() => editor.chain().focus().toggleUnderline().run()}
                    data-state={editor.isActive('underline') ? 'on' : 'off'}
                  >
                    <UnderlineIcon className="h-4 w-4" />
                  </ToggleGroupItem>

                  <Separator orientation="vertical" className="mx-1 h-8" />

                  <ToggleGroupItem
                    value="left"
                    aria-label="Align left"
                    onClick={() => editor.chain().focus().setTextAlign('left').run()}
                    data-state={editor.isActive({ textAlign: 'left' }) ? 'on' : 'off'}
                  >
                    <AlignLeft className="h-4 w-4" />
                  </ToggleGroupItem>
                  <ToggleGroupItem
                    value="center"
                    aria-label="Align center"
                    onClick={() => editor.chain().focus().setTextAlign('center').run()}
                    data-state={editor.isActive({ textAlign: 'center' }) ? 'on' : 'off'}
                  >
                    <AlignCenter className="h-4 w-4" />
                  </ToggleGroupItem>
                  <ToggleGroupItem
                    value="right"
                    aria-label="Align right"
                    onClick={() => editor.chain().focus().setTextAlign('right').run()}
                    data-state={editor.isActive({ textAlign: 'right' }) ? 'on' : 'off'}
                  >
                    <AlignRight className="h-4 w-4" />
                  </ToggleGroupItem>

                  <Separator orientation="vertical" className="mx-1 h-8" />

                  <ToggleGroupItem
                    value="bulletList"
                    aria-label="Toggle bullet list"
                    onClick={() => editor.chain().focus().toggleBulletList().run()}
                    data-state={editor.isActive('bulletList') ? 'on' : 'off'}
                  >
                    <List className="h-4 w-4" />
                  </ToggleGroupItem>
                  <ToggleGroupItem
                    value="orderedList"
                    aria-label="Toggle ordered list"
                    onClick={() => editor.chain().focus().toggleOrderedList().run()}
                    data-state={editor.isActive('orderedList') ? 'on' : 'off'}
                  >
                    <ListOrdered className="h-4 w-4" />
                  </ToggleGroupItem>

                  <Separator orientation="vertical" className="mx-1 h-8" />

                  <ToggleGroupItem
                    value="bulletList"
                    aria-label="Toggle bullet list"
                    onClick={setLink}
                    data-state={editor.isActive('link') ? 'on' : 'off'}
                  >
                    <LinkIcon className="h-4 w-4" />
                  </ToggleGroupItem>
                  <ToggleGroupItem
                    value="orderedList"
                    aria-label="Toggle ordered list"
                    onClick={() => editor.chain().focus().unsetLink().run()}
                    disabled={!editor.isActive('link')}
                  >
                    <Unlink className="h-4 w-4" />
                  </ToggleGroupItem>
                </ToggleGroup>
              </header>


              <section className="min-h-[500px]  p-4">
                <EditorContent editor={editor} className="prose max-w-none" />
              </section>
              {
                !!errors?.content && <FormError errorMessage={errors?.content} />
              }
            </article>

            {/* <FileUpload
                  label="Attachment: Offer Letter (PDF)"
                  accept={{ 'application/pdf': [] }}
                  maxSizeMB={15}
                  dragActiveClassName="bg-green-50 border-green-500"
                  containerClassName="w-full max-w-2xl"
                  value={selectedAttachment}
                  onFileSelect={setSelectedAttachment}
                  onFileRemove={() => setSelectedAttachment(null)}
                /> */}

          </div>
        </CardContent>
      </Card>


      <Card className="border-none">
        <CardHeader className="text-lg 2xl:text-xl">
          Preview
        </CardHeader>
        <CardContent className="p-6 pt-2">
          {onboardingStep.title.trim() != "" &&
            <div className="text-body-text font-normal text-[0.85rem]  mb-5 gap-2">
              <p className="text-body-text">Title
              </p>
              <p className=" font-medium text-lg text-black">{onboardingStep.title}</p>
            </div>
          }

          <div className="tiptap max-w-none border p-2 rounded-lg">
            <div
              dangerouslySetInnerHTML={{ __html: editor.getHTML() }}
              className="tiptap"
            />
          </div>

        </CardContent>
      </Card>
    </div>


  )
}



export default EditRejectionLetterModal;
