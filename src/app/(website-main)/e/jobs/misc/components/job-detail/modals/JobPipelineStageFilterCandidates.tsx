import React, { <PERSON><PERSON><PERSON>, SetStateAction, useState } from 'react'
import toast from 'react-hot-toast'

import { cn } from '@/utils'
import { useBooleanStateControl } from '@/hooks'
import Modal from '@/components/Modal'
import { Button, ConfirmActionModal, EmptyCard, ToolTip } from '@/components/shared'
import { CaretDown, Filter, StrokeCheck, Trash } from '@/components/shared/icons'
import { Menu } from '@headlessui/react'

import { PipelineApplicant, PipelineCandidate, PipelineColumn } from '../../../types'
import JobPipelineStageFilterParameters from './JobPipelineStageFilterParameters'
import FilteredGroupsModal from './FilteredGroupsModal'
import { useMoveCandidate } from '../../../api'
import { CloseCircle } from 'iconsax-react'
import JobPipelineStageApplicantCard from '../4. JobPipelineCandidate'


interface ModalProps {
    applicants: PipelineApplicant[]
    isOpen: boolean
    close: () => void
    job_id: number
    job_unique_id: string
    pipeline_id: string | number
    stage: PipelineColumn,
    refetch: () => void
    filterUrl: string
    setFilterUrl: Dispatch<SetStateAction<string>>;
    cvRoles: string[];
    uniqueSkills: string[];
    uniqueTools: string[];
    searchFilters: {
        name: string;
        value: string | undefined;
    }[]
    moveToGroup?: boolean
}

const filterByes = [
    { name: 'Experience', value: 'years_of_experience' },
    { name: 'CV Role', value: 'cv_role' },
]

const PreviewMoveCandidates: React.FC<ModalProps> = ({ isOpen, close, applicants, pipeline_id, job_id, stage, refetch, job_unique_id, cvRoles, uniqueSkills, uniqueTools, filterUrl, setFilterUrl, searchFilters, moveToGroup = false }) => {
    const [applicantsToDisplay, setApplicantsToDisplay] = React.useState(applicants)
    const [searchText, setSearchText] = React.useState('')
    const [searchBy, setSearchBy] = React.useState('name')
    const [selectedRoles, setSelectedRoles] = useState<string[]>(filterUrl.trim() !== "" ? filterUrl.slice(9).split(',') : []);
    const [selectedExperiences, setSelectedExperiences] = useState<string[]>([])
    const [selectedSkills, setSelectedSkills] = useState<string[]>([])
    const [selectedTools, setSelectedTools] = useState<string[]>([])
    const [selectedApplicants, setSelectedApplicants] = React.useState<string[]>([])
    const [minPercentageMatch, setMinPercentageMatch] = useState(0);
    const [maxPercentageMatch, setMaxPercentageMatch] = useState(100);
    const [isSeenValue, setIsSeenValue] = useState<boolean | null>(null)

    const handleApplyFilters = () => {
        const filteredCandidates = applicants.filter((applicant) => {
            const cvRole = applicant.cv_role?.toLowerCase() || '';
            const roleFilter = selectedRoles.length === 0 || selectedRoles.includes(cvRole);
            const experienceFilter = selectedExperiences.length === 0 || selectedExperiences.includes(applicant.years_of_experience?.toString());

            // Text search filter
            const lowerSearchText = searchText.toLowerCase();

            const textSearchFilter =
                !searchText ||
                applicant.name?.toLowerCase().includes(lowerSearchText) ||
                cvRole.includes(lowerSearchText) ||
                applicant.email?.toLowerCase().includes(lowerSearchText) ||
                applicant.years_of_experience?.toString().toLowerCase().includes(lowerSearchText) ||
                applicant.custom_job_requirements.some((req) =>
                    req.custom_field_name === searchBy && req.custom_text?.toLowerCase().includes(lowerSearchText)
                );

            // Match percentage filter
            const matchPercentageFilter =
                applicant.percentage_match >= minPercentageMatch && applicant.percentage_match <= maxPercentageMatch;

            // is_seen filter
            const isSeenFilter = isSeenValue === null || applicant.is_seen === isSeenValue;

            return roleFilter && experienceFilter && textSearchFilter && matchPercentageFilter && isSeenFilter;
        });

        setApplicantsToDisplay(filteredCandidates);
    };


    React.useEffect(() => {
        handleApplyFilters()
    }, [searchText, searchBy, applicants, selectedExperiences, selectedRoles, selectedTools, selectedSkills, minPercentageMatch, maxPercentageMatch, isSeenValue]);


    const {
        state: isGroupsModalOpen,
        setTrue: openGroupsModal,
        setFalse: closeGroupsModal,
    } = useBooleanStateControl()

    const {
        state: isConfirmProgressSelectedCandidateModalOpen,
        setTrue: openConfirmProgressSelectedCandidateModal,
        setFalse: closeConfirmProgressSelectedCandidateModal,
    } = useBooleanStateControl(false)

    const resetAllFilters = () => {
        setSelectedRoles([])
        setSelectedExperiences([])
        setSelectedSkills([])
        setSelectedTools([])
        setFilterUrl("");
        setSearchText("")
        setMinPercentageMatch(0);
        setMaxPercentageMatch(100);
        setIsSeenValue(null)
    };

    const { mutate: moveCandidate, isLoading: isMovingCandidates } = useMoveCandidate(job_id)
    const progressSelectedCandidates = () => {
        if (selectedApplicants.length > 0) {
            moveCandidate({ job_application_ids: selectedApplicants, stage_id: stage.stage_id + 1, job_unique_id }, {
                onSuccess(data, variables, context) {
                    toast.success("Candidates successfully moved to next stage")
                    closeConfirmProgressSelectedCandidateModal()
                    setSelectedApplicants([])
                    refetch()
                },
                onError(errors: any) {
                    if (errors?.response?.data?.message === "Invalid stage ID") {
                        toast.error("This is the last stage of the pipeline")
                    }
                    else {
                        toast.error("Something went wrong")
                    }
                    closeConfirmProgressSelectedCandidateModal()
                }
            })
        } else {
            toast.error("Select applicants to move")
        }
    }


    const copyJobLink = () => {
        if (typeof navigator !== undefined) {
            navigator.clipboard.writeText(`https://app.getlinked.ai/jobs/${job_unique_id}`)
            toast.success("Job link copied successfully")
        }
    }


    return (
        <Modal is_open={isOpen} close={close} title='Filter applicants'
            portalClass='w-screen flex flex-col !h-[95vh] w-[90vw] max-w-[1750px]'
        >
            <div className='relative grow flex flex-col overflow-y-hidden'>
                {
                    !moveToGroup &&

                    // <header className='flex items-center gap-4 justify-between p-4 lg:px-6 w-full overflow-x-scroll'>
                    <header className='flex items-center gap-4 justify-between p-4 '>
                        <div className='flex items-center gap-2 xl:gap-4'>
                            <div className='relative flex  max-w-full items-center justify-between gap-2 border-2 border-[#D6D6D6] p-0 pl-2 rounded-lg focus-within:border-primary'>
                                <input type="text" value={searchText}
                                    className=' outline-none  text-sm h-full'
                                    onChange={(e) => setSearchText(e.target.value)}
                                    placeholder={`Search applicants ${searchFilters.find(fil => fil.value == searchBy)?.name || 'by name'}`}
                                />
                                <Menu>
                                    {({ open }) => (
                                        <div className="relative min-w-max shrink-0">
                                            <Menu.Button className={`flex min-w-[120px] items-center justify-center gap-x-2 rounded-lg  p-1 px-1.5 text-sm bg-primary-light text-primary`}>
                                                <Filter className='scale-75' fill='#755AE2' />
                                                <p className="text-[0.825rem]">{searchFilters.find(fil => fil.value == searchBy)?.name || 'search by'}</p>
                                                <CaretDown color={'#755AE2'} className={cn("transition-all", open && 'rotate-180')} />
                                            </Menu.Button>
                                            <Menu.Items className='absolute -bottom-2 left-0 z-50 flex min-h-max max-h-[350px] min-w-max max-w-[400px] translate-y-full flex-col rounded-lg border bg-white px-2 py-2'>
                                                <div className={cn("h-full overflow-y-scroll")}>

                                                    {
                                                        searchFilters?.map((option, index: number) => {
                                                            return (
                                                                <div
                                                                    className={cn("flex items-center gap-x-1 rounded-md p-1.5 px-2 text-xs  hover:bg-primary-light cursor-pointer !min-w-max")}
                                                                    key={index}
                                                                    onClick={() => setSearchBy(option.value || "")}
                                                                >
                                                                    <StrokeCheck
                                                                        className={cn("mr h-[1rem] w-[1rem]",
                                                                            searchBy.toLowerCase() == option.value?.toLowerCase() ? "opacity-100" : "opacity-0"
                                                                        )}
                                                                    />
                                                                    <span>

                                                                        {option.name}
                                                                    </span>
                                                                </div>
                                                            );
                                                        })
                                                    }
                                                </div>
                                            </Menu.Items>
                                        </div>)
                                    }
                                </Menu>
                            </div>

                            <div className='flex items-center gap-2 lg:min-w-max'>
                                <JobPipelineStageFilterParameters
                                    setFilterUrl={setFilterUrl}
                                    selectedRoles={selectedRoles}
                                    selectedExperiences={selectedExperiences}
                                    setSelectedExperiences={setSelectedExperiences}
                                    setSelectedRoles={setSelectedRoles}
                                    selectedSKills={selectedSkills}
                                    selectedTools={selectedTools}
                                    setSelectedSkills={setSelectedSkills}
                                    setSelectedTools={setSelectedTools}
                                    setApplicantsToDisplay={setApplicantsToDisplay}
                                    cvRoles={cvRoles}
                                    uniqueSkills={uniqueSkills}
                                    uniqueTools={uniqueTools}

                                    minPercentageMatch={minPercentageMatch}
                                    maxPercentageMatch={maxPercentageMatch}
                                    setMinPercentageMatch={setMinPercentageMatch}
                                    setMaxPercentageMatch={setMaxPercentageMatch}
                                    isSeenValue={isSeenValue}
                                    SetIsSeenValue={setIsSeenValue}
                                />
                            </div>



                        </div>
                        <ToolTip content='Clear all filters' className='ml-auto'>
                            <span className={cn('flex items-center justify-center bg-primary-light-active hover:bg-red-400 !rounded-full mr-2 cursor-pointer',
                                !searchText && !selectedRoles.length && !selectedExperiences.length && minPercentageMatch == 0 && maxPercentageMatch == 100 && isSeenValue == null && "opacity-30"
                            )}
                                onClick={resetAllFilters}>
                                <CloseCircle className="text-[#755AE2]" fill='white' />
                            </span>
                        </ToolTip>
                    </header>
                }

                <div className='grow overflow-y-scroll'>
                    <section className={cn(' grid gap-5 sm:grid-cols-[repeat(auto-fit,minmax(300px,1fr))] overflow-y-scroll p-4 lg:px-6',
                        applicantsToDisplay.length > 0 && applicants.length < 4 && "!grid-cols-[repeat(auto-fit,minmax(300px,320px))]",
                        applicantsToDisplay.length < 1 && "flex items-center justify-center",
                        applicantsToDisplay.length >= 4 && "",

                    )}>

                        {
                            (applicants.length > 0 && applicantsToDisplay.length < 1) &&

                            <EmptyCard
                                title='No applicants found'
                                content={
                                    <div className='flex flex-col items-center justify-center max-w-xl gap-y-4 p-2 rounded-lg text-sm w-full'>
                                        <p>
                                            There are no applicants that match your filter parameters. Change your filter or search parameters and try again.
                                        </p>

                                        <Button size='capsule' className='bg-primary-light-active' variant='extralight' onClick={resetAllFilters} >
                                            Clear All Filters
                                        </Button>
                                    </div>
                                }
                            />

                        }
                        {
                            applicants.length < 1 &&

                            <EmptyCard
                                title='No applicants in stage'
                                content={
                                    <div className='flex flex-col items-center justify-center max-w-xl gap-y-4 p-2 rounded-lg text-sm w-full'>
                                        <p>
                                            There are either no applicants on this stage yet. Share the job link to your potential applicants or move applicants from other stages to get started
                                        </p>

                                        <Button size='capsule' className='bg-primary-light-active' variant='extralight' onClick={copyJobLink} >
                                            Copy job link
                                        </Button>
                                    </div>
                                }
                            />

                        }
                        {
                            applicantsToDisplay?.map((applicant, index) => (
                                <JobPipelineStageApplicantCard
                                    key={index}
                                    applicant={applicant}
                                    allApplicantsId={applicants.map(candidato => candidato['id'])}
                                    allApplicantsData={applicants}
                                    index={index}
                                    currentStage={stage}
                                    jobId={job_id}
                                    jobUniqueId={job_unique_id}
                                    selectedApplicants={selectedApplicants}
                                    setSelectedApplicants={setSelectedApplicants}
                                    refetchJobData={refetch}
                                    refetchPipelineData={refetch}
                                    className='!my-0 shrink-0'
                                    pipelineId={pipeline_id}
                                />
                            ))
                        }
                    </section>
                </div>



                <footer className='sticky bottom-0 flex items-center gap-4 bg-primary-light p-5 rounded-t-xl'>

                    <ToolTip asChild content='Move selected applicants to next stage'>
                        <Button onClick={openConfirmProgressSelectedCandidateModal} disabled={selectedApplicants?.length < 1} className='ml-auto px-6 !py-[0.65rem] text-[0.85rem] '>
                            Move to next stage
                        </Button>
                    </ToolTip>
                    <ToolTip asChild content='Move selected applicants to a filtered CV  group'>
                        <Button onClick={openGroupsModal} disabled={selectedApplicants?.length < 1} className='px-6 !py-[0.65rem] text-[0.85rem] '>
                            Move to a CV group
                        </Button>
                    </ToolTip>
                </footer>
            </div>



            <FilteredGroupsModal
                isModalOpen={isGroupsModalOpen}
                closeModal={closeGroupsModal}
                openModal={openGroupsModal}
                applications={applicants.filter(applicant => selectedApplicants.includes(applicant.id.toString())).map(applicant => applicant.id)}
                job_id={job_id}
            />

            <ConfirmActionModal
                isModalOpen={isConfirmProgressSelectedCandidateModalOpen}
                closeModal={closeConfirmProgressSelectedCandidateModal}
                title="Progress applicants to next stage"
                confirmFunction={progressSelectedCandidates}
            >
                <p className='text-[#8C8CA1] text-sm font-normal'>
                    You are about to progress  <span className='text-header-text font-bold mr-1'>{selectedApplicants.length}</span>
                    {selectedApplicants.length < 2 ? "applicant" : "applicants"} to the next stage,
                    Please be aware that any applicants progressed to a stage with email notification enabled will <span className='text-header-text font-medium mr-1'>receive an email</span> from us even if you move them back later.
                </p>
            </ConfirmActionModal>
        </Modal>
    )
}

export default PreviewMoveCandidates