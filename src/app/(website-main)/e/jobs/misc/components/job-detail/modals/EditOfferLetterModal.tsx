import React, { FormEvent, useCallback, useRef, useEffect, useState } from "react";

import Modal from "@/components/Modal";
import "@/styles/quill.css"

import { useDropzone } from "react-dropzone";
import { blobToBase64 } from "@/lib/utils/functions";
import { Input } from "@/components/shared";
import EditOfferLetterModalRTE from "./EditOfferLetterModalRTE";
import { PipelineColumn } from "../../../types";
import { useCloudinary, useErrorModalState } from "@/hooks";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { singleStageSchema } from "../../../utils/pipelineSchemas";
import { EditSinglePipelineStageType } from "../../../types/PipelineType";
import { useEditPipelineStage } from "../../../api";
import { formatAxiosErrorMessage } from "@/utils/errors";
import { AxiosError } from "axios";
import { useLoading } from "@/lib/contexts/LoadingContext";

type FormDataType = {
  subject: string,
  message: string,
  cover_image: Blob | undefined,
}



interface Props {
  is_open: boolean;
  close: () => void;
  stage: PipelineColumn;
  pipelineId: string | number;

}

const EditOfferLetterModal: React.FC<Props> = ({
  is_open,
  close,
  stage,
  pipelineId

}) => {





  return (
    <Modal title="Customize Offer Letter" close={close} is_open={is_open} portalClass="grid grid-rows-[max-content,1fr,max-content] h-[calc(100vh_-_2rem)] overflow-y-hidden">

      <EditOfferLetterModalRTE
        stage={stage}
        pipelineId={pipelineId}
        closeStagesModal={close}
      />

    </Modal>
  );
};

export default EditOfferLetterModal;
