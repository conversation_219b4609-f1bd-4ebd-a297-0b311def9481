import React, { useEffect, useState } from 'react';
import { SmallSpinner, Spinner } from '@/components/shared/icons';

interface props {
    cvFile: string
}
const ViewPDF: React.FC<props> = ({ cvFile }) => {
    const [isLoading, setLoading] = useState(true);
    const [file, setFile] = useState<string>('');
    useEffect(() => {
        function removeQueryParameters(url: string) {
            const indexPdf = url.indexOf(".pdf");
            if (indexPdf !== -1) {
                return url.substring(0, indexPdf + 4);
            } else {
                return url;
            }
        }

        const cleanedUrl = removeQueryParameters(cvFile);
        setFile(cleanedUrl);
        setLoading(false);  

    }, [cvFile])



    return (
        <>
            {
                isLoading ?
                    <div className='w-full h-full flex items-center justify-center'>
                        <SmallSpinner color="#755AE2" />
                    </div>
                    :
                    <embed className="w-full h-full min-h-700px" src={`${file}`} type="application/pdf"></embed>
            }
            {/* <object className="w-full h-full min-h-700px" onLoad={handleLoad} data={`data:application/pdf;base64,${cvFile}`} type="application/pdf">
                {
                    isLoading && <SmallSpinner />
                }
            </object> */}
        </>
    );
}
export default ViewPDF