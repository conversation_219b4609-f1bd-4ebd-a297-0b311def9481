'use client';

import {
  Checkbox,
  ConfirmActionModal,
  ConfirmDeleteModal,
  LoadingOverlay,
  Popover,
  PopoverContent,
  PopoverTrigger,
  ToolTip,
} from '@/components/shared';
import { Elipsis, Eye, Rotate } from '@/components/shared/icons';
import { useBooleanStateControl } from '@/hooks';
import { useUser } from '@/lib/contexts/UserContext';
import { cn } from '@/utils';
import { convertToTitleCase } from '@/utils/strings';
import { format } from 'date-fns';
import { motion } from 'framer-motion';
import React, { useMemo, useState } from 'react';
import toast from 'react-hot-toast';
import {
  useDeleteCandidates,
  useMoveCandidate,
  useRescoreCandidates,
  useSendAssessmentInvite,
} from '../../api';
import { PipelineApplicant, PipelineColumn } from '../../types';
import { DropIndicator } from './2. JobPipelineStage';
import CandidateDetailsDrawer from './5. JobPipelineCandidateDetails';

interface ItemProps {
  className?: string;
  index: number;
  jobId: number;
  jobUniqueId: string;
  applicant: PipelineApplicant;
  allApplicantsId: number[];
  allApplicantsData: PipelineApplicant[];
  handleDragStart?: (
    e: React.DragEvent<HTMLDivElement>,
    card: PipelineApplicant
  ) => void;
  refetchJobData: () => void;
  selectedApplicants: string[];
  setSelectedApplicants: React.Dispatch<React.SetStateAction<string[]>>;
  refetchPipelineData: () => void;
  nextStageId?: string | number;
  pipelineId: string | number;
  currentStage: PipelineColumn;
}

const JobPipelineStageApplicantCard: React.FC<ItemProps> = ({
  applicant,
  refetchPipelineData,
  allApplicantsId: allApplicantsIdId,
  index,
  jobId,
  selectedApplicants,
  setSelectedApplicants,
  jobUniqueId,
  className,
  currentStage,
  handleDragStart,
}) => {
  const {
    id: applicant_id,
    name,
    email,
    years_of_experience,
    current_location,
    percentage_match,
    created_at,
    matching_reason,
    is_seen,
    trail,
    combined_score,
    interview_overall_score,
  } = applicant;
  const { mutate: moveCandidate, isLoading: isMovingCandidate } =
    useMoveCandidate(jobId);
  const { mutate: removeCandidates, isLoading: isRemovingCandidate } =
    useDeleteCandidates(jobId);
  const { mutate: sendInvite } = useSendAssessmentInvite();
  const { userInfo } = useUser();
  const allApplicantsId = useMemo(() => allApplicantsIdId, []);
  const [localIndex, setLocalIndex] = useState(index);
  React.useEffect(() => {
    setLocalIndex(index);
  }, [index]);

  const {
    state: isDrawerOpen,
    setTrue: openDrawer,
    setFalse: closeDrawer,
  } = useBooleanStateControl(false);
  const {
    state: isConfirmRescoreModalOpen,
    setTrue: openConfirmRescoreModal,
    setFalse: closeConfirmRescoreModal,
  } = useBooleanStateControl(false);
  const {
    state: isConfirmSendAssessmentInvite,
    setTrue: openConfirmSendAssessmentModal,
    setFalse: closeConfirmSendAssessmentModal,
  } = useBooleanStateControl(false);
  const {
    state: isConfirmDeleteCandidateModalOpen,
    setTrue: openConfirmDeleteCandidateModal,
    setFalse: closeConfirmDeleteModal,
  } = useBooleanStateControl(false);
  const {
    state: isConfirmProgressModalOpen,
    setTrue: openConfirmProgressModal,
    setFalse: closeConfirmProgressModal,
  } = useBooleanStateControl(false);

  const { mutate: rescore } = useRescoreCandidates(Number(jobId));
  const rescoreCandidate = () => {
    rescore(
      {
        job_id: Number(jobId),
        job_applications: [applicant_id],
        calculate_all: false,
      },
      {
        onSuccess() {
          toast.success(
            "Candidate's match score is being recalculated, It may take up to 15 minutes for the new score to reflect in the pipeline"
          );
          closeConfirmRescoreModal();
        },
        onError(error) {
          toast.error('Something went wrong');
        },
      }
    );
  };

  const progressCandidate = (fromDrawer?: boolean) => {
    const currentCandidateIndex = allApplicantsId.findIndex(
      applicant => applicant === applicant_id
    );
    const nextCandidate =
      currentCandidateIndex + 1 < allApplicantsId.length
        ? allApplicantsId[currentCandidateIndex + 1]
        : allApplicantsId[0];
    moveCandidate(
      {
        job_unique_id: jobUniqueId,
        job_application_ids: [applicant_id],
        stage_id: currentStage.stage_id + 1,
      },
      {
        onSuccess() {
          toast.success('Candidate Progressed to next stage');
          closeConfirmProgressModal();
          if (fromDrawer) {
            openDrawer();
          }
        },
        onError(errors: any) {
          if (errors?.response?.data?.message === 'Invalid stage ID') {
            toast.error('This is the last stage of the pipeline');
          } else {
            toast.error('Something went wrong');
          }
          closeConfirmProgressModal();
          if (fromDrawer) {
            openDrawer();
          }
        },
      }
    );
  };
  const removeCandidate = () => {
    removeCandidates(
      { job_application_ids: [applicant_id] },
      {
        onSuccess() {
          toast.success('Candidate removed successfully');
          closeConfirmDeleteModal();
        },
      }
    );
  };

  const sendCandidateAssessmentInvite = (fromDrawer?: boolean) => {
    if (currentStage.assessment) {
      const dto = {
        assessment: currentStage.assessment!!,
        candidates: [{ name, email: email, job_application_id: applicant_id }],
        recruiter: userInfo?.id.toString() || '0',
        duration: currentStage.duration || 4,
        is_from_job_post: true,
        is_assessment: true,
        is_interview: false,
      };
      sendInvite(dto, {
        onSuccess() {
          closeConfirmSendAssessmentModal();
          toast.success('Invite sent successfully');
          if (fromDrawer) {
            openDrawer();
          }
        },
      });
    }
  };

  return (
    <>
      {!!handleDragStart && (
        <DropIndicator
          beforeId={applicant.id.toString()}
          column={applicant.current_stage.stage_id}
        />
      )}

      <motion.div
        className={cn(
          'mb-4 grid w-full max-w-[400px] cursor-grab grid-cols-[1fr,max-content] gap-1.5 rounded-xl border-[0.3px] border-[#E3E3E3] bg-white p-4 !text-[0.85rem]',
          selectedApplicants.includes(String(applicant_id)) && 'border-primary',
          className
        )}
        draggable={!!handleDragStart ? 'true' : 'false'}
        layoutId={applicant.id.toString()}
        layout
        onDragStart={e => {
          if (!handleDragStart) return;
          handleDragStart(
            e as unknown as React.DragEvent<HTMLDivElement>,
            applicant
          );
        }}
      >
        <section className="flex flex-col gap-5">
          <header className="grid grid-cols-[1fr,0.8fr] gap-8">
            <h3 className="overflow-hidden text-ellipsis font-semibold">
              <span className="mr-1 text-[0.7rem] font-bold ">
                {index + 1}.
              </span>
              {name && (
                <span className=" whitespace-nowrap">
                  {convertToTitleCase(name)}
                </span>
              )}
            </h3>

            {/* <p
              className={cn(
                'hidden items-center text-left text-[0.7rem] font-normal',
                currentStage.is_assessment && 'flex'
              )}
            >
              <span>Assessment:</span>
              {!matching_reason ? (
                <ToolTip
                  content="The applicant's data has been submitted, please remain patient while the AI scores them."
                  contentClass="text-[0.7rem] !max-w-xs"
                >
                  <p className="ml-1 text-[0.8rem] text-orange-400">
                    scoring...
                  </p>
                </ToolTip>
              ) : (
                <div className="flex flex-col gap-1">
                  <span
                    className={cn(
                      percentage_match <= 20 && 'text-red-500',
                      percentage_match > 20 &&
                        percentage_match <= 40 &&
                        'text-orange-500',
                      percentage_match > 40 &&
                        percentage_match <= 60 &&
                        'text-yellow-500',
                      percentage_match > 60 &&
                        percentage_match < 70 &&
                        'text-blue-500',
                      percentage_match >= 70 && 'text-green-500',
                      'text-[0.8rem] font-medium'
                    )}
                  >
                    CV: {percentage_match}%
                  </span>
                  {combined_score ? (
                    <span
                      className={cn(
                        combined_score <= 20 && 'text-red-500',
                        combined_score > 20 &&
                          combined_score <= 40 &&
                          'text-orange-500',
                        combined_score > 40 &&
                          combined_score <= 60 &&
                          'text-yellow-500',
                        combined_score > 60 &&
                          combined_score < 70 &&
                          'text-blue-500',
                        combined_score >= 70 && 'text-green-500',
                        'text-[0.7rem] font-medium'
                      )}
                    >
                      Combined: {combined_score}%
                    </span>
                  ) : null}
                  {interview_overall_score && (
                    <span
                      className={cn(
                        interview_overall_score <= 20 && 'text-red-500',
                        interview_overall_score > 20 &&
                          interview_overall_score <= 40 &&
                          'text-orange-500',
                        interview_overall_score > 40 &&
                          interview_overall_score <= 60 &&
                          'text-yellow-500',
                        interview_overall_score > 60 &&
                          interview_overall_score < 70 &&
                          'text-blue-500',
                        interview_overall_score >= 70 && 'text-green-500',
                        'text-[0.7rem] font-medium'
                      )}
                    >
                      Interview: {interview_overall_score.toFixed(1)}%
                    </span>
                  )}
                </div>
              )}
              {is_seen && (
                <ToolTip
                  className="ml-1.5"
                  content="Candidate marked as seen"
                  contentClass="text-[0.7rem]"
                >
                  <Eye
                    circlecolor="#00A37D"
                    stroke="white"
                    width={17}
                    height={17}
                  />
                </ToolTip>
              )}
            </p> */}
            <p
              className={cn(
                'flex items-center text-left text-[0.7rem] font-normal',
              )}
            >
              <span>Overall: </span>
              <div className="flex flex-col gap-1">
                <span
                  className={cn(
                    combined_score <= 20 && 'text-red-500',
                    combined_score > 20 &&
                      combined_score <= 40 &&
                      'text-orange-500',
                    combined_score > 40 &&
                      combined_score <= 60 &&
                      'text-yellow-500',
                    combined_score > 60 &&
                      combined_score < 70 &&
                      'text-blue-500',
                    combined_score >= 70 && 'text-green-500',
                    'text-[0.7rem] font-medium'
                  )}
                >
                  {combined_score}%
                </span>
              </div>
              {is_seen && (
                <ToolTip
                  className="ml-1.5"
                  content="Candidate marked as seen"
                  contentClass="text-[0.7rem]"
                >
                  <Eye
                    circlecolor="#00A37D"
                    stroke="white"
                    width={17}
                    height={17}
                  />
                </ToolTip>
              )}
            </p>
          </header>

          <div className="grid grid-cols-[1fr,0.8fr] gap-8 text-[0.7rem]">
            <p className="flex flex-col font-medium text-[#7D8590]">
              CV:{' '}
              <span
                className={cn(
                  'font-medium',
                  (percentage_match || 0) <= 20 && 'text-red-500',
                  (percentage_match || 0) > 20 &&
                    (percentage_match || 0) <= 40 &&
                    'text-orange-500',
                  (percentage_match || 0) > 40 &&
                    (percentage_match || 0) <= 60 &&
                    'text-yellow-500',
                  (percentage_match || 0) > 60 &&
                    (percentage_match || 0) < 70 &&
                    'text-blue-500',
                  (percentage_match || 0) >= 70 && 'text-green-500',
                  'text-[0.7rem] font-medium'
                  // (percentage_match || 0) === 0 && 'text-[#344051]'
                )}
              >
                {percentage_match || 0}%
              </span>
            </p>
            <p className="flex flex-col font-medium text-[#7D8590]">
              Preassessment:{' '}
              <span
                className={cn(
                  'font-medium',
                  (interview_overall_score || 0) <= 20 && 'text-red-500',
                  (interview_overall_score || 0) > 20 &&
                    (interview_overall_score || 0) <= 40 &&
                    'text-orange-500',
                  (interview_overall_score || 0) > 40 &&
                    (interview_overall_score || 0) <= 60 &&
                    'text-yellow-500',
                  (interview_overall_score || 0) > 60 &&
                    (interview_overall_score || 0) < 70 &&
                    'text-blue-500',
                  (interview_overall_score || 0) >= 70 && 'text-green-500',
                  'text-[0.7rem] font-medium'
                  // (interview_overall_score || 0) === 0 && 'text-[#344051]'
                )}
              >
                {interview_overall_score || 0}%
              </span>
            </p>
          </div>

          <div className="grid grid-cols-[1fr,0.8fr] gap-8 text-[0.7rem]">
            <p className="flex flex-col font-medium text-[#7D8590]">
              Experience:{' '}
              <span className="font-medium text-[#344051]">
                {years_of_experience}
              </span>
            </p>
            <p className="flex flex-col truncate text-left font-medium text-[#7D8590]">
              Location:{' '}
              <span className="truncate font-medium text-[#344051]">
                {current_location}
              </span>
            </p>
          </div>

          <div className="grid grid-cols-[1fr,0.875fr] items-center gap-8">
            <div>
              <p className="flex items-center gap-1 text-[0.7rem] text-[#7D8590]">
                Applied -{' '}
                <span className="">
                  {format(new Date(created_at), 'dd/MM/yyyy')}
                </span>
              </p>
              {trail.filter(elem => elem.event == 'progression')?.length >
                1 && (
                <p className="flex items-center gap-1 text-[0.7rem] text-[#7D8590]">
                  Moved -
                  <span className="">
                    {format(
                      new Date(
                        trail[
                          trail.findLastIndex(
                            elem => elem.event == 'progression'
                          )
                        ].timestamp || 0
                      ),
                      'dd/MM/yyyy'
                    )}
                  </span>
                </p>
              )}
            </div>
            <CandidateDetailsDrawer
              applicant_id={applicant_id}
              allApplicantsId={allApplicantsId}
              isDrawerOpen={isDrawerOpen}
              openDrawer={openDrawer}
              closeDrawer={closeDrawer}
              currentStage={currentStage}
              refetchJobData={refetchPipelineData}
              progressCandidate={() => progressCandidate(true)}
              sendAssessmentInvite={() => sendCandidateAssessmentInvite(true)}
            />
          </div>
        </section>

        <section className="ml-auto flex h-full flex-col items-center justify-between">
          <Popover>
            <PopoverTrigger className="px-2">
              <Elipsis />
            </PopoverTrigger>

            <PopoverContent
              className="flex max-w-max flex-col items-stretch p-2"
              align="end"
            >
              <button
                onClick={openConfirmRescoreModal}
                className="rounded-md p-2 text-left text-[0.7rem] font-normal text-header-text hover:bg-primary-light-hover"
              >
                Rescore Candidate
              </button>
              <button
                onClick={openConfirmProgressModal}
                className="rounded-md p-2 text-left text-[0.7rem] font-normal text-header-text hover:bg-primary-light-hover"
              >
                Progress Candidate
              </button>
              <button
                onClick={openConfirmDeleteCandidateModal}
                className="rounded-md p-2 text-left text-[0.7rem] font-normal text-header-text hover:bg-primary-light-hover"
              >
                Remove Candidate
              </button>
              {currentStage.is_assessment && (
                <button
                  onClick={openConfirmSendAssessmentModal}
                  className="rounded-md p-2 text-left text-[0.7rem] font-normal text-header-text hover:bg-primary-light-hover"
                >
                  Send Assessment Invite
                </button>
              )}
            </PopoverContent>
          </Popover>
          <Checkbox
            circle
            className="p-0.5"
            checked={selectedApplicants.includes(applicant_id.toString())}
            onCheckedChange={() => {
              const updatedSelectedCandidates = selectedApplicants.includes(
                applicant_id.toString()
              )
                ? selectedApplicants.filter(
                    id => id !== applicant_id.toString()
                  )
                : [...selectedApplicants, applicant_id.toString()];
              setSelectedApplicants(updatedSelectedCandidates);
            }}
          />
        </section>

        {/* /////////////////////////////////////////////////////// */}
        {/* /////////////////////////////////////////////////////// */}
        <ConfirmActionModal
          isModalOpen={isConfirmRescoreModalOpen}
          closeModal={closeConfirmRescoreModal}
          title="Rescore Candidate"
          confirmFunction={rescoreCandidate}
          icon={<Rotate width={44} height={44} fill="white" />}
        >
          <p className="text-sm font-normal text-[#8C8CA1]">
            You are about to rescore{' '}
            <span className="mr-1 font-bold text-header-text">{name}</span>,
            Please be aware that it can take up to 5 minutes to reflect the new
            score. It is also possible that the score doesn&apos;t change if the
            grading criterias like{' '}
            <span className="mr-1 font-medium text-header-text">
              compulsory requirements, job responsibilities etc..{' '}
            </span>{' '}
            remain the same.
          </p>
        </ConfirmActionModal>

        <ConfirmActionModal
          isModalOpen={isConfirmProgressModalOpen}
          closeModal={closeConfirmProgressModal}
          title="Progress applicant to next stage"
          confirmFunction={progressCandidate}
        >
          <p className="text-sm font-normal text-[#8C8CA1]">
            You are about to progress{' '}
            <span className="mr-1 font-bold text-header-text">{name}</span> to
            the next stage, Please be aware that if the next stage has email
            notification enabled. The applicant will{' '}
            <span className="mr-1 font-medium text-header-text">
              receive an email
            </span>{' '}
            from us even if you move them back later.
          </p>
        </ConfirmActionModal>

        <ConfirmActionModal
          isModalOpen={isConfirmSendAssessmentInvite}
          closeModal={closeConfirmSendAssessmentModal}
          title={`Send assessment invite to ${name}`}
          confirmFunction={sendCandidateAssessmentInvite}
        >
          <div className="text-sm font-normal text-[#8C8CA1]">
            <p>
              Assessment ID:
              <span className="mx-1 mb-2 font-medium text-header-text">
                {currentStage.assessment}
              </span>
            </p>
            <p>
              You are about to send assessment invitation to{' '}
              <span className="mx-1 font-bold text-header-text">{name}</span>.
              Please be aware that all applicants selected at this stage are
              requiredto complete the assessment within the specified time frame
              of{' '}
              <span className="mx-1 font-bold text-header-text">
                {currentStage.duration}
              </span>{' '}
              days upon receiving this invitation.
            </p>
          </div>
        </ConfirmActionModal>

        <ConfirmDeleteModal
          isModalOpen={isConfirmDeleteCandidateModalOpen}
          closeModal={closeConfirmDeleteModal}
          title="Remove Candidate"
          deleteFunction={removeCandidate}
        >
          <div className="text-sm font-normal text-[#8C8CA1]">
            <p>
              You are about to delete{' '}
              <span className="mx-1 font-bold text-header-text">{name}</span>{' '}
              from the pipeline, Please be aware that {name} will be deleted
              from the pipeline but will still be in the applicants tab.
            </p>
          </div>
        </ConfirmDeleteModal>

        <LoadingOverlay isOpen={isMovingCandidate || isRemovingCandidate} />
      </motion.div>
    </>
  );
};

export default JobPipelineStageApplicantCard;
