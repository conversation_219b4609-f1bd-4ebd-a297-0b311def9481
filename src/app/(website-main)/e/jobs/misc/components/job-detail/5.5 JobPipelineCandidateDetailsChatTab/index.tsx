'use client';

import { format } from 'date-fns';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Small<PERSON>pinner, Spinner } from '@/components/shared/icons';
import { useUser } from '@/lib/contexts/UserContext';
import { cn } from '@/utils';
import { ChatMessage, useFetchChatMessagesQuery } from './api';
import MessageInput from './components/MessageInput';
import RealtimeMessage from './components/RealtimeMessage';
import TakeoverControls from './components/TakeoverControls';
import {
  RealtimeChatMessage,
  useRecruiterChat,
} from './hooks/useRecruiterChat';

interface Props {
  unique_id: string;
  applicant_email: string;
  enabled?: boolean;
}

const CandidateDetailsChatTab: React.FC<Props> = ({
  unique_id,
  applicant_email,
  enabled = true,
}) => {
  // Get current user data from authentication context
  const { user, userInfo, isLoading: userLoading } = useUser();
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Unified message state for dual data source handling
  const [unifiedMessages, setUnifiedMessages] = useState<ChatMessage[]>([]);
  const [lastHttpFetchTime, setLastHttpFetchTime] = useState<Date | null>(null);
  const [processedWebSocketMessageIds, setProcessedWebSocketMessageIds] =
    useState<Set<string>>(new Set());
  const [hasInitialHttpFetch, setHasInitialHttpFetch] = useState(false);

  // Extract recruiter information from user context
  const recruiterName = user ? `${user.first_name} ${user.last_name}` : '';
  const recruiterId = user?.id || '';

  // Handle loading state for user data
  if (userLoading) {
    return (
      <div className="flex h-full w-full items-center justify-center p-8">
        <SmallSpinner color="#755AE2" />
        <span className="ml-2 text-sm text-helper-text">
          Loading user data...
        </span>
      </div>
    );
  }

  // Handle case where user is not authenticated or not a recruiter
  if (
    !user ||
    !userInfo ||
    userInfo.user?.user_type !== 'RECRUITER' ||
    !recruiterName.trim() ||
    !recruiterId.trim()
  ) {
    return (
      <div className="flex h-full w-full items-center justify-center p-8">
        <p className="text-sm text-red-500">
          Access denied. This feature is only available to authenticated
          recruiters with complete profile information.
        </p>
      </div>
    );
  }

  // Initialize recruiter chat WebSocket
  const recruiterChat = useRecruiterChat({
    jobUuid: unique_id,
    applicantEmail: applicant_email,
    recruiterName,
    recruiterId,
    onError: setError,
    onTakeoverStatusChange: status => {
      console.log('Takeover status changed:', status);
    },
  });

  // Determine if HTTP refetch should be disabled (when WebSocket is connected and recruiter is active)
  const shouldDisableHttpRefetch =
    recruiterChat.isConnected && recruiterChat.takeoverStatus.isRecruiterActive;

  // Fetch historical chat messages
  const {
    data,
    isLoading,
    isError,
    error: fetchError,
  } = useFetchChatMessagesQuery({
    unique_id,
    applicant_email,
    enabled,
    disableRefetch: shouldDisableHttpRefetch,
  });

  // Helper function to convert RealtimeChatMessage to ChatMessage format for unified display
  const convertRealtimeToChat = useCallback(
    (realtimeMsg: RealtimeChatMessage): ChatMessage => {
      return {
        content: realtimeMsg.content,
        user_type:
          realtimeMsg.type === 'recruiter'
            ? 'human_recruiter'
            : realtimeMsg.type,
        message_type:
          realtimeMsg.messageType === 'recruiter_message'
            ? 'recruiter_message'
            : realtimeMsg.messageType === 'takeover_status'
            ? 'takeover_status'
            : 'text',
        timestamp: realtimeMsg.timestamp.toISOString(),
        recruiter_name: realtimeMsg.recruiterName,
        is_realtime: true,
      };
    },
    []
  );

  // Function to handle HTTP API message updates (replace all messages)
  const handleHttpMessages = useCallback(
    (httpMessages: ChatMessage[]) => {
      // Only process HTTP messages if WebSocket is not active or this is the initial fetch
      if (shouldDisableHttpRefetch && hasInitialHttpFetch) {
        console.log('Skipping HTTP message update - WebSocket is active');
        return;
      }

      setUnifiedMessages(() => [...httpMessages]);
      setLastHttpFetchTime(new Date());
      setHasInitialHttpFetch(true);
      // Reset processed WebSocket message IDs when HTTP data is refreshed
      setProcessedWebSocketMessageIds(new Set());
    },
    [shouldDisableHttpRefetch, hasInitialHttpFetch]
  );

  // Function to handle WebSocket message updates (append new messages only)
  const handleWebSocketMessages = useCallback(
    (wsMessages: RealtimeChatMessage[]) => {
      if (wsMessages.length === 0) return;

      // Only process new messages that haven't been processed yet
      const newMessages = wsMessages.filter(
        msg => !processedWebSocketMessageIds.has(msg.id)
      );

      if (newMessages.length === 0) return;

      console.log('Appending new WebSocket messages:', newMessages);
      const convertedMessages = newMessages.map(convertRealtimeToChat);

      setUnifiedMessages(prev => [...prev, ...convertedMessages]);

      // Update processed message IDs
      setProcessedWebSocketMessageIds(prev => {
        const newSet = new Set(prev);
        newMessages.forEach(msg => newSet.add(msg.id));
        return newSet;
      });
    },
    [convertRealtimeToChat, processedWebSocketMessageIds]
  );

  // Memoized sorted messages and statistics
  const sortedMessages = useMemo(() => {
    return [...unifiedMessages].sort(
      (a, b) =>
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
  }, [unifiedMessages]);

  // Handle HTTP API data updates (replace all messages)
  useEffect(() => {
    if (data?.messages) {
      handleHttpMessages(data.messages);
    }
  }, [data?.messages, handleHttpMessages]);

  // Handle WebSocket message updates (append new messages)
  useEffect(() => {
    if (recruiterChat.realtimeMessages.length > 0) {
      handleWebSocketMessages(recruiterChat.realtimeMessages);
    }
  }, [recruiterChat.realtimeMessages, handleWebSocketMessages]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [sortedMessages]);

  // Connect to WebSocket when component mounts
  useEffect(() => {
    if (enabled && unique_id && applicant_email) {
      recruiterChat.connect();
    }
    return () => {
      recruiterChat.disconnect();
    };
  }, [enabled, unique_id, applicant_email]);

  const messageStats = useMemo(() => {
    const httpMessages = unifiedMessages.filter(msg => !msg.is_realtime);
    const realtimeMessages = unifiedMessages.filter(msg => msg.is_realtime);
    return {
      total: unifiedMessages.length,
      http: httpMessages.length,
      realtime: realtimeMessages.length,
    };
  }, [unifiedMessages]);

  if (isLoading) {
    return (
      <div className="flex h-full w-full items-center justify-center p-8">
        <Spinner color="#755AE2" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex h-full w-full items-center justify-center p-8">
        <p className="text-sm text-red-500">
          Error loading chat messages: {String(fetchError) || 'Unknown error'}
        </p>
      </div>
    );
  }

  const formatTimestamp = (timestamp: string) => {
    try {
      return format(new Date(timestamp), 'MMM d, yyyy h:mm aaaa');
    } catch {
      return timestamp;
    }
  };

  const renderHistoricalMessage = (message: ChatMessage, index: number) => {
    const isAI = message.user_type === 'ai';
    const isRecruiter =
      message.user_type === 'recruiter' ||
      message.user_type === 'human_recruiter';
    const isApplicant = message.user_type === 'applicant';
    const isCVUpload = message.message_type === 'cv_upload';
    const isTakeoverMessage =
      message.message_type === 'takeover_status' ||
      message.message_type === 'takeover_start';

    // Special rendering for takeover status messages
    if (isTakeoverMessage) {
      return (
        <div
          key={`historical-${index}`}
          className="mb-4 flex w-full justify-center"
        >
          <div className="max-w-[80%] rounded-lg border border-blue-200 bg-blue-50 px-4 py-2 text-center">
            <div className="flex items-center justify-center gap-2 text-sm text-blue-800">
              <div className="flex h-4 w-4 items-center justify-center">
                <span className="text-xs">🔄</span>
              </div>
              <span className="font-medium">{message.content}</span>
            </div>
            <div className="mt-1 text-xs text-blue-600">
              {formatTimestamp(message.timestamp)}
            </div>
          </div>
        </div>
      );
    }

    return (
      <div
        key={`historical-${index}`}
        className={cn(
          'mb-4 flex w-full',
          isAI || isApplicant ? 'justify-start' : 'justify-end'
        )}
      >
        <div
          className={cn(
            'max-w-[80%] rounded-lg px-4 py-3 shadow-sm',
            isAI
              ? 'border border-primary/20 bg-primary-light text-primary'
              : isRecruiter
              ? 'border border-purple-200 bg-purple-100 text-purple-900'
              : 'border border-gray-200 bg-gray-100 text-header-text'
          )}
        >
          {/* Message content */}
          <div className="mb-2">
            {isCVUpload ? (
              <div className="flex items-center gap-2">
                <div className="flex h-4 w-4 items-center justify-center rounded-full bg-green-500">
                  <span className="text-xs text-white">✓</span>
                </div>
                <span className="text-sm font-medium">
                  CV uploaded successfully
                </span>
              </div>
            ) : (
              <p className="whitespace-pre-wrap text-sm">{message.content}</p>
            )}
          </div>

          {/* Timestamp and user type */}
          <div className="flex items-center justify-between text-xs opacity-70">
            <div className="flex items-center gap-1">
              {/* User type icon */}
              <div className="flex h-4 w-4 items-center justify-center">
                {isAI ? (
                  <span className="text-xs">🤖</span>
                ) : isRecruiter ? (
                  <span className="text-xs">👨‍💼</span>
                ) : (
                  <span className="text-xs">👤</span>
                )}
              </div>
              <span className="font-medium">
                {isAI
                  ? 'AI Assistant'
                  : isRecruiter
                  ? message.recruiter_name || 'Human Recruiter'
                  : 'Applicant'}
              </span>
              {/* Message type indicator for recruiter messages */}
              {isRecruiter &&
                message.message_type &&
                message.message_type !== 'text' && (
                  <span className="ml-1 rounded bg-black/10 px-1 py-0.5 text-[10px]">
                    {message.message_type.replace('_', ' ')}
                  </span>
                )}
            </div>
            <span>{formatTimestamp(message.timestamp)}</span>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="flex h-full flex-col">
      {/* Takeover Controls */}
      <TakeoverControls
        takeoverStatus={recruiterChat.takeoverStatus}
        isConnected={recruiterChat.isConnected}
        isConnecting={recruiterChat.isConnecting}
        onJoinConversation={recruiterChat.joinConversation}
        onLeaveConversation={recruiterChat.leaveConversation}
      />
      {/* Chat Content */}
      <div className="flex-1 overflow-hidden">
        <section className="h-full max-w-full p-4 md:px-8 md:py-6">
          <div className="flex h-full flex-col">
            {/* Chat header */}
            <div className="mb-1 border-b border-gray-200 pb-2">
              {data && (
                <div className="flex flex-wrap gap-4 text-sm text-helper-text">
                  <span>Total Messages: {unifiedMessages.length}</span>
                  {data.cv_processed && (
                    <span className="text-green-600">CV Processed</span>
                  )}
                  {data.cv_score && <span>CV Score: {data.cv_score}%</span>}
                  {/* Add interview scoring information if available */}
                  {data.interview_overall_score && (
                    <span className="text-primary">
                      Interview Score: {data.interview_overall_score.toFixed(1)}
                      %
                    </span>
                  )}
                  {data.combined_score && (
                    <span className="text-blue-600">
                      Overall Score: {data.combined_score}%
                    </span>
                  )}
                </div>
              )}
              {error && (
                <div className="mt-2 rounded-lg bg-red-50 p-2 text-sm text-red-600">
                  {error}
                </div>
              )}
            </div>

            {/* Messages Container */}
            <div className="flex-1 overflow-y-auto">
              {/* Unified Messages */}
              {sortedMessages.length > 0 && (
                <div className="space-y-4">
                  {sortedMessages.map((message, index) => {
                    // Render realtime messages using RealtimeMessage component if they have is_realtime flag
                    if (message.is_realtime) {
                      // Convert back to RealtimeChatMessage format for RealtimeMessage component
                      const realtimeMessage: RealtimeChatMessage = {
                        id: `realtime-${index}`,
                        type:
                          message.user_type === 'recruiter' ||
                          message.user_type === 'human_recruiter'
                            ? 'recruiter'
                            : (message.user_type as 'ai' | 'applicant'),
                        content: message.content,
                        timestamp: new Date(message.timestamp),
                        messageType: message.message_type as any,
                        recruiterName: message.recruiter_name,
                      };
                      return (
                        <RealtimeMessage
                          key={`realtime-${index}`}
                          message={realtimeMessage}
                          isLatest={index === sortedMessages.length - 1}
                        />
                      );
                    }
                    // Render historical messages using the existing function
                    return renderHistoricalMessage(message, index);
                  })}
                </div>
              )}

              {/* Empty state */}
              {sortedMessages.length === 0 && (
                <div className="flex items-center justify-center py-12">
                  <p className="text-sm text-helper-text">
                    No chat messages available. Take over the conversation to
                    start messaging.
                  </p>
                </div>
              )}

              {/* Scroll anchor */}
              <div ref={messagesEndRef} />
            </div>
          </div>
        </section>
      </div>
      {/* Message Input */}
      <MessageInput
        onSendMessage={recruiterChat.sendMessage}
        disabled={
          !recruiterChat.takeoverStatus.isRecruiterActive ||
          !recruiterChat.isConnected
        }
        isTyping={recruiterChat.isTyping}
        placeholder={
          recruiterChat.takeoverStatus.isRecruiterActive
            ? 'Type your message to the candidate...'
            : 'Take over the conversation to send messages'
        }
      />
    </div>
  );
};

export default CandidateDetailsChatTab;
