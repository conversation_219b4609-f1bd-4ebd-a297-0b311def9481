import React from 'react'
import { SmallSpinner } from '@/components/shared/icons'
import ViewDOCX from './ViewDOCX'
import ViewPDF from './ViewPDF'

interface Props {
    isLoadingCVFile: boolean
    cvFileName: string
    cvFile: string | null | undefined

}

const CandidateDetailsDocumentsTab: React.FC<Props> = ({ isLoadingCVFile, cvFileName, cvFile }) => {
    return (
        <>
            {
                isLoadingCVFile ?
                    <div className="flex items-center justify-center w-full h-full">
                        <SmallSpinner color="#755AE2" />
                    </div>
                    :
                    !isLoadingCVFile && cvFile && cvFileName?.toLowerCase().includes(".pdf") ?
                        <ViewPDF cvFile={cvFile} />
                        :
                        !isLoadingCVFile && cvFile && cvFileName?.toLowerCase().includes(".docx") ?
                            <ViewDOCX cvFile={cvFile} />
                            :
                            <p>NO CV FILE</p>
            }
        </>
    )
}

export default CandidateDetailsDocumentsTab