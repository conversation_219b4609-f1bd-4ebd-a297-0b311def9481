import React, {
  Dispatch,
  FC,
  SetStateAction,
  useEffect,
  useRef,
  useState,
} from 'react';
import toast from 'react-hot-toast';
import { Button, Input } from '@/components/shared';
import { useBooleanStateControl } from '@/hooks';
import { cn } from '@/utils';
import { useMoveCandidate } from '../../api';
import { XIcon } from '../../icons';
import {
  customRequirement,
  PipelineApplicant,
  PipelineColumn,
} from '../../types';
import { convertPipelineColumnToStage } from '../../utils/functions';
import { EditSinglePipelineStage } from '../modals';
import { OfferLetterUpdateSuccessModal } from '../modals/OfferLetterUpdateSuccssModal';
import { OnbordingConfimationModal } from '../modals/OnbordingConfimationModal';
import { RejectionLetterSuccessModal } from '../modals/RejectionLetterSuccessModal';
import JobPipelineStageHeader from './3. JobPipelineStageHeader';
import JobPipelineStageApplicantCard from './4. JobPipelineCandidate';
import { PreviewMoveCandidates } from './modals';

interface JobPipelineStageProps {
  jobId: number;
  jobUniqueId: string;
  filterUrl: string;
  setFilterUrl: Dispatch<SetStateAction<string>>;
  cvRoles: string[];
  uniqueSkills: string[];
  uniqueTools: string[];
  jobCustomRequirements: customRequirement[];

  applicants: PipelineApplicant[];
  column: number;
  setApplicants: React.Dispatch<React.SetStateAction<PipelineApplicant[]>>;
  pipelineId: string | number;
  refetchPipelineData: () => void;
  thisStage: PipelineColumn;
  allStages: PipelineColumn[];
}

interface DropIndicatorProps {
  beforeId: string | null;
  column: number;
}

export const DropIndicator: FC<DropIndicatorProps> = ({ beforeId, column }) => {
  return (
    <div
      className="my-0.5 h-0.5 w-full bg-primary opacity-0"
      data-before={beforeId || '-1'}
      data-column={column}
    />
  );
};

const JobPipelineStage: React.FC<JobPipelineStageProps> = ({
  thisStage,
  applicants,
  setApplicants,
  allStages,
  column,
  jobId,
  pipelineId,
  refetchPipelineData,
  filterUrl,
  setFilterUrl,
  cvRoles,
  uniqueSkills,
  uniqueTools,
  jobCustomRequirements,
  jobUniqueId,
}) => {
  /////////////////////////////////////////////////////////////////////////////////////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////
  //////////////////                DND FUNCTIONS AND STATES                     //////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////

  const [active, setActive] = useState(false);

  const getIndicators = () => {
    return Array.from(
      document.querySelectorAll<HTMLElement>(`[data-column="${column}"]`)
    );
  };

  const clearHighlights = (els?: HTMLElement[]) => {
    const indicators = els || getIndicators();

    indicators.forEach(i => {
      i.style.opacity = '0';
    });
  };
  const getNearestIndicator = (
    e: React.DragEvent<HTMLDivElement>,
    indicators: HTMLElement[]
  ) => {
    const DISTANCE_OFFSET = 50;

    const el = indicators.reduce(
      (closest, child) => {
        const box = child.getBoundingClientRect();

        const offset = e.clientY - (box.top + DISTANCE_OFFSET);

        if (offset < 0 && offset > closest.offset) {
          return { offset: offset, element: child };
        } else {
          return closest;
        }
      },
      {
        offset: Number.NEGATIVE_INFINITY,
        element: indicators[indicators.length - 1],
      }
    );

    return el;
  };

  const highlightIndicator = (e: React.DragEvent<HTMLDivElement>) => {
    const indicators = getIndicators();

    clearHighlights(indicators);

    const el = getNearestIndicator(e, indicators);

    el.element.style.opacity = '1';
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    highlightIndicator(e);
    setActive(true);
  };
  const handleDragStart = (
    e: React.DragEvent<HTMLDivElement>,
    card: PipelineApplicant
  ) => {
    e.dataTransfer.setData('cardId', card.id.toString());
  };

  const handleDragEnd = (e: React.DragEvent<HTMLDivElement>) => {
    const cardId = e.dataTransfer.getData('cardId');

    setActive(false);
    clearHighlights();

    const indicators = getIndicators();
    const { element } = getNearestIndicator(e, indicators);

    const before = element.dataset.before || '-1';

    if (before !== cardId) {
      let copy = [...applicants];

      let cardToTransfer = copy.find(c => c.id.toString() === cardId);
      if (!cardToTransfer) return;

      // Update the order's stage to the new column
      const newStage = allStages.find(stage => stage.stage_id === column);
      if (newStage) {
        cardToTransfer = {
          ...cardToTransfer,
          current_stage: {
            ...cardToTransfer.current_stage,
            stage_name: newStage.stage_name,
            stage_id: column,
          },
        };
      }

      copy = copy.filter(c => c.id.toString() !== cardId.toString());

      const moveToBack = before === '-1';

      if (moveToBack) {
        copy.push(cardToTransfer);
      } else {
        const insertAtIndex = copy.findIndex(el => el.id.toString() === before);
        if (insertAtIndex === -1) return;

        copy.splice(insertAtIndex, 0, cardToTransfer);
      }

      setApplicants(copy);

      if (newStage) {
        moveApplicantToNextStage(
          {
            stage_id: newStage.stage_id,
            job_application_ids: [cardId],
            job_unique_id: jobUniqueId,
          },
          {
            onSuccess() {
              refetchPipelineData();
              toast.success('Applicant moved successfully');
            },
          }
        );
      }
    }
  };

  const handleDragLeave = () => {
    clearHighlights();
    setActive(false);
  };

  const searchTextBoxRef = useRef<HTMLInputElement | null>(null);
  const [searchText, setSearchText] = React.useState('');
  const [currentStageApplicants, setCurrentStageApplicants] = useState<
    PipelineApplicant[]
  >(
    applicants.filter(
      applicant => applicant.current_stage.stage_id === thisStage.stage_id
    )
  );
  const [searchBy, setSearchBy] = React.useState('name');

  const filteredApplicants = React.useMemo(() => {
    if (searchText.trim() === '') {
      return currentStageApplicants;
    }

    if (
      searchBy === 'name' ||
      searchBy === 'cv_role' ||
      searchBy === 'years_of_experience' ||
      searchBy === 'email'
    ) {
      return currentStageApplicants.filter(candidate => {
        const lowerSearchText = searchText.toLowerCase();
        return (candidate as any)[searchBy]
          ?.toString()
          .toLowerCase()
          .includes(lowerSearchText);
      });
    } else {
      return currentStageApplicants.filter(candidate => {
        const lowerSearchText = searchText.toLowerCase();
        const filteredData = candidate.custom_job_requirements.filter(
          req =>
            req.custom_field_name === searchBy &&
            req.custom_text?.toLowerCase().includes(lowerSearchText)
        );
        return filteredData.length > 0;
      });
    }
  }, [searchText, searchBy, currentStageApplicants]);

  useEffect(() => {
    setApplicantsToDisplay(filteredApplicants);
  }, [filteredApplicants]);
  const [applicantsToDisplay, setApplicantsToDisplay] = useState<
    PipelineApplicant[]
  >(
    currentStageApplicants ||
      applicants.filter(
        order => order.current_stage.stage_id === thisStage.stage_id
      )
  );
  const [selectedApplicants, setSelectedApplicants] = React.useState<string[]>(
    []
  );
  const {
    stage_id,
    stage_name,
    move_criteria,
    is_assessment,
    is_interview,
    is_offer_stage,
    interview,
    order,
    assessment,
    count,
    data,
    duration,
    automation_enabled,
    email_notification_enabled,
    email_body,
    email_subject,
    email_text,
  } = thisStage || ({} as PipelineColumn);

  const currentStage =
    allStages.find(stage => stage.stage_id === column) ||
    ({} as PipelineColumn);
  const nextStageId = allStages[currentStage?.order || 0]?.stage_id;

  /////////////////////////////////////////////////////////////////////////////////////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////
  //////////////////                          BOOLEAN STATES                     //////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////

  const {
    state: isEditStageModalOpen,
    setTrue: openEditStageModal,
    setFalse: closeEditStageModal,
  } = useBooleanStateControl();
  const {
    state: isSearchBoxOpen,
    setFalse: closeSearchBox,
    toggle: toggleSearchBox,
  } = useBooleanStateControl();
  const {
    state: isPreviewModalOpen,
    setTrue: openPreviewModal,
    setFalse: closePreviewModal,
  } = useBooleanStateControl();
  const {
    state: moveToGroupState,
    setTrue: confirmMoveToGroup,
    setFalse: rejectMoveToGroup,
  } = useBooleanStateControl();

  const { mutate: moveApplicantToNextStage, isLoading: isMovingOders } =
    useMoveCandidate(jobId);
  const qualifiedApplicants = currentStageApplicants?.filter(
    candidate => candidate.percentage_match >= move_criteria
  ).length;

  /////////////////////////////////////////////////////////////////////////////////////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////
  //////////////////                          SEARCH AND FILTERS                     //////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////
  /////////////////////////////////////////////////////////////////////////////////////////////////////

  useEffect(() => {
    setFilterUrl(filterUrl);
  }, [filterUrl]);

  const mainFilters = [
    { name: 'name', value: 'name' },
    { name: 'email', value: 'email' },
    { name: 'CV role', value: 'cv_role' },
  ];
  const additionalFilters = jobCustomRequirements
    .filter(requirement => requirement.fieldType === 'TEXT_INPUT')
    .map(({ fieldTitle, fieldValue }) => ({
      name: fieldTitle,
      value: fieldValue,
    }));

  const searchFilters = [...mainFilters, ...additionalFilters];

  React.useEffect(() => {
    setCurrentStageApplicants(
      applicants.filter(
        applicant => applicant.current_stage.stage_id === currentStage.stage_id
      )
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [applicants]);

  React.useEffect(() => {
    if (isSearchBoxOpen && searchTextBoxRef.current) {
      searchTextBoxRef.current.focus();
    }
  }, [isSearchBoxOpen]);

  useEffect(() => {
    if (isSearchBoxOpen && searchTextBoxRef.current) {
      searchTextBoxRef.current.focus();
    }
  }, [isSearchBoxOpen]);

  const MemoizedInitialApplicants = React.useMemo(() => {
    return currentStageApplicants?.map(candidato => candidato['id']);
  }, []);
  const [initialApplicants, setInitialApplicants] = React.useState<number[]>(
    MemoizedInitialApplicants
  );
  const [currentApplicantsPool, setCurrentApplicantsPool] = React.useState<
    number[]
  >(MemoizedInitialApplicants);

  useEffect(() => {
    const initialApplicants = currentStageApplicants?.map(
      candidato => candidato['id']
    );
    setInitialApplicants(initialApplicants);
  }, []);

  const stageHasNewApplicant = () => {
    const currentPool = currentStageApplicants?.map(candidate => candidate.id);
    const difference = currentPool.filter(
      candidate => !initialApplicants.includes(candidate)
    );

    if (difference.length && difference.length > 0) {
      return true;
    } else {
      false;
    }
  };

  // MODAL CONTROL
  const {
    state: offerLetterConfigurationConfirmationModal,
    setTrue: openOfferLetterConfigurationConfirmationModal,
    setFalse: closeOfferLetterConfigurationConfirmationModal,
  } = useBooleanStateControl();

  const {
    state: isOfferConfigurationModalOpen,
    setTrue: openOfferConfigurationModal,
    setFalse: closeOfferConfigurationModal,
  } = useBooleanStateControl();

  // Rejection Boolean State
  const {
    state: rejectionConfigSuccessModal,
    setTrue: openRejectionConfigSuccessModal,
    setFalse: closeRejectionConfigSuccessModal,
  } = useBooleanStateControl();

  const {
    state: isRejectionConfigurationOpen,
    setTrue: openRejectionConfigurationModal,
    setFalse: closeRejectionConfigurationModal,
  } = useBooleanStateControl();

  // Onboading Bool State
  const {
    state: isOnboardingConfigurationOpen,
    setTrue: openOnboardingConfigurationModal,
    setFalse: closeOnboardingConfigurationModal,
  } = useBooleanStateControl();

  const {
    state: onboardingEnabledSuccessModal,
    setTrue: openOnboardingEnabledSuccessModal,
    setFalse: closeOnboardingEnabledSuccessModal,
  } = useBooleanStateControl();

  useEffect(() => {
    if (isSearchBoxOpen && searchTextBoxRef.current) {
      searchTextBoxRef.current.focus();
    }
  }, [isSearchBoxOpen]);

  return (
    <div
      className={cn(
        ' mt-0 flex h-full  w-max min-w-[200px] max-w-[425px] !shrink-0 flex-col gap-2 overflow-y-scroll  rounded-2xl px-2 pt-1 transition-colors  lg:w-[400px] max-md:min-w-[90vw]',
        active ? 'bg-main-bg' : 'bg-neutral-800/0'
      )}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDragEnd}
    >
      <JobPipelineStageHeader
        selectedApplicants={selectedApplicants}
        qualifiedApplicants={qualifiedApplicants}
        stageHasNewApplicant={stageHasNewApplicant}
        applicantsToDisplay={applicantsToDisplay}
        initialApplicants={initialApplicants}
        setInitialApplicants={setInitialApplicants}
        setSelectedApplicants={setSelectedApplicants}
        toggleSearchBox={toggleSearchBox}
        searchText={searchText}
        job_id={jobId}
        job_unique_id={jobUniqueId}
        confirmMoveToGroup={confirmMoveToGroup}
        openEditStageModal={openEditStageModal}
        stage={thisStage}
        filterUrl={filterUrl}
        pipelineId={pipelineId}
        data={currentStageApplicants}
        openPreviewModal={openPreviewModal}
        openOfferConfigurationModal={openOfferConfigurationModal}
        isOpenOfferConfigurationModal={isOfferConfigurationModalOpen}
        closeOfferConfigurationModal={closeOfferConfigurationModal}
        openRejectionConfigurationModal={openRejectionConfigurationModal}
        isOpenRejectionConfigurationModal={isRejectionConfigurationOpen}
        closeRejectionConfigurationModal={closeRejectionConfigurationModal}
        openOnboardingConfigurationModal={openOnboardingConfigurationModal}
        isOpenOnboardingConfigurationModal={isOnboardingConfigurationOpen}
        closeOnboardingConfigurationModal={closeOnboardingConfigurationModal}
      />

      {isSearchBoxOpen && (
        <div className="relative grid max-w-full grid-cols-[1fr,max-content] items-center justify-between gap-0 overflow-hidden rounded-lg border-[1.6px] border-[#D6D6D6] p-0 focus-within:border-primary">
          <Input
            className=" px-2  py-1 text-sm outline-none focus:border-0 focus:outline-none focus:!ring-0"
            placeholder="start typing"
            ref={searchTextBoxRef}
            type="text"
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
          />

          <Button
            className="h-full w-full rounded-none bg-[#F9FAFB] p-0 px-1 hover:bg-red-300"
            variant="unstyled"
            onClick={() => {
              closeSearchBox();
              setSearchText('');
            }}
          >
            <XIcon width={16} />
          </Button>
        </div>
      )}

      <section className="flex grow flex-col overflow-y-scroll rounded-b-xl bg-[#F8F9FB] p-4">
        {applicantsToDisplay.map((c, index) => {
          return (
            <JobPipelineStageApplicantCard
              index={index}
              allApplicantsData={applicants}
              allApplicantsId={applicants.map(order => order.id)}
              currentStage={currentStage}
              handleDragStart={handleDragStart}
              key={c.id}
              nextStageId={nextStageId!}
              applicant={c}
              pipelineId={pipelineId}
              refetchPipelineData={refetchPipelineData}
              refetchJobData={() => {}}
              selectedApplicants={selectedApplicants}
              setSelectedApplicants={setSelectedApplicants}
              jobId={jobId}
              jobUniqueId={jobUniqueId}
            />
          );
        })}
      </section>

      <DropIndicator beforeId={null} column={column} />

      {/* ////////////////////////////////////////////////////////////////////////////////////// */}
      {/* ////////////////////////////////////////////////////////////////////////////////////// */}
      {/* ////////////////////////////////////////////////////////////////////////////////////// */}
      {/* //////////////                           MODALS                           //////////// */}
      {/* ////////////////////////////////////////////////////////////////////////////////////// */}
      {/* ////////////////////////////////////////////////////////////////////////////////////// */}
      {/* ////////////////////////////////////////////////////////////////////////////////////// */}

      <PreviewMoveCandidates
        applicants={applicantsToDisplay}
        isOpen={isPreviewModalOpen}
        close={closePreviewModal}
        job_id={jobId}
        stage={currentStage}
        refetch={refetchPipelineData}
        job_unique_id={jobUniqueId}
        filterUrl={filterUrl}
        setFilterUrl={setFilterUrl}
        cvRoles={cvRoles}
        uniqueSkills={uniqueSkills}
        uniqueTools={uniqueTools}
        searchFilters={searchFilters}
        moveToGroup={moveToGroupState}
        pipeline_id={pipelineId}
      />

      <EditSinglePipelineStage
        stage={convertPipelineColumnToStage(currentStage)}
        closeStagesModal={closeEditStageModal}
        isEditStageModalOpen={isEditStageModalOpen}
        pipelineId={parseInt(pipelineId.toString())}
        refetchData={refetchPipelineData}
        openOfferLetterConfigurationConfirmationModal={
          openOfferLetterConfigurationConfirmationModal
        }
        openRejectionConfigSuccessModal={openRejectionConfigSuccessModal}
        openOnboardingEnabledSuccessModal={openOnboardingEnabledSuccessModal}
      />

      {/* SUCCESS NODALS */}
      <OfferLetterUpdateSuccessModal
        configureNow={() => {
          openOfferConfigurationModal();
          closeOfferLetterConfigurationConfirmationModal();
        }}
        isOpen={offerLetterConfigurationConfirmationModal}
        // isOpen={true}
        closeModal={closeOfferLetterConfigurationConfirmationModal}
      />

      <RejectionLetterSuccessModal
        configureNow={() => {
          openRejectionConfigurationModal();
          closeRejectionConfigSuccessModal();
        }}
        isOpen={rejectionConfigSuccessModal}
        closeModal={closeRejectionConfigSuccessModal}
      />

      <OnbordingConfimationModal
        configureNow={() => {
          openOnboardingConfigurationModal();
          closeOnboardingEnabledSuccessModal();
        }}
        isOpen={onboardingEnabledSuccessModal}
        closeModal={closeOnboardingEnabledSuccessModal}
      />
    </div>
  );
};

export default JobPipelineStage;

///TODO
/////Create array, currenyly moving/manipulating - maipulation_type?optional
///// use effect on isfetching - once isfetching is complete, empty array
///// use array state to mark candidates as currently moving/deleting/manipulating - opacity and spinner
