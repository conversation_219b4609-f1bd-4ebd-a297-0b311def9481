import React from 'react'
import { CandidateDetails } from "../../types/PipelineType"
import Timeline from '@/app/(website-main)/e/jobs/misc/components/job-detail/Timeline'
import { useGetApplicantAssessmentResult } from '../../api'
import { SmallSpinner } from '@/components/shared/icons'
import { format } from 'date-fns'

interface Props {
    enabled: boolean
    id: number
}

const CandidateDetailsAssessmentsTab: React.FC<Props> = ({ enabled, id }) => {

    const dataDTO = { enabled, candidate: id }
    const { data: applicant, isLoading } = useGetApplicantAssessmentResult(dataDTO)


    return (
        <section className="max-w-full p-4 md:py-6 md:px-8">
            {
                isLoading ?
                    <SmallSpinner />
                    :
                    <section className='flex flex-col gap-8'>
                        <article className='p-4 md:px-6 rounded-xl bg-primary-light text-sm'>
                            <h4 className='font-semibold'>Assessment Details</h4>

                            <p className="flex flex-col my-2">
                                <span className="text-header-text text-[0.812] font-medium">Name</span>
                                <span className="text-body-text text-sm">{applicant?.assessment.name}</span>
                            </p>
                            <p className="flex flex-col">
                                <span className="text-header-text text-[0.812] font-medium">Description</span>
                                <span className=" text-body-text text-sm ">{applicant?.assessment.description}</span>
                            </p>
                        </article>

                        <div className="md:grid grid-cols-2 gap-5 overflow-scroll h-full">

                            <p className="flex flex-col">
                                <span className="text-body-text text-xs">Name</span>
                                <span className="text-header-text text-sm font-semibold">{applicant?.candidate_name}</span>
                            </p>
                            <p className="flex flex-col">
                                <span className="text-body-text text-xs">Email</span>
                                <span className="text-header-text text-sm font-semibold">{applicant?.candidate_email}</span>
                            </p>
                            <p className="flex flex-col">
                                <span className="text-body-text text-xs">Total Score</span>
                                <span className="text-header-text text-sm font-semibold">{applicant?.total_score}</span>
                            </p>
                            <p className="flex flex-col">
                                <span className="text-body-text text-xs">Score Percentage</span>
                                <span className="text-header-text text-sm font-semibold">{applicant?.overall_percentage}%</span>
                            </p>
                            <p className="flex flex-col">
                                <span className="text-body-text text-xs">Date Started</span>
                                <span className="text-header-text text-sm font-semibold">{applicant?.date_taken && format(applicant?.date_taken!, "dd/MMMM/yyyy")}</span>
                            </p>
                            <p className="flex flex-col">
                                <span className="text-body-text text-xs">Status</span>
                                <span className="text-header-text text-sm font-semibold">{applicant?.status}</span>
                            </p>
                        </div>
                    </section>
            }
        </section>
    )
}

export default CandidateDetailsAssessmentsTab