'use client';

import React, { useState } from 'react';
import { useRecruiterChat } from '../hooks/useRecruiterChat';

interface RecruiterChatTestProps {
  jobUuid: string;
  applicantEmail: string;
}

const RecruiterChatTest: React.FC<RecruiterChatTestProps> = ({
  jobUuid,
  applicantEmail,
}) => {
  const [testLogs, setTestLogs] = useState<string[]>([]);
  const [isTestRunning, setIsTestRunning] = useState(false);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  const recruiterChat = useRecruiterChat({
    jobUuid,
    applicantEmail,
    recruiterName: 'Test Recruiter',
    recruiterId: 'test_recruiter_123',
    onError: error => {
      addLog(`❌ Error: ${error}`);
    },
    onTakeoverStatusChange: status => {
      addLog(`🔄 Takeover status changed: ${JSON.stringify(status)}`);
    },
  });

  const runConnectionTest = async () => {
    setIsTestRunning(true);
    addLog('🚀 Starting connection test...');

    try {
      // Test connection
      addLog('📡 Attempting to connect...');
      recruiterChat.connect();

      // Wait for connection
      await new Promise(resolve => setTimeout(resolve, 2000));

      if (recruiterChat.isConnected) {
        addLog('✅ Connection successful');

        // Test takeover
        addLog('🎯 Testing takeover...');
        recruiterChat.joinConversation(
          'Testing recruiter takeover functionality'
        );

        await new Promise(resolve => setTimeout(resolve, 1000));

        // Test message sending
        addLog('💬 Testing message sending...');
        recruiterChat.sendMessage(
          'Hello! This is a test message from the recruiter.'
        );

        await new Promise(resolve => setTimeout(resolve, 1000));

        // Test handover
        addLog('🔄 Testing handover...');
        recruiterChat.leaveConversation('Test completed. Handing back to AI.');

        await new Promise(resolve => setTimeout(resolve, 1000));

        addLog('✅ All tests completed successfully');
      } else {
        addLog('❌ Connection failed');
      }
    } catch (error) {
      addLog(`❌ Test failed: ${error}`);
    } finally {
      setIsTestRunning(false);
    }
  };

  const clearLogs = () => {
    setTestLogs([]);
  };

  const disconnectTest = () => {
    addLog('🔌 Disconnecting...');
    recruiterChat.disconnect();
  };

  return (
    <div className="rounded-lg border border-gray-200 bg-white p-6">
      <h3 className="mb-4 text-lg font-semibold text-gray-900">
        Recruiter Chat Test Console
      </h3>

      {/* Test Configuration */}
      <div className="mb-4 rounded-lg bg-gray-50 p-4">
        <h4 className="mb-2 font-medium text-gray-700">Test Configuration</h4>
        <div className="space-y-1 text-sm text-gray-600">
          <p>
            <strong>Job UUID:</strong> {jobUuid}
          </p>
          <p>
            <strong>Applicant Email:</strong> {applicantEmail}
          </p>
          <p>
            <strong>Recruiter:</strong> Test Recruiter (test_recruiter_123)
          </p>
        </div>
      </div>

      {/* Connection Status */}
      <div className="mb-4 rounded-lg bg-blue-50 p-4">
        <h4 className="mb-2 font-medium text-blue-700">Connection Status</h4>
        <div className="space-y-1 text-sm">
          <p>
            <strong>Connected:</strong>{' '}
            <span
              className={
                recruiterChat.isConnected ? 'text-green-600' : 'text-red-600'
              }
            >
              {recruiterChat.isConnected ? 'Yes' : 'No'}
            </span>
          </p>
          <p>
            <strong>Connecting:</strong>{' '}
            <span
              className={
                recruiterChat.isConnecting ? 'text-yellow-600' : 'text-gray-600'
              }
            >
              {recruiterChat.isConnecting ? 'Yes' : 'No'}
            </span>
          </p>
          <p>
            <strong>Takeover Active:</strong>{' '}
            <span
              className={
                recruiterChat.takeoverStatus.isRecruiterActive
                  ? 'text-primary'
                  : 'text-gray-600'
              }
            >
              {recruiterChat.takeoverStatus.isRecruiterActive ? 'Yes' : 'No'}
            </span>
          </p>
          <p>
            <strong>Messages:</strong> {recruiterChat.realtimeMessages.length}
          </p>
          {recruiterChat.connectionError && (
            <p className="text-red-600">
              <strong>Error:</strong> {recruiterChat.connectionError}
            </p>
          )}
        </div>
      </div>

      {/* Test Controls */}
      <div className="mb-4 flex gap-2">
        <button
          onClick={runConnectionTest}
          disabled={isTestRunning}
          className="rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50"
        >
          {isTestRunning ? 'Running Test...' : 'Run Full Test'}
        </button>
        <button
          onClick={() => recruiterChat.connect()}
          disabled={recruiterChat.isConnected || recruiterChat.isConnecting}
          className="rounded-lg bg-green-600 px-4 py-2 text-sm font-medium text-white hover:bg-green-700 disabled:opacity-50"
        >
          Connect
        </button>
        <button
          onClick={disconnectTest}
          disabled={!recruiterChat.isConnected}
          className="rounded-lg bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 disabled:opacity-50"
        >
          Disconnect
        </button>
        <button
          onClick={clearLogs}
          className="rounded-lg bg-gray-600 px-4 py-2 text-sm font-medium text-white hover:bg-gray-700"
        >
          Clear Logs
        </button>
      </div>

      {/* Test Logs */}
      <div className="rounded-lg bg-gray-900 p-4">
        <div className="mb-2 flex items-center justify-between">
          <h4 className="font-medium text-white">Test Logs</h4>
          <span className="text-xs text-gray-400">
            {testLogs.length} entries
          </span>
        </div>
        <div className="max-h-64 overflow-y-auto">
          {testLogs.length === 0 ? (
            <p className="text-sm text-gray-400">
              No logs yet. Run a test to see output.
            </p>
          ) : (
            <div className="space-y-1">
              {testLogs.map((log, index) => (
                <div key={index} className="font-mono text-xs text-green-400">
                  {log}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Real-time Messages */}
      {recruiterChat.realtimeMessages.length > 0 && (
        <div className="mt-4 rounded-lg bg-purple-50 p-4">
          <h4 className="mb-2 font-medium text-purple-700">
            Real-time Messages
          </h4>
          <div className="max-h-32 space-y-1 overflow-y-auto">
            {recruiterChat.realtimeMessages.map((message, index) => (
              <div key={message.id} className="text-xs">
                <span className="font-medium text-primary">
                  {message.type}:
                </span>{' '}
                <span className="text-gray-700">{message.content}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default RecruiterChatTest;
