import React, { Dispatch, SetStateAction, } from 'react';

import { Menu } from '@headlessui/react';
import { FilterDropdown, RadioGroup } from '@/components/shared';
import { CaretDown, Filter } from '@/components/shared/icons';
import { cn } from '@/utils';

import { pipeline_work_experiences } from '../../../constants';
import { PipelineCandidate } from '../../../types';


interface FilterProps {
    setFilterUrl: Dispatch<SetStateAction<string>>;
    selectedRoles: string[];
    selectedTools: string[];
    selectedSKills: string[];
    setSelectedRoles: Dispatch<SetStateAction<string[]>>;
    setSelectedTools: Dispatch<SetStateAction<string[]>>;
    setSelectedSkills: Dispatch<SetStateAction<string[]>>;
    selectedExperiences: string[];
    setSelectedExperiences: Dispatch<SetStateAction<string[]>>;
    cvRoles: string[];
    uniqueTools: string[];
    uniqueSkills: string[];
    minPercentageMatch: number
    maxPercentageMatch: number
    setMinPercentageMatch: Dispatch<React.SetStateAction<number>>
    setMaxPercentageMatch: Dispatch<React.SetStateAction<number>>
    setApplicantsToDisplay: Dispatch<React.SetStateAction<PipelineCandidate[]>>
    isSeenValue: boolean | null
    SetIsSeenValue: Dispatch<React.SetStateAction<boolean | null>>
}


const JobPipelineStageFilterParameters: React.FC<FilterProps> = ({ setFilterUrl, cvRoles, uniqueSkills, uniqueTools, setApplicantsToDisplay, selectedRoles, selectedExperiences, setSelectedExperiences, setSelectedRoles, selectedSKills, setSelectedSkills, selectedTools, setSelectedTools, maxPercentageMatch, minPercentageMatch, setMinPercentageMatch, setMaxPercentageMatch, isSeenValue, SetIsSeenValue }) => {


    const clearCVFilters = () => {
        if (selectedRoles.length > 0) {
            setSelectedRoles([])
            setFilterUrl("");
        }
    };
    const clearExperienceFilters = () => {
        if (selectedExperiences.length > 0) {
            setSelectedExperiences([])
        }
    };
    const clearPercentageFilters = () => {
        setMinPercentageMatch(0)
        setMaxPercentageMatch(100)
    };
    const clearSeenFilters = () => {
        SetIsSeenValue(null)
    };


    return (
        <>

            <Menu>
                {({ open }) => (
                    <div className="relative">
                        <Menu.Button className={`flex min-w-max items-center justify-center gap-x-2 rounded-lg  p-1 px-2 text-sm bg-primary-light text-primary`}>
                            <Filter className='scale-75' fill='#755AE2' />
                            <p className="flex items-center gap-2 text-[0.825rem]">
                                Seen
                                <span className={cn('flex items-center justify-center !text-xs bg-primary text-white h-4 w-4 ml-1 rounded-full', isSeenValue === null && 'hidden')}>
                                    *
                                </span>
                            </p>
                            <div className={`transition-all ${open ? 'rotate-180' : ''}`}>
                                <CaretDown
                                    color={'#755AE2'}
                                />
                            </div>
                        </Menu.Button>
                        <Menu.Items className='absolute -bottom-2 left-0 flex min-h-max max-h-[250px] min-w-max max-w-[400px] translate-y-full flex-col rounded-lg border bg-white px-2 py-2'>

                            <div className='flex flex-row gap-4 my-1'>
                                <RadioGroup
                                    options={[
                                        { name: 'Seen', value: true },
                                        { name: 'Not Seen', value: false },
                                    ]}
                                    onChange={(value) => SetIsSeenValue(value == 'true' ? true : false)}
                                    value={isSeenValue!}
                                    name="Seen"
                                    variant='unstyled'
                                    className='!py-0'
                                    containerClass='!my-0'
                                />
                            </div>
                            <div
                                className={cn(isSeenValue == null && "opacity-50 cursor-not-allowed", 'py-2 px-[1.35rem] mt-4 text-xs w-full justify-center text-center text-primary bg-primary-light hover:bg-primary-light-active rounded-md cursor-pointer transition-colors duration-300')}
                                onClick={clearSeenFilters}
                                aria-disabled={isSeenValue == null}
                            >
                                Reset
                            </div>


                        </Menu.Items>
                    </div>
                )}
            </Menu>



            <FilterDropdown
                options={pipeline_work_experiences}
                onChange={(value) => setSelectedExperiences(value)}
                valueKey='value'
                labelKey="name"
                label='Experience'
                labelClass='text-primary'
                values={selectedExperiences}
                customFooterActions={
                    <div
                        className={cn(selectedExperiences.length < 1 && "opacity-50", 'py-2 px-[1.35rem] mt-6 text-xs text-center justify-center w-full text-primary bg-primary-light hover:bg-primary-light-active rounded-md cursor-pointer transition-colors duration-300')}
                        onClick={clearExperienceFilters}
                        aria-disabled={selectedExperiences.length < 1}
                    >
                        Clear
                    </div>
                }
            />
            <FilterDropdown
                options={cvRoles.map((role) => ({ label: role, value: role }))}
                onChange={(value) => setSelectedRoles(value)}
                valueKey='value'
                label='CV Role'
                labelKey="label"
                labelClass='text-primary'
                values={selectedRoles}
                customFooterActions={
                    <div
                        className={cn(selectedRoles.length < 1 && "opacity-50", 'py-2 px-[1.35rem] mt-6 text-xs w-full justify-center text-primary bg-primary-light hover:bg-primary-light-active rounded-md cursor-pointer transition-colors duration-300')}
                        onClick={clearCVFilters}
                        aria-disabled={selectedRoles.length < 1}
                    >
                        Clear
                    </div>
                }
            />
            <FilterDropdown
                options={uniqueSkills.map((skill) => ({ label: skill, value: skill }))}
                onChange={(value) => setSelectedSkills(value)}
                valueKey='value'
                label='Skills'
                labelKey="label"
                labelClass='text-primary'
                values={selectedSKills}
                customFooterActions={
                    <div
                        className={cn(selectedSKills.length < 1 && "opacity-50", 'py-2 px-[1.35rem] mt-6 text-xs w-full justify-center text-primary bg-primary-light hover:bg-primary-light-active rounded-md cursor-pointer transition-colors duration-300')}
                        onClick={clearCVFilters}
                        aria-disabled={selectedSKills.length < 1}
                    >
                        Clear
                    </div>
                }
            />
            <FilterDropdown
                options={uniqueTools.map((tool) => ({ label: tool, value: tool }))}
                onChange={(value) => setSelectedTools(value)}
                valueKey='value'
                label='Tools'
                labelKey="label"
                labelClass='text-primary'
                values={selectedTools}
                customFooterActions={
                    <div
                        className={cn(selectedTools.length < 1 && "opacity-50", 'py-2 px-[1.35rem] mt-6 text-xs w-full justify-center text-primary bg-primary-light hover:bg-primary-light-active rounded-md cursor-pointer transition-colors duration-300')}
                        onClick={clearCVFilters}
                        aria-disabled={selectedTools.length < 1}
                    >
                        Clear
                    </div>
                }
            />


            <Menu>
                {({ open }) => (
                    <div className="relative">
                        <Menu.Button className={`flex min-w-max items-center justify-center gap-x-2 rounded-lg  p-1 px-2 text-sm bg-primary-light text-primary`}>
                            <Filter className='scale-75' fill='#755AE2' />
                            <p className="flex items-center gap-2 text-[0.825rem]">
                                AI Match Score
                                <span className={cn('flex items-center justify-center !text-xs bg-primary text-white h-4 w-4 ml-1 rounded-full', (minPercentageMatch === 0 && maxPercentageMatch === 100) && 'hidden')}>
                                    *
                                </span>
                            </p>
                            <div className={`transition-all ${open ? 'rotate-180' : ''}`}>
                                <CaretDown
                                    color={'#755AE2'}
                                />
                            </div>
                        </Menu.Button>
                        <Menu.Items className='absolute -bottom-2 left-0 flex min-h-max max-h-[250px] min-w-max max-w-[400px] translate-y-full flex-col rounded-lg border bg-white px-2 py-2'>

                            <div className='flex flex-row gap-4 my-1'>
                                <div className='inputdiv'>
                                    <label htmlFor="minPercentageMatch">Min</label>
                                    <input
                                        id='minPercentageMatch'
                                        type="number"
                                        min={0}
                                        value={minPercentageMatch}
                                        onChange={(e) => setMinPercentageMatch(parseInt(e.target.value))}
                                        placeholder="Min Match"
                                        className='max-w-[100px] !py-1 !px-1.5 !outline-none !border-helper-text focus:!border-primary'
                                    />
                                </div>
                                <div className='inputdiv'>
                                    <label htmlFor="maxPercentageMatch">Max</label>
                                    <input
                                        id='maxPercentageMatch'
                                        type="number"
                                        max={100}
                                        value={maxPercentageMatch}
                                        onChange={(e) => setMaxPercentageMatch(parseInt(e.target.value))}
                                        placeholder="Max Match"
                                        className='max-w-[100px] !py-1 !px-1.5 !outline-none !border-helper-text focus:!border-primary'
                                    />
                                </div>
                            </div>

                            <div
                                className={cn((minPercentageMatch == 0 && maxPercentageMatch === 100) && "opacity-50", 'py-2 px-[1.35rem] mt-2 text-xs w-full justify-center text-primary bg-primary-light hover:bg-primary-light-active rounded-md cursor-pointer transition-colors duration-300')}
                                onClick={clearPercentageFilters}
                                aria-disabled={minPercentageMatch == 0 && maxPercentageMatch === 100}
                            >
                                Reset
                            </div>

                        </Menu.Items>
                    </div>
                )}
            </Menu>


        </>

    );
};

export default JobPipelineStageFilterParameters;