import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { getAccessToken } from '@/utils';

// TypeScript types for the chat API response
export interface ChatMessage {
  content: string;
  user_type: 'ai' | 'applicant' | 'recruiter' | 'human_recruiter';
  message_type:
    | 'text'
    | 'system'
    | 'cv_upload'
    | 'recruiter_message'
    | 'takeover_status'
    | 'takeover_start';
  timestamp: string;
  recruiter_name?: string;
  is_realtime?: boolean;
}

export interface ChatApiResponse {
  session_id: string;
  job_id: number;
  applicant_email: string;
  messages: ChatMessage[];
  total_messages: number;
  application_status: string;
  cv_processed: boolean;
  cv_score: number | null;
  combined_score?: number;
  interview_overall_score?: number;
}

type FetchChatMessagesProps = {
  unique_id: string;
  applicant_email: string;
};

async function fetchChatMessages({
  unique_id,
  applicant_email,
}: FetchChatMessagesProps): Promise<ChatApiResponse> {
  const token = getAccessToken();
  const aiBackendUrl = process.env.NEXT_PUBLIC_AI_BACKEND_URL;

  const response = await axios.get(
    `${aiBackendUrl}/api/v1/applications/job/${unique_id}/applicant/${applicant_email}/messages`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    }
  );
  return response.data;
}

export function useFetchChatMessagesQuery({
  unique_id,
  applicant_email,
  enabled = true,
  disableRefetch = false,
}: FetchChatMessagesProps & { enabled?: boolean; disableRefetch?: boolean }) {
  return useQuery({
    queryFn: async () =>
      await fetchChatMessages({ unique_id, applicant_email }),
    queryKey: [
      'chat-messages',
      `unique_id=${unique_id}`,
      `applicant_email=${applicant_email}`,
    ],
    enabled: enabled && !!unique_id && !!applicant_email,
    staleTime: 30000, // 30 seconds
    refetchInterval: disableRefetch ? false : 60000, // Disable refetch when WebSocket is active
  });
}

// Hook to check if chat data exists (for conditional tab visibility)
export function useHasChatData({
  unique_id,
  applicant_email,
}: FetchChatMessagesProps) {
  return useQuery({
    queryFn: async () => {
      try {
        const data = await fetchChatMessages({ unique_id, applicant_email });
        return data && data.messages && data.messages.length > 0;
      } catch (error) {
        // If API returns 404 or any error, assume no chat data exists
        return false;
      }
    },
    queryKey: [
      'has-chat-data',
      `unique_id=${unique_id}`,
      `applicant_email=${applicant_email}`,
    ],
    enabled: !!unique_id && !!applicant_email,
    staleTime: 60000, // 1 minute
    retry: false, // Don't retry on error, just return false
  });
}

// Extended interface for applicants with chat data
export interface ApplicantWithChat {
  id: number;
  name: string;
  email: string;
  hasChat: boolean;
  lastMessage?: string;
  lastMessageTime?: string;
  totalMessages: number;
  applicationStatus?: string;
  cvProcessed?: boolean;
  cvScore?: number | null;
}

// Hook to fetch all applicants with chat data for a job
export function useFetchApplicantsWithChat({
  unique_id,
  candidates,
  enabled = true,
}: {
  unique_id: string;
  candidates?: any[];
  enabled?: boolean;
}) {
  return useQuery({
    queryFn: async () => {
      if (!candidates || candidates.length === 0) return [];

      // Check chat data for each candidate in parallel
      const chatDataPromises = candidates.map(
        async (candidate): Promise<ApplicantWithChat | null> => {
          try {
            const data = await fetchChatMessages({
              unique_id,
              applicant_email: candidate.email,
            });

            if (data.messages && data.messages.length > 0) {
              const lastMessage = data.messages[data.messages.length - 1];
              return {
                id: candidate.id,
                name: candidate.name,
                email: candidate.email,
                hasChat: true,
                lastMessage: lastMessage.content,
                lastMessageTime: lastMessage.timestamp,
                totalMessages: data.total_messages || data.messages.length,
                applicationStatus: data.application_status,
                cvProcessed: data.cv_processed,
                cvScore: data.cv_score,
              };
            }
          } catch (error) {
            // No chat data for this candidate
          }
          return null;
        }
      );

      const results = await Promise.all(chatDataPromises);
      return results.filter(
        (result): result is ApplicantWithChat => result !== null
      );
    },
    queryKey: [
      'applicants-with-chat',
      `unique_id=${unique_id}`,
      candidates?.length,
    ],
    enabled: enabled && !!unique_id && !!candidates,
    staleTime: 60000, // 1 minute
    refetchInterval: 120000, // Refetch every 2 minutes
    retry: 1,
  });
}
