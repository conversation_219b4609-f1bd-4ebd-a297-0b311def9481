import React, { FormEvent, useCallback, useRef, useEffect, useState, useReducer } from "react";

import Modal from "@/components/Modal";
import "@/styles/quill.css"
import Image from 'next/image'

import { useDropzone } from "react-dropzone";
import { blobToBase64 } from "@/lib/utils/functions";
import { Badge, Separator, Card, CardContent, CardHeader, FileUpload, Input, Label, ToggleGroup, ToggleGroupItem, LoaderBtn } from "@/components/shared";

import EditOfferLetterModalRTE from "./EditOfferLetterModalRTE";
import { PipelineColumn } from "../../../types";
import { useCloudinary, useErrorModalState } from "@/hooks";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { singleStageSchema } from "../../../utils/pipelineSchemas";
import { EditSinglePipelineStageType, Stage } from "../../../types/PipelineType";
import { useEditPipelineStage } from "../../../api";
import { formatAxiosErrorMessage } from "@/utils/errors";
import { AxiosError } from "axios";
import { useLoading } from "@/lib/contexts/LoadingContext";
import z from "zod";
import { addContentToTemplate, convertPipelineColumnToStage, extractContentFromTemplate } from "../../../utils/functions";
import { toast } from "react-toastify";

import { Bold, Italic, Underline as UnderlineIcon, List, AlignLeft, AlignCenter, AlignRight, Upload, Strikethrough, ListOrdered, LinkIcon, Unlink, UploadIcon, Eye, } from 'lucide-react'

import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Underline from '@tiptap/extension-underline'
import TextAlign from '@tiptap/extension-text-align'
import BulletList from '@tiptap/extension-bullet-list'
import { default as TipTapLink } from '@tiptap/extension-link'
import ListItem from '@tiptap/extension-list-item'
import OrderedList from '@tiptap/extension-ordered-list'
import { DocumentUpload } from 'iconsax-react';
import FormError from "@/components/shared/form-error";
import { cn } from "@/utils";

type FormDataType = {
  subject: string,
  message: string,
  cover_image: Blob | undefined,
}



interface Props {
  is_open: boolean;
  close: () => void;
  stage: PipelineColumn;
  pipelineId: string | number;

}


const defaultContent = `
                 <div contenteditable="true" translate="no" class="tiptap ProseMirror" role="textbox" aria-label="Rich-Text Editor" tabindex="0">
                <p>Hello {{ candidate_firstname }},</p>
                <p>Thank you for your interest in the [Role] position at [Company Name] and for taking the time to go through our recruitment process. </p>
                <p>After careful consideration, we regret to inform you that we have decided to move forward with another candidate for this role. Please know that this decision in no way diminishes the impressive qualifications and skills you demonstrated throughout the application process. </p>
                <p>We want to sincerely thank you for the effort and dedication you put into your application. We will keep your profile on file and may reach out if there are other positions that align with your skills and experience in the future.</p>
<p>We encourage you to apply for other roles at [Company Name] as they become available. We value the potential you bring, and we would love to keep in touch regarding future opportunities.
</p>
<p>Thank you again for your time and interest in joining our team. We wish you all the best in your career endeavors.</>

                 
                <p>
                    <br class="ProseMirror-trailingBreak">
                </p>
                <p>
                    Yours sincerely,
                </p>
                <p>{{ recruiter_full_name }}</p><p>{{ job_title }}
                </p>
                <p>
                    {{ company_name }}
                </p>
                <p>
                    {{ company_support_email }}
                </p>
                <p>
                    {{ company_address }}
                </p>
            </div>
            `

export const offerSchema = z.object({
  id: z.union([z.string(), z.number()]).optional(),

  reject_email_subject: z.string().optional(),
  reject_email_body: z.string().optional(),
  reject_cover_image: z.string().optional(),
  reject_attachment_url: z.string().optional(),
})
export type OfferShemaType = z.infer<typeof offerSchema>;

const EditRejectionLetterModal: React.FC<Props> = ({
  is_open,
  close,
  stage,
  pipelineId

}) => {
  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();
  const { stage_id, stage_name, rejection_setup_customization, } = stage
  const {
    email_subject,
    email_message,
    email_cover_image } = rejection_setup_customization || {}


  const { register, formState: { errors }, setValue, handleSubmit, reset, watch, control, clearErrors } = useForm({
    defaultValues: {
      id: stage_id,
      reject_email_subject: email_subject || "",
      reject_email_body: !!email_message ? extractContentFromTemplate(email_message) : defaultContent
    },
    resolver: zodResolver(offerSchema),
  });
  const closeModal = () => {
    reset({
      id: stage_id,
      reject_email_subject: email_subject || "",
      reject_email_body: !!email_message ? extractContentFromTemplate(email_message) : defaultContent
    })
    close()
  }



  const getLinkName = (link: string) => {
    const url = new URL(link)
    return url.pathname.split('/').pop()
  }

  const { mutate: editStage, isLoading: isStageEditing } = useEditPipelineStage();
  const { uploadToServer } = useCloudinary()
  const { isUploading } = useLoading()

  const submitForm = async (data: OfferShemaType) => {
    // let offer_cover_image: string | undefined;
    // let attachment_url: string | undefined;

    const prevStage = convertPipelineColumnToStage(stage)

    const newStage: Stage = {
      ...prevStage,
      rejection_setup_customization: {
        email_subject: data.reject_email_subject || "",
        email_message: addContentToTemplate(data.reject_email_body || "<p></p>", email_cover_image),
        email_cover_image: prevStage.rejection_setup_customization?.email_cover_image || "",
      },
    }

     if (selectedcoverImage) {
      const res = await uploadToServer(selectedcoverImage)
      if (newStage.rejection_setup_customization) {
        newStage.rejection_setup_customization.email_cover_image = res.secure_url
        newStage.rejection_setup_customization.email_message = addContentToTemplate(data.reject_email_body || "<p></p>", res.secure_url)
      }
    }

     editStage(
      { pipelineId, stageId: Number(stage_id), newStage },
      {
        onSuccess() {
          toast.success("Rejection letter updated");
          closeModal()
        },
        onError(error: any) {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(error?.response?.data || errorMessage || "Something went wrong");
        },
      }
    )

  }

  const [selectedcoverImage, setSelectedCoverImage] = useState<File | null>(null)
  const [selectedAttachment, setSelectedAttachment] = useState<File | null>(null);



  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: false,
        orderedList: false,
        listItem: false,
      }),
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      BulletList,
      OrderedList,
      ListItem,
      TipTapLink.configure({
        openOnClick: false,
        autolink: true,
        defaultProtocol: 'https',
      }),
    ],

    content: watch('reject_email_body'),
    onUpdate: ({ editor }) => {
      setValue('reject_email_body', editor.getHTML())
    },
  })

  const tokens = [
    { title: 'First name', value: 'candidate_firstname' },
    { title: 'Last name', value: 'candidate_lastname' },
    { title: 'Position', value: 'position' },
    { title: 'Company Name', value: 'company_name' },
    { title: 'Salary', value: 'salary' },
    { title: 'Employment Type', value: 'employment_type' },
    { title: 'Recruiter\'s Job Title', value: 'recruiter_job_title' },
    // { title: 'Start date', value: 'start_date' },
    // { title: 'Start date', value: 'start_date' },
  ]

  const insertToken = (token: string) => {
    if (editor) {
      editor.commands.insertContent(`{{ ${token} }}`)
    }
  }

  const [, forceUpdate] = useReducer((x) => x + 1, 0)

  useEffect(() => {
    if (editor) {
      editor.on('selectionUpdate', () => {
        // Force a re-render to update the toggle states
        forceUpdate()
      })
    }
  }, [editor])



  const setLink = useCallback(() => {
    if (editor) {
      const previousUrl = editor.getAttributes('link').href
      const url = window.prompt('URL', previousUrl)

      if (url === null) {
        return
      }

      if (url === '') {
        editor.chain().focus().extendMarkRange('link').unsetLink()
          .run()
        return
      }

      editor.chain().focus().extendMarkRange('link').setLink({ href: url })
        .run()
    }
  }, [editor])



 
  if (!editor) {
    return null
  }
  const rejectEmailSubject = watch('reject_email_subject')





  return (
    <Modal title="Customize rejection letter" close={close} is_open={is_open} portalClass="grid grid-rows-[max-content,1fr,max-content] h-[calc(100vh_-_2rem)] overflow-y-hidden">


      <div className="flex flex-col relative w-[90vw] max-w-[1200px] p- h-full overflow-hidden bg-[#F8F9FB] ">
        <form id="offer-letter-form" className="grid grid-cols-2 w-full p-4 gap-4 grow overflow-y-scroll" onSubmit={handleSubmit(submitForm)}>

          <Card className="border-none">
            <CardHeader className="text-lg 2xl:text-xl">
              Edit Rejection Letter
            </CardHeader>
            <CardContent className="">
              <div className="space-y-6">
                <Input
                  className="border rounded-xl p-4 border-[helper-text] w-full"
                  id="email-subject" type="text"
                  label="Email Subject"
                  labelClass="text-black"
                  placeholder="Enter email subject here"
                  // onChange={(e) => setSubject(e.target.value)}
                  required
                  {...register('reject_email_subject')}
                  hasError={!!errors?.reject_email_subject}
                  errorMessage={errors?.reject_email_subject?.message}
                />


                <FileUpload
                  label="COVER IMAGE"
                  accept={{ 'image/*': [] }}
                  maxSizeMB={15}
                  dragActiveClassName="bg-green-50 border-green-500"
                  containerClassName="w-full max-w-2xl"
                  hint="Files types: PNG, JPG, Max size: 10MB"
                  value={selectedcoverImage}
                  onFileSelect={setSelectedCoverImage}
                  onFileRemove={() => setSelectedCoverImage(null)}
                />



                {/* Token Buttons */}
                <div className="text-body-text font-normal text-[0.85rem]">
                  <Label className="my-4 mb-3 text-body-text">To customize this email, drag the placeholder options displayed below
                    and place them where you will like to have them.
                  </Label>
                  <div className="flex flex-wrap gap-2 border border-[#E4E4E4] p-4 rounded-xl">
                    {tokens.map((token) => (
                      <Badge
                        key={token.value}
                        variant="light"
                        shape="rounded"
                        className='cursor-pointer border-none font-normal'
                        size="lg"
                        onClick={() => insertToken(token.value)}
                      >
                        {token.title}
                      </Badge>
                    ))}
                  </div>
                </div>


                <article className="border-[0.7px] border-[#E4E4E4] rounded-xl">
                  <header>
                    <ToggleGroup type="multiple" className="justify-start flex-wrap border-b">
                      <ToggleGroupItem
                        value="bold"
                        aria-label="Toggle bold"
                        onClick={() => editor.chain().focus().toggleBold().run()}
                        data-state={editor.isActive('bold') ? 'on' : 'off'}
                      >
                        <Bold className="h-4 w-4" />
                      </ToggleGroupItem>
                      <ToggleGroupItem
                        value="italic"
                        aria-label="Toggle italic"
                        onClick={() => editor.chain().focus().toggleItalic().run()}
                        data-state={editor.isActive('italic') ? 'on' : 'off'}
                      >
                        <Italic className="h-4 w-4" />
                      </ToggleGroupItem>
                      <ToggleGroupItem
                        value="italic"
                        aria-label="Toggle italic"
                        onClick={() => editor.chain().focus().toggleStrike().run()}
                        data-state={editor.isActive('italic') ? 'on' : 'off'}
                      >
                        <Strikethrough className="h-4 w-4" />
                      </ToggleGroupItem>
                      <ToggleGroupItem
                        value="underline"
                        aria-label="Toggle underline"
                        onClick={() => editor.chain().focus().toggleUnderline().run()}
                        data-state={editor.isActive('underline') ? 'on' : 'off'}
                      >
                        <UnderlineIcon className="h-4 w-4" />
                      </ToggleGroupItem>

                      <Separator orientation="vertical" className="mx-1 h-8" />

                      <ToggleGroupItem
                        value="left"
                        aria-label="Align left"
                        onClick={() => editor.chain().focus().setTextAlign('left').run()}
                        data-state={editor.isActive({ textAlign: 'left' }) ? 'on' : 'off'}
                      >
                        <AlignLeft className="h-4 w-4" />
                      </ToggleGroupItem>
                      <ToggleGroupItem
                        value="center"
                        aria-label="Align center"
                        onClick={() => editor.chain().focus().setTextAlign('center').run()}
                        data-state={editor.isActive({ textAlign: 'center' }) ? 'on' : 'off'}
                      >
                        <AlignCenter className="h-4 w-4" />
                      </ToggleGroupItem>
                      <ToggleGroupItem
                        value="right"
                        aria-label="Align right"
                        onClick={() => editor.chain().focus().setTextAlign('right').run()}
                        data-state={editor.isActive({ textAlign: 'right' }) ? 'on' : 'off'}
                      >
                        <AlignRight className="h-4 w-4" />
                      </ToggleGroupItem>

                      <Separator orientation="vertical" className="mx-1 h-8" />

                      <ToggleGroupItem
                        value="bulletList"
                        aria-label="Toggle bullet list"
                        onClick={() => editor.chain().focus().toggleBulletList().run()}
                        data-state={editor.isActive('bulletList') ? 'on' : 'off'}
                      >
                        <List className="h-4 w-4" />
                      </ToggleGroupItem>
                      <ToggleGroupItem
                        value="orderedList"
                        aria-label="Toggle ordered list"
                        onClick={() => editor.chain().focus().toggleOrderedList().run()}
                        data-state={editor.isActive('orderedList') ? 'on' : 'off'}
                      >
                        <ListOrdered className="h-4 w-4" />
                      </ToggleGroupItem>

                      <Separator orientation="vertical" className="mx-1 h-8" />

                      <ToggleGroupItem
                        value="bulletList"
                        aria-label="Toggle bullet list"
                        onClick={setLink}
                        data-state={editor.isActive('link') ? 'on' : 'off'}
                      >
                        <LinkIcon className="h-4 w-4" />
                      </ToggleGroupItem>
                      <ToggleGroupItem
                        value="orderedList"
                        aria-label="Toggle ordered list"
                        onClick={() => editor.chain().focus().unsetLink().run()}
                        disabled={!editor.isActive('link')}
                      >
                        <Unlink className="h-4 w-4" />
                      </ToggleGroupItem>
                    </ToggleGroup>
                  </header>


                  <section className="min-h-[500px]  p-4">
                    <EditorContent editor={editor} className="prose max-w-none" />
                  </section>
                  {
                    !!errors?.reject_email_body && <FormError errorMessage={errors?.reject_email_body.message} />
                  }
                </article>

                {/* <FileUpload
                  label="Attachment: Offer Letter (PDF)"
                  accept={{ 'application/pdf': [] }}
                  maxSizeMB={15}
                  dragActiveClassName="bg-green-50 border-green-500"
                  containerClassName="w-full max-w-2xl"
                  value={selectedAttachment}
                  onFileSelect={setSelectedAttachment}
                  onFileRemove={() => setSelectedAttachment(null)}
                /> */}

              </div>
            </CardContent>
          </Card>


          <Card>
            <CardHeader className="text-lg 2xl:text-xl">
              Preview
            </CardHeader>
            <CardContent className="p-6 pt-2">
              {rejectEmailSubject.trim() != "" &&
                <div className="text-body-text font-normal text-[0.85rem]  mb-5 gap-2">
                  <p className="text-body-text">EMAIL SUBJECT
                  </p>
                  <p className=" font-medium text-lg text-black">{rejectEmailSubject}</p>
                </div>
              }
              {
                (selectedcoverImage || email_cover_image != null) &&
                <div className="relative inline-block rounded-[0.975rem] h-[7.2rem] w-full">
                  <Image
                    src={selectedcoverImage ? URL.createObjectURL(selectedcoverImage) : email_cover_image || ""}
                    alt="Cover Image Preview"
                    fill
                    objectFit='cover'
                    className={cn("rounded-lg border border-gray-200")}
                  />
                </div>

              }
              <div className="tiptap max-w-none">
                <div
                  dangerouslySetInnerHTML={{ __html: editor.getHTML() }}
                  className="tiptap"
                />
              </div>

            </CardContent>
          </Card>

        </form>

      </div>
      <div className="sticky bottom-0 flex gap-4 items-center justify-end p-4 bg-white">
        <button type="button" onClick={close} className="btn-primary-light"> Cancel </button>
        <button form="offer-letter-form" type="submit" className="btn-primary items-center gap-2" > Save

          {
            (isUploading || isStageEditing) && <LoaderBtn color="white" />
          }
        </button>
      </div>

    </Modal>
  );
};

export default EditRejectionLetterModal;
