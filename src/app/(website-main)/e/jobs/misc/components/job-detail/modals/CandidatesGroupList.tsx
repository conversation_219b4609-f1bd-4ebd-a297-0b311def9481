import React from 'react'
import TextareaAutosize from 'react-textarea-autosize';

import { Button, ErrorModal, LoaderBtn, LoadingOverlay, Modal, RadioGroup, Select, SingleDatePicker, Switch } from '@/components/shared'
import { ActiveJob, CreateJob, PipelineCandidate } from '../../../types'
import { Controller, useFieldArray, useForm } from 'react-hook-form'
import { useGetCandidatesGroup, usePatchEditJobDetails } from '../../../api'
import { cn } from '@/utils';
import { useErrorModalState } from '@/hooks';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { AxiosError } from 'axios';
import { useUser } from '@/lib/contexts/UserContext';

interface ModalProps {
    isOpen: boolean
    candidates: PipelineCandidate[]
    close: () => void
}



interface CustomFormErrors {
    job_custom_fields?: Array<{ fieldTitle?: { message?: string }, fieldValue?: { message: string }, }>;
}


const EditJobBasicInfoModal: React.FC<ModalProps> = ({ isOpen, close }) => {
    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();


    const [selectedGroups, setselectedGroups] = React.useState([])
    const customErrors = useForm().formState.errors as CustomFormErrors;





    const { user, } = useUser();
    const [currentPage, setcurrentPage] = React.useState(1)
    const { data, isLoading: isLoadingCandidateGroups } = useGetCandidatesGroup(user?.email || "")



    const onSubmit = async (data: CreateJob) => {


    };






    return (
        <Modal isModalOpen={isOpen} closeModal={close} heading='Edit Job Details'
            body={
                <div className='bg-white px-3 py-4 rounded-xl overflow-y-scroll '>


                    <section className=''>

                    </section>

                    <div className='flex items-center justify-between mt-10'>
                        <Button variant='outlined' type="button" disabled={false} icon={false && <LoaderBtn />} onClick={() => close()}>
                            Cancel
                        </Button>
                        <Button className='ml-auto' type="submit" form="create-job-form" disabled={false} icon={false && <LoaderBtn />}>
                            Save
                        </Button>
                    </div>

                    <LoadingOverlay isOpen={false} />
                    <ErrorModal
                        isErrorModalOpen={isErrorModalOpen}
                        setErrorModalState={setErrorModalState}

                        subheading={
                            errorModalMessage || 'Please select and try again.'
                        }
                    >
                        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                            <Button
                                className="grow bg-red-950 hover:border-red-950 hover:text-red-950 px-1.5 sm:text-sm md:px-6"
                                type="button"
                                onClick={closeErrorModal}
                            >
                                Okay
                            </Button>
                        </div>
                    </ErrorModal>
                </div>
            }
        />
    )
}

export default EditJobBasicInfoModal