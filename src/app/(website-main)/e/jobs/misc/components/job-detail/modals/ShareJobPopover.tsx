'use client';

import * as Popover from '@radix-ui/react-popover';
import { ArrowDown2 } from 'iconsax-react';
import { ShareLinkToClient } from '@/app/(website-main)/e/my-clients/misc/components/ShareLinkToClient';
import ShareLinkToTalent from '@/app/(website-main)/e/my-clients/misc/components/ShareLinkToTalent';
import { Button } from '@/components/shared';
import { cn } from '@/utils';

interface SharePopoverProps {
  job_id: string;
}

export function SharePopover({ job_id }: SharePopoverProps) {
  return (
    <div>
      <Popover.Root>
        <Popover.Trigger asChild>
          <Button
            size="tiny"
            variant="extralight"
            className="!min-w-max text-xs !font-medium max-lg:hidden"
            icon={<ArrowDown2 size={12} />}
            iconPosition="after"
          >
            Share job link
          </Button>
        </Popover.Trigger>

        <Popover.Portal>
          <Popover.Content
            sideOffset={8}
            className={cn(
              'z-50 w-auto rounded-xl  border border-gray-200 bg-white shadow-lg',
              'data-[state=open]:animate-fade-in'
            )}
          >
            <div className="flex flex-col items-center gap-2 p-4">
              <div>
                <ShareLinkToTalent
                  job_id={`${job_id}`}
                  trigger={<button>Share to Talent</button>}
                />
              </div>

              <div>
                <ShareLinkToClient
                  job_id={job_id}
                  trigger={<button>Share to Client</button>}
                />
              </div>
            </div>
          </Popover.Content>
        </Popover.Portal>
      </Popover.Root>
    </div>
  );
}
