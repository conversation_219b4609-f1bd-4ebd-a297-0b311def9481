# Job Chat Interface

A multi-conversation chat interface for recruiters to view and manage all
applicant chat conversations for a specific job.

## 🎯 Overview

The Job Chat Interface provides recruiters with a centralized view of all chat
conversations from applicants for a specific job. It features a conversation
list sidebar and an integrated chat view, allowing recruiters to easily switch
between different applicant conversations.

## 🏗️ Architecture

```
JobChatInterface/
├── index.tsx                    # Main component with conversation list and chat view
└── README.md                   # This documentation
```

**Note**: The API hooks are located in the existing chat component's API file:
`5.5 JobPipelineCandidateDetailsChatTab/api/index.tsx`

## 🚀 Features

### Multi-Conversation Management

- **Conversation List**: Displays all applicants who have chat conversations for
  the job
- **Last Message Preview**: Shows the most recent message from each conversation
- **Message Count**: Displays total number of messages per conversation
- **CV Processing Status**: Shows if CV has been processed and the score
- **Auto-Selection**: Automatically selects the first conversation when
  available

### Responsive Design

- **Desktop**: Side-by-side layout with conversation list and chat view
- **Mobile**: Stack layout with navigation between conversation list and chat
- **Back Button**: Mobile-specific navigation to return to conversation list

### Integration

- **Existing Chat Component**: Reuses the existing `CandidateDetailsChatTab`
  component
- **Real-time Updates**: Supports WebSocket connections for live chat
- **Recruiter Takeover**: Full support for AI-to-human handover functionality

## 📱 User Experience

### Desktop Flow

1. Recruiter navigates to the "Chat" tab in job details
2. Conversation list shows all applicants with chat data
3. Clicking an applicant loads their chat conversation
4. Recruiter can switch between conversations seamlessly

### Mobile Flow

1. Conversation list is shown first on mobile
2. Selecting an applicant hides the list and shows the chat
3. Back button returns to the conversation list
4. Status indicators are hidden on mobile for space efficiency

## 🔧 API Integration

### Data Sources

- **Job Candidates**: Uses existing `useGetJobCandidates` API to fetch all job
  applicants
- **Chat Messages**: Extends existing `fetchChatMessages` function from
  `5.5 JobPipelineCandidateDetailsChatTab/api`
- **Parallel Processing**: New `useFetchApplicantsWithChat` hook checks chat
  data for all candidates simultaneously
- **Reused Logic**: Leverages existing chat API endpoints and authentication

### Performance Optimizations

- **Efficient Filtering**: Only returns applicants who have chat conversations
- **Caching**: 1-minute stale time with 2-minute refetch interval
- **Error Handling**: Graceful handling of missing chat data

## 🎨 Design System

### Colors & Styling

- **Primary**: Blue (#3B82F6) for selected conversations and accents
- **Success**: Green (#10B981) for CV processed indicators
- **Gray Scale**: Consistent with existing design system
- **Hover States**: Subtle background changes for interactive elements

### Typography

- **Headers**: lg font-weight semibold for conversation names
- **Body**: sm text for email addresses and messages
- **Meta**: xs text for timestamps and counts

### Spacing

- **Padding**: Consistent 4-unit (1rem) padding for main sections
- **Gaps**: 3-unit (0.75rem) gaps between elements
- **Borders**: 1px gray-200 borders for separation

## 🔌 Usage

### Basic Integration

```tsx
import { JobChatInterface } from '../misc/components/job-detail/JobChatInterface';

// In job details tab array
{
  id: 6,
  title: 'Chat',
  link: './chat',
  component: <JobChatInterface unique_id={unique_id!} />,
}
```

### Props

```tsx
interface Props {
  unique_id: string; // Job UUID for API calls
}
```

## 🔄 State Management

### Local State

- `selectedApplicant`: Currently selected applicant for chat view
- `showSidebar`: Controls sidebar visibility (responsive)

### API State

- `applicantsWithChat`: List of applicants with chat conversations
- `isLoadingChats`: Loading state for chat data fetching
- `chatError`: Error state for failed API calls

## 🎯 Future Enhancements

### Planned Features

- **Unread Message Indicators**: Show unread message counts per conversation
- **Search & Filter**: Search applicants by name or filter by status
- **Bulk Actions**: Mark multiple conversations as read/unread
- **Export Conversations**: Download chat transcripts for record keeping

### Performance Improvements

- **Virtual Scrolling**: For large numbers of conversations
- **Incremental Loading**: Load conversations on demand
- **WebSocket Optimization**: Shared connections across conversations

## 🐛 Troubleshooting

### Common Issues

1. **No Conversations Showing**

   - Verify job has applicants who used the AI chat feature
   - Check API endpoints are accessible
   - Ensure proper authentication tokens

2. **Chat Not Loading**

   - Verify the existing `CandidateDetailsChatTab` component works
   - Check WebSocket connectivity
   - Validate applicant email format

3. **Mobile Layout Issues**
   - Ensure responsive classes are applied correctly
   - Test on various screen sizes
   - Verify back button functionality

### Debug Mode

Enable debug logging by setting `console.log` statements in the API hooks to
trace data flow and identify issues.
