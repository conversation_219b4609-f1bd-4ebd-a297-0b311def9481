'use client'

import CommentItem from "@/app/(website-main)/e/assessments-and-interviews/assessments/[assessment_id]/components/CommentItem"
import { CandidateDetails } from "../../../types/PipelineType"
import { useCreateCandidateCommentMutation, useDeleteCandidateCommentMutation, useEditCandidateCommentMutation, useFetchCandidateCommentsQuery } from "./api"
import Button from "@/components/shared/Button"
import { useState } from "react"
import { Rating } from "@smastrom/react-rating"
import StarIcon from "@/components/shared/icons/Star"
import "@smastrom/react-rating/style.css";
import { CommentActions, Comment } from "@/app/(website-main)/e/assessments-and-interviews/misc/types/create-assessments"

interface Props {
    candidate: CandidateDetails
}

export default function CandidateDetailsCommentsTab({ candidate }: Props) {
    const { data, isLoading, isError, error } = useFetchCandidateCommentsQuery({ job_application_id: `${candidate.id}` })
    const createCommentMutation = useCreateCandidateCommentMutation({
        job_application_id: `${candidate.id}`,
        onSuccess() {
            resetCommentForm()
        }
    })
    const deleteCommentMutation = useDeleteCandidateCommentMutation({
        job_application_id: `${candidate.id}`,
        onSuccess() {
            resetCommentForm()
        }
    })
    const editCommentMutation = useEditCandidateCommentMutation({
        job_application_id: `${candidate.id}`,
        onSuccess() {
            resetCommentForm()
        }
    })

    function resetCommentForm() {
        setComment("")
        setRating(1)
        setCommentID("")
    }

    const [comment, setComment] = useState("")
    const [comment_id, setCommentID] = useState("")
    const [rating, setRating] = useState(1)

    function submitCreate() {
        createCommentMutation.mutate({
            job_application_id: `${candidate.id}`,
            comment,
            rating,
        })
    }

    function submitEdit() {
        editCommentMutation.mutate({
            id: comment_id,
            job_application_id: `${candidate.id}`,
            comment,
            rating,
        })
    }



    function handleCommentAction(action: CommentActions, comment: Comment) {
        switch (action) {
            case "edit comment":
                setComment(comment.text)
                setCommentID(comment.id)
                setRating(comment.rating)
                break
            case "delete comment":
                deleteCommentMutation.mutate({ id: comment.id })
                setCommentID(comment.id)
                break;
        }
    }

    if (isLoading) {
        return (
            <p>Loading comments...</p>
        )
    }
    if (isError) {
        return (
            <p>Error loading comments</p>
        )
    }

    return (
        <div className="p-2 h-full">
            <div className="flex h-full flex-col justify-between">

                {data.length ? (
                    <ul className="px-12 space-y-2 overflow-y-auto pb-4">
                        {data.map((comment, index) => (
                            <li key={index} className="border rounded-md p-4">
                                <CommentItem comment={comment} onAction={handleCommentAction} is_use_dropdown={false} />
                            </li>
                        ))}
                    </ul>

                ) : (
                    <p>No comments on this candidate's appliction yet.</p>
                )}


                <div className="bg-[#F1EFFC] p-4 rounded-md shadow-[-120px_0px_130px_rgb(0,0,0,0.2)]">
                    <div className="space-y-2">
                        <h2 className="heading-text">Give rating</h2>
                        <p>
                            Provide your personal rating for this candidate based on
                            your impressions and interactions
                        </p>
                        <div>
                            <Rating
                                style={{ maxWidth: 120 }}
                                itemStyles={{
                                    itemShapes: StarIcon,
                                    activeFillColor: "#755AE2",
                                    inactiveFillColor: "#E2DDFF",
                                }}
                                value={rating}
                                onChange={setRating}
                            />
                        </div>
                    </div>
                    <div className="space-y-2">
                        <h2 className="heading-text">Comment:</h2>
                        <label className="space-y-1">
                            <p aria-hidden="true" className="hidden">
                                Comment
                            </p>
                            <textarea
                                className="input-grey border min-h-[200px]"
                                placeholder="Add notes regarding this candidate"
                                value={comment}
                                onChange={(e) => setComment(e.target.value)}
                            >
                            </textarea>
                        </label>
                    </div>
                    <div className="flex justify-end">
                        <Button
                            is_busy={createCommentMutation.isLoading || editCommentMutation.isLoading}
                            onClick={comment_id ? submitEdit : submitCreate}
                            className="btn-primary"
                        >
                            Submit
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    )
}
