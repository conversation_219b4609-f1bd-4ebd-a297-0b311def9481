// Message payload types for recruiter WebSocket communication
export interface RecruiterJoinPayload {
  type: 'recruiter_join';
  data: {
    recruiter_id: string;
    recruiter_name: string;
    applicant_email: string;
    reason?: string;
  };
}

export interface RecruiterMessagePayload {
  type: 'recruiter_message';
  data: {
    message: string;
  };
}

export interface RecruiterLeavePayload {
  type: 'recruiter_leave';
  data: {
    handover_message?: string;
  };
}

// Incoming message types from WebSocket
export interface IncomingRecruiterJoin {
  type: 'recruiter_join';
  data: {
    recruiter_id: string;
    recruiter_name: string;
    applicant_email: string;
    reason?: string;
    timestamp?: string;
  };
}

export interface IncomingRecruiterMessage {
  type: 'recruiter_message';
  data: {
    message: string;
    recruiter_name?: string;
    timestamp?: string;
  };
}

export interface IncomingRecruiterLeave {
  type: 'recruiter_leave';
  data: {
    handover_message?: string;
    recruiter_name?: string;
    timestamp?: string;
  };
}

export interface IncomingAIResponse {
  type: 'ai_response';
  data: {
    message: string;
    timestamp?: string;
    takeover_status?: 'ai_active' | 'recruiter_active';
  };
}

export interface IncomingChatMessage {
  type: 'chat_message';
  data: {
    message: string;
    user_type: 'applicant' | 'ai';
    timestamp?: string;
  };
}

// Union type for all possible incoming messages
export type IncomingWebSocketMessage = 
  | IncomingRecruiterJoin
  | IncomingRecruiterMessage
  | IncomingRecruiterLeave
  | IncomingAIResponse
  | IncomingChatMessage;

// Union type for all possible outgoing messages
export type OutgoingWebSocketMessage = 
  | RecruiterJoinPayload
  | RecruiterMessagePayload
  | RecruiterLeavePayload;

// Validation functions
export const validateRecruiterJoinPayload = (data: any): data is RecruiterJoinPayload => {
  return (
    data &&
    data.type === 'recruiter_join' &&
    data.data &&
    typeof data.data.recruiter_id === 'string' &&
    typeof data.data.recruiter_name === 'string' &&
    typeof data.data.applicant_email === 'string'
  );
};

export const validateRecruiterMessagePayload = (data: any): data is RecruiterMessagePayload => {
  return (
    data &&
    data.type === 'recruiter_message' &&
    data.data &&
    typeof data.data.message === 'string' &&
    data.data.message.trim().length > 0
  );
};

export const validateRecruiterLeavePayload = (data: any): data is RecruiterLeavePayload => {
  return (
    data &&
    data.type === 'recruiter_leave' &&
    data.data &&
    (data.data.handover_message === undefined || typeof data.data.handover_message === 'string')
  );
};

export const validateIncomingMessage = (data: any): data is IncomingWebSocketMessage => {
  if (!data || !data.type || !data.data) {
    return false;
  }

  switch (data.type) {
    case 'recruiter_join':
      return (
        typeof data.data.recruiter_id === 'string' &&
        typeof data.data.recruiter_name === 'string' &&
        typeof data.data.applicant_email === 'string'
      );
    
    case 'recruiter_message':
      return typeof data.data.message === 'string';
    
    case 'recruiter_leave':
      return true; // handover_message is optional
    
    case 'ai_response':
      return typeof data.data.message === 'string';
    
    case 'chat_message':
      return (
        typeof data.data.message === 'string' &&
        ['applicant', 'ai'].includes(data.data.user_type)
      );
    
    default:
      return false;
  }
};

// Message formatting utilities
export const formatRecruiterMessage = (
  recruiterName: string,
  message: string
): string => {
  return `🧑‍💼 ${recruiterName}: ${message}`;
};

export const formatApplicantMessage = (message: string): string => {
  return `👤 Applicant: ${message}`;
};

export const formatAIMessage = (message: string): string => {
  return `🤖 AI Assistant: ${message}`;
};

export const formatTakeoverStatusMessage = (
  isJoining: boolean,
  recruiterName?: string,
  customMessage?: string
): string => {
  if (customMessage) {
    return customMessage;
  }
  
  if (isJoining) {
    return `🔄 ${recruiterName || 'Recruiter'} has taken over the conversation`;
  } else {
    return `🔄 AI Assistant has resumed the conversation`;
  }
};

// Error handling utilities
export const getWebSocketErrorMessage = (error: Event | Error): string => {
  if (error instanceof Error) {
    return error.message;
  }
  
  return 'WebSocket connection error occurred';
};

export const getConnectionStatusMessage = (
  isConnected: boolean,
  isConnecting: boolean,
  error?: string
): string => {
  if (error) {
    return `Connection error: ${error}`;
  }
  
  if (isConnecting) {
    return 'Connecting to chat service...';
  }
  
  if (isConnected) {
    return 'Connected to chat service';
  }
  
  return 'Disconnected from chat service';
};
