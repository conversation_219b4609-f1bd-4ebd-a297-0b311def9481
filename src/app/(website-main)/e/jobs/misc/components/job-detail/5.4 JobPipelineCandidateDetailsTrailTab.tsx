import React from 'react';
import Timeline, { TimelineEvent } from './Timeline';

interface Props {
  data: TimelineEvent[];
}

const CandidateDetailsTrailTab: React.FC<Props> = ({ data }) => {
  return (
    <section className="max-w-full p-4 md:px-8 md:py-6">
      <div className="h-full grid-cols-2 gap-5 overflow-scroll md:grid">
        <Timeline data={data} />
      </div>
    </section>
  );
};

export default CandidateDetailsTrailTab;
