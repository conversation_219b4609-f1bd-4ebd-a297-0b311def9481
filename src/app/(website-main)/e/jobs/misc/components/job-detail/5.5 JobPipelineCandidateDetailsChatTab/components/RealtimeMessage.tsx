'use client';

import { format } from 'date-fns';
import React from 'react';
import { cn } from '@/utils';
import { RealtimeChatMessage } from '../hooks/useRecruiterChat';

interface RealtimeMessageProps {
  message: RealtimeChatMessage;
  isLatest?: boolean;
}

const RealtimeMessage: React.FC<RealtimeMessageProps> = ({
  message,
  isLatest = false,
}) => {
  const isAI = message.type === 'ai';
  const isRecruiter = message.type === 'recruiter';
  const isApplicant = message.type === 'applicant';
  const isTakeoverStatus =
    message.messageType === 'takeover_status' ||
    message.messageType === 'takeover_start';

  const formatTimestamp = (timestamp: Date) => {
    try {
      return format(timestamp, 'h:mm aaaa');
    } catch {
      return timestamp.toLocaleTimeString();
    }
  };

  const getMessageIcon = () => {
    if (isAI) return <span className="text-xs">🤖</span>;
    if (isRecruiter) return <span className="text-xs">👨‍💼</span>;
    if (isApplicant) return <span className="text-xs">👤</span>;
    return <span className="text-xs">⚠️</span>;
  };

  const getMessageLabel = () => {
    if (isAI) return 'AI Assistant';
    if (isRecruiter) return message.recruiterName || 'Human Recruiter';
    if (isApplicant) return 'Applicant';
    return 'System';
  };

  const getMessageAlignment = () => {
    if (isRecruiter) return 'justify-end';
    return 'justify-start';
  };

  const getMessageStyling = () => {
    if (isTakeoverStatus) {
      return 'border border-blue-200 bg-blue-50 text-blue-800';
    }
    if (isRecruiter) {
      return 'border border-purple-200 bg-purple-100 text-purple-900';
    }
    if (isAI) {
      return 'border border-primary/20 bg-primary-light text-primary';
    }
    if (isApplicant) {
      return 'border border-gray-200 bg-gray-100 text-header-text';
    }
    return 'border border-gray-200 bg-gray-50 text-gray-700';
  };

  // Special rendering for takeover status messages
  if (isTakeoverStatus) {
    return (
      <div className="mb-4 flex w-full justify-center">
        <div className="max-w-[80%] rounded-lg border border-blue-200 bg-blue-50 px-4 py-2 text-center">
          <div className="flex items-center justify-center gap-2 text-sm text-blue-800">
            <div className="flex h-4 w-4 items-center justify-center">
              <span className="text-xs">🔄</span>
            </div>
            <span className="font-medium">{message.content}</span>
          </div>
          <div className="mt-1 text-xs text-blue-600">
            {formatTimestamp(message.timestamp)}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        'mb-4 flex w-full',
        getMessageAlignment(),
        isLatest && 'duration-300 animate-in slide-in-from-bottom-2'
      )}
    >
      <div
        className={cn(
          'max-w-[80%] rounded-lg px-4 py-3 shadow-sm',
          getMessageStyling()
        )}
      >
        {/* Message content */}
        <div className="mb-2">
          <p className="whitespace-pre-wrap text-sm">{message.content}</p>
        </div>

        {/* Message metadata */}
        <div className="flex items-center justify-between text-xs opacity-70">
          <div className="flex items-center gap-1">
            {/* User type icon */}
            <div className="flex h-4 w-4 items-center justify-center">
              {getMessageIcon()}
            </div>
            <span className="font-medium">{getMessageLabel()}</span>
            {/* Message type indicator for recruiter messages */}
            {isRecruiter &&
              message.messageType &&
              message.messageType !== 'chat_message' && (
                <span className="ml-1 rounded bg-black/10 px-1 py-0.5 text-[10px]">
                  {message.messageType.replace('_', ' ')}
                </span>
              )}
          </div>
          <span>{formatTimestamp(message.timestamp)}</span>
        </div>

        {/* Real-time indicator */}
        {isLatest && (
          <div className="mt-1 flex items-center gap-1 text-[10px] opacity-60">
            <div className="h-1 w-1 animate-pulse rounded-full bg-current" />
            <span>Live</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default RealtimeMessage;
