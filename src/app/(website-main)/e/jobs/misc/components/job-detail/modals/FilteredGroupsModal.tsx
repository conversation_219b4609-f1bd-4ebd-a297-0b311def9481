import React, { Disp<PERSON>, SetStateAction } from 'react'

import Modal from '@/components/Modal'

import { useUser } from '@/lib/contexts/UserContext'
import { useGetCandidatesGroup, useMoveCandidatesToGroup } from '../../../api'
import { useBooleanStateControl, useErrorModalState } from '@/hooks'
import toast from 'react-hot-toast'
import { Button, ConfirmActionModal, ErrorModal, Input, LoadingOverlay } from '@/components/shared'
import { getInitials } from '@/utils/strings'
import { cn } from '@/utils'
import { format } from 'date-fns'
import { CVGroup, useGetCVGroups } from '@/app/(website-main)/e/my-candidates/misc/api/getCVGroups'
import CreateCVGroupModal from '@/app/(website-main)/e/my-candidates/misc/components/CreateCVGroupModal'


interface ModalProps {
    applications: (string | number)[];
    isModalOpen: boolean
    closeModal: () => void
    openModal: () => void
    job_id: number
}



const FilteredGroupsModal: React.FC<ModalProps> = ({ applications, isModalOpen, closeModal, openModal, job_id }) => {

    const { user } = useUser()
    const { data: CVGroups, refetch: refetchCVGroupsList } = useGetCVGroups(1)

    const [search, setSearch] = React.useState('');

    const filteredGroupOptions = CVGroups?.data.results.filter((option) => {
        const searchString = search.toLowerCase();
        const label = option.name.toLowerCase();
        const count = option.cv_count.toString().toLowerCase();
        const id = String(option.id).toLowerCase();

        return (
            label.includes(searchString) ||
            count.includes(searchString) ||
            id.includes(searchString)
        );
    });
    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,

    } = useErrorModalState();
    const {
        state: isConfirmationModalOpen,
        setTrue: openConfirmationModal,
        setFalse: closeConfirmationModal,
    } = useBooleanStateControl()
    const {
        state: isCreateGroupModalOpen,
        setTrue: openCreateGroupModal,
        setFalse: closeCreateGroupModal,
    } = useBooleanStateControl()


    const [selectedGroup, setSelectedGroup] = React.useState<CVGroup>({} as CVGroup);
    const createdGroupId = React.useRef<number | null>(null);
    const handleSelect = (currentValue: CVGroup) => {
        setSelectedGroup(currentValue)
        openConfirmationModal()
    };

    const [errorModalHeader, setErrorModalHeader] = React.useState<string>("Something went wrong");
    const { mutate: moveCandidates, isLoading: isMovingCandidates } = useMoveCandidatesToGroup(job_id, user?.email || "")
    const moveCandidatestoGroup = () => {
        const DTO = {
            application_ids: applications,
            group_id: selectedGroup.id,
        }
        moveCandidates(
            DTO,
            {
                onSuccess() {
                    toast.success("Candidates successfully moved to group")
                    closeConfirmationModal()
                    closeModal()
                },
                onError(error: any, text) {
                    console.log(error)
                    toast.error("Failed to move candidates to group")
                    if (error?.response?.data.error.includes("duplicate key value violates unique constraint")) {
                        setErrorModalHeader("Applicants already in group")
                        openErrorModalWithMessage(error?.response?.data.error || "Something went wrong, please try again.")
                    }
                },
            }
        )
    }




    return (
        <>
            <Modal is_open={isModalOpen} close={closeModal} title='Filtered candidates'
            // portalClass='lg:!min-w-[80vw] max-w-[1750px]'
            >
                <div className='relative grid grid-rows-[max-content_1fr_max-content] max-h-[90vh] overflow-y-hidden p-6'>
                    <Input
                        placeholder='Search groups...'
                        value={search}
                        onChange={(e) => setSearch(e.target.value)}

                    />
                    <div className='flex flex-col gap-3'>
                        {
                            filteredGroupOptions?.map((group) => {
                                const initials = getInitials(group.name);

                                return (
                                    <article
                                        className='relative flex select-none items-center rounded-md py-1.5 px-4 text-sm outline-none aria-selected:bg-blue-100/70 aria-selected:text-primary hover:bg-primary-light cursor-pointer'
                                        onClick={() => handleSelect(group)}
                                        key={group.id}

                                    >
                                        <div className="flex items-center gap-3.5">
                                            <span className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-xs text-white">
                                                {initials}
                                            </span>

                                            <section className="flex flex-col">
                                                <span>{group.name}</span>
                                                <div className='flex items-center divide-x-2'>
                                                    <span className={cn('text-[11px] text-[#646464] text-opacity-80 mr-1')}>
                                                        {group.cv_count} candidates
                                                    </span>
                                                    <span className={cn('text-[11px] text-[#646464] text-opacity-80 italic pl-1')}>
                                                        Last updated: {format(new Date(group.updated_at), "dd/MM/yyyy")}
                                                    </span>
                                                </div>
                                            </section>
                                        </div>

                                    </article>
                                );
                            })
                        }
                    </div>


                    <section className='flex flex-col items-center gap-2 pt-5 mt-auto'>
                        <p>------- OR ---------</p>
                        <Button className='w-full' onClick={() => {
                            openCreateGroupModal()
                        }}>
                            Create New Group
                        </Button>
                    </section>
                </div>
            </Modal>

            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                heading={errorModalHeader}
                subheading={
                    errorModalMessage || 'Please check your inputs and try again.'
                }
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button
                        className="grow bg-red-950 hover:border-red-950 hover:text-red-950 px-1.5 sm:text-sm md:px-6"
                        type="button"
                        onClick={closeErrorModal}
                    >
                        Okay
                    </Button>
                </div>
            </ErrorModal>

            <ConfirmActionModal
                isModalOpen={isConfirmationModalOpen}
                closeModal={closeConfirmationModal}
                title="Move candidates to group"
                confirmFunction={moveCandidatestoGroup}
            >
                <p className='text-[#8C8CA1] text-sm font-normal'>
                    You are about to move <span className='text-header-text font-bold mr-1'>{applications.length}</span> candidates to the group <span className='text-header-text font-bold mr-1'>{selectedGroup.name}</span>.,
                    Please be aware that it can take up to 5 minutes to reflect the new score.
                </p>
            </ConfirmActionModal>

            <LoadingOverlay isOpen={isMovingCandidates} />

            <CreateCVGroupModal
                isOpen={isCreateGroupModalOpen}
                closeModal={closeCreateGroupModal}
                onSuccess={(data) => {
                    refetchCVGroupsList()
                    openModal()
                    createdGroupId.current = data?.id
                }}
            />

        </>
    )
}

export default FilteredGroupsModal