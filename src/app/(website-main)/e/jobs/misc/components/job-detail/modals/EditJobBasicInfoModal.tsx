import React, { useEffect } from 'react'
import { AxiosError } from 'axios';
import { z } from 'zod';

import { cn } from '@/utils';
import { useErrorModalState } from '@/hooks';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { AmountInput, Button, ErrorModal, Input, LoaderBtn, LoadingOverlay, RadioGroup, Select, SingleDatePicker, Switch } from '@/components/shared'
import Modal from '@/components/Modal';
import { removeSpecialCharacters } from '@/utils/numbers';
import { zodResolver } from '@hookform/resolvers/zod';

import { ActiveJob } from '../../../types'
import { Controller, useForm } from 'react-hook-form'
import { usePatchEditJobDetails } from '../../../api'
import { currencies, industries, proficiency_levels, work_experiences, working_options, job_type as job_types, salary_types } from '../../../constants';
import { customFieldSchema } from '../../../utils/jobSchemas';
import { format } from 'date-fns';
import IndustrySelector from '../../job/create/IndustrySelector';
import useGetIndustries from '../../../api/getIndustries';

interface ModalProps {
    job: ActiveJob
    isOpen: boolean
    close: () => void
    refetch: () => void
}

const JobSchema = z.object({
    name: z.string().min(3, { message: 'Please enter job title' }),
    location: z.string().min(1, { message: 'Please enter location' }),
    is_multi_location: z.boolean(),
    preferred_locations: z.array(z.string()),
    industry: z.string().min(3, { message: 'Please select an industry' }),
    work_experience: z.string().min(1, { message: 'Please select an experience level' }),
    proficiency_level: z.string().min(1, { message: 'Please select a proficiency level' }),
    working_option: z.string().min(1, { message: 'Please select working option' }),
    job_type: z.string().min(3, { message: 'Please select an employment type' }),
    salary_currency: z.string(),
    min_salary: z.number().optional(),
    max_salary: z.number().optional(),
    salary_type: z.string().min(1, { message: 'Please select salary type' }).optional(),
    salary_negotiable: z.boolean(),
    fixed_salary: z.number().optional(),
    application_start_date: z.date(),
    application_deadline: z.date(),
    show_in_career_page: z.boolean().default(false),
    post_as_anonymous: z.boolean(),
    job_custom_fields: z.array(customFieldSchema).optional().default([]),


}).refine((data) => {
    if (data.salary_type === "RANGE") {
        if (typeof data.min_salary !== 'number' || isNaN(data.min_salary)) {
            throw new z.ZodError([{
                path: ['min_salary'],
                message: 'Please enter a minimum salary amount.',
                code: 'custom',
            }]);
        }
        if (typeof data.max_salary !== 'number' || isNaN(data.max_salary)) {
            throw new z.ZodError([{
                path: ['max_salary'],
                message: 'Please enter a maximum salary amount.',
                code: 'custom',
            }]);
        }
    }
    if (data.salary_type === "FIXED") {
        if (typeof data.fixed_salary !== 'number' || isNaN(data.fixed_salary)) {
            throw new z.ZodError([{
                path: ['fixed_salary'],
                message: 'Please enter an amount.',
                code: 'custom',
            }]);
        }
    }

    if (data.application_deadline && data.application_start_date) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const application_start_date = new Date(data.application_start_date);
        application_start_date.setHours(0, 0, 0, 0);
        const application_deadline = new Date(data.application_deadline);
        application_deadline.setHours(0, 0, 0, 0);
        if (application_deadline < application_start_date) {
            throw new z.ZodError([{
                path: ['application_deadline'],
                message: "End date can't be before start date",
                code: 'custom',
            }]);
        }
        // if (application_deadline < today) {
        //     throw new z.ZodError([{
        //         path: ['application_deadline'],
        //         message: "Deadline date can't be before today",
        //         code: 'custom',
        //     }]);
        // }
    }
    return true;
});
type EditJobBasicInfoFormType = z.infer<typeof JobSchema>

const EditJobBasicInfoModal: React.FC<ModalProps> = ({ job, isOpen, close, refetch }) => {
    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();
    const { post_as_anonymous, show_in_career_page, id, job_title, industry, job_type, location, application_start_date, application_deadline, is_multi_location, preferred_locations,
        salary_currency, max_salary, min_salary, salary_type, salary_negotiable, fixed_salary, proficiency_level, work_experience, working_option, job_custom_fields, } = job || {}
    const { data, isLoading: isLoadingIndustries } = useGetIndustries()


    const { handleSubmit, formState: { errors }, register, watch, getValues, trigger, setValue, setError, control, clearErrors } = useForm<EditJobBasicInfoFormType>({
        defaultValues: {
            name: job_title || undefined,
            location: location || undefined,
            is_multi_location: is_multi_location || false,
            preferred_locations: preferred_locations || undefined,
            industry: industry || undefined,
            work_experience: work_experience || undefined,
            proficiency_level: proficiency_level || undefined,
            working_option: working_option || undefined,
            job_type: job_type || undefined,
            salary_currency: salary_currency || undefined,
            min_salary: min_salary || undefined,
            max_salary: max_salary || undefined,
            salary_type: salary_type || undefined,
            salary_negotiable: salary_negotiable || false,
            fixed_salary: fixed_salary || undefined,
            application_start_date: new Date(application_start_date!!) || new Date(),
            application_deadline: new Date(application_deadline!!) || new Date(),
            show_in_career_page: show_in_career_page || false,
            post_as_anonymous: post_as_anonymous || true,
            job_custom_fields: job_custom_fields || [],
        },
        resolver: zodResolver(JobSchema)
    });

    const currentSalaryType = watch('salary_type');

    const { mutate: patchJobDetail, isLoading: isJobCreatingFromDraft } = usePatchEditJobDetails();
    const onSubmit = async (data: EditJobBasicInfoFormType) => {
        const isValid = await trigger();

        if (isValid) {
            const today = new Date().setHours(0, 0, 0, 0)
            const start = new Date(data.application_start_date!).setHours(0, 0, 0, 0)
            const dataToSubmit = {
                ...data,
                application_start_date: format(new Date(data.application_start_date || 0), "yyyy-MM-dd"),
                application_deadline: format(new Date(data.application_deadline || 0), "yyyy-MM-dd"),

            }


            patchJobDetail([String(id)!!, start > today ? { ...dataToSubmit, job_status: "QUEUED", } : { ...dataToSubmit, job_status: "OPEN", }], {
                onSuccess: (data) => {
                    close()
                    refetch()

                },
                onError(error) {
                    const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                    openErrorModalWithMessage(errorMessage);
                },
            })
        }
    };
    useEffect(() => {
        if (currentSalaryType == "RANGE") {
            setValue('fixed_salary', undefined);
        } else if (currentSalaryType == "FIXED") {
            setValue('min_salary', undefined);
            setValue('max_salary', undefined);
        } else if (currentSalaryType == "UNDISCLOSED") {
            setValue('min_salary', undefined);
            setValue('max_salary', undefined);
            setValue('fixed_salary', undefined);
        }
    }, [currentSalaryType])






    return (
        <Modal is_open={isOpen} close={close} title='Edit Job Details' portalClass="lg:max-w-[520px]">
            <form onSubmit={handleSubmit(onSubmit)} className='bg-white px-3  py-4 md:p-8 xl:p-10 rounded-xl overflow-y-scroll ' id="create-job-form">
                <div className='max-w-[900px] space-y-4'>
                    <header className='mb-6'>
                        <h4 className='text-header-text text-[1.05rem] font-medium'>Job post details</h4>
                        <p className='text-body-text  text-[0.875rem]'>A job posting represents a new opening or a vacancy listing.<br />
                            Kindly note that details provide here will be made available to applicants</p>
                    </header>

                    <div className="inputdiv">
                        <label htmlFor="name">Job Title (Role)</label>
                        <input
                            type="text"
                            placeholder="Enter role name"
                            id="name"
                            maxLength={100}
                            {...register('name', { required: 'Job role name is required' })}
                            className={cn('!bg-[#F5F7F9]', errors.name && 'error')}
                        />
                        {errors.name && <p className='formerror'>{String(errors.name.message)}</p>}
                    </div>

                    <Input
                        type="text"
                        variant="showcase"
                        placeholder="Enter location"
                        label='Location'
                        id="location"
                        maxLength={100}
                        {...register('location', { required: 'Location is required' })}
                        hasError={!!errors.location}
                        errorMessage={errors.location?.message as string}
                    />

                    <IndustrySelector
                        label="Industry"
                        variant="showcase"
                        {...register("industry")}
                        value={watch('industry')}
                        isLoadingOptions={isLoadingIndustries}
                        onChange={(val) => { setValue('industry', val); clearErrors("industry") }}
                        options={data?.map(ind => ({ name: ind.name, value: ind.name }))}
                        labelKey='name'
                        valueKey='value'
                        placeholder="Select industry"
                        hasError={!!errors.industry}
                        errorMessage={errors.industry?.message as string}
                    />

                    <Select
                        label="Experience"
                        variant="showcase"
                        {...register("work_experience")}
                        value={watch('work_experience')}
                        onChange={(value) => setValue('work_experience', value)}
                        options={work_experiences}
                        labelKey='name'
                        valueKey='value'
                        placeholder="Select required experience"
                        hasError={!!errors.industry}
                        errorMessage={errors.industry?.message as string}
                    />


                    <Select
                        label="Proficiency"
                        variant="showcase"
                        {...register("proficiency_level")}
                        value={watch('proficiency_level')}
                        onChange={(value) => setValue('proficiency_level', value)}
                        options={proficiency_levels}
                        labelKey='name'
                        valueKey='value'
                        placeholder="Select proficiency level"
                        hasError={!!errors.proficiency_level}
                        errorMessage={errors.proficiency_level?.message as string}

                    />

                    <Select
                        label="Working Option"
                        variant="showcase"
                        {...register("working_option")}
                        value={watch('working_option')}
                        onChange={(value) => setValue('working_option', value)}
                        options={working_options}
                        labelKey='name'
                        valueKey='value'
                        placeholder="Select work option"
                        hasError={!!errors.working_option}
                        errorMessage={errors.working_option?.message as string}

                    />



                    <RadioGroup
                        options={job_types}
                        fallback={"CONTRACT"}
                        onChange={(value) => setValue('job_type', value)}
                        label="Employment Type"
                        errors={errors}
                        value={watch('job_type')}
                        name='job_type'
                        variant="light"
                        arrangement='row'
                    />


                    <Select
                        label="Salary Option"
                        variant="showcase"
                        {...register("salary_type")}
                        value={watch('salary_type')}
                        onChange={(value) => setValue('salary_type', value as "FIXED" | "RANGE" | "UNDISCLOSED")}
                        options={salary_types}
                        valueKey='value'
                        labelKey='name'
                        placeholder="Select salary type"
                        hasError={!!errors.salary_type}
                        errorMessage={errors.salary_type?.message as string}
                        itemClass='text-[0.825rem]'
                        containerClass='!mt-2 w-full'
                    />

                    <fieldset className={cn('', currentSalaryType === "UNDISCLOSED" && "hidden")}>
                        <label htmlFor="" className="text-sm text-header-text">
                            {
                                currentSalaryType === "RANGE" ? "Salary Range" :
                                    currentSalaryType === "FIXED" ? "Salary Amount" : null
                            }
                        </label>

                        <div
                            className={cn('grid  gap-4',
                                currentSalaryType === "RANGE" && "grid-cols-[minmax(0,5rem)_repeat(2,minmax(0,1fr))]",
                                currentSalaryType === "FIXED" && "grid-cols-[minmax(0,5rem)_repeat(1,minmax(0,1fr))]",
                            )}
                        >
                            <Select
                                {...register('salary_currency')}
                                value={watch('salary_currency')}
                                options={currencies}
                                onChange={(value) => setValue('salary_currency', value)}
                                labelKey='name'
                                valueKey='value'
                                variant="showcase"
                                placeholder="Curr"
                                hasError={!!errors.salary_currency}
                                errorMessage={errors.salary_currency?.message as string}
                            />

                            {
                                currentSalaryType === "RANGE" ?
                                    <>
                                        <AmountInput
                                            {...register('min_salary')}
                                            hasError={!!errors.min_salary}
                                            errorMessage={errors.min_salary?.message as string}
                                            variant="showcase"
                                        />
                                        <AmountInput
                                            {...register('max_salary')}
                                            hasError={!!errors.max_salary}
                                            errorMessage={errors.max_salary?.message as string}
                                            variant="showcase"
                                        />

                                    </>
                                    :
                                    currentSalaryType === "FIXED" ?
                                        <AmountInput
                                            {...register('fixed_salary')}
                                            hasError={!!errors.fixed_salary}
                                            errorMessage={errors.fixed_salary?.message as string}
                                            variant="showcase"
                                        />
                                        : null
                            }

                        </div>

                    </fieldset>







                    <div className='inputdiv'>
                        <label className='!font-normal !text-[#4A4A68]'>
                            How long do you want this Job to be active for ?
                        </label>
                        <div className='max-xs:flex flex-wrap xs:grid grid-cols-2 gap-6 xs:gap-[2vw]'>
                            <Controller
                                control={control}
                                name="application_start_date"
                                render={({ field: { onChange, value } }) => (
                                    <SingleDatePicker
                                        className="py-2.5 bg-[#F5F7F9] w-full"
                                        id="application_start_date"
                                        label="Post date"
                                        placeholder="Start Date"
                                        value={watch('application_start_date') || value}
                                        onChange={onChange}
                                        errors={errors}
                                    />
                                )}
                            />
                            <Controller
                                control={control}
                                name="application_deadline"
                                render={({ field: { onChange, value } }) => (
                                    <SingleDatePicker
                                        className="py-2.5 bg-[#F5F7F9] w-full"
                                        id="application_deadline"
                                        label="End opening"
                                        placeholder="End Date"
                                        value={watch('application_deadline') || value}
                                        onChange={onChange}
                                        errors={errors}
                                    />
                                )}
                            />
                        </div>

                    </div>



                    <div className='flex items-start'>
                        <Controller
                            control={control}
                            name={"show_in_career_page"}
                            render={({ field: { onChange, value = false } }) => (
                                <div className=' flex items-center gap-1'>
                                    <label
                                        className="block"
                                        htmlFor="show_in_career_page"
                                    >
                                        Show in career page
                                    </label>
                                    <Switch
                                        checked={value || false}
                                        className="scale-[.75] my-2"
                                        id={"show_in_career_page"}
                                        onCheckedChange={onChange}
                                    />

                                </div>
                            )}
                        />
                    </div>

                </div >



                <div className='flex items-center justify-between mt-10'>
                    <Button variant='outlined' type="button" disabled={isJobCreatingFromDraft} icon={isJobCreatingFromDraft && <LoaderBtn />} onClick={() => close()}>
                        Cancel
                    </Button>
                    <Button className='ml-auto' type="submit" form="create-job-form" disabled={isJobCreatingFromDraft} icon={isJobCreatingFromDraft && <LoaderBtn />}>
                        Save
                    </Button>
                </div>

                <LoadingOverlay isOpen={isJobCreatingFromDraft} />
                <ErrorModal
                    isErrorModalOpen={isErrorModalOpen}
                    setErrorModalState={setErrorModalState}

                    subheading={
                        errorModalMessage || 'Please check your inputs and try again.'
                    }
                >
                    <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                        <Button
                            className="grow bg-red-950 hover:border-red-950 hover:text-red-950 px-1.5 sm:text-sm md:px-6"
                            type="button"
                            onClick={closeErrorModal}
                        >
                            Okay
                        </Button>
                    </div>
                </ErrorModal>
            </form>
        </Modal>

    )
}

export default EditJobBasicInfoModal