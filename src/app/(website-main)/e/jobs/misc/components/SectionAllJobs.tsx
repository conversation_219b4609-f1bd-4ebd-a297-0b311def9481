'use client';

import React, { useState } from 'react';
import useInfiniteScroll from 'react-infinite-scroll-hook';
import {
  Button,
  EmptyCard,
  LinkButton,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/shared';
import { Minus, Search } from '@/components/shared/icons';
import useGetJobs, { PaginatedJobTypes } from '../api/getCreatedJobs';
import { EmptyJobIcon, PlusIcon } from '../icons';
import { ActiveJob } from '../types';
import JobCard from './cards/JobCard';
import JobMatchLoader from './cards/JobLoader';
import Filter from './Filter';


const ActiveJobs = () => {
  const [searchParams, setSearchParams] = useState<string>('');
  const [filterUrl, setFilterUrl] = useState('');

  const {
    data,
    error,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isFetchingNextPage,
    isLoading: loading,
    refetch: refetchJobs,
  } = useGetJobs(filterUrl, searchParams);

  console.log(data, 'JOBS');
  const nextPage = hasNextPage !== undefined && data?.pages !== null;

  const [sentryRef] = useInfiniteScroll({
    loading: isFetchingNextPage || loading,
    hasNextPage: nextPage,
    onLoadMore: fetchNextPage,
    disabled: !!error,
    rootMargin: '0px 0px 400px 0px',
  });

  return (
    <div className="relative h-full overflow-x-auto md:mt-2">
      <section className="sticky top-0 flex flex-wrap items-center justify-between bg-white px-4 py-2 shadow-sm md:mx-4">
        <h2 className="max-w-max">
          Jobs:{' '}
          <span className="font-medium text-header-text">
            {data?.pages && data.totalCount}
          </span>
        </h2>

        <div className="ml-auto flex items-center gap-2">
          <div className="relative ml-auto">
            <Search className="absolute right-[5%] top-[25%]" />
            <input
              type="search"
              placeholder="Search"
              value={searchParams}
              onChange={e => setSearchParams(e.target.value)}
              className="rounded-lg border-[1.75px] border-[#D6D6D6] px-2.5 py-2 text-xs transition-all focus:border-primary focus:outline-none sm:px-4 md:text-[0.9rem]"
            />
          </div>

          <Popover>
            <PopoverTrigger>
              <span className="rounded-[0.35rem] border-[1.75px] border-[#D6D6D6] px-2.5 py-2 text-xs text-[#556575] sm:px-4 md:text-[0.875rem]">
                Filter
              </span>
            </PopoverTrigger>
            <PopoverContent className="max-h-96" align="end">
              <Filter filterUrl={filterUrl} setFilterUrl={setFilterUrl} />
            </PopoverContent>
          </Popover>
        </div>
      </section>

      <div className="w-full overflow-y-auto rounded-[1.25rem] bg-[#F8F9FB] px-5 py-4 pt-4 max-md:pb-16">
        {loading && (
          <div className="flex w-full flex-col gap-6">
            <JobMatchLoader match={10} />
          </div>
        )}

        <div className="flex flex-col gap-3">
          {data?.pages?.map((jobs: PaginatedJobTypes, index: number) => (
            <React.Fragment key={index}>
              {jobs?.results.length < 1 ? (
                <div
                  className="align-center flex min-h-full w-full grow justify-center "
                  key={index}
                >
                  <EmptyCard
                    title={
                      filterUrl.trim() == '' && searchParams.trim() == ''
                        ? 'No Jobs Created Yet'
                        : 'No jobs match your search or filter parameters'
                    }
                    icon={<EmptyJobIcon />}
                    contentClass="flex flex-col items-center gap-6"
                    titleClass="mt-8"
                    content={
                      <>
                        <div className="text-center">
                          {filterUrl.trim() == '' &&
                          searchParams.trim() == '' ? (
                            <p className="text-[0.875rem] font-normal">
                              Do you have a new job position opened up? You can
                              have them reflect here.
                            </p>
                          ) : (
                            <p className="text-[0.875rem] font-normal">
                              No jobs matches your filter parameters, Change or
                              clear them and try again.
                            </p>
                          )}
                        </div>
                        {filterUrl.trim() == '' && searchParams.trim() == '' ? (
                          <LinkButton
                            href="./create"
                            variant={'default'}
                            icon={<PlusIcon width={17} height={17} />}
                          >
                            Create Job
                          </LinkButton>
                        ) : (
                          <Button
                            onClick={() => {
                              setFilterUrl('');
                              setSearchParams('');
                            }}
                            variant={'default'}
                            icon={<Minus fill="white" width={17} height={17} />}
                          >
                            Clear Filter
                          </Button>
                        )}
                      </>
                    }
                  />
                </div>
              ) : (
                jobs?.results?.map((job: ActiveJob) => (
                  <React.Fragment key={job?.id}>
                    {!loading && <JobCard job={job} refetch={refetchJobs} />}
                  </React.Fragment>
                ))
              )}
            </React.Fragment>
          ))}

          {(isFetchingNextPage || hasNextPage) && (
            <div className=" flex w-full flex-col gap-6" ref={sentryRef}>
              {isFetchingNextPage && <JobMatchLoader match={3} />}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ActiveJobs;

