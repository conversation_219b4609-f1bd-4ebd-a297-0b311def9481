'use client';

import { addWeeks, format, subWeeks } from 'date-fns';
import React, { useState } from 'react';
import toast from 'react-hot-toast';
import { But<PERSON>, Modal } from '@/components/shared';
import { Calendar } from '@/components/shared/calendar';
import {
  useCheckMemberAvailability,
  type CheckAvailabilityRequest,
} from '../../../api/checkMemberAvailability';

interface CheckAvailabilityModalProps {
  isOpen: boolean;
  onClose: () => void;
  memberEmail: string;
  memberName: string;
  onAvailabilityChecked?: (
    isAvailable: boolean,
    timeSlot?: string,
    availabilityData?: {
      date: string;
      start_time: string;
      end_time: string;
    }
  ) => void;
}

const timeSlots = [
  '08:00',
  '08:30',
  '09:00',
  '09:30',
  '10:00',
  '10:30',
  '11:00',
  '11:30',
  '12:00',
  '12:30',
  '01:00',
  '01:30',
  '02:00',
  '02:30',
  '03:00',
  '03:30',
];

const CheckAvailabilityModal: React.FC<CheckAvailabilityModalProps> = ({
  isOpen,
  onClose,
  memberEmail,
  memberName,
  onAvailabilityChecked,
}) => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    new Date()
  );
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [checkedAvailability, setCheckedAvailability] = useState<{
    date: string;
    time: string;
    available: boolean;
  } | null>(null);

  const checkAvailabilityMutation = useCheckMemberAvailability();

  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
    setCheckedAvailability(null); // Reset availability when date changes
  };

  const handleTimeSelect = (time: string) => {
    setSelectedTime(time);
    setCheckedAvailability(null); // Reset availability when time changes
  };

  const handleCheckAvailability = async () => {
    if (!selectedDate || !selectedTime) {
      toast.error('Please select both date and time');
      return;
    }

    const endTime = calculateEndTime(selectedTime);
    const requestData: CheckAvailabilityRequest = {
      date: format(selectedDate, 'yyyy-MM-dd'),
      start_time: `${selectedTime}:00`, // Add seconds for API compatibility - this API expects HH:MM:SS
      end_time: `${endTime}:00`, // Add seconds for API compatibility - this API expects HH:MM:SS
      team_member_email: memberEmail,
    };

    try {
      const result = await checkAvailabilityMutation.mutateAsync(requestData);

      setCheckedAvailability({
        date: format(selectedDate, 'yyyy-MM-dd'),
        time: selectedTime,
        available: result.available,
      });

      if (result.available) {
        toast.success('Team member is available!');
      } else {
        toast.error(
          result.message || 'Team member is not available at this time'
        );
      }

      onAvailabilityChecked?.(
        result.available,
        `${selectedTime} - ${endTime}`,
        {
          date: format(selectedDate, 'yyyy-MM-dd'),
          start_time: selectedTime, // Keep HH:MM format without seconds
          end_time: endTime, // Keep HH:MM format without seconds
        }
      );
    } catch (error) {
      toast.error('Failed to check availability');
      console.error('Availability check error:', error);
    }
  };

  const calculateEndTime = (startTime: string): string => {
    const [hours, minutes] = startTime.split(':').map(Number);
    const endHours = hours + 1; // Assuming 1-hour interview slots
    const endMinutes = minutes;

    return `${endHours.toString().padStart(2, '0')}:${endMinutes
      .toString()
      .padStart(2, '0')}`;
  };

  const handleScheduleInterview = () => {
    if (checkedAvailability?.available && selectedDate && selectedTime) {
      const timeSlot = `${selectedTime} - ${calculateEndTime(selectedTime)}`;
      onAvailabilityChecked?.(true, timeSlot);
      onClose();
      toast.success('Interview scheduled successfully!');
    }
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      setCurrentMonth(subWeeks(currentMonth, 4));
    } else {
      setCurrentMonth(addWeeks(currentMonth, 4));
    }
  };

  return (
    <Modal
      isModalOpen={isOpen}
      closeModal={onClose}
      heading={'Team Member Availability Check'}
      customWidths
      contentClass="max-w-3xl"
    >
      <div className="p-6">
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-100">
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M6.66667 1.66667V5M13.3333 1.66667V5M2.5 8.33333H17.5M4.16667 3.33333H15.8333C16.7538 3.33333 17.5 4.07953 17.5 5V16.6667C17.5 17.5871 16.7538 18.3333 15.8333 18.3333H4.16667C3.24619 18.3333 2.5 17.5871 2.5 16.6667V5C2.5 4.07953 3.24619 3.33333 4.16667 3.33333Z"
                  stroke="#8B5CF6"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-header-text">
                Check Availability
              </h3>
              <p className="text-sm text-body-text/80">
                Schedule interview with {memberName}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="rounded-lg p-1 text-gray-400 transition-colors hover:bg-gray-100 hover:text-gray-600"
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 5L5 15M5 5L15 15"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>

        <div className="space-y-6">
          <div className="space-y-3">
            <h4 className="text-sm font-semibold text-header-text">
              Choose preferred date for the interview
            </h4>
            <div className="rounded-lg border border-gray-200 bg-gray-50/50">
              <div className="mb-4 flex items-center justify-between">
                <button
                  onClick={() => navigateMonth('prev')}
                  className="rounded-lg border border-transparent p-2 transition-colors hover:border-purple-200 hover:bg-white"
                >
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10 12L6 8L10 4"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </button>
                <h5 className="font-semibold text-header-text">
                  {format(currentMonth, 'MMMM yyyy')}
                </h5>
                <button
                  onClick={() => navigateMonth('next')}
                  className="rounded-lg border border-transparent p-2 transition-colors hover:border-purple-200 hover:bg-white"
                >
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M6 4L10 8L6 12"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </button>
              </div>

              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={handleDateSelect}
                month={currentMonth}
                onMonthChange={setCurrentMonth}
                className="w-full rounded-lg bg-white"
                showLeftIcon={false}
                showRightIcon={false}
              />
            </div>
          </div>

          <div className="space-y-3">
            <h4 className="text-sm font-semibold text-header-text">
              Choose preferred time for the interview
            </h4>
            <div className="grid grid-cols-4 gap-2">
              {timeSlots.map(time => (
                <button
                  key={time}
                  onClick={() => handleTimeSelect(time)}
                  className={`rounded-lg border px-3 py-2.5 text-sm font-medium transition-all duration-200 ${
                    selectedTime === time
                      ? 'border-primary bg-primary text-white shadow-sm'
                      : 'border-gray-300 bg-white text-gray-700 hover:border-purple-300 hover:bg-purple-50 hover:text-purple-700'
                  }`}
                >
                  {time}
                </button>
              ))}
            </div>
          </div>

          {checkedAvailability && (
            <div
              className={`rounded-lg border p-4 transition-colors ${
                checkedAvailability.available
                  ? 'border-green-200 bg-green-50 hover:bg-green-100'
                  : 'border-red-200 bg-red-50 hover:bg-red-100'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div
                    className={`h-4 w-4 rounded-full ${
                      checkedAvailability.available
                        ? 'bg-green-500'
                        : 'bg-red-500'
                    }`}
                  />
                  <div>
                    <span
                      className={`text-sm font-semibold ${
                        checkedAvailability.available
                          ? 'text-green-800'
                          : 'text-red-800'
                      }`}
                    >
                      {checkedAvailability.available
                        ? 'Available'
                        : 'Not Available'}
                    </span>
                    <p
                      className={`mt-0.5 text-xs ${
                        checkedAvailability.available
                          ? 'text-green-600'
                          : 'text-red-600'
                      }`}
                    >
                      {format(
                        new Date(checkedAvailability.date),
                        'EEEE, MMMM d, yyyy'
                      )}{' '}
                      at {checkedAvailability.time}
                    </p>
                  </div>
                </div>
                <span
                  className={`rounded-full px-2 py-1 text-xs font-medium ${
                    checkedAvailability.available
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}
                >
                  {checkedAvailability.available ? 'Confirmed' : 'Conflict'}
                </span>
              </div>
            </div>
          )}

          <div className="flex space-x-3">
            <Button variant="outlined" onClick={onClose} className="flex-1">
              Cancel
            </Button>

            {!checkedAvailability ? (
              <Button
                onClick={handleCheckAvailability}
                disabled={
                  !selectedDate ||
                  !selectedTime ||
                  checkAvailabilityMutation.isLoading
                }
                className="flex-1"
              >
                {checkAvailabilityMutation.isLoading
                  ? 'Checking...'
                  : 'Check Availability'}
              </Button>
            ) : checkedAvailability.available ? (
              <Button onClick={handleScheduleInterview} className="flex-1">
                Schedule Interview
              </Button>
            ) : (
              <Button
                onClick={handleCheckAvailability}
                disabled={
                  !selectedDate ||
                  !selectedTime ||
                  checkAvailabilityMutation.isLoading
                }
                className="flex-1"
              >
                Check Different Time
              </Button>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default CheckAvailabilityModal;
