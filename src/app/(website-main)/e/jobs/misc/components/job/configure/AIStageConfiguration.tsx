'use client';

import React from 'react';
import type {
  Control,
  FieldErrors,
  UseFormSetValue,
  UseFormWatch,
} from 'react-hook-form';
import { Controller, useFieldArray } from 'react-hook-form';
import toast from 'react-hot-toast';
import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionItem,
  AccordionTrigger,
  Button,
  LoaderBtn,
  RadioGroup,
  Select,
} from '@/components/shared';
import { Plus } from '@/components/shared/icons';
import {
  useGenerateResponsibilities,
  useGenerateStageQuestions,
  useGenerateStageSummary,
  useGetTeamMembers,
} from '../../../api';
import { XIcon } from '../../../icons';
import type { AIJobConfiguration } from '../../../utils/aiJobConfigurationSchema';
import { TeamMemberCombobox } from '../create/TeamMemberDropDown';
import CheckAvailabilityModal from './CheckAvailabilityModal';

// Clock icon component
const ClockIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8 14.6667C11.6819 14.6667 14.6667 11.6819 14.6667 8C14.6667 4.31814 11.6819 1.33334 8 1.33334C4.31814 1.33334 1.33334 4.31814 1.33334 8C1.33334 11.6819 4.31814 14.6667 8 14.6667Z"
      stroke="#9CA3AF"
      strokeWidth="1.33333"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8 4V8L10.6667 9.33333"
      stroke="#9CA3AF"
      strokeWidth="1.33333"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

interface AIStageConfigurationProps {
  stageName: string;
  stageKey: string; // Changed to string to support array paths like "interview_setup_stages.0"
  control: Control<AIJobConfiguration>;
  errors: FieldErrors<AIJobConfiguration>;
  setValue: UseFormSetValue<AIJobConfiguration>;
  watch: UseFormWatch<AIJobConfiguration>;
  isExpanded?: boolean;
  onToggle?: () => void;
  stageNumber?: number;
  jobUniqueId?: string;
}

const AIStageConfiguration: React.FC<AIStageConfigurationProps> = ({
  stageName,
  stageKey,
  control,
  errors,
  setValue,
  watch,
  isExpanded = false,
  onToggle,
  stageNumber = 1,
  jobUniqueId,
}) => {
  const {
    fields: questionFields,
    append: appendQuestion,
    remove: removeQuestion,
  } = useFieldArray({
    control: control as any,
    name: `${stageKey}.custom_questions`,
  });
  const [newQuestion, setNewQuestion] = React.useState('');
  const [availabilityModal, setAvailabilityModal] = React.useState<{
    isOpen: boolean;
    memberEmail: string;
    memberName: string;
    memberId: string;
  }>({
    isOpen: false,
    memberEmail: '',
    memberName: '',
    memberId: '',
  });

  // State to track availability results for display
  const [availabilityResults, setAvailabilityResults] = React.useState<{
    [memberEmail: string]: {
      date: string;
      start_time: string;
      end_time: string;
      available: boolean;
      checked_at: string;
    }[];
  }>({});

  const { data: teamMembers } = useGetTeamMembers();

  // Generate responsibilities mutation
  const { mutate: generateResponsibilities, isLoading: isGeneratingDetails } =
    useGenerateResponsibilities();

  // Generate stage summary mutation
  const { mutate: generateStageSummary, isLoading: isGeneratingStageSummary } =
    useGenerateStageSummary();

  // Generate stage questions mutation
  const {
    mutate: generateStageQuestions,
    isLoading: isGeneratingStageQuestions,
  } = useGenerateStageQuestions();

  const handleAddQuestion = () => {
    if (newQuestion.trim()) {
      appendQuestion(newQuestion.trim());
      setNewQuestion('');
    }
  };

  const handleCheckAvailability = (
    memberId: string,
    memberEmail: string,
    memberName: string
  ) => {
    setAvailabilityModal({
      isOpen: true,
      memberEmail,
      memberName,
      memberId,
    });
  };

  const handleCloseAvailabilityModal = () => {
    setAvailabilityModal({
      isOpen: false,
      memberEmail: '',
      memberName: '',
      memberId: '',
    });
  };

  const handleAvailabilityChecked = (
    isAvailable: boolean,
    timeSlot?: string,
    availabilityData?: {
      date: string;
      start_time: string;
      end_time: string;
    }
  ) => {
    if (!availabilityData) return;

    const memberEmail = availabilityModal.memberEmail;
    const newAvailabilityEntry = {
      date: availabilityData.date,
      start_time: availabilityData.start_time, // Now in HH:MM format from modal (fixed)
      end_time: availabilityData.end_time, // Now in HH:MM format from modal (fixed)
      team_member_email: memberEmail,
      available: isAvailable,
      checked_at: new Date().toISOString(),
    };

    // Update the availability results state for UI display
    setAvailabilityResults(prev => ({
      ...prev,
      [memberEmail]: [
        ...(prev[memberEmail] || []),
        {
          date: availabilityData.date,
          start_time: availabilityData.start_time,
          end_time: availabilityData.end_time,
          available: isAvailable,
          checked_at: new Date().toISOString(),
        },
      ],
    }));

    // Update the form data with the availability information
    const currentAvailability =
      watch(`${stageKey}.team_members_availability` as any) || [];

    // Check if this exact availability check already exists to prevent duplicates
    const existingIndex = currentAvailability.findIndex(
      (existing: any) =>
        existing.team_member_email === memberEmail &&
        existing.date === availabilityData.date &&
        existing.start_time === availabilityData.start_time &&
        existing.end_time === availabilityData.end_time
    );

    let updatedAvailability;
    if (existingIndex >= 0) {
      // Update existing entry
      updatedAvailability = [...currentAvailability];
      updatedAvailability[existingIndex] = newAvailabilityEntry;
    } else {
      // Add new entry
      updatedAvailability = [...currentAvailability, newAvailabilityEntry];
    }

    setValue(
      `${stageKey}.team_members_availability` as any,
      updatedAvailability
    );
  };

  const handleGenerateDetails = () => {
    if (!jobUniqueId) {
      toast.error('Job ID is required for generating stage details');
      return;
    }

    // Map stage names to API expected values
    const stageNameMapping: { [key: string]: string } = {
      Initial: 'initial_screening',
      Technical: 'technical_screening',
      Final: 'final_interview',
      // Handle cases where " Stage" suffix might be present
      'Initial Stage': 'initial_screening',
      'Technical Stage': 'technical_screening',
      'Final Stage': 'final_interview',
    };

    // Get the stage name for API from the mapping, fallback to the original logic
    const stageNameForApi =
      stageNameMapping[stageName] ||
      stageName.replace(' Stage', '').toLowerCase().replace(/\s+/g, '_');

    generateStageSummary(
      {
        jobUniqueId,
        stage: stageNameForApi,
      },
      {
        onSuccess: data => {
          // Update the description field with the generated stage summary
          setValue(`${stageKey}.description` as any, data.data.summary);
          toast.success('Stage details generated successfully!');
        },
        onError: (error: any) => {
          console.error('Error generating stage details:', error);
          toast.error('Failed to generate stage details. Please try again.');
        },
      }
    );
  };

  const handleGenerateQuestionForNewInput = () => {
    if (!jobUniqueId) {
      toast.error('Job ID is required for generating question suggestions');
      return;
    }

    // Map stage names to API expected values
    const stageNameMapping: { [key: string]: string } = {
      Initial: 'initial_screening',
      Technical: 'technical_screening',
      Final: 'final_interview',
      // Handle cases where " Stage" suffix might be present
      'Initial Stage': 'initial_screening',
      'Technical Stage': 'technical_screening',
      'Final Stage': 'final_interview',
    };

    // Get the stage name for API from the mapping, fallback to the original logic
    const stageNameForApi =
      stageNameMapping[stageName] ||
      stageName.replace(' Stage', '').toLowerCase().replace(/\s+/g, '_');

    generateStageQuestions(
      {
        jobUniqueId,
        stage: stageNameForApi,
      },
      {
        onSuccess: data => {
          // Get a random suggested question and prefill the new question input field
          if (data.data.suggestions && data.data.suggestions.length > 0) {
            const randomIndex = Math.floor(
              Math.random() * data.data.suggestions.length
            );
            const suggestedQuestion = data.data.suggestions[randomIndex];
            setNewQuestion(suggestedQuestion);
            toast.success('Question suggestion applied successfully!');
          } else {
            toast.error('No question suggestions available for this stage.');
          }
        },
        onError: (error: any) => {
          console.error('Error generating question suggestions:', error);
          toast.error(
            'Failed to generate question suggestions. Please try again.'
          );
        },
      }
    );
  };

  const overallConductMode = watch('conduct_mode');
  const stageConductMode = watch(`${stageKey}.conduct_mode` as any);
  const stageData = watch(stageKey as any) as any;

  // Get current interview duration for display
  const interviewDuration = stageData?.interview_duration || '01:30';
  const [hours, minutes] = interviewDuration.split(':');
  const durationDisplay = `${parseInt(hours)}h ${parseInt(minutes)}m`;

  return (
    <>
      <Accordion
        type="single"
        collapsible
        className="overflow-hidden rounded-lg border opacity-50"
        disabled
      >
        <AccordionItem value={stageKey} className="border-0">
          <AccordionTrigger
            className="rounded-t-lg bg-[#F5F3FF] px-4 py-3 hover:no-underline"
            onClick={onToggle}
          >
            <div className="flex w-full items-center justify-between gap-3 pr-2">
              <div className="flex items-center gap-2">
                <span className="text-[0.90625rem] font-medium text-header-text">
                  Stage:{' '}
                </span>
                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-xs font-medium text-white">
                  {stageNumber}
                </div>
                <div className="text-[0.90625rem] font-medium text-header-text">
                  {stageName}
                </div>
              </div>
              <div className="flex items-center gap-1 text-sm text-gray-500">
                <ClockIcon />
                <span>{durationDisplay}</span>
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent
            className="space-y-6 px-4 pb-4 pt-2"
            hidden={!isExpanded}
          >
            {/* Conduct Mode - Only show in HYBRID mode */}
            {overallConductMode === 'HYBRID' && (
              <div className="space-y-3">
                <h5 className="text-[0.8125rem] font-medium text-header-text">
                  Conduct mode
                </h5>
                <Controller
                  control={control}
                  name={`${stageKey}.conduct_mode` as any}
                  render={({ field }) => (
                    <Select
                      value={field.value as string}
                      onChange={field.onChange}
                      options={[
                        { name: 'AI-led', value: 'AI_ONLY' },
                        { name: 'Human-Led', value: 'HUMAN_RECRUITER' },
                      ]}
                      labelKey="name"
                      valueKey="value"
                      placeholder="Select conduct mode"
                      variant="dark_showcase"
                      name={`${stageKey}.conduct_mode`}
                    />
                  )}
                />
              </div>
            )}

            {/* Interview Duration */}
            <div className="space-y-3">
              <h5 className="text-[0.8125rem] font-medium text-header-text">
                Interview duration
              </h5>
              <div className="grid grid-cols-2 gap-4">
                <Controller
                  control={control}
                  name={`${stageKey}.interview_duration` as any}
                  render={({ field }) => {
                    const currentValue = (field.value as string) || '';
                    const [hours] = currentValue.split(':');
                    const currentHours = hours || '';

                    return (
                      <Select
                        value={currentHours}
                        onChange={value => {
                          const [_, minutes] = (
                            (field.value as string) || ''
                          ).split(':');
                          const newDuration = `${value}:${minutes || '00'}`;
                          field.onChange(newDuration);
                        }}
                        options={[
                          { name: '0 hour', value: '00' },
                          { name: '1 hour', value: '01' },
                          { name: '2 hours', value: '02' },
                          { name: '3 hours', value: '03' },
                        ]}
                        labelKey="name"
                        valueKey="value"
                        placeholder="Select hours"
                        variant="dark_showcase"
                        name={`${stageKey}.interview_duration_hours`}
                      />
                    );
                  }}
                />
                <Controller
                  control={control}
                  name={`${stageKey}.interview_duration` as any}
                  render={({ field }) => {
                    const currentValue = (field.value as string) || '';
                    const [_, minutes] = currentValue.split(':');
                    const currentMinutes = minutes || '';

                    return (
                      <Select
                        value={currentMinutes}
                        onChange={value => {
                          const [hours] = ((field.value as string) || '').split(
                            ':'
                          );
                          const newDuration = `${hours || '00'}:${value}`;
                          field.onChange(newDuration);
                        }}
                        options={[
                          { name: '00 minutes', value: '00' },
                          { name: '15 minutes', value: '15' },
                          { name: '30 minutes', value: '30' },
                          { name: '45 minutes', value: '45' },
                        ]}
                        labelKey="name"
                        valueKey="value"
                        placeholder="Select minutes"
                        variant="dark_showcase"
                        name={`${stageKey}.interview_duration_minutes`}
                      />
                    );
                  }}
                />
              </div>
            </div>

            {/* Team Section */}
            <div className="space-y-3">
              <h5 className="text-[0.8125rem] font-medium text-header-text">
                TEAM
              </h5>
              <p className="text-sm text-gray-600">
                Search and add team members you'd like to be part of this
                recruitment process.
              </p>

              <fieldset className="my-8">
                <legend>
                  <h5 className="text-[0.75rem] font-medium text-header-text">
                    Add Team Members
                  </h5>
                </legend>
                <Controller
                  control={control as any}
                  name={`${stageKey}.team_members`}
                  render={({ field }) => (
                    <div>
                      <TeamMemberCombobox
                        setValue={setValue as UseFormSetValue<any>}
                        watch={watch as UseFormWatch<any>}
                        teamMemberKey={`${stageKey}.team_members`}
                      />
                    </div>
                  )}
                />
              </fieldset>
            </div>

            <div className="my-6 border-t border-gray-200"></div>

            {/* Set Scheduling Options */}
            <div className="space-y-3">
              <h5 className="text-[0.8125rem] font-medium text-header-text">
                SET SCHEDULING OPTIONS
              </h5>
              <p className="text-sm text-gray-600">
                Choose how the interview scheduling will occur for this job
                role. Configure whether scheduling is immediate, delayed, or
                based on candidate preferences.
              </p>

              <div className="space-y-3">
                <Controller
                  control={control}
                  name={`${stageKey}.scheduling_options` as any}
                  render={({ field }) => (
                    <RadioGroup
                      options={[
                        // {
                        //   name: 'Schedule later after job posting',
                        //   value: 'after_job_posting',
                        // },
                        {
                          name: 'Schedule within a duration span',
                          value: 'within_a_duration_span',
                        },
                        // {
                        //   name: 'Schedule based on candidate dates',
                        //   value: 'base_on_candidate_dates',
                        // },
                      ]}
                      onChange={field.onChange}
                      value={field.value as string}
                      name={`${stageKey}.scheduling_options`}
                      variant="light"
                      arrangement="row"
                    />
                  )}
                />
              </div>
            </div>

            <div className="my-6 border-t border-gray-200"></div>

            {/* Team Members Availability - Only show in HYBRID mode */}
            {overallConductMode === 'HYBRID' && (
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-purple-100">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M5.33333 1.33333V4M10.6667 1.33333V4M2 6.66667H14M3.33333 2.66667H12.6667C13.4031 2.66667 14 3.26362 14 4V13.3333C14 14.0697 13.4031 14.6667 12.6667 14.6667H3.33333C2.59695 14.6667 2 14.0697 2 13.3333V4C2 3.26362 2.59695 2.66667 3.33333 2.66667Z"
                        stroke="#8B5CF6"
                        strokeWidth="1.2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                  <h5 className="text-sm font-semibold text-header-text">
                    Team Members Availability
                  </h5>
                </div>

                <div className="space-y-3">
                  {stageData?.team_members &&
                  (stageData.team_members as (string | number)[]).length > 0 ? (
                    (stageData.team_members as (string | number)[]).map(
                      (memberId, index) => {
                        // Convert to number for finding team member
                        const memberIdNum =
                          typeof memberId === 'string'
                            ? parseInt(memberId, 10)
                            : memberId;
                        const member = teamMembers?.find(
                          tm => tm.id === memberIdNum
                        );
                        const memberEmail = member?.user_email || '';
                        const memberAvailability =
                          availabilityResults[memberEmail] || [];

                        return (
                          <div
                            key={index}
                            className="rounded-lg border border-gray-200 bg-white p-4 transition-colors hover:border-purple-200"
                          >
                            <div className="mb-3 flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <div className="flex items-center space-x-2">
                                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100">
                                    <span className="text-xs font-medium text-gray-600">
                                      {member?.user?.first_name?.[0] ||
                                        member?.user_email?.[0]?.toUpperCase() ||
                                        'M'}
                                    </span>
                                  </div>
                                  <div>
                                    <span className="text-sm font-medium text-gray-900">
                                      {member?.user?.first_name &&
                                      member?.user?.last_name
                                        ? `${member.user.first_name} ${member.user.last_name}`
                                        : member?.user_email ||
                                          `Member ${memberId}`}
                                    </span>
                                    {member?.user_email &&
                                      member?.user?.first_name && (
                                        <p className="text-xs text-gray-500">
                                          {member.user_email}
                                        </p>
                                      )}
                                  </div>
                                </div>
                                {memberAvailability.length > 0 && (
                                  <span className="rounded-full bg-purple-100 px-2 py-1 text-xs font-medium text-purple-700">
                                    {memberAvailability.length} check
                                    {memberAvailability.length !== 1 ? 's' : ''}
                                  </span>
                                )}
                              </div>
                              <Button
                                variant="light"
                                size="small"
                                className="text-sm font-medium"
                                type="button"
                                onClick={() =>
                                  handleCheckAvailability(
                                    String(memberId),
                                    memberEmail,
                                    member?.user?.first_name &&
                                      member?.user?.last_name
                                      ? `${member.user.first_name} ${member.user.last_name}`
                                      : memberEmail || `Member ${memberId}`
                                  )
                                }
                              >
                                Check Availability
                              </Button>
                            </div>

                            {/* Display availability results */}
                            {memberAvailability.length > 0 && (
                              <div className="space-y-3 border-t border-gray-100 pt-3">
                                <h6 className="text-xs font-semibold uppercase tracking-wide text-gray-700">
                                  Availability History
                                </h6>
                                <div className="space-y-2">
                                  {memberAvailability.map(
                                    (availability, availIndex) => (
                                      <div
                                        key={availIndex}
                                        className={`flex items-center justify-between rounded-lg border p-3 transition-colors ${
                                          availability.available
                                            ? 'border-green-200 bg-green-50 hover:bg-green-100'
                                            : 'border-red-200 bg-red-50 hover:bg-red-100'
                                        }`}
                                      >
                                        <div className="flex items-center space-x-3">
                                          <div
                                            className={`h-3 w-3 rounded-full ${
                                              availability.available
                                                ? 'bg-green-500'
                                                : 'bg-red-500'
                                            }`}
                                          ></div>
                                          <div className="flex flex-col">
                                            <span className="text-sm font-medium text-gray-900">
                                              {availability.date}
                                            </span>
                                            <span className="text-xs text-gray-600">
                                              {availability.start_time} -{' '}
                                              {availability.end_time}
                                            </span>
                                          </div>
                                        </div>
                                        <span
                                          className={`rounded-full px-2 py-1 text-sm font-semibold ${
                                            availability.available
                                              ? 'bg-green-100 text-green-800'
                                              : 'bg-red-100 text-red-800'
                                          }`}
                                        >
                                          {availability.available
                                            ? 'Available'
                                            : 'Unavailable'}
                                        </span>
                                      </div>
                                    )
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        );
                      }
                    )
                  ) : (
                    <div className="py-4 text-center text-sm text-gray-500">
                      No team members selected. Add team members above to see
                      their availability.
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Description */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h5 className="text-[0.8125rem] font-medium text-header-text">
                  Description (optional)
                </h5>
                <Button
                  variant="light"
                  size="tiny"
                  className="text-xs text-primary"
                  type="button"
                  onClick={handleGenerateDetails}
                  disabled={isGeneratingStageSummary || !jobUniqueId}
                >
                  Generate details
                  {isGeneratingStageSummary && (
                    <LoaderBtn width={12} height={12} />
                  )}
                </Button>
              </div>
              <Controller
                control={control}
                name={`${stageKey}.description` as any}
                render={({ field }) => (
                  <div className="inputdiv">
                    <textarea
                      {...field}
                      placeholder="Enter details about what will be covered in this stage"
                      className="h-24 w-full resize-none rounded-md border border-gray-300 bg-gray-50 p-3 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                  </div>
                )}
              />
            </div>

            <div className="my-6 border-t border-gray-200"></div>

            {/* Custom Question Setup */}
            <div className="space-y-3">
              <h5 className="text-[0.8125rem] font-medium text-header-text">
                CUSTOM QUESTION SETUP
              </h5>
              <p className="text-sm text-gray-600">
                Define the questions the AI or human recruiter will ask during
                the interview.
              </p>

              <div className="flex flex-col gap-2">
                <div className="flex gap-2">
                  <input
                    type="text"
                    placeholder="Enter a custom question..."
                    value={newQuestion}
                    onChange={e => setNewQuestion(e.target.value)}
                    className="flex-1 rounded-md border border-gray-300 bg-gray-50 p-3 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
                    onKeyDown={e => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAddQuestion();
                      }
                    }}
                  />
                  <Button
                    variant="light"
                    size="tiny"
                    className="whitespace-nowrap text-xs text-primary"
                    type="button"
                    onClick={handleGenerateQuestionForNewInput}
                    disabled={isGeneratingStageQuestions || !jobUniqueId}
                  >
                    Suggested Questions
                    {isGeneratingStageQuestions && (
                      <LoaderBtn width={12} height={12} />
                    )}
                  </Button>
                </div>
                <Button
                  icon={<Plus />}
                  variant="light"
                  type="button"
                  size="tiny"
                  onClick={handleAddQuestion}
                >
                  Add Custom Questions
                </Button>
              </div>
            </div>

            <div className="my-6 border-t border-gray-200"></div>

            {/* Questions */}
            <div className="space-y-3">
              <h5 className="text-[0.8125rem] font-medium text-header-text">
                QUESTIONS
              </h5>

              <div className="space-y-3">
                {questionFields.map((field, index) => (
                  <div key={field.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">
                        Question {index + 1}
                      </span>
                      <div className="flex items-center space-x-2">
                        <button className="text-gray-400 hover:text-gray-600">
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M2 4H14M2 8H14M2 12H14"
                              stroke="currentColor"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                            />
                          </svg>
                        </button>
                        <XIcon
                          className="h-4 w-4 cursor-pointer text-gray-400 hover:text-red-500"
                          onClick={() => removeQuestion(index)}
                        />
                      </div>
                    </div>
                    <Controller
                      control={control}
                      name={`${stageKey}.custom_questions.${index}` as any}
                      render={({ field }) => (
                        <textarea
                          {...field}
                          placeholder="Could you elaborate on your role as a product designer at your previous place of work?"
                          className="h-20 w-full resize-none rounded-md border border-gray-300 bg-gray-50 p-3 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
                        />
                      )}
                    />
                  </div>
                ))}
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      {/* Availability Check Modal */}
      <CheckAvailabilityModal
        isOpen={availabilityModal.isOpen}
        onClose={handleCloseAvailabilityModal}
        memberEmail={availabilityModal.memberEmail}
        memberName={availabilityModal.memberName}
        onAvailabilityChecked={handleAvailabilityChecked}
      />
    </>
  );
};

export default AIStageConfiguration;
