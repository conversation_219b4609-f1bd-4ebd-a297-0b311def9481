'use client';

import { Button, ErrorModal } from '@/components/shared';
import { useErrorModalState } from '@/hooks';
import { cn } from '@/utils';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useGetJobDetails } from '../../../api';
import JobCreatorValidationProvider from '../../../utils/JobCreatorValidationContext';
import JobCreatorPreview from './JobCreatorPreview';
import JobCreatorStep1 from './JobCreatorStep1';
import JobCreatorStep2 from './JobCreatorStep2';
import JobCreatorStepFooter from './JobCreatorStepFooter';

export interface customField {
  fieldTitle: string;
  fieldValue: string;
}
export interface customRequirement {
  fieldTitle: string;
  fieldValue?: string;
  fieldType: 'FILE_UPLOAD' | 'TEXT_INPUT';
}

export type TJobCreatorData = {
  accept_personal_details: boolean;
  accept_resume: boolean;
  accept_cover_letter: boolean;
  industry: string | undefined;
  name: string | undefined;
  job_type: string | undefined;
  company_overview: string | undefined;
  location: string | undefined;
  is_multi_location: boolean;
  preferred_locations: string[];
  salary_currency: 'NGN' | 'USD' | 'GBP' | 'EUR' | undefined;
  salary_type: 'FIXED' | 'RANGE' | 'UNDISCLOSED';
  max_salary: number | undefined;
  min_salary: number | undefined;
  fixed_salary: number | undefined;
  salary_negotiable: boolean;
  proficiency_level: string | undefined;
  description: string | undefined;
  responsibilities: string | undefined;
  requirements: string | undefined;
  compulsory_requirements?: string | undefined;
  added_advantage?: string | undefined;
  work_experience: string | undefined;
  working_option: string | undefined;
  application_start_date: Date | undefined;
  application_deadline: Date | undefined;
  pipeline_type: string;
  pipeline?: string | undefined;
  team_member?: string[] | number[] | undefined;
  show_in_career_page: boolean;
  post_as_anonymous: boolean;
  job_status?: string;
  logo?: string | null;
  job_custom_fields: customField[] | undefined;
  viewers_count?: number;
  job_requirement_custom_fields: customRequirement[] | undefined;
  default_pipeline_automation_enabled?: boolean;
  default_pipeline_move_criteria?: number;
};

export const JobFormInitialState: TJobCreatorData = {
  accept_personal_details: true,
  accept_resume: true,
  accept_cover_letter: false,
  industry: undefined,
  name: '',
  job_type: 'FULL_TIME',
  company_overview: '',
  location: undefined,
  is_multi_location: false,
  preferred_locations: [],
  salary_currency: 'NGN',
  salary_type: 'RANGE',
  max_salary: undefined,
  min_salary: undefined,
  fixed_salary: undefined,
  salary_negotiable: false,
  proficiency_level: undefined,
  description: undefined,
  responsibilities: undefined,
  requirements: undefined,
  compulsory_requirements: undefined,
  added_advantage: undefined,
  work_experience: undefined,
  working_option: undefined,
  application_start_date: new Date(),
  application_deadline: new Date(),
  pipeline_type: 'DEFAULT',
  pipeline: undefined,
  team_member: [],
  show_in_career_page: false,
  post_as_anonymous: false,
  job_custom_fields: [],
  job_requirement_custom_fields: [],
  // Default pipeline configuration with sensible defaults
  default_pipeline_automation_enabled: false,
  default_pipeline_move_criteria: 50,
};

const JobCreator = () => {
  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();
  const [jobData, setJobData] = useState(JobFormInitialState);
  const [step, setStep] = useState(1);
  const searchParams = useSearchParams();
  const edit = searchParams.get('edit');
  const { data: jobDetail, isLoading: isFetchingJobData } = useGetJobDetails(
    edit!!
  );
  const [logoFile, setLogoFile] = useState<File | null>(null);

  useEffect(() => {
    if (jobDetail) {
      setJobData({
        ...jobDetail,
        name: jobDetail.job_title || '',
        application_start_date: new Date(jobDetail.application_start_date),
        team_member: jobDetail.team_member.map(
          (member: { id: number }) => member.id
        ),
        application_deadline: jobDetail.application_deadline
          ? new Date(jobDetail.application_deadline)
          : undefined,
        max_salary: jobDetail.max_salary || undefined,
        min_salary: jobDetail.min_salary || undefined,
        fixed_salary: jobDetail.fixed_salary || undefined,
        job_custom_fields: jobDetail.job_custom_fields || [],
        job_requirement_custom_fields:
          jobDetail.job_requirement_custom_fields || [],
        salary_negotiable:
          jobDetail.salary_negotiable !== undefined
            ? jobDetail.salary_negotiable
            : false,
        pipeline: jobDetail.pipeline?.id
          ? String(jobDetail.pipeline.id)
          : undefined,
      });
    }
  }, [jobDetail, isFetchingJobData]);

  return (
    <JobCreatorValidationProvider>
      <div
        className={cn(
          'relative mt-1 max-h-full gap-6 overflow-y-scroll bg-[#F8F9FB] p-4 lg:grid xl:grid-cols-[minmax(0,1fr)_minmax(10rem,0.55fr)] lg:max-xl:grid-cols-[minmax(0,1fr)_minmax(12rem,0.6fr)]'
        )}
      >
        <div className="sticky top-0 overflow-y-scroll rounded-xl bg-white px-3 py-4 md:px-8 ">
          {step == 1 && (
            <JobCreatorStep1 jobData={jobData} setJobData={setJobData} />
          )}
          {step == 2 && (
            <JobCreatorStep2 jobData={jobData} setJobData={setJobData} />
          )}
        </div>
        <JobCreatorPreview
          logoFile={logoFile}
          setLogoFile={setLogoFile}
          jobData={jobData}
        />
      </div>

      <JobCreatorStepFooter
        editData={jobDetail}
        logoFile={logoFile}
        step={step}
        setStep={setStep}
        jobData={jobData}
        setJobData={setJobData}
      />

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
            size="lg"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </JobCreatorValidationProvider>
  );
};

export default JobCreator;
