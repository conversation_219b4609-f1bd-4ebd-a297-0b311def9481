'use client';

import { <PERSON><PERSON>, Checkbox, RadioGroup } from '@/components/shared';
import { Plus } from '@/components/shared/icons';
import { useBooleanStateControl } from '@/hooks';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import {
  useCreateJobConfiguration,
  useGetJobConfiguration,
  useUpdateJobConfiguration,
} from '../../../api';
import useGetJobDetails from '../../../api/getJobDetails';
import { XIcon } from '../../../icons';
import {
  aiJobConfigurationFormSchema,
  defaultAIJobConfiguration,
  transformFromApiResponse,
  transformToApiRequest,
  type AIJobConfiguration,
} from '../../../utils/aiJobConfigurationSchema';
import AIStageConfiguration from './AIStageConfiguration';

const overallConductModeOptions = [
  { name: 'AI Only', value: 'AI_ONLY' },
  { name: 'Human Recruiter', value: 'HUMAN_RECRUITER' },
  { name: 'Hybrid', value: 'HYBRID' },
];

interface AIJobConfigurationProps {
  jobId: string;
  jobUniqueId: string;
  onCancel?: () => void;
}

const AIJobConfiguration: React.FC<AIJobConfigurationProps> = ({
  jobId,
  jobUniqueId,
  onCancel,
}) => {
  const router = useRouter();
  const [expandedStage, setExpandedStage] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    state: isSaving,
    setTrue: startSaving,
    setFalse: stopSaving,
  } = useBooleanStateControl();

  // Fetch job details to get job title and industry for API calls
  const { data: jobDetails } = useGetJobDetails(jobUniqueId);

  // Fetch existing job configuration
  const {
    data: existingConfiguration,
    isLoading: isLoadingConfiguration,
    error: configurationError,
  } = useGetJobConfiguration(jobId);

  // Determine if we have existing configuration (not a 404 error)
  const hasExistingConfiguration = existingConfiguration && !configurationError;

  // Initialize form with default values, existing configuration, or provided initial data
  const defaultValues = hasExistingConfiguration
    ? transformFromApiResponse(existingConfiguration)
    : defaultAIJobConfiguration;

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isDirty },
    reset,
  } = useForm<AIJobConfiguration>({
    resolver: zodResolver(aiJobConfigurationFormSchema),
    defaultValues,
  });

  // Reset form when existing configuration is loaded
  useEffect(() => {
    if (hasExistingConfiguration && existingConfiguration) {
      const formData = transformFromApiResponse(existingConfiguration);
      reset(formData);
    }
  }, [hasExistingConfiguration, existingConfiguration]);

  const createJobConfigMutation = useCreateJobConfiguration();
  const updateJobConfigMutation = useUpdateJobConfiguration();

  const conductMode = watch('conduct_mode');
  const formData = watch();

  // Pre-qualifying questions management
  const {
    fields: preQualifyingQuestionFields,
    append: appendPreQualifyingQuestion,
    remove: removePreQualifyingQuestion,
  } = useFieldArray({
    control: control as any,
    name: 'pre_assessment_questions',
  });
  const [newPreQualifyingQuestion, setNewPreQualifyingQuestion] = useState('');

  const handleStageToggle = (stageKey: string) => {
    setExpandedStage(expandedStage === stageKey ? null : stageKey);
  };

  const handleAddPreQualifyingQuestion = () => {
    if (newPreQualifyingQuestion.trim()) {
      appendPreQualifyingQuestion(newPreQualifyingQuestion.trim());
      setNewPreQualifyingQuestion('');
    }
  };

  const onSubmit = async (data: AIJobConfiguration) => {
    if (isSubmitting) return;

    setIsSubmitting(true);
    startSaving();

    try {
      const apiData = transformToApiRequest(data);

      if (hasExistingConfiguration) {
        // Update existing configuration
        await updateJobConfigMutation.mutateAsync({
          jobId,
          configuration: apiData,
        });
        toast.success('Job configuration updated successfully!');
      } else {
        // Create new configuration
        await createJobConfigMutation.mutateAsync({
          jobId,
          configuration: apiData,
        });
        toast.success('Job configuration created successfully!');
      }

      router.push(`/e/jobs/all`);
    } catch (error) {
      console.error('Error saving job configuration:', error);
      toast.error('Failed to save job configuration. Please try again.');
    } finally {
      setIsSubmitting(false);
      stopSaving();
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      router.back();
    }
  };

  // const handleReset = () => {
  //   reset(defaultAIJobConfiguration);
  //   toast.success('Form reset to default values');
  // };

  // Auto-save draft functionality (optional)
  useEffect(() => {
    if (isDirty && !isSubmitting) {
      const timer = setTimeout(() => {
        // Could implement auto-save to localStorage here
        console.log('Auto-saving draft...');
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [formData, isDirty, isSubmitting]);

  // Show loading state while fetching configuration
  if (isLoadingConfiguration) {
    return (
      <div className="mx-auto max-w-4xl p-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
            <p className="text-body-text/70">Loading configuration...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-4xl p-6">
      <header className="mb-8">
        <h1 className="mb-2 text-2xl font-semibold text-header-text">
          AI Job Configuration
        </h1>
        <p className="max-w-[70ch] text-sm text-body-text/90">
          Configure your AI-powered recruitment process. Set up interview
          stages, team member involvement, and assessment requirements for this
          job posting.
        </p>
      </header>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Basic Requirements Section */}
        <fieldset className="space-y-6">
          <legend>
            <h2 className="mb-2 text-lg font-bold text-header-text">
              Application Requirements
            </h2>
            <p className="max-w-[70ch] text-sm text-body-text/90">
              Select requirements
            </p>
          </legend>

          <div className="mt-2 flex gap-4">
            <Controller
              control={control}
              name="require_personal_details"
              render={({ field }) => (
                <div className="flex items-center justify-between gap-2 rounded-lg bg-[#F5F3FF] p-2">
                  <Checkbox
                    id="require_personal_details"
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled
                  />
                  <label
                    htmlFor="require_personal_details"
                    className="cursor-pointer text-sm font-medium text-gray-700"
                  >
                    Personal Details
                  </label>
                </div>
              )}
            />
            <Controller
              control={control}
              name="require_resume"
              render={({ field }) => (
                <div className="flex items-center justify-between gap-2 rounded-lg bg-[#F5F3FF] p-2">
                  <Checkbox
                    id="require_resume"
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled
                  />
                  <label
                    htmlFor="require_resume"
                    className="cursor-pointer text-sm font-medium text-gray-700"
                  >
                    Attach Resume
                  </label>
                </div>
              )}
            />
            <Controller
              control={control}
              name="require_cover_letter"
              render={({ field }) => (
                <div className="flex items-center justify-between gap-2 rounded-lg bg-[#F5F3FF] p-2">
                  <Checkbox
                    id="require_cover_letter"
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                  <label
                    htmlFor="require_cover_letter"
                    className="cursor-pointer text-sm font-medium text-gray-700"
                  >
                    Attach Cover Letter
                  </label>
                </div>
              )}
            />
          </div>
        </fieldset>

        {/* Pre-Qualifying Questions */}
        <fieldset className="space-y-6">
          <legend>
            <h2 className="mb-2 text-lg font-bold text-header-text">
              Prequalifying Questions
            </h2>
            <p className="max-w-[70ch] text-sm text-body-text/90">
              Define the questions the AI or human recruiter will ask before the interview.
            </p>
          </legend>

          <div className="space-y-3">
            <div className="flex flex-col gap-2">
              <div className="flex gap-2">
                <input
                  type="text"
                  placeholder="Type here"
                  value={newPreQualifyingQuestion}
                  onChange={e => setNewPreQualifyingQuestion(e.target.value)}
                  className="flex-1 rounded-md border border-gray-300 bg-gray-50 p-3 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
                  onKeyDown={e => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddPreQualifyingQuestion();
                    }
                  }}
                />
              </div>
              <Button
                icon={<Plus />}
                variant="light"
                type="button"
                size="tiny"
                onClick={handleAddPreQualifyingQuestion}
              >
                Add Custom Questions
              </Button>
            </div>
          </div>

          <div className="my-6 border-t border-gray-200"></div>

          {/* Questions */}
          <div className="space-y-3">
            <h5 className="text-[0.8125rem] font-medium text-header-text">
              QUESTIONS
            </h5>

            <div className="space-y-3">
              {preQualifyingQuestionFields.map((field, index) => (
                <div key={field.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">
                      Question {index + 1}:
                    </span>
                    <div className="flex items-center space-x-2">
                      <button className="text-gray-400 hover:text-gray-600">
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 16 16"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M2 4H14M2 8H14M2 12H14"
                            stroke="currentColor"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                          />
                        </svg>
                      </button>
                      <XIcon
                        className="h-4 w-4 cursor-pointer text-gray-400 hover:text-red-500"
                        onClick={() => removePreQualifyingQuestion(index)}
                      />
                    </div>
                  </div>
                  <Controller
                    control={control}
                    name={`pre_assessment_questions.${index}` as any}
                    render={({ field }) => (
                      <textarea
                        {...field}
                        placeholder="What motivates you in your daily work?"
                        className="h-20 w-full resize-none rounded-md border border-gray-300 bg-gray-50 p-3 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
                      />
                    )}
                  />
                </div>
              ))}
            </div>
          </div>
        </fieldset>

        {/* Interview Conduct Mode */}
        <fieldset className="space-y-6">
          <legend>
            <h2 className="text-lg font-medium text-header-text">
              Interview Conduct Mode
            </h2>
            <p className="text-sm text-body-text/80">
              Choose who will conduct the interview—AI or a human recruiter.
            </p>
          </legend>

          <Controller
            control={control}
            name="conduct_mode"
            render={({ field }) => (
              <RadioGroup
                options={overallConductModeOptions}
                className="mt-2"
                onChange={field.onChange}
                value={field.value}
                name="conduct_mode"
                variant="light"
                arrangement="row"
              />
            )}
          />
          {errors.conduct_mode && (
            <p className="text-sm text-red-500">
              {errors.conduct_mode.message}
            </p>
          )}
        </fieldset>

        {/* Stage Configurations */}
        <fieldset className="space-y-6">
          <legend>
            <h2 className="mb-2 text-lg font-medium text-header-text">
              Interview Stages Setup
            </h2>
            <p className="mb-4 text-sm text-body-text/80">
              Configure multiple stages for the interview process, indicating
              which ones are AI-led and which are human-led.
            </p>
          </legend>

          <div className="space-y-4">
            {formData.interview_setup_stages?.map((stage, index) => (
              <AIStageConfiguration
                key={index}
                stageName={`${stage.title} Stage`}
                stageKey={`interview_setup_stages.${index}` as any}
                control={control}
                errors={errors}
                setValue={setValue}
                watch={watch}
                stageNumber={index + 1}
                isExpanded={expandedStage === `interview_setup_stages.${index}`}
                onToggle={() =>
                  handleStageToggle(`interview_setup_stages.${index}`)
                }
                jobUniqueId={jobUniqueId}
              />
            ))}
          </div>

          {/* Configuration Summary */}
          <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
            <h3 className="mb-2 text-sm font-medium text-blue-800">
              Configuration Summary
            </h3>
            <div className="space-y-1 text-xs text-blue-700">
              <p>• Overall Mode: {formData.conduct_mode?.replace('_', ' ')}</p>
              {formData.interview_setup_stages?.map((stage, index) => (
                <p key={index}>
                  • {stage.title}: {stage.conduct_mode?.replace('_', ' ')}
                </p>
              ))}
            </div>
          </div>
        </fieldset>

        {/* Form Actions */}
        <div className="flex items-center justify-between border-t border-gray-200 pt-6">
          {/* <div className="flex items-center space-x-3">
            <Button
              type="button"
              variant="light"
              onClick={handleReset}
              disabled={isSubmitting}
            >
              Reset to Defaults
            </Button>
            {isDirty && (
              <span className="text-sm text-amber-600">
                You have unsaved changes
              </span>
            )}
          </div> */}

          <div className="flex items-center space-x-3">
            <Button
              type="button"
              variant="outlined"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              // disabled={isSubmitting || !isDirty}
              disabled={isSubmitting}
              // loading={isSaving}
            >
              {hasExistingConfiguration
                ? 'Update Configuration'
                : 'Save Configuration'}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default AIJobConfiguration;
