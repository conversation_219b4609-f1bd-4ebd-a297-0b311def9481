import { Refresh } from 'iconsax-react';
import { Dispatch, SetStateAction } from 'react';
import { ToolTip } from '@/components/shared';
import { Trash, Upload } from '@/components/shared/icons';
import { useUser } from '@/lib/contexts/UserContext';
import { recruiterInterface } from '@/types/userInfoTypes';
import { cn } from '@/utils';
import { formatCurrency } from '@/utils/numbers';
import {
  convertKebabAndSnakeToTitleCase,
  formatLineBreaks,
} from '@/utils/strings';
import { customField } from '../../../types';

interface PreviewProps {
  info: {
    currentStep?: number;
    show_in_career_page?: boolean;
    name?: string;
    work_experience?: string;
    location?: string;
    job_type?: string;
    salary_type?: string;
    salary_currency?: 'NGN' | 'USD' | 'GBP' | 'EUR';
    fixed_salary?: number;
    min_salary?: number;
    max_salary?: number | string;
    description?: string;
    company_overview?: string;
    responsibilities?: string;
    requirements?: string;
    compulsory_requirements?: string;
    job_custom_fields?: customField[];
  };
  file: File | null;
  setFile: Dispatch<SetStateAction<File | null>>;
}
const Preview = ({ info, file, setFile }: PreviewProps) => {
  const {
    name,
    work_experience,
    location,
    job_type,
    salary_type,
    salary_currency,
    min_salary,
    max_salary,
    fixed_salary,
    description,
    compulsory_requirements,
    responsibilities,
    requirements,
    company_overview,
    job_custom_fields,
  } = info!;

  const { userInfo } = useUser();
  const getCompanyLogo = () => {
    const companyData = userInfo as recruiterInterface;
    return companyData.company?.logo;
  };

  return (
    <div className="w-full max-w-full space-y-3 text-[0.875rem] text-body-text">
      <h2 className="mb-6 text-xl font-medium text-header-text">Job preview</h2>
      <section className="mb-6 flex items-start gap-5 2xl:gap-8">
        <div className="flex flex-col items-center gap-2">
          {getCompanyLogo() && !file ? (
            <img
              src={getCompanyLogo()!}
              alt="company logo"
              className="h-24 w-24 rounded-full object-cover"
            />
          ) : !file ? (
            <div className="h-24 w-24 rounded-full bg-[#F5F7F9]"></div>
          ) : (
            <img
              src={URL.createObjectURL(file!)}
              alt="company logo"
              className="h-24 w-24 rounded-full object-cover"
            />
          )}
          <div
            className={cn(
              'w-full rounded-lg border-[1.75px]',
              file !== null
                ? 'z-40 flex  shrink-0 items-center justify-between'
                : ''
            )}
          >
            <label
              htmlFor={`upload-${name}`}
              className={cn(
                'flex w-full shrink-0 cursor-pointer items-center gap-x-[.5rem] rounded-lg p-1.5',
                file === null ? '' : ''
              )}
            >
              <div className={cn('rounded-full')}>
                <Upload height={20} width={20} fill="#000" />
              </div>

              <div className="text-primary ">
                <input
                  onChange={e => setFile(e.target.files && e.target.files[0])}
                  type="file"
                  id={`upload-${name}`}
                  className="hidden"
                  name={`upload-${name}`}
                  accept={'img*'}
                />
                <p
                  className={cn(
                    '!max-w-[10ch] overflow-hidden text-ellipsis whitespace-nowrap text-xs font-medium text-header-text '
                  )}
                >
                  {file !== null ? file?.name : `Upload logo`}
                </p>
                {file !== null && (
                  <p
                    className={cn(' w-full text-xs opacity-60 max-xs:text-xs')}
                  >
                    change
                  </p>
                )}
              </div>

              <ToolTip
                content="Remove logo"
                contentClass={cn(file == null && 'opacity-0')}
              >
                <div
                  className={cn(
                    'ml-auto h-full',
                    file == null && 'cursor-not-allowed opacity-50'
                  )}
                  onClick={e => {
                    e.preventDefault();
                    setFile(null);
                  }}
                >
                  {file == null ? (
                    <Refresh size={18} className="cursor-pointer" fill="#000" />
                  ) : (
                    <Trash className="cursor-pointer" fill="#000" />
                  )}
                </div>
              </ToolTip>
            </label>
          </div>
        </div>

        <div className="flex flex-col gap-1.5 text-base">
          <h3 className=" font-semibold">{name}</h3>
          <p className="text-sm ">
            {work_experience} {work_experience && 'experience'}
          </p>
          <div className="flex flex-wrap items-center gap-4">
            {job_type && (
              <p className=" max-w-max rounded-[0.5rem]  border-transparent bg-[#F5F3FF] px-4 py-[0.275rem] text-center text-[0.75rem] text-primary hover:border-primary">
                {convertKebabAndSnakeToTitleCase(job_type)}
              </p>
            )}
            <p className="text-sm">{location}</p>
          </div>
          <p className="text-sm">
            {salary_type == 'RANGE' ? (
              <>
                {min_salary &&
                  `${formatCurrency(Number(min_salary), 'NGN')} - `}
                {max_salary && `${formatCurrency(Number(max_salary), 'NGN')}`}
              </>
            ) : salary_type == 'UNDISCLOSED' ? (
              'Undisclosed'
            ) : (
              <>
                {fixed_salary &&
                  `${formatCurrency(
                    Number(fixed_salary),
                    salary_currency || 'NGN'
                  )}`}
              </>
            )}
          </p>
        </div>
      </section>

      {company_overview && (
        <div className="mb-4 mt-10">
          <h3 className="font-medium text-header-text">Company Overview</h3>
          <div
            className="text-[#4A4A68]"
            dangerouslySetInnerHTML={{ __html: company_overview }}
          ></div>
        </div>
      )}
      {description && (
        <div className="mb-4 mt-10">
          <h3 className="font-medium text-header-text">Job Description</h3>
          <p
            className="text-[#4A4A68]"
            dangerouslySetInnerHTML={{ __html: formatLineBreaks(description) }}
          ></p>
        </div>
      )}
      {requirements && (
        <div className="mb-4 mt-10">
          <h3 className="font-medium text-header-text">Basic Requirements</h3>
          <p
            className="text-[#4A4A68]"
            dangerouslySetInnerHTML={{ __html: formatLineBreaks(requirements) }}
          ></p>
        </div>
      )}
      {compulsory_requirements &&
        compulsory_requirements.replace(/<[^>]*>/g, '').trim() && (
          <div className="mb-4 mt-10">
            <h3 className="font-medium text-header-text">
              Compulsory Requirements
            </h3>
            <p
              className="text-[#4A4A68]"
              dangerouslySetInnerHTML={{
                __html: formatLineBreaks(compulsory_requirements),
              }}
            ></p>
          </div>
        )}
      {responsibilities && (
        <div className="mb-4 mt-10">
          <h3 className="font-medium text-header-text">
            Role Responsibilities
          </h3>
          <p
            className="text-[#4A4A68]"
            dangerouslySetInnerHTML={{
              __html: formatLineBreaks(responsibilities),
            }}
          ></p>
        </div>
      )}
      {job_custom_fields &&
        job_custom_fields.length > 0 &&
        job_custom_fields.map((field, index) => {
          const { fieldTitle, fieldValue } = field;
          return (
            <div className="mb-4 mt-10">
              <h3 className="font-medium text-header-text">{fieldTitle}</h3>
              {fieldValue && (
                <p
                  className="text-[#4A4A68]"
                  dangerouslySetInnerHTML={{
                    __html: formatLineBreaks(fieldValue!),
                  }}
                ></p>
              )}
            </div>
          );
        })}
    </div>
  );
};

export default Preview;
