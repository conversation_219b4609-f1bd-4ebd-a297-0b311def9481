'use client';

import * as React from 'react';
import { UseFormSetValue, UseFormWatch } from 'react-hook-form';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandList,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/shared';
import { CaretDown, Plus } from '@/components/shared/icons';
import { cn } from '@/utils';
import { getInitials } from '@/utils/strings';
import { useGetTeamMembers } from '../../../api';
import { TCreateFormStep2FormData } from './JobCreatorStep2';

interface TeamMemberComboboxProps {
  id?: string;
  onChange?: (value: string) => void;
  setValue: UseFormSetValue<TCreateFormStep2FormData>;
  watch?: UseFormWatch<TCreateFormStep2FormData>;
  teamMemberKey?: string;
}

export function TeamMemberCombobox({
  id,
  onChange,
  setValue,
  watch,
  teamMemberKey = 'team_member',
}: TeamMemberComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [search, setSearch] = React.useState('');
  const { data: teamMembers, isLoading: isFetchingTeamMembers } =
    useGetTeamMembers();
  console.log('teamMembers select', teamMembers);

  // Get the current team_member values from the form
  const currentTeamMembers = watch ? watch(teamMemberKey as any) || [] : [];
  const [selectedValues, setSelectedValues] =
    React.useState<string[]>(currentTeamMembers);

  // Only update selectedValues if it actually differs from current state
  React.useEffect(() => {
    // Simple shallow comparison assuming arrays of strings
    const areArraysEqual = (a: string[], b: string[]) =>
      a.length === b.length && a.every((v, i) => v === b[i]);

    if (!areArraysEqual(currentTeamMembers, selectedValues)) {
      setSelectedValues(currentTeamMembers);
    }
  }, [currentTeamMembers, selectedValues]);

  const options = teamMembers?.map(teamMember => {
    const { user, id } = teamMember;
    // const { user } = member;
    const { email, first_name, last_name } = user;
    return {
      value: id,
      label: `${first_name || 'Name unavailable'} ${last_name || ''}`,
      email: email,
      first_name,
      last_name,
    };
  });

  const filteredOptions = options?.filter(option => {
    const searchString = search.toLowerCase();
    const label = option.label.toLowerCase();
    const email = option.email.toLowerCase();
    const first_name = option.first_name.toLowerCase();
    const last_name = option.last_name.toLowerCase();
    const id = String(option.value).toLowerCase();

    return (
      label.includes(searchString) ||
      email.includes(searchString) ||
      first_name.includes(searchString) ||
      last_name.includes(searchString) ||
      id.includes(searchString)
    );
  });

  const handleSelect = (currentValue: string) => {
    const updatedValues = [...selectedValues];

    if (selectedValues.includes(currentValue)) {
      const index = updatedValues.indexOf(currentValue);
      updatedValues.splice(index, 1);
    } else {
      updatedValues.push(currentValue);
    }

    setSelectedValues(updatedValues);
    // @ts-ignore
    setValue(teamMemberKey, updatedValues);
    setOpen(false);
  };

  return (
    <>
      <div className="my-2 flex flex-wrap items-center gap-2.5 gap-y-1.5">
        {selectedValues.map((selectedMember, index) => {
          // const memberObject = teamMembers?.filter(teamMember => teamMember.member.id == Number(selectedMember))[0]
          const memberObject = teamMembers?.find(
            teamMember => teamMember.id === Number(selectedMember)
          );

          return (
            <article
              key={index}
              className="flex cursor-pointer items-center gap-1 rounded-full bg-primary-light px-3 py-1 text-sm text-body-text"
            >
              {memberObject?.user_email}
              <span
                className="ml-2"
                onClick={() => handleSelect(selectedMember)}
              >
                <Plus className="rotate-45" />
              </span>
            </article>
          );
        })}
      </div>

      <Popover open={open} onOpenChange={setOpen}>
        <Command>
          <PopoverTrigger>
            <div className="flex max-w-[550px] cursor-text items-center rounded-md bg-[#F5F7F9] px-4 py-[0.875rem] text-left text-[0.85rem]">
              <span className="text-sm">Search member</span>
              <CaretDown fill="#755AE2" className="ml-auto opacity-90" />
            </div>
          </PopoverTrigger>

          <PopoverContent
            align="start"
            className="w-[85vw] !pt-1 lg:max-w-[550px]"
          >
            <CommandInput
              isLoading={isFetchingTeamMembers}
              placeholder={'Search or enter a new member'}
              value={search}
              onValueChange={text => setSearch(text)}
            />
            <CommandEmpty className="p-1">
              {filteredOptions && filteredOptions?.length < 1 && (
                <p className="px-2 py-3 pl-6 pt-1 text-xs">No matches found.</p>
              )}
            </CommandEmpty>

            <CommandList className="max-h-60 overflow-y-auto">
              {filteredOptions?.map(option => {
                const initials = getInitials(option.label);

                return (
                  <div
                    className="relative flex cursor-pointer select-none items-center rounded-md p-1.5 text-sm outline-none hover:bg-primary-light aria-selected:bg-blue-100/70 aria-selected:text-primary"
                    onClick={() => handleSelect(String(option.value))}
                    key={option.value}
                  >
                    <svg
                      className={cn(
                        'mr-2 h-4 w-4',
                        selectedValues.includes(String(option.value))
                          ? 'opacity-100'
                          : 'opacity-0'
                      )}
                      fill="none"
                      height={16}
                      viewBox="0 0 16 16"
                      width={16}
                      xmlns="http://www.w3.org/2000/svg"
                      aria-hidden
                    >
                      <path
                        d="m14.53 5.03-8 8a.751.751 0 0 1-1.062 0l-3.5-3.5a.751.751 0 1 1 1.063-1.062L6 11.438l7.47-7.469a.751.751 0 0 1 1.062 1.063l-.001-.002Z"
                        fill="#755AE2"
                      />
                    </svg>

                    <span className="flex items-center gap-3.5">
                      <span className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-xs text-white">
                        {initials}
                      </span>

                      <span className="flex flex-col">
                        <span>{option.label}</span>
                        <span
                          className={cn(
                            'text-xxs text-[#646464] text-opacity-80'
                          )}
                        >
                          {option.email}
                        </span>
                      </span>
                    </span>
                  </div>
                );
              })}
            </CommandList>
          </PopoverContent>
        </Command>
      </Popover>
    </>
  );
}
