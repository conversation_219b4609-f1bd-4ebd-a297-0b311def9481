import { AxiosError } from 'axios';
import { useR<PERSON>er, useSearchParams } from 'next/navigation';
import React, { useState } from 'react';
import toast from 'react-hot-toast';
import {
  Button,
  ErrorModal,
  LinkButton,
  LoadingOverlay,
  Modal,
} from '@/components/shared';
import {
  useBooleanStateControl,
  useCloudinary,
  useErrorModalState,
} from '@/hooks';
import { useLoading } from '@/lib/contexts/LoadingContext';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { useCreateJob, useEditDraftJob, usesaveDraft } from '../../../api';
import { SuitcaseWhite } from '../../../icons';
import { ActiveJob } from '../../../types';
import { useJobCreatorValidation } from '../../../utils/JobCreatorValidationContext';
import { TJobCreatorData } from './JobCreator';

// Helper function to format validation errors into user-friendly messages
const formatValidationErrors = (errors: any): string => {
  const errorMessages: string[] = [];

  const processErrors = (obj: any, prefix = '') => {
    if (!obj) return;

    Object.keys(obj).forEach(key => {
      const error = obj[key];
      if (error?.message) {
        // The error message from Zod already contains the field context
        errorMessages.push(error.message);
      } else if (Array.isArray(error)) {
        // Handle array errors (like custom fields)
        error.forEach((item, index) => {
          if (typeof item === 'object' && item !== null) {
            processErrors(
              item,
              `${prefix}${key.replace(/_/g, ' ')} ${index + 1} - `
            );
          }
        });
      } else if (typeof error === 'object' && error !== null) {
        processErrors(error, prefix);
      }
    });
  };

  processErrors(errors);

  if (errorMessages.length === 0) {
    return 'Please fill all required fields';
  }

  if (errorMessages.length === 1) {
    return errorMessages[0];
  }

  return `Please fix the following issues:\n• ${errorMessages.join('\n• ')}`;
};

interface JobCreatorStepFooterProps {
  editData?: ActiveJob;
  logoFile: File | null;
  step: number;
  setStep: React.Dispatch<React.SetStateAction<number>>;
  jobData: TJobCreatorData;
  setJobData: React.Dispatch<React.SetStateAction<TJobCreatorData>>;
}

const JobCreatorStepFooter: React.FC<JobCreatorStepFooterProps> = ({
  editData,
  logoFile,
  step,
  setStep,
  jobData,
  setJobData,
}) => {
  const [jobId, setJobId] = useState('');
  const [jobLink, setJobLink] = useState('');

  const searchParams = useSearchParams();
  const clientId = searchParams.get('clientId') || '';

  const router = useRouter();
  const { validateStep } = useJobCreatorValidation();

  const handleNext = async () => {
    const isValid = await validateStep(step);
    if (isValid) {
      setStep(step + 1);
    } else {
      toast.error('Please fill all required fields');
    }
  };

  const handleBack = () => {
    setStep(step - 1);
  };

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();
  const { state: isSuccessModalOpen, setTrue: openSuccessModal } =
    useBooleanStateControl();

  const { uploadToServer, deleteFromCloudinary } = useCloudinary();
  const { isUploading, isDeleting } = useLoading();
  const { mutate: createJob, isLoading: isJobCreating } = useCreateJob();
  const { mutate: createJobFromDraft, isLoading: isJobCreatingFromDraft } =
    useEditDraftJob();

  const onSubmit = async () => {
    const isValid = await validateStep(2);
    if (!isValid) {
      toast.error('Please fill all required fields');
      return;
    }
    const today = new Date().setHours(0, 0, 0, 0);
    const start = new Date(jobData.application_start_date || 0).setHours(
      0,
      0,
      0,
      0
    );

    let logoUrl;
    let PublicId: string | undefined;
    if (logoFile) {
      const data = await uploadToServer(logoFile);
      logoUrl = data.secure_url;
      PublicId = data.id;
    }
    const dataToSubmit = {
      ...jobData,
      logo: logoFile ? logoUrl : jobData.logo ? jobData.logo : undefined,
      job_status: start > today ? 'QUEUED' : 'OPEN',
      viewers_count: 0,
      client: clientId,
    };

    if (editData) {
      createJobFromDraft([String(editData?.id)!!, dataToSubmit], {
        onSuccess: data => {
          setJobId(data?.unique_id);
          setJobLink(`${window?.location?.origin}/jobs/${data.unique_id}`);
          openSuccessModal();
        },
        onError(error: any) {
          const errorMessage =
            (error?.response?.data.detail as string) ||
            formatAxiosErrorMessage(error as AxiosError);
          if (logoFile && PublicId) {
            deleteFromCloudinary(PublicId);
          }
          openErrorModalWithMessage(errorMessage);
        },
      });
    } else {
      createJob(dataToSubmit, {
        onSuccess: data => {
          setJobId(data?.unique_id);
          setJobLink(`${window?.location?.origin}/jobs/${data.unique_id}`);
          openSuccessModal();
        },
        onError(error: any) {
          const errorMessage =
            (error?.response?.data.detail as string) ||
            formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage);
        },
      });
    }
  };

  const { mutate: saveDraftJob, isLoading: isDraftSaving } = usesaveDraft();
  const saveDraft = async () => {
    function removestep(obj: any) {
      const newObj = { ...obj };
      delete newObj.step;
      return newObj;
    }

    function removeEmptyFields(obj: any): any {
      if (typeof obj === 'object' && obj !== null && !Array.isArray(obj)) {
        const newObj: any = {};

        Object?.keys(obj).forEach(key => {
          const processedValue = removeEmptyFields(obj[key]);
          if (
            typeof processedValue === 'object' &&
            processedValue !== null &&
            Object.keys(processedValue).length > 0
          ) {
            newObj[key] = processedValue;
          } else if (obj[key] !== '') {
            newObj[key] = obj[key];
          }
        });
        return newObj;
      }
      return obj;
    }

    let logoUrl;
    if (logoFile) {
      const data = await uploadToServer(logoFile);
      logoUrl = data.secure_url;
    }

    const newData = removestep(jobData);
    const cleanedData = removeEmptyFields(newData);
    const dataToSubmit = {
      ...cleanedData,
      logo: logoFile ? logoUrl : jobData.logo ? jobData.logo : undefined,
      job_status: 'DRAFT',
    };
    if (editData) {
      createJobFromDraft([String(editData?.id)!!, dataToSubmit], {
        onSuccess: data => {
          toast.success('Job saved as draft');
          router.push('./drafts');
        },
        onError(error: any) {
          const errorMessage =
            (error?.response?.data.detail as string) ||
            formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage);
        },
      });
    } else {
      saveDraftJob(dataToSubmit, {
        onSuccess() {
          toast.success('Job saved as draft');
          router.push('./drafts');
        },
        onError(error: any) {
          const errorMessage =
            (error?.response?.data.detail as string) ||
            formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage);
        },
      });
    }
  };

  const copyJobLink = (link: string) => {
    if (typeof navigator !== undefined) {
      navigator.clipboard.writeText(link);
      toast.success('Job link copied successfully');
    }
  };

  return (
    <div className="sticky bottom-0 flex items-center justify-end gap-[0.94rem] border-t pt-4 sm:px-0 sm:pb-0">
      {step > 1 && (
        <Button type="button" variant="light" onClick={handleBack}>
          Previous
        </Button>
      )}
      <div className=" ml-auto flex items-center gap-4">
        <div className=" ml-auto">
          <Button
            type="button"
            variant="unstyled"
            onClick={saveDraft}
            disabled={isJobCreating || isJobCreatingFromDraft || isUploading}
          >
            Save as Draft
          </Button>

          {step == 1 && (
            <Button type="button" onClick={handleNext} className="!py-[0.7rem]">
              Proceed
            </Button>
          )}
          {step === 2 && (
            <Button
              type="submit"
              form="create-job-form"
              disabled={isJobCreating || isJobCreatingFromDraft || isUploading}
              onClick={onSubmit}
            >
              Post Job
            </Button>
          )}
        </div>
      </div>

      <LoadingOverlay
        isOpen={
          isJobCreating ||
          isDraftSaving ||
          isJobCreatingFromDraft ||
          isUploading
        }
      />

      <Modal
        isModalOpen={isSuccessModalOpen}
        heading="Success"
        closeModal={() => router.push('./all')}
        color="purple"
        body={
          <div className="flex flex-col items-center justify-center gap-4 ">
            <div className="self-start  rounded-full bg-primary p-4">
              <SuitcaseWhite />
            </div>
            <div>
              <h3 className="text-xl font-medium text-primary">
                Job created successfully
              </h3>
              <p className="text-[0.875rem] text-[#8C8CA1]">
                Your job posting for the{' '}
                <span className="font-bold text-[#0E0E2C]">{jobData.name}</span>{' '}
                position is now live on GetLinked, providing an opportunity for
                candidates within the platform. To expand the reach and invite
                candidates from other sources, you can easily share the link
                provided below on various job boards. Copy and distribute the
                link to ensure a diverse pool of applicants for this exciting
                opportunity
              </p>
            </div>

            <div className="flex w-full items-center justify-between rounded-lg bg-white px-4 py-1.5">
              <p className="text-[0.75rem] text-primary">{jobLink}</p>
              <Button
                variant="light"
                size="thin"
                onClick={() => copyJobLink(jobLink)}
                className="rounded-[0.6rem] !py-[0.325rem] outline-none focus:outline-none"
              >
                copy
              </Button>
            </div>
          </div>
        }
        footer={
          <div className="sticky bottom-0 ml-auto flex w-full items-center justify-end gap-4 rounded-[1rem] bg-white p-5">
            <LinkButton
              href={`./job/${jobId}/pipeline`}
              variant="light"
              size="thin"
            >
              View pipeline
            </LinkButton>
            <LinkButton
              href={`./all`}
              className="!px-6"
              size="thin"
              variant="light"
            >
              Configure later
            </LinkButton>
            <LinkButton href={`./job/${jobId}/configure`} size="thin">
              Configure now
            </LinkButton>
          </div>
        }
      />

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 hover:border-red-950 hover:text-red-950 sm:text-sm md:px-6"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </div>
  );
};

export default JobCreatorStepFooter;
