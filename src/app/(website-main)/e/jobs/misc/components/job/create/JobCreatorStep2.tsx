'use client';

import { <PERSON><PERSON>, Checkbox, RadioGroup, Select } from '@/components/shared';
import { Plus } from '@/components/shared/icons';
import { useBooleanStateControl } from '@/hooks';
import { cn } from '@/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { debounce } from 'lodash';
import React, { useEffect } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import type { z } from 'zod';
import { useGetPipelinesWithoutPagination } from '../../../api';
import { XIcon } from '../../../icons';
import { useJobCreatorValidation } from '../../../utils/JobCreatorValidationContext';
import { CreateJobFormSchema2 } from '../../../utils/jobSchemas';
import CreateNewPipeline from '../../modals/CreateNewPipelineStep1';
import type { TJobCreatorData } from './JobCreator';
import { TeamMemberCombobox } from './TeamMemberDropDown';

const pipeline_type = [
  { name: 'Getlinked default pipeline', value: 'DEFAULT' },
  { name: 'Select from created pipelines', value: 'CUSTOM' },
];
const file_types = [
  { name: 'Text Input', value: 'TEXT_INPUT' },
  { name: 'File Upload', value: 'FILE_UPLOAD' },
];

const yes_or_no = [
  { name: 'Yes', value: 'true' },
  { name: 'No', value: 'false' },
];

interface CustomFormErrors {
  job_requirement_custom_fields?: Array<{
    fieldTitle?: { message?: string };
    fieldType?: { message: string };
  }>;
}

export type TCreateFormStep2FormData = z.infer<typeof CreateJobFormSchema2>;

interface Step2Props {
  jobData: TJobCreatorData;
  setJobData: React.Dispatch<React.SetStateAction<TJobCreatorData>>;
}

const Step2 = ({ jobData, setJobData }: Step2Props) => {
  const {
    register,
    formState: { errors },
    trigger,
    setValue,
    watch,
    control,
  } = useForm<TCreateFormStep2FormData>({
    resolver: zodResolver(CreateJobFormSchema2),
    defaultValues: {
      accept_personal_details: true,
      accept_resume: true,
      accept_cover_letter: jobData.accept_cover_letter || false,
      job_requirement_custom_fields: jobData.job_requirement_custom_fields,
      pipeline_type: jobData.pipeline_type || 'DEFAULT',
      pipeline: jobData.pipeline?.toString(),
      default_pipeline_automation_enabled:
        jobData.default_pipeline_automation_enabled ?? false,
      default_pipeline_move_criteria:
        jobData.default_pipeline_move_criteria ?? 50,
    },
  });
  const { fields, append, remove } = useFieldArray({
    name: 'job_requirement_custom_fields',
    control,
  });
  const selectedPipelineType = watch('pipeline_type');
  const customErrors = useForm().formState.errors as CustomFormErrors;

  const { data: pipelines, isLoading: isLoadingPipelines } =
    useGetPipelinesWithoutPagination();
  const custom_pipelines = pipelines?.map(
    (pipeline: { id: number; name: string }) => ({
      name: pipeline.name,
      value: pipeline.id.toString(),
    }),
  ) as { name: string; value: string }[];

  const {
    state: isNewPipelineStep1ModalOpen,
    setTrue: openNewPipelineStep1Modal,
    setFalse: closeNewPipelineStep1Modal,
  } = useBooleanStateControl();


  const handleCloseNewPipelineStep1Modal = (pipelineId?: string) => {
     if (pipelineId) {
      setValue('pipeline', pipelineId.toString());
    }
    closeNewPipelineStep1Modal();
  };

  const { registerValidation, registerValidationWithErrors } =
    useJobCreatorValidation();

  useEffect(() => {
    registerValidation(2, async () => {
      const result = await trigger();
      return result;
    });

    registerValidationWithErrors(2, async () => {
      const result = await trigger();
      return {
        isValid: result,
        errors: result ? undefined : errors,
      };
    });
  }, [registerValidation, registerValidationWithErrors, trigger, errors]);

  useEffect(() => {
    const debouncedFn = debounce(value => {
      setJobData(prev => {
        const updatedData = { ...prev, ...value };
        // Ensure job_requirement_custom_fields is not overwritten unless it's explicitly changed
        if (value.job_requirement_custom_fields) {
          updatedData.job_requirement_custom_fields =
            value.job_requirement_custom_fields;
        }
        return updatedData;
      });
      Object.entries(value).forEach(([key, val]) => {
        if (
          JSON.stringify(jobData[key as keyof typeof jobData]) !==
          JSON.stringify(val)
        ) {
          setValue(key as keyof typeof jobData as any, val as any);
        }
      });
    }, 100);
    const subscription = watch(debouncedFn);
    return () => subscription.unsubscribe();
  }, [watch, jobData, setValue, setJobData]);
  const default_pipeline_automation_enabled = watch('default_pipeline_automation_enabled');
  return (
    <div>
      <header className="mb-8">
        <h4 className="text-[1.05rem] font-medium text-header-text">
          Configure your posting
        </h4>
        <p className="max-w-[60ch]  text-[0.8125rem] text-body-text/90">
          In this section, you have the flexibility to define application
          requirements, include{' '}
          <span className="text-header-text">team members</span> in the hiring
          process, add <span className="text-header-text">pipelines</span>, and
          seamlessly share the job posting on our career page and various job
          boards.
        </p>
      </header>

      <fieldset className="my-8">
        <legend>
          <h3 className="text-[0.90625rem] font-medium text-header-text">
            Application requirements
          </h3>
          <p className="mb-2.5 text-[0.8125rem] text-body-text">
            Select requirements
          </p>
        </legend>

        <div className="item-center flex flex-wrap gap-4 ">
          <Controller
            control={control}
            name="accept_personal_details"
            render={({ field }) => (
              <div
                className="flex max-w-max flex-row items-center space-x-2 space-y-0 rounded-[0.45rem] bg-[#F5F3FF] px-4 py-2">
                <Checkbox
                  checked={true}
                  onCheckedChange={field.onChange}
                  label="Personal details"
                  disabled
                />
              </div>
            )}
          />
          <Controller
            control={control}
            name="accept_resume"
            render={({ field }) => (
              <div
                className="flex max-w-max flex-row items-center space-x-2 space-y-0 rounded-[0.45rem] bg-[#F5F3FF] px-4 py-2">
                <Checkbox
                  checked={true}
                  onCheckedChange={field.onChange}
                  label="Attach resume"
                  disabled
                />
              </div>
            )}
          />
          <Controller
            control={control}
            name="accept_cover_letter"
            render={({ field }) => (
              <div
                className="flex max-w-max flex-row items-center space-x-2 space-y-0 rounded-[0.45rem] bg-[#F5F3FF] px-4 py-2">
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                  label="Attach cover letter"
                />
              </div>
            )}
          />
        </div>

        <div className="my-2 flex flex-col gap-y-2">
          {fields.map((field, index) => {
            return (
              <>
                <fieldset
                  key={field.id}
                  className="flex items-center gap-2.5 !space-y-1"
                >
                  <div className="inputdiv !m-0 !mt-1 ">
                    <input
                      placeholder="Custom requirement"
                      type="text"
                      className={cn(
                        '!bg-[#F5F7F9] !py-1.5 text-sm font-[500] text-secondary-text',
                        customErrors.job_requirement_custom_fields &&
                        customErrors.job_requirement_custom_fields[index]
                          ?.fieldTitle &&
                        'error',
                      )}
                      {...register(
                        `job_requirement_custom_fields.${index}.fieldTitle`,
                        { required: true },
                      )}
                    />
                  </div>

                  <Select
                    className="lg:vmin-w-[200px] !py-1.5"
                    itemClass="!text-xs"
                    {...register(
                      `job_requirement_custom_fields.${index}.fieldType`,
                      { required: true },
                    )}
                    onChange={value => {
                      setValue(
                        `job_requirement_custom_fields.${index}.fieldType`,
                        value,
                      );
                      setJobData(prevJobData => {
                        const updatedFields = [
                          ...(prevJobData.job_requirement_custom_fields || []),
                        ];
                        updatedFields[index] = {
                          ...updatedFields[index],
                          fieldType: value as 'TEXT_INPUT' | 'FILE_UPLOAD',
                        };
                        return {
                          ...prevJobData,
                          job_requirement_custom_fields: updatedFields,
                        };
                      });
                    }}
                    options={file_types}
                    labelKey="name"
                    valueKey="value"
                    placeholder="Select requirement type"
                    variant="dark_showcase"
                    hasError={
                      customErrors.job_requirement_custom_fields &&
                      !!customErrors.job_requirement_custom_fields[index]
                        ?.fieldType
                    }
                    errorMessage={
                      customErrors.job_requirement_custom_fields &&
                      customErrors.job_requirement_custom_fields[index]
                        ?.fieldType?.message
                    }
                  />

                  <div title="Delete Field">
                    <XIcon
                      className="cursor-pointer rounded-full bg-white p-1 hover:bg-red-300"
                      onClick={() => {
                        remove(index);
                        setJobData(prevJobData => {
                          const updatedFields = [
                            ...(prevJobData.job_requirement_custom_fields ||
                              []),
                          ];
                          updatedFields.splice(index, 1);
                          return {
                            ...prevJobData,
                            job_requirement_custom_fields: updatedFields,
                          };
                        });
                      }}
                    />
                  </div>
                </fieldset>
                {customErrors.job_requirement_custom_fields &&
                  customErrors.job_requirement_custom_fields[index]
                    ?.fieldTitle && (
                    <p className="text-[0.8125rem] text-red-500">
                      {
                        customErrors?.job_requirement_custom_fields[index]
                          ?.fieldTitle?.message
                      }
                    </p>
                  )}
                {customErrors.job_requirement_custom_fields &&
                  customErrors.job_requirement_custom_fields[index]
                    ?.fieldType && (
                    <p className="text-[0.8125rem] text-red-500">
                      {
                        customErrors?.job_requirement_custom_fields[index]
                          ?.fieldType?.message
                      }
                    </p>
                  )}
              </>
            );
          })}
        </div>
        <Button
          icon={<Plus />}
          variant="light"
          type="button"
          size="tiny"
          className="mb-5 w-max self-start"
          onClick={e => {
            e.preventDefault();
            const newField = {
              fieldTitle: '',
              fieldType: 'TEXT_INPUT' as 'TEXT_INPUT' | 'FILE_UPLOAD',
            };
            append(newField);
            setJobData(prevJobData => ({
              ...prevJobData,
              job_requirement_custom_fields: [
                ...(prevJobData.job_requirement_custom_fields || []),
                newField,
              ],
            }));
          }}
        >
          Add Custom Requirement
        </Button>
      </fieldset>

      <>
        <fieldset className="my-8">
          <legend>
            <h3 className="text-[0.90625rem] font-medium text-header-text">
              Job Pipeline
            </h3>
            <p className="text-[0.8125rem] text-body-text">
              Add pipeline to guide your hiring process
            </p>
          </legend>

          <Select
            className="mt-4"
            label="Pipeline type"
            {...register('pipeline_type', {
              required: 'Please select a pipeline type',
            })}
            onChange={value => setValue('pipeline_type', value)}
            value={watch('pipeline_type') || 'DEFAULT'}
            options={pipeline_type}
            labelKey="name"
            valueKey="value"
            placeholder="Select pipeline type"
            variant="dark_showcase"
            hasError={!!errors.pipeline_type}
            errorMessage={errors.pipeline_type?.message as string}
          />
          {selectedPipelineType === 'CUSTOM' && (
            <>


              <Controller
                control={control}
                name="pipeline"
                rules={{ required: 'Please select a pipeline' }}
                render={({ field }) => (
                  <Select
                    {...field}
                    onChange={(value) => {
                      field.onChange(value);
                    }}
                    value={field.value}
                    className="my-3"
                    options={custom_pipelines}
                    labelKey="name"
                    valueKey="value"
                    variant="dark_showcase"
                    label="Select pipeline"
                    placeholder="Select a custom created pipeline"
                    isLoadingOptions={isLoadingPipelines}
                    hasError={!!errors.pipeline}
                    errorMessage={errors.pipeline?.message as string}
                  />
                )}
              />
              <article
                className="flex w-full max-w-[550px] flex-col items-center justify-center gap-5 rounded-lg bg-primary-light p-4">
                <p className="max-w-sm text-center text-[0.8125rem] text-body-text">
                  {custom_pipelines.length > 0
                    ? 'Click the button below to create a new custom pipeline if you do not want to use any of the existing ones.'
                    : 'You have not created any job pipeline yet. Click the button below to start creating one to use for this job'}
                </p>

                <Button
                  size="tiny"
                  onClick={e => {
                    e.preventDefault();
                    openNewPipelineStep1Modal();
                  }}
                >
                  Create Pipeline
                </Button>
              </article>
            </>
          )}

          {selectedPipelineType === 'DEFAULT' && (
            <div className="mt-6 rounded-lg border border-gray-200 p-4">
              <h4 className="mb-4 text-[0.90625rem] font-medium text-header-text">
                Default Pipeline Configuration
              </h4>
              <p className="mb-4 text-[0.8125rem] text-body-text">
                Configure automation settings for the default pipeline stages.
              </p>

              <div className="mb-5 gap-2">
                <div className="sm:flex-column my-3 flex flex-wrap items-start gap-3 md:flex-row md:items-center">
                  <p className="text-[14px] text-[#4A4A68]">
                    Enable staging automation
                  </p>
                  <Controller
                    control={control}
                    name="default_pipeline_automation_enabled"
                    render={({ field }) => (
                      <RadioGroup
                        name="default_pipeline_automation_enabled"
                        options={yes_or_no}
                        onChange={(value: string) => {
                          field.onChange(value === 'true');
                          setValue(
                            'default_pipeline_automation_enabled',
                            value === 'true',
                          );
                        }}
                        value={field.value ? 'true' : 'false'}
                        containerClass="flex-row items-start md:items-center my-0"
                        baseClass="!w-max"
                        className="!py-0"
                        variant="unstyled"
                        arrangement="row"
                      />
                    )}
                  />
                </div>

                {default_pipeline_automation_enabled && <>
                  <div className="sm:flex-column my-3 flex flex-wrap items-start gap-3 md:flex-row md:items-center">
                    <p className="text-[14px] text-[#4A4A68]">
                      Move candidates with at least
                    </p>


                    <Controller
                      control={control}
                      name="default_pipeline_move_criteria"
                      render={({ field }) => (
                        <input
                          type="number"
                          min="20"
                          max="100"
                          value={field.value ?? ''} // allow empty string
                          onChange={(e) => {
                            const val = e.target.value;
                            if (val === '') {
                              field.onChange(''); // keep empty
                            } else {
                              field.onChange(val); // keep as string until blur
                            }
                          }}
                          onBlur={(e) => {
                            const val = e.target.value;
                            if (val !== '') {
                              const num = Number(val);
                              if (!isNaN(num)) {
                                field.onChange(num); // convert to number on blur
                              }
                            }
                          }}
                          className={cn(
                            'w-16 rounded-md border p-1 text-center text-sm',
                            errors.default_pipeline_move_criteria
                              ? 'border-red-500'
                              : 'border-gray-300',
                          )}
                        />
                      )}
                    />
                    <p className="text-[14px] text-[#4A4A68]">
                      % match to the next stage
                    </p>
                  </div>
                  {errors.default_pipeline_move_criteria && (
                    <div className="mt-1 text-xs text-red-500">
                      {errors.default_pipeline_move_criteria.message}
                    </div>
                  )}
                </>
                }
              </div>
            </div>
          )}
        </fieldset>

        <fieldset className="my-8">
          <legend>
            <h3 className="text-[0.90625rem] font-medium text-header-text">
              Team
            </h3>
            <p className="text-[0.8125rem] text-body-text">
              Search and add team members you&apos;d like to be part of this
              recruitment process.
            </p>
          </legend>

          <TeamMemberCombobox setValue={setValue} watch={watch} />
        </fieldset>
      </>

      <CreateNewPipeline
        isNewPipelineStep1ModalOpen={isNewPipelineStep1ModalOpen}
        closeNewPipelineStep1Modal={(pipelineId?: string) => handleCloseNewPipelineStep1Modal(pipelineId)}
      />
    </div>
  );
};

export default Step2;
