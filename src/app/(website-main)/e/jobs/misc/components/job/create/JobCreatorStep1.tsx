import { zodResolver } from '@hookform/resolvers/zod';
import { Country, ICountry, IState, State } from 'country-state-city';
import { debounce } from 'lodash';
import { Send } from 'lucide-react';
import { MouseEvent, useCallback, useEffect, useMemo, useState } from 'react';
import { Controller, useFieldArray, useForm, useWatch } from 'react-hook-form';
import TextareaAutosize from 'react-textarea-autosize';
import {
  AmountInput,
  Button,
  Checkbox3,
  Input,
  LoaderBtn,
  RadioGroup,
  RichTextEditor,
  Select,
  SelectSingleCombo,
  SingleDatePicker,
  Switch,
  ToolTip,
} from '@/components/shared';
import FormError from '@/components/shared/form-error';
import { Info, Plus } from '@/components/shared/icons';
import { useUser } from '@/lib/contexts/UserContext';
import { recruiterInterface } from '@/types/userInfoTypes';
import { cn } from '@/utils';
import { useGenerateResponsibilities } from '../../../api';
import useGetIndustries from '../../../api/getIndustries';
import {
  currencies,
  job_type,
  proficiency_levels,
  salary_types,
  work_experiences,
  working_options,
} from '../../../constants';
import { XIcon } from '../../../icons';
import { useJobCreatorValidation } from '../../../utils/JobCreatorValidationContext';
import { CreateJobFormSchema1 } from '../../../utils/jobSchemas';
import IndustrySelector from './IndustrySelector';
import { TJobCreatorData } from './JobCreator';

interface CustomFormErrors {
  job_custom_fields?: Array<{
    fieldTitle?: { message?: string };
    fieldValue?: { message: string };
  }>;
}

interface Step1Props {
  jobData: TJobCreatorData;
  setJobData: React.Dispatch<React.SetStateAction<TJobCreatorData>>;
}

const Step1 = ({ jobData, setJobData }: Step1Props) => {
  const { userInfo } = useUser();

  const getCompanyData = useCallback(() => {
    const companyData = userInfo as recruiterInterface;
    return companyData.company?.description;
  }, [userInfo]);
  const {
    register,
    formState: { errors },
    getValues,
    setValue,
    clearErrors,
    setError,
    watch,
    control,
    trigger,
    reset,
  } = useForm({
    resolver: zodResolver(CreateJobFormSchema1),
    defaultValues: {
      name: jobData.name,
      industry: jobData.industry,
      description: jobData.description,
      requirements: jobData.requirements,
      compulsory_requirements: jobData.compulsory_requirements,
      added_advantage: jobData.added_advantage,
      company_overview: jobData.company_overview || getCompanyData(),
      job_type: jobData.job_type,
      work_experience: jobData.work_experience,
      proficiency_level: jobData.proficiency_level,
      post_as_anonymous: jobData.post_as_anonymous,
      show_in_career_page: jobData.show_in_career_page,
      location: jobData.location,
      is_multi_location: jobData.is_multi_location,
      preferred_locations: jobData.preferred_locations,
      salary_type: jobData.salary_type,
      min_salary: jobData.min_salary,
      max_salary: jobData.max_salary,
      fixed_salary: jobData.fixed_salary,
      salary_currency: jobData.salary_currency,
      salary_negotiable: jobData.salary_negotiable,
      application_start_date: jobData.application_start_date
        ? new Date(jobData.application_start_date!)
        : new Date(),
      application_deadline: jobData.application_deadline
        ? new Date(jobData.application_deadline!)
        : new Date(),
      responsibilities: jobData.responsibilities,
      job_custom_fields: jobData.job_custom_fields,
      working_option: jobData.working_option,
     },
    mode: 'onChange',
  });
  const currentSalaryType = watch('salary_type');
  const { fields, append, remove } = useFieldArray({
    name: 'job_custom_fields',
    control,
  });
  const customErrors = useForm().formState.errors as CustomFormErrors;

  const {
    mutate: generateResponsibilities,
    isLoading: isGeneratingResponsibilities,
  } = useGenerateResponsibilities();
  const generateJobDescription = async (
    e: MouseEvent<HTMLButtonElement, globalThis.MouseEvent>
  ) => {
    e.preventDefault();
    const job_title = watch('name');
    const industry = watch('industry');
    if (
      job_title === undefined ||
      job_title === '' ||
      industry === undefined ||
      industry === ''
    ) {
      if (job_title == undefined || job_title == '') {
        setError('name', {
          message: 'Please enter job title to generate job description',
        });
      }
      if (industry == undefined || industry == '') {
        setError('industry', {
          message: 'Please select industry to generate job description',
        });
      }
      setError('description', {
        message:
          'Please select industry and enter job title to generate job description',
      });
      return;
    }
    const data = { job_title, industry };
    clearErrors([
      'industry',
      'name',
      'description',
      'requirements',
      'responsibilities',
    ]);
    generateResponsibilities(data, {
      onSuccess: data => {
        const { job_description, job_requirements, job_responsibilities } =
          data;
        setValue('description', job_description);
        setValue('requirements', job_requirements.join('\n'));
        setValue('responsibilities', job_responsibilities.join('\n'));
        clearErrors([
          'industry',
          'name',
          'description',
          'requirements',
          'responsibilities',
        ]);
      },
    });
  };

  const isMultiLocation = watch('is_multi_location');
  const PreferredLocations = watch('preferred_locations');
  const [prefferedLocationInput, setPrefferedLocationInput] = useState('');
  const [countryList, setCountryList] = useState<ICountry[]>();
  const [stateList, setStateList] = useState<IState[]>();
  const [selectedLocation, setSelectedLocation] = useState<{
    country?: string;
    state?: string;
  }>({
    country: watch('location')?.split(',')[0] || 'Nigeria',
    state: watch('location')?.split(',')[1] || 'Lagos',
  });
  const [countryCode, setCountryCode] = useState('NG');
  const countryOptions = countryList?.map(country => ({
    value: country?.name,
    label: country.name,
    code: country?.isoCode,
  }));
  const stateOptions = stateList?.map(state => ({
    value: state?.name,
    label: state.name,
    code: state?.isoCode,
  }));



  const handlePrefferedLocationInput = () => {
    if (!prefferedLocationInput || prefferedLocationInput?.trim() == '') return;
    const prevPreferredLocations = watch('preferred_locations');
    setValue('preferred_locations', [
      ...prevPreferredLocations,
      prefferedLocationInput,
    ]);
    setPrefferedLocationInput('');
  };
  const handleRemovePrefferredLocation = (location: string) => {
    const prevPreferredLocations = watch('preferred_locations');
    const updatedLocations = prevPreferredLocations.filter(
      (loc: string) => loc !== location
    );
    setValue('preferred_locations', updatedLocations);
  };
  useEffect(() => {
    setCountryList(Country?.getAllCountries());
  }, []);

  useEffect(() => {
    setStateList(State?.getStatesOfCountry(String(countryCode)));
  }, [countryCode]);

  function handleChange(fieldName: string, value: string | Date) {
    setValue(fieldName as keyof typeof jobData as any, value);
    setJobData(prev => {
      return {
        ...prev,
        [fieldName]: value,
      };
    });
  }

  const { data, isLoading: isLoadingIndustries } = useGetIndustries();

  const handleJobTypeChange = useMemo(
    () => debounce(value => handleChange('job_type', value), 100),
    [handleChange]
  );
  const handleCurrencyChange = useMemo(
    () => debounce(value => handleChange('salary_currency', value), 100),
    [handleChange]
  );

  const handleOverviewChange = useMemo(
    () =>
      debounce((value: string) => {
        handleChange('company_overview', value),
          clearErrors('company_overview');
      }, 100),
    [handleChange]
  );
  const handleShowInCareerPageChange = useMemo(
    () => debounce(value => handleChange('show_in_career_page', value), 100),
    [handleChange]
  );
  const handleDescriptionChange = useMemo(
    () =>
      debounce(value => {
        handleChange('description', value), clearErrors('description');
      }, 100),
    [handleChange]
  );
  const handleRequirementsChange = useMemo(
    () =>
      debounce(value => {
        handleChange('requirements', value), clearErrors('requirements');
      }, 100),
    [handleChange]
  );
  const handleResponsibilitiesChange = useMemo(
    () =>
      debounce(value => {
        handleChange('responsibilities', value), clearErrors('industry');
      }, 100),
    [handleChange]
  );
  const handleCompulsoryRequirementsChange = useMemo(
    () =>
      debounce(value => {
        handleChange('compulsory_requirements', value),
          clearErrors('compulsory_requirements');
      }, 100),
    [handleChange]
  );
  const handleAddedAdvantageChange = useMemo(
    () => debounce(value => handleChange('added_advantage', value), 100),
    [handleChange]
  );
  const handleApplicationStartDateChange = useMemo(
    () => debounce(value => handleChange('application_start_date', value), 100),
    [handleChange]
  );

  useEffect(() => {
    if (currentSalaryType == 'RANGE') {
      setValue('fixed_salary', undefined);
    } else if (currentSalaryType == 'FIXED') {
      setValue('min_salary', undefined);
      setValue('max_salary', undefined);
    } else if (currentSalaryType == 'UNDISCLOSED') {
      setValue('min_salary', undefined);
      setValue('max_salary', undefined);
      setValue('fixed_salary', undefined);
    }
  }, [currentSalaryType]);


  const { registerValidation, registerValidationWithErrors } =
    useJobCreatorValidation();
  useEffect(() => {
    registerValidation(1, async () => {
      const result = await trigger();
      return result;
    });

    registerValidationWithErrors(1, async () => {
      const result = await trigger();
      return {
        isValid: result,
        errors: result ? undefined : errors,
      };
    });
  }, [registerValidation, registerValidationWithErrors, trigger, errors]);

  useEffect(() => {
    const debouncedFn = debounce(value => {
      setJobData(prev => {
        return { ...prev, ...value };
      });
    }, 100);
    const subscription = watch(debouncedFn);
    return () => subscription.unsubscribe();
  }, [watch]);

 
  const _location = watch('location');
  useEffect(()=>{
console.log(_location, "LOCATION CHANGED")
  },[_location])
 
 
  useEffect(() => {
    if (selectedLocation.country && selectedLocation.state) {
      const location = `${selectedLocation.country}, ${selectedLocation.state}`;
      console.log(location, "LOCATION ON MOUNT SE");

      // Use setTimeout to ensure form is ready
      const timer = setTimeout(() => {
        setValue('location', location, { shouldValidate: true });
        setJobData(prev => ({
          ...prev,
          location,
        }));
      }, 0);

      return () => clearTimeout(timer);
    }
  }, [setValue,  selectedLocation.state, setJobData]);
  





  useEffect(() => {
    reset({
      name: jobData.name,
      industry: jobData.industry,
      description: jobData.description,
      requirements: jobData.requirements,
      compulsory_requirements: jobData.compulsory_requirements || '',
      added_advantage: jobData.added_advantage || '',
      company_overview: jobData.company_overview || getCompanyData(),
      job_type: jobData.job_type,
      work_experience: jobData.work_experience,
      proficiency_level: jobData.proficiency_level,
      post_as_anonymous: jobData.post_as_anonymous,
      show_in_career_page: jobData.show_in_career_page,
      location: jobData.location,
      is_multi_location: jobData.is_multi_location,
      preferred_locations: jobData.preferred_locations,
      salary_type: jobData.salary_type,
      min_salary: jobData.min_salary,
      max_salary: jobData.max_salary,
      fixed_salary: jobData.fixed_salary,
      salary_currency: jobData.salary_currency,
      salary_negotiable: jobData.salary_negotiable,
      application_start_date: jobData.application_start_date
        ? new Date(jobData.application_start_date!)
        : new Date(),
      application_deadline: jobData.application_deadline
        ? new Date(jobData.application_deadline!)
        : new Date(),
      responsibilities: jobData.responsibilities,
      job_custom_fields: jobData.job_custom_fields,
      working_option: jobData.working_option,
    });
  }, [jobData.name]);

  return (
    <form className="flex max-w-[900px] flex-col gap-4">
      <header className="mb-8">
        <h4 className="text-[1.05rem] font-medium text-header-text">
          Job post details
        </h4>
        <p className="text-[0.875rem]  text-body-text">
          A job posting represents a new opening or a vacancy listing.
          <br />
          Kindly note that details provide here will be made available to
          applicants
        </p>
      </header>

      <Input
        label="Job Title/Role"
        variant="dark_showcase"
        type="text"
        placeholder="Enter role name"
        id="name"
        maxLength={100}
        {...register('name')}
        hasError={!!errors.name}
        errorMessage={errors.name?.message as string}
      />

      <div className="mt-4 flex flex-col">
        <label className="mb-1 flex w-full items-center justify-between text-sm text-header-text">
          <span>
            {isMultiLocation && 'Main '}
            Location
          </span>

          <Checkbox3
            label="Set multiple location"
            checked={isMultiLocation}
            onCheckedChange={checked =>
              setValue('is_multi_location', !!checked)
            }
            className="h-4 w-4 rounded-full"
            labelClass="text-body-text font-nornal"
            checkClassName="h-2.5 w-2.5 stroke-[5]"
          />
        </label>
        <div className="grid w-full flex-col items-center sm:grid-cols-2 sm:gap-4">
          <SelectSingleCombo
            name="country"
            placeholder="Select Country"
            value={selectedLocation.country}
            onChange={val => {
              const chosen =
                countryOptions &&
                countryOptions.filter(
                  country =>
                    country.value.toLowerCase() == val.toLocaleLowerCase()
                )[0];
              setSelectedLocation(prev => {
                return { ...prev, country: chosen?.value!, state:"" };
              });
              countryOptions && setCountryCode(chosen?.code!);
              setValue('location', "");

            }}
            variant="dark_showcase"
            options={countryOptions! || []}
            valueKey="value"
            labelKey="label"
          />

          <SelectSingleCombo
            name="state"
            placeholder="Select state"
            value={selectedLocation.state}
            valueKey="value"
            labelKey="label"
            onChange={val => {
              setSelectedLocation(prev => {
                return { ...prev, state: val };
              });
              setValue('location', `${selectedLocation.country}, ${val}`);
            }}
            variant="dark_showcase"
            options={stateOptions || []}
            isLoadingOptions={
              !stateOptions || selectedLocation.country == undefined
            }
          />
        </div>
        {!!errors.location && (
          <FormError errorMessage={errors.location?.message as string} />
        )}
        <div className="mt-2 rounded-xl bg-primary-light p-2 px-4 text-xs text-primary">
          Choose multiple locations if this job can be performed from more than
          one office or city. You can list all applicable locations to broaden
          your candidate pool. This can be useful if you're hiring for multiple
          office branches for example
        </div>
      </div>

      {isMultiLocation && (
        <div>
          <Input
            label="Preferred Locations"
            variant="dark_showcase"
            type="text"
            placeholder="Enter location and click enter"
            id="location"
            maxLength={100}
            value={prefferedLocationInput}
            onChange={e => setPrefferedLocationInput(e.target.value)}
            hasError={isMultiLocation && !PreferredLocations.length}
            errorMessage={'Enter at least one location'}
            onKeyDown={e => {
              if (e.key == 'Enter') {
                handlePrefferedLocationInput();
              }
            }}
            rightIcon={
              <button
                className="text-primary"
                disabled={prefferedLocationInput?.trim().length == 0}
                type="button"
                title={
                  prefferedLocationInput?.trim().length == 0
                    ? 'Enter location'
                    : ''
                }
                onClick={handlePrefferedLocationInput}
              >
                <Send size={17} />
              </button>
            }
          />

          <div className="mt-2.5 flex flex-wrap items-center gap-x-3 gap-y-2">
            {PreferredLocations?.map((location: string) => (
              <div className="flex items-center gap-3 rounded-full bg-primary-light px-2.5 py-1 text-xs text-primary md:gap-6 md:px-4 ">
                {location}
                <button
                  type="button"
                  className="text-base font-bold"
                  onClick={() => handleRemovePrefferredLocation(location)}
                >
                  x
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
      <fieldset className="grid-cols-2 gap-x-12 md:grid max-md:space-y-[0.875rem]">
        <IndustrySelector
          name="industry"
          label="Industry"
          value={watch('industry')}
          isLoadingOptions={isLoadingIndustries}
          onChange={val => {
            setValue('industry', val);
            clearErrors('industry');
          }}
          options={data?.map(ind => ({ name: ind.name, value: ind.name }))}
          valueKey="value"
          labelKey="name"
          variant="dark_showcase"
          placeholder="Search industry or create new one"
          hasError={!!errors.industry}
          errorMessage={errors.industry?.message as string}
          containerClass="!mt-2"
          itemClass="text-[0.8rem]"
        />

        <Select
          label="Experience"
          {...register('work_experience')}
          value={watch('work_experience')}
          onChange={val => {
            setValue('work_experience', val);
            clearErrors('work_experience');
          }}
          options={work_experiences}
          labelKey="name"
          valueKey="value"
          variant="dark_showcase"
          placeholder="Select required experience"
          containerClass="!mt-2"
          hasError={!!errors.work_experience}
          errorMessage={errors.work_experience?.message as string}
        />
      </fieldset>

      <fieldset className="grid-cols-2 gap-x-12 md:grid max-md:space-y-[0.875rem]">
        <Select
          label="Proficiency"
          {...register('proficiency_level')}
          value={watch('proficiency_level')}
          onChange={val => {
            setValue('proficiency_level', val);
            clearErrors('proficiency_level');
          }}
          options={proficiency_levels}
          labelKey="name"
          valueKey="value"
          variant="dark_showcase"
          placeholder="Select proficiency level"
          hasError={!!errors.proficiency_level}
          errorMessage={errors.proficiency_level?.message as string}
        />

        <Select
          label="Working Option"
          {...register('working_option')}
          value={watch('working_option')}
          onChange={val => {
            setValue('working_option', val);
            clearErrors('working_option');
          }}
          labelKey="name"
          valueKey="value"
          variant="dark_showcase"
          hasError={!!errors.working_option}
          errorMessage={errors.working_option?.message as string}
          options={working_options}
          placeholder="Select work option"
        />
      </fieldset>

      <RadioGroup
        options={job_type}
        onChange={handleJobTypeChange}
        label="Employment Type"
        errors={errors}
        value={watch('job_type')}
        name="job_type"
        variant="light"
        arrangement="row"
      />

      <Select
        label="Salary Option"
        {...register('salary_type')}
        value={watch('salary_type')}
        onChange={val =>
          setValue('salary_type', val as 'FIXED' | 'RANGE' | 'UNDISCLOSED')
        }
        options={salary_types}
        labelKey="name"
        valueKey="value"
        variant="dark_showcase"
        placeholder="Select salary type"
        itemClass="text-[0.825rem]"
        fullWidth
      />

      <fieldset
        className={cn('', currentSalaryType === 'UNDISCLOSED' && 'hidden')}
      >
        <label htmlFor="" className="text-sm text-header-text">
          {currentSalaryType === 'RANGE'
            ? 'Salary Range'
            : currentSalaryType === 'FIXED'
              ? 'Salary Amount'
              : null}
        </label>

        <div
          className={cn(
            'grid  gap-4',
            currentSalaryType === 'RANGE' &&
            'grid-cols-[minmax(0,5rem)_repeat(2,minmax(0,1fr))]',
            currentSalaryType === 'FIXED' &&
            'grid-cols-[minmax(0,5rem)_repeat(1,minmax(0,1fr))]'
          )}
        >
          <Select
            {...register('salary_currency')}
            onChange={handleCurrencyChange}
            value={watch('salary_currency')}
            options={currencies}
            labelKey="name"
            valueKey="value"
            variant="dark_showcase"
            placeholder="Curr"
            hasError={!!errors.salary_currency}
            errorMessage={errors.salary_currency?.message as string}
          />

          {currentSalaryType === 'RANGE' ? (
            <>
              <AmountInput
                {...register('min_salary')}
                value={watch('min_salary')}
                hasError={!!errors.min_salary}
                errorMessage={errors.min_salary?.message as string}
                variant="dark_showcase"
              />
              <AmountInput
                {...register('max_salary')}
                value={watch('max_salary')}
                hasError={!!errors.max_salary}
                errorMessage={errors.max_salary?.message as string}
                variant="dark_showcase"
              />
            </>
          ) : currentSalaryType === 'FIXED' ? (
            <AmountInput
              {...register('fixed_salary')}
              value={watch('fixed_salary')}
              hasError={!!errors.fixed_salary}
              errorMessage={errors.fixed_salary?.message as string}
              variant="dark_showcase"
            />
          ) : null}
        </div>
      </fieldset>
      <RichTextEditor
        label="Company Overview"
        onChange={handleOverviewChange}
        initialContent={watch('company_overview') || getCompanyData()}
        hasError={!!errors.company_overview}
        errorMessage={errors.company_overview?.message as string}
        disableCopyPaste
      // className='max-h-[300px] overflow-y-scroll'
      />
      <RichTextEditor
        label={
          <div className="flex flex-wrap items-center justify-between">
            <p>Job Description</p>
            <Button
              variant="light"
              size="capsule"
              type="button"
              onClick={e => generateJobDescription(e)}
              icon={isGeneratingResponsibilities && <LoaderBtn />}
            >
              Generate Description/Requirements/Responsibilities
            </Button>
          </div>
        }
        initialContent={watch('description')}
        onChange={handleDescriptionChange}
        placeholder="Enter job description"
        hasError={!!errors.description}
        errorMessage={errors.description?.message as string}
      // className='max-h-[300px] overflow-y-scroll'
      />

      <RichTextEditor
        label="Requirements"
        value={watch('requirements')}
        initialContent={watch('requirements')}
        onChange={handleRequirementsChange}
        placeholder="Enter requirements"
        hasError={!!errors.requirements}
        errorMessage={errors.requirements?.message as string}
      // className='max-h-[300px] overflow-y-scroll'
      />
      <RichTextEditor
        label={
          <p className="flex items-center">
            Additional Requirements{' '}
            <span className="text-helper-text">(optional)</span>
            <ToolTip
              contentClass="text-body-text font-normal"
              content="This field should ONLY contain what the candidate MUST have, our system will automatically disregard and score any candidates without the requirements listed here ZERO."
            >
              <Info />
            </ToolTip>
          </p>
        }
        placeholder="This field should ONLY contain what the candidate MUST have, our system will automatically disregard and score any candidates without the requirements listed here ZERO."
        value={watch('compulsory_requirements')}
        initialContent={watch('compulsory_requirements')}
        onChange={handleCompulsoryRequirementsChange}
        hasError={!!errors.compulsory_requirements}
        errorMessage={errors.compulsory_requirements?.message as string}
      // className='max-h-[300px] overflow-y-scroll'
      />

      <RichTextEditor
        label={
          <>
            Added Advantage <span className="text-helper-text">(optional)</span>
          </>
        }
        placeholder="Enter added advantage"
        value={watch('added_advantage')}
        initialContent={watch('added_advantage')}
        onChange={handleAddedAdvantageChange}
        hasError={!!errors.added_advantage}
        errorMessage={errors.added_advantage?.message as string}
      // className='max-h-[300px] overflow-y-scroll'
      />

      <RichTextEditor
        label={
          <>
            Responsibilities{' '}
            <span className="text-helper-text">(optional)</span>
          </>
        }
        value={watch('responsibilities')}
        placeholder="Enter responsibilities"
        initialContent={watch('responsibilities')}
        onChange={handleResponsibilitiesChange}
        hasError={!!errors.responsibilities}
        errorMessage={errors.responsibilities?.message as string}
      // className='max-h-[300px] overflow-y-scroll'
      />

      <div className="my-2 flex flex-col gap-y-2">
        {fields.map((field, index) => {
          return (
            <fieldset key={field.id}>
              <div className="inputdiv relative">
                <input
                  placeholder="Type field title"
                  type="text"
                  className={cn(
                    '!bg-[#F5F7F9] text-[15px] font-[500] text-secondary-text',
                    customErrors.job_custom_fields &&
                    customErrors.job_custom_fields[index]?.fieldTitle &&
                    'error'
                  )}
                  {...register(`job_custom_fields.${index}.fieldTitle`, {
                    required: true,
                  })}
                />
                <div
                  className="absolute right-[2%] top-[5%]"
                  title="Delete Field"
                >
                  <XIcon
                    className="cursor-pointer rounded-full bg-white p-1 hover:bg-red-300"
                    onClick={() => remove(index)}
                  />
                </div>
                {customErrors.job_custom_fields &&
                  customErrors.job_custom_fields[index]?.fieldTitle && (
                    <p className="formerror">
                      {
                        customErrors?.job_custom_fields[index]?.fieldTitle
                          ?.message
                      }
                    </p>
                  )}

                <TextareaAutosize
                  className={cn(
                    'resize-none !bg-[#F5F7F9]',
                    customErrors.job_custom_fields &&
                    customErrors.job_custom_fields[index]?.fieldValue &&
                    'error'
                  )}
                  minRows={5}
                  maxRows={10}
                  {...register(`job_custom_fields.${index}.fieldValue`, {
                    required: true,
                  })}
                  placeholder="Type here"
                />
                {customErrors.job_custom_fields &&
                  customErrors.job_custom_fields[index]?.fieldValue && (
                    <p className="formerror">
                      {
                        customErrors?.job_custom_fields[index]?.fieldValue
                          ?.message
                      }
                    </p>
                  )}
              </div>
            </fieldset>
          );
        })}

        <Button
          icon={<Plus />}
          variant="light"
          type="button"
          className="mb-8 w-full"
          onClick={e => {
            append({ fieldTitle: '', fieldValue: '' });
            e.preventDefault();
          }}
        >
          Add Custom Field
        </Button>
      </div>

      <div className="inputdiv">
        <label className="">
          How long do you want this Job to be active for ?
        </label>
        <div className="grid-cols-2 flex-wrap gap-8 xs:grid xs:gap-[10vw] max-xs:flex">
          <Controller
            control={control}
            name="application_start_date"
            render={({ field: { onChange, value } }) => (
              <SingleDatePicker
                className="w-full bg-[#F5F7F9] py-2.5"
                id="application_start_date"
                label="Post date"
                placeholder="Start Date"
                value={watch('application_start_date') || value}
                onChange={handleApplicationStartDateChange}
                errors={errors}
              />
            )}
          />
          <Controller
            control={control}
            name="application_deadline"
            render={({ field: { onChange, value } }) => (
              <SingleDatePicker
                className="w-full bg-[#F5F7F9] py-2.5"
                id="application_deadline"
                label="End opening"
                placeholder="End Date"
                value={
                  watch('application_deadline')
                    ? new Date(watch('application_deadline') as Date)
                    : value
                }
                onChange={value => setValue('application_deadline', value)}
                errors={errors}
              />
            )}
          />
        </div>
      </div>

      <div className="flex items-start">
        <Controller
          control={control}
          name={'show_in_career_page'}
          render={({ field: { onChange, value = false } }) => (
            <div className=" flex items-center gap-1">
              <label className="block text-sm" htmlFor="show_in_career_page">
                Show in career page
              </label>
              <Switch
                checked={value || false}
                // className="scale-[.75] my-2"
                id={'show_in_career_page'}
                onCheckedChange={checked =>
                  handleShowInCareerPageChange(checked) && onChange(checked)
                }
              />
            </div>
          )}
        />
      </div>
    </form>
  );
};

export default Step1;
