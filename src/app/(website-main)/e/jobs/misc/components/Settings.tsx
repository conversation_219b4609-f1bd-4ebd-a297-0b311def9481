import React, { useEffect, useState } from 'react'
import useInfiniteScroll from 'react-infinite-scroll-hook'

import { Button } from '@/components/shared'
import { Plus } from '@/components/shared/icons'
import { useBooleanStateControl } from '@/hooks'

import CreateNewPipeline from './modals/CreateNewPipelineStep1'
import { Pipeline, Stage } from '../types'
import { useGetPipelines } from '../api'
import PipelineCardSkeleton from './cards/PipelineCardLoader'
import PipelineCard from './cards/PipelineCard'
import PipelineDetails from './SectionPipelineDetails'




const Settings = () => {
  const defaultPipeline = {
    id: 1,
    name: "Getlinked Default Pipeline",
    stages: [
      {
        id: 1,
        name: "New Candidates",
        move_criteria: 100,
        is_interview: false,
        is_assessment: false,
        automation_enabled: true,
        email_notification_enabled: false,
        email_subject: "",
        email_body: "",
        assessment: null,
        order: 1
      },
      {
        id: 2,
        name: "<PERSON>",
        move_criteria: 50,
        is_interview: false,
        is_assessment: true,
        automation_enabled: true,
        email_notification_enabled: true,
        email_subject: "Assessment Invitation",
        email_body: "Assessment",
        assessment: null,
        order: 2
      },
      {
        id: 3,
        name: "Interview",
        move_criteria: 15,
        is_assessment: false,
        is_interview: true,
        automation_enabled: true,
        email_notification_enabled: true,
        email_subject: "Interview Invitation",
        email_body: "Assessment",
        assessment: null,
        order: 3
      },
      {
        id: 4,
        name: "Hired",
        move_criteria: 0,
        is_interview: false,
        is_assessment: false,
        automation_enabled: true,
        email_notification_enabled: true,
        email_subject: "You're hired",
        email_body: "Hired!",
        assessment: null,
        order: 4
      }
    ],
    is_default: true,
    recruiter: 1,
    company: 1,
    created_at: "2023-03-19T12:00:00Z"
  }

  //@ts-ignore
  const [currentDetails, setcurrentDetails] = useState<Pipeline>(defaultPipeline)
  const [currentDetailsIndex, setcurrentDetailsIndex] = useState<number>(0)


  const {
    state: isNewPipelineStep1ModalOpen,
    setTrue: openNewPipelineStep1Modal,
    setFalse: closeNewPipelineStep1Modal
  } = useBooleanStateControl()




  const {
    data,
    error,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isFetchingNextPage,
    isLoading: loading,
  } = useGetPipelines()
  const nextPage = hasNextPage !== undefined && data?.pages !== null;
  const [sentryRef] = useInfiniteScroll({
    loading: isFetchingNextPage || loading,
    hasNextPage: nextPage,
    onLoadMore: fetchNextPage,
    disabled: !!error,
    rootMargin: "0px 0px 400px 0px",
  });

  useEffect(() => {
    if (data && data.pages) {
      const pipelines = data.pages.flatMap(page => page.results);
      if (pipelines[currentDetailsIndex]) {
        setcurrentDetails(pipelines[currentDetailsIndex]);
      }
    }
  }, [data, currentDetailsIndex]);



  return (
    <div className="grid lg:grid-cols-2 gap-6 md:gap-[2vw] h-[80vh] relative overflow-x-auto overflow-y-hidden px-4 md:px-8 pt-4 max-md:pb-16">
      {/* /////////////////////////////////// */}
      {/* ///////////     LEFT    /////////// */}
      {/* /////////////////////////////////// */}
      <section className="flex flex-col gap-2 top-0 bg-white max-h-full pt-4 md:pt-8 rounded-[1rem] overflow-y-scroll" >
        <header className='flex flex-col px-4 md:px-8'>

          <h3 className='font-medium text-header-text text-base'>Job pipelines settings</h3>
          <p className='text-body-text text-[0.875rem] mb-6 max-w-[45ch]'>
            Here you can create and configure existing pipelines to fit your recruitment process needs
          </p>
        </header>



        <div className='flex flex-col gap-1.5 mb-12 px-4 md:px-8'>
          <article className='p-4 border-[1.2px] border-[#E4E4E4] rounded-lg max-w-[500px]'>
            <header className='flex items-center justify-between'>
              <h3 className='text-[0.875rem] text-header-text font-medium'>Default Job Pipeline</h3>

            </header>
            <p className='text-[#7D8590] text-[0.75rem] mb-3 max-w-[35ch]'>
              This pipeline is a default pipeline created by Getlinked to ease your hiring process
            </p>
            <footer className='flex items-center justify-between'>
              <p className='text-[#7D8590] text-[0.75rem]'>Pipeline stages: <span className='text-[#0E0E2C]'>4</span></p>
              <Button variant='extralight' size='thin' className='text-[0.75rem]' onClick={() => {
                //@ts-ignore
                setcurrentDetails(defaultPipeline); setcurrentDetailsIndex(-1)
              }}>
                View details
              </Button>
            </footer>
          </article>



          {
            loading &&
            Array(6)?.fill(8)?.map((_, idx: number) => (
              <div className="flex flex-col gap-6 w-full">
                <PipelineCardSkeleton />
              </div>
            ))
          }


          {
            data?.pages?.map((pipelines: any) =>
              pipelines?.results?.map((pipeline: Pipeline, index: number) => (
                <>
                  {
                    !loading && (
                      <PipelineCard
                        key={pipeline?.id}
                        index={index}
                        pipeline={pipeline}
                        //@ts-ignore
                        defaultPipeline={defaultPipeline}
                        currentDetailsIndex={currentDetailsIndex}
                        setcurrentDetails={(pipeline) => {
                          setcurrentDetails(pipeline);
                          setcurrentDetailsIndex(index);
                        }}
                        setcurrentDetailsIndex={setcurrentDetailsIndex}
                      />
                    )}
                </>
              ))
            )
          }


          {
            data?.pages?.map((pipelines: any) =>
              (!pipelines?.results?.length || pipelines?.results?.length < 1) &&
              <article className='flex flex-col items-center justify-center gap-5 bg-primary-light w-full p-4 rounded-lg max-w-[550px]'>
                <p className='text-primary text-[0.875rem] text-center'>You have not created any custom pipelines</p>
                <Button size='tiny' onClick={(e) => { e.preventDefault(); openNewPipelineStep1Modal() }}>Create Pipeline</Button>
              </article>
            )
          }


        </div>




        {
          data?.pages?.map((pipelines: any) =>
            (pipelines?.results?.length && pipelines?.results?.length > 0) && (
              <footer className='sticky bottom-0 p-4 md:pb-6 mt-auto bg-white shadow-xl'>
                <Button variant='light' className='w-full max-w-[510px] ' icon={<Plus />} onClick={openNewPipelineStep1Modal}>
                  Create new pipeline
                </Button>
              </footer>
            )
          )
        }

      </section>

      <CreateNewPipeline isNewPipelineStep1ModalOpen={isNewPipelineStep1ModalOpen} closeNewPipelineStep1Modal={closeNewPipelineStep1Modal} />


      <PipelineDetails pipeline={currentDetails} />
    </div>
  )
}

export default Settings