import { RightUpArrow } from '@/app/(auth)/misc/icons';
import {
  Button,
  ConfirmActionModal,
  ConfirmDeleteModal,
  LinkButton,
  LoadingOverlay,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/shared';
import { Elipsis, StrokeClose, View } from '@/components/shared/icons';
import { useBooleanStateControl } from '@/hooks';
import { cn } from '@/utils';
import { convertKebabAndSnakeToTitleCase } from '@/utils/strings';
import { format } from 'date-fns';
import Link from 'next/link';
import React, { useMemo } from 'react';
import toast from 'react-hot-toast';
import { useCloseJob, useDeleteJob } from '../../api';
import {
  CloseJobIcon,
  CopyIcon,
  DeleteIcon,
  EditIcon,
  SuitcaseIcon,
} from '../../icons';
import { ActiveJob } from '../../types';

interface CardProps {
  job: ActiveJob;
  refetch: () => void;
}

const JobCard: React.FC<CardProps> = ({ job, refetch }) => {
  const {
    state: isConfirmDeleteModalOpen,
    setTrue: openConfirmDeleteModal,
    setFalse: closeConfirmDeleteModal,
  } = useBooleanStateControl(false);
  const {
    state: isConfirmCloseModalOpen,
    setTrue: openConfirmCloseModal,
    setFalse: closeConfirmCloseModal,
  } = useBooleanStateControl(false);

  const { mutate: deleteJob, isLoading: isDeletingJob } = useDeleteJob();
  const { mutate: closeJob, isLoading: isClosingJob } = useCloseJob();
  const {
    id,
    job_title,
    job_status,
    job_type,
    applicant,
    unique_id,
    application_deadline,
    total_applicants,
    pipeline,
    stage_data,
    viewers_count,
    hiring_metrics,
  } = job!!;
  const copyJobLink = (link: string) => {
    if (typeof navigator !== undefined) {
      navigator.clipboard.writeText(link);
      toast.success('Job link copied successfully');
    }
  };

  const getCurrentJobLink = () => {
    if (typeof window !== 'undefined') {
      return `${window.location.origin}/jobs/${unique_id}`;
    }
    return `https://app.getlinked.ai/jobs/${unique_id}`; // fallback for SSR
  };

  const Delete = () => {
    deleteJob(String(id), {
      onSuccess: () => {
        toast.success('Job deleted successfully');
        closeConfirmDeleteModal();
        refetch();
      },
    });
  };
  const Close = () => {
    closeJob(String(id), {
      onSuccess: () => {
        toast.success('Job closed successfully');
        closeConfirmCloseModal();
        refetch();
      },
    });
  };

  const stagesShow = useMemo(() => {
    const keyz = Object.keys(stage_data.data);
    const valuez = Object.values(stage_data.data);
    const initialStagesToDisplay = keyz.map((key, index) => ({
      name: key,
      value: valuez[index],
    }));

    const remStagesCount = 4 - keyz.length > 0 ? 4 - keyz.length : 0;
    const splicedRemainingStages = pipeline?.stages.slice(
      keyz.length,
      keyz.length + remStagesCount
    );
    const remainingStagesToDisplay = splicedRemainingStages
      ? splicedRemainingStages.map(stage => ({ name: stage.name, value: 0 }))
      : [];

    const combinedStagesToDisplay = [
      ...initialStagesToDisplay,
      ...remainingStagesToDisplay,
    ];
    return combinedStagesToDisplay;
  }, [stage_data, pipeline]);

  return (
    <article className="cursor-pointer  space-y-7 rounded-[1.25rem] border-[0.4px] border-[#C5D2E5] bg-white p-6 text-[0.875rem] shadow-sm md:border-none">
      <header className="flex items-center gap-4 ">
        <section className="flex items-center gap-4 ">
          <div className="rounded-[0.5rem] bg-primary-light p-[0.4rem]">
            <SuitcaseIcon />
          </div>
          <h3 className="font-semibold leading-tight">{job_title}</h3>
          <span
            className={cn(
              'rounded-md px-3.5 py-1',
              job_status == 'OPEN' &&
                'bg-[rgba(18,182,105,0.10)] text-[#12B669]',
              job_status == 'DRAFT' && 'bg-primary-light text-primary',
              job_status == 'CLOSED' &&
                'bg-[rgba(255,95,86,0.10)] text-[#FF5F56]',
              job_status == 'QUEUED' && 'bg-[#F8F9FB] text-[#7d7d7f]'
            )}
          >
            {convertKebabAndSnakeToTitleCase(job_status!)}
          </span>
        </section>

        <section className="flex items-center gap-4 text-[0.75rem] max-sm:hidden">
          <p className="text-[#8C8CA1]">
            Type:{' '}
            <span className="text-[#4A4A68]">
              {convertKebabAndSnakeToTitleCase(job_type!)}
            </span>
          </p>
          <p className="text-[#8C8CA1]">
            Expires:{' '}
            <span className="text-[#4A4A68]">
              {' '}
              {format(new Date(application_deadline!!), 'd MMM yyyy')}
            </span>
          </p>
        </section>

        <div className="ml-auto flex items-center gap-4">
          <span className="flex items-center justify-center gap-4 rounded-md bg-[#F8F9FB] px-3 py-1.5 max-sm:hidden">
            <View />
            {viewers_count} views
          </span>
          <Popover>
            <PopoverTrigger className="whitespace-nowrap px-2">
              <Elipsis />
            </PopoverTrigger>
            <PopoverContent className="w-max bg-white p-1" align="end">
              <div className="flex w-full flex-col gap-[5px]">
                {(job_status === 'DRAFT' || job_status === 'QUEUED') && (
                  <Link
                    className="no-wrap flex flex-row items-center rounded-xl px-4 py-2 hover:bg-[#F8FAFF]"
                    href={`./create?edit=${unique_id}`}
                  >
                    <EditIcon />
                    <p className="ml-2 text-xs text-[#4E4E4E]">
                      Continue Editing
                    </p>
                  </Link>
                )}
                {(job_status === 'OPEN' || job_status === 'CLOSED') && (
                  <Link
                    className="no-wrap flex flex-row items-center rounded-xl px-4 py-2 hover:bg-[#F8FAFF]"
                    href={`./job/${unique_id}/candidates`}
                  >
                    <EditIcon />
                    <p className="ml-2 text-xs text-[#4E4E4E]">View details</p>
                  </Link>
                )}
                {job_status !== 'DRAFT' && (
                  <Link
                    className="no-wrap flex flex-row items-center rounded-xl px-4 py-2 hover:bg-[#F8FAFF]"
                    href={`./job/${unique_id}/configure`}
                  >
                    <EditIcon />
                    <p className="ml-2 text-xs text-[#4E4E4E]">Configure Job</p>
                  </Link>
                )}
                {job_status !== 'DRAFT' && (
                  <button
                    className="no-wrap flex flex-row items-center rounded-xl px-4 py-2 hover:bg-[#F8FAFF]"
                    onClick={openConfirmCloseModal}
                  >
                    <CloseJobIcon />
                    <p className="ml-2 text-xs text-[#4E4E4E]">Close Job</p>
                  </button>
                )}
                <button
                  className="no-wrap flex flex-row items-center rounded-xl px-4 py-2 hover:bg-[#F8FAFF]"
                  onClick={openConfirmDeleteModal}
                >
                  <DeleteIcon />
                  <p className="ml-2 text-xs text-[#4E4E4E]">Delete Job</p>
                </button>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </header>

      <div className=" flex items-center gap-8 max-sm:grid max-sm:grid-cols-2 max-sm:gap-2.5">
        <div className="flex items-center gap-2">
          <p className="rounded-[0.4375rem] bg-[#F8F9FB] px-2 py-1 text-sm text-header-text">
            {stage_data.total_count || '0'}
          </p>
          <p className="text-sm">Applicants</p>
        </div>
        {stagesShow?.map((stage, index) => (
          <div key={index} className="flex items-center gap-2">
            <p className="rounded-[0.5rem] bg-[#F8F9FB] px-2 py-1 text-header-text">
              {stage.value || '0'}
            </p>
            <p className="text-[0.75rem]">{stage.name}</p>
          </div>
        ))}

        {pipeline?.stages?.length && pipeline?.stages?.length > 4 && (
          <Link
            href={`./job/${unique_id}/pipeline`}
            className="ml-4 text-xs text-primary underline decoration-primary underline-offset-2"
          >
            See more...
          </Link>
        )}
      </div>

      {/* Hiring metrics row */}
      <div className="flex flex-wrap items-center gap-8 max-sm:grid max-sm:grid-cols-2 max-sm:gap-2.5">
        <div className="flex items-center gap-2">
          <p className="rounded-[0.4375rem] bg-[#F8F9FB] px-2 py-1 text-sm text-header-text">
            {typeof hiring_metrics?.application_conversion_rate === 'number'
              ? `${Math.round(hiring_metrics.application_conversion_rate)}%`
              : '—'}
          </p>
          <p className="text-[0.75rem]">Application Conversion Rate</p>
        </div>

        <div className="flex items-center gap-2">
          <p className="rounded-[0.4375rem] bg-[#F8F9FB] px-2 py-1 text-sm text-header-text">
            {typeof hiring_metrics?.applicant_quality_rate === 'number'
              ? `${Math.round(hiring_metrics.applicant_quality_rate)}%`
              : '—'}
          </p>
          <p className="text-[0.75rem]">Applicant Quality Rate</p>
        </div>

        <div className="flex items-center gap-2">
          <p className="rounded-[0.4375rem] bg-[#F8F9FB] px-2 py-1 text-sm text-header-text">
            {typeof hiring_metrics?.interview_to_hire_ratio === 'number'
              ? hiring_metrics.interview_to_hire_ratio
              : '—'}
          </p>
          <p className="text-[0.75rem]">Interview-to-Hire Ratio</p>
        </div>
      </div>

      <footer className="flex gap-2 sm:items-center max-sm:flex-col">
        <Button
          variant="outlined"
          size="tiny"
          className="!border-[1.25px] py-2"
          onClick={() => {
            copyJobLink(getCurrentJobLink());
          }}
        >
          Copy link <CopyIcon className="h-4 w-4" />
        </Button>
        {/* <AddTeamMemberDropdown
                    previouslyAddedMembers={job.team_member}
                    job_id={job.id}
                /> */}
        <LinkButton
          href={`./job/${unique_id}/pipeline`}
          variant="light"
          size="tiny"
          className="py-2 sm:ml-auto"
          icon={<RightUpArrow fill="#755AE2" />}
        >
          View Job Detail
        </LinkButton>
      </footer>

      {/* ////////////////////////////////////////////////////// */}
      {/* ////////////////////////////////////////////////////// */}
      {/* //////////              MODALS            //////////// */}
      {/* ////////////////////////////////////////////////////// */}
      {/* ////////////////////////////////////////////////////// */}

      <ConfirmDeleteModal
        isModalOpen={isConfirmDeleteModalOpen}
        closeModal={closeConfirmDeleteModal}
        title="Delete Job"
        deleteFunction={Delete}
      >
        <div className="text-sm font-normal text-[#8C8CA1]">
          <p>
            You are about to delete job:{' '}
            <span className="mx-1 font-bold text-header-text">{job_title}</span>
            . Please be aware that this action is irreversible and any progess
            or candidates connected to this job will be{' '}
            <span className="mx-1 font-bold text-header-text">
              lost forever
            </span>
          </p>
        </div>
      </ConfirmDeleteModal>
      <ConfirmActionModal
        isModalOpen={isConfirmCloseModalOpen}
        closeModal={closeConfirmCloseModal}
        title={'Close Job'}
        confirmFunction={Close}
        icon={
          <StrokeClose stroke="white" width={45} height={45} strokeWidth={2} />
        }
      >
        <p className="text-sm font-normal text-[#8C8CA1]">
          You are about to close job:{' '}
          <span className="mx-1 font-bold text-header-text">{job_title}</span>.
          Please be aware that this action stops this job posting from taking
          more applications from the moment you click on the confirm button.
        </p>
      </ConfirmActionModal>
      <LoadingOverlay isOpen={isDeletingJob || isClosingJob} />
    </article>
  );
};

export default JobCard;
