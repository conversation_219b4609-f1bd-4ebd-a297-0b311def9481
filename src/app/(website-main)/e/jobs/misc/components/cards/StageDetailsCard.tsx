"use client"
import React from 'react';
import { toast } from 'react-hot-toast'
import { useRouter } from 'next/navigation'

import { Button, Popover, PopoverContent, PopoverTrigger, Switch } from '@/components/shared';
import { Elipsis } from '@/components/shared/icons';
import { useBooleanStateControl } from '@/hooks';

import { Pipeline, Stage } from '../../types';
import { DeleteIcon, EditIcon, TrashIcon } from '../../icons';
import { useDeletePipelineStage } from '../../api';
import { EditSinglePipelineStage } from '../modals';



interface ItemProps {
    stage: Stage
    pipeline: Pipeline
}

export const StageDetailsCard: React.FC<ItemProps> = ({ stage, pipeline }) => {
    const router = useRouter()

    const {
        state: isEditOneStageModalOpen,
        setTrue: openEditOneStageModal,
        setFalse: closeEditOneStageModal
    } = useBooleanStateControl()
    const { mutate: deleteSelected } = useDeletePipelineStage()
    const deleteStage = () => {
        deleteSelected({ pipelineId: pipeline.id, stageId: Number(stage.id) }, {
            onSuccess: () => {
                toast.success("deleted successfully")
                router.push('./')
            }
        })
    }

    return (
        <article className='relative flex flex-col gap-3 bg-white rounded-lg p-4'>
            <header className='flex items-center justify-between'>
                <h3 className='text-[0.875rem] text-header-text font-medium'>{stage.name}</h3>
                {
                    !pipeline.is_default &&
                    <Popover>
                        <PopoverTrigger>
                            <Elipsis fill='#D4CCF6' />
                        </PopoverTrigger>

                        <PopoverContent align='end' className='flex flex-col items-start gap-2 w-max p-1.5'>
                            <Button variant='unstyled' className='flex items-center justify-start gap-2 p-1.5 w-full hover:bg-primary-light-hover text-xs font-normal'
                                onClick={() => openEditOneStageModal()} justify='start'
                            >
                                <EditIcon />
                                Edit stage
                            </Button>
                            <Button variant='unstyled' className='flex items-center justify-start gap-2 py-1.5 pl-1 pr-5 hover:bg-primary-light-hover text-xs font-normal'
                                onClick={deleteStage}
                            >
                                <DeleteIcon />
                                Delete stage
                            </Button>
                        </PopoverContent>
                    </Popover>
                }
            </header>

            <div>
                <span className="flex items-center" >
                    <Switch checked={stage.is_assessment == true ? true : false} className='scale-[0.6]' disabled />
                    <span className="!text-[0.8rem] text-header-text ml-[-0.125rem]">Assessment Stage</span>
                </span>
            </div>

            <h5 className="!text-[0.8rem] text-header-text ml-[-0.125rem]">Move top <span className='bg-primary-light py-1 px-2 rounded-lg'>{stage.move_criteria || 0}</span> to next stage</h5>


            <EditSinglePipelineStage pipelineId={pipeline.id} isEditStageModalOpen={isEditOneStageModalOpen} stage={stage} closeStagesModal={closeEditOneStageModal} />
        </article>
    );
};