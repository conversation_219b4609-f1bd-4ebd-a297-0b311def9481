import React from 'react';
import { Draggable, DraggableProvided } from 'react-beautiful-dnd';

import { CreateStage } from '../../types';
import { TrashIcon } from '../../icons';
import { Button } from '@/components/shared';
import { cn } from '@/utils';


interface ItemProps {
    stage: CreateStage
    index: number
    activeIndex: number
    edit?: (index: number) => void
    remove?: (index: number) => void
    hasError?: boolean
}

const StageCard: React.FC<ItemProps> = ({ stage, index, activeIndex, edit, remove, hasError = false }) => {

    return (
        <Draggable draggableId={stage.name || String(index)} index={index} >
            {(provided: DraggableProvided) => (
                <article
                    className={cn('flex flex-col gap-4 bg-white p-4 rounded-xl border-[0.3px] border-[#E3E3E3] !text-[0.85rem] mb-1',
                        hasError && '!border-red-500 !border-2'
                    )}
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                >
                    <header className='flex items-center justify-between gap-4'>
                        <h3 className='font-medium text-header-text m-0 text-base'>{stage.name}</h3>

                        <div className='flex items-center gap-4'>
                            {
                                edit &&
                                <Button variant='unstyled' onClick={(e) => { 
                                    e.preventDefault()
                                    if(activeIndex === index){
                                        edit(-1);   
                                    }else{
                                        edit(index);
                                    }
                                     }} className='bg-[#F8F9FB] px-2.5 py-1.5 rounded-lg text-[0.75rem]'>
                                    { activeIndex === index ? 'Cancel' : 'Configure' }
                                </Button>
                            }
                            {
                                remove &&
                                <TrashIcon onClick={() => remove(index)} />
                            }
                        </div>
                    </header>
                </article>
            )}
        </Draggable>
    );
};

export default StageCard;
