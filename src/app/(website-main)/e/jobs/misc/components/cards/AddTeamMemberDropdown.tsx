'use client';

import { AxiosError } from 'axios';
import * as React from 'react';
import toast from 'react-hot-toast';
import {
  Button,
  Command,
  CommandEmpty,
  CommandInput,
  CommandList,
  ConfirmActionModal,
  ErrorModal,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/shared';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { useUser } from '@/lib/contexts/UserContext';
import { arrayDifference, cn } from '@/utils';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { convertToTitleCase, getInitials } from '@/utils/strings';
import { useGetTeamMembers, usePatchEditJobDetails } from '../../api';
import { TeamMember } from '../../api/getTeamMembers';

interface AddTeamMemberDropdownProps {
  closeModal?: () => void;
  job_id: number;
  previouslyAddedMembers: {
    id: number;
    company_email: string;
    verified: boolean;
    type_of_recruiter: string;
    profile_picture: string | null;
    role: string | null;
    created_at: string;
    updated_at: string;
    user: string;
  }[];
}

export function AddTeamMemberDropdown({
  closeModal,
  previouslyAddedMembers,
  job_id,
}: AddTeamMemberDropdownProps) {
  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();
  const {
    state: isConfirmationModalOpen,
    setTrue: openConfirmationModal,
    setFalse: closeConfirmationModal,
  } = useBooleanStateControl();
  const triggerRef = React.useRef<HTMLDivElement | null>(null);
  const width = `${triggerRef && triggerRef.current?.clientWidth! + 150}px`;

  const [open, setOpen] = React.useState(false);
  const [search, setSearch] = React.useState('');
  const { user } = useUser();
  const { data: teamMembers, isLoading } = useGetTeamMembers(open);

  const filteredOptions = teamMembers?.filter(option => {
    const { user, id } = option;
    const searchString = search.toLowerCase();
    const first_name = user.first_name.toLowerCase();
    const last_name = user.last_name.toLowerCase();
    const email = user.email.toLowerCase();
    const uid = String(id).toLowerCase();

    return (
      first_name.includes(searchString) ||
      last_name.includes(searchString) ||
      email.includes(searchString) ||
      uid.includes(searchString)
    );
  });
  const [selectedTeamMembers, setSelectedTeamMembers] = React.useState<
    number[]
  >(previouslyAddedMembers.map(member => member.id));
  const [clickedTeamMember, setclickedTeamMember] = React.useState<TeamMember>(
    {} as TeamMember
  );
  const handleSelect = (currentValue: number) => {
    const updatedValues = [...selectedTeamMembers];

    if (selectedTeamMembers.includes(currentValue)) {
      const index = updatedValues.indexOf(currentValue);
      updatedValues.splice(index, 1);
    } else {
      updatedValues.push(currentValue);
    }

    setSelectedTeamMembers(updatedValues);
  };

  const { mutate: editJobDetails, isLoading: isSavingEditedJob } =
    usePatchEditJobDetails();

  const addTeamMemberToJob = async () => {
    const dataToSubmit = {
      team_member: selectedTeamMembers,
    };

    editJobDetails([String(job_id), dataToSubmit], {
      onSuccess: () => {
        toast.success('Team Member updated successfully successfully');
        close();
        setOpen(false);
      },
      onError(error) {
        const errorMessage = formatAxiosErrorMessage(error as AxiosError);
        setSelectedTeamMembers(previouslyAddedMembers.map(member => member.id));
        openErrorModalWithMessage(errorMessage);
      },
    });
  };

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <Command className="h-max max-w-[300px]">
          <PopoverTrigger>
            <div
              ref={triggerRef}
              className={cn(
                'flex max-w-max cursor-pointer items-center justify-center rounded-lg bg-primary  px-[0.75rem] py-2 text-center !text-xs font-normal text-white md:max-w-max max-md:min-w-full',
                'border-[1.65px] border-primary bg-transparent text-primary hover:border-primary-light-active hover:bg-primary-light-hover'
              )}
            >
              Add team members
            </div>
          </PopoverTrigger>

          <PopoverContent
            align="center"
            className={cn(
              'max-h-96 max-w-[90vw] p-0 !pt-1  lg:max-w-[500px]',
              triggerRef?.current && `!w-[${width}]`
            )}
            style={{ width }}
          >
            <CommandInput
              isLoading={isLoading}
              placeholder={'Search or enter a new member'}
              value={search}
              onValueChange={text => setSearch(text)}
            />
            <CommandEmpty className="p-1">
              {filteredOptions && filteredOptions?.length < 1 && (
                <p className="px-2 py-3 pl-6 pt-1 text-xs">No matches found.</p>
              )}
            </CommandEmpty>

            <span className="mb-3 px-3 text-xs font-medium text-header-text ">
              {selectedTeamMembers.length} members selected
            </span>

            <CommandList className="max-h-60 overflow-y-auto">
              {filteredOptions?.map(option => {
                const { user, id } = option;
                const fullName = `${user.first_name!} ${user.last_name!}`;
                const initials = getInitials(fullName);

                return (
                  <article
                    className="relative flex cursor-pointer select-none items-center rounded-md p-1.5 text-sm outline-none hover:bg-primary-light aria-selected:bg-blue-100/70 aria-selected:text-primary"
                    onClick={() => handleSelect(option.id)}
                    key={option.id}
                  >
                    <svg
                      className={cn(
                        'mr-2 mt-2 h-5 w-5 shrink-0 self-start justify-self-start',
                        selectedTeamMembers.includes(option.id)
                          ? 'opacity-100'
                          : 'opacity-0'
                      )}
                      fill="none"
                      height={16}
                      viewBox="0 0 16 16"
                      width={16}
                      xmlns="http://www.w3.org/2000/svg"
                      aria-hidden
                    >
                      <path
                        d="m14.53 5.03-8 8a.751.751 0 0 1-1.062 0l-3.5-3.5a.751.751 0 1 1 1.063-1.062L6 11.438l7.47-7.469a.751.751 0 0 1 1.062 1.063l-.001-.002Z"
                        fill="#755AE2"
                      />
                    </svg>

                    <div className="flex items-start gap-2.5">
                      <span className="flex h-9 w-9 items-center justify-center rounded-full bg-primary text-[13px] text-white">
                        {initials}
                      </span>

                      <section className="flex flex-col">
                        <span>{convertToTitleCase(fullName! || '')}</span>
                        <div className="flex flex-col">
                          <p
                            className={cn(
                              'mr-1 text-[11px] text-[#646464] text-opacity-80'
                            )}
                          >
                            Role: {''}
                            <span className="text-header-text">
                              {convertToTitleCase(option.role_name! || '') ||
                                'No role'}
                            </span>
                          </p>
                          <p
                            className={cn(
                              'mr-1 text-[11px] text-[#646464] text-opacity-80'
                            )}
                          >
                            Email: {''}
                            <span className="text-header-text">
                              {option.user.email! || 'No email'}
                            </span>
                          </p>
                        </div>
                      </section>
                    </div>
                  </article>
                );
              })}
            </CommandList>

            <footer className="sticky bottom-0 p-2">
              <Button
                className="w-full bg-primary text-white"
                size="tiny"
                onClick={addTeamMemberToJob}
                disabled={
                  arrayDifference(
                    selectedTeamMembers,
                    previouslyAddedMembers.map(member => member.id)
                  ).length < 1
                  // ||
                  // arrayDifference(previouslyAddedMembers.map(member => member.id), selectedTeamMembers).length < 1
                }
              >
                Save changes
              </Button>
            </footer>
          </PopoverContent>
        </Command>
      </Popover>

      <ConfirmActionModal
        isModalOpen={isConfirmationModalOpen}
        closeModal={closeConfirmationModal}
        title="Move candidates to group"
        confirmFunction={addTeamMemberToJob}
        // icon={<Rotate width={44} height={44} fill='white' />}
      >
        <p className="text-sm font-normal text-[#8C8CA1]">
          You are about to move{' '}
          <span className="mr-1 font-bold text-header-text"></span> candidates
          to the group <span className="mr-1 font-bold text-header-text"></span>
          ., Please be aware that it can take up to 5 minutes to reflect the new
          score. It is also possible that the score doesn&apos;t change if the
          grading criterias like{' '}
          <span className="mr-1 font-medium text-header-text">
            compulsory requirements, job responsibilities etc..{' '}
          </span>{' '}
          remain the same.
        </p>
      </ConfirmActionModal>
      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 hover:border-red-950 hover:text-red-950 sm:text-sm md:px-6"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>

      {/* <LoadingOverlay isOpen={isMovingCandidates} /> */}
    </>
  );
}
