import React from 'react';

import { Button, ConfirmDeleteModal, LoadingOverlay, Popover, PopoverContent, PopoverTrigger } from '@/components/shared';
import { DoubleForward, Elipsis } from '@/components/shared/icons';
import { useBooleanStateControl } from '@/hooks';


import { Pipeline } from '../../types';
import { DeleteIcon, EditIcon, TrashIcon } from '../../icons';
import EditPipeline from '../modals/EditPipeline';
import { useDeletePipeline } from '../../api';
import { cn } from '@/utils';

interface ItemProps {
    index: number
    pipeline: Pipeline
    currentDetailsIndex: number
    setcurrentDetails: (pipeline: Pipeline) => void
    setcurrentDetailsIndex: (index: number) => void
    defaultPipeline: Pipeline
}

const PipelineCard: React.FC<ItemProps> = ({ index, currentDetailsIndex, pipeline, setcurrentDetails, setcurrentDetailsIndex, defaultPipeline }) => {
    const { name, stages, id } = pipeline
    const {
        state: isEditStagesModalOpen,
        setTrue: openEditStagesModal,
        setFalse: closeEditStagesModal
    } = useBooleanStateControl()
    const {
        state: isConfirmDeletePipelineModalOpen,
        setTrue: openConfirmDeleteCandidateModal,
        setFalse: closeConfirmDeleteModal,
    } = useBooleanStateControl(false)

    const { mutate: deletePipeline, isLoading: isDeleting } = useDeletePipeline()
    const Delete = () => {
        deletePipeline(id, {
            onSuccess(data, variables, context) {
                setcurrentDetailsIndex(index - 1)
            },
        })
    }

    return (

        <article className={cn('p-4 border-[1.5px] border-[#E4E4E4]  rounded-lg max-w-[500px] transition-all duration-300', (index === currentDetailsIndex) && "!border-primary !border-2")} onClick={() => { setcurrentDetails(pipeline); setcurrentDetailsIndex(index) }}>
            <header className='flex items-center justify-between'>
                <h3 className='text-[0.875rem] text-header-text font-medium'>{name}</h3>
                <Popover>
                    <PopoverTrigger>
                        <Elipsis fill='#D4CCF6' />
                    </PopoverTrigger>

                    <PopoverContent align='end' className='flex flex-col items-start gap-2 w-max p-1.5 text-primary'>
                        <Button variant='unstyled' className='flex items-center justify-start gap-2 p-1.5 w-full hover:bg-primary-light-hover text-xs font-normal'
                            onClick={() => openEditStagesModal()} justify='start'
                        >
                            <EditIcon />
                            Edit pipeline
                        </Button>
                        <Button variant='unstyled' className='flex items-center justify-start gap-2 py-1.5 pl-1 pr-5 hover:bg-primary-light-hover text-xs font-normal'
                            onClick={openConfirmDeleteCandidateModal}
                        >
                            <DeleteIcon />
                            Delete pipeline
                        </Button>
                    </PopoverContent>
                </Popover>
            </header>

            <footer className='flex items-center justify-between mt-8'>
                <p className='text-[#7D8590] text-[0.75rem]'>Pipeline stages: <span className='text-[#344051]'>{stages.length}</span></p>
                <Button variant='extralight' size='thin' className='text-[0.75rem]' onClick={() => setcurrentDetails(pipeline)}>
                    View details <DoubleForward fill='#755AE2' />
                </Button>
            </footer>

            <LoadingOverlay isOpen={isDeleting} />
            <EditPipeline
                isStagesModalOpen={isEditStagesModalOpen}
                pipeline={pipeline}
                closeStagesModal={closeEditStagesModal}
            />
            <ConfirmDeleteModal
                isModalOpen={isConfirmDeletePipelineModalOpen}
                closeModal={closeConfirmDeleteModal}
                title="Delete Pipeline"
                deleteFunction={Delete}
            >
                <div className='text-[#8C8CA1] text-sm font-normal'>
                    <p>
                        You are about to delete pipeline - <span className='text-header-text font-bold mx-1'>{name}</span>,
                        Please be aware that this is an irreversible action. Once you delete this pipeline, it <span className='text-header-text font-bold mx-1'>cannot be recovered.</span>
                        Furthermore, every job associated with this pipeline will lose all its details, progress and applicants.
                    </p>
                </div>
            </ConfirmDeleteModal>
        </article>
    );
};

export default PipelineCard;