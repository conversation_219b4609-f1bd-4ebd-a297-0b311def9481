import { Skeleton } from '@/components/shared'
import { setAccessToken } from '@/utils'
import React from 'react'
// import Skeleton from 'react-loading-skeleton'

interface props {
    match: number
}
const JobMatchLoader = ({ match }: props) => {

    return (
        <>
            {Array(match)?.fill(8)?.map((_, idx: number) => (
                <div className=' p-4 sm:p-6 space-y-8 bg-white rounded-[1.25rem]' key={idx}>
                    <header className="flex justify-between items-center">
                        <section className='flex items-center gap-4 '>
                            <div className='flex items-center gap-4 '>
                                <Skeleton className="h-7 w-7 rounded-md" />
                                <Skeleton className='h-3 px-20 rounded-[0.5rem] mr-12' />

                                <Skeleton className="max-md:hidden h-5 w-16 rounded-md" />


                                <div className=' max-md:hidden flex items-center gap-4'>
                                    <Skeleton className='h-2 px-8 rounded-[0.5rem]' />
                                    <Skeleton className='h-2 px-8 rounded-[0.5rem]' />
                                </div>
                            </div>
                        </section>

                        {/* Popover Menu */}
                        <Skeleton className="h-6 w-6 rounded-md" />
                    </header>



                    <div className='max-sm:grid max-sm:grid-cols-2 max-sm:gap-2.5 flex items-center gap-5 '>
                        <div className="flex items-center ">
                            <Skeleton className="h-5 md:h-7 w-5 md:w-7 rounded-full" />
                            <Skeleton className="h-5 md:h-7 w-5 md:w-7 rounded-full !translate-x-[-0.5rem]" />
                            <Skeleton className="h-5 md:h-7 w-5 md:w-7 rounded-full !translate-x-[-1rem]" />
                            <Skeleton className='h-2 px-8 md:px-12 rounded-[0.5rem]' />
                        </div>

                        <div className='flex items-center gap-2'>
                            <Skeleton className="h-5 w-5 rounded-md" />
                            <Skeleton className='h-2 px-8 rounded-[0.5rem]' />
                        </div>
                        <div className='flex items-center gap-2'>
                            <Skeleton className="h-5 w-5 rounded-md" />
                            <Skeleton className='h-2 px-8 rounded-[0.5rem]' />
                        </div>
                        <div className='flex items-center gap-2'>
                            <Skeleton className="h-5 w-5 rounded-md" />
                            <Skeleton className='h-2 px-8 rounded-[0.5rem]' />
                        </div>
                    </div>



                    <footer className='flex max-sm:flex-col sm:items-center gap-4'>
                        <Skeleton className='h-8 px-16 rounded-md' />
                        <Skeleton className='h-8 px-16 rounded-md' />
                        <Skeleton className='h-8 px-16 rounded-md sm:ml-auto' />
                    </footer>


                </div>

            ))}

        </>
    )
}

export default JobMatchLoader