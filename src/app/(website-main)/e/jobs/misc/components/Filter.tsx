import type React from "react"
import { type Dispatch, type SetStateAction, useEffect, useState } from "react"
import { Controller, useForm } from "react-hook-form"
import { Button, Checkbox3, Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/shared"
import { CaretDown } from "@/components/shared/icons"

interface FilterProps {
    filterUrl: string
    setFilterUrl: Dispatch<SetStateAction<string>>
    draft?: boolean
}

const Filter: React.FC<FilterProps> = ({ filterUrl, setFilterUrl, draft }) => {
    const urlSearchParams = new URLSearchParams(filterUrl)
    const { handleSubmit, register, setValue, watch, control, reset } = useForm({
        defaultValues: {
            job_type: {
                FULL_TIME: urlSearchParams.has("job_type") && urlSearchParams.get("job_type") === "FULL_TIME",
                PART_TIME: urlSearchParams.has("job_type") && urlSearchParams.get("job_type") === "PART_TIME",
                INTERNSHIP: urlSearchParams.has("job_type") && urlSearchParams.get("job_type") === "INTERNSHIP",
                CONTRACT: urlSearchParams.has("job_type") && urlSearchParams.get("job_type") === "CONTRACT",
                VOLUNTEER: urlSearchParams.has("job_type") && urlSearchParams.get("job_type") === "VOLUNTEER",
            },
            job_status: {
                OPEN: urlSearchParams.has("job_status") && urlSearchParams.get("job_status") === "OPEN",
                CLOSED: urlSearchParams.has("job_status") && urlSearchParams.get("job_status") === "CLOSED",
                DRAFT: draft ? true : false,
                QUEUED: urlSearchParams.has("job_status") && urlSearchParams.get("job_status") === "QUEUED",
            },
        },
    })

    const submitHandler = (data: Record<string, any>) => {
        let filterString = ""

        Object.keys(data).forEach((category) => {
            const selectedCheckbox3es = Object.keys(data[category]).filter((Checkbox3) => data[category][Checkbox3])

            if (selectedCheckbox3es.length > 0) {
                const categoryFilter = selectedCheckbox3es.map((Checkbox3) => `&${category}=${Checkbox3}`).join("")
                filterString += categoryFilter
            }
        })

        setFilterUrl(filterString)
    }

    function countSelectedKeys<T>(obj: Record<string, T>): number {
        let count = 0

        for (const key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
                if (obj[key] === true) {
                    count++
                }
            }
        }

        return count
    }

    const [selectedFiltersCount, setSelectedFiltersCount] = useState({ job_type: 0, job_status: 0 })
    const values = watch()
    console.log(values)

    useEffect(() => {
        console.log(values.job_type)
        const jobTypeCount = countSelectedKeys(values.job_type)
        const jobStatusCount = countSelectedKeys(values.job_status)

        setSelectedFiltersCount({
            job_type: jobTypeCount,
            job_status: jobStatusCount,
        })
    }, [watch("job_type"), watch("job_status")])

    const getIsApplyButtonDisabled = () => {
        const jobTypeValues = Object.values(watch("job_type"))
        const jobStatusValues = Object.values(watch("job_status"))

        if (draft) {
            return jobTypeValues.every((val) => !val)
        }

        return jobTypeValues.every((val) => !val) && jobStatusValues.every((val) => !val)
    }

    return (
        <form onSubmit={handleSubmit(submitHandler)}>
            <Collapsible className="group/collapsible1">
                <CollapsibleTrigger className="flex items-center w-full gap-4 rounded-lg py-3 text-left text-sm font-medium text-header-text">
                    <h3 className="font-semibold text-base">Employment Type</h3>
                    {selectedFiltersCount.job_type > 0 && (
                        <span className="flex items-center justify-center h-4 w-4 bg-primary text-[0.75rem] text-white rounded-full">
                            {selectedFiltersCount.job_type}
                        </span>
                    )}
                    <CaretDown className='group-data-[state="open"]/collapsible1:rotate-180 ml-auto' height={20} />
                </CollapsibleTrigger>
                <CollapsibleContent className="grid grid-cols-2 items-start justify-between gap-4">
                    <Controller
                        control={control}
                        name={`job_type.FULL_TIME`}
                        render={({ field }) => (
                            <Checkbox3
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                label="Full Time"
                                labelClass="text-primary text-[0.9rem]"
                                className="justify-self-start"
                            />
                        )}
                    />
                    <Controller
                        control={control}
                        name={`job_type.PART_TIME`}
                        render={({ field }) => (
                            <Checkbox3
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                label="Part Time"
                                labelClass="text-primary text-[0.9rem]"
                                className="justify-self-start"
                            />
                        )}
                    />
                    <Controller
                        control={control}
                        name={`job_type.CONTRACT`}
                        render={({ field }) => (
                            <Checkbox3
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                label="Contract"
                                labelClass="text-primary text-[0.9rem]"
                                className="justify-self-start"
                            />
                        )}
                    />
                    <Controller
                        control={control}
                        name={`job_type.VOLUNTEER`}
                        render={({ field }) => (
                            <Checkbox3
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                label="Volunteer"
                                labelClass="text-primary text-[0.9rem]"
                                className="justify-self-start"
                            />
                        )}
                    />
                </CollapsibleContent>
            </Collapsible>

            {!draft && (
                <Collapsible className="group/collapsible2">
                    <CollapsibleTrigger className="flex items-center w-full gap-4 rounded-lg py-3 text-left text-sm font-medium text-header-text">
                        <h3 className="font-semibold text-base pt-4">Job Status</h3>
                        {selectedFiltersCount.job_status > 0 && (
                            <span className="flex items-center justify-center h-4 w-4 bg-primary text-[0.75rem] text-white rounded-full">
                                {selectedFiltersCount.job_status}
                            </span>
                        )}
                        <CaretDown className='group-data-[state="open"]/collapsible2:rotate-180 ml-auto' height={20} />
                    </CollapsibleTrigger>
                    <CollapsibleContent className="grid grid-cols-2 justify-between gap-4">
                        <Controller
                            control={control}
                            name={`job_status.OPEN`}
                            render={({ field }) => (
                                <Checkbox3
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                    label="Open"
                                    labelClass="text-primary text-[0.9rem]"
                                    className="justify-self-start"
                                />
                            )}
                        />
                        <Controller
                            control={control}
                            name={`job_status.QUEUED`}
                            render={({ field }) => (
                                <Checkbox3
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                    label="Queued"
                                    labelClass="text-primary text-[0.9rem]"
                                    className="justify-self-start"
                                />
                            )}
                        />
                        <Controller
                            control={control}
                            name={`job_status.CLOSED`}
                            render={({ field }) => (
                                <Checkbox3
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                    label="Closed"
                                    labelClass="text-primary text-[0.9rem]"
                                    className="justify-self-start"
                                />
                            )}
                        />
                        <Controller
                            control={control}
                            name={`job_status.DRAFT`}
                            render={({ field }) => (
                                <Checkbox3
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                    label="Draft"
                                    labelClass="text-primary text-[0.9rem]"
                                    className="justify-self-start"
                                />
                            )}
                        />
                    </CollapsibleContent>
                </Collapsible>
            )}

            <div className="flex items-center justify-between mt-[1.25rem]">
                <Button
                    type="submit"
                    size="thin"
                    disabled={getIsApplyButtonDisabled()}
                    className={getIsApplyButtonDisabled() ? "opacity-50 cursor-not-allowed" : ""}
                >
                    Apply
                </Button>
                <Button type="submit" variant="light" size="thin" onClick={() => reset()}>
                    Reset
                </Button>
            </div>
        </form>
    )
}

export default Filter

