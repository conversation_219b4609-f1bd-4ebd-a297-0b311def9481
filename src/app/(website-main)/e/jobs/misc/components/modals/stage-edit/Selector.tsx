import { useState } from "react";
import useGetAssessments from "../../../api/getAssessments";
import { PopoverSelect } from "./PopoverSelector";
import { useGetInterviewAssessments } from "../../../api";
 
// Assessment Selector Component
export const AssessmentSelector: React.FC<{
  onChange: (value: string) => void;
  value: string | undefined;
  error?: string;
}> = ({ onChange, value, error }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading } =
    useGetAssessments(true);

  // Flatten the pages of assessments into a single array
  const assessments =
    data?.pages?.flatMap(page =>
      page.results.map(assessment => ({
        name: assessment.name,
        value: assessment.id,
      }))
    ) || [];

  return (
    <PopoverSelect
      options={assessments}
      value={value}
      onChange={onChange}
      placeholder="Select assessment"
      label="Assessment"
      error={error}
      isLoading={isLoading || isFetchingNextPage}
      onLoadMore={() => hasNextPage && fetchNextPage()}
      hasMore={Boolean(hasNextPage)}
      searchable={true}
    />
  );
};

// Interview Selector Component
export const InterviewSelector = ({
  onChange,
  value,
  error,
 }: {
  onChange: (value: string) => void;
  value: string | undefined;
  error?: string;
  }) => {

  const { data: interviews, isLoading } =
    useGetInterviewAssessments(true);

  const interviewOptions =
    interviews?.map((interview:any) => ({
      name: interview.name,
      value: interview.id,
    })) || [];

  return (
    <PopoverSelect
      options={interviewOptions}
      value={value}
      onChange={onChange}
      placeholder="Select interview"
      label="Interview"
      error={error}
      isLoading={isLoading}
      onLoadMore={() => { }} // No pagination for interviews
      hasMore={false}
      searchable={true}
    />
  );
};
