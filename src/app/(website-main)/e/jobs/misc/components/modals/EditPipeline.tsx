
import React, { useEffect, useMemo } from 'react'
import { <PERSON>, FormProvider, useFieldArray, useForm, useFormContext } from 'react-hook-form'
import { DragDropContext, DropResult, Droppable } from 'react-beautiful-dnd'
import dynamic from 'next/dynamic'
import "react-quill/dist/quill.snow.css";

import { Button, ErrorModal, LoadingOverlay, Modal, RadioGroup, Select, ToolTip } from '@/components/shared'
import { Info, Plus } from '@/components/shared/icons'
import { convertToTitleCase } from '@/utils/strings'
import { cn } from '@/utils'
import { useErrorModalState } from '@/hooks';

import { usePutEditStages } from '../../api'
import { CreateStage, Pipeline, Stage } from '../../types'
import useGetAssessments, { Assessment } from '../../api/getAssessments'
import { AddHeaderToEmailBody } from '../../utils/AddHeaderToEmailBody'
import StageCard from '../cards/StageCard';
import { editPipelineStagesSchema } from '../../utils/pipelineSchemas';
import { zodResolver } from '@hookform/resolvers/zod';
import StageForm from './EditPipelineSingleStageForm';
import EditAllPipelineStages from './EditAllPipelineStages';
import { EditPipelineStage } from './stage-edit/EditePipelineStage';


interface StagesProps {
    pipeline: Pipeline
    isStagesModalOpen: boolean
    closeStagesModal: () => void
}
interface CustomFormErrors {
    stages: Array<{ [key: string]: { message?: string } }>;
}




const EditPipeline: React.FC<StagesProps> = ({ pipeline, isStagesModalOpen, closeStagesModal }) => {
    const convertedPipeline: Pipeline = useMemo(() => {
        const stages = pipeline.stages.map((stage: Stage) => {
            return {
                ...stage,
                is_interview: Boolean(stage.is_interview) || false,
                is_assessment: Boolean(stage.is_assessment) || false,
                automation_enabled: Boolean(stage.automation_enabled) || false,
                email_notification_enabled: Boolean(stage.email_notification_enabled) || false,
                assessment: stage.assessment || undefined,
                email_subject: stage.email_subject || undefined,
                email_body: stage.email_body || undefined,
                email_text: stage.email_text || undefined,
                duration: Number(stage.duration) || 0,
            } as Stage
        })
        return { ...pipeline, stages }
    }, [pipeline]);
    const { stages, id, name } = convertedPipeline

    useEffect(() => {
        reset({
            stages: [...convertedPipeline.stages].sort((a, b) => (a.order || 0) - (b.order || 0)),
            currentEdit: -1,
        });
    }, [pipeline]);



    const methods = useForm({
        defaultValues: {
            stages: convertedPipeline.stages,
            currentEdit: -1,
        },
        resolver: zodResolver(editPipelineStagesSchema),
        // mode: 'onBlur',
    });
    const { register, formState: { errors }, getValues, trigger, clearErrors, handleSubmit, setValue, setError, reset, watch, control } = methods
    // const customErrors = errors as CustomFormErrors;
    const { append, remove } = useFieldArray({
        name: 'stages',
        control,
    });
    const currentEdit = watch("currentEdit")
    const theStages = watch('stages')

    const onDragEnd = ({ source, destination, draggableId }: DropResult) => {
        if (!destination) return;

        const updatedFields = [...theStages];
        const [movedItem] = updatedFields.splice(source.index, 1);
        updatedFields.splice(destination.index, 0, movedItem);
        setValue('stages', updatedFields);
    };

    const createNewStage = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
        e.preventDefault()
        append({
            id: Date.now(), // Using timestamp as a temporary ID
            name: "Newest Stage",
            is_new_candidate: false,
            is_interview: false,
            is_assessment: false,
            is_offer_stage: false,
            offer_letter_send_automatic: false,
            offer_letter_manual_review: false,
            offer_setup_customization: null,
            offer_email_subject: null,
            offer_email_body: null,
            offer_cover_image: null,
            offer_attachment_url: null,
            enable_automated_reminders: false,
            enable_onboarding_setup: false,
            customize_rejection_message: false,
            assessment: null,
            interview: null,
            duration: 0,
            order: theStages.length + 1,
            move_criteria: 0,
            automation_enabled: false,
            email_notification_enabled: false,
            email_subject: "",
            email_body: "",
            email_text: "",
            applicant_count: null,
            reminders: null,
            rejection_setup_customization: null
        })
        setValue('currentEdit', watch('stages').length);

    };
    const editStage = (index: number) => {
        // console.log(currentEdit)
        setValue('currentEdit', index);
    };



    const { mutate: editStages, isLoading: areStagesEditing } = usePutEditStages();
    const submitForm = async (data: { stages: Stage[], currentEdit: number }) => {
        if (theStages.length > 0) {
            try {

                const stagesToSubmit = theStages.map((stage, index) => {
                    return { ...stage, order: index, email_body: stage.email_text ? AddHeaderToEmailBody(stage.email_text, stage.is_interview) : null } as CreateStage
                });

                editStages(
                    { id: id, stages: stagesToSubmit },
                    {
                        onSuccess: (data) => {
                            reset({ currentEdit: -1, stages: stages });
                            closeStagesModal();
                        }
                    }
                );

            } catch (error) {
                console.error('Validation error:', error);
            }
        }
    };

    const handleUpdateStage = (index: number, stage: Stage) => {

        const updatedStages = [...watch('stages')];
        updatedStages[index] = stage;
        setValue('stages', updatedStages);

    }






    return (
        <>
            <Modal heading='Edit Pipeline' isModalOpen={isStagesModalOpen} allowDismiss closeModal={() => { closeStagesModal(); reset({ stages: stages }) }} color='white'
                className='!wax-w-[700px]'
                body={
                    <form onSubmit={handleSubmit(submitForm)} id={`pipeline-stages-edit-form-${pipeline.id}`}>
                        <header>
                            <h3 className='font-medium text-header-text m-0 text-base'>{convertToTitleCase(name)}</h3>
                            <p className='text-body-text text-[0.875rem]'>
                                Create, reorder, edit, remove, and configure your pipeline stages accordingto your desired behaviour. Any adjustments made will be applied to all pipelines to which it is connected.
                            </p>
                        </header>


                        <DragDropContext onDragEnd={onDragEnd}>
                            <div className="h-full overflow-y-hidden w-full">
                                <Droppable droppableId="Pipeline">
                                    {(provided) => (
                                        <div className="flex flex-col gap-6 mt-0 py-4 px-2 h-full overflow-hidden">
                                            <div {...provided.droppableProps} ref={provided.innerRef} className="flex flex-col gap-3.5">

                                                {
                                                    theStages?.map((item, index) => {

                                                        return (
                                                            <>
                                                                <div
                                                                    className={cn(
                                                                        currentEdit === index && 'bg-[#F5F3FF] rounded-lg p-4 my-0 border-transparent border-2',
                                                                        errors.stages && errors.stages[index] && 'border-red-500'
                                                                    )}
                                                                    key={index}
                                                                >
                                                                    <StageCard
                                                                        stage={item as CreateStage}
                                                                        index={index}
                                                                        activeIndex={currentEdit}
                                                                        key={item.name}
                                                                        edit={editStage}
                                                                        remove={() => {
                                                                            remove(index);
                                                                            setValue('currentEdit', -1);
                                                                        }}
                                                                        hasError={(errors.stages && errors.stages[index]) ? true : false}
                                                                    />


                                                                    {
                                                                        currentEdit === index &&

                                                                        <div className="bg-white p-2 px-4 rounded-2xl ">
                                                                            {errors?.stages && errors?.stages[index] && errors?.stages[index] && <>
                                                                                {Object.entries(errors.stages[index] || {}).map(([field, value]) => {
                                                                                    if (
                                                                                        typeof value === 'object' &&
                                                                                        value !== null &&
                                                                                        'message' in value
                                                                                    ) {
                                                                                        const error = {
                                                                                            field,
                                                                                            ...(value as { message: string; type?: string }),
                                                                                        };

                                                                                        return (
                                                                                            <div
                                                                                                key={field}
                                                                                                className="text-[0.7rem] text-red-500 rounded-sm w-full p-2 px-3 bg-red-50 mb-2"
                                                                                            >
                                                                                                {`${error.field}: ${error.message}`}
                                                                                            </div>
                                                                                        );
                                                                                    }

                                                                                    return null;
                                                                                })}
                                                                            </>}

                                                                            
                                                                            <EditPipelineStage
                                                                                stage={item as Stage}
                                                                                isSubmitting={false}
                                                                                submitForm={(form) => handleUpdateStage(index, form)}
                                                                                save="Save"
                                                                                closeOnSave={true}
                                                                            />
                                                                        </div>
                                                                    }


                                                                    {/* {
                                                                        currentEdit === index &&
                                                                        <>
                                                                            <EditAllPipelineStages
                                                                                index={index}
                                                                                control={control}
                                                                                errors={errors}
                                                                                register={register}
                                                                                setValue={setValue}
                                                                                watch={watch}
                                                                                clearErrors={clearErrors}
                                                                                isAssessment={isAssessment || false}
                                                                                isInterview={isAssessment || false}
                                                                                isEmailEnabled={isEmailEnabled || false}
                                                                                emailBody={emailBody || ""}
                                                                                currentEdit={currentEdit}
                                                                                isStagesModalOpen={isStagesModalOpen}
                                                                            />

                                                                        </>
                                                                    } */}
                                                                </div>
                                                            </>
                                                        )
                                                    })
                                                }

                                                {provided.placeholder}
                                            </div>
                                        </div>
                                    )}
                                </Droppable>
                            </div>
                        </DragDropContext>


                        {
                            errors.stages && theStages.length < 1 &&
                            <span className='text-red-500 text-xs text-center'>Your pipeline must contain at least one stage</span>
                        }

                        <Button onClick={(e) => createNewStage(e)} size='thin' variant='extralight' className='w-full' icon={< Plus />}>
                            Add new stage
                        </Button >
                    </form>
                }



                footer={
                    <div className='flex p-6 ml-auto' >
                        <Button
                            className='px-12 py-2 md:py-2.5 text-[0.875rem]'
                            type='submit'
                            form={`pipeline-stages-edit-form-${pipeline.id}`}
                        >
                            Save
                        </Button>
                    </div >
                }
            />

            < LoadingOverlay isOpen={areStagesEditing} />
        </>
    )
}

export default EditPipeline