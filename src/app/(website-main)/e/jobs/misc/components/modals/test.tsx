import { RadioGroup } from "@/components/shared";

<DragDropContext onDragEnd={onDragEnd} >
<div className='h-full overflow-y-hidden w-full'>
    <Droppable droppableId="Pipeline">
        {(provided) => (
            <div className='flex flex-col gap-6 mt-0 py-4 px-2 h-full overflow-hidden'>
                <h2 className='sticky top-0 flex items-center gap-4 px-5 py-0.5 bg-white rounded-full text-primary text-[0.75rem] font-semibold max-w-max'></h2>
                <div {...provided.droppableProps} ref={provided.innerRef} className='flex flex-col gap-3.5'>
                    {
                        theStages?.map((item, index) => {
                            const isAssessment = watch(`stages.${index}.is_assessment`) === 'true';
                            const isEmailEnabled = watch(`stages.${index}.email_notification_enabled`) === 'true';
                            const emailBody = watch(`stages.${index}.email_text`);

                            return (
                                <div
                                    className={cn(
                                        currentEdit === index && 'bg-[#F5F3FF] rounded-lg p-4 my-0 border-transparent border-2',
                                        errors.stages && errors.stages[index] && 'border-red-500'
                                    )}
                                    key={index}
                                >
                                    <StageCard
                                        stage={item}
                                        index={index}
                                        key={item.name}
                                        edit={editStage}
                                        remove={() => {
                                            remove(index);
                                            setValue('currentEdit', -1);
                                        }}
                                        hasError={(errors.stages && errors.stages[index]) ? true : false}
                                    />
                                    {currentEdit === index &&
                                        <div>
                                            <div className="rounded-lg pt-4 px-2 my-0">
                                                <input
                                                    type="text"
                                                    placeholder="Enter stage name"
                                                    className="text-[0.75rem] text-body-text px-3.5 py-2 sm:px-4 sm:py-2.5 w-full border-2 border-transparent rounded-lg focus:border-primary focus:outline-none bg-white transition-all"
                                                    {...register(`stages.${index}.name`)}
                                                />
                                                {errors.stages && errors.stages[index]?.name && (
                                                    <span className="text-red-500 text-xs">{errors.stages[index]?.name?.message}</span>
                                                )}

                                            </div>
                                            <div className='mt-8'>
                                                <header>
                                                    <h6 className='flex items-center gap-1 text-sm text-header-text font-medium'>
                                                        Configure stage
                                                        <ToolTip content='More Info Coming Soon...'>
                                                            <Info />
                                                        </ToolTip>
                                                    </h6>
                                                    <p className='text-body-text text-[0.75rem]'>
                                                        Here you can define the criteria for candidate progression between stages
                                                        and specify the automated actions you want the system to execute
                                                    </p>
                                                </header>


                                                <section className='my-6 ml-4'>
                                                    <h6 className='flex items-center gap-1 text-[0.8rem] text-header-text font-medium'>
                                                        Assessments and Interviews
                                                        <ToolTip content='More Info Coming Soon...'>
                                                            <Info />
                                                        </ToolTip>
                                                    </h6>

                                                    <RadioGroup
                                                        name={`stages.${index}.is_interview`}
                                                        options={yes_or_no}
                                                        onChange={(value) => setValue(`stages.${index}.is_interview`, value)}
                                                        label='Is this an interview stage?'
                                                        radioClass='h-3 !w-3'
                                                        labelClass='grow text-body-text !text-xs !font-normal'
                                                        containerClass='!grid grid-cols-[max-content,max-content] items-center gap-4 my-1.5'
                                                        value={watch(`stages.${index}.is_interview`)}
                                                        variant="unstyled"
                                                        size='tiny'
                                                        className='p-1'
                                                        itemClass='!text-xs'
                                                        arrangement='row'
                                                        errors={errors}
                                                    />
                                                    <RadioGroup
                                                        name={`stages.${index}.is_assessment`}
                                                        options={yes_or_no}
                                                        value={watch(`stages.${index}.is_assessment`)}
                                                        onChange={(value) => setValue(`stages.${index}.is_assessment`, value)}
                                                        variant="unstyled"
                                                        size='tiny'
                                                        label='Will this stage require an assessment?'
                                                        radioClass='h-3 !w-3'
                                                        labelClass='grow text-body-text !text-xs !font-normal'
                                                        containerClass='!grid grid-cols-[max-content,max-content] items-center gap-4 my-1.5'
                                                        className='p-1'
                                                        itemClass='!text-xs'
                                                        arrangement='row'
                                                        errors={errors}
                                                    />


                                                    {isAssessment &&
                                                        <>
                                                            <Select
                                                                name="assessment"
                                                                label="Assessment"
                                                                value={String(watch(`stages.${index}.assessment`))}
                                                                onChange={(value) => { setValue(`stages.${index}.assessment`, value); clearErrors(`stages.${index}.assessment_duration`) }}
                                                                options={custom_assessments}
                                                                placeholder="Select from created assessments"
                                                                errors={customErrors.stages && customErrors.stages![index]}
                                                                isLoadingOptions={isFetchingAssessments}
                                                                className='shrink !px-1.5 !py-0.5 bg-white !text-xxs'
                                                                containerClass='!grid grid-cols-[max-content,max-content] items-center gap-4 my-1.5'
                                                                labelClass='grow text-body-text !text-xs font-normal'
                                                                itemClass='!text-xs text-body-text'
                                                            />

                                                            <div className='my-[0.875rem]'>

                                                                <div className='flex flex-wrap items-center gap-2 my-3 text-body-text text-xs'>
                                                                    <span>Candidates have  </span>
                                                                    <input type="number" placeholder="0"
                                                                        className='appearance-none text-xs text-[#818181] p-1 w-max max-w-[50px] border-2 border-transparent rounded-lg focus:border-primary focus:outline-none bg-white transition-all '
                                                                        {...register(`stages.${index}.assessment_duration` as const, { required: true })}
                                                                        onChange={(e) => setValue(`stages.${index}.assessment_duration`, Number(e.target.value))}
                                                                        value={watch(`stages.${index}.assessment_duration`, 0)}
                                                                    />
                                                                    <span>day(s) to take assessment.</span>
                                                                    {errors.stages && errors.stages[index]?.assessment_duration && <span className='text-[0.8rem] text-red-500'>{errors.stages[index]?.assessment_duration?.message || "This field is required"}</span>}
                                                                </div>
                                                            </div>
                                                        </>
                                                    }
                                                </section>


                                                <section className='my-6 ml-4'>
                                                    <h6 className='flex items-center gap-1 text-[0.8rem] text-header-text font-medium'>
                                                        Automations
                                                        <ToolTip content='More Info Coming Soon...'>
                                                            <Info />
                                                        </ToolTip>
                                                    </h6>

                                                    <RadioGroup
                                                        name={`stages.${index}.automation_enabled`}
                                                        options={yes_or_no}
                                                        onChange={(value) => setValue(`stages.${index}.automation_enabled`, value)}
                                                        label={
                                                            <p>
                                                                Enable staging automation
                                                                <ToolTip content="">
                                                                    <Info />
                                                                </ToolTip>
                                                            </p>
                                                        }
                                                        radioClass='h-3 !w-3'
                                                        labelClass='grow !text-body-text !text-xs !font-normal'
                                                        containerClass='!grid grid-cols-[max-content,max-content] items-center gap-4 my-1.5'
                                                        value={watch(`stages.${index}.automation_enabled`)}
                                                        variant="unstyled"
                                                        size='tiny'
                                                        className='p-1'
                                                        itemClass='!text-xs'
                                                        arrangement='row'
                                                        errors={errors}
                                                    />

                                                    {errors.stages && errors.stages[index]?.automation_enabled && <span className='-mt-4 text-[0.75rem] text-red-500'>{errors.stages[index]?.automation_enabled?.message || "This field is required"}</span>}

                                                    <div className='my-1.5'>
                                                        <div className='flex flex-wrap items-center gap-2  text-body-text text-xs'>
                                                            <span>Move candidates with  </span>
                                                            <input type="number" placeholder="0"
                                                                className='appearance-none text-xs text-body-text p-1 w-max max-w-[50px] border-2 border-transparent rounded-lg focus:border-primary focus:outline-none bg-white transition-all '
                                                                {...register(`stages.${index}.move_criteria` as const, { required: true })}
                                                                onChange={(e) => setValue(`stages.${index}.move_criteria`, Number(e.target.value))}
                                                                value={watch(`stages.${index}.move_criteria`, 50)}
                                                            />
                                                            <span>% match to the next stage</span>
                                                        </div>

                                                        {errors.stages && errors.stages[index]?.move_criteria && <span className='text-[0.8rem] text-red-500'>{errors.stages[index]?.move_criteria?.message || "This field is required"}</span>}

                                                    </div>
                                                </section>


                                                <section className='my-6 ml-4'>
                                                    <h6 className='flex items-center gap-1 text-[0.8rem] text-header-text font-medium'>
                                                        Notifications
                                                        <ToolTip content='More Info Coming Soon...'>
                                                            <Info />
                                                        </ToolTip>
                                                    </h6>
                                                    <RadioGroup
                                                        name={`stages.${index}.email_notification_enabled`}
                                                        options={
                                                            [
                                                                { name: 'Yes', value: 'true' },
                                                                { name: 'No', value: 'false' },
                                                            ]
                                                        }
                                                        onChange={(value) => setValue(`stages.${index}.email_notification_enabled`, value)}
                                                        label={
                                                            <span className='flex items-center gap-1'>
                                                                Will this stage require an email to be sent?
                                                                <ToolTip content='More Info Coming Soon...'>
                                                                    <Info />
                                                                </ToolTip>
                                                            </span>
                                                        }
                                                        radioClass='h-3 !w-3'
                                                        labelClass='grow text-body-text !text-xs !font-normal'
                                                        containerClass='!grid grid-cols-[max-content,max-content] items-center gap-4 my-1.5'
                                                        value={watch(`stages.${index}.email_notification_enabled`)}
                                                        variant="unstyled"
                                                        size='tiny'
                                                        className='p-1'
                                                        itemClass='!text-xs'
                                                        arrangement='row'
                                                        errors={errors}
                                                    />

                                                    {
                                                        isEmailEnabled &&
                                                        <>


                                                            <div className={cn('inputdiv mt-[0.875rem] mb-[0.25rem] border-[1.75px] p-2 border-helper-text rounded-xl',
                                                                errors?.stages && (errors?.stages[index]?.email_text || errors?.stages[index]?.email_subject) && "border-red-500"
                                                            )}>
                                                                <input type="text" placeholder="Enter email subject"
                                                                    className={cn('appearance-none !text-xs w-max border-2 border-transparent rounded-lg focus:border-primary focus:outline-none bg-white transition-all ',)}
                                                                    {...register(`stages.${index}.email_subject` as const, { required: true })}
                                                                    onChange={(e) => setValue(`stages.${index}.email_subject`, e.target.value)}
                                                                    value={watch(`stages.${index}.email_subject`, "")}
                                                                />
                                                                <Controller
                                                                    name={`stages.${index}.email_text`}
                                                                    control={control}
                                                                    defaultValue=""
                                                                    render={({ field }) => (
                                                                        <ReactQuill
                                                                            value={field.value}
                                                                            onChange={field.onChange}
                                                                            onBlur={field.onBlur}
                                                                            defaultValue={emailBody || ""}
                                                                            modules={{
                                                                                toolbar: {
                                                                                    container: [
                                                                                        [{ 'header': 1 }, { 'header': 2 }], // Header formats
                                                                                        // [{ 'size': ['extra-small', 'small', 'medium', 'large'] }], // Size format
                                                                                        ['bold', 'italic', 'underline', 'strike'], // Existing formats
                                                                                        [{ 'list': 'ordered' }, { 'list': 'bullet' }], // List formats
                                                                                        [{ 'indent': '-1' }, { 'indent': '+1' }], // Indent formats
                                                                                        [{ 'align': [] }], // Alignment format
                                                                                        ['link',], // Other formats
                                                                                        ['clean'] // Remove formatting
                                                                                    ]
                                                                                }
                                                                            }}

                                                                            id="email-subject-edit-pipeline"
                                                                            className={cn(`w-full rounded-lg bg-white outline-none min-h-[250px]`)}
                                                                            placeholder='Enter email body'
                                                                            style={{ border: "none", outline: "none" }}
                                                                        />
                                                                    )}
                                                                />
                                                            </div>
                                                        </>
                                                    }
                                                    {errors.stages && (errors.stages[index]?.email_text || errors.stages[index]?.email_subject) && <span className='text-[0.7rem] text-red-500 leading-tight'>{errors.stages[index]?.email_subject?.message || errors.stages[index]?.email_text?.message || "This field is required"}</span>}


                                                    <div className='flex items-center gap-4 justify-end mt-6'>
                                                        {
                                                            item.name == "Newest Stage" &&
                                                            <Button size='tiny' variant='unstyled' className='bg-white hover:bg-red-500 hover:text-white rounded-lg'
                                                                onClick={() => { remove(index); setValue("currentEdit", -1) }}
                                                            >
                                                                Delete
                                                            </Button>
                                                        }
                                                        <Button size='tiny' onClick={(e) => setValue('currentEdit', -1)}>
                                                        {/* <Button size='tiny' onClick={(e) => saveStage(index, e)}> */}
                                                            {item.name === "Newest Stage" ? "Add" : "Save"}
                                                        </Button>
                                                    </div>
                                                </section>

                                            </div>
                                        </div>
                                    }
                                </div>
                            )

                        })
                    }

                    {provided.placeholder}
                </div>
            </div>
        )}
    </Droppable>

    <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}

        subheading={
            errorModalMessage || 'Please check your inputs and try again.'
        }
    >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
            <Button
                className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
                // size=""
                type="button"
                onClick={closeErrorModal}
            >
                Okay
            </Button>
        </div>
    </ErrorModal>
</div>
</DragDropContext >