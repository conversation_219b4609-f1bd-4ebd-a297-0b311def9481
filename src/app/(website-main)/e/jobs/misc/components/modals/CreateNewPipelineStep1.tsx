import React, { useState } from 'react'

import { <PERSON>ton, LoadingOverlay, Modal } from '@/components/shared'
import { useBooleanStateControl } from '@/hooks'

import CreateNewPipelineStep2 from './CreateNewPipelineStep2'
import { cn } from '@/utils'
import { useCreatePipeline } from '../../api'
import { Stage } from '../../types'


interface PipeProps {
    isNewPipelineStep1ModalOpen: boolean
    closeNewPipelineStep1Modal: (pipelineId?:string) => void
}
const CreateNewPipeline: React.FC<PipeProps> = ({ isNewPipelineStep1ModalOpen, closeNewPipelineStep1Modal }) => {
    const [pipelineName, setPipelineName] = useState("")
    const [pipelineId, setPipelineId] = useState(0)
    const [error, setError] = useState(false)
    const [newCandidatesStage, setNewCandidatesStage] = useState({} as Stage)

    const {
        state: isStagesModalOpen,
        setTrue: openStagesModal,
        setFalse: closeStagesModal
    } = useBooleanStateControl()


    const { mutate: createPipeline, isLoading: isPipelineCreating } = useCreatePipeline();
    const next = () => {
        if (pipelineName.trim() === "") {
            setError(true)
            return
        } else {
            createPipeline(
                { name: pipelineName, is_default: "False" },
                {
                    onSuccess: (data) => {
                        console.log(data?.id, "DATA id from response")
                        closeNewPipelineStep1Modal(data?.id)

                        setPipelineId(data?.id)
                        setNewCandidatesStage(data?.stages[0])
                        openStagesModal()
                    }
                }
            )
        }
    }

  



    return (
        <>
            <Modal isModalOpen={isNewPipelineStep1ModalOpen} allowDismiss color='white' closeModal={closeNewPipelineStep1Modal}
                heading='New job pipeline'
                body={
                    <form onSubmit={next}>
                        <p className='text-body-text text-[0.875rem]'>Create new pipeline by naming, creating stages and ordering them.</p>
                        <div className='inputdiv mt-6'>
                            <label htmlFor="pipeline" className='text-[0.875rem]'>Name pipeline</label>
                            <input type="text" id='pipeline' className={cn('!bg-[#F5F7F9] ', error && "error")} placeholder='Enter job pipeline name'
                                onChange={(e) => setPipelineName(e.target.value)}
                            />
                            {
                                error && <span className='text-red-500 text-[0.9rem]'>Pipeline name cant be empty</span>
                            }
                        </div>

                        <div className='flex mt-10'>
                            <Button className='ml-auto px-12 py-3' onClick={next} type='button'>
                                Proceed
                            </Button>
                        </div>
                    </form>
                }

            />

            <CreateNewPipelineStep2 isStagesModalOpen={isStagesModalOpen} closeStagesModal={closeStagesModal} name={pipelineName} id={pipelineId} newCandidatesStage={newCandidatesStage} />
            <LoadingOverlay isOpen={isPipelineCreating} />
        </>
    )
}

export default CreateNewPipeline