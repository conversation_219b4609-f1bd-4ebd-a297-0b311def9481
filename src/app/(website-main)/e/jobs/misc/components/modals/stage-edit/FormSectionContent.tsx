import { LoaderBtn } from "@/components/shared";
import {Button} from "@/components/shared/btn";

// Form Section Component
export const FormSection: React.FC<{
  title: string;
  description?: string;
  className?: string;
  children: React.ReactNode;
  showChildren: boolean;
  setShowChildren: () => void;
}> = ({
  title,
  description,
  className = '',
  showChildren = false,
  setShowChildren,
  children,
}) => (
    <section
      className={`mb-5 border-gray-200 ${className} ${showChildren ? 'border-t  pt-4' : 'rounded-lg border p-4'
        }`}
    >
      <div className="flex items-start justify-between">
        <div>
          <h6 className="mb-1 text-[16px] font-medium text-header-text">
            {title}
          </h6>
          {description && (
            <p className="text-[13px] text-body-text">{description}</p>
          )}
        </div>
        {!showChildren && (
          <Button
            size="thin"
            variant="default"
            className="bg-gray py-2 text-sm text-body-text hover:bg-gray-300"
            onClick={setShowChildren}
          >
            Configure
          </Button>
        )}
      </div>

      {showChildren && <div className="mb-4 mt-4 space-y-4">{children}</div>}
    </section>
  );

// Form Section Component
export const FormContent: React.FC<{
  className?: string;
  children: React.ReactNode;
  submit: () => void;
  cancel: () => void;
  isLoading?: boolean;
  save?: string;
  hideCancel?: boolean;
}> = ({ submit, cancel, className = '', isLoading, save, hideCancel, children }) => (
  <div className={`rounded-lg bg-[#F5F3FF] p-4 ${className} `}>
    {children}

    <div className="flex w-full items-end justify-end gap-x-3">
      {!hideCancel && (
        <Button
        size="thin"
        variant="default"
        className="bg-white py-2 text-body-text hover:bg-gray-300"
        onClick={cancel}
      >
        Cancel
      </Button>
      )}
      <Button
        size="thin"
        variant="default"
        type="button"
        className=" py-2"
        onClick={submit}
        disabled={isLoading}
      >
        <div className="flex items-center gap-2">
          {isLoading && <LoaderBtn width={10} height={10} color="white" />}
          {save || 'Submit'}
        </div>
      </Button>
    </div>
  </div>
);
