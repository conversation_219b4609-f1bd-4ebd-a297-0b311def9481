import { useState } from "react";
import { Stage } from "../../../types";
import { <PERSON><PERSON><PERSON> } from "../../../types/PipelineType";
import { FormContent, FormSection } from "./FormSectionContent";
import { Button, ErrorModal, Input, RadioGroup, Select, Switch, Textarea } from "@/components/shared";
import { yes_or_no } from "../../../constants";
import { AssessmentSelector, InterviewSelector } from "./Selector";
import { cn } from "@/utils";
import { Add, CloseCircle } from "iconsax-react";



type Props = {
  stage: Stage,
  isSubmitting: boolean,
  submitForm: (form: Stage) => void,
  save?: string;
  closeOnSave?: boolean;
  hideCancel?: boolean;
}
export const EditPipelineStage = ({ stage, isSubmitting, submitForm: prepSubmitForm, save, closeOnSave, hideCancel }: Props) => {
  const [form, setForm] = useState<Stage>(stage);
  const [editingStageIndex, setEditingStageIndex] = useState<
    number | null
  >(null);

  const prepClose = () => {
    setEditingStageIndex(null);

  }

  const submitForm = (form: Stage) => {
    if (closeOnSave) {
      prepClose()
    }
    prepSubmitForm(form)
  }

  const handleChange = <K extends keyof Stage>(key: K, value: Stage[K]) => {
    console.log(key, value);
    setForm(prev => ({ ...prev, [key]: value }));
  };

  // For nested objects
  const handleNestedChange = <K extends keyof Stage, V>(
    key: K,
    path: string,
    value: V
  ) => {
    setForm(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        [path]: value,
      },
    }));
  };

  const handleContentToggle = (index: number) => {
    if (editingStageIndex === index) {
      setEditingStageIndex(null);
    } else {
      setEditingStageIndex(index);
    }
  };

  const [activeReminderIndex, setActiveReminderIndex] = useState<number>(0);
  const updateReminder = (
    index: number,
    key: keyof NonNullable<Stage['reminders']>[0],
    value: string | number
  ) => {
    if (!form.reminders) return; // Guard against null

    const updated = [...form.reminders]; // Now TypeScript knows it's not null
    updated[index] = { ...updated[index], [key]: value };
    handleChange('reminders', updated);
  };

  const addReminder = () => {
    const newReminder: Reminder = { days_before: 0, title: '', content: '' };
    handleChange(
      'reminders',
      form.reminders ? [...form.reminders, newReminder] : [newReminder]
    );

    setActiveReminderIndex(form.reminders ? form.reminders.length : 0);
  };

  const removeReminder = (index: number) => {
    if (!form.reminders) return;
    const updated = [...form.reminders];
    updated.splice(index, 1);
    handleChange('reminders', updated);
    setActiveReminderIndex(updated.length > 0 ? updated.length - 1 : 0);
  };

  return (
    <div>
      <FormSection
        showChildren={true}
        setShowChildren={() => { }}
        title="Configure stage"
        description="Here you can define the criteria for candidate progression between stages and specify the automated actions you want the system to execute."
      >
        <div className="spacing-y-3">
          <label className=" block text-sm font-medium text-body-text">
            Stage Name
          </label>

          <Input
            type="text"
            placeholder="Enter stage name"
            variant="default"
            className="bg-[#F5F7F9]"
            value={form.name}
            onChange={e => handleChange('name', e.target.value)}
          // hasError={!!errors?.name}
          // errorMessage={errors?.name?.message as string}
          // {...field}
          />
        </div>

        {/* {errors?.name && (
                        <div className="mt-1 text-xs text-red-500">
                          {(errors?.name?.message as string) ||
                            'Stage name is required'}
                        </div>
                      )} */}
      </FormSection>

      {/* <div className="my-5">
                      <p className=" block text-xs text-body-text">
                        Drag desired stage to reorder
                      </p>
                    </div> */}

      {/* ASSESSMENT SETUP */}
      <FormSection
        showChildren={editingStageIndex == 0}
        setShowChildren={() => handleContentToggle(0)}
        title="Assessment & Interview Setup"
        description="Configuring assessments now helps ensure only qualified candidates move forward. You can adjust these settings anytime."
        className=""


      >
        <FormContent
          submit={() => submitForm(form)}
          cancel={() => handleContentToggle(0)}
          hideCancel={hideCancel}
          save={save}
          isLoading={isSubmitting && editingStageIndex == 0}
        >
          <div className="mb-5 gap-2">
            <p className="text-[14px] font-medium">
              Assessments and Interviews
            </p>

            <div className="sm:flex-column my-3 flex flex-wrap items-start gap-3 md:flex-row md:items-center">
              <p className="text-[14px] text-[#4A4A68]">
                Is this an interview stage?
              </p>
              <RadioGroup
                name="is_interview"
                options={yes_or_no}
                onChange={value =>
                  handleChange('is_interview', value === 'true')
                }
                value={form.is_interview ? 'true' : 'false'}
                containerClass="flex-row items-start md:items-center my-0"
                baseClass="!w-max"
                className="!py-0"
                variant="unstyled"
                arrangement="row"
              />
            </div>
            {form.is_interview && (
              <>
                <InterviewSelector
                  onChange={value => handleChange('interview', value)}
                  value={form.interview}
                />
              </>
            )}

            <div className="sm:flex-column my-3 flex flex-wrap items-start gap-3 md:flex-row md:items-center">
              <p className="text-[14px] text-[#4A4A68]">
                Will this stage require an assessment?
              </p>
              <RadioGroup
                name="is_assessment"
                options={yes_or_no}
                onChange={value => {
                  handleChange('is_assessment', value === 'true');
                }}
                value={form.is_assessment ? 'true' : 'false'}
                containerClass="flex-row items-start md:items-center my-0"
                baseClass="!w-max"
                className="!py-0"
                variant="unstyled"
                arrangement="row"
              />
            </div>
            {form.is_assessment && (
              <>
                <AssessmentSelector
                  onChange={value => handleChange('assessment', value)}
                  value={form.assessment}
                />
              </>
            )}
          </div>

          <div className="mb-5 gap-2">
            <p className="text-[14px] font-medium">Automations</p>

            <div className="sm:flex-column my-3 flex flex-wrap items-start gap-3 md:flex-row md:items-center">
              <p className="text-[14px] text-[#4A4A68]">
                Enabling staging automation
              </p>
              <RadioGroup
                name="is_interview"
                options={yes_or_no}
                onChange={value => {
                  handleChange('automation_enabled', value === 'true');
                }}
                value={form.automation_enabled ? 'true' : 'false'}
                containerClass="flex-row items-start md:items-center my-0"
                baseClass="!w-max"
                className="!py-0"
                variant="unstyled"
                arrangement="row"
              />
            </div>

            {form.automation_enabled &&
              <div className="sm:flex-column my-3 flex flex-wrap items-start gap-3 md:flex-row md:items-center">
                <p className="text-[14px] text-[#4A4A68]">
                  Move candidates with at least
                </p>
                <input
                  type="number"
                  min="0"
                  value={form.move_criteria}
                  onChange={e => {
                    const num = Number(e.target.value);
                    if (!isNaN(num)) handleChange('move_criteria', num);
                  }}
                  className={cn(
                    'w-10 rounded-md border p-1 text-center text-sm',
                    !form.move_criteria && form.move_criteria !== 0
                      ? 'border-red-500'
                      : 'border-gray-300'
                  )}
                />
                <p className="text-[14px] text-[#4A4A68]">
                  % match to the next stage
                </p>
              </div>
            }
          </div>

          <div className="mb-5 gap-2">
            <p className="text-[14px] font-medium">Notifications</p>

            <div className="sm:flex-column my-5 flex flex-wrap items-start gap-3 md:flex-row md:items-center">
              <p className="text-[14px] text-[#4A4A68]">
                Will this stage require an email to be sent?
              </p>

              <RadioGroup
                name="is_interview"
                options={yes_or_no}
                onChange={value => {
                  handleChange(
                    'email_notification_enabled',
                    value === 'true'
                  );
                }}
                value={form.email_notification_enabled ? 'true' : 'false'}
                containerClass="flex-row items-start md:items-center my-0"
                baseClass="!w-max"
                className="!py-0"
                variant="unstyled"
                arrangement="row"
              />
            </div>

            {form.email_notification_enabled && (
              <>
                <div className="mt-3 flex flex-col gap-y-2 rounded-lg border p-4">
                  <p className="text-xs font-medium">
                    Email notification
                  </p>

                  <Input
                    placeholder="Email subject"
                    onChange={e => {
                      handleChange('email_subject', e.target.value);
                    }}
                    value={form.email_subject || ''}
                    className="bg-white text-[13px]"
                    variant="default"
                  />

                  <Textarea
                    id="email_body"
                    placeholder="Email body"
                    variant="showcase"
                    className="text-[13px]"
                    rows={10}
                    onChange={e => {
                      handleChange('email_text', e.target.value);
                    }}
                    value={form.email_text || ''}
                  />
                </div>
              </>
            )}
          </div>
        </FormContent>
      </FormSection>

      {/* Offer setup */}
      <FormSection
        showChildren={editingStageIndex == 1}
        setShowChildren={() => handleContentToggle(1)}
        title="Offer Letter Setup"
        description="Recruiters can define the conditions and content of the offer letter that will be sent once a candidate reaches the hired stage."
        className="border-t border-gray-200 pt-4"
      >
        <FormContent
          submit={() => submitForm(form)}
          cancel={() => handleContentToggle(1)}
          isLoading={isSubmitting && editingStageIndex == 1}
        >
          <>
            <div className="sm:flex-column my-5 flex flex-wrap items-start gap-3 md:flex-row md:items-center">
              <p className="text-[14px] text-[#4A4A68]">
                Offer Stage?
              </p>

              <RadioGroup
                name="is_offer_stage"
                options={yes_or_no}
                onChange={value => {
                  handleChange(
                    'is_offer_stage',
                    value === 'true'
                  );
                }}
                value={form.is_offer_stage ? 'true' : 'false'}
                containerClass="flex-row items-start md:items-center my-0"
                baseClass="!w-max"
                className="!py-0"
                variant="unstyled"
                arrangement="row"
              />
            </div>
            {
              form.is_offer_stage &&

              <div className="my-3 flex w-full  flex-col items-start gap-1">
                <p className="text-[14px] font-medium">Offer Letter Setup</p>
                <RadioGroup
                  name="is_interview"
                  options={[
                    {
                      name: 'Automatically send offer letter when a candidate reaches the hired stage.',
                      value: 'true',
                    },
                    {
                      name: 'Send offer letter manually after review.',
                      value: 'false',
                    },
                  ]}
                  onChange={value => {
                    handleChange(
                      'offer_letter_send_automatic',
                      value === 'true'
                    );

                    // offer_letter_send_automatic	true
                    // if (value == "send_offer") {
                    //   handleChange('offer_letter_send_automatic', true);
                    //   handleChange('offer_letter_manual_review', false);

                    // } else {
                    //   handleChange('offer_letter_send_automatic', false);
                    //   handleChange('offer_letter_manual_review', true);
                    // }
                  }}
                  value={form.offer_letter_send_automatic ? 'true' : 'false'}
                  containerClass="my-0"
                  labelClass="text-xs font-medim"
                  itemClass="text-left text-[#4A4A68] "
                  className="!py-0"
                  variant="unstyled"
                  arrangement="column"
                />
              </div>
            }
          </>
        </FormContent>
      </FormSection>

      {/* Rejection Setup */}
      <FormSection
        showChildren={editingStageIndex == 2}
        setShowChildren={() => handleContentToggle(2)}
        title="Rejection Setup"
        description="Here you can define the criteria for candidate progression between stages and specify the automated actions you want the system to execute."
        className="border-t border-gray-200 pt-4"
      >
        <FormContent
          submit={() => submitForm(form)}
          cancel={() => handleContentToggle(2)}
          isLoading={isSubmitting && editingStageIndex == 2}
        >
          <div className="mb-5 gap-2">
            <p className="text-[14px] font-medium">
              Configure Rejection Message
            </p>

            <div className="sm:flex-column  my-5 flex items-start gap-3 md:flex-row md:items-center">
              <p className="text-[14px] text-[#4A4A68]">
                Customize rejection messages based on the reason for
                rejection or feedback provided by the interviewer.
              </p>
              <RadioGroup
                name="customize_rejection_message"
                options={yes_or_no}
                onChange={value => {
                  handleChange(
                    'customize_rejection_message',
                    value === 'true'
                  );
                }}
                value={
                  form.customize_rejection_message ? 'true' : 'false'
                }
                label=""
                containerClass="flex-row items-start md:items-center my-0"
                baseClass="!w-max"
                className="!py-0"
                variant="unstyled"
                arrangement="row"
              />
            </div>

            {form.customize_rejection_message && (
              <>
                <div className="flex items-center gap-2">
                  <span className="text-[12px] text-[#4A4A68]">
                    Send an invite to apply for future roles
                  </span>
                  <Select
                    placeholder=""
                    onChange={val => {
                      handleChange(
                        'allow_invites_for_future_roles',
                        val === 'true'
                      );
                    }}
                    name="allow_invites_for_future_roles"
                    value={form.allow_invites_for_future_roles ? 'true' : 'false'}
                    options={yes_or_no}
                    labelKey="name"
                    valueKey="value"
                    className="font-sm h-[1.875rem] bg-white p-1"
                  // hasError={!!errors.years_of_experience}
                  // errorMessage={errors?.years_of_experience?.message as string}
                  />
                </div>

                {form.allow_invites_for_future_roles &&
                  <div className="mt-3 flex flex-col gap-y-2 rounded-lg border p-4">
                    <p className="text-xs font-medium">Email notification</p>
                    <p className="text-xs text-body-text">
                      Here you can enter the email content for the email
                      notification you want to send to candidates at this stage.
                      Also note that this email will be sent immediately
                      candidates enter this stage.
                    </p>

                    <Input
                      placeholder="Candidate Invitation for Future Roles"
                      onChange={e => {
                        handleNestedChange(
                          'future_invites_data',
                          'email_subject',
                          e.target.value
                        );
                      }}
                      value={
                        form.future_invites_data?.email_subject || ''
                      }
                      className="bg-white text-[13px]"
                      variant="default"
                    />

                    <Textarea
                      id="future_invites_data_email_body"
                      placeholder="We’d love to stay in touch with you for other openings at 2PalAfrica. Please feel free to apply for future roles that match your experience."
                      variant="showcase"
                      className="text-[13px]"
                      rows={10}
                      onChange={e => {
                        handleNestedChange(
                          'future_invites_data',
                          'email_body',
                          e.target.value
                        );
                      }}
                      value={
                        form.future_invites_data?.email_body || ''
                      }
                    />
                  </div>
                }
              </>)
            }
          </div>
        </FormContent>
      </FormSection>

      {/* Resumption Notification */}
      <FormSection
        showChildren={editingStageIndex == 3}
        setShowChildren={() => handleContentToggle(3)}
        title="Resumption Notification"
        description="Send reminders to the candidate leading up to their start date and notify the supervisor when the candidate arrives on the first day."
        className="border-t border-gray-200 pt-4"
      >
        <FormContent
          submit={() => submitForm(form)}
          cancel={() => handleContentToggle(3)}
          isLoading={isSubmitting && editingStageIndex == 3}
        >
          <div className="mb-5 gap-2">
            <p className="text-[14px] font-medium">Reminder Settings</p>

            <div className="mb-4 flex items-center justify-between">
              <span className="text-[14px] text-[#4A4A68]">
                {' '}
                Enable automated reminders{' '}
              </span>
              <Switch
                checked={form.enable_automated_reminders}
                onCheckedChange={e => {
                  console.log(e, 'E SWITCH');
                  handleChange('enable_automated_reminders', e);
                }}
              />
            </div>
            {form.enable_automated_reminders && (
              <>
                <p className="my-4 text-[12px] text-[#4A4A68]">
                  Select reminder intervals
                </p>

                <div className="flex overflow-x-scroll gap-2 mb-4">
                  {form.reminders?.map((reminder, index) => (

                    <Button
                      variant="unstyled"
                      key={index}
                      className={`group relative border border-primary bg-white p-1 ${index === activeReminderIndex ? 'border-solid' : 'border-0'}`}
                      onClick={() => setActiveReminderIndex(index)}
                    >
                      <div className="flex items-center rounded-lg bg-[#F1EFFC] px-2 py-0">
                        <span className="flex items-center gap-2 text-[12px] text-primary">
                          {reminder.days_before} days before
                        </span>

                        {/* Close icon shown only on hover */}
                        <CloseCircle
                          size={16}
                          variant="Bold"
                          className="ml-2 hidden cursor-pointer text-primary group-hover:inline"
                          onClick={e => {
                            e.stopPropagation(); // prevent button click
                            removeReminder(index); // your custom remove handler
                          }}
                        />
                      </div>
                    </Button>
                  ))}
                  <Button
                    variant="unstyled"
                    className="border border-dashed border-primary bg-white p-1"
                    onClick={addReminder}
                  >
                    <div className="flex items-center rounded-lg bg-[#F1EFFC] p-0 px-2">
                      <span className="flex items-center gap-2 text-[12px] text-primary">
                        <Add /> Add
                      </span>
                    </div>
                  </Button>
                </div>

                {form.reminders?.map((reminder, index) => (
                  <div key={index}>
                    {index === activeReminderIndex && <>
                      <p className="text-[13px] text-[#4A4A68]">Intervals</p>

                      <div className="relative mb-4">
                        <Input
                          placeholder=""
                          value={reminder.days_before}
                          onChange={e =>
                            updateReminder(
                              index,
                              'days_before',
                              e.target.value
                            )
                          }
                          className="bg-white text-[13px]"
                          variant="default"
                          type="number"
                        />
                        <div className="absolute right-2 top-1/2 -translate-y-1/2 rounded-md bg-gray-200 px-4 py-2 text-[10px] text-[#4A4A68]">
                          days before
                        </div>
                      </div>
                      <div
                        key={index}
                        className="flex flex-col gap-y-3 rounded-lg border p-4"
                      >
                        <p className="text-[14px] font-medium">
                          Reminder {index + 1}
                        </p>
                        <p className="text-[13px] text-[#4A4A68]">
                          Set Automated Messages
                        </p>

                        <Input
                          placeholder="Reminder title"
                          value={reminder.title}
                          onChange={(e) => updateReminder(index, 'title', e.target.value)}
                          className="bg-white text-[13px]"
                          variant="default"
                        />
                        <Textarea
                          placeholder="Reminder message content"
                          value={reminder.content}
                          onChange={(e) => updateReminder(index, 'content', e.target.value)}
                          variant="showcase"
                          rows={5}
                          className="text-[13px]"
                        />
                      </div>
                    </>}
                  </div>
                ))}


              </>
            )}
          </div>
        </FormContent>
      </FormSection>

      {/* ONBOARDING STAGE */}
      <FormSection
        showChildren={editingStageIndex == 4}
        setShowChildren={() => handleContentToggle(4)}
        title="Onboarding Setup"
        description="Help new hires feel connected and ready from day one by creating a smooth onboarding experience for successful candidates."
        className="border-t border-gray-200 pt-4"
      >
        <FormContent
          submit={() => submitForm(form)}
          cancel={() => handleContentToggle(4)}
          isLoading={isSubmitting && editingStageIndex == 4}
        >
          <div className="mb-0 gap-2">
            <p className="text-[14px] font-medium">Onboarding Setup</p>
            <div className="sm:flex-column  my-5 flex items-start gap-3 md:flex-row md:items-center">
              <p className="text-[14px] text-[#4A4A68]">
                Enable candidate onboarding after job offer
              </p>
              <RadioGroup
                name="is_interview"
                options={yes_or_no}
                onChange={value => {
                  handleChange(
                    'enable_onboarding_setup',
                    value === 'true'
                  );
                }}
                label=""
                value={form.enable_onboarding_setup}
                containerClass="flex-row items-start md:items-center my-0"
                baseClass="!w-max"
                className="!py-0"
                variant="unstyled"
                arrangement="row"
              />
            </div>
          </div>
        </FormContent>
      </FormSection>

      {/* <ErrorModal
                      isErrorModalOpen={isErrorModalOpen}
                      setErrorModalState={setErrorModalState}
                      subheading={
                        errorModalMessage || 'Please check your inputs and try again.'
                      }
                    >
                      <div className="flex gap-3 rounded-lg bg-red-50 p-4">
                        <Button
                          className="w-full bg-red-600 text-white hover:bg-red-700"
                          type="button"
                          onClick={closeErrorModal}
                        >
                          Okay
                        </Button>
                      </div>
                    </ErrorModal> */}
    </div>
  )
}


