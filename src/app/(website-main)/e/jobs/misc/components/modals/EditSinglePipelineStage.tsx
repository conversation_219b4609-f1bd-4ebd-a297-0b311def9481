import { AxiosError } from 'axios';
import { Add, CloseCircle } from 'iconsax-react';
import { ChevronDown } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import toast from 'react-hot-toast';
import { useInView } from 'react-intersection-observer';
import { FixedSizeList as List } from 'react-window';
import {
  Button,
  ErrorModal,
  Input,
  LoaderBtn,
  Modal,
  RadioGroup,
  Select,
  Switch,
  Textarea,
} from '@/components/shared';
import { useErrorModalState } from '@/hooks';
import { cn } from '@/utils';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { convertToTitleCase } from '@/utils/strings';
import { useEditPipelineStage, useGetInterviewAssessments } from '../../api';
import useGetAssessments from '../../api/getAssessments';
import { yes_or_no } from '../../constants';
import { Stage } from '../../types';
import { Reminder } from '../../types/PipelineType';
import { AddHeaderToEmailBody } from '../../utils/AddHeaderToEmailBody';
import { EditPipelineStage } from './stage-edit/EditePipelineStage';


// Main Component
export const EditSinglePipelineStage = ({
  pipelineId,
  stage,
  isEditStageModalOpen,
  closeStagesModal,
  refetchData,
  openOfferLetterConfigurationConfirmationModal,
  openRejectionConfigSuccessModal,
  openOnboardingEnabledSuccessModal,
}: {
  pipelineId: number;
  stage: Stage;
  isEditStageModalOpen: boolean;
  closeStagesModal: () => void;
  openOfferLetterConfigurationConfirmationModal?: () => void;
  openRejectionConfigSuccessModal?: () => void;
  openOnboardingEnabledSuccessModal?: () => void;
  refetchData?: () => void;
}) => {
  

   const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();


 

  const closeModal = () => {
    closeStagesModal();
  };

  const { mutate: editStage, isLoading: isStageEditing } =
    useEditPipelineStage();

  const validateReminders = (form:Stage) => {
    // Check reminders for validation
    if (form.reminders && form.reminders.length > 0) {
      // Check for empty or invalid reminders
      const invalidReminders = form.reminders.filter(
        reminder =>
          reminder.days_before === 0 ||
          !reminder.title?.trim() ||
          !reminder.content?.trim()
      );

      if (invalidReminders.length > 0) {
        openErrorModalWithMessage(
          'All reminders must have a valid title and content, and their intervals must not be 0'
        );
        return false;
      }

      // Check for duplicate days_before values
      const daysBeforeSet = new Set<number>();
      const hasDuplicates = form.reminders.some(reminder => {
        if (daysBeforeSet.has(reminder.days_before)) {
          return true;
        }
        daysBeforeSet.add(reminder.days_before);
        return false;
      });

      if (hasDuplicates) {
        openErrorModalWithMessage('Each reminder must have a unique interval.');
        return false;
      }
    }
    return true;
  };

  const submitForm = (form:Stage) => {

     const body_text = form.email_text;
    const newStage = {
      ...form,
      email_body: body_text
        ? AddHeaderToEmailBody(body_text, form.is_interview)
        : null,
      order: form.order,
    };

    if (newStage.is_interview && !newStage.interview) {
      toast.error('Kindly select an interview');
      return;
    }

    if (newStage.is_assessment && !newStage.assessment) {
      toast.error('Kindly select an assessment');
      return;
    }

    if (
      newStage.email_notification_enabled &&
      (!newStage.email_text ||
        newStage.email_text?.trim() === '' ||
        !newStage.email_subject ||
        newStage.email_subject?.trim() === '')
    ) {
      toast.error(
        'Email subject and body are required when notification is enabled'
      );
      return;
    }
    if (!validateReminders(form)) return;

     editStage(
      { pipelineId, stageId: Number(stage.id), newStage },
      {
        onSuccess() {
           if (
            form.offer_letter_send_automatic &&
            !stage.offer_letter_send_automatic
          ) {
             openOfferLetterConfigurationConfirmationModal?.();
          }

          closeStagesModal();
          if (
            form.customize_rejection_message &&
            !stage.customize_rejection_message
          ) {
            openRejectionConfigSuccessModal?.();
          }

          if (
            form.enable_onboarding_setup &&
            !stage.enable_onboarding_setup
          ) {
            openOnboardingEnabledSuccessModal?.();
          }

          toast.success("Pipeline config updated successfully");

          closeModal();
          if (refetchData) refetchData();
        },
        onError(error: any) {
           const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(
            error?.response?.data.error ||
            errorMessage ||
            'Something went wrong'
          );
        },
      }
    );
  };



  return (
    <>
      <Modal
        heading={`Edit Pipeline Stage: ${convertToTitleCase(stage.name)} `}
        isModalOpen={isEditStageModalOpen}
        allowDismiss
        closeModal={closeModal}
        color="white"
        contentClass="max-w-2xl"
        body={
          <div className="">
       <EditPipelineStage
       stage={stage}
       isSubmitting={isStageEditing}
       submitForm={(form:Stage)=>submitForm(form)}
       />
          </div>
        }
      // footer={
      //   < div className="flex justify-end gap-3 border-t p-4" >
      //     <Button
      //       className="bg-gray-200 px-4 py-2 text-gray-800 hover:bg-gray-300"
      //       type="button"
      //       onClick={closeModal}
      //     >
      //       Cancel
      //     </Button>
      //     <Button
      //       className="bg-primary px-6 py-2 text-white hover:bg-primary-dark"
      //       type="submit"
      //       form="edit-single-pipeline-stage-form"
      //     >
      //       Save Changes
      //     </Button>
      //   </div >
      // }
      />
      {/* < LoadingOverlay isOpen={isStageEditing} /> */}
    </>
  );
};
