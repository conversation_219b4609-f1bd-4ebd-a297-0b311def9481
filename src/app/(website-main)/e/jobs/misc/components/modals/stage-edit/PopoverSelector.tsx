import { useInView } from 'react-intersection-observer';
import { useEffect, useRef, useState } from "react";
import { ChevronDown } from 'lucide-react';
import { cn } from '@/utils';
import { FixedSizeList as List } from 'react-window';


 
// PopoverSelect Component
export const PopoverSelect: React.FC<{
  options: { name: string; value: string }[];
  value: string | undefined;
  onChange: (value: string) => void;
  placeholder?: string;
  label?: string;
  error?: string;
  isLoading: boolean;
  onLoadMore: () => void;
  hasMore: boolean;
  searchable?: boolean;
}> = ({
  options,
  value,
  onChange,
  placeholder = 'Select an option',
  label,
  error,
  isLoading,
  onLoadMore,
  hasMore,
  searchable = true,
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const popoverRef = useRef(null);
    const { ref, inView } = useInView();

    const selectedOption = !!value
      ? options?.find(opt => opt.value === value)
      : undefined;

    // Filter options based on search term
    const filteredOptions =
      options?.filter(opt =>
        opt.name.toLowerCase().includes(searchTerm.toLowerCase())
      ) || [];

    // Close popover when clicking outside
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (
          popoverRef.current &&
          !(popoverRef.current as HTMLElement).contains(event.target as Node)
        ) {
          setIsOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    // Load more data when scrolling to bottom
    useEffect(() => {
      if (inView && hasMore && !isLoading) {
        onLoadMore();
      }
    }, [inView, hasMore, isLoading, onLoadMore]);

    return (
      <div className="relative w-full" ref={popoverRef}>
        {label && (
          <label className="mb-1 block text-sm font-medium text-gray-700">
            {label}
          </label>
        )}

        <button
          type="button"
          className={cn(
            'flex w-full items-center justify-between rounded-lg border bg-white p-2.5 text-left',
            error ? 'border-red-500' : 'border-gray-300',
            'focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary'
          )}
          onClick={() => setIsOpen(!isOpen)}
        >
          <span className={cn('text-sm', !selectedOption && 'text-gray-500')}>
            {selectedOption ? selectedOption.name : placeholder}
          </span>
          <ChevronDown
            className={cn(
              'h-4 w-4 text-gray-500 transition-transform',
              isOpen && 'rotate-180 transform'
            )}
          />
        </button>

        {error && <div className="mt-1 text-xs text-red-500">{error}</div>}

        {isOpen && (
          <div className="absolute z-10 mt-1 w-full rounded-md border border-gray-200 bg-white shadow-lg">
            {searchable && (
              <div className="border-b p-2">
                <input
                  type="text"
                  placeholder="Search..."
                  className="w-full rounded border p-2 text-sm"
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  onClick={e => e.stopPropagation()}
                />
              </div>
            )}

            <div className="max-h-60 overflow-auto">
              {isLoading && filteredOptions.length === 0 ? (
                <div className="flex items-center justify-center p-4 text-sm text-gray-500">
                  Loading...
                </div>
              ) : filteredOptions.length > 0 ? (
                <>
                  <List
                    height={Math.min(filteredOptions.length * 36, 220)}
                    itemCount={filteredOptions.length}
                    itemSize={36}
                    width="100%"
                  >
                    {({ index, style }) => {
                      const option = filteredOptions[index];
                      return (
                        <div
                          style={style}
                          className={cn(
                            'cursor-pointer px-3 py-2 text-sm hover:bg-gray-100',
                            value === option.value && 'bg-primary/10 font-medium'
                          )}
                          onClick={() => {
                            onChange(option.value);
                            setIsOpen(false);
                          }}
                        >
                          {option.name}
                        </div>
                      );
                    }}
                  </List>
                  {hasMore && (
                    <div
                      ref={ref}
                      className="flex h-10 items-center justify-center text-xs text-gray-500"
                    >
                      {isLoading ? 'Loading more...' : 'Scroll for more'}
                    </div>
                  )}
                </>
              ) : (
                <div className="p-4 text-center text-sm text-gray-500">
                  No options found
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };
