import DeleteSectionIcon from "@/components/icons/DeleteSectionIcon";
import { Button, Modal } from "@/components/shared";
import { CircleCheck } from "lucide-react";

// Main Component
export const RejectionLetterSuccessModal = ({
    isOpen,
    configureNow,
    closeModal,

}: {
    isOpen: boolean;
    configureNow: () => void;
    closeModal: () => void;

}) => {



    return (
        <>
            <Modal
                heading={`Offer Letter Configuration Successful `}
                isModalOpen={isOpen}
                allowDismiss
                closeModal={closeModal}
                color="white"
                contentClass="max-w-2xl"
                body={
                    <>
                        <div className="flex flex-col gap-3 items-start py-6">
                            <div className="rounded-full flex items-center justify-center p-2 w-20 h-20 bg-primary">
                                <CircleCheck color={"white"} size={35} />
                            </div>

                            <h2 className="heading-1 font-normal text-primary">
                                Your offer letter configuration has been successfully saved!
                            </h2>
                            <p className="text-[14px] text-[#4A4A68]">
                                Would you like to customize the offer letter now or later?
                            </p>
                        </div>

                    </>
                }
                footer={
                    <div className="flex w-full items-center justify-end gap-2 p-3  bg-[#F5F3FF]">
                        <Button
                            className="bg-white hover:bg-gray-300 hover:text-primary  px-8 py-3"
                            onClick={closeModal}
                            size={"lg"}
                            variant="white"
                        >
                            Customize Later
                        </Button>
                        <Button
                            size={"lg"}
                            className="btn-primary px-8 py-3 "
                            onClick={configureNow}
                        >
                            Customize Now
                        </Button>
                    </div>
                }

            />
        </>
    );
};

