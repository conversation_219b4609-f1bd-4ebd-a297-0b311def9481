import React, { useState } from 'react'
import { AxiosError } from 'axios'
import { Form<PERSON>rovider, useFieldArray, useForm } from 'react-hook-form'
import { DragDropContext, DropResult, Droppable } from 'react-beautiful-dnd'

import { Button, ErrorModal, LoadingOverlay, Modal, ToolTip } from '@/components/shared'
import { Info, Plus } from '@/components/shared/icons'
import { zodResolver } from '@hookform/resolvers/zod'
import { convertToTitleCase } from '@/utils/strings'
import { cn } from '@/utils'
import { useErrorModalState } from '@/hooks'
import { formatAxiosErrorMessage } from '@/utils/errors'

import StageCard from '../cards/StageCard'
import { useCreatePipelineStages, useEditPipelineStage } from '../../api'
import { AddHeaderToEmailBody } from '../../utils/AddHeaderToEmailBody'
import { CreateStage, Stage } from '../../types'
import { editPipelineStagesSchema } from '../../utils/pipelineSchemas'
import EditAllPipelineStages from './EditAllPipelineStages'
import { EditPipelineStage } from './stage-edit/EditePipelineStage'

interface StagesProps {
    name: string
    id: number
    isStagesModalOpen: boolean
    closeStagesModal: () => void
    newCandidatesStage: Stage
}
// const defaultStages: Stage[] = [
//     { id: 0, name: "Assessment stage", move_criteria: 50, is_assessment: 'true', is_interview: "false", automation_enabled: "true", assessment: "", interview: "", order: 1, email_notification_enabled: "false", offer_setup_customization: "" },
//     { name: "Interview", move_criteria: 50, is_assessment: 'false', is_interview: "true", duration: '0', automation_enabled: "true", order: 2, email_notification_enabled: "true", email_subject: "", email_body: "", email_text: "", offer_setup_customization: "" },
//     { name: "Hired", is_assessment: 'false', assessment: undefined, is_interview: "false", order: 3, email_notification_enabled: "false", automation_enabled: "true", offer_setup_customization: "" },
// ]

const defaultStages: Stage[] = [
    {
        id: 0,
        name: "Assessment stage",
        move_criteria: 50,
        is_assessment: true,
        is_interview: false,
        is_offer_stage: false,
        automation_enabled: true,
        assessment: "",
        interview: "",
        order: 1,
        email_notification_enabled: false,
        offer_setup_customization: {
            offer_letter: "",
            attachments: [],
            attachments_file_path: []
        }
    },
    {
        id: 0,
        name: "Interview",
        move_criteria: 50,
        is_assessment: false,
        is_interview: true,
        is_offer_stage: false,
        duration: 0,
        automation_enabled: true,
        order: 2,
        email_notification_enabled: true,
        email_subject: "",
        email_body: "",
        email_text: "",
        offer_setup_customization: {
            offer_letter: "",
            attachments: [],
            attachments_file_path: []
        }
    },
    {
        id: 0,
        name: "Hired",
        is_assessment: false,
        assessment: undefined,
        is_interview: false,
        is_offer_stage: false,
        move_criteria: 0,
        automation_enabled: false,

        order: 3,
        email_notification_enabled: false,
        offer_setup_customization: {
            offer_letter: "",
            attachments: [],
            attachments_file_path: []
        }
    },
]



const CreateNewPipelineStep2: React.FC<StagesProps> = ({ name, id, isStagesModalOpen, closeStagesModal, newCandidatesStage }) => {
    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();

    const methods = useForm({
        defaultValues: {
            stages: defaultStages,
            currentEdit: -1,
        },
        resolver: zodResolver(editPipelineStagesSchema),
        mode: 'onBlur',
    });
    const { register, formState: { errors }, clearErrors, handleSubmit, setValue, reset, watch, control } = methods;
    const { fields, append, remove } = useFieldArray({
        name: 'stages',
        control,
    });
    const [newCandidatesMoveCriteria, setNewCandidatesMoveCriteria] = useState(0);
    const currentEdit = watch("currentEdit")
    const theStages = watch('stages')

    const closeModal = () => {
        reset({ currentEdit: -1, stages: defaultStages })
        closeStagesModal()
    }


    const onDragEnd = ({ source, destination, draggableId }: DropResult) => {
        if (!destination) return;
        const updatedFields = [...watch('stages')];
        const [movedItem] = updatedFields.splice(source.index, 1);
        updatedFields.splice(destination.index, 0, movedItem);
        setValue('stages', updatedFields);
    };



    const createNewStage = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
        e.preventDefault()
        append({
            id: 0,
            name: "Newest Stage",
            is_interview: false,
            is_assessment: false,
            is_offer_stage: false,
            assessment: undefined,
            duration: 0,
            order: theStages.length + 1,
            move_criteria: 50,
            automation_enabled: false,
            email_notification_enabled: false,
            email_subject: "",
            email_text: "",
            email_body: "",
            offer_setup_customization: {
                offer_letter: "",
                attachments: [],
                attachments_file_path: []
            }, // Added missing property
        })
        setValue('currentEdit', watch('stages').length);
    };

    const editStage = (index: number) => {
        setValue('currentEdit', index);
    };

    const handleUpdateStage = (index: number, stage: Stage) => {

        const updatedStages = [...watch('stages')];
        updatedStages[index] = stage;
        setValue('stages', updatedStages);
        console.log(errors, "ERRORs")

    }


    ////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////
    /////////                   SUBMIT               ///////////
    ////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////
    const { mutate: editNewCandidatesStage, isLoading: isEditingNewCandidates } = useEditPipelineStage();
    const { mutate: createStages, isLoading: areStagesCreating } = useCreatePipelineStages();
    const submitForm = (data: { stages: Stage[], currentEdit: number }) => {
        const newNewCandidatesStage = {
            pipelineId: id,
            stageId: Number(newCandidatesStage.id),
            newStage: {
                ...newCandidatesStage,
                is_assessment: newCandidatesStage.is_assessment,
                is_interview: newCandidatesStage.is_interview,
                is_offer_stage: newCandidatesStage.is_offer_stage,
                automation_enabled: newCandidatesStage.automation_enabled,
                email_notification_enabled: newCandidatesStage.email_notification_enabled,
                move_criteria: newCandidatesMoveCriteria,
                assessment: newCandidatesStage.assessment || undefined,
                interview: newCandidatesStage.interview || undefined,
                offer_letter_send_automatic: newCandidatesStage.offer_letter_send_automatic,
                offer_letter_manual_review: newCandidatesStage.offer_letter_manual_review,
            }
        }
        const OtherStagesToSubmit = theStages.map((stage, index) => {
            return { ...stage, order: index, email_body: stage.email_text ? AddHeaderToEmailBody(stage.email_text, stage.is_interview === true) : null } as CreateStage
        })

        editNewCandidatesStage(newNewCandidatesStage,
            {
                onSuccess() {
                    createStages(
                        { id: id, stages: OtherStagesToSubmit },
                        {
                            onSuccess: () => {
                                reset({ currentEdit: -1, stages: defaultStages })
                                closeStagesModal()
                            },
                            onError(error: any) {
                                const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                                openErrorModalWithMessage(error?.response?.data || errorMessage);
                            },
                        }
                    )
                },
                onError(error: any) {
                    const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                    openErrorModalWithMessage(error?.response?.data || errorMessage);
                },
            }
        )
    };




    return (
        <>
            <Modal heading='New Pipeline' isModalOpen={isStagesModalOpen} allowDismiss closeModal={() => closeModal()} color='white'
                body={

                    <FormProvider {...methods} >
                        <form onSubmit={handleSubmit(submitForm)} id={`pipeline-stages-create-form`}>

                            <header>
                                <h3 className='font-medium text-header-text m-0 text-base'>{convertToTitleCase(name)}</h3>
                                <p className='text-body-text text-[0.875rem]'>
                                    Create, reorder, edit, remove, and configure your pipeline stages accordingto your desired behaviour. Any adjustments made will be applied to all pipelines to which it is connected.
                                </p>
                            </header>



                            <DragDropContext onDragEnd={onDragEnd} >

                                <div className='overflow-y-hidden w-full mt-12 mb-2.5'>
                                    <Droppable droppableId="Pipeline">
                                        {(provided) => (
                                            <div className='flex flex-col gap-6 mt-0 pb-4 px-2 h-full overflow-hidden'>
                                                <div {...provided.droppableProps} ref={provided.innerRef} className='flex flex-col gap-3.5'>
                                                    <article className='bg-white p-4 rounded-xl border-[0.3px] border-[#E3E3E3] !text-[0.85rem] mb-1 cursor-not-allowed'>
                                                        <div>
                                                            <h3 className='font-medium text-header-text text-base'>New Candidate</h3>
                                                            <p className='text-body-text'>
                                                                This stage has been automatically created for you. Every candidate that applies for your job is initially placed in this stage and ranked (by our algorithm) by how
                                                                much they fit your job description until you decide what to do with them.
                                                            </p>
                                                            <div className='flex flex-wrap items-center gap-2 my-1.5 py-1.5 px-3 text-body-text text-xs bg-primary-light-hover rounded-lg'>
                                                                <span>Move candidates with at least</span>
                                                                <input type="number" placeholder="0"
                                                                    className='appearance-none text-xs text-body-text p-1 w-max max-w-[50px] border-2 border-transparent rounded-lg focus:border-primary focus:outline-none bg-white transition-all '
                                                                    onChange={(e) => setNewCandidatesMoveCriteria(Number(e.target.value))}
                                                                />
                                                                <span className='flex items-center flex-wrap'>
                                                                    % match to the next stage
                                                                    <ToolTip content='More Info Coming Soon...' contentClass='text-xs'>
                                                                        <Info />
                                                                    </ToolTip>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </article>
                                                    {
                                                        theStages?.map((item, index) => {
                                                            const isAssessment = watch(`stages.${index}.is_assessment`) === true;
                                                            const isInterview = watch(`stages.${index}.is_interview`) === true;
                                                            const isEmailEnabled = watch(`stages.${index}.email_notification_enabled`) === true;

                                                            const emailBody = watch(`stages.${index}.email_text`);

                                                            return (
                                                                <>
                                                                    <div
                                                                        className={cn(
                                                                            currentEdit === index && 'bg-[#F5F3FF] rounded-lg p-4 my-0 border-transparent border-2',
                                                                            errors.stages && errors.stages[index] && 'border-red-500'
                                                                        )}
                                                                        key={index}
                                                                    >
                                                                        <StageCard
                                                                            stage={item as CreateStage}
                                                                            index={index}
                                                                            key={item.name}
                                                                            activeIndex={currentEdit}
                                                                            edit={editStage}
                                                                            remove={() => {
                                                                                remove(index);
                                                                                setValue('currentEdit', -1);
                                                                            }}
                                                                            hasError={(errors.stages && errors.stages[index]) ? true : false}
                                                                        />

                                                                        {
                                                                            currentEdit === index &&
                                                                            <div className="bg-white p-2 px-4 rounded-2xl ">
                                                                                {errors?.stages && errors?.stages[index] && errors?.stages[index] && <>
                                                                                    {Object.entries(errors.stages[index] || {}).map(([field, value]) => {
                                                                                        if (
                                                                                            typeof value === 'object' &&
                                                                                            value !== null &&
                                                                                            'message' in value
                                                                                        ) {
                                                                                            const error = {
                                                                                                field,
                                                                                                ...(value as { message: string; type?: string }),
                                                                                            };

                                                                                            return (
                                                                                                <div
                                                                                                    key={field}
                                                                                                    className="text-[0.7rem] text-red-500 rounded-sm w-full p-2 px-3 bg-red-50 mb-2"
                                                                                                >
                                                                                                    {`${error.field.replaceAll("_", " ")}: ${error.message}`}
                                                                                                </div>
                                                                                            );
                                                                                        }

                                                                                        return null;
                                                                                    })}
                                                                                </>}


                                                                                <EditPipelineStage
                                                                                    stage={item as Stage}
                                                                                    isSubmitting={false}
                                                                                    submitForm={(form) => handleUpdateStage(index, form)}
                                                                                    save="Save"
                                                                                    closeOnSave={true}
                                                                                />
                                                                            </div>
                                                                            // <>
                                                                            //     <EditAllPipelineStages
                                                                            //         index={index}
                                                                            //         control={control}
                                                                            //         errors={errors}
                                                                            //         register={register}
                                                                            //         setValue={setValue}
                                                                            //         watch={watch}
                                                                            //         clearErrors={clearErrors}
                                                                            //         isAssessment={isAssessment}
                                                                            //         isInterview={isInterview}
                                                                            //         isEmailEnabled={isEmailEnabled}
                                                                            //         emailBody={emailBody || ""}
                                                                            //         currentEdit={currentEdit}
                                                                            //         isStagesModalOpen={isStagesModalOpen}
                                                                            //     />

                                                                            // </>
                                                                        }
                                                                    </div>
                                                                </>
                                                            )
                                                        })
                                                    }
                                                    {provided.placeholder}
                                                </div>
                                            </div>
                                        )}
                                    </Droppable>

                                    <ErrorModal
                                        isErrorModalOpen={isErrorModalOpen}
                                        setErrorModalState={setErrorModalState}
                                        subheading={
                                            errorModalMessage || 'Please check your inputs and try again.'
                                        }
                                    >
                                        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                                            <Button
                                                className="grow bg-red-950 hover:border-red-950 hover:text-red-950 px-1.5 sm:text-sm md:px-6"
                                                type="button"
                                                onClick={closeErrorModal}
                                            >
                                                Okay
                                            </Button>
                                        </div>
                                    </ErrorModal>
                                </div>
                            </DragDropContext>

                            {
                                errors.stages && theStages.length < 1 &&
                                <span className='text-red-500 text-sm'>Your pipeline must contain at least one stage</span>
                            }

                            <Button onClick={(e) => createNewStage(e)} size='thin' variant='extralight' className='w-full' icon={<Plus />}>
                                Create new stage
                            </Button>

                        </form>
                    </FormProvider>

                }



                footer={
                    <div className='flex p-6 ml-auto'>
                        <Button
                            className='px-12 py-2 md:py-2.5 text-[0.875rem]'
                            type='submit' form={`pipeline-stages-create-form`}
                        >
                            Create Pipeline
                        </Button>
                    </div>
                }
            />

            <LoadingOverlay isOpen={areStagesCreating || isEditingNewCandidates} />

        </>
    )
}

export default CreateNewPipelineStep2
