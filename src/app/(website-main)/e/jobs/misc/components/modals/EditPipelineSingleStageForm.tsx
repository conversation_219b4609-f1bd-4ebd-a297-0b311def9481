import React, { useMemo } from 'react';
import { useF<PERSON><PERSON><PERSON><PERSON><PERSON>, Controller, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, RadioGroup, Select, SelectSingleCombo, ToolTip } from '@/components/shared';
import { Info } from '@/components/shared/icons';
import useGetAssessments, { Assessment } from '../../api/getAssessments';
import dynamic from 'next/dynamic';
import { cn } from '@/utils';
// import { singleStageSchema } from './schemas';

interface StageFormProps {
    index: number;
    isStagesModalOpen: boolean;
}
const yes_or_no = [
    { name: 'Yes', value: "true" },
    { name: 'No', value: "false" },
]
interface CustomFormErrors {
    stages: Array<{ [key: string]: { message?: string } }>;
}

const StageForm: React.FC<StageFormProps> = ({ index, isStagesModalOpen }) => {
    const ReactQuill = useMemo(() => dynamic(() => import('react-quill'), { ssr: false }), []);

    const { control, setValue, watch, formState: { errors }, clearErrors, register, trigger } = useFormContext();
    const { fields, remove } = useFieldArray({
        name: `stages`,
        control,
    });
    const isOfferStage = watch(`stages.${index}.is_offer_stage`) === 'true';
    const isAssessment = watch(`stages.${index}.is_assessment`) === 'true';
    const isEmailEnabled = watch(`stages.${index}.email_notification_enabled`) === 'true';
    const emailBody = watch(`stages.${index}.email_text`);
    const { data: assessments, isLoading: isFetchingAssessments } = useGetAssessments(isStagesModalOpen);
    const custom_assessments = assessments?.pages.flatMap((page: any) =>
         page.results.map((assessment: Assessment) => ({
             name: assessment.name,
             value: assessment.id,
         }))
     ) as { name: string; value: string }[];

    const customErrors = errors as unknown as CustomFormErrors;
    // const { fields, append, remove } = useFieldArray({
    //     name: 'stages',
    //     control,
    // });
    const saveStage = async () => {
        setValue("currentEdit", -1)
    }
    console.log(customErrors, "customErrors")
    return (
        <div className="rounded-lg pt-4 px-2 my-0">
            <Controller
                name={`stages.${index}.name`}
                control={control}
                render={({ field }) => (
                    <input
                        type="text"
                        placeholder="Enter stage name"
                        className="text-[0.75rem] text-body-text px-3.5 py-2 sm:px-4 sm:py-2.5 w-full border-2 border-transparent rounded-lg focus:border-primary focus:outline-none bg-white transition-all"
                        {...field}
                    />
                )}
            />


            <div className='mt-8'>
            </div>

            <header>
                <h6 className='flex items-center gap-1 text-sm text-header-text font-medium'>
                    Configure stage
                    <ToolTip content='More Info Coming Soon...'>
                        <Info />
                    </ToolTip>
                </h6>
                <p className='text-body-text text-[0.75rem]'>
                    Here you can define the criteria for candidate progression between stages
                    and specify the automated actions you want the system to execute
                </p>
            </header>


            <section className='my-6 ml-4'>
                <h6 className='flex items-center gap-1 text-[0.8rem] text-header-text font-medium'>
                    Assessments and Interviews
                    <ToolTip content='More Info Coming Soon...'>
                        <Info />
                    </ToolTip>
                </h6>

                <RadioGroup
                    name={`stages.${index}.is_interview`}
                    options={yes_or_no}
                    onChange={(value) => setValue(`stages.${index}.is_interview`, value)}
                    label='Is this an interview stage?'
                    radioClass='h-3 !w-3'
                    labelClass='grow !text-[#818181] !text-xs !font-normal'
                    containerClass='!grid grid-cols-[max-content,max-content] items-center gap-4 my-1.5'
                    value={watch(`stages.${index}.is_interview`)}
                    variant="unstyled"
                    size='tiny'
                    className='p-1'
                    itemClass='!text-xs'
                    arrangement='row'
                    errors={errors}
                />
                <RadioGroup
                    name={`stages.${index}.is_assessment`}
                    options={yes_or_no}
                    value={watch(`stages.${index}.is_assessment`)}
                    onChange={(value) => setValue(`stages.${index}.is_assessment`, value)}
                    variant="unstyled"
                    size='tiny'
                    label='Will this stage require an assessment?'
                    radioClass='h-3 !w-3'
                    labelClass='grow !text-[#818181] !text-xs !font-normal'
                    containerClass='!grid grid-cols-[max-content,max-content] items-center gap-4 my-1.5'
                    className='p-1'
                    itemClass='!text-xs'
                    arrangement='row'
                    errors={errors}
                />


                {isAssessment &&
                    <>
                        <SelectSingleCombo
                            label="Assessment"
                            {...register(`stages.${index}.assessment`)}
                            value={watch(`stages.${index}.assessment`)}
                            onChange={(value) => { setValue(`stages.${index}.assessment`, value); clearErrors(`stages.${index}.duration`) }}
                            options={custom_assessments}
                            labelKey='name'
                            valueKey='value'
                            placeholder="Select from created assessments"
                            hasError={Array.isArray(errors?.stages) && !!errors?.stages[index]?.assessment}
                            errorMessage={((errors.stages as unknown) as CustomFormErrors['stages'])?.[index]?.assessment?.message as string}
                            isLoadingOptions={isFetchingAssessments}
                            className='shrink !px-1.5 !py-[0.1rem] bg-white !text-xxs'
                            containerClass='!grid grid-cols-[max-content,max-content] items-center gap-4 my-1.5'
                            labelClass='grow !text-[#818181] !text-xs font-normal'
                            itemClass='!text-xs text-body-text'
                        />

                        <div className='my-[0.875rem]'>
                            <div className='flex flex-wrap items-center gap-2 mt-1.5 text-[#818181] text-xs'>
                                <span>Candidates have  </span>
                                <input type="string" placeholder="0"
                                    className='appearance-none text-xs text-[#818181] p-1  w-max max-w-[50px] border-2 border-transparent rounded-lg focus:border-primary focus:outline-none bg-white transition-all '
                                    {...register(`stages.${index}.duration` as const, { required: true })}
                                    onChange={(e) => {
                                        if (isNaN(Number(e.target.value))) {

                                            setValue(`stages.${index}.duration`, '0')
                                        }
                                        else {
                                            setValue(`stages.${index}.duration`, e.target.value)
                                        }
                                    }
                                    }
                                    value={watch(`stages.${index}.duration`, 0)}
                                />
                                <span>day(s) to take assessment.</span>
                            </div>
                            {errors?.duration && <div className='text-[0.7rem] text-red-500 rounded-sm w-full'>{errors?.duration?.message as string || "This field is required"}</div>}
                        </div>
                    </>
                }
            </section>


            <section className='my-6 ml-4'>
                <h6 className='flex items-center gap-1 text-[0.8rem] text-header-text font-medium'>
                    Automations
                    <ToolTip content='More Info Coming Soon...'>
                        <Info />
                    </ToolTip>
                </h6>

                <RadioGroup
                    name={`stages.${index}.automation_enabled`}
                    options={yes_or_no}
                    onChange={(value) => setValue(`stages.${index}.automation_enabled`, value)}
                    label={
                        <p className='flex items-center gap-0.5'>
                            Enable staging automation
                            <ToolTip content="">
                                <Info width={13} height={13} />
                            </ToolTip>
                        </p>
                    }
                    radioClass='h-3 !w-3'
                    labelClass=' grow !text-[#818181] !text-xs !font-normal'
                    containerClass='!grid grid-cols-[max-content,max-content] items-center gap-4 my-1.5'
                    value={watch(`stages.${index}.automation_enabled`)}
                    variant="unstyled"
                    size='tiny'
                    className='p-1'
                    itemClass='!text-xs'
                    arrangement='row'
                    errors={errors}
                />

                {errors.stages && errors?.automation_enabled && <span className='-mt-4 text-[0.75rem] text-red-500'>{errors?.automation_enabled?.message as string || "This field is required"}</span>}

                <div className='my-1.5'>
                    <div className='flex flex-wrap items-center gap-2 !text-[#818181]  text-xs'>
                        <span>Move candidates with  </span>
                        <input type="number" placeholder="0"
                            className='appearance-none text-xs !text-[#818181]  p-1 w-max max-w-[50px] border-2 border-transparent rounded-lg focus:border-primary focus:outline-none bg-white transition-all '
                            {...register(`stages.${index}.move_criteria` as const, { required: true })}
                            onChange={(e) => setValue(`stages.${index}.move_criteria`, Number(e.target.value))}
                            value={watch(`stages.${index}.move_criteria`, 50)}
                        />
                        <span>% match to the next stage</span>
                    </div>

                    {errors.stages && errors?.move_criteria && <span className='text-[0.8rem] text-red-500'>{errors?.move_criteria?.message as string || "This field is required"}</span>}

                </div>
            </section>


            <section className='my-6 ml-4'>
                <h6 className='flex items-center gap-1 text-[0.8rem] text-header-text font-medium'>
                    Notifications
                    <ToolTip content='More Info Coming Soon...'>
                        <Info />
                    </ToolTip>
                </h6>
                <RadioGroup
                    name={`stages.${index}.email_notification_enabled`}
                    options={
                        [
                            { name: 'Yes', value: 'true' },
                            { name: 'No', value: 'false' },
                        ]
                    }
                    onChange={(value) => setValue(`stages.${index}.email_notification_enabled`, value)}
                    label={
                        <span className='flex items-center gap-1'>
                            Will this stage require an email to be sent?
                            <ToolTip content='More Info Coming Soon...'>
                                <Info />
                            </ToolTip>
                        </span>
                    }
                    radioClass='h-3 !w-3'
                    labelClass='grow text-body-text !text-xs !font-normal'
                    containerClass='!grid grid-cols-[max-content,max-content] items-center gap-4 my-1.5'
                    value={watch(`stages.${index}.email_notification_enabled`)}
                    variant="unstyled"
                    size='tiny'
                    className='p-1'
                    itemClass='!text-xs'
                    arrangement='row'
                    errors={errors}
                />

                {
                    isEmailEnabled &&
                    <>


                        <div className={cn('inputdiv mt-[0.875rem] mb-[0.25rem] border-[1.75px] p-2 border-helper-text rounded-xl',
                            (errors?.email_text || errors?.email_subject) && "border-red-500"
                        )}>
                            <input type="text" placeholder="Enter email subject"
                                className={cn('appearance-none !text-xs w-max border-2 border-transparent rounded-lg focus:border-primary focus:outline-none bg-white transition-all ',)}
                                {...register(`stages.${index}.email_subject` as const, { required: true })}
                                onChange={(e) => setValue(`stages.${index}.email_subject`, e.target.value)}
                                value={watch(`stages.${index}.email_subject`, "")}
                            />
                            <Controller
                                name={`stages.${index}.email_text`}
                                control={control}
                                defaultValue=""
                                render={({ field }) => (
                                    <ReactQuill
                                        value={field.value}
                                        onChange={field.onChange}
                                        onBlur={field.onBlur}
                                        defaultValue={emailBody || ""}
                                        modules={{
                                            toolbar: {
                                                container: [
                                                    [{ 'header': 1 }, { 'header': 2 }], // Header formats
                                                    // [{ 'size': ['extra-small', 'small', 'medium', 'large'] }], // Size format
                                                    ['bold', 'italic', 'underline', 'strike'], // Existing formats
                                                    [{ 'list': 'ordered' }, { 'list': 'bullet' }], // List formats
                                                    [{ 'indent': '-1' }, { 'indent': '+1' }], // Indent formats
                                                    [{ 'align': [] }], // Alignment format
                                                    ['link',], // Other formats
                                                    ['clean'] // Remove formatting
                                                ]
                                            }
                                        }}

                                        id="email-subject-edit-pipeline"
                                        className={cn(`w-full rounded-lg bg-white outline-none min-h-[250px]`)}
                                        placeholder='Enter email body'
                                        style={{ border: "none", outline: "none" }}
                                    />
                                )}
                            />
                        </div>
                    </>
                }
                {errors.stages && (errors?.email_text || errors?.email_subject) && <span className='text-[0.7rem] text-red-500 leading-tight'>{errors?.email_subject?.message as string || errors?.email_text?.message as string || "This field is required"}</span>}


                <div className='flex items-center gap-4 justify-end mt-6'>
                    {/* {
                        item.name == "Newest Stage" &&
                        <Button size='tiny' variant='unstyled' className='bg-white hover:bg-red-500 hover:text-white rounded-lg'
                            onClick={() => { remove(index); setValue("currentEdit", -1) }}
                        >
                            Delete
                        </Button>
                    } */}
                    <Button size='tiny' onClick={saveStage}>
                        Save
                    </Button>
                </div>
            </section>


            {/* {errors.stages?.[index]?.name && <span className="text-red-500 text-xs">{errors.stages[index].name.message}</span>} */}

            {/* Render other form fields for the stage */}
        </div>
    );
};

export default StageForm;