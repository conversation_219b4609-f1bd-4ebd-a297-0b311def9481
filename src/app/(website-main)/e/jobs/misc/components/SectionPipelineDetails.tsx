import React, { useEffect, useState } from 'react'
import { format } from 'date-fns'

import { useBooleanStateControl } from '@/hooks'
import { Button } from '@/components/shared'

import { StageDetailsCard } from './cards/StageDetailsCard'
import { Pipeline, Stage } from '../types'
import EditPipeline from './modals/EditPipeline'


interface Props {
    pipeline: Pipeline
}
const PipelineDetails: React.FC<Props> = ({ pipeline }) => {
    const {
        state: isEditStagesModalOpen,
        setTrue: openEditStagesModal,
        setFalse: closeEditStagesModal
    } = useBooleanStateControl()
    const [pipe, setLocalPipeline] = useState(pipeline)
    useEffect(() => {
        setLocalPipeline(pipeline)
    }, [pipeline])


    return (
        <section className="relative flex flex-col max-lg:hidden w-full bg-transparent border-dashed border-[1.7px] border-primary  border-spacing-3 rounded-[1.25rem] overflow-y-scroll pb-0 max-md:pb-16">
            <header className='sticky top-0 z-[2] pt-6 pb-4 px-6 bg-[#F8F9FB]  drop-shadow-sm'>
                <h3 className='text-base  text-header-text font-medium'>{pipeline.name}</h3>
                <div className='flex items-center flex-wrap gap-6 '>
                    <p className='text-[#7D8590] text-[0.75rem]'>Pipeline stages: <span className='text-[#0E0E2C]'>{pipeline.stages.length}</span></p>
                    <p className='text-[#7D8590] text-[0.75rem]'>Created: <span className='text-[#0E0E2C]'>{format(new Date(pipeline.created_at), "dd/MM/yyyy")}</span></p>
                </div>
            </header>

            <div className='flex flex-col gap-4 px-6 pt-3'>
                <h4 className='text-[0.875rem] text-header-text font-medium'>Stages:</h4>
                {
                    [...pipeline.stages].sort((a, b) => (a.order || 0) - (b.order || 0)).map((stage: Stage, index: number) => {
                        return (
                            <StageDetailsCard pipeline={pipeline} stage={stage} key={index} />
                        )
                    })
                }
            </div>

            <footer className='sticky bottom-0 flex items-center justify-end p-4 px-6'>
                {
                    pipeline.is_default == false && pipeline.name != "Getlinked Default Pipeline" &&
                    <>
                        <Button onClick={() => openEditStagesModal()}>
                            Edit Pipeline
                        </Button>
                        <EditPipeline isStagesModalOpen={isEditStagesModalOpen} pipeline={pipe} closeStagesModal={closeEditStagesModal} />
                    </>
                }
            </footer>
        </section>
    )
}

export default PipelineDetails