import { TimelineEvent } from '@/app/(website-main)/e/jobs/misc/components/job-detail/Timeline';
import { z } from 'zod';
import { singleStageSchema } from '../utils/pipelineSchemas';
import { ActiveJob } from './JobType';

export interface submittedCustomRequirements {
  custom_text?: string;
  custom_field_name: string;
  custom_files?: {
    custom_file_id: string;
    custom_file_url: string;
  };
}

export type CreatePipeline = {
  name: string;
  is_default: 'False';
};

export type CreateStage = {
  name: string;
  move_criteria?: number;
  is_assessment: boolean | string;
  automation_enabled: boolean | string;
  assessment?: null | number | string;
  order: number | string;
};
export type CreatePipelineStages = {
  stages: CreateStage[];
  id: number;
};
// export type Stage = {
//     id?: number | string
//     name: string,
//     is_new_candidate?:boolean
//     move_criteria?: number,
//     is_assessment: boolean | string,
//     is_offer_stage?: boolean | string
//     is_interview: boolean | string,
//     interview?: null | string
//     assessment?: null | string
//     automation_enabled: boolean | string,
//     email_notification_enabled?: boolean | string,
//     email_subject?: string,
//     email_body?: string,
//     offer_email_subject?: string,
//     offer_email_body?: string,
//     offer_cover_image?: string,
//     offer_attachment_url?: string,
//     offer_setup_customization?: any;
//     email_text?: string,
//     duration?: undefined | number | string
//     order: number
//     applicant_counts?: number
//     offer_letter_send_automatic?: boolean | string
//     offer_letter_manual_review?: boolean | string
// }
export interface Reminder {
  title: string;
  content: string;
  days_before: number;
}

export interface Stage {
    id: number;
    name: string;
    move_criteria?: number;
    is_new_candidate?: boolean;
    is_assessment?: boolean;
    is_interview?: boolean;
    is_offer_stage?: boolean;
    offer_letter_send_automatic?: boolean;
    offer_letter_manual_review?: boolean;
    offer_setup_customization?: {
        offer_letter: string; // base64 string
        attachments: any[]; // Adjust type if known
        attachments_file_path: string[];
    } | null;
    offer_email_subject?: string | null;
    offer_email_body?: string | null;
    offer_cover_image?: string | null;
    offer_attachment_url?: string | null;
    reminders?: Reminder[] | null;
    enable_automated_reminders?: boolean;
    enable_onboarding_setup?: boolean;
    rejection_setup_customization?: {
        email_message: string
        email_subject: string
        email_cover_image?: string

    } | null;
    customize_rejection_message?: boolean;
    assessment?: any | null;
    interview?: any | null;
    duration?: number;
    order?: number;
    email_subject?: string | null;
    email_body?: string | null;
    email_notification_enabled?: boolean;
    email_text?: string | null;
    applicant_count?: number | null;
    automation_enabled?: boolean;
    allow_invites_for_future_roles?: boolean;
    future_invites_data?: {
        email_subject: string,
        email_body: string,
    }
}

export type OnboardingStep = {
    title: string;
    content: string;
    order: number;
    attachments: string;

}

export type EditSinglePipelineStageType = z.infer<typeof singleStageSchema>;
// export type EditSinglePipelineStageType = Stage;

export interface Pipeline {
  id: number;
  name: string;
  stages: Stage[];
  is_default: boolean;
  created_at: string;
}

export interface PipelineDataResult {
  result_data: PipelineColumn[];
  unique_roles: string[];
  unique_skills: string[];
  unique_tools: string[];
}
export interface PipelineColumnData {
  stage_id: number;
  stage_name: string;
  move_criteria: number;
  is_assessment: boolean;
  is_interview: boolean;
  is_offer_stage: boolean;
  offer_letter_send_automatic: boolean;
  offer_letter_manual_review: boolean;
  automation_enabled: boolean;
  order: number;
  assessment: string | null;
  interview: string | null;
  email_notification_enabled: boolean;
  email_subject: undefined | string;
  email_body: undefined | string;
  email_text: undefined | string;
  offer_email_subject?: string;
  offer_email_body?: string;
  offer_cover_image?: string;
  offer_attachment_url?: string;
  duration: number;
  count: number;
}
export interface PipelineColumn {
  stage_id: number;
  stage_name: string;
  move_criteria: number;
  is_assessment: boolean;
  is_interview: boolean;
  is_offer_stage: boolean;
  offer_letter_send_automatic: boolean;
  offer_letter_manual_review: boolean;
  automation_enabled: boolean;
  order: number;
  assessment: string | null;
  interview: string | null;
  email_notification_enabled: boolean;
  email_subject: undefined | string;
  email_body: undefined | string;
  email_text: undefined | string;
  offer_email_subject?: string;
  offer_email_body?: string;
  offer_cover_image?: string;
  offer_attachment_url?: string;
  duration: number;
  data: PipelineCandidate[];
  count: number;

    customize_rejection_message: boolean;
    offer_setup_customization: any;
    rejection_setup_customization?: {
        email_message: string
        email_subject: string
        email_cover_image: string
    } | null;
    enable_onboarding_setup: boolean;
    enable_automated_reminders: boolean;
    reminders: Reminder[] | null;
    total_pages: number;
    current_page: number;
    next: string | null;
    previous: string | null;
};

export interface InterviewPhaseScore {
  score: number;
  completed_at: string;
  score_reason: string;
  responses_count: number;
}

export interface InterviewScoresData {
  phases: {
    final: InterviewPhaseScore;
    initial: InterviewPhaseScore;
    technical: InterviewPhaseScore;
  };
  last_updated: string;
  overall_score: number;
  scoring_source: string;
  total_phases_completed: number;
}

export interface PipelineCandidate {
  id: number;
  name: string | null;
  email: string;
  phone_number: string | null;
  years_of_experience: string;
  current_location: string;
  percentage_match: number;
  matching_reason: string | null;
  created_at: string;
  trail: TimelineEvent[];
  cv_role: string | null;
  status: string | null;
  is_seen: boolean;
  is_viewed: boolean;
  cv_parser_id: number;
  custom_job_requirements: submittedCustomRequirements[];
  current_stage: PipelineColumnData;
  combined_score: number;
  interview_overall_score: number;
  interview_scores_data: InterviewScoresData;
  interview_last_scored_at: string;
}

export interface CandidateDetails {
  id: number;
  name: string;
  email: string;
  years_of_experience: string;
  current_location: string;
  phone_number: string;
  about_me: string | null;
  gender: string | null;
  portfolio_link: string | null;
  linkedin_link: string | null;
  cover_letter_text: string;
  cover_letter_file: string;
  custom_job_requirements: submittedCustomRequirements[];
  cv: string;
  cv_data: string | null;
  cv_file_path: string | null;
  percentage_match: number;
  matching_reason: string;
  strength: string | null;
  weakness: string | null;
  cv_and_resume_id: string;
  cover_letter_id: string | null;
  expected_salary: string;
  is_talent: boolean;
  unique_id: string;
  job_stage: string;
  created_at: string;
  updated_at: string;
  trail: TimelineEvent[];
  is_seen: boolean;
  job: ActiveJob;
  combined_score?: number;
  interview_overall_score?: number;
  interview_scores_data?: InterviewScoresData | null;
}

export interface ApplicantAssessmentResult {
  id: string;
  candidate_email: string;
  candidate_name: string;
  total_score: number;
  overall_percentage: string;
  invite_date: Date;
  date_taken: Date;
  status: string;
  assessment: {
    id: string;
    name: string;
    description: string;
  };
  job_application: number;
}
