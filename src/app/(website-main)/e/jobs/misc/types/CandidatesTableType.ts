import { TimelineEvent } from "../components/job-detail/Timeline";
import { ActiveJob } from "./JobType";
import { submittedCustomRequirements } from "./PipelineType";


export interface CTableCandidate {
    id: number;
    name: string;
    email: string;
    years_of_experience: string;
    current_location: string;
    phone_number: string;
    about_me: string | null;
    gender: string | null;
    portfolio_link: string | null;
    linkedin_link: string | null;
    cover_letter_text: string;
    cover_letter_file: string;
    custom_job_requirements: submittedCustomRequirements[];
    cv: string;
    cv_data: string;
    cv_role: string;
    percentage_match: number;
    matching_reason: string;
    cv_and_resume_id: number | null;
    cover_letter_id: number | null;
    expected_salary: string;
    is_talent: boolean;
    unique_id: string;
    job_stage: string;
    trail: TimelineEvent[];
    status: string | null;
    is_existing_in_cv_parser_tbl: boolean;
    is_seen: boolean;
    created_at: string;
    updated_at: string;
    job: ActiveJob;
    pipeline: number | null;
}