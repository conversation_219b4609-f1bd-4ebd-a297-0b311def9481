import { Client } from '../../../my-clients/misc/types';
import { JobConfiguration } from '../api/jobConfiguration';
import { Pipeline } from './PipelineType';

export interface customField {
  fieldTitle: string;
  fieldValue: string;
}
export interface customRequirement {
  fieldTitle: string;
  fieldValue?: string;
  fieldType: 'FILE_UPLOAD' | 'TEXT_INPUT';
}

export interface CreateJob {
  accept_personal_details: boolean;
  accept_resume: boolean;
  accept_cover_letter: boolean;
  industry: string | undefined;
  name: string | undefined;
  job_type: string | undefined;
  company_overview: string | undefined;
  location: string | undefined;
  is_multi_location: boolean;
  preferred_locations: string[];
  salary_currency: 'NGN' | 'USD' | 'GBP' | 'EUR' | undefined;
  salary_type: 'FIXED' | 'RANGE' | 'UNDISCLOSED';
  max_salary: number | undefined;
  min_salary: number | undefined;
  fixed_salary: number | undefined;
  salary_negotiable: boolean;
  proficiency_level: string | undefined;
  description: string | undefined;
  responsibilities: string | undefined;
  requirements: string | undefined;
  compulsory_requirements?: string | undefined;
  added_advantage?: string | undefined;
  work_experience: string | undefined;
  working_option: string | undefined;
  application_start_date: string | Date | undefined;
  application_deadline: string | Date | undefined;
  pipeline_type: string | undefined;
  pipeline?: string | undefined;
  team_member?: string[] | number[] | undefined;
  show_in_career_page: boolean;
  post_as_anonymous: boolean;
  job_status?: string;
  logo?: string | null;
  job_custom_fields: customField[] | undefined;
  viewers_count?: number;
  job_requirement_custom_fields: customRequirement[] | undefined;
}

export interface CreateDraftJob {
  accept_personal_details: boolean;
  accept_resume: boolean;
  accept_cover_letter: boolean;
  industry: string | undefined;
  name: string | undefined;
  job_type: string | undefined;
  company_overview: string | undefined;
  location: string | undefined;
  is_multi_location: boolean;
  preferred_locations: string[];
  salary_currency: 'NGN' | 'USD' | 'GBP' | 'EUR' | undefined;
  salary_type?: 'FIXED' | 'RANGE' | 'UNDISCLOSED';
  fixed_salary?: number | undefined;
  max_salary: number | undefined;
  min_salary: number | undefined;
  salary_negotiable?: boolean;
  proficiency_level: string | undefined;
  description: string | undefined;
  responsibilities: string | undefined;
  requirements: string | undefined;
  compulsory_requirements?: string | undefined;
  added_advantage?: string | undefined;
  work_experience: string | undefined;
  working_option: string | undefined;
  application_start_date: string | Date | undefined;
  application_deadline: string | Date | undefined;
  pipeline_type: string | undefined;
  pipeline?: Pipeline | undefined;
  show_in_career_page?: boolean;
  team_member?: string[] | number[] | undefined;
  post_as_anonymous: boolean;
  viewers_count?: number;
  job_status?: string;
  logo?: string | null;
  job_custom_fields: customField[] | undefined;
  job_requirement_custom_fields: customRequirement[] | undefined;
}

export interface ActiveJob {
  id: number;
  unique_id: string;
  job_title: string;
  description: string;
  responsibilities: string;
  requirements: string;
  compulsory_requirements?: string | undefined;
  added_advantage?: string | undefined;
  proficiency_level: string;
  working_option: string;
  work_experience: string;
  job_type: string;
  industry: string;
  industry_category: string;
  job_tags: string[];
  location: string;
  is_multi_location: boolean;
  preferred_locations: string[];
  salary_type: 'FIXED' | 'RANGE' | 'UNDISCLOSED';
  fixed_salary?: number | undefined;
  salary_range: string;
  min_salary?: number;
  max_salary?: number;
  salary_currency: 'NGN' | 'USD' | 'GBP' | 'EUR';
  salary_negotiable?: boolean;
  qualifications: null | string;
  skills_required: null | string[];
  application_start_date: Date | string;
  application_deadline: Date | string;
  accpting_applications: boolean;
  benefits: null | string[];
  job_status: string;
  logo: string | null;
  show_in_career_page: boolean;
  pipeline_type: string;
  job_custom_fields: customField[];
  company_overview: string;
  created_at: string;
  updated_at: string;
  post_as_anonymous: boolean;
  viewers_count: number;
  exclusion_list: null | string;
  type_of_assessment: string;
  boosted?: boolean;
  job_id?: string;
  job_application_requirement_custom_fields: null;
  job_requirement_custom_fields: customRequirement[];
  client?: Client;
  requirement_custom_fields: {
    id: number;
    accept_personal_details: boolean;
    accept_resume: boolean;
    accept_cover_letter: boolean;
    custom_fields: customRequirement[];
    created_at: string;
    updated_at: string;
  };
  accept_cover_letter: boolean;
  job_openining_expiration_date: string;
  accept_resume: boolean;
  accept_personal_details: boolean;
  talent: any[];
  bookmarks: any[];
  team_member: {
    id: number;
    company_email: string;
    verified: boolean;
    type_of_recruiter: string;
    profile_picture: string | null;
    role: string | null;
    created_at: string;
    updated_at: string;
    user: string;
  }[];
  applicant: any[];
  company: {
    id: number;
    name: string;
    address: string;
    website: string;
    industry: string;
    size: string;
    description: string;
    logo: string;
    type_of_company: string;
    company_domain: string;
    created_at: string;
  };
  pipeline?: Pipeline;
  stage_data: {
    total_count: number;
    data: {
      [key: string]: number;
    };
  };
  hiring_metrics?: {
    application_conversion_rate?: number | null;
    applicant_quality_rate?: number | null;
    interview_to_hire_ratio?: number | null;
  };

  total_applicants: number | null;
  recruiter: {
    id: number;
    company_email: string;
    verified: boolean;
    type_of_recruiter: string;
    created_at: string;
    updated_at: string;
    user: {
      id: string;
      first_name: string;
      middle_name: null | string;
      last_name: string;
      email: string;
      phone_number: string;
      date_of_birth: null | string;
      gender: string;
      user_type: string;
    };
    company: {
      id: number;
      name: string;
      address: string;
      website: string;
      industry: string;
      size: string;
      description: string;
      logo: string;
      type_of_company: string;
      company_domain: string;
      created_at: string;
    };
  };
  configuration: JobConfiguration;
}

export interface DraftJob {
  id?: number;
  unique_id?: string;
  job_title?: string;
  description?: string;
  responsibilities?: string;
  requirements?: string;
  compulsory_requirements?: string;
  added_advantage?: string;
  proficiency_level?: string;
  working_option?: string;
  work_experience?: null | string;
  job_type?: string;
  industry?: null | string;
  industry_category?: null | string;
  job_tags?: null | string[];
  location?: string;
  is_multi_location?: boolean;
  preferred_locations?: string[];
  salary_range?: string;
  min_salary?: number;
  max_salary?: number;
  salary_currency?: string;
  salary_negotiable?: boolean;
  qualifications?: null | string;
  skills_required?: null | string[];
  application_start_date?: null | Date | string;
  application_deadline?: null | Date | string;
  accpting_applications?: boolean;
  benefits?: null | string[];
  job_status?: string;
  logo?: string | null;
  show_in_career_page?: boolean;
  pipeline_type?: string | undefined;
  pipeline?: Pipeline | undefined;
  team_member?: string[] | number[] | undefined;
  job_custom_fields?: customField[];
  viewers_count?: number;
  company_overview?: string;
  created_at?: string;
  updated_at?: string;
  post_as_anonymous?: boolean;
  job_requirement_custom_fields?: customRequirement[];
  requirement_custom_fields?: {
    id: number;
    accept_personal_details: boolean;
    accept_resume: boolean;
    accept_cover_letter: boolean;
    custom_fields: customRequirement[];
    created_at: string;
    updated_at: string;
  };
  accept_cover_letter?: boolean;
  accept_resume?: boolean;
  accept_personal_details?: boolean;
  talent?: any[];
  bookmarks?: any[];
  applicant?: any[];
  company?: {
    id?: number;
    name?: string;
    address?: string;
    website?: string;
    industry?: string;
    size?: string;
    description?: string;
    logo?: string;
    type_of_company?: string;
    company_domain?: string;
    created_at?: string;
  };
  recruiter?: {
    id?: number;
    company_email?: string;
    verified?: boolean;
    type_of_recruiter?: string;
    created_at?: string;
    updated_at?: string;
    user?: {
      id?: string;
      first_name?: string;
      middle_name?: null | string;
      last_name?: string;
      email?: string;
      phone_number?: string;
      date_of_birth?: null | string;
      gender?: string;
      user_type?: string;
    };
    company?: {
      id?: number;
      name?: string;
      address?: string;
      website?: string;
      industry?: string;
      size?: string;
      description?: string;
      logo?: string;
      type_of_company?: string;
      company_domain?: string;
      created_at?: string;
    };
  };
}
export interface ShareToTalent {
  emails: string;
  shareType: 'talent' | 'client';
  viewAccess?: 'full' | 'limited';
}

export interface ShareToClient {
  emails: string;
  viewAccess: 'full' | 'limited';
}

export interface ShareJobModalProps {
  isOpen: boolean;
  onClose: () => void;
  jobId: string;
  jobLink: string;
  shareType: 'talent' | 'client';
}
