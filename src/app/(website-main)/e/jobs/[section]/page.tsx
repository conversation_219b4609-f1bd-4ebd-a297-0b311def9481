'use client';

import React from 'react';
import { AppHeader<PERSON><PERSON><PERSON><PERSON>, LinkButton, Tab } from '@/components/shared';
import { AllJobs, Drafts, Settings } from '../misc/components';
import { PlusIcon } from '../misc/icons';




const RecruiterJobsView = ({ params }: { params: { section: string }; }) => {
  const { section } = params;

  const tabsArray = [
    {
      id: 1,
      title: 'Jobs',
      link: './all',
      component: <AllJobs />,
    },
    {
      id: 2,
      title: 'Job drafts',
      link: './drafts',
      component: <Drafts />,
    },
    {
      id: 3,
      title: 'Job settings',
      link: './settings',
      component: <Settings />,
    },
  ];

  return (
    <main className="main">
      <div className='maincontentwithheader grid grid-rows-[max-content_1fr] flex-col bg-[#F8F9FB] h-full overflow-y-hidden '>
        <AppHeaderRecruiter title='Jobs' className='bg-white' />
        <Tab
          fallback='all'
          listClass="md:px-6"
          className='md:py-2'
          currentTab={section}
          catgoryArray={tabsArray}
          sideButton={<LinkButton href='./create' variant={'default'} icon={<PlusIcon width={20} height={20} />}>Create Job</LinkButton>}
        />
      </div>
    </main>
  )
}


export default RecruiterJobsView
