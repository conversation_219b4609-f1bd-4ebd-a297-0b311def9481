'use client';

import { useParams, useRouter } from 'next/navigation';
import { AppHeaderRecruiter } from '@/components/shared';
import useGetJobDetails from '../../../misc/api/getJobDetails';
import AIJobConfiguration from '../../../misc/components/job/configure/AIJobConfiguration';

// Loading component
const LoadingSpinner = () => (
  <div className="flex min-h-[400px] items-center justify-center">
    <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
  </div>
);

// Error component
const ErrorMessage = ({
  message,
  onRetry,
}: {
  message: string;
  onRetry: () => void;
}) => (
  <div className="flex min-h-[400px] flex-col items-center justify-center space-y-4">
    <div className="text-center text-red-500">
      <h3 className="mb-2 text-lg font-medium">Error Loading Configuration</h3>
      <p className="text-sm">{message}</p>
    </div>
    <button
      onClick={onRetry}
      className="rounded-md bg-primary px-4 py-2 text-white transition-colors hover:bg-primary/90"
    >
      Try Again
    </button>
  </div>
);

const AIConfigurationPage = () => {
  const params = useParams();
  const router = useRouter();
  const jobUniqueId = params.id as string;

  // Validate that we have a job ID
  if (!jobUniqueId) {
    router.push('/e/jobs');
    return null;
  }

  // Fetch job details to verify job exists
  const {
    data: jobDetails,
    isLoading: isLoadingJob,
    error: jobError,
  } = useGetJobDetails(jobUniqueId);

  const handleCancel = () => {
    router.push(`/e/jobs/all`);
  };

  // Show loading state
  if (isLoadingJob) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-4xl py-8">
          <LoadingSpinner />
        </div>
      </div>
    );
  }

  // Show error state
  if (jobError) {
    const errorMessage =
      jobError instanceof Error
        ? jobError.message
        : 'An unexpected error occurred while loading the job details.';

    return (
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-4xl py-8">
          <ErrorMessage
            message={errorMessage}
            onRetry={() => window.location.reload()}
          />
        </div>
      </div>
    );
  }

  // Show job header info
  const jobTitle = jobDetails?.job_title || 'Job Configuration';

  return (
    <div className="min-h-screen bg-gray-50">
      <AppHeaderRecruiter title="Jobs" className="!sticky top-0" />

      <div className="py-8">
        <AIJobConfiguration
          jobUniqueId={jobUniqueId}
          jobId={jobDetails?.id?.toString() || ''}
          onCancel={handleCancel}
        />
      </div>
    </div>
  );
};

export default AIConfigurationPage;
