'use client';

import moment from 'moment';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import {
  AppH<PERSON>erR<PERSON>ruiter,
  Button,
  LineTabs,
  LinkButton,
  ToolTip,
} from '@/components/shared';
import { <PERSON><PERSON><PERSON>, Spinner } from '@/components/shared/icons';
import { useBooleanStateControl } from '@/hooks';
import { cn } from '@/utils';
import { convertToTitleCase } from '@/utils/strings';
import { useGetJobDetails, useRescoreCandidates } from '../../../misc/api';
import {
  JobCandidatesTable,
  JobDetails,
  JobPipeline,
  JobPipelineConfig,
  Performance,
} from '../../../misc/components/job-detail';
import JobChatInterface from '../../../misc/components/job-detail/JobChatInterface';
import { BulkUploadModal } from '../../../misc/components/job-detail/modals';
import { SharePopover } from '../../../misc/components/job-detail/modals/ShareJobPopover';
import { CopyIcon, EmptyJobIcon } from '../../../misc/icons';
import {
  ActiveJob,
  customRequirement,
  Pipeline as PipelineType,
} from '../../../misc/types';

const RecruiterJobDetailView = ({
  params,
}: {
  params: { id: string; section: string };
}) => {
  const { id: unique_id, section } = params;
  const {
    data: jobDetail,
    isLoading: isFetchingJobData,
    refetch,
  } = useGetJobDetails(unique_id!);

  const refetchJobDetails = () => {
    refetch();
  };

  const tabsArray = [
    {
      id: 1,
      title: 'Candidates',
      link: './candidates',
      component: <JobCandidatesTable unique_id={unique_id!} />,
    },
    {
      id: 2,
      title: 'Pipeline',
      link: './pipeline',
      component: (
        <JobPipeline
          id={Number(jobDetail?.id!)}
          pipeline_id={jobDetail?.pipeline?.id!}
          unique_id={unique_id}
          job_custom_requirements={
            jobDetail?.requirement_custom_fields?.custom_fields ||
            ([] as customRequirement[])
          }
        />
      ),
    },
    {
      id: 3,
      title: 'Job details',
      link: './details',
      component: (
        <JobDetails
          job={jobDetail || ({} as ActiveJob)}
          refetch={refetchJobDetails}
        />
      ),
    },
    {
      id: 4,
      title: 'Pipeline config',
      link: './config',
      component: (
        <JobPipelineConfig
          pipeline={jobDetail?.pipeline || ({} as PipelineType)}
        />
      ),
    },
    {
      id: 5,
      title: 'Performance',
      link: './performance',
      component: <Performance job={jobDetail || ({} as ActiveJob)} />,
    },
    {
      id: 6,
      title: 'Chat',
      link: './chat',
      component: <JobChatInterface unique_id={unique_id!} />,
    },
  ];

  const { job_status, created_at } = jobDetail || {};
  const copyJobLink = (link: string) => {
    if (typeof navigator !== undefined) {
      navigator.clipboard.writeText(link);
      toast.success('Job link copied successfully');
    }
  };

  const { mutate: rescore } = useRescoreCandidates(Number(jobDetail?.id));
  const rescoreAllCandidates = () => {
    rescore(
      {
        job_id: Number(jobDetail?.id!),
        calculate_all: true,
      },
      {
        onSuccess(data, variables, context) {
          toast.success(
            'Rescoring underway...candidates scores will be updated soon.'
          );
        },
        onError(error) {
          toast.error('Something went wrong');
        },
      }
    );
  };

  const {
    state: isBulkUploadModalOpen,
    setTrue: openBulkUploadModal,
    setFalse: closeBulkUploadModal,
  } = useBooleanStateControl();

  return (
    <div className="main">
      <div className="maincontentwithheader grid max-h-full grid-rows-[max-content,1fr] overflow-y-hidden">
        <AppHeaderRecruiter title="Jobs" />
        {isFetchingJobData ? (
          <Spinner />
        ) : !isFetchingJobData && !jobDetail ? (
          <article className="flex items-center justify-center">
            <div className="via-primary-[#D9D9D9] my-[1vh] mb-10 mt-5 flex w-[90%] max-w-[400px] flex-col items-center  justify-center rounded-xl bg-gradient-to-tr from-primary-light to-transparent px-8 py-12 text-center sm:p-16 sm:py-20">
              {/* <StrokeClose width={105} height={105} stroke='#755AE2' strokeWidth={2} className='mt-5 mb-10'/> */}
              <EmptyJobIcon />
              <h3 className="text-xl font-medium ">Job not found</h3>
              <p className="text-sm">
                This job has either been deleted, doesn&apos;t exist or you are
                not authorized to view its details.
              </p>
              <LinkButton
                size="capsule"
                variant="light"
                href="/e/jobs/all"
                className="mt-6"
              >
                Go Back
              </LinkButton>
            </div>
          </article>
        ) : (
          <main className="flex flex-col overflow-hidden">
            <div className="mb-2 h-[0.3rem] w-full rounded-full bg-[#F8F9FB] max-md:hidden"></div>

            <div className="flex items-start gap-4 xl:items-center max-md:p-4 ">
              <LinkButton size="capsule" variant="light" href="/e/jobs/all">
                Back
              </LinkButton>

              <section className="flex flex-wrap items-start lg:items-center lg:gap-2 2xl:gap-12 max-lg:flex-col">
                <h3 className="font-medium text-header-text">
                  {jobDetail?.job_title}
                </h3>
                <div className="flex min-w-max items-center  gap-3 text-[0.8325rem]">
                  <p className="text-[#8C8CA1] ">
                    Applicants:
                    <span className="ml-1 font-medium text-body-text">
                      {jobDetail?.stage_data.total_count}
                    </span>
                  </p>
                  <p className="text-[#8C8CA1] max-lg:hidden">
                    Date Created:
                    <span className="ml-1 font-medium text-body-text">
                      {moment(created_at).format('D MMM YYYY')}
                    </span>
                  </p>
                  <p className="text-[#8C8CA1] max-lg:hidden">
                    Status:
                    <span
                      className={cn(
                        'ml-2 font-medium',
                        job_status == 'OPEN' && 'text-[#12B669]',
                        job_status == 'DRAFT' && 'text-primary',
                        job_status == 'CLOSED' && 'text-[#FF5F56]',
                        job_status == 'QUEUED' && 'text-[#7d7d7f]'
                      )}
                    >
                      {convertToTitleCase(job_status!!)}
                    </span>
                  </p>
                </div>
              </section>

              <div className="ml-auto flex items-center gap-2">
                <ToolTip
                  asChild
                  content="Recalculate the score of all candidats in the pipeline"
                >
                  <Button
                    size="tiny"
                    variant="extralight"
                    className="text-xs  !font-medium max-sm:hidden"
                    onClick={rescoreAllCandidates}
                    icon={<Rotate fill="#755AE2" height={18} width={18} />}
                    iconPosition="after"
                  >
                    <span>Rescore</span>
                  </Button>
                </ToolTip>

                <ToolTip
                  asChild
                  content="Upload Bulk CVs of candidates you want to add to the pipeline"
                >
                  <Button
                    size="tiny"
                    variant="extralight"
                    className="w-max  text-xs !font-medium max-sm:hidden"
                    onClick={openBulkUploadModal}
                  >
                    <span>Bulk add candidates</span>
                  </Button>
                </ToolTip>

                <Button
                  size="tiny"
                  variant="extralight"
                  className="!min-w-max text-xs !font-medium max-lg:hidden"
                  onClick={() =>
                    copyJobLink(`${window.location.origin}/jobs/${unique_id}`)
                  }
                  icon={<CopyIcon />}
                  iconPosition="after"
                >
                  Copy link
                </Button>

                <SharePopover job_id={unique_id} />

                <Button
                  size="icon"
                  variant="extralight"
                  className="lg:hidden"
                  onClick={() =>
                    copyJobLink(`${window.location.origin}/jobs/${unique_id}`)
                  }
                >
                  <CopyIcon />
                </Button>
              </div>
            </div>

            <LineTabs
              className="max-w-screen grid max-h-full grow grid-rows-[max-content,1fr] overflow-y-hidden"
              tabListClassName="md:!mt-2"
              fallback="candidates"
              currentTab={section}
              catgoryArray={tabsArray}
            />
          </main>
        )}
      </div>

      <BulkUploadModal
        isOpen={isBulkUploadModalOpen}
        closeModal={closeBulkUploadModal}
        job_id={jobDetail?.id!}
      />
    </div>
  );
};

export default RecruiterJobDetailView;
