import React from 'react';
import { AppHeaderRecruiter, Tab } from '@/components/shared';
import ProTalents from '../misc/components/ProTalents';
import VerifiedInterns from '../misc/components/VerifiedInterns';

const TalentPoolPage = ({ params }: { params: { section: string } }) => {
  const { section } = params;
  const tabsArray = [
    {
      id: 1,
      title: 'Pro Talents',
      link: './pro-talents',
      component: <ProTalents />,
    },
    {
      id: 2,
      title: 'Verified Interns',
      link: './verified-interns',
      component: <VerifiedInterns />,
    },
  ];
  return (
    <main className="main">
      <div className="maincontentwithheader grid h-full grid-rows-[max-content_1fr] flex-col overflow-y-hidden bg-[#F8F9FB] ">
        <AppHeaderRecruiter title="Talent pool" className="bg-white md:!pt-2" />
        <div>
          <Tab
            fallback="pro-talents"
            listClass="md:px-6"
            className="md:py-2"
            currentTab={section}
            catgoryArray={tabsArray}
          />
        </div>
      </div>
    </main>
  );
};

export default TalentPoolPage;
