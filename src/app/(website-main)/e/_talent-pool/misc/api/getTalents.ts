import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { Axios } from '@/lib/api/axios';
import { TalentResponse } from '../types/types';

export const getProTalents = async () => {
  const response = await Axios.get(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/recruiter/talents/`
  );
  return response.data?.data as TalentResponse;
};

export const useGetTalents = () => {
  return useQuery({
    queryFn: getProTalents,
    queryKey: ['get-talents'],
  });
};
