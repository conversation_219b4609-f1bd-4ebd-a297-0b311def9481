import { useQuery } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';

export const getSingleIntern = async (id: string) => {
  const response = await Axios.get(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/global_intern/global_intern_detail/${id}/`
  );
  return response.data;
};

export const useGetSingleIntern = (id: string) => {
  return useQuery({
    queryFn: () => getSingleIntern(id),
    queryKey: [id],
  });
};
