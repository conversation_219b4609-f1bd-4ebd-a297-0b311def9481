import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { Axios } from '@/lib/api/axios';

export const getInterns = async () => {
  const response = await Axios.get(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/global_intern/get_global_interns/`
  );
  return response.data;
};

export const useGetInterns = () => {
  return useQuery({
    queryFn: getInterns,
    queryKey: ['john'],
  });
};
