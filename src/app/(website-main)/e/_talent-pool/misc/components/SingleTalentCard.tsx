import { Rating } from '@smastrom/react-rating';
import React, { SetStateAction } from 'react';
import { Button } from '@/components/shared';
import EllipsesIcon from '../icons/EllipsesIcon';
import EmailIcon from '../icons/EmailIcon';
import InternCard from '../icons/InternCard';
import LocationIcon from '../icons/LocationIcon';
import RoleIcon from '../icons/RoleIcon';
import VerifiedInternIcon from '../icons/VerifiedInternIcon';
import { Talent } from '../types/types';
import '@smastrom/react-rating/style.css';
import { getInitials } from '../util';

export interface SingleTalentModalProps {
  talent: Talent;
  setIsOpen: React.Dispatch<SetStateAction<boolean>>;
  setCurrentIndex: React.Dispatch<SetStateAction<number>>;
  index: number;
}

const SingleTalentCard: React.FunctionComponent<SingleTalentModalProps> = ({
  talent,
  setIsOpen,
  index,
  setCurrentIndex,
}) => {
  const handleViewTalent = () => {
    setCurrentIndex(index);
    setIsOpen(true);
  };

  return (
    <div className="w-full max-w-md  rounded-2xl bg-[#f7f7f7] p-4">
      {/* Header */}
      <div className="flex items-start justify-between rounded-2xl bg-white p-3">
        <div className="flex items-start gap-2">
          {/* {avatarUrl ? (
              <img src={avatarUrl} alt="avatar" className="w-12 h-12 rounded-full object-cover" />
            ) : ( */}
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-light font-semibold text-primary">
            {getInitials(talent.full_name)}
          </div>
          {/* )} */}
          <div>
            <h3 className="text-sm font-bold text-gray-900">
              {talent.full_name}
            </h3>
            <p className="text-xs text-gray-500">{talent.desired_role}</p>
            <span className="rounded-full bg-green-500 px-2 py-0.5 text-xs font-medium text-white">
              {talent.currently_employed ? 'Available' : 'Unavailable'}
            </span>
          </div>
        </div>

        <div className="flex items-start gap-2">
          {/* <span className="text-yellow-600 text-xl">
            🔒
          </span> */}
          <Button
            className="!h-6 !w-6 !rounded-full bg-primary-light text-primary"
            variant="light"
            size="icon"
          >
            <EllipsesIcon width="14" className="text-primary" />
          </Button>
        </div>
      </div>

      <div className="mt-4 rounded-2xl bg-white p-3">
        {/* Email / Location / Score */}
        <div className=" flex items-start justify-between">
          <div className="space-y-2">
            {/* <div className="flex items-center gap-2 text-sm text-primary">
            <EmailIcon /> <span className="text-gray-700">{email}</span>
          </div> */}
            <div className="flex items-center gap-2 text-sm text-primary">
              <LocationIcon width="20" height="20" />{' '}
              <span className="text-xs text-gray-700">
                {talent.address ?? 'Unknown'}
              </span>
            </div>
          </div>
          <div className="flex items-center">
            <ScoreChart percentage={talent.cv_score} />
            <span className="flex flex-col items-center text-[11px] text-red-500">
              <span className="font-semibold">{talent.cv_score}% </span>
              <span>Score</span>
            </span>
          </div>
        </div>

        {/* Stack */}
        <div className="mt-1 flex flex-wrap gap-2">
          {(talent.stack ?? []).map(skill => (
            <span
              key={skill}
              className="rounded-full bg-primary-light px-4 py-1 text-[10px] text-primary"
            >
              {skill}
            </span>
          ))}
        </div>

        {/* Rating */}
        <div className="mt-4 flex items-center justify-between border-t border-[#0000001F] pt-3">
          <div className="flex items-center gap-1 text-lg text-yellow-400">
            <div className="w-1/3">
              <Rating
                className=" max-w-full"
                value={talent.rating}
                //@ts-ignore
                onChange={val => {}}
                isDisabled={true}
              />
            </div>{' '}
            <span className="ml-2 text-sm font-semibold text-black">
              {talent.rating.toFixed(1)}/5
            </span>
          </div>
          <button className="rounded-full bg-gray-200 px-4 py-1 text-sm font-semibold text-black">
            View
          </button>
        </div>
      </div>
    </div>
    // <div className="relative flex flex-col gap-1  rounded-lg border-[0.5px] bg-[#F7F7F7] p-5">
    //   <div className="p-2 rounded-lg bg-white ">
    //     <div className="flex items-center gap-x-2">
    //       <div className="flex items-center justify-center rounded-full bg-primary p-1">
    //         <InternCard />
    //       </div>
    //       <p className="text-lg font-medium text-primary">
    //         {item?.full_name}
    //       </p>
    //       <VerifiedInternIcon />
    //     </div>
    //     <div>
    //       <EllipsesIcon />
    //     </div>

    //   </div>

    //   <div className="flex items-center gap-x-2">
    //     <EmailIcon />
    //     {/* <p className="text-sm font-light text-[#344051]">{item?.user?.email}</p> */}
    //   </div>
    //   <div className="flex items-center gap-x-2">
    //     <RoleIcon />
    //     <p className="text-sm font-light text-[#344051]">{item?.desired_role}</p>
    //   </div>
    //   <div className="flex items-center gap-x-2">
    //     <LocationIcon />
    //     <p className="text-sm font-light text-[#344051]">
    //       {item?.location}
    //     </p>
    //   </div>
    //   <button
    //     onClick={handleViewTalent}
    //     className="absolute bottom-3 right-3 rounded-md bg-primary-light p-2 px-5 text-xs text-primary"
    //   >
    //     View
    //   </button>
    // </div>
  );
};

export default SingleTalentCard;

type ScoreChartProps = {
  percentage: number;
};

const ScoreChart = ({ percentage }: ScoreChartProps) => {
  const radius = 12;
  const strokeWidth = 3;
  const circumference = 2 * Math.PI * radius;
  const offset = circumference - (percentage / 100) * circumference;

  // Choose color based on percentage
  const getColor = () => {
    if (percentage >= 75) return 'stroke-green-500 text-green-500';
    if (percentage >= 50) return 'stroke-yellow-500 text-yellow-500';
    if (percentage >= 25) return 'stroke-orange-500 text-orange-500';
    return 'stroke-red-500 text-red-500';
  };

  return (
    <div className="relative h-10 w-10">
      <svg width="100%" height="100%" viewBox="0 0 40 40">
        <circle
          className="text-gray-200"
          strokeWidth={strokeWidth}
          stroke="currentColor"
          fill="transparent"
          r={radius}
          cx="20"
          cy="20"
        />
        <circle
          className={getColor()}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          stroke="currentColor"
          fill="transparent"
          r={radius}
          cx="20"
          cy="20"
          strokeDasharray={circumference}
          strokeDashoffset={offset}
        />
      </svg>
    </div>
  );
};
