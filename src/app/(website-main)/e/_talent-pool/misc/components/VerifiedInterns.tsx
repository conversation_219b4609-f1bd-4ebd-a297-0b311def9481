'use client';

import React, { useState } from 'react';
import { Search } from '@/components/shared/icons';
import { useGetInterns } from '../api/getInterns';
import SingleInternCard from './SingleInternCard';
import SingleInternModal from './SingleInternModal';

const VerifiedInterns = () => {
  const { data: internData, isLoading: loadingInterns } = useGetInterns();

  const [currentIndex, setCurrentIndex] = useState(0);
  const [isSingleInternModalOpen, setIsSingleInternModalOpen] = useState(false);

  return (
    <main>
      <section className="sticky top-0 flex flex-wrap items-center justify-between bg-white px-4 py-2 shadow-sm md:mx-4">
        <h2 className="max-w-max">
          Interns:{' '}
          <span className="font-medium text-header-text">
            {internData?.count}
          </span>
        </h2>

        <div className="ml-auto flex items-center gap-2">
          <div className="relative ml-auto">
            <Search className="absolute right-[5%] top-[25%]" />
            <input
              type="search"
              placeholder="Search"
              className="rounded-lg border-[1.75px] border-[#D6D6D6] px-2.5 py-2 text-xs transition-all focus:border-primary focus:outline-none sm:px-4 md:text-[0.9rem]"
            />
          </div>
          <span className="rounded-[0.35rem] border-[1.75px] border-[#D6D6D6] px-2.5 py-2 text-xs text-[#556575] sm:px-4 md:text-[0.875rem]">
            Filter
          </span>
        </div>
      </section>
      <div className="my-5 grid grid-cols-1 gap-2 p-5 px-4 py-2 shadow-sm md:mx-4 md:grid-cols-2 lg:grid-cols-4">
        {internData?.results?.map(
          (
            item: {
              id: string;
              user: {
                first_name: string;
                last_name: string;
                email: string;
              };
              role: string;
              current_location: string;
            },
            index: number
          ) => {
            return (
              <SingleInternCard
                key={index}
                item={item}
                setIsOpen={setIsSingleInternModalOpen}
                index={index}
                setCurrentIndex={setCurrentIndex}
              />
            );
          }
        )}
      </div>
      <SingleInternModal
        isOpen={isSingleInternModalOpen}
        currentIndex={currentIndex}
        arrayOfInterns={internData?.results}
        setIsOpen={setIsSingleInternModalOpen}
        setCurrentIndex={setCurrentIndex}
      />
    </main>
  );
};

export default VerifiedInterns;
