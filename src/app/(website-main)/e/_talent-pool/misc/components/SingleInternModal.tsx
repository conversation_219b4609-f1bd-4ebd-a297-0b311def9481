import { Dialog, Tab } from '@headlessui/react';
import Link from 'next/link';
import React, { SetStateAction, useEffect, useState } from 'react';
import ReactLoader from 'react-loading';
import CircleTickIcon from '@/app/(website-main)/i/misc/icons/CircleTickIcon';
import { cn } from '@/utils';import { useGetSingleIntern } from '../api/getSIngleInternData';
import NextIcon from '../icons/NextIcon';
import VerifiedInternIcon from '../icons/VerifiedInternIcon';
import AboutIntern from './AboutIntern';
import { SingleInternProps } from './SingleInternCard';
import CandidateDetailsDocumentsTab from '../../../jobs/misc/components/job-detail/5.3 JobPipelineCandidateDetailsDocumentsTab';

export interface SingleInternModalProps {
  currentIndex: number;
  arrayOfInterns: SingleInternProps[];
  isOpen: boolean;
  setIsOpen: React.Dispatch<SetStateAction<boolean>>;
  setCurrentIndex: React.Dispatch<SetStateAction<number>>;
}
const SingleInternModal: React.FunctionComponent<SingleInternModalProps> = ({
  isOpen,
  setIsOpen,
  currentIndex,
  setCurrentIndex,
  arrayOfInterns,
}) => {
  const [currentIntern, setCurrentIntern] = useState<any>();
  const { data: internData, isLoading } = useGetSingleIntern(
    arrayOfInterns ? arrayOfInterns[currentIndex]?.id : '1'
  );

  const closeSuccessModal = () => {
    setIsOpen(false);
  };

  const tabList = [
    {
      title: 'About',
      disabled: false,
      component: <AboutIntern globalIntern={internData?.global_intern} />,
    },
    {
      title: 'Documents',
      disabled: true,
      component: (
        <div className="flex h-full w-full items-center justify-center p-5">
          <CandidateDetailsDocumentsTab
            cvFileName={internData?.global_intern?.cv}
            cvFile={internData?.global_intern?.cv_file_path}
            isLoadingCVFile={isLoading}
          />
        </div>
      ),
    },
    {
      title: 'Assessment',
      disabled: true,
      component: (
        <div className="flex items-center justify-center p-5">
          <p className="font-medium text-primary">Coming soon...</p>
        </div>
      ),
    },
  ];

  useEffect(() => {
    setCurrentIntern(internData);
  }, [internData]);
  return (
    <Dialog open={isOpen} onClose={closeSuccessModal}>
      <div className="fixed inset-0 z-30 bg-[#000000aa]">
        <Dialog.Panel
          className={
            'single-intern-modal absolute inset-y-0 right-0 top-1/2 flex h-full w-[90%] -translate-y-1/2 flex-col rounded-bl-2xl rounded-tl-2xl bg-white sm:w-[35rem]'
          }
        >
          <div className="flex h-[64px] items-end justify-between rounded-tl-2xl bg-primary px-10 py-[0.87rem] text-white">
            <p>View Details</p>
            <button
              className="flex h-8 w-[4.6rem] items-center justify-center rounded-md bg-[#F5F3FF33]"
              onClick={closeSuccessModal}
            >
              close
            </button>
          </div>

          {/* MAIN SECTION  */}

          <main className="flex h-full flex-col gap-y-2 p-5">
            {isLoading ? (
              <div className=" flex h-full w-full items-center justify-center">
                <ReactLoader
                  type="spin"
                  color="#755ae2"
                  height={40}
                  width={40}
                />
              </div>
            ) : (
              <section className="flex h-full w-full flex-col">
                <div className="flex items-center gap-x-4">
                  <div className="flex h-14 w-14 items-center justify-center rounded-full bg-primary text-white">
                    {internData?.global_intern?.user.first_name.slice(0, 1)}
                    {internData?.global_intern?.user?.last_name.slice(0, 1)}
                  </div>
                  <div className="flex flex-col">
                    <div className="flex items-center gap-x-2">
                      <p className="text-xl font-medium text-primary">
                        {internData?.global_intern?.user?.first_name}{' '}
                        {internData?.global_intern?.user?.last_name}
                      </p>
                      <VerifiedInternIcon />
                    </div>
                    <p className="font-light text-[#4A4A68]">
                      {internData?.global_intern?.user?.email}
                    </p>
                  </div>
                </div>
                <div className="h-full w-full py-5">
                  <Tab.Group>
                    <Tab.List className={'w-full bg-primary-light p-1'}>
                      {tabList.map(
                        (
                          item: { title: string; disabled: boolean },
                          index: number
                        ) => {
                          return (
                            <Tab key={index}>
                              {({ selected }) => (
                                <button
                                  className={cn(
                                    'min-w-[60px] rounded-md p-2 px-2 text-xs font-light sm:min-w-[120px] sm:px-5 sm:text-base',
                                    {
                                      'bg-primary  text-white': selected,
                                    }
                                  )}
                                >
                                  {item.title}
                                </button>
                              )}
                            </Tab>
                          );
                        }
                      )}
                    </Tab.List>
                    <Tab.Panels className={'h-full w-full'}>
                      {tabList.map(
                        (
                          item: { component: React.ReactNode },
                          index: number
                        ) => {
                          return (
                            <Tab.Panel className={'h-full w-full'} key={index}>
                              {item?.component}
                            </Tab.Panel>
                          );
                        }
                      )}
                    </Tab.Panels>
                  </Tab.Group>
                </div>
              </section>
            )}
          </main>

          {/* BOTTOM DIV */}
          <div className="flex h-[5rem] justify-between gap-x-4 rounded-b-2xl bg-[#F5F3FF] p-5 font-light">
            <button
              className="flex items-center justify-center gap-x-2 rounded-md border border-primary p-3 px-5 text-xs text-primary sm:text-base"
              disabled={currentIndex <= 0}
              onClick={() => setCurrentIndex(currentIndex - 1)}
            >
              <NextIcon />
              Previous
            </button>
            <button
              className="flex items-center justify-center gap-x-2 rounded-md border border-primary p-3 px-5 text-xs text-primary sm:text-base"
              disabled={!(currentIndex < arrayOfInterns?.length - 1)}
              onClick={() => setCurrentIndex(currentIndex + 1)}
            >
              Next
              <NextIcon className="rotate-180" />
            </button>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default SingleInternModal;
