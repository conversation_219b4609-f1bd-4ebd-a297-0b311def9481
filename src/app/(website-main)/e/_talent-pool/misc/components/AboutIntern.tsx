import Link from 'next/link';
import React from 'react';

const AboutIntern = ({ globalIntern }: { globalIntern: any }) => {
  return (
    <>
      <div className="grid h-max w-full grid-cols-2 p-2 sm:p-5">
        <div className="p-2">
          <p className="text-xs font-extralight sm:text-base">Name</p>
          <p className="text-sm font-medium sm:text-base">
            {globalIntern?.user?.first_name} {globalIntern?.user?.last_name}
          </p>
        </div>
        <div className="p-2">
          <p className="text-xs font-extralight sm:text-base">Email</p>
          <p className="text-sm font-medium sm:text-base">
            {globalIntern?.user?.email || 'N/A'}
          </p>
        </div>
        <div className="p-2">
          <p className="text-xs font-extralight sm:text-base">
            Current Location
          </p>
          <p className="text-sm font-medium sm:text-base">
            {globalIntern?.current_location || 'N/A'}
          </p>
        </div>
        <div className="p-2">
          <p className="text-xs font-extralight sm:text-base">
            Years of Experience
          </p>
          <p className="text-sm font-medium sm:text-base">
            {globalIntern?.years_of_experience || 'N/A'}
          </p>
        </div>
        <div className="p-2">
          <p className="text-xs font-extralight sm:text-base">Linkendin link</p>
          {globalIntern?.linkedin_profile ? (
            <Link target="__blank" href={globalIntern.linkedin_profile}>
              <p className="w-full truncate text-sm font-medium text-primary hover:underline sm:text-base">
                {globalIntern?.linkedin_profile}
              </p>
            </Link>
          ) : (
            <p className="text-sm font-medium sm:text-base">N/A</p>
          )}
        </div>
        <div className="p-2">
          <p className="text-xs font-extralight sm:text-base">
            Portfolio Website
          </p>
          {globalIntern?.portfolio_website ? (
            <Link target="__blank" href={globalIntern.portfolio_website}>
              <p className="w-full truncate text-sm font-medium text-primary hover:underline sm:text-base">
                {globalIntern?.portfolio_website}
              </p>
            </Link>
          ) : (
            <p className="text-sm font-medium sm:text-base">N/A</p>
          )}
        </div>
        <div className="p-2">
          <p className="text-xs font-extralight sm:text-base">Role</p>
          <p className="text-sm font-medium sm:text-base">
            {globalIntern?.role || 'N/A'}
          </p>
        </div>
      </div>
      <div className="p-2 sm:p-5">
        <p className="text-xs font-extralight sm:text-base">Skills:</p>
        <div className="flex w-full flex-wrap">
          {globalIntern?.skills?.map((item: string, index: number) => {
            return (
              <div className="rounded-full bg-primary-light p-2 text-primary">
                {item}
              </div>
            );
          })}
        </div>
      </div>
    </>
  );
};

export default AboutIntern;
