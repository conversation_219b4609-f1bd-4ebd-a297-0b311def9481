"use client"
import React, { useState } from 'react';
import { useGetInterns } from '../api/getInterns';
import SingleInternCard from './SingleInternCard';
import SingleInternModal from './SingleInternModal';
import Search from '@/components/shared/icons/Search';
import { Task, ElementEqual } from 'iconsax-react';

const ProTalents = () => {
  const { data: internData, isLoading: loadingInterns } = useGetInterns();

  const [currentIndex, setCurrentIndex] = useState(0);
  const [isSingleInternModalOpen, setIsSingleInternModalOpen] = useState(false);

  return <section className="flex h-full grow flex-col">
    <header className="m-2 mb-1 flex flex-wrap items-center  justify-between rounded-md bg-white px-5 py-4 transition-all duration-300">
      <h2 className="text-header-text">
        <span> Talents: </span>
        <span className="font-bold text-body-text">
          {internData?.count}
        </span>
      </h2>

      <div className="ml-auto flex flex-wrap items-center gap-2 transition-all duration-300">
        <div className="relative ml-auto">
          <Search className="absolute right-[5%] top-[25%]" />
          <input
            type="search"
            placeholder="Search"
            className="rounded-lg border-[1.75px] border-[#D6D6D6] px-2.5 py-2 text-xs transition-all focus:border-primary focus:outline-none sm:px-4 md:text-[0.9rem]"
          />
        </div>
        <button className="rounded-md border border-[#D6D6D6] px-2.5 py-3 text-xs text-[#556575] sm:px-4 md:text-[0.875rem]">
          Filter
        </button>

        <button className="rounded-md border-[1.75px] border-[#D6D6D6] px-2.5 py-2 text-xs text-[#556575] sm:px-4 md:text-[0.875rem]">
          <Task size="20" />
        </button>

        <button className="rounded-md border-[1.75px] border-[#D6D6D6] px-2.5 py-2 text-xs text-[#556575] sm:px-4 md:text-[0.875rem]">
          <ElementEqual size="20" />
        </button>


      </div>
    </header>


    <div className="my-5 grid grid-cols-1 gap-2 p-5 px-4 py-2 shadow-sm md:mx-4 md:grid-cols-2 lg:grid-cols-4">
      {internData?.results?.map(
        (
          item: {
            id: string;
            user: {
              first_name: string;
              last_name: string;
              email: string;
            };
            role: string;
            current_location: string;
          },
          index: number
        ) => {
          return (
            <SingleInternCard
              key={index}
              item={item}
              setIsOpen={setIsSingleInternModalOpen}
              index={index}
              setCurrentIndex={setCurrentIndex}
            />
          );
        }
      )}
    </div>

  </section>;
};

export default ProTalents;
