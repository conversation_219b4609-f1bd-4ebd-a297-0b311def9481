import { ColumnDef, PaginationState } from '@tanstack/react-table';
import React, { ReactNode, useState } from 'react';
import RemoveMemberModal from '@/app/(website-main)/e/teams/misc/components/RemoveMemberModal';
import {
  Avatar,
  Button,
  DataTable,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/shared';
import { Elipsis } from '@/components/shared/icons';
import Delete from '@/components/shared/icons/Delete';
import { useBooleanStateControl } from '@/hooks';
import { cn } from '@/utils';
import { addCommasToNumber } from '@/utils/numbers';
import { convertKebabAndSnakeToTitleCase, getInitials } from '@/utils/strings';
import { CloseJobIcon } from '../../../jobs/misc/icons';
import { Intern, InternResponse } from '../types/types';
import { InternCheckValues } from '@/app/(website-main)/i/misc/components/InternsBody';

interface TeamMemberTableColumns {
  id: number;
  name: string;
  email: string;
  role: string;
  location: string;
  action: Intern;
}

interface Props {
  data: InternResponse;
  filteredInterns: Intern[];
  isFetchingInterns: boolean;
  isLoadingInterns: boolean;
  emptyDisplay: ReactNode;
  pagination: PaginationState;
  setPagination: React.Dispatch<React.SetStateAction<PaginationState>>;
  refetch: () => void;
}

const InternTable: React.FC<Props> = ({
  filteredInterns,
  isLoadingInterns,
  isFetchingInterns,
  emptyDisplay,
  data,
  pagination,
  setPagination,
  refetch,
}) => {
  console.log(filteredInterns, "FILTERED INTERNS")
  const [memberToUpdate, setMemberToUpdate] = useState<Intern | null>(null);


  const {
    state,
    setTrue: openViewMemberDetailsDrawer,
    setFalse: closeViewMemberDetailsDrawer,
  } = useBooleanStateControl();

  const {
    state: isRemoveMemberModalOpen,
    setTrue: openRemoveMemberModal,
    setFalse: closeRemoveMemberModal,
  } = useBooleanStateControl();

  const handleOpenRemoveMemberModal = (member: Intern) => {
    setMemberToUpdate(member);
    openRemoveMemberModal();
  };

  const {
    state: isDeleteInviteModalOpen,
    setTrue: openDeleteInviteModal,
    setFalse: closeDeleteInviteModal,
  } = useBooleanStateControl();



  const {
    state: isSuspendMemberModalOpen,
    setTrue: openSuspendMemberModal,
    setFalse: closeSuspendMemberModal,
  } = useBooleanStateControl();

  const columns: ColumnDef<Intern>[] = [
    {
      header: 'S/N',
      cell: ({ row }) => (
        <span className="my-1 hidden text-sm text-header-text md:visible md:block">
          {pagination.pageIndex * pagination.pageSize + row.index + 1}
        </span>
      ),
    },
    {
      header: 'Name',
      accessorKey: 'name',
      cell: ({ getValue, row }) => {
        const memberName = `${row.original.user.first_name} ${row.original.user.last_name}`;
        const memberRole = row.original.role;
        return (
          <span className="my-1 flex items-center gap-1.5 text-sm">
            <Avatar
              fallback={memberName}
              size="small"
              alt={`${memberName} avatar`}
              fallbackClass={cn()}
            />
            <span>{memberName}</span>
          </span>
        );
      },
    },
    {
      header: 'Email',
      accessorKey: 'user.email',
      cell: ({ getValue }) => {
        return <span className="my-1 text-sm">{getValue() as string}</span>;
      },
    },
    {
      header: 'Role',
      accessorKey: 'role',
      cell: ({ getValue, row }) => {
        return (
          <span >
            {convertKebabAndSnakeToTitleCase(getValue() as string)}
          </span>
        );
      },
    },
    {
      header: 'Location',
      accessorKey: 'current_location',
      cell: ({ getValue, row }) => {
        const location = row.original.current_location;
        console.log(location);
        return (
          <span  >
            {location.toUpperCase()}
          </span>
        );
      },
    },

    {
      header: 'Action',
      accessorKey: 'action',
      accessorFn: row => {
        return row;
      },
      cell: ({ row }) => {
        const data = row.original;
        // const customerId = data.ben_phone;
        // const amount = data.amount;
        // const packageSlug = data.biller;
        // const bills_type = data.bills_type;

        return (
          <Popover>
            <PopoverTrigger className="items-cente flex gap-5 rounded-md border border-[#D4CCF6] px-2.5 py-1.5 text-xs">
              Action <Elipsis fill="#D4CCF6" />
            </PopoverTrigger>

            <PopoverContent
              className="flex w-max flex-col gap-1.5 !p-1 pr-4"
              align="end"
            >
              {/* <ViewMemberDetails
                teamMember={data.action}
                openDrawer={openViewMemberDetailsDrawer}
                closeDrawer={() => {
                  refetchTeamMembers();
                  closeViewMemberDetailsDrawer();
                }}
              />  */}



              <Button
                // onClick={() => handleOpenRemoveMemberModal(data.action)}
                variant="unstyled"
                size="tiny"
                justify="start"
                className="max-w-full rounded-sm py-[0.275rem] text-left text-[0.8125rem] hover:bg-primary-light"
              >
                <Delete />
                Remove member
              </Button>

            </PopoverContent>
          </Popover >
        );
      },
    },
  ];

  // const formattedData = filteredMembers?.map((member, index) => {
  //   const { user_email, user_last_name, user_first_name, role_name, status } =
  //     member;
  //   return {
  //     id: index,
  //     name: `${user_first_name} ${user_last_name}`,
  //     email: user_email,
  //     action: member,
  //     role: role_name,
  //     status: status,
  //   };
  // });

  return (
    <div>
      <DataTable
        columns={columns}
        isFetching={isFetchingInterns}
        isLoading={isLoadingInterns}
        pageCount={data?.count}
        pageIndex={pagination.pageIndex}
        pageSize={5}
        rows={filteredInterns}
        setPagination={setPagination}
        emptyDisplay={emptyDisplay}
      />


    </div>
  );
};

export default InternTable;
