import React, { SetStateAction } from 'react';
import EllipsesIcon from '../icons/EllipsesIcon';
import EmailIcon from '../icons/EmailIcon';
import InternCard from '../icons/InternCard';
import LocationIcon from '../icons/LocationIcon';
import RoleIcon from '../icons/RoleIcon';
import VerifiedInternIcon from '../icons/VerifiedInternIcon';

export interface SingleInternModalProps {
  item: SingleInternProps;
  setIsOpen: React.Dispatch<SetStateAction<boolean>>;
  setCurrentIndex: React.Dispatch<SetStateAction<number>>;
  index: number;
}

export interface SingleInternProps {
  id: string;
  user: {
    first_name: string;
    last_name: string;
    email: string;
  };
  role: string;
  current_location: string;
}
const SingleInternCard: React.FunctionComponent<SingleInternModalProps> = ({
  item,
  setIsOpen,
  index,
  setCurrentIndex,
}) => {
  const handleViewIntern = () => {
    setCurrentIndex(index);
    setIsOpen(true);
  };

  return (
    <div className="relative flex flex-col gap-1  rounded-md border-[0.5px] bg-white p-5">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-x-2">
          <div className="flex items-center justify-center rounded-full bg-primary p-1">
            <InternCard />
          </div>
          <p className="text-lg font-medium text-primary">
            {item?.user?.first_name} {item?.user?.last_name}
          </p>
          <VerifiedInternIcon />
        </div>
        <div>
          <EllipsesIcon />
        </div>
      </div>
      <div className="flex items-center gap-x-2">
        <EmailIcon />
        <p className="text-sm font-light text-[#344051]">{item?.user?.email}</p>
      </div>
      <div className="flex items-center gap-x-2">
        <RoleIcon />
        <p className="text-sm font-light text-[#344051]">{item?.role}</p>
      </div>
      <div className="flex items-center gap-x-2">
        <LocationIcon />
        <p className="text-sm font-light text-[#344051]">
          {item?.current_location}
        </p>
      </div>
      <button
        onClick={handleViewIntern}
        className="absolute bottom-3 right-3 rounded-md bg-primary-light p-2 px-5 text-xs text-primary"
      >
        View
      </button>
    </div>
  );
};

export default SingleInternCard;
