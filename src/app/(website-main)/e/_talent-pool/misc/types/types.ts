export type TalentResponse = {
    count: number;
    total_pages: number;
    current_page: number;
    next: string | null;
    previous: string | null;
    results: Talent[];
};

export type Talent = {
    id: string;
    address: string | null;
    desired_role: string;
    year_of_work_experience: string;
    proficiency_level: string | null;
    stack: string[] | null;
    location: string | null;
    is_pro: boolean;
    currently_employed: boolean;
    cv_score: number;
    rating: number;
    full_name: string;
};


export type Intern = {

    id: number;
    user: User;
    role: string;
    years_of_experience: string;
    current_location: string;
    cv: string | null;
    cv_file_path: string | null;
    linkedin_profile: string;
    portfolio_website: string;
    create_password: string | null;
    created_at: string;
    updated_at: string;
}

export interface User {
    first_name: string;
    last_name: string;
    phone_number: string;
    email: string;
    gender: string;
}
export type InternResponse = {
    count: number;
    next: string | null;
    previous: string | null;
    results: Intern[];
};
