'use client'
import React, { useEffect, useState } from 'react'
import { Record } from 'iconsax-react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import moment from 'moment'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import ReactLoading from "react-loading"

import { zodResolver } from '@hookform/resolvers/zod'
import { Button, LinkButton, Loader } from '@/components/shared'
import { cn } from '@/utils'
import { EmptyTeamMembers } from '@/app/(website-main)/e/teams/misc/icons'
import { Axios } from '@/lib/api/axios'

import JobMediaLayout from '../../misc/components/layout/JobMediaLayout'
import JobMediaHeader from '../../misc/components/layout/JobMediaHeader'
import { useNonJobFetchJobDetail } from '../../misc/api/jobdetails'
import { useEmailCheckerRequest } from '../../misc/api/emailchecker'
import TalentForm from '../../misc/components/application/TalentForm'
import NonTalentForm from '../../misc/components/application/NonTalentForm'
import { userEmailProp } from '../../misc/types'



const emailCheckSchema = z.object({
    email: z.string({ required_error: "Enter email." }).email({ message: "Enter a valid email" }),
});
export type emailCheckerProps = z.infer<typeof emailCheckSchema>;
const page = () => {
    const params: any = useParams()
    const { data, isLoading } = useNonJobFetchJobDetail(params?.id, true)
    const { mutate: handleValidateEmail, isLoading: emailLoader } = useEmailCheckerRequest()
    const [userExist, setUserExist] = useState(false)
    const [userNotExist, setUserNotExist] = useState(false)
    const [hasCheckedEmail, setHasCheckedEmail] = useState(false)
    const [userEmail, setUserEmail] = useState("")
    const [userDetail, setUserDetail] = useState<userEmailProp | null>(null)

    const [publicUrl, setPublicUrl] = useState(false)
    useEffect(() => {
        if (window.location.href.indexOf("apply") != -1) {
            setPublicUrl(true)
        } else {
            setPublicUrl(false)
        }

    }, [])


    const {
        register,
        control,
        handleSubmit,
        watch,
        formState: { errors },
    } = useForm<emailCheckerProps>({
        resolver: zodResolver(emailCheckSchema),
        defaultValues: {
            email: ""
        },
        mode: "onChange"
    });
    const submit = (data: emailCheckerProps) => {
        let formData = new FormData()
        formData.append("email", data?.email)
        setUserEmail(data?.email)
        handleValidateEmail(formData, {
            onSuccess: (data: any) => {
                if (data?.message === "User already exists.") {
                    setUserExist(true)
                    setHasCheckedEmail(true)
                    setUserDetail(data)
                }

            },
            onError: (error: any) => {
                if (error?.response?.data?.message === "User does not exist. Kindly create an account.") {
                    setUserNotExist(true)
                    setHasCheckedEmail(true)
                }
            },
        })
    }

    if (isLoading) {
        return <div className='flex items-center justify-center w-screen h-screen'>
            <Loader />
        </div>
    }

    const jobId = params?.id
    const sessionKey = `viewed_job_${jobId}`;
    const isJobViewed = sessionStorage.getItem(sessionKey);
    const viewJob = async () => {
        try {
            const response = await Axios.post(
                '/recruiter/track_job_view/',
                { unique_id: jobId },
            );
            sessionStorage.setItem(sessionKey, 'true');
            return response?.data;
        } catch (error) {

        }
    }
    if (sessionStorage && !isJobViewed && jobId && !isLoading && data) {
        viewJob()
    }



    return (
        <div className={`grow w-screen md:bg-[#F8F9FB] py-4 ${!publicUrl ? "bg-white" : "bg-white"} `}>
            <div className="xl:w-[70rem] mx-auto">
                <div className={` ${!publicUrl ? "h-[6rem]" : ""}  md:h-[3.875rem]  sticky top-0 z-50 bg-[#FBFAFF]`}><JobMediaHeader /></div>
                <div className=" w-full">
                    <JobMediaLayout>
                        <div className={`flex gap-x-5 px-7  md:px-5`}>
                            <div className="">
                                <h2 className='text-[#0E0E2C] block text-[clamp(1.3rem,1.8vw,1.5rem)] font-medium'>{data?.job_title}</h2>
                                <p className='flex items-center gap-0.5 text-sm md:text-base text-[#4A4A68]'>
                                    {data?.location}
                                    <span className='font-bold text-xl py-4 mx-1.5'><Record height={5} width={5} fill={"#000"} /></span>
                                    {moment(data?.created_at).fromNow(true) + " ago"}
                                </p>
                            </div>

                        </div>
                        {
                            !hasCheckedEmail &&
                            <div className="border-t-[0.5px] mt-8 md:mt-0 border-[#F0EDFE] md:border-none px-7  md:px-5">
                                <form onSubmit={handleSubmit(submit)} noValidate method='post' encType='multipart/form-data'>
                                    <div className="mt-8">
                                        <p className='text-[#0E0E2C] text-[clamp(1rem,1.35vw,1.25rem)] font-medium'>Apply for Job</p>
                                        <p className='text-[#4A4A68] text-[0.8625rem]'>Complete the fields below or <span className='text-primary'><Link href={`/t/jobs/apply?id=${data?.id}`}>Login</Link></span> with your getlinked account to apply.</p>
                                    </div>
                                    <div className="flex flex-col mt-4 px-1 mb-24">
                                        <div className='inputdiv !my-2'>
                                            <label htmlFor="email"></label>
                                            <input   {...register("email")} style={{ textTransform: "lowercase" }} placeholder='Enter email' type='email'
                                                className={cn("w-full lowercase border-2", errors.email ? "!border-red-500" : "!border-black focus:!border-primary")}
                                            />
                                            {errors?.email && <p className='text-red-500 text-xs mt-1'>{errors?.email?.message}</p>}
                                        </div>

                                        <button
                                            type='submit'
                                            className='self-end bg-black border-2 border-transparent hover:border-black hover:text-black hover:bg-white w-[7.625rem] flex justify-center items-center gap-x-3 h-[2.75rem] text-white text-[0.875rem] font-medium rounded-lg'
                                        // disabled={data?.job_status == "CLOSED"}
                                        // title={data?.job_status == "CLOSED" && "This job is "}
                                        >
                                            Proceed
                                            {
                                                emailLoader && <ReactLoading
                                                    className="z-20 "
                                                    type="spin"
                                                    color="#000"
                                                    width={18}
                                                    height={18}
                                                />
                                            }
                                        </button>
                                    </div>

                                </form>

                            </div>
                        }

                        <div>
                            <div className={cn('grow w-full flex justify-center min-h-full lg:[@media(min-height:700px)]:min-h-[400px] align-center py-6 mt-8', (!hasCheckedEmail || data?.job_status === "OPEN" || data?.job_status == "QUEUED") && "hidden")} >
                                <article className='flex flex-col items-center justify-center gap-6 p-8 sm:p-12  w-[90%] min-h-[90%] max-w-[390px]  bg-gradient-to-tr from-primary-light via-primary-[#D9D9D9] to-transparent rounded-xl'>
                                    <EmptyTeamMembers />
                                    <div className='text-center mt-3 mb-4'>
                                        <h3 className='text-header-text text-base font-medium'> This job has stopped accepting applications</h3>
                                        <p className='text-sm font-normal text-[#7D8590]'>
                                            We regret to inform you that this position is no longer accepting applications. However, we encourage you to explore other opportunities available on our site. Click the button below to discover additional job openings. Thank you for your understanding.
                                        </p>
                                    </div>
                                    <LinkButton href="/jobs" variant={'light'} size="capsule">See other job openings</LinkButton>
                                </article>
                            </div>
                            {
                                (data?.job_status == "OPEN" || data?.job_status == "QUEUED") && userExist && <TalentForm userDetails={userDetail} />
                            }
                            {
                                (data?.job_status == "OPEN" || data?.job_status == "QUEUED") && userNotExist && <NonTalentForm userEmail={userEmail} />
                            }
                        </div>

                    </JobMediaLayout>

                </div>          
            </div >
        </div >
    )
}

export default page