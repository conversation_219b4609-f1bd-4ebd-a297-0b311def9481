import { Suspense } from 'react'
import type { Metadata, ResolvingMetadata } from 'next'
import { ActiveJob } from '@/app/(website-main)/e/jobs/misc/types'
import { AxiosWithNoAuth } from '@/lib/api/axios'
import { Spinner } from '@/components/shared/icons'
import axios from 'axios'
import { stripHtml } from '@/utils/strings'
import { useParams } from 'next/navigation'

type Props = {
  params: { id: string }
}
export const dynamicParams = true;
export const revalidate = 0

export async function generateMetadata(
  { params }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  // const params = useParams()
  const job_id = params.id
  const fetchJobPost = async (job_id: string) => {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/talent/non_job_detail/${job_id}/`, {
      cache: 'no-store',
      next: { revalidate: 0 }
    });
    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
    const data = await response.json();
    return data as ActiveJob;

    // return response.data as ActiveJob;
  }

  const post = await fetchJobPost(job_id)

  return {
    title: `${post.company.name} is hiring a ${post?.job_title}` || 'Job Opening',
    description: `${post?.description?.substring(0, 100) || ""}...` || 'Check out this job opening on getlinked.ai',
    openGraph: {
      title: post?.job_title || 'Job Opening on Getlined.AI',
      description: `${post?.description?.replace(/<[^>]*>/g, '').substring(0, 100) || ""}...` || 'Check out this new job opening on getlinked.ai',
      images: [
        {
          url: post?.logo || post?.recruiter.company.logo || '/default-image.png',
          width: 1200,
          height: 630,
          alt: post?.job_title || 'Job Opening',
        },
      ],
      url: `https://app.getlinked.ai/jobs/${job_id}`,
      type: 'article',
    },
    twitter: {
      card: 'summary_large_image',
      title: post?.job_title || 'Job Opening on Getlined.AI',
      description: `${post?.description || ""}...` || 'Check out this new job opening on getlinked.ai',
      images: [post?.logo || post?.recruiter.company.logo || '/default-image.png'],
    },
  }
}

export default async function PostDetailsPageLayout({ children }: { children: React.ReactNode }) {

  return (
    <Suspense fallback={
      <div className='flex items-center justify-center w-screen h-screen'>
        <Spinner className='text-foreground' />
      </div>
    }>
      {children}
    </Suspense>
  )
}