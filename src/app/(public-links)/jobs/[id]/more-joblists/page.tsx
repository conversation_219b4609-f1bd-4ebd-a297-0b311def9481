'use client'
import React from 'react'
import moment from 'moment';
import Link from 'next/link';
import ReactLoading from "react-loading"
import useInfiniteScroll from 'react-infinite-scroll-hook';

import { AxiosWithNoAuth } from '@/lib/api/axios';
import { Loader } from '@/components/shared';
import { useInfiniteQuery } from "@tanstack/react-query";

import JobMediaHeader from '../../misc/components/layout/JobMediaHeader';
import { JobListTypesProps } from '../../misc/types/joblistTypes';


interface queryKeyTypes {
    pageParam: number;
}
const page = () => {


    const fetchAllJobs = async ({ pageParam = 1 }: queryKeyTypes) => {
        try {
            const response = await AxiosWithNoAuth.get(`/talent/non_talent_job_list/?page=${pageParam}`);
            return response?.data as JobListTypesProps;
        } catch (error: any) {
            if (error?.response && error?.response?.status === 404) {
                console.error("Page not found:", error);
                throw new Error("Page not found");
            } else {
                console.error("Error fetching data:", error);
                throw new Error("Error fetching data");
            }
        }
    };

    const {
        data,
        error,
        fetchNextPage,
        hasNextPage,
        isFetchingNextPage,
        isLoading: loading,
    } = useInfiniteQuery(["fetch-all-jobs-list"], ({ pageParam = 1 }) => fetchAllJobs({ pageParam }), {
        staleTime: 6000,
        getNextPageParam: (lastPage, pages) => {
            return lastPage?.next ? pages.length + 1 : undefined;
        }
    });

    const nextPage = hasNextPage !== undefined;

    const [sentryRef] = useInfiniteScroll({
        loading: isFetchingNextPage || loading,
        hasNextPage: nextPage,
        onLoadMore: fetchNextPage,
        disabled: !!error || !nextPage,
        rootMargin: "0px 0px 400px 0px",
    });

    if (loading) {
        return  <div className='flex items-center justify-center w-screen h-screen'>
        <Loader />
    </div>
    }
    return (
        <div className="w-full bg-[#F8F9FB]   px-5 py-4 ">
            <div className="xl:w-[70rem] mx-auto">
                <div className="   md:h-[3.875rem]  sticky top-0 z-50 bg-[#FBFAFF]"><JobMediaHeader /></div>


                <div className="bg-white md:mt-6 md:pt-[3rem] pb-4 gap-4 px-4 md:px-[3rem] ">
                    <div className="overflow-x-auto">
                        <div className="">


                            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                                {data?.pages?.map((jobs) => {
                                    return (
                                        jobs?.results?.map((job) => (
                                            <React.Fragment key={job?.id}>
                                                {!loading && (
                                                    <Link href={`/jobs/${job?.unique_id}`} className="bg-[#F8F9FB] rounded-[0.625rem] shrink-0 p-6 hover:scale-95 cursor-pointer transition-transform ">

                                                        <div className="flex justify-between items-start flex-wrap">

                                                            <div className="flex items-start gap-x-3">

                                                                <div className="h-[1.7rem] w-[1.7rem] rounded-full shrink-0">
                                                                    <img src={job?.company?.logo ?? ""} alt={job?.company?.logo ?? "Logo"} className='w-full h-full rounded-full' />
                                                                </div>
                                                                <div className="">
                                                                    <h2 className="text-[1.125rem] font-medium text-[#0E0E2C]"> <p>{job?.job_title}</p></h2>
                                                                    <p className="text-sm text-[#4A4A68]">{job?.location} - {moment(job?.created_at).fromNow(true) + " ago"}</p>
                                                                </div>

                                                            </div>

                                                            <Link href={`/jobs/${job?.unique_id}`} >
                                                                <div className="">
                                                                    <button className="w-20 h-8 hover:bg-black hover:text-white mt-3 md:mt-0  transition-colors shrink-0 bg-[#FBFAFF] border-[1px] border-black text-black text-sm rounded-[1.25rem]">View</button>
                                                                </div>
                                                            </Link>
                                                        </div>
                                                        <div className="mt-4">
                                                            <p className="text-sm text-[#4A4A68] text-justify">{job?.description && job?.description?.length >= 200 ? job?.description?.substring(0, 170) + "..." : job?.description}</p>

                                                        </div>
                                                    </Link>

                                                )}
                                            </React.Fragment>
                                        )
                                        )
                                    )
                                }
                                )}

                            </div>
                            {(isFetchingNextPage || hasNextPage) && (


                                <div
                                    className=" w-full mt-5 flex justify-center items-center"
                                    ref={sentryRef}
                                >
                                    {hasNextPage && hasNextPage ? <ReactLoading
                                        className="z-20"
                                        type="bubbles"
                                        color="#755ae2"
                                        width={70}
                                        height={70}
                                    /> : ""}
                                </div>
                            )}
                        </div>
                    </div>

                </div>

            </div>
        </div>
    )
}

export default page