'use client';
import React, { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'

import moment from 'moment';
import { Loader, Textarea } from '@/components/shared';
import { useNonJobFetchJobDetail } from '../misc/api/jobdetails';
import JobMediaLayout from '../misc/components/layout/JobMediaLayout';
import JobMediaHeader from '../misc/components/layout/JobMediaHeader';
import MoreJobsList from '../MoreJobsList';
import ReactTextareaAutosize from 'react-textarea-autosize';
import { useTrackJobViews } from '../misc/api';
import { Axios } from '@/lib/api/axios';




const NonTalentJobDetailPage = () => {

    const params: any = useParams()
    const { data, isLoading } = useNonJobFetchJobDetail(params?.id)


    const jobId = params?.id
    const { mutate: trackJobView } = useTrackJobViews()
    useEffect(() => {
        const sessionKey = `viewed_job_${jobId}`;
        const isJobViewed = sessionStorage.getItem(sessionKey);

        if (!isJobViewed) {
            trackJobView({ unique_id: jobId },
                {
                    onSuccess() {
                        sessionStorage.setItem(sessionKey, 'true');
                    },
                }
            );
        }
    }, [jobId, trackJobView]);


    if (isLoading) {
        return <div className='flex items-center justify-center w-screen h-screen'>
            <Loader />
        </div>
    }
    return (
        <>
            {<div className="w-screen grow bg-white md:bg-[#F8F9FB] md:py-4 ">
                <div className="xl:w-[70rem] mx-auto">
                    <div className="   md:h-[3.875rem] sticky top-0 z-50 bg-[#FBFAFF]"><JobMediaHeader /></div>

                    <JobMediaLayout>
                        <div className="flex gap-x-5 ">
                            <div className="">

                                <h2 className='text-[#0E0E2C] flex items-center gap-2 text-[clamp(1.3rem,1.8vw,1.5rem)] font-medium'>
                                    {data?.job_title}
                                    {
                                        data?.job_status === "CLOSED" && <span className='bg-danger-light-active px-3 py-1 rounded-full text-[0.8rem] font-medium'>CLOSED</span>
                                    }
                                </h2>
                                <p className='text-base block text-[#4A4A68]'>{data?.location}{data?.location && ", "} {moment(data?.created_at).fromNow(true) + " ago"}</p>
                            </div>

                        </div>
                        <div className="border-[0.5px] border-[#E4E4E4] w-full rounded-[0.625rem] p-[1rem] mt-6 mb-1">
                            <p className='text-header-text text-[clamp(1.05rem,1.05vw,1.25rem)] font-medium'>Company overview</p>
                            <div dangerouslySetInnerHTML={{ __html: data?.company_overview! }} className='tiptap w-full bg-transparent text-[0.9rem] text-[#4A4A68] resize-none' />
                        </div>

                        <div className="border-[0.5px] border-[#E4E4E4] w-full rounded-[0.625rem] p-[1rem] mt-6 mb-1">
                            <p className='text-header-text text-[clamp(1.05rem,1.05vw,1.25rem)] font-medium'>Job Description</p>
                            <div dangerouslySetInnerHTML={{ __html: data?.description! }} className='tiptap w-full bg-transparent text-[0.9rem] text-[#4A4A68] resize-none' />
                        </div>


                        {
                            data?.responsibilities &&
                            <div className="border-[0.5px] border-[#E4E4E4] w-full rounded-[0.625rem] p-[1rem] mt-6 mb-1">
                                <p className='text-header-text text-[clamp(1.05rem,1.05vw,1.25rem)] font-medium'>Role Responsibilities</p>
                                <div dangerouslySetInnerHTML={{ __html: data?.responsibilities! }} className='tiptap w-full bg-transparent text-[0.9rem] text-[#4A4A68] resize-none' />
                            </div>
                        }

                        {
                            data?.compulsory_requirements &&
                            <div className="border-[0.5px] border-[#E4E4E4] w-full rounded-[0.625rem] p-[1rem] mt-6 mb-1">
                                <p className='text-header-text text-[clamp(1.05rem,1.05vw,1.25rem)] font-medium'>Complusory Requirements</p>
                                <div dangerouslySetInnerHTML={{ __html: data?.compulsory_requirements }} className='tiptap w-full bg-transparent text-[0.9rem] text-[#4A4A68] resize-none' />
                            </div>
                        }
                        {
                            data?.requirements &&
                            <div className="border-[0.5px] border-[#E4E4E4] w-full rounded-[0.625rem] p-[1rem] mt-6 mb-1">
                                <p className='text-header-text text-[clamp(1.05rem,1.05vw,1.25rem)] font-medium'>{data?.compulsory_requirements ? "Additional" : ""} Requirements </p>
                                <div dangerouslySetInnerHTML={{ __html: data?.requirements }} className='tiptap w-full bg-transparent text-[0.9rem] text-[#4A4A68] resize-none' />
                            </div>
                        }
                        {data && data?.job_custom_fields?.length > 0 && <div className="border-[0.5px] border-[#E4E4E4] w-full rounded-[0.625rem] p-[1rem] mt-6 mb-1">

                            {
                                data?.job_custom_fields?.map((job, idx: number) => (
                                    <div className="" key={idx}>
                                        <p className='text-header-text text-[clamp(1.05rem,1.05vw,1.25rem)] font-medium'>{job?.fieldTitle}</p>
                                        <ReactTextareaAutosize value={job?.fieldValue!} className='w-full bg-transparent text-[0.9rem] text-[#4A4A68] resize-none' disabled />
                                    </div>
                                ))
                            }
                        </div>}

                    </JobMediaLayout>

                    <div className="bg-white mt-6 pt-[3rem] pb-4 gap-4 px-3 md:px-[3rem] ">
                        <p className='p-3 font-medium text-xl'>More Jobs</p>
                        <MoreJobsList />

                    </div>

                </div>
            </div>}
        </>
    )
}

export default NonTalentJobDetailPage