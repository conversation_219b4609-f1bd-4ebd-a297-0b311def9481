import { useInfiniteQuery } from '@tanstack/react-query';
import moment from 'moment';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import ReactLoading from 'react-loading';
import { Loader } from '@/components/shared';
import { AxiosWithNoAuth } from '@/lib/api/axios';
import { useUser } from '@/lib/contexts/UserContext';
import { cn } from '@/utils';
import { JobCard } from './misc/components';
// import InfiniteScroll from "react-infinite-scroll-component";
import { JobListTypesProps } from './misc/types/joblistTypes';

interface queryKeyTypes {
  pageParam: number;
}

const MoreJobsList = () => {
  const params: any = useParams();
  const [isFirstFetch, setIsFirstFetch] = useState(true); // Track the first fetch

  const fetchAllJobsRequest = async ({ pageParam = 1 }: queryKeyTypes) => {
    try {
      const response = await AxiosWithNoAuth.get(
        `/talent/non_talent_job_list/?page=${pageParam}`
      );
      return response?.data as JobListTypesProps;
    } catch (error: any) {
      if (error.response && error.response.status === 404) {
        console.error('Page not found:', error);
        throw new Error('Page not found');
      } else {
        console.error('Error fetching data:', error);
        throw new Error('Error fetching data');
      }
    }
  };

  const {
    data,
    error,
    fetchNextPage,
    // hasNextPage,
    // isFetchingNextPage,
    isLoading: loading,
  } = useInfiniteQuery(
    ['fetch-all-jobs-list'],
    ({ pageParam = 1 }) => fetchAllJobsRequest({ pageParam }),
    {
      staleTime: 6000,
      getNextPageParam: (lastPage, pages) => {
        if (lastPage?.next !== null) {
          return pages;
        } else {
          return;
        }
      },
      enabled: !isFirstFetch, // Disable initial fetch
    }
  );

  useEffect(() => {
    if (isFirstFetch) {
      setIsFirstFetch(false); // Set isFirstFetch to false after the initial render
    }
  }, []);

  return (
    <div className="overflow-x-auto">
      <div className="">
        {loading && (
          <div className="flex h-full w-full items-center justify-center">
            <Loader />
          </div>
        )}

        <div className="grid grid-cols-1 gap-5 px-3 md:grid-cols-2">
          {data?.pages?.map(jobs => {
            let filterJob = jobs?.results?.filter(
              x => String(x?.unique_id) !== params?.id
            );

            return (
              filterJob &&
              filterJob?.slice(0, 4)?.map(job => {
                return <JobCard job={job} key={job.id} />;
              })
            );
          })}
        </div>
        {data?.pages?.some(
          jobs =>
            jobs?.results?.filter(x => String(x?.unique_id) !== params?.id)
              ?.length > 5
        ) && (
          <div className="mt-4 text-center">
            <Link href={`/jobs`}>
              <button className="flex h-[2.75rem] shrink-0 items-center gap-x-4 rounded-[2.5rem] border-2 border-transparent bg-black px-8 py-[0.81rem] text-[0.875rem] font-medium text-white hover:border-black hover:bg-white hover:text-black">
                See All
              </button>
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default MoreJobsList;
