import { useQuery } from '@tanstack/react-query';
import { AxiosWithNoAuth } from '@/lib/api/axios';
import { ActiveJob } from '@/app/(website-main)/e/jobs/misc/types';


export const nonJobfetchJobDetail = async (id: string | null) => {
  const response = await AxiosWithNoAuth.get(
    `/talent/non_job_detail/${id}/`
  );
  return response.data as ActiveJob;
};

export const useNonJobFetchJobDetail = (id: string | null, enabled: boolean = true) => {
  return useQuery(
    ['fetch-non-job-details', id],
    () => nonJobfetchJobDetail(id),
    {
      staleTime: 500000,
      keepPreviousData: true,
      enabled,
    }
  );
};
