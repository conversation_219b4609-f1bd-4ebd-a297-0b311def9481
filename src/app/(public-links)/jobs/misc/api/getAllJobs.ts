import { useInfiniteQuery, useQuery, useQueryClient } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';
import { ActiveJob } from '@/app/(website-main)/e/jobs/misc/types';



export interface PaginatedJobTypes {
    page: number;
    count: number;
    next: null;
    previous: null;
    results: ActiveJob[];
}
interface queryKeyTypes {
    pageParam: number;
    filterUrl?: string;
    search?: string
}

const fetchJobs = async ({ filterUrl, pageParam = 1, search }: queryKeyTypes) => {
    const endpoint = () => {
        if (search && search.trim() !== "") {
            return `talent/non_talent_job_list/?search=${search}${filterUrl}`
        }
        else {
            return `talent/non_talent_job_list/?page=${pageParam}${filterUrl}`
        }
    }
    const response = await Axios.get(endpoint())
    return response?.data as PaginatedJobTypes;
};


const useGetJobs = (filterUrl?: string, search?: string) => {
    const queryClient = useQueryClient();

    return useInfiniteQuery(["fetch-all-job-posting", { filterUrl, search }], ({ pageParam = 1 }) => fetchJobs({ filterUrl, pageParam, search }), {
        staleTime: 6000,
        keepPreviousData: true,
        // getNextPageParam: (lastPage, pages) => {
        //     if (lastPage?.results?.length > 0 && (lastPage?.page !== null)) {
        //         return pages.length + 1;
        //     } else {
        //         return undefined;
        //     }
        // },
        getNextPageParam: (lastPage, pages) => {
            return lastPage?.next ? pages.length + 1 : undefined;
        }
    });
};

export default useGetJobs;
