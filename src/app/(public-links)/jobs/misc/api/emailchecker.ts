import { useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { useUser } from '@/lib/contexts/UserContext';
import { ResultTypes } from '../types/joblistTypes';
import { AxiosWithNoAuth } from '@/lib/api/axios';

interface props {
  message: string;
}

export const emailCheckerRequest = async (email: any | null) => {
  const response = await AxiosWithNoAuth.post(
    `/talent/email_check/`,
    email
  );
  return response.data as props;
};

export const useEmailCheckerRequest = () => {
  return useMutation({
    mutationFn: emailCheckerRequest,
  });
};
