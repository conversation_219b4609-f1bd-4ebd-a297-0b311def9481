import exp from 'constants';
import { useMutation } from '@tanstack/react-query';
import { AxiosWithNoAuth } from '@/lib/api/axios';


interface non_talent_dataProp {
  job_id: any;
  non_talent_data: {
    current_location: string;
    expected_salary: string;
    email: string;
    name: string;
    years_of_experience: string;
    password: string;
    phone_number: string;
    cv: string;
    hasCoverLetter: boolean;
    gender: string;

    cover_letter_file: File | any;
    cover_letter_text: string;
    portfolio_link: string;
    linkedin_link: string;
    custom_job_requirements: any[];
    custom_job_requirements_id: any[];
    preferred_location?: string;
  };
}

const createNonTalentJob = async ({
  job_id,
  non_talent_data,
}: non_talent_dataProp) => {
  const formData = new FormData();
  formData.append('name', non_talent_data?.name);
  formData.append('email', non_talent_data?.email);
  formData.append('years_of_experience', non_talent_data?.years_of_experience);
  formData.append('password', non_talent_data?.password);
  formData.append('gender', non_talent_data?.gender);
  formData.append('current_location', non_talent_data?.current_location);
  formData.append('phone_number', non_talent_data?.phone_number);
  formData.append('expected_salary', non_talent_data?.expected_salary);
  formData.append('cv', non_talent_data?.cv);
  formData.append('linkedin_link', non_talent_data?.linkedin_link);
  formData.append('portfolio_link', non_talent_data?.portfolio_link);
  if (non_talent_data?.preferred_location) { 
    formData.append('preferred_location', non_talent_data?.preferred_location);
  }
  if (non_talent_data?.hasCoverLetter) {
    if (non_talent_data?.cover_letter_file !== null) {
      formData.append('cover_letter_file', non_talent_data?.cover_letter_file);
    }
    if (
      non_talent_data?.cover_letter_text !== '' &&
      non_talent_data?.cover_letter_text !== undefined
    ) {
      formData.append('cover_letter_text', non_talent_data?.cover_letter_text);
    }
  }

  formData.append(
    'custom_job_requirements',
    JSON.stringify(non_talent_data?.custom_job_requirements)
  );

  const response = await AxiosWithNoAuth.post(
    `/talent/v2/non_talent_job_application/${job_id}/`,
    formData,
    { headers: { 'Content-Type': 'multipart/form-data' } }
  );

  return response.data;
};

export const useCreateNonTalentJob = () => {
  return useMutation({
    mutationFn: createNonTalentJob,
 
  });
};
