import { useMutation } from '@tanstack/react-query';
import { Axios } from '@/lib/api/axios';

interface TrackJobViewDTO {
    unique_id: string
}

const increaseJobViews = async (jobData: TrackJobViewDTO) => {
    const response = await Axios.post(
        '/recruiter/track_job_view/',
        jobData,

    );
    return response.data;
};

const useTrackJobViews = () => {

    return useMutation({
        mutationFn: increaseJobViews
    });
};

export default useTrackJobViews