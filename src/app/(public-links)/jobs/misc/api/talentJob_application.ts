import exp from 'constants';
import { useMutation } from '@tanstack/react-query';
import axios from 'axios';
import { AxiosWithNoAuth } from '@/lib/api/axios';

interface talent_dataProp {
  job_id: any;
  non_talent_data: {
    current_location: string;
    email: string;
    name: string;
    years_of_experience: string;
    phone_number: string;
    cv: string;
    // cover_letter_id: string;
    gender: string;
    cv_and_resume_id: string;
    cover_letter_file: File | any;
    cover_letter_text: string;
    portfolio_link: string;
    linkedin_link: string;
    custom_job_requirements: any[];
    hasCoverLetter: boolean;
    accept_cv: boolean;
    preferred_location?: string
  };
}

const createTalentJob = async ({
  job_id,
  non_talent_data,
}: talent_dataProp) => {
  const formData = new FormData();
  formData.append('name', non_talent_data?.name);
  formData.append('email', non_talent_data?.email);
  formData.append('years_of_experience', non_talent_data?.years_of_experience);
  formData.append('gender', non_talent_data?.gender);
  formData.append('current_location', non_talent_data?.current_location);
  formData.append('phone_number', non_talent_data?.phone_number);
  if (non_talent_data?.preferred_location) { formData.append('preferred_location', non_talent_data?.preferred_location) }
  formData.append('linkedin_link', non_talent_data?.linkedin_link);
  formData.append('portfolio_link', non_talent_data?.portfolio_link);
  if (non_talent_data?.hasCoverLetter) {
    if (non_talent_data?.cover_letter_file !== null) {
      formData.append('cover_letter_file', non_talent_data?.cover_letter_file);
    }
    if (
      non_talent_data?.cover_letter_text !== '' &&
      non_talent_data?.cover_letter_text !== undefined
    ) {
      formData.append('cover_letter_text', non_talent_data?.cover_letter_text);
    }
  }

  if (non_talent_data?.accept_cv) {
    if (non_talent_data?.cv_and_resume_id !== '') {
      formData.append('cv_and_resume_id', non_talent_data?.cv_and_resume_id);
    }

    if (
      (non_talent_data?.cv_and_resume_id === '' &&
        non_talent_data?.cv !== null) ||
      (non_talent_data?.cv_and_resume_id === '' &&
        non_talent_data?.cv !== undefined)
    ) {
      formData.append('cv', non_talent_data?.cv);
    }
  }
  formData.append(
    'custom_job_requirements',
    JSON.stringify(non_talent_data?.custom_job_requirements)
  );

  // console.log(JSON.stringify(processedRequirements));

  const response = await AxiosWithNoAuth.post(
    `/talent/v2/non_talent_job_application/${job_id}/`,
    formData,
    { headers: { 'Content-Type': 'multipart/form-data' } }
  );

  return response.data;
};

export const useCreateTalentJob = () => {
  return useMutation({
    mutationFn: createTalentJob,
    // onError: error => {
    //   processedRequirements?.map(async (x: custom_fileProp) => {
    //     // console.log(x?.custom_file?.id);
    //     let id = x?.custom_file?.id !== undefined && x?.custom_file?.id;

    //     try {
    //       await deleteFromCloudinary(String(id));
    //     } catch (error: any) {}
    //   });

    //   // })
    // },
  });
};
