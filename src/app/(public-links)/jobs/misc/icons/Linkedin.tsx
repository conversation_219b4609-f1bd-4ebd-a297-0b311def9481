import * as React from "react";
import { SVGProps } from "react";
const LinkedinIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M18 3C18.7956 3 19.5587 3.31607 20.1213 3.87868C20.6839 4.44129 21 5.20435 21 6V18C21 18.7956 20.6839 19.5587 20.1213 20.1213C19.5587 20.6839 18.7956 21 18 21H6C5.20435 21 4.44129 20.6839 3.87868 20.1213C3.31607 19.5587 3 18.7956 3 18V6C3 5.20435 3.31607 4.44129 3.87868 3.87868C4.44129 3.31607 5.20435 3 6 3H18ZM18 5H6C5.73478 5 5.48043 5.10536 5.29289 5.29289C5.10536 5.48043 5 5.73478 5 6V18C5 18.2652 5.10536 18.5196 5.29289 18.7071C5.48043 18.8946 5.73478 19 6 19H18C18.2652 19 18.5196 18.8946 18.7071 18.7071C18.8946 18.5196 19 18.2652 19 18V6C19 5.73478 18.8946 5.48043 18.7071 5.29289C18.5196 5.10536 18.2652 5 18 5ZM8 10C8.24493 10 8.48134 10.09 8.66437 10.2527C8.84741 10.4155 8.96434 10.6397 8.993 10.883L9 11V16C8.99972 16.2549 8.90212 16.5 8.72715 16.6854C8.55218 16.8707 8.31305 16.9822 8.05861 16.9972C7.80416 17.0121 7.55362 16.9293 7.35817 16.7657C7.16271 16.6021 7.0371 16.3701 7.007 16.117L7 16V11C7 10.7348 7.10536 10.4804 7.29289 10.2929C7.48043 10.1054 7.73478 10 8 10ZM11 9C11.2342 8.99996 11.461 9.08213 11.6408 9.23216C11.8206 9.3822 11.9421 9.59059 11.984 9.821C12.1852 9.70431 12.3933 9.59979 12.607 9.508C13.274 9.223 14.273 9.066 15.175 9.349C15.648 9.499 16.123 9.779 16.475 10.256C16.79 10.681 16.96 11.198 16.994 11.779L17 12V16C16.9997 16.2549 16.9021 16.5 16.7272 16.6854C16.5522 16.8707 16.313 16.9822 16.0586 16.9972C15.8042 17.0121 15.5536 16.9293 15.3582 16.7657C15.1627 16.6021 15.0371 16.3701 15.007 16.117L15 16V12C15 11.67 14.92 11.516 14.868 11.445C14.7934 11.3522 14.6905 11.2862 14.575 11.257C14.227 11.147 13.726 11.205 13.393 11.347C12.893 11.561 12.435 11.897 12.123 12.208L12 12.34V16C11.9997 16.2549 11.9021 16.5 11.7272 16.6854C11.5522 16.8707 11.313 16.9822 11.0586 16.9972C10.8042 17.0121 10.5536 16.9293 10.3582 16.7657C10.1627 16.6021 10.0371 16.3701 10.007 16.117L10 16V10C10 9.73478 10.1054 9.48043 10.2929 9.29289C10.4804 9.10536 10.7348 9 11 9ZM8 7C8.26522 7 8.51957 7.10536 8.70711 7.29289C8.89464 7.48043 9 7.73478 9 8C9 8.26522 8.89464 8.51957 8.70711 8.70711C8.51957 8.89464 8.26522 9 8 9C7.73478 9 7.48043 8.89464 7.29289 8.70711C7.10536 8.51957 7 8.26522 7 8C7 7.73478 7.10536 7.48043 7.29289 7.29289C7.48043 7.10536 7.73478 7 8 7Z"
      fill="white"
    />
  </svg>
);
export default LinkedinIcon;
