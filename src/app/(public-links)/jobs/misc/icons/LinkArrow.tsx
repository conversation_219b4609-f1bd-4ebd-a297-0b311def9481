import * as React from "react";
import { SVGProps } from "react";
const LinkArrow = (props: SVGProps<SVGSVGElement>, color: "#fff") => (
    <svg
        width={14}
        height={14}
        viewBox="0 0 14 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            d="M3.54843 10.6166C3.65718 10.6166 3.76594 10.5582 3.82032 10.4999L11.4333 2.33324C11.5964 2.15824 11.5964 1.9249 11.4333 1.7499C11.2702 1.5749 10.9983 1.5749 10.8351 1.7499L3.22215 9.91657C3.05902 10.0916 3.05902 10.3832 3.22215 10.5582C3.33091 10.6166 3.43967 10.6166 3.54843 10.6166Z"
            fill={color}
        />
        <path
            d="M11.1617 8.45828C11.3792 8.45828 11.5967 8.28329 11.5967 7.99162V2.04162C11.5967 1.80828 11.4335 1.57495 11.1617 1.57495H5.56066C5.34315 1.57495 5.12563 1.74995 5.12563 2.04162C5.12563 2.33328 5.28877 2.50828 5.56066 2.50828H10.7266V8.04995C10.7266 8.28329 10.9441 8.45828 11.1617 8.45828Z"
            fill={color}
        />
    </svg>
);
export default LinkArrow;
