import * as React from 'react';
import { SVGProps } from 'react';

const WebIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={24}
    height={27}
    viewBox="0 0 24 27"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M12 24.1124C17.5228 24.1124 22 19.2054 22 13.1524C22 7.09934 17.5228 2.19238 12 2.19238C6.47715 2.19238 2 7.09934 2 13.1524C2 19.2054 6.47715 24.1124 12 24.1124Z"
      stroke="white"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 24.1124C14.2091 24.1124 16 19.2054 16 13.1524C16 7.09934 14.2091 2.19238 12 2.19238C9.79086 2.19238 8 7.09934 8 13.1524C8 19.2054 9.79086 24.1124 12 24.1124Z"
      stroke="white"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2 13.1523H22"
      stroke="white"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default WebIcon;
