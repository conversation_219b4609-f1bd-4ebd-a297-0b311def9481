import * as React from 'react';
import { SVGProps } from 'react';

const TelegramIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={26}
    height={26}
    viewBox="0 0 26 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M2.0625 13C2.0625 15.9008 3.21484 18.6828 5.26602 20.734C7.3172 22.7852 10.0992 23.9375 13 23.9375C15.9008 23.9375 18.6828 22.7852 20.734 20.734C22.7852 18.6828 23.9375 15.9008 23.9375 13C23.9375 10.0992 22.7852 7.3172 20.734 5.26602C18.6828 3.21484 15.9008 2.0625 13 2.0625C10.0992 2.0625 7.3172 3.21484 5.26602 5.26602C3.21484 7.3172 2.0625 10.0992 2.0625 13Z"
      stroke="white"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17.7979 7.41895L16.8791 19.3574L11.8356 14.1809L5.85938 12.9297L17.7979 7.41895Z"
      stroke="white"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.60938 13.7108L10.5071 18.8943L13.7919 16.1713M11.8301 14.1553L13.7446 12.624"
      stroke="white"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default TelegramIcon;
