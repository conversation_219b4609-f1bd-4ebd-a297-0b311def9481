'use client';

import { useCallback, useEffect, useState } from 'react';
import {
  getBestAvailableEmail,
  isValidEmail,
  shouldSkipEmailCollection,
  storeUserEmail,
} from '../utils/userContext';
import {
  ApplicationSummary,
  useAIApplicationChat,
} from './useAIApplicationChat';
import { UploadResult, useAIApplicationUpload } from './useAIApplicationUpload';

export type ApplicationStep =
  | 'email_collection'
  | 'introduction'
  | 'upload'
  | 'chat'
  | 'interview_scheduling'
  | 'interview_confirmation'
  | 'complete'
  | 'error';

export interface ApplicationState {
  currentStep: ApplicationStep;
  isReady: boolean;
  error: string | null;
  canProceed: boolean;
}

interface UseAIApplicationStateProps {
  jobId: string;
  applicantEmail?: string; // Make optional to request if not provided
  jobData?: any;
  onComplete?: (summary: ApplicationSummary) => void;
  onError?: (error: string) => void;
}

export const useAIApplicationState = ({
  jobId,
  applicantEmail: providedEmail,
  jobData,
  onComplete,
  onError,
}: UseAIApplicationStateProps) => {
  // Get the best available email from various sources
  const initialEmail = providedEmail || getBestAvailableEmail() || '';
  const shouldSkipEmail = shouldSkipEmailCollection() || !!providedEmail;

  // Application state
  const [applicationState, setApplicationState] = useState<ApplicationState>({
    currentStep: shouldSkipEmail ? 'introduction' : 'email_collection',
    isReady: false,
    error: null,
    canProceed: false,
  });

  const [applicationSummary, setApplicationSummary] =
    useState<ApplicationSummary | null>(null);
  const [userInput, setUserInput] = useState('');
  const [isOnline, setIsOnline] = useState(
    typeof navigator !== 'undefined' ? navigator.onLine : true
  );
  const [applicantEmail, setApplicantEmail] = useState(initialEmail);
  const [emailError, setEmailError] = useState('');
  const [interviewScheduling, setInterviewScheduling] = useState({
    isSchedulingModalOpen: false,
    isConfirmationModalOpen: false,
    selectedDate: '',
    selectedTime: '',
    isScheduled: false,
  });

  // Handle application completion
  const handleApplicationComplete = useCallback(
    (summary: ApplicationSummary) => {
      setApplicationSummary(summary);
      setApplicationState(prev => ({
        ...prev,
        currentStep: 'complete',
        canProceed: false,
      }));
      onComplete?.(summary);
    },
    [onComplete]
  );

  // Handle errors
  const handleError = useCallback(
    (error: string) => {
      console.error('Application error:', error);

      // Check if it's a network-related error
      const isOffline = typeof navigator !== 'undefined' && !navigator.onLine;
      const isNetworkError =
        isOffline ||
        error.toLowerCase().includes('network') ||
        error.toLowerCase().includes('connection') ||
        error.toLowerCase().includes('timeout');

      // Provide more context for network errors
      let enhancedError = error;
      if (isOffline) {
        enhancedError =
          'No internet connection. Please check your network and try again.';
      } else if (isNetworkError) {
        enhancedError = `${error} Please check your internet connection.`;
      }

      setApplicationState(prev => ({
        ...prev,
        error: enhancedError,
        currentStep: 'error',
        canProceed: false,
      }));
      onError?.(enhancedError);
    },
    [onError]
  );

  // Initialize chat service
  const chat = useAIApplicationChat({
    jobUuid: jobData?.unique_id || '', // Use job UUID from jobData
    applicantEmail,
    onApplicationComplete: handleApplicationComplete,
    onError: handleError,
  });

  // Initialize upload service
  const upload = useAIApplicationUpload({
    jobId,
    jobUuid: jobData?.unique_id, // Pass the job UUID from jobData
    applicantEmail,
    isWebSocketConnected: chat.isConnected,
    hasWelcomeResponse: chat.hasWelcomeResponse,
    onUploadStart: () => {
      console.log('Upload started');
    },
    onUploadProgress: progress => {
      console.log('Upload progress:', progress);
    },
    onUploadComplete: (result: UploadResult) => {
      console.log('Upload completed:', result);
      if (result.success) {
        // File uploaded successfully, WebSocket should receive cv_uploaded message
        // and the chat will continue automatically

        // Store uploaded file info for later use in chat messages
        if (upload.uploadedFile) {
          // The file info will be used by the ChatMessage component when rendering cv_uploaded messages
          console.log('File uploaded:', {
            name: upload.uploadedFile.name,
            size: upload.uploadedFile.size,
            type: upload.uploadedFile.type,
          });
        }
      }
    },
    onUploadError: (error: string) => {
      console.error('Upload error:', error);
      handleError(`File upload failed: ${error}`);
    },
  });

  // Update application state based on chat progress
  useEffect(() => {
    if (chat.progress.status === 'completed') {
      setApplicationState(prev => ({
        ...prev,
        currentStep: 'complete',
        canProceed: false,
      }));
    } else if (chat.progress.status === 'rejected') {
      handleError('Application was rejected based on CV analysis');
    }
  }, [chat.progress.status]);

  // Email validation using imported utility
  const validateEmail = useCallback((email: string): boolean => {
    return isValidEmail(email);
  }, []);

  const handleEmailSubmit = useCallback(
    (email: string) => {
      console.log('handleEmailSubmit called with email:', email);
      console.log('Current applicantEmail state:', applicantEmail);

      setEmailError('');

      if (!email.trim()) {
        setEmailError('Email is required');
        return;
      }

      if (!validateEmail(email)) {
        setEmailError('Please enter a valid email address');
        return;
      }

      // Store email for session
      storeUserEmail(email);
      console.log('Setting applicantEmail to:', email);
      setApplicantEmail(email);

      // Move to introduction step - WebSocket connection will be initiated immediately
      setApplicationState(prev => ({
        ...prev,
        currentStep: 'introduction',
        error: null,
      }));

      // Initiate WebSocket connection with the email immediately
      // No delay needed since we're passing the email directly to avoid stale state
      console.log('Initiating WebSocket connection with email:', email);
      chat.connectWithEmail(email);
    },
    [validateEmail, applicantEmail, chat]
  );

  // Step navigation functions
  const goToUpload = useCallback(() => {
    setApplicationState(prev => ({
      ...prev,
      currentStep: 'upload',
      canProceed: false,
      error: null,
    }));

    // WebSocket connection should already be established from email submission
    // Only connect if somehow not connected (fallback)
    if (!chat.isConnected && !chat.isConnecting && applicantEmail) {
      console.warn(
        'WebSocket not connected when reaching upload step, attempting to connect...'
      );
      chat.connectWithEmail(applicantEmail);
    }
  }, [chat, applicantEmail]);

  const goToChat = useCallback(() => {
    // WebSocket connection should already be established from email submission
    // Only connect if somehow not connected (fallback)
    if (!chat.isConnected && !chat.isConnecting && applicantEmail) {
      console.warn(
        'WebSocket not connected when reaching chat step, attempting to connect...'
      );
      chat.connectWithEmail(applicantEmail);
    }
    setApplicationState(prev => ({
      ...prev,
      currentStep: 'chat',
      canProceed: true,
      error: null,
    }));
  }, [chat, applicantEmail]);

  const startApplication = useCallback(() => {
    setApplicationState(prev => ({
      ...prev,
      isReady: true,
      error: null,
    }));
    goToUpload();
  }, [goToUpload]);

  const proceedToChat = useCallback(() => {
    if (upload.uploadResult?.success) {
      goToChat();
    } else {
      handleError('Please upload your CV first');
    }
  }, [upload.uploadResult, goToChat, handleError]);

  const sendMessage = useCallback(
    (message: string) => {
      if (!message.trim()) return;

      chat.sendMessage(message);
      setUserInput('');
    },
    [chat]
  );

  const handleKeyPress = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendMessage(userInput);
      }
    },
    [sendMessage, userInput]
  );

  const resetApplication = useCallback(() => {
    chat.disconnect();
    upload.resetUpload();
    setApplicationState({
      currentStep: 'email_collection',
      isReady: false,
      error: null,
      canProceed: false,
    });
    setApplicationSummary(null);
    setUserInput('');
    setApplicantEmail('');
    setEmailError('');
  }, [chat, upload]);

  const retryConnection = useCallback(() => {
    setApplicationState(prev => ({
      ...prev,
      error: null,
      // Return to appropriate step based on upload status
      currentStep: upload.uploadResult?.success ? 'chat' : 'upload',
    }));

    // Use the chat's retry method which handles the full retry flow
    chat.retryConnection();
  }, [chat, upload.uploadResult]);

  // Interview scheduling functions
  const openSchedulingModal = useCallback(() => {
    setInterviewScheduling(prev => ({
      ...prev,
      isSchedulingModalOpen: true,
    }));
  }, []);

  const closeSchedulingModal = useCallback(() => {
    setInterviewScheduling(prev => ({
      ...prev,
      isSchedulingModalOpen: false,
    }));
  }, []);

  const openConfirmationModal = useCallback((date: string, time: string) => {
    setInterviewScheduling(prev => ({
      ...prev,
      selectedDate: date,
      selectedTime: time,
      isSchedulingModalOpen: false,
      isConfirmationModalOpen: true,
    }));
  }, []);

  const closeConfirmationModal = useCallback(() => {
    setInterviewScheduling(prev => ({
      ...prev,
      isConfirmationModalOpen: false,
    }));
  }, []);

  const confirmInterview = useCallback(() => {
    setInterviewScheduling(prev => ({
      ...prev,
      isScheduled: true,
      isConfirmationModalOpen: false,
    }));

    // Move to complete step after interview is scheduled
    setApplicationState(prev => ({
      ...prev,
      currentStep: 'complete',
    }));
  }, []);

  const goToInterviewScheduling = useCallback(() => {
    setApplicationState(prev => ({
      ...prev,
      currentStep: 'interview_scheduling',
    }));
  }, []);

  // Determine if user can proceed to next step
  useEffect(() => {
    let canProceed = false;

    switch (applicationState.currentStep) {
      case 'email_collection':
        canProceed = validateEmail(applicantEmail);
        break;
      case 'introduction':
        canProceed = true;
        break;
      case 'upload':
        canProceed = upload.uploadResult?.success || false;
        break;
      case 'chat':
        canProceed = chat.isConnected && !chat.isTyping;
        break;
      case 'complete':
        canProceed = false;
        break;
      case 'error':
        canProceed = false;
        break;
    }

    setApplicationState(prev => ({
      ...prev,
      canProceed,
    }));
  }, [
    applicationState.currentStep,
    upload.uploadResult,
    chat.isConnected,
    chat.isTyping,
    validateEmail,
    applicantEmail,
  ]);

  // Auto-advance from upload to chat when file is uploaded and chat receives confirmation
  useEffect(() => {
    if (
      applicationState.currentStep === 'upload' &&
      upload.uploadResult?.success &&
      chat.progress.cv_uploaded &&
      chat.messages.some(msg => msg.messageType === 'cv_uploaded')
    ) {
      // Small delay to let user see the success state, then continue in the same chat interface
      setTimeout(() => {
        setApplicationState(prev => ({
          ...prev,
          currentStep: 'chat',
        }));
      }, 1500);
    }
  }, [
    applicationState.currentStep,
    upload.uploadResult,
    chat.progress.cv_uploaded,
    chat.messages,
  ]);

  // Monitor network status
  useEffect(() => {
    // Only add event listeners in browser environment
    if (typeof window === 'undefined') return;

    const handleOnline = () => {
      setIsOnline(true);
      // Attempt to reconnect if we were disconnected due to network issues
      if (
        !chat.isConnected &&
        applicationState.currentStep === 'chat' &&
        applicantEmail
      ) {
        setTimeout(() => {
          chat.connectWithEmail(applicantEmail);
        }, 1000);
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      handleError(
        'Internet connection lost. Please check your network connection.'
      );
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [chat, applicationState.currentStep]);

  return {
    // Application state
    applicationState,
    applicationSummary,
    userInput,
    setUserInput,
    isOnline,
    applicantEmail,
    setApplicantEmail,
    emailError,
    handleEmailSubmit,
    validateEmail,

    // Chat state and functions
    chat: {
      messages: chat.messages,
      isConnected: chat.isConnected,
      isConnecting: chat.isConnecting,
      progress: chat.progress,
      applicationKey: chat.applicationKey,
      jobTitle: chat.jobTitle,
      isTyping: chat.isTyping,
      hasWelcomeResponse: chat.hasWelcomeResponse,
      sendMessage: chat.sendMessage,
      connect: chat.connect,
      connectWithEmail: chat.connectWithEmail,
      retryConnection: chat.retryConnection,
      disconnect: chat.disconnect,
      sendSkip: chat.sendSkip,
    },

    // Upload state and functions
    upload: {
      isUploading: upload.isUploading,
      uploadProgress: upload.uploadProgress,
      uploadedFile: upload.uploadedFile,
      uploadResult: upload.uploadResult,
      uploadFile: upload.uploadFile,
      resetUpload: upload.resetUpload,
      handleFileSelect: upload.handleFileSelect,
      handleFileDrop: upload.handleFileDrop,
      handleDragOver: upload.handleDragOver,
      handleDragEnter: upload.handleDragEnter,
      validateFile: upload.validateFile,
    },

    // Navigation functions
    startApplication,
    goToUpload,
    goToChat,
    proceedToChat,
    sendMessage,
    handleKeyPress,
    resetApplication,
    retryConnection,
    goToInterviewScheduling,

    // Interview scheduling
    interviewScheduling,
    openSchedulingModal,
    closeSchedulingModal,
    openConfirmationModal,
    closeConfirmationModal,
    confirmInterview,

    // Job data
    jobData,
  };
};
