'use client';

import axios from 'axios';
import { useCallback, useState } from 'react';

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface UploadResult {
  success: boolean;
  message?: string;
  error?: string;
  data?: any;
}

interface UseAIApplicationUploadProps {
  jobId: string;
  jobUuid?: string; // Add optional jobUuid parameter
  applicantEmail: string;
  isWebSocketConnected?: boolean;
  hasWelcomeResponse?: boolean;
  onUploadStart?: () => void;
  onUploadProgress?: (progress: UploadProgress) => void;
  onUploadComplete?: (result: UploadResult) => void;
  onUploadError?: (error: string) => void;
}

export const useAIApplicationUpload = ({
  jobId,
  jobUuid,
  applicantEmail,
  isWebSocketConnected = false,
  hasWelcomeResponse = false,
  onUploadStart,
  onUploadProgress,
  onUploadComplete,
  onUploadError
}: UseAIApplicationUploadProps) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress>({
    loaded: 0,
    total: 0,
    percentage: 0
  });
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [uploadResult, setUploadResult] = useState<UploadResult | null>(null);

  // Get upload URL
  const getUploadUrl = useCallback(() => {
    // Use the specific AI application backend URL
    const aiBackendUrl = process.env.NEXT_PUBLIC_AI_BACKEND_URL;

    // Remove trailing slash if present
    const baseUrl = aiBackendUrl?.replace(/\/$/, '');

    // Use jobUuid if available, otherwise fall back to jobId
    const jobIdentifier = jobUuid || jobId;
    console.log('Upload URL:', `${baseUrl}/api/v1/files/upload-cv/${jobIdentifier}/${applicantEmail}`);
    return `${baseUrl}/api/v1/files/upload-cv/${jobIdentifier}/${applicantEmail}`;
  }, [jobId, jobUuid, applicantEmail]);

  // Validate file
  const validateFile = useCallback((file: File): { valid: boolean; error?: string } => {
    // Check file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: 'Please upload a PDF, DOC, or DOCX file'
      };
    }

    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxSize) {
      return {
        valid: false,
        error: 'File size must be less than 10MB'
      };
    }

    return { valid: true };
  }, []);

  // Upload file
  const uploadFile = useCallback(async (file: File): Promise<UploadResult> => {
    // Validate WebSocket session first - critical requirement
    if (!isWebSocketConnected) {
      const error = 'WebSocket connection required. Please wait for connection to be established.';
      onUploadError?.(error);
      return { success: false, error };
    }

    if (!hasWelcomeResponse) {
      const error = 'Application session not ready. Please wait for welcome response.';
      onUploadError?.(error);
      return { success: false, error };
    }

    // Validate file
    const validation = validateFile(file);
    if (!validation.valid) {
      const error = validation.error || 'Invalid file';
      onUploadError?.(error);
      return { success: false, error };
    }

    const uploadUrl = getUploadUrl();
    if (!uploadUrl) {
      const error = 'Upload URL not available';
      onUploadError?.(error);
      return { success: false, error };
    }

    setIsUploading(true);
    setUploadedFile(file);
    setUploadResult(null);
    onUploadStart?.();

    try {
      // Create FormData
      const formData = new FormData();
      formData.append('cv', file);

      console.log('Uploading file to:', uploadUrl);
      console.log('File details:', {
        name: file.name,
        size: file.size,
        type: file.type
      });

      // Upload with progress tracking
      const response = await axios.post(uploadUrl, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const progress: UploadProgress = {
              loaded: progressEvent.loaded,
              total: progressEvent.total,
              percentage: Math.round((progressEvent.loaded * 100) / progressEvent.total)
            };
            
            setUploadProgress(progress);
            onUploadProgress?.(progress);
          }
        },
        timeout: 60000, // 60 second timeout
      });

      console.log('Upload response:', response.data);

      const result: UploadResult = {
        success: true,
        message: response.data.message || 'File uploaded successfully',
        data: response.data
      };

      setUploadResult(result);
      onUploadComplete?.(result);
      
      return result;

    } catch (error: any) {
      console.error('Upload error:', error);

      let errorMessage = 'Failed to upload file';

      if (!navigator.onLine) {
        errorMessage = 'No internet connection. Please check your network and try again.';
      } else if (error.response) {
        // Server responded with error status
        const status = error.response.status;
        const data = error.response.data;

        switch (status) {
          case 400:
            errorMessage = data?.message || 'Invalid file format or size. Please check your file and try again.';
            break;
          case 401:
            errorMessage = 'Authentication failed. Please refresh the page and try again.';
            break;
          case 403:
            errorMessage = 'You do not have permission to upload files.';
            break;
          case 404:
            errorMessage = 'Upload service not found. Please contact support.';
            break;
          case 413:
            errorMessage = 'File is too large. Please use a smaller file (max 10MB).';
            break;
          case 415:
            errorMessage = 'File type not supported. Please use PDF, DOC, or DOCX format.';
            break;
          case 429:
            errorMessage = 'Too many upload attempts. Please wait a moment and try again.';
            break;
          case 500:
            errorMessage = 'Server error occurred. Please try again later.';
            break;
          case 503:
            errorMessage = 'Upload service is temporarily unavailable. Please try again later.';
            break;
          default:
            errorMessage = data?.message || data?.error || `Upload failed (Error ${status})`;
        }
      } else if (error.request) {
        // Request was made but no response received
        errorMessage = 'No response from server. Please check your connection and try again.';
      } else if (error.code === 'ECONNABORTED') {
        // Request timeout
        errorMessage = 'Upload timeout. The file may be too large or your connection is slow. Please try again.';
      } else if (error.code === 'NETWORK_ERROR') {
        errorMessage = 'Network error. Please check your internet connection.';
      } else {
        // Something else happened
        errorMessage = error.message || 'An unexpected error occurred during upload.';
      }

      const result: UploadResult = {
        success: false,
        error: errorMessage
      };

      setUploadResult(result);
      onUploadError?.(errorMessage);

      return result;

    } finally {
      setIsUploading(false);
    }
  }, [
    isWebSocketConnected,
    hasWelcomeResponse,
    validateFile,
    getUploadUrl,
    onUploadStart,
    onUploadProgress,
    onUploadComplete,
    onUploadError
  ]);

  // Reset upload state
  const resetUpload = useCallback(() => {
    setIsUploading(false);
    setUploadProgress({ loaded: 0, total: 0, percentage: 0 });
    setUploadedFile(null);
    setUploadResult(null);
  }, []);

  // Handle file selection
  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      uploadFile(file);
    }
  }, [uploadFile]);

  // Handle drag and drop
  const handleFileDrop = useCallback((event: React.DragEvent<HTMLElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files?.[0];
    if (file) {
      uploadFile(file);
    }
  }, [uploadFile]);

  const handleDragOver = useCallback((event: React.DragEvent<HTMLElement>) => {
    event.preventDefault();
  }, []);

  const handleDragEnter = useCallback((event: React.DragEvent<HTMLElement>) => {
    event.preventDefault();
  }, []);

  return {
    isUploading,
    uploadProgress,
    uploadedFile,
    uploadResult,
    uploadFile,
    resetUpload,
    handleFileSelect,
    handleFileDrop,
    handleDragOver,
    handleDragEnter,
    validateFile
  };
};
