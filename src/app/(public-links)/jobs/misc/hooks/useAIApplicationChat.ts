'use client';

import { useCallback, useEffect, useRef, useState } from 'react';

// Message types based on the API documentation
export interface ChatMessage {
  id: string;
  type: 'bot' | 'user';
  content: string;
  timestamp: Date;
  messageType?:
    | 'welcome'
    | 'ai_response'
    | 'cv_uploaded'
    | 'cv_processed'
    | 'application_complete'
    | 'phase_complete'
    | 'chat_message';
  data?: any;
}

export interface ApplicationProgress {
  collected_fields: number;
  total_fields: number;
  missing_fields: string[];
  cv_uploaded: boolean;
  cv_processed: boolean;
  cv_score?: number;
  ready_for_completion: boolean;
  status:
    | 'started'
    | 'cv_uploaded'
    | 'cv_processing'
    | 'collecting_info'
    | 'completed'
    | 'rejected';
}

export interface ApplicationSummary {
  application_key: string;
  job_title: string;
  applicant_name: string;
  cv_score: number;
  status: string;
  threshold_met: boolean;
}

interface UseAIApplicationChatProps {
  jobUuid: string; // Changed from jobId to jobUuid to use job UUID instead of numeric ID
  applicantEmail: string;
  onApplicationComplete?: (summary: ApplicationSummary) => void;
  onError?: (error: string) => void;
}

export const useAIApplicationChat = ({
  jobUuid,
  applicantEmail,
  onApplicationComplete,
  onError,
}: UseAIApplicationChatProps) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [progress, setProgress] = useState<ApplicationProgress>({
    collected_fields: 0,
    total_fields: 9,
    missing_fields: [],
    cv_uploaded: false,
    cv_processed: false,
    ready_for_completion: false,
    status: 'started',
  });
  const [applicationKey, setApplicationKey] = useState<string>('');
  const [jobTitle, setJobTitle] = useState<string>('');
  const [isTyping, setIsTyping] = useState(false);
  const [hasWelcomeResponse, setHasWelcomeResponse] = useState(false);

  const socketRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const welcomeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const maxReconnectAttempts = 5;
  const welcomeTimeoutMs = 10000; // 10 seconds timeout for welcome response

  // Get WebSocket URL
  const getWebSocketUrl = useCallback(() => {
    // Use the specific AI application backend URL
    const aiBackendUrl = process.env.NEXT_PUBLIC_AI_BACKEND_URL;

    // Convert HTTP URL to WebSocket URL
    const protocol = aiBackendUrl?.startsWith('https://') ? 'wss://' : 'ws://';
    const baseUrl = aiBackendUrl?.replace(/^(https?:\/\/)/, '');

    return `${protocol}${baseUrl}/ws/chat/${jobUuid}`;
  }, [jobUuid]);

  // Add message to chat
  const addMessage = useCallback(
    (
      type: 'bot' | 'user',
      content: string,
      messageType?: string,
      data?: any
    ) => {
      const newMessage: ChatMessage = {
        id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
        type,
        content,
        timestamp: new Date(),
        messageType: messageType as any,
        data,
      };
      setMessages(prev => [...prev, newMessage]);
      return newMessage;
    },
    []
  );

  // Handle incoming WebSocket messages
  const handleMessage = useCallback(
    (event: MessageEvent) => {
      try {
        const data = JSON.parse(event.data);
        console.log('Received WebSocket message:', data);

        // Validate message structure
        if (!data.type || !data.data) {
          console.warn('Invalid message structure:', data);
          return;
        }

        switch (data.type) {
          case 'welcome':
            addMessage(
              'bot',
              data.data.message || 'Welcome to the application process!',
              'welcome',
              data.data
            );
            setApplicationKey(data.data.application_key || '');
            setJobTitle(data.data.job_title || '');
            setProgress(prev => ({
              ...prev,
              status: data.data.status || 'started',
            }));
            setHasWelcomeResponse(true);

            // Clear welcome timeout since we received the response
            if (welcomeTimeoutRef.current) {
              clearTimeout(welcomeTimeoutRef.current);
              welcomeTimeoutRef.current = null;
            }
            break;

          case 'ai_response':
            setIsTyping(false);
            addMessage(
              'bot',
              data.data.message || 'I received your message.',
              'ai_response',
              data.data
            );
            if (data.data.progress) {
              setProgress(prev => ({ ...prev, ...data.data.progress }));
            }
            break;

          case 'cv_uploaded':
            addMessage(
              'bot',
              data.data.message || 'Your CV has been uploaded successfully.',
              'cv_uploaded',
              data.data
            );
            setProgress(prev => ({
              ...prev,
              cv_uploaded: true,
              status: data.data.status || 'cv_uploaded',
            }));
            break;

          case 'cv_processed':
            addMessage(
              'bot',
              data.data.message || 'Your CV has been analyzed.',
              'cv_processed',
              data.data
            );
            setProgress(prev => ({
              ...prev,
              cv_processed: true,
              cv_score: data.data.cv_score || 0,
              status: data.data.status || 'cv_processing',
            }));
            break;

          case 'application_complete':
            addMessage(
              'bot',
              data.data.message || 'Your application has been completed!',
              'application_complete',
              data.data
            );
            setProgress(prev => ({ ...prev, status: 'completed' }));
            if (onApplicationComplete && data.data.application_summary) {
              onApplicationComplete(data.data.application_summary);
            }
            break;

          case 'phase_complete':
            // Clear typing indicator since we're receiving a response
            setIsTyping(false);

            // Add the congratulatory message and phase transition info
            addMessage(
              'bot',
              data.data.message || 'Phase completed! Moving to the next phase.',
              'phase_complete',
              data.data
            );

            // Update progress with phase information if available
            if (data.data.progress) {
              setProgress(prev => ({ ...prev, ...data.data.progress }));
            }

            // If there's a first question, add it as a separate message
            if (data.data.first_question) {
              // Small delay to show the phase transition message first
              setTimeout(() => {
                addMessage('bot', data.data.first_question, 'ai_response', {
                  ...data.data,
                  message: data.data.first_question,
                  is_interview_question: true,
                });
              }, 500);
            }
            break;

          case 'error':
            const errorMessage =
              data.data.message ||
              'An error occurred during the application process.';
            addMessage('bot', errorMessage, 'error', data.data);
            onError?.(errorMessage);
            break;

          default:
            console.warn('Unknown message type:', data.type);
            // Still add the message to chat for debugging
            addMessage(
              'bot',
              data.data.message || 'Received unknown message type.',
              data.type,
              data.data
            );
            break;
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
        const errorMsg =
          'Failed to parse server message. Please try refreshing the page.';
        addMessage('bot', errorMsg, 'error');
        onError?.(errorMsg);
      }
    },
    [addMessage, onApplicationComplete, onError]
  );

  // Connect to WebSocket with specific email
  const connect = useCallback(
    (emailForConnection?: string) => {
      if (isConnecting || isConnected) return;

      const wsUrl = getWebSocketUrl();
      if (!wsUrl) {
        onError?.('WebSocket URL not available');
        return;
      }

      // Use provided email or fall back to applicantEmail state
      const emailToUse = emailForConnection || applicantEmail;
      if (!emailToUse) {
        console.error('Cannot connect WebSocket without applicant email');
        onError?.('Email is required before connecting');
        return;
      }

      setIsConnecting(true);
      setHasWelcomeResponse(false); // Reset welcome response state for retry scenarios

      try {
        const ws = new WebSocket(wsUrl);
        socketRef.current = ws;

        ws.onopen = () => {
          console.log('WebSocket connected');
          setIsConnected(true);
          setIsConnecting(false);
          reconnectAttemptsRef.current = 0;

          // Immediately send start_application message after connection
          console.log(
            'WebSocket connected, sending start_application immediately with email:',
            emailToUse
          );
          startApplicationWithEmail(emailToUse);
        };

        ws.onmessage = handleMessage;

        ws.onclose = event => {
          console.log('WebSocket closed:', event.code, event.reason);
          setIsConnected(false);
          setIsConnecting(false);
          socketRef.current = null;

          // Determine if we should attempt to reconnect
          const shouldReconnect =
            event.code !== 1000 && // Not a normal closure
            event.code !== 1001 && // Not going away
            reconnectAttemptsRef.current < maxReconnectAttempts;

          if (shouldReconnect) {
            const delay = Math.min(
              Math.pow(2, reconnectAttemptsRef.current) * 1000,
              30000
            ); // Max 30s delay
            console.log(
              `Attempting to reconnect in ${delay}ms (attempt ${
                reconnectAttemptsRef.current + 1
              })`
            );

            reconnectTimeoutRef.current = setTimeout(() => {
              reconnectAttemptsRef.current++;
              connect(emailToUse);
            }, delay);
          } else if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
            const errorMsg =
              'Connection lost. Please check your internet connection and try again.';
            addMessage('bot', errorMsg, 'error');
            onError?.(errorMsg);
          } else if (event.code === 1006) {
            // Abnormal closure
            const errorMsg =
              'Connection was unexpectedly closed. Please try again.';
            addMessage('bot', errorMsg, 'error');
            onError?.(errorMsg);
          }
        };

        ws.onerror = error => {
          console.error('WebSocket error:', error);
          setIsConnecting(false);

          // Provide more specific error messages based on connection state
          let errorMessage = 'Connection error occurred.';
          if (!navigator.onLine) {
            errorMessage =
              'No internet connection. Please check your network and try again.';
          } else if (reconnectAttemptsRef.current === 0) {
            // First connection attempt failed
            errorMessage =
              'Failed to connect to the application service. This might be due to network issues or server unavailability.';
          } else {
            // Retry attempt failed
            errorMessage = `Connection attempt ${
              reconnectAttemptsRef.current + 1
            } failed. Please check your internet connection.`;
          }

          addMessage('bot', errorMessage, 'error');
          onError?.(errorMessage);
        };
      } catch (error) {
        console.error('Failed to create WebSocket:', error);
        setIsConnecting(false);
        onError?.('Failed to create WebSocket connection');
      }
    },
    [isConnecting, isConnected, getWebSocketUrl, handleMessage, onError]
  );

  // Disconnect WebSocket
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (welcomeTimeoutRef.current) {
      clearTimeout(welcomeTimeoutRef.current);
      welcomeTimeoutRef.current = null;
    }

    if (socketRef.current) {
      socketRef.current.close(1000, 'User disconnected');
      socketRef.current = null;
    }

    setIsConnected(false);
    setIsConnecting(false);
    setHasWelcomeResponse(false);
    reconnectAttemptsRef.current = 0;
  }, []);

  // Start application
  const startApplication = useCallback(() => {
    if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
      console.error('WebSocket not connected');
      return;
    }

    if (!applicantEmail) {
      console.error('Applicant email not available');
      return;
    }

    if (hasWelcomeResponse) {
      console.log(
        'Welcome response already received, skipping start_application'
      );
      return;
    }

    const message = {
      type: 'start_application',
      data: {
        applicant_email: applicantEmail,
      },
    };

    console.log(
      'Sending start_application message with email:',
      applicantEmail
    );
    console.log('Full message:', message);
    socketRef.current.send(JSON.stringify(message));

    // Set timeout for welcome response
    welcomeTimeoutRef.current = setTimeout(() => {
      if (!hasWelcomeResponse) {
        const errorMsg =
          'Application initialization timed out. Please try again.';
        addMessage('bot', errorMsg, 'error');
        onError?.(errorMsg);
      }
    }, welcomeTimeoutMs);
  }, [applicantEmail, hasWelcomeResponse, addMessage, onError]);

  // Start application with specific email (used during connection to avoid stale state)
  const startApplicationWithEmail = useCallback(
    (emailForApplication: string) => {
      if (
        !socketRef.current ||
        socketRef.current.readyState !== WebSocket.OPEN
      ) {
        console.error('WebSocket not connected');
        return;
      }

      if (!emailForApplication) {
        console.error('Email not provided for application start');
        return;
      }

      if (hasWelcomeResponse) {
        console.log(
          'Welcome response already received, skipping start_application'
        );
        return;
      }

      const message = {
        type: 'start_application',
        data: {
          applicant_email: emailForApplication,
        },
      };

      console.log(
        'Sending start_application message with email:',
        emailForApplication
      );
      console.log('Full message:', message);
      socketRef.current.send(JSON.stringify(message));

      // Set timeout for welcome response
      welcomeTimeoutRef.current = setTimeout(() => {
        if (!hasWelcomeResponse) {
          const errorMsg =
            'Application initialization timed out. Please try again.';
          addMessage('bot', errorMsg, 'error');
          onError?.(errorMsg);
        }
      }, welcomeTimeoutMs);
    },
    [hasWelcomeResponse, addMessage, onError]
  );

  // Send chat message
  const sendMessage = useCallback(
    (message: string) => {
      if (
        !socketRef.current ||
        socketRef.current.readyState !== WebSocket.OPEN
      ) {
        console.error('WebSocket not connected');
        onError?.('Not connected to chat service');
        return;
      }

      if (!message.trim()) return;

      // Add user message to chat
      addMessage('user', message, 'chat_message');

      // Send to server
      const payload = {
        type: 'chat_message',
        data: {
          message: message.trim(),
        },
      };

      console.log('Sending chat message:', payload);
      socketRef.current.send(JSON.stringify(payload));

      // Show typing indicator
      setIsTyping(true);
    },
    [addMessage, onError]
  );

  // Send skip as a normal chat message ("skip")
  const sendSkip = useCallback(() => {
    if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
      console.error('WebSocket not connected');
      onError?.('Not connected to chat service');
      return;
    }

    // Add user message to chat for immediate feedback
    addMessage('user', 'skip', 'chat_message');

    // Send normal chat payload
    const payload = {
      type: 'chat_message',
      data: { message: 'skip' },
    } as const;

    console.log('Sending skip as chat message:', payload);
    socketRef.current.send(JSON.stringify(payload));

    // Show typing indicator until server responds
    setIsTyping(true);
  }, [addMessage, onError]);


  // Note: WebSocket connection is now manually triggered, not automatic
  // The connection will be initiated when explicitly requested via connect() method

  // Handle email changes - reconnect WebSocket with new email if already connected
  const previousEmailRef = useRef<string>('');
  useEffect(() => {
    console.log('useEffect [email change detection] triggered:', {
      currentEmail: applicantEmail,
      previousEmail: previousEmailRef.current,
      isConnected,
      emailChanged:
        previousEmailRef.current && previousEmailRef.current !== applicantEmail,
    });

    // If email changes and we're already connected, we need to reconnect with the new email
    if (
      applicantEmail &&
      previousEmailRef.current &&
      previousEmailRef.current !== applicantEmail &&
      isConnected
    ) {
      console.log(
        'Email changed from',
        previousEmailRef.current,
        'to',
        applicantEmail,
        '- reconnecting WebSocket'
      );

      // Disconnect current connection
      disconnect();

      // Note: Manual reconnection will be required via connect() method
      // We don't automatically reconnect here to maintain explicit control
    }

    // Update the previous email reference
    previousEmailRef.current = applicantEmail;
  }, [applicantEmail, isConnected, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  // Connect with email - this is the main method to initiate connection
  const connectWithEmail = useCallback(
    (email: string) => {
      if (!email) {
        console.error('Email is required to connect');
        onError?.('Email is required to connect');
        return;
      }

      console.log('Connecting WebSocket with email:', email);

      // Reset reconnection attempts for new connection
      reconnectAttemptsRef.current = 0;

      // Connect with the provided email - pass email to avoid stale state issues
      connect(email);
    },
    [connect, onError]
  );

  // Retry connection with better error handling
  const retryConnection = useCallback(() => {
    if (!applicantEmail) {
      console.error('Cannot retry connection without email');
      onError?.('Email is required to retry connection');
      return;
    }

    console.log('Retrying WebSocket connection...');

    // Disconnect first to ensure clean state
    disconnect();

    // Small delay to ensure cleanup is complete, then reconnect
    setTimeout(() => {
      connectWithEmail(applicantEmail);
    }, 500);
  }, [applicantEmail, disconnect, connectWithEmail, onError]);

  return {
    messages,
    isConnected,
    isConnecting,
    progress,
    applicationKey,
    jobTitle,
    isTyping,
    hasWelcomeResponse,
    connect,
    connectWithEmail,
    retryConnection,
    disconnect,
    sendMessage,
    startApplication,
    sendSkip,
  };
};
