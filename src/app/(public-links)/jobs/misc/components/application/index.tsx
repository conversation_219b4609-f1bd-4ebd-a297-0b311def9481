import * as yup from 'yup';

// Define a function to check if the file type is valid
export const isValidFileType = (file: File | any) => {
  if (!file) {
    return true; // No file, no validation needed
  }

  const allowedTypes = [
    'application/pdf',
    'image/jpeg',
    'image/png',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  ];
  return allowedTypes.includes(file.type);
};

// Define a function to check if the file size is within the limit
export const isFileSizeWithinLimit = (
  file: File | any,
  maxSizeInBytes: number
) => {
  if (!file) {
    return true; // No file, no validation needed
  }

  return file.size <= maxSizeInBytes;
};

type CustomJobRequirement = {
  custom_file: File;
  custom_text: string;
  custom_field_name?: string;
  custom_files: {
    custom_file_id: string;
    custom_file_url: string;
  };
};
export interface FormDataProps {
  email: string;
  name: string;
  years_of_experience: string;
  password: string;
  confirm_password: string;
  country: string;
  phone_number: string;
  state: string;
  cv: string;
  cv_and_resume_id: string;
  cover_letter_id: string;
  cover_letter_file: File | null;
  cover_letter_text: string;
  acceptCV: boolean;
  hasCoverLetter: boolean;
  custom_job_requirements: Array<CustomJobRequirement>;
  gender: 'male' | 'female' | 'other';
  portfolio_link: string;
  linkedin_link: string;
  preferred_location?: string;
}

export interface FileObject {
  file: File;
}

export const isFile = (value: any): value is FileObject => {
  return value && value.file instanceof File;
};

const customJobRequirementSchema = yup.array().of(
  yup.object().shape({
    custom_text: yup
      .string()
      .test('custom_text', 'Custom text is required', function (value) {
        const customFile = this.parent.custom_file;

        if (customFile !== null && value === null) {
          return this.createError({
            message: 'Custom text is required',
            path: 'custom_text',
          });
        }

        return true;
      })
      .min(2),
    custom_field_name: yup.string().optional(),
  })
);

//   custom_file_id: yup.string().required('Custom file ID is required'),
// custom_file_url: yup.string().required('Custom file URL is required')

export const nonTalentSchema = yup.object().shape(
  {
    email: yup.string().email('Invalid email format').required().trim(),
    name: yup
      .string()
      .required('Name is required')
      .test(
        'is-valid-name',
        'Name must not be empty or contain only whitespace characters and must contain at least one letter',
        (value: string | undefined) => {
          if (!value) return false; // Value is empty
          const trimmedValue = value.trim();
          if (trimmedValue.length === 0) return false; // Value consists of only whitespace characters
          return /[a-zA-Z]/.test(trimmedValue); // Value contains at least one letter
        }
      ),

    years_of_experience: yup.string().required(),
    hasCoverLetter: yup.boolean(),
    phone_number: yup.string().required(),
    password: yup.string().min(4).required(),
    confirm_password: yup
      .string()
      .oneOf([yup.ref('password')], 'Passwords must match'),
    country: yup.string().required(),
    state: yup.string(),
    portfolio_link: yup.string(),
    linkedin_link: yup.string(),
    gender: yup
      .string()
      .required('Gender is required')
      .oneOf(['male', 'female', 'other'], 'Invalid gender'),

    cv: yup.mixed().when('acceptCV', {
      is: true,
      then: schema =>
        schema
          .required('Needed')
          .test('filePresence', 'File is required', value => !!value)
          .test('fileType', 'Invalid file type', isValidFileType)
          .test('fileSize', 'File size exceeds 10MB limit', value =>
            isFileSizeWithinLimit(value, 10 * 1024 * 1024)
          ),
      otherwise: schema => schema, // technically this otherwise isnt needed
    }),

    // cv: yup.mixed().when(['acceptCV', 'cv_and_resume_id'], {
    //     is: (acceptCV: boolean, cvAndResumeId: string) => acceptCV && cvAndResumeId === "",
    //     then: (schema) => schema.required("Needed")
    //         .test('filePresence', 'File is required', (value) => !!value)
    //         .test('fileType', 'Invalid file type', isValidFileType)
    //         .test('fileSize', 'File size exceeds 10MB limit', (value) =>
    //             isFileSizeWithinLimit(value, 10 * 1024 * 1024)
    //         ),
    //     otherwise: (schema) => schema.strip(), // Ignore validation if cv_and_resume_id is provided
    // }),

    // cover_letter_file: fileSchema.fields.cover_letter_file,
    // cover_letter_text: textSchema.fields.cover_letter_text,
    preferred_location: yup.string().when("isMultiLocation", {
      is: true,
      then: (schema) => schema.required("Preferred location is required when multiple locations are available"),
      otherwise: (schema) => schema.optional(),
    }),
    acceptCV: yup.boolean(),
    isMultiLocation: yup.boolean(),


    custom_job_requirements: customJobRequirementSchema,
  },
  //@ts-ignore
  ['cover_letter_text', 'cover_letter_file']
);

export type FormDataPropsWithoutPasswords = Omit<
  FormDataProps,
  'password' | 'confirm_password'
>;

export const talentSchema = yup.object().shape({
  email: yup.string().email('Invalid email format').required().trim(),
  name: yup
    .string()
    .required('Name is required')
    .test(
      'is-valid-name',
      'Name must not be empty or contain only whitespace characters and must contain at least one letter',
      (value: string | undefined) => {
        if (!value) return false; // Value is empty
        const trimmedValue = value.trim();
        if (trimmedValue.length === 0) return false; // Value consists of only whitespace characters
        return /[a-zA-Z]/.test(trimmedValue); // Value contains at least one letter
      }
    ),
  years_of_experience: yup.string().required(),
  hasCoverLetter: yup.boolean(),
  phone_number: yup.string().required(),
  country: yup.string().required(),
  state: yup.string(),
  portfolio_link: yup.string(),
  linkedin_link: yup.string(),
  gender: yup
    .string()
    .required('Gender is required')
    .oneOf(['male', 'female', 'other'], 'Invalid gender'),
  // cv: yup.mixed().when('acceptCV', {
  //     is: true,
  //     then: (schema) => schema.required("Needed")
  //         .test('filePresence', 'File is required', (value) => !!value)
  //         .test('fileType', 'Invalid file type', isValidFileType)
  //         .test('fileSize', 'File size exceeds 10MB limit', (value) =>
  //             isFileSizeWithinLimit(value, 10 * 1024 * 1024)
  //         ),
  //     otherwise: (schema) => schema, // technically this otherwise isnt needed
  // }),
  cv: yup.mixed().when(['acceptCV', 'cv_and_resume_id'], {
    is: (acceptCV: boolean, cvAndResumeId: string) =>
      acceptCV && cvAndResumeId === '',
    then: schema =>
      schema
        .required('Needed')
        .test('filePresence', 'File is required', value => !!value)
        .test('fileType', 'Invalid file type', isValidFileType)
        .test('fileSize', 'File size exceeds 10MB limit', value =>
          isFileSizeWithinLimit(value, 10 * 1024 * 1024)
        ),
    otherwise: schema => schema.strip(), // Ignore validation if cv_and_resume_id is provided
  }),
  preferred_location: yup.string().when("isMultiLocation", {
    is: true,
    then: (schema) => schema.required("Preferred location is required when multiple locations are available"),
    otherwise: (schema) => schema.optional(),
  }),
  acceptCV: yup.boolean(),
  isMultiLocation: yup.boolean(),

  custom_job_requirements: customJobRequirementSchema,

  //@ts-ignore
});
