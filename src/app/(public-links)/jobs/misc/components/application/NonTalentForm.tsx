import React, { useEffect, useRef, useState } from 'react'
import { useParams } from 'next/navigation';
import dynamic from 'next/dynamic';
import { Controller, useForm } from 'react-hook-form';
import { Country, State, ICountry, IState } from 'country-state-city';
import ReactTextareaAutosize from 'react-textarea-autosize';
import PhoneInput from 'react-phone-input-2'
import ReactLoading from "react-loading"


import { cn } from '@/utils';
import { yupResolver } from '@hookform/resolvers/yup';
import { SelectSingleCombo, Select } from '@/components/shared';


import UploadIcon from '../../icons/UploadIcon';

import { experience_level, genderArray } from './data';
import { useNonJobFetchJobDetail } from '../../api/jobdetails';
import { FormDataProps, nonTalentSchema } from '.';
import 'react-phone-input-2/lib/style.css'
import 'react-quill/dist/quill.snow.css'; // Make sure to import the Quill styles
import { useCreateNonTalentJob } from '../../api/non_talent_job_application';
import EyeIcon from '../../icons/EyeIcon';
import RemoveIcon from '../../icons/RemoveIcon';
import TalentErrorModal from './ErrorModal';
import TalentSuccessModal from './SuccessModal';
import { deleteFromCloudinary, uploadToServer } from './upload';


const ReactQuill = dynamic(() => import('react-quill'), {
    ssr: false, // This ensures that Quill is only loaded on the client side
});


interface Props {
    userEmail: string
}




const NonTalentForm = ({ userEmail }: Props) => {
    const [resumeFIle, setResumeFIle] = useState<File | null>(null);
    const params: any = useParams()
    const [coverLetterFIle, setCoverLetterFIle] = useState<File | null>(null);
    const { data } = useNonJobFetchJobDetail(params?.id, true)
    // const [customFiles, setCustomFiles] = useState<{ [id: number]: File | null }>({});
    const [loading, setLoading] = useState<boolean[]>(data?.requirement_custom_fields?.custom_fields?.map(() => false) ?? []);
    const [countryList, setCountryList] = useState<ICountry[]>()
    const [stateList, setStateList] = useState<IState[]>()
    const [countryCode, setCountryCode] = useState("")
    const [showPassword, setShowPassword] = useState(false)
    const fileInputRef = useRef<HTMLInputElement>(null)
    const [showConfirmPassword, setShowConfirmPassword] = useState(false)
    const [showSuccessModal, setShowSuccessModal] = useState(false)

    const [showErrorModal, setShowErrorModal] = useState(false)
    const [errorMsg, setErrorMsg] = useState("")
    const [successData, setSuccessData] = useState<any>()
    const [nonTalentPass, setNonTalentPass] = useState("")
    const {
        register,
        control,
        handleSubmit,
        setValue,
        watch,
        setError,
        clearErrors,
        formState: { isValid, errors },
    } = useForm<FormDataProps | any>({
        resolver: yupResolver(nonTalentSchema),
        defaultValues: {
            email: userEmail,
            name: "",
            years_of_experience: "No experience",
            password: "",
            confirm_password: "",
            country: "",
            state: "",
            phone_number: "",
            amount: "",
            currency: "",
            cv: "",
            gender: "male",
            cover_letter_file: "",
            cover_letter_text: "",
            acceptCV: data?.requirement_custom_fields?.accept_resume,
            isMultiLocation: data?.is_multi_location && !!data?.preferred_locations.length,
            preferred_location: '',
            hasCoverLetter: data?.requirement_custom_fields?.accept_cover_letter,
            custom_job_requirements: [],
            portfolio_link: "",
            linkedin_link: ""
        },
        mode: "onChange",

    });

    const docx = [
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/docx'
    ];
    const customJobRequirements = watch('custom_job_requirements');




    const { mutate: handleSubmitNotTalentJob, isLoading: isSubmitting } = useCreateNonTalentJob()
    const handleApplication = async (data: FormDataProps) => {
        let isValid = true; // Track overall validation status

        // Validate custom fields
        data?.custom_job_requirements?.forEach((requirement: any, index: number) => {
            const { custom_file, custom_files } = requirement;

            if (custom_file) {
                // Check if custom_files.custom_file_id is missing or empty
                if (!custom_files || !custom_files.custom_file_id || custom_files.custom_file_id === "") {
                    setError(`custom_job_requirements.${index}.custom_files`, {
                        type: 'required',
                        message: 'Custom file is not provided.'
                    });
                    isValid = false; // Set overall validation status to false
                } else {
                    // Clear any existing errors for custom_files.custom_file_id
                    clearErrors(`custom_job_requirements.${index}.custom_files`);
                }
            }
        });

        if (!isValid) {
            // Validation failed, do not submit form
            return;
        }

        // Validation passed, proceed to submit form
        // Prepare data for submission
        let non_talent_data = {
            current_location: `${data?.state},${data?.country}`,
            expected_salary: ``,
            email: data?.email,
            name: data?.name,
            years_of_experience: data?.years_of_experience,
            password: data?.password,
            phone_number: data?.phone_number,
            cv: data?.cv,
            gender: data?.gender,
            preferred_location: data?.preferred_location,
            cover_letter_file: data?.cover_letter_file,
            hasCoverLetter: data?.hasCoverLetter,
            cover_letter_text: data?.cover_letter_text,
            custom_job_requirements: data?.custom_job_requirements?.map(item => {
                // Create a copy of the item without the custom_file property
                const { custom_file, ...rest } = item;
                return rest; // Return the modified item without the custom_file property
            }),
            custom_job_requirements_id: data?.custom_job_requirements?.filter(item => item.custom_files?.custom_file_id),



            portfolio_link: data?.portfolio_link,
            linkedin_link: data?.linkedin_link
        }

        let mydata = {
            job_id: params?.id,
            non_talent_data,
        }




        handleSubmitNotTalentJob(mydata, {
            onSuccess: (data) => {
                setSuccessData(data);
                setNonTalentPass(mydata?.non_talent_data?.password)
                setShowSuccessModal(true)
            },
            onError: (error: any) => {
                mydata?.non_talent_data?.custom_job_requirements_id?.map(async (x: any) => {
                    let id = x?.custom_file?.id !== undefined && x?.custom_file?.id;
                    try {
                        await deleteFromCloudinary(String(id));
                    } catch (error: any) { }
                });

                setShowErrorModal((prev) => !prev);
                if (error?.response) {
                    const errorMessage = error?.response?.data?.message || error?.response?.data?.non_field_errors[0] || "Something went wrong.";
                    setErrorMsg(errorMessage);
                    // console.log(error?.response?.data?.non_field_errors[0]);
                } else {
                    setErrorMsg("An unexpected error occurred.");
                    console.error("Unexpected error:", error);
                }
            }
        })


    }

    useEffect(() => {
        setCountryList(Country?.getAllCountries())
    }, [])

    useEffect(() => {
        setStateList(State?.getStatesOfCountry(String(countryCode)))
    }, [countryCode])



    const countryOptions = countryList?.map(country => ({ value: country?.name, label: country.name, code: country?.isoCode }))
    const stateOptions = stateList?.map(state => ({ value: state?.name, label: state.name, code: state?.isoCode }))


    const [coverLetterTextNotEmpty, setCoverLetterTextNotEmpty] = useState(false)


    const validateCVFields = async () => {
        const data = watch(); // Get current form data

        if (!data?.acceptCV) {
            return; // No need to validate if acceptCV is not true
        }

        try {
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for 1 second

            const hasCvValue = !!data?.cv;

            if (!hasCvValue) {
                setError('cv', { type: 'manual', message: 'upload file' });
            } else {
                clearErrors('cv');
            }
        } catch (error) {
            console.error('Error occurred during CV validation:', error);
        }
    };
    const validateCoverLetterFields = async () => {
        const data = watch(); // Get current form data
        if (data?.hasCoverLetter) {
            if (data?.cover_letter_text === "<p><br></p>") {
                setValue("cover_letter_text", "");
            }
            try {
                await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for 1 seconds
                const hasCoverLetterText = !!data?.cover_letter_text;
                const hasCoverLetterFile = !!data?.cover_letter_file;
                if (!hasCoverLetterText && !hasCoverLetterFile) {
                    setError('cover_letter_text', { type: 'manual', message: 'Either cover letter text or file is required' });
                    setError('cover_letter_file', { type: 'manual', message: 'Either cover letter text or file is required' });
                } else {
                    clearErrors(['cover_letter_text', 'cover_letter_file']);
                }
            } catch (error) {
                console.error('Error occurred during cover letter validation:', error);
            }
        }
    };

    const validateForm = async () => {
        const data = watch(); // Get current form data
        // console.log(data?.cover, "textxtetxetetx");
        if (data?.cover_letter_text?.length > 0) {
            setCoverLetterTextNotEmpty(true);
        } else {
            setCoverLetterTextNotEmpty(false);
        }
        await validateCVFields()
        await validateCoverLetterFields();
    };

    useEffect(() => {
        validateForm();
    }, [watch()]);

    // console.log(errors &&);

    const watcxx = watch("custom_job_requirements")


    // console.log(watcxx, errors);


    // Function to handle file selection
    // const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    //     const file = e.target.files && e.target.files[0];
    //     if (file) {
    //         // Set the selected file in the state
    //         removePictures(index)
    //         setCustomFiles((prevFiles) => ({
    //             ...prevFiles,
    //             [index]: file,
    //         }));
    //         // Trigger upload to Cloudinary
    //         handleUploadToCloudinary(file, index);
    //     } else {
    //         // If no file is selected (e.g., user clicks cancel), remove the file from the state
    //         removePictures(index);
    //     }

    //     // Reset the value of the file input field to allow selecting the same file again
    //     e.target.value = '';
    // };
    const [customFiles, setCustomFiles] = useState<File[]>([]);

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
        const file = e.target.files && e.target.files[0];
        if (file) {
            // Set the selected file in the state
            removePictures(index);
            setCustomFiles((prevFiles) => ({
                ...prevFiles,
                [index]: file,
            }));
            // Set the value of the custom_file field in the form state to the uploaded file
            setValue(`custom_job_requirements.${index}.custom_file`, file);
            // Trigger upload to Cloudinary
            handleUploadToCloudinary(file, index);
        } else {
            // If no file is selected (e.g., user clicks cancel), remove the file from the state
            removePictures(index);
        }

        // Reset the value of the file input field to allow selecting the same file again
        e.target.value = '';
    };





    // Function to handle file upload to Cloudinary
    const handleUploadToCloudinary = async (file: File, index: number) => {
        try {
            const fieldType = data?.requirement_custom_fields?.custom_fields[index].fieldType;
            if (fieldType === "FILE_UPLOAD") {
                setLoading(prevLoading => {
                    const newLoading = [...prevLoading];
                    newLoading[index] = true;
                    return newLoading;
                });

                // Check if file is a valid instance of File
                if (file instanceof File) {
                    // Call uploadToServer function to upload file
                    const { secure_url, id } = await uploadToServer(file);

                    // Update form values with uploaded file information
                    setValue(`custom_job_requirements.${index}.custom_files.custom_file_url`, secure_url);
                    setValue(`custom_job_requirements.${index}.custom_files.custom_file_id`, id);
                    // Clear error for custom_files.custom_file_id and custom_files.custom_file_url
                    clearErrors(`custom_job_requirements.${index}.custom_files`);
                } else {
                    console.error('Invalid file:', file);
                }
            }
        } catch (error) {
            console.error('Error uploading to Cloudinary:', error);
        } finally {
            setLoading(prevLoading => {
                const newLoading = [...prevLoading];
                newLoading[index] = false;
                return newLoading;
            });
        }
    };






    //remove icons from cloudinery and dom
    const removePictures = async (index: number) => {
        setCustomFiles((prevFiles) => {
            const updatedFiles = { ...prevFiles };
            delete updatedFiles[index];
            return updatedFiles;
        });

        let imgIdToDelete = watcxx[index]?.custom_files?.custom_file_id //Access img_id at the specified index
        if (imgIdToDelete) {
            try {
                await deleteFromCloudinary(imgIdToDelete); // Pass the public_id for deletion
                setValue(`custom_job_requirements.${index}.custom_files.custom_file_url`, "");
                setValue(`custom_job_requirements.${index}.custom_files.custom_file_id`, "");
                setError(`custom_job_requirements.${index}.custom_files`, {
                    type: 'required',
                    message: 'Custom files must include a non-empty custom_file_id.'
                });
            } catch (error: any) {
                console.error('Error deleting image:', error.message);
            }
        } else {
            // console.error('img_id is missing');
        }

        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };



    return (
        <div className='px-4 lg:px-1'>
            <form action="" noValidate onSubmit={handleSubmit(handleApplication)}>
                <div className={`inputdiv  `}>
                    <label htmlFor="email" className=''>Email</label>
                    <input type="text" id="email"  {...register("email")} placeholder='<EMAIL>' disabled className={cn("!bg-[#f5f7f9] lowercase", errors?.email?.message && "error")} />
                    {errors?.email && <span className='text-red-500 text-xs mt-1'>{errors?.email?.message as string}</span>}
                </div>

                <div className={`inputdiv  `}>
                    <label htmlFor="name" className=''>Full Name</label>
                    <input type="text" id="name"  {...register("name")} placeholder='Joseph Abeokuta' className={cn("!bg-[#f5f7f9]", errors?.name?.message && "error")} />
                    {errors && errors?.name && <span className='formerror'>{errors?.name?.message as string}</span>}
                </div>

                <div className='inputdiv '>
                    <label className='flex flex-col' htmlFor="password">
                        <span>
                            Create password
                        </span>
                        <span className='text-[#4A4A68] text-xs mb-2'>
                            Create a password to access getlinked and view your application status
                        </span>
                    </label>
                    <div className='relative'>
                        <input type={showPassword ? 'text' : 'password'} placeholder="min 8 characters" className={cn("!bg-[#f5f7f9]", errors.password && "error", "")}   {...register('password')} id="password" />
                        <span className="absolute right-[3%] top-[25%] cursor-pointer" onClick={() => setShowPassword(!showPassword)} >
                            <EyeIcon />
                        </span>
                    </div>
                    {errors.password && <p className='formerror'>{errors.password.message as string}</p>}
                </div>
                <div className='inputdiv '>
                    <label htmlFor="confirm_password">
                        Confirm password
                    </label>
                    <div className='relative'>
                        <input type={showConfirmPassword ? 'text' : 'password'} placeholder="min 8 characters" className={cn("!bg-[#f5f7f9]", errors.confirm_password && "error", "")}   {...register('confirm_password')} id="confirm_password" />
                        <span className="absolute right-[3%] top-[25%] cursor-pointer" onClick={() => setShowConfirmPassword(!showConfirmPassword)} >
                            <EyeIcon />
                        </span>
                    </div>
                    {errors.confirm_password && <p className='formerror'>{errors.confirm_password.message as string}</p>}
                </div>



                <Select
                    label="Years of Experience"
                    {...register("years_of_experience")}
                    onChange={(val) => setValue("years_of_experience", val)}
                    fullWidth
                    options={experience_level}
                    labelKey='name'
                    valueKey='value'
                    className="bg-[#f5f7f9]"
                    placeholder="Select experience level"
                    hasError={!!errors.years_of_experience}
                    errorMessage={errors?.years_of_experience?.message as string}
                />


                <div className="flex flex-col mt-4">
                    <label className='text-header-text text-sm'>Location</label>
                    <div className="grid w-full flex-col sm:grid-cols-2 items-center sm:gap-4">
                        <SelectSingleCombo
                            name='country'
                            placeholder="Select Country"
                            value={watch("country")}
                            onChange={(val) => {
                                const chosen = countryOptions && countryOptions.filter((country) => country.value.toLowerCase() == val.toLocaleLowerCase())[0]
                                setValue("country", chosen?.value!)
                                countryOptions && setCountryCode(chosen?.code!)
                            }}
                            className='bg-[#f5f7f9]'
                            options={countryOptions! || []}
                            valueKey='value'
                            labelKey="label"
                        />

                        <SelectSingleCombo
                            name='state'
                            placeholder="Select state"
                            value={watch("state")}
                            valueKey='value'
                            labelKey="label"
                            onChange={(val) =>
                                setValue("state", val)
                            }
                            className='bg-[#f5f7f9]'
                            options={stateOptions || []}
                            isLoadingOptions={!stateOptions || watch('country') == undefined}

                        />
                    </div>
                </div>

                {
                    data?.is_multi_location && data?.preferred_locations.length &&
                    <div className='my-4'>

                        <SelectSingleCombo
                            name="state"
                            label="Preferred Location"
                            placeholder="Select preferred location"
                            value={watch('preferred_location')}
                            valueKey="value"
                            labelKey={"name"}
                            onChange={val => setValue('preferred_location', val)}
                            className="bg-[#f5f7f9]"
                            variant="dark_showcase"
                            containerClass="max-sm:my-1"
                            options={data?.preferred_locations.map(loc => ({ name: loc, value: loc })) || []}
                            hasError={!!errors?.state}
                            errorMessage={errors?.state?.message as string}
                            isLoadingOptions={!stateOptions || watch('country') == undefined}
                            fullWidth
                        />

                        <div className="text-xs bg-primary-light text-primary p-2 px-4 mt-2 rounded-xl">
                            This job has multiple locations set, which means it means that it can be performed from more than one office or city, giving you the flexibility to choose the one that suits you best. Select your preferred location—whether it's the closest to you, the most convenient, or simply your favorite!
                        </div>
                    </div>
                }
                <Select
                    placeholder="Select gender"
                    label="Gender"
                    value={watch("gender")}
                    {...register('gender')}
                    valueKey='value'
                    labelKey='name'
                    onChange={(val) => setValue("gender", val)}
                    options={genderArray}
                    className='bg-[#f5f7f9]'
                    variant="dark_showcase"
                    containerClass='my-1.5'
                    hasError={!!errors.gender}
                    errorMessage={errors.gender?.message as string}
                    fullWidth
                />


                <div className={`inputdiv`}>
                    <label htmlFor="phone_number" className='non-talent-label text-sm'>Phone Number</label>
                    <div className={`flex items-center text-sm py-2 px-3 rounded-lg bg-[#F5F7F9] ${errors?.phone_number ? "border-red-500" : ""} `}>
                        <Controller
                            control={control}
                            name="phone_number"
                            render={({ field: { ref, ...field } }) => (
                                <PhoneInput
                                    {...field}
                                    containerStyle={{ display: "flex", alignItems: "center", height: "2rem", width: "100%" }}
                                    // defaultMask='ng'
                                    country={"ng"}
                                    inputStyle={{ height: "100%", width: "100%", outline: "none", color: "#818181", border: "none", backgroundColor: "transparent", }}
                                    // countryCodeEditable={false}
                                    placeholder='+234 9090003426'
                                    specialLabel={"Mobile Number"}
                                    buttonStyle={{ borderRadius: "100%", height: "1.6rem", width: "1.7rem", padding: "0px", marginBlock: "auto", border: "none", justifyContent: "center", alignItems: "center", backgroundColor: "#ECE7EE", }}
                                    disableDropdown
                                    dropdownStyle={{ border: "2px solid red" }}
                                // disabled={!error?.phone_number?}

                                />
                            )}
                        />
                    </div>
                </div>
                <div className="inputdiv">
                    <label htmlFor="email" className=''>Portfolio link (Optional)</label>
                    <input type="text" {...register("portfolio_link")} name="" placeholder='Enter portfolio link' className={cn("!bg-[#f5f7f9]")} id="" />
                </div>
                <div className="inputdiv">
                    <label htmlFor="email" className=''>LinkedIn (Optional)</label>
                    <input type="text" {...register("linkedin_link")} name="" placeholder='Enter LinkedIn' className={cn("!bg-[#f5f7f9]")} id="" />
                </div>


                {data?.requirement_custom_fields?.accept_resume &&
                    <div className={`flex flex-col mt-7 `}>
                        <label htmlFor="upload-resume" className='non-talent-label text-sm'>Upload Resume</label>
                        <div className={`w-full ${errors?.cv?.message ? "showcase-input-error" : ""}`}>
                            <Controller
                                name="cv"
                                control={control}
                                render={({ field }) => (
                                    <div className={` ${resumeFIle !== null ? "justify-between bg-[#F6F5FF] z-40 rounded-lg shrink-0 flex items-center w-full h-[7.3125rem] " : "bg-[#F5F7F9] "}  `}>
                                        <label htmlFor='upload-resume' className={` cursor-pointer ${resumeFIle === null ? "non-talent-upload  h-[6.3125rem] " : "flex items-center p-6 gap-x-[1.31rem] "}`}>
                                            <div className=""><UploadIcon /></div>

                                            <div className="mt-2 text-primary ">
                                                <input onChange={(e) => {
                                                    field?.onChange(e.target.files && e.target.files[0],
                                                        setResumeFIle(e.target.files && e.target.files[0])
                                                    )
                                                }} type='file' id='upload-resume' className='hidden' name='uploadddd' accept={`${docx}, application/pdf`
                                                } ref={fileInputRef} />
                                                <p className='non-talent-upload-text'> {resumeFIle !== null ? resumeFIle?.name : "Tap to upload document"}</p>
                                                {
                                                    resumeFIle !== null ?
                                                        <p className='text-[0.875rem] non-talent-upload-text w-full'>Tap on <span className='text-primary font-medium'>change</span> to select other CVs</p>
                                                        :
                                                        <p className=' non-talent-upload-subtext-text'>Files types: Pdf or Doc, Docx. RTF Max file size 10MB</p>
                                                }
                                            </div>

                                        </label>
                                        {resumeFIle !== null && <div className="select-none h-full  px-3 py-3">

                                            <RemoveIcon onClick={() => {
                                                setValue("cv", null)
                                                setResumeFIle(null)
                                                if (fileInputRef.current) {
                                                    fileInputRef.current.value = '';
                                                }
                                            }} className='cursor-pointer' />
                                        </div>}


                                    </div>
                                )}
                            />

                        </div>


                    </div>
                }
                {data?.requirement_custom_fields?.accept_cover_letter &&
                    <div className={`flex flex-col mt-8 `}>
                        <label htmlFor="upload-cover-letter" className='non-talent-label text-sm'>Upload Cover letter or write why you are a good fit in the field below</label>
                        <div className="border-[#F5F7F9] border-[0.3px] shrink-0 rounded-lg p-2">
                            <div className={`${errors?.cover_letter_file?.message ? "showcase-input-error" : ""}`}>
                                <Controller
                                    name="cover_letter_file"
                                    control={control}
                                    render={({ field }) => (
                                        <div className={` ${coverLetterFIle !== null ? "justify-between bg-[#F6F5FF] z-40 rounded-lg shrink-0 flex items-center  h-[7.3125rem] " : "bg-[#F5F7F9] "}  ${coverLetterTextNotEmpty ? 'opacity-50 pointer-events-none cursor-not-allowed' : ""}  `}>
                                            <label htmlFor='upload-cover-letter' className={` cursor-pointer ${coverLetterFIle === null ? "non-talent-upload  h-[6.3125rem] " : "flex items-center p-6 gap-x-[1.31rem] "}`}>
                                                <div className=""><UploadIcon /></div>

                                                <div className="mt-2 text-primary ">
                                                    <input onChange={(e) => {
                                                        field?.onChange(e.target.files && e.target.files[0],
                                                            setCoverLetterFIle(e.target.files && e.target.files[0])
                                                        )
                                                    }} ref={fileInputRef} type='file' id={coverLetterTextNotEmpty === false ? 'upload-cover-letter' : ""} className='hidden' name='uploadddd' accept={`${docx}, application/pdf`} />
                                                    <p className='non-talent-upload-text'> {coverLetterFIle !== null ? coverLetterFIle?.name : "Tap to upload document"}</p>
                                                    {
                                                        coverLetterFIle !== null ?
                                                            <p className='text-[0.875rem] non-talent-upload-text w-full'>Tap on <span className='text-primary font-medium'>change</span> to select other cover letter</p>
                                                            :
                                                            <p className=' non-talent-upload-subtext-text'>Files types: Pdf or Doc, Docx. RTF Max file size 10MB</p>
                                                    }
                                                </div>

                                            </label>
                                            {coverLetterFIle !== null && <div className="select-none h-full  px-3 py-3">

                                                <RemoveIcon onClick={() => {
                                                    setCoverLetterFIle(null)
                                                    setValue("cover_letter_file", null)
                                                    if (fileInputRef.current) {
                                                        fileInputRef.current.value = '';
                                                    }
                                                }} className='cursor-pointer' />
                                            </div>}
                                        </div>
                                    )}
                                />
                            </div>

                            <div className="">
                                <div className="">
                                    <label htmlFor="email" className='non-talent-label text-sm'>Or</label>
                                </div>
                                <div className={` ${errors?.cover_letter_text && errors?.cover_letter_text?.message ? "showcase-input-error" : ""}`}>
                                    {/* <QuillToolbar /> */}
                                    <Controller
                                        name="cover_letter_text"
                                        control={control}
                                        defaultValue=""
                                        disabled={coverLetterFIle !== null}
                                        render={({ field }) => (
                                            <div className={coverLetterFIle !== null ? 'opacity-50 pointer-events-none cursor-not-allowed' : ''}>
                                                <ReactQuill
                                                    theme="snow"
                                                    value={field?.value?.replace("<p><br></p>", "") || ''}
                                                    onBlur={field.onBlur}
                                                    onChange={(content, delta, source, editor) => {
                                                        const updatedContent = content.replace(/<p><br><\/p>/g, '');
                                                        field.onChange(updatedContent);
                                                    }}

                                                    modules={{
                                                        toolbar: {
                                                            container: [
                                                                [{ 'header': 1 }, { 'header': 2 }], // Header formats
                                                                [{ 'size': ['extra-small', 'small', 'medium', 'large'] }], // Size format
                                                                ['bold', 'italic', 'underline', 'strike'], // Existing formats
                                                                [{ 'list': 'ordered' }, { 'list': 'bullet' }], // List formats
                                                                [{ 'indent': '-1' }, { 'indent': '+1' }], // Indent formats
                                                                [{ 'align': [] }], // Alignment format

                                                                ['link', 'image', 'video'], // Other formats
                                                                ['clean'] // Remove formatting
                                                            ]
                                                        }
                                                    }}

                                                    className={`w-full p-4 mt-2 rounded-lg bg-[#F5F7F9] outline-none ${errors?.cover_letter_text && errors?.cover_letter_text?.message ? "showcase-input-error" : ""}`}
                                                    placeholder='Why are you a good fit'
                                                    style={{ border: "none" }}
                                                    id="myQuillEditor"


                                                />
                                            </div>
                                        )}
                                    />

                                </div>
                            </div>

                        </div>

                    </div>
                }


                {
                    data?.requirement_custom_fields?.custom_fields?.map((custField, index: number) => {

                        if (custField?.fieldType === "FILE_UPLOAD") {
                            return (

                                <>


                                    <div className="">
                                        <Controller
                                            name={`custom_job_requirements.${index}.custom_field_name`}
                                            control={control}
                                            defaultValue={custField?.fieldTitle}
                                            // rules={{ required: true }}
                                            render={({ field }) => (
                                                <div className="">
                                                    {/* <input type="text" placeholder='' onChange={(e) => field?.onChange(e.target.value)} className={`non-talent-input`} */}
                                                    <ReactTextareaAutosize

                                                        {...field}
                                                        placeholder='write here'
                                                        className="w-full rounded-lg  hidden bg-[#FAFAFA] p-3 text-sm outline-none"
                                                        minRows={3}

                                                    />
                                                </div>
                                            )}
                                        />
                                    </div>
                                    {/* <p>{errors.custom_job_requirements && errors?.custom_job_requirement[index].custom_file}</p> */}

                                    <div className={`flex flex-col mt-7 `}>
                                        <label htmlFor={custField?.fieldTitle} className='non-talent-label text-sm'>{custField?.fieldTitle}</label>
                                        {/*/@ts-ignore */}
                                        <div className={` ${errors?.custom_job_requirements && errors?.custom_job_requirements[index] && errors?.custom_job_requirements[index]?.custom_files?.message ? "showcase-input-error" : ""}`}>

                                            <Controller
                                                name={`custom_job_requirements.${index}.custom_file`}
                                                control={control}
                                                defaultValue={`${customFiles[index]}`}
                                                render={({ field }) => (
                                                    <div className={`relative ${customFiles[index] !== undefined ? "bg-[#F6F5FF]" : "bg-[#F5F7F9]"}`}>
                                                        {loading[index] && (
                                                            <div className="absolute inset-0 flex items-center justify-center bg-[#F6F5FF] bg-opacity-20 z-50">
                                                                {/* <p>Loading...</p> */}
                                                                <ReactLoading
                                                                    className="z-20"
                                                                    type="cylon"
                                                                    color="#8b75e7"
                                                                    width={30}
                                                                    height={30}
                                                                />
                                                            </div>
                                                        )}
                                                        <div className={`flex justify-between rounded-lg shrink-0 items-center h-[7.3125rem] ${customFiles[index] !== undefined ? "p-4" : "p-6"}`}>
                                                            <label htmlFor={custField?.fieldTitle} className={`cursor-pointer non-talent-label text-sm ${customFiles[index] ? "flex gap-3 justify-center items-center h-[6.3125rem]" : "flex items-center gap-x-[1.31rem]"}`}>
                                                                <div className=""><UploadIcon /></div>
                                                                <div className="mt-2 text-primary">
                                                                    <input
                                                                        onChange={(e) => {
                                                                            handleFileChange(e, index);
                                                                            field.onChange(e);
                                                                        }}
                                                                        type='file'
                                                                        id={custField?.fieldTitle}
                                                                        disabled={loading[index]}
                                                                        ref={fileInputRef}
                                                                        className='hidden'
                                                                        name='uploadddd'
                                                                        accept={` application/pdf, image/*`}
                                                                    />
                                                                    {!loading[index] && <>
                                                                        <p className='non-talent-upload-text'> {customFiles[index] ? customFiles[index]?.name : "Tap to upload document"}</p>
                                                                        {customFiles[index] ?
                                                                            <p style={{ wordBreak: "break-word" }} className='text-[0.875rem] non-talent-upload-text flex-wrap w-full'>Tap on <span className='text-primary font-medium'>change</span> to select other {custField?.fieldTitle}</p> :
                                                                            <p className='non-talent-upload-subtext-text'>Files types: Pdf, png, jpg, jpeg. RTF Max file size 10MB</p>
                                                                        }
                                                                    </>}
                                                                </div>
                                                            </label>
                                                            {customFiles[index] &&
                                                                <div className="select-none px-3 py-3">
                                                                    <RemoveIcon onClick={() => {
                                                                        removePictures(index)
                                                                    }} className='cursor-pointer' />
                                                                </div>
                                                            }
                                                        </div>
                                                    </div>
                                                )}
                                            />



                                        </div>
                                    </div>

                                </>






                            )



                        } else if (custField?.fieldType === "TEXT_INPUT") {
                            return (
                                <div className="flex flex-col mt-4" key={index}>
                                    <label htmlFor={custField?.fieldTitle} className='non-talent-label text-sm '>{custField?.fieldTitle}</label>
                                    {/* @ts-ignore */}
                                    <div className={` ${errors?.custom_job_requirements && errors?.custom_job_requirements[index] && errors?.custom_job_requirements[index]?.custom_text?.message ? "border-red-600 border" : ""}`}>

                                        <Controller
                                            name={`custom_job_requirements.${index}.custom_text`}
                                            control={control}
                                            defaultValue={""}
                                            // rules={{ required: true }}
                                            render={({ field }) => (
                                                <div className="">
                                                    {/* <input type="text" placeholder='' onChange={(e) => field?.onChange(e.target.value)} className={`non-talent-input`} */}
                                                    <ReactTextareaAutosize
                                                        id={custField?.fieldTitle}
                                                        {...field}
                                                        placeholder='write here'
                                                        className="w-full rounded-lg  bg-[#FAFAFA] p-3 text-sm outline-none"
                                                        minRows={3}

                                                    />
                                                </div>
                                            )}
                                        />
                                    </div>
                                    <div className="">
                                        <Controller
                                            name={`custom_job_requirements.${index}.custom_field_name`}
                                            control={control}
                                            defaultValue={custField?.fieldTitle}
                                            // rules={{ required: true }}
                                            render={({ field }) => (
                                                <div className="">
                                                    {/* <input type="text" placeholder='' onChange={(e) => field?.onChange(e.target.value)} className={`non-talent-input`} */}
                                                    <ReactTextareaAutosize

                                                        {...field}
                                                        placeholder='write here'
                                                        className="w-full rounded-lg hidden bg-[#FAFAFA] p-3 text-sm outline-none"
                                                        minRows={3}

                                                    />
                                                </div>
                                            )}
                                        />
                                    </div>
                                </div>
                            )
                        }
                    })
                }
                <div>

                    {/* Your component JSX */}
                </div>
                <button className='bg-primary flex w-full h-[3.375rem] gap-x-2 items-center justify-center rounded-[0.4375rem] mt-7 text-white text-base hover:bg-primary-dark-hover' type='submit'>Submit   {isSubmitting &&
                    <ReactLoading
                        className="z-20"
                        type="spin"
                        color="#fff"
                        width={18}
                        height={18}
                    />}
                </button>
            </form>
            {showErrorModal && <TalentErrorModal data={errorMsg} setErrorMsg={setErrorMsg} setIsOpen={setShowErrorModal} isOpen={showErrorModal} />}
            {showSuccessModal && <TalentSuccessModal data={successData} setIsOpen={setShowSuccessModal} isOpen={showSuccessModal} />
            }
        </div>
    )
}
{/* @ts-ignore */ }


export default NonTalentForm