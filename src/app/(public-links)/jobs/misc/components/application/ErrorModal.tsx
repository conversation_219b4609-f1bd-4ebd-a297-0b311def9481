import SuccessfulSvg from '@/app/(website-main)/t/dashboard/misc/icons/SuccessfulSvg'
import { Dialog, Transition } from '@headlessui/react'
import { Dispatch, Fragment, SetStateAction } from 'react'


interface Prop {
    setIsOpen: React.Dispatch<React.SetStateAction<boolean>>
    isOpen: boolean;
    setErrorMsg: React.Dispatch<React.SetStateAction<string>>
    data: any
}
export default function TalentErrorModal({ isOpen, setIsOpen, data, setErrorMsg }: Prop) {

    return (
        <>

            {/* <Transition appear show={isOpen} as={Fragment}>
                <Dialog as="div" className="relative z-50" onClose={() => setIsOpen(true)}>
                    <Transition.Child
                        as={Fragment}
                        enter="ease-out duration-300"
                        enterFrom="opacity-0"
                        enterTo="opacity-100"
                        leave="ease-in duration-200"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0"
                    >
                        <div className="fixed inset-0 bg-black/25" />
                    </Transition.Child>

                    <div className="fixed inset-0 overflow-y-auto">
                        <div className="flex min-h-full items-center justify-center p-4 text-center">
                            <Transition.Child
                                as={Fragment}
                                enter="ease-out duration-300"
                                enterFrom="opacity-0 scale-95"
                                enterTo="opacity-100 scale-100"
                                leave="ease-in duration-200"
                                leaveFrom="opacity-100 scale-100"
                                leaveTo="opacity-0 scale-95"
                            >
                                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                    <Dialog.Title
                                        as="h3"
                                        className="text-lg font-medium text-gray-900  flex justify-center items-center w-full"
                                    >
                                        <SuccessfulSvg />
                                    </Dialog.Title>
                                    <div className="text-center">
                                        <p className="text-base text-center text-gray-500">
                                            {data?.replace(/_/g, ' ')}
                                        </p>

                                    </div>

                                    <div className="py-4 mt-2">
                                        <button
                                            type="button"
                                            className="flex items-center justify-center rounded-md w-full bg-danger-dark-active h-[3rem]  px-4 py-2 text-sm font-medium text-white hover:bg-blue-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                                            onClick={() => {
                                                setIsOpen(false)
                                                setErrorMsg("")
                                            }}
                                        >
                                            Ok
                                        </button>
                                    </div>
                                </Dialog.Panel>
                            </Transition.Child>
                        </div>
                    </div>
                </Dialog>
            </Transition> */}





            <div className="z-50">
                <Dialog open={isOpen} onClose={() => setIsOpen(true)} className={"z-50"}>
                    <div className="fixed inset-0 z-50 flex w-screen items-center justify-center bg-[#000000B7]">
                        <Dialog.Panel
                            className={"w-[27.5rem] sm:w-[33.5rem] overflow-y-auto rounded-2xl bg-[#Ffff]"}
                        >
                            {/* <DialogHeader> */}
                            <div className="flex h-[50px] w-full items-center justify-between rounded-t-2xl bg-[#450A0A] px-8 text-white sm:h-[65px] sm:pt-2">
                                <p className="text-sm sm:text-base">Error</p>
                                <button
                                    className="rounded-lg bg-[#FFFFFF20] px-3 py-1"
                                    onClick={() => setIsOpen(false)}
                                >
                                    <p className="text-sm sm:text-base">Close</p>
                                </button>
                            </div>
                            <Dialog.Description
                                className={
                                    "max-h-[35rem] overflow-y-auto pt-6 lg:max-h-[42rem]"
                                }
                            >

                                <div className="px-6 py-10">


                                    <p className="text-base text-center text-[#675E8B] font-bold">
                                        {data?.replace(/_/g, ' ')}
                                    </p>
                                </div>

                                <div className="py-4 mt-2 flex justify-end bg-[#FEF2F2] w-full items-end gap-x-5 px-6">

                                    <button
                                        type="button"
                                        className="flex items-center  justify-center rounded-md bg-[#ffff] h-[3rem] border-[0.1px]   px-10 py-2 text-sm font-medium text-primary hover:bg-blue-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                                        onClick={() => {
                                            setIsOpen(false)
                                            setErrorMsg("")
                                        }}
                                    >
                                        Ok
                                    </button>

                                </div>
                            </Dialog.Description>
                        </Dialog.Panel>
                    </div>
                </Dialog>
            </div>
        </>
    )
}
