
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/shared';
import { Dialog, Transition } from '@headlessui/react'
import { useRouter } from 'next/navigation';
import { Dispatch, Fragment, SetStateAction, useState } from 'react'
import SuccessIcons from '../../icons/SuccessIcons';
import Link from 'next/link';



export interface successProps {
    message: string;
    talent_name: null;
    talent_email: string;
    talent_phone_number: string;
    talent_cv_ids: any[];
    talent_cover_letter_text: string;
    talent_expected_salary: string;
    talent_custom_job_requirements: TalentCustomJobRequirement[];
    talent_years_of_experience: string;
    talent_current_location: string;
    applied_job: AppliedJob;
}

export interface AppliedJob {
    job_id: string;
    job_title: string;
    job_description: string;
    working_option: string;
    company: string;
}

export interface TalentCustomJobRequirement {
    custom_text: string;
}


interface Prop {
    setIsOpen: Dispatch<SetStateAction<boolean>>;
    isOpen: boolean;
    password?: string;
    data: successProps
    talent?: boolean

}
export default function TalentSuccessModal({ isOpen, setIsOpen, data, talent = false }: Prop) {
    const router = useRouter()

    return (
        <>
            <div className="z-50">
                <Dialog open={isOpen} onClose={() => setIsOpen(true)} className={"z-50"}>
                    <div className="fixed inset-0 z-50 flex w-screen items-center justify-center bg-[#000000B7]">
                        <Dialog.Panel
                            className={"w-[27.5rem] sm:w-[33.5rem] overflow-y-auto rounded-2xl bg-[#Ffff]"}
                        >
                            {/* <DialogHeader> */}
                            <div className="flex h-[50px] w-full items-center justify-between rounded-t-2xl bg-[#755AE2] px-8 text-white sm:h-[65px] sm:pt-2">
                                <p className="text-sm sm:text-base">Application successful</p>
                                <button
                                    className="rounded-lg bg-[#FFFFFF20] px-3 py-1"
                                    onClick={() => setIsOpen(false)}
                                >
                                    <p className="text-sm">Close</p>
                                </button>
                            </div>
                            <Dialog.Description
                                className={
                                    "max-h-[35rem] overflow-y-auto pt-6 lg:max-h-[42rem]"
                                }
                            >
                                <div className="px-6 ">
                                    <SuccessIcons />
                                </div>
                                <div className="px-6 ">
                                    <h2 className='text-primary text-[1.25rem] py-3 font-semibold'>Application successful</h2>

                                    <p className="text-sm text-helper-text">
                                        You have successfully applied for the role of {data?.applied_job?.job_title} at <span className='text-header-text font-semibold'>{data?.applied_job?.company}</span>{talent ? ". You will be contacted for further steps when needed" : ". A getlinked account has been created for you so you can track your applications."}
                                    </p>
                                    <p className="text-sm text-helper-text mt-2 ">
                                        Please ensure the completion of your talent profile on GetLinked with a minimum of 70% profile completion to enhance your opportunities in the job pipeline.
                                    </p>
                                </div>
                                <div className="py-4 mt-4 flex justify-end bg-[#F5F3FF] rounded-t-xl w-full items-end gap-x-5 px-6">
                                    <LinkButton
                                        type="button"
                                        href={`/jobs`}
                                        variant='white'
                                        size='thin'
                                    >
                                        Okay
                                    </LinkButton>
                                    <LinkButton
                                        type="button"
                                        href='/login/talent'
                                        size='thin'
                                    >
                                        Login
                                    </LinkButton>
                                </div>
                            </Dialog.Description>
                        </Dialog.Panel>
                    </div>
                </Dialog>
            </div>
        </>
    )
}
