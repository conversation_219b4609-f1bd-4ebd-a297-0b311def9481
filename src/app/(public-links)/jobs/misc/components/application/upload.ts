import crypto from 'crypto'; // Import the crypto module for generating the signature
import axios from 'axios';

const apiSecret = process.env.NEXT_PUBLIC_UPLOAD_API_SECRET;
const baseUrl = process.env.NEXT_PUBLIC_BACKEND_URL;

 
export const uploadToServer = async (
  file: File
): Promise<{ id: string; secure_url: string }> => {
  const formData = new FormData();
  formData.append('file', file);
  
  try {
    const { data } = await axios.post(
      `${baseUrl}/services/upload/file/`,
      formData,
      {
        headers: {
          'X-API-KEY': apiSecret,
          'Content-Type': 'multipart/form-data'
        }
      }
    );

    console.log(data, "DATA FROM IMAGE UPLOAD")


    // Adjust these property names based on your API response structure
    return { id: data?.id || data?.file_id, secure_url: data?.url || data?.url };
  } catch (error) {
    // console.error('Error uploading file:', error);
    throw new Error('Failed to upload file');
  }
};

const generateSignature = (publicId: string) => {
  // const [publicId, setPublicId] = useState('');
  const timestamp = Math.floor(Date.now() / 1000);
  const signaturePayload = `public_id=${publicId}&timestamp=${timestamp}${apiSecret}`;
  const signature = crypto
    .createHash('sha1')
    .update(signaturePayload)
    .digest('hex');

  return {
    signature,
    timestamp,
  };
};

export const deleteFromCloudinary = async (publicId: string): Promise<void> => {
 
 // TODO: change the signature of this method to accept imageURL
 return 
  // try {
  //   let cloudName = "";
  //   const { signature, timestamp } = generateSignature(publicId);

  //   const response = await axios.post(
  //     `https://api.cloudinary.com/v1_1/${cloudName}/image/destroy`,
  //     {
  //       public_id: publicId,
  //       api_key: process.env.NEXT_PUBLIC_CLOUDINERY_API_KEY,
  //       timestamp,
  //       signature,
  //     }
  //   );

  //   return response.data; // Return any response data if needed
  // } catch (error) {
  //   // console.error('Error deleting file from Cloudinary:', error);
  //   throw new Error('Failed to delete file from Cloudinary');
  // }
};
