import { zodResolver } from '@hookform/resolvers/zod';
import React, {
  Fragment,
  SetStateAction,
  useEffect,
  useReducer,
  useState,
} from 'react';
import { Controller, useForm, useWatch } from 'react-hook-form';
import { isValid, z } from 'zod';
import 'react-phone-input-2/lib/style.css';
import { Dialog, RadioGroup } from '@headlessui/react';
// import { generateLetterProp } from './CoverLetterDetails';
import {
  InfiniteData,
  useInfiniteQuery,
  useQueryClient,
} from '@tanstack/react-query';
import axios from 'axios';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';
import ReactLoading from 'react-loading';
import { useDeleteResume } from '@/app/(website-main)/t/showcase/components/createCV/resume/api/resume/deleteResume';
import Template1 from '@/app/(website-main)/t/showcase/components/createCV/resume/cv-templates/Template1';
import Template2 from '@/app/(website-main)/t/showcase/components/createCV/resume/cv-templates/Template2';
import { RecommendedProps } from '@/app/(website-main)/t/showcase/misc/components/createCV/CvRecommendation';
import CheckedIcon from '@/app/(website-main)/t/showcase/misc/components/cv-upload/icons/CheckedIcon';
import { useSaveContactStore } from '@/app/(website-main)/t/showcase/misc/store/useResuming';
import { Loader } from '@/components/shared';
import RemoveIcon from '../../icons/RemoveIcon';
import { ResumeTypes } from '../../types/rusumeTypes';

export interface WorkExperiencePro {
  company: string;
  title: string;
  location: string;
  city?: string;
  state?: string;
  description: string[];
  start_date?: string;
  end_date?: string;
}

interface Props {
  isOpen: boolean;
  setIsOpen: React.Dispatch<SetStateAction<boolean>>;
  data: InfiniteData<ResumeTypes> | undefined;
  hasNextPage: boolean | undefined;
  isFetchingNextPage: boolean;
  loading: boolean;
  sentryRef: any;
  setCvTalentData: React.Dispatch<
    React.SetStateAction<{
      id: string;
      name: string;
    }>
  >;
  cvTalentData: {
    id: string;
    name: string;
  };
}

const ListCreatedResume = ({
  isOpen,
  setIsOpen,
  cvTalentData,
  data,
  hasNextPage,
  isFetchingNextPage,
  loading,
  sentryRef,
  setCvTalentData,
}: Props) => {
  const router = useRouter();

  const [selectTemp, setSelectTemp] = useState(
    `${cvTalentData?.id} - ${cvTalentData?.name}`
  );
  const removeCvItem = useSaveContactStore(state => state?.removeCvItem);
  // navigate to create new cv page
  const handleNewCv = () => {
    removeCvItem();
    router.push(`/t/showcase/resume?type=create`);
  };
  const uploadYourCV = () => {
    router.push(`/t/showcase/overview`);
  };

  const cvDatas: RecommendedProps[] = [
    {
      id: 1,
      title: 'I have a resume ?',
      description:
        'Taking the first step towards your dream job is a crucial milestone on your professional journey.',
      button_text: 'Upload Resume',
      barge: 'Recommended',
      background: '#DCF0FF',
      barge_bg: '#5a45b1',
      button_bg: '#fff',
      link: '',
      border: false,
      function: uploadYourCV,
    },
    {
      id: 1,
      title: 'Don’t have a resume ?',
      description:
        'Let’s build your professional resume together with few easy steps to take your first step towards your dream Job',
      button_text: 'Get professional CV',
      barge: 'Recommended',
      background: '#fff',
      barge_bg: '#A33A8C',
      button_bg: '#F2EFFF',
      link: '',
      border: false,
      function: handleNewCv,
    },
  ];

  const submit = () => {
    const [id, name] = selectTemp?.split(' - '); // Split the concatenated value into id and name
    setCvTalentData({ id, name });
    setIsOpen(false);
  };

  const [deletedId, setDeletedId] = useState('');
  const { mutate: handleDeleteFunc, isLoading: isDeleteting } =
    useDeleteResume();
  const queryClient = useQueryClient();

  const handleDelete = (id: string) => {
    setDeletedId(id);
    handleDeleteFunc(id, {
      onSuccess: (data: any) => {
        queryClient.invalidateQueries({
          queryKey: ['fetch-resume-lists'],
        });
      },
    });
  };

  return (
    <div className="">
      <Dialog open={isOpen} onClose={() => setIsOpen(false)}>
        <div className="fixed inset-0 z-50 flex w-screen items-center justify-center bg-[#000000B7] px-2 sm:px-0">
          <Dialog.Panel
            className={
              'w-[27.5rem] overflow-y-auto rounded-2xl bg-[#F5F3FF] md:w-[35rem]'
            }
          >
            {/* <DialogHeader> */}
            <div className="flex h-[50px] w-full items-center justify-between rounded-t-2xl bg-[#755AE2] px-8 text-white sm:h-[65px] sm:pt-2">
              <p className="text-sm sm:text-base">Select Resume</p>
              <button
                className="rounded-lg bg-[#FFFFFF20] px-3 py-1"
                onClick={() => setIsOpen(false)}
              >
                <p className="text-sm sm:text-base">Close</p>
              </button>
            </div>
            <Dialog.Description
              className={
                'max-h-[35rem] overflow-y-auto px-1 pt-6 md:max-h-[80vh] md:px-3'
              }
            >
              {data?.pages[0]?.message !== 'No Resumes found for the talent' ? (
                <p className="px-2 text-[0.875rem] text-[#7E7F80]">
                  Kindly select your resume to apply for this job
                </p>
              ) : (
                ''
              )}
              <div className="mt-[1.25rem]">
                {/* <form noValidate onSubmit={onsubmit}> */}

                <RadioGroup value={selectTemp} onChange={setSelectTemp}>
                  {loading && (
                    <div className="">
                      <Loader />
                    </div>
                  )}
                  {data?.pages[0]?.message ===
                    'No Resumes found for the talent' && (
                    <div className="relative flex flex-col gap-[1.5rem] p-[1.5rem] ">
                      {cvDatas?.map((data: RecommendedProps) => (
                        <div
                          className={`bg-[${data?.background}] relative rounded-[0.625rem] px-4 py-[1.44rem] ${
                            data?.border
                              ? 'border-[0.4px] border-[#C5D2E5] '
                              : ''
                          }`}
                          key={data?.id}
                          onClick={data?.function}
                        >
                          <p className="text-[1.125rem] font-medium text-primary">
                            {data?.title}
                          </p>
                          <p className="mt-[0.5rem] text-[0.875rem] text-[#675E8B]">
                            {data?.description}
                          </p>
                          <button
                            className={`h-[3rem] w-full bg-[${data?.button_bg}] mt-[0.62rem] flex  items-center justify-center rounded-[0.5rem] text-[0.875rem] font-medium text-[#755AE2] hover:scale-95`}
                          >
                            {data?.button_text}
                          </button>
                          <span
                            style={{
                              background: data?.barge_bg,
                              borderRadius: '0rem 0.625rem',
                            }}
                            className={`absolute right-0 top-0 px-[0.75rem] py-[0.25rem] text-[0.625rem] text-white`}
                          >
                            {data?.barge}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}

                  {!loading && (
                    <div className="relative grid grid-cols-1 justify-items-center   p-4">
                      {data?.pages?.map(
                        resumes =>
                          resumes?.results?.resume_creation?.map(resume => {
                            let experienceLevel = {
                              job_role: resume?.job_role,
                              proficiency_level: resume?.work_experience_level,
                            };
                            let contactInput = {
                              name: resume?.name,
                              address: resume?.address,
                              city: resume?.city,
                              state: resume?.state,
                              country: resume?.country,
                              email: resume?.email,
                              phone_number: resume?.phone_number,
                              linkedin_profile: resume?.linkedin_profile,
                              github_profile: resume?.github_profile,
                              behance_profile: resume?.behance_profile,
                              dribble_profile: resume?.dribble_profile,
                              personal_website: resume?.portfolio_website,
                              portfolio_website: resume?.personal_website,
                            };

                            return (
                              <div className="relative">
                                <div className="absolute right-7 top-7 z-[999] -translate-y-1/2  translate-x-1/2 scale-125 cursor-pointer">
                                  <RemoveIcon
                                    className=""
                                    onClick={() =>
                                      handleDelete(String(resume?.id))
                                    }
                                  />
                                </div>
                                <RadioGroup.Option
                                  as={Fragment}
                                  key={resume?.id}
                                  value={`${resume?.id} - ${resume?.name}`}
                                >
                                  {({ checked }) => (
                                    <div
                                      className={`my-2  cursor-pointer                                             ${
                                        checked
                                          ? 'relative scale-[1] overflow-hidden rounded-lg border  border-[#755AE2] transition-all '
                                          : 'relative scale-[1] overflow-hidden   border border-transparent transition-all '
                                      }
                                        `}
                                    >
                                      {/* <div className={`relative h-[350px] lg:w-[350px] w-[270px] border-[0.4px] border-[#C5D2E5] rounded-[0.1875rem] p-3`}> */}
                                      <div
                                        className={`relative grid  w-full cursor-pointer grid-cols-1 gap-y-5 rounded-[0.1875rem] border-[0.4px] border-[#C5D2E5] p-3`}
                                      >
                                        <div
                                          className="relative mb-[2rem]"
                                          key={resume?.id}
                                        >
                                          {resume?.cv_template === 1 && (
                                            <Template1
                                              contactInputs={contactInput}
                                              objectiveInput={resume?.objective}
                                              experienceLevel={experienceLevel}
                                              work_experience={
                                                resume?.work_experience
                                              }
                                              education={resume?.education}
                                              saveSkills={resume?.skills}
                                              projectInput={resume?.projects}
                                              certificationData={
                                                resume?.certifications
                                              }
                                              awardData={
                                                resume?.awards_and_honor
                                              }
                                              language={resume?.languages}
                                              hobbies={resume?.hobbies}
                                              other_links={resume?.other_links}
                                              fontFamilyData={''}
                                              templateSettings={{
                                                cv_template:
                                                  resume?.cv_template,
                                                cv_background_color:
                                                  resume?.cv_background_color,
                                                cv_header_text_color:
                                                  resume?.cv_body_text_color,
                                                cv_body_text_color:
                                                  resume?.cv_body_text_color,
                                                cv_header_font:
                                                  resume?.cv_header_font,
                                                cv_body_font:
                                                  resume?.cv_body_font,
                                                cv_header_font_size:
                                                  resume?.cv_header_font_size,
                                                cv_header_body_size:
                                                  resume?.cv_header_body_size,
                                                cv_right_header_text_color:
                                                  resume?.cv_right_header_text_color,
                                                cv_right_body_text_color:
                                                  resume?.cv_right_body_text_color,
                                              }}
                                            />
                                          )}

                                          {resume?.cv_template === 2 && (
                                            <Template2
                                              contactInputs={contactInput}
                                              objectiveInput={resume?.objective}
                                              experienceLevel={experienceLevel}
                                              work_experience={
                                                resume?.work_experience
                                              }
                                              education={resume?.education}
                                              saveSkills={resume?.skills}
                                              projectInput={resume?.projects}
                                              certificationData={
                                                resume?.certifications
                                              }
                                              awardData={
                                                resume?.awards_and_honor
                                              }
                                              language={resume?.languages}
                                              hobbies={resume?.hobbies}
                                              other_links={resume?.other_links}
                                              fontFamilyData={''}
                                              templateSettings={{
                                                cv_template:
                                                  resume?.cv_template,
                                                cv_background_color:
                                                  resume?.cv_background_color,
                                                cv_header_text_color:
                                                  resume?.cv_body_text_color,
                                                cv_body_text_color:
                                                  resume?.cv_body_text_color,
                                                cv_header_font:
                                                  resume?.cv_header_font,
                                                cv_body_font:
                                                  resume?.cv_body_font,
                                                cv_header_font_size:
                                                  resume?.cv_header_font_size,
                                                cv_header_body_size:
                                                  resume?.cv_header_body_size,
                                                cv_right_header_text_color:
                                                  resume?.cv_right_header_text_color,
                                                cv_right_body_text_color:
                                                  resume?.cv_right_body_text_color,
                                              }}
                                            />
                                          )}
                                        </div>
                                      </div>
                                      {checked && (
                                        <>
                                          <div className="absolute left-1/2 top-1/2  z-10 -translate-x-1/2 -translate-y-1/2 scale-125">
                                            <CheckedIcon />
                                          </div>
                                          <div className="absolute inset-0 h-full w-full bg-[#FFFFFF77]"></div>
                                        </>
                                      )}
                                    </div>
                                  )}
                                </RadioGroup.Option>
                              </div>
                            );
                          })
                      )}
                      {data?.pages?.map(
                        resumes =>
                          resumes?.results?.cv_data?.map(
                            (resume, idx: number) => {
                              let experienceLevel = {
                                job_role: '',
                                proficiency_level: '',
                              };
                              let contactInput = {
                                name: resume?.Name,
                                address: resume?.Address,
                                city: '',
                                state: '',
                                country: '',
                                email: resume?.Email,
                                phone_number: resume?.Phone,
                                linkedin_profile: resume?.LinkedIn,
                                github_profile: resume?.github,
                                behance_profile: resume?.behance,
                                dribble_profile: resume?.Dribble,
                                personal_website: resume?.personal_website,
                                portfolio_website: resume?.portfolio_website,
                              };
                              function extractSkillsValues(data: any) {
                                // Map over each object in the Skills array
                                return resume?.Skills?.map(skillSet => {
                                  // Extract values from each subarray and concatenate them
                                  return Object.values(skillSet).reduce(
                                    (acc, skillsArray) =>
                                      acc.concat(skillsArray),
                                    []
                                  );
                                }).flat(); // Flatten the resulting array
                              }

                              // Convert WorkExperience array to workExperiencePro array
                              const workExperienceProArray: WorkExperiencePro[] =
                                resume?.WorkExperience?.map(
                                  (experience: any) => {
                                    const description =
                                      experience?.Responsibilities_Achievements;

                                    return {
                                      company: experience?.Company,
                                      title: experience?.Job_Title,
                                      location: '', // You may need to parse location from Company if available
                                      description: description,
                                      start_date:
                                        experience?.Dates?.split('-')[0],
                                      end_date:
                                        experience?.Dates?.split('-')[1] ===
                                        'present'
                                          ? 'Present'
                                          : experience?.Dates?.split('-')[1],
                                      // end_year: endYear,
                                      // end_month: endMonth,
                                    };
                                  }
                                );

                              return (
                                <div className="relative">
                                  <div className="absolute right-7 top-7 z-[999] -translate-y-1/2  translate-x-1/2 scale-125 cursor-pointer">
                                    <RemoveIcon
                                      onClick={() =>
                                        handleDelete(String(resume?.cv_data_id))
                                      }
                                    />
                                  </div>
                                  <RadioGroup.Option
                                    as={Fragment}
                                    key={resume?.cv_data_id}
                                    value={`${resume?.cv_data_id} - ${resume?.Name}`}
                                  >
                                    {({ checked }) => (
                                      <div
                                        className={`my-2  cursor-pointer                                             ${
                                          checked
                                            ? 'relative scale-[1] overflow-hidden rounded-lg border  border-[#755AE2] transition-all '
                                            : 'relative scale-[1] overflow-hidden   border border-transparent transition-all '
                                        }
                                            `}
                                      >
                                        {/* <div className={`relative h-[350px] lg:w-[350px] w-[270px] border-[0.4px] border-[#C5D2E5] rounded-[0.1875rem] p-3`}> */}
                                        <div
                                          className={`relative grid w-full cursor-pointer grid-cols-1 gap-y-5 rounded-[0.1875rem] border-[0.4px] border-[#C5D2E5] p-1 md:p-3`}
                                        >
                                          <div
                                            className="mb-[2rem]"
                                            key={resume?.cv_data_id}
                                          >
                                            <Template2
                                              //@ts-ignore
                                              contactInputs={contactInput ?? ''}
                                              objectiveInput={
                                                resume?.Objective ?? ''
                                              }
                                              experienceLevel={
                                                experienceLevel ?? ''
                                              }
                                              work_experience={
                                                workExperienceProArray
                                              }
                                              education={
                                                resume?.Education ?? ''
                                              }
                                              saveSkills={extractSkillsValues(
                                                resume
                                              )}
                                              projectInput={resume?.Projects}
                                              certificationData={
                                                resume?.Certifications
                                              }
                                              awardData={
                                                resume?.Awards_and_Honors
                                              }
                                              language={resume?.Languages}
                                              hobbies={[]}
                                              other_links={[]}
                                              fontFamilyData={''}
                                              templateSettings={{
                                                cv_template: 0,
                                                cv_background_color: '#755ae2',
                                                cv_header_text_color: '',
                                                cv_body_text_color: '',
                                                cv_right_header_text_color:
                                                  '#fff',
                                                cv_right_body_text_color:
                                                  '#eee',
                                                cv_header_font: '',
                                                cv_body_font: '',
                                                cv_header_font_size: '',
                                                cv_header_body_size: '',
                                              }}
                                            />
                                          </div>
                                        </div>
                                        {checked && (
                                          <>
                                            <div className="absolute left-1/2 top-1/2  z-10 -translate-x-1/2 -translate-y-1/2 scale-125">
                                              <CheckedIcon />
                                            </div>
                                            <div className="absolute inset-0 h-full w-full bg-[#FFFFFF77]"></div>
                                          </>
                                        )}
                                      </div>
                                    )}
                                  </RadioGroup.Option>
                                </div>
                              );
                            }
                          )
                      )}
                      {data?.pages[0]?.message !==
                        'No Resumes found for the talent' && (
                        <>
                          {(isFetchingNextPage || hasNextPage) && (
                            <div
                              className=" flex w-full items-center justify-center"
                              ref={sentryRef}
                            >
                              {isFetchingNextPage ? (
                                <ReactLoading
                                  className="z-20"
                                  type="bubbles"
                                  color="#755ae2"
                                  width={50}
                                  height={50}
                                />
                              ) : (
                                ''
                              )}
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  )}
                </RadioGroup>

                <div className=" sticky bottom-0 z-[9999] flex h-[7rem] w-full items-center justify-center bg-[#F5F3FF]">
                  <button
                    disabled={!selectTemp}
                    onClick={submit}
                    type="submit"
                    className={` z-[9999] flex h-[3.375rem] w-full items-center justify-center font-sans text-base text-white  ${
                      !selectTemp
                        ? 'bg-purple-300'
                        : 'bg-[#755AE2] hover:scale-95'
                    } rounded-[0.4375rem] `}
                  >
                    Continue
                  </button>
                </div>
              </div>
            </Dialog.Description>
          </Dialog.Panel>
        </div>
      </Dialog>
    </div>
  );
};

export default ListCreatedResume;
