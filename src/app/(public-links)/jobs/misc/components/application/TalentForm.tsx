import { yupResolver } from '@hookform/resolvers/yup';
import { City, Country, ICountry, IState, State } from 'country-state-city';
import { useParams } from 'next/navigation';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import PhoneInput from 'react-phone-input-2';
import { Input, Select } from '@/components/shared';
import 'react-phone-input-2/lib/style.css';
import dynamic from 'next/dynamic';
import ReactLoading from 'react-loading';
import 'react-quill/dist/quill.snow.css'; // Make sure to import the Quill styles
import { useInfiniteQuery } from '@tanstack/react-query';
import useInfiniteScroll from 'react-infinite-scroll-hook';
import ReactTextareaAutosize from 'react-textarea-autosize';
import { FileUploader, SelectSingleCombo } from '@/components/shared';
import { AxiosWithNoAuth } from '@/lib/api/axios';
import { cn } from '@/utils';
import { FormDataPropsWithoutPasswords, talentSchema } from '.';
import { useNonJobFetchJobDetail } from '../../api/jobdetails';
import { useCreateTalentJob } from '../../api/talentJob_application';
import RemoveIcon from '../../icons/RemoveIcon';
import UploadIcon from '../../icons/UploadIcon';
import { userEmailProp } from '../../types';
import { coverLetteristTypes } from '../../types/coverLetterTypes';
import { ResumeTypes } from '../../types/rusumeTypes';
import { deleteFromCloudinary, uploadToServer } from './upload';
import { amountRanges, experience_level, genderArray } from './data';
import TalentErrorModal from './ErrorModal';
import ListCreatedCoverLetter from './ListCreatedCoverLetter';
import ListCreatedResume from './ListCreatedResume';
import TalentSuccessModal from './SuccessModal';
import toast from 'react-hot-toast';

const ReactQuill = dynamic(() => import('react-quill'), {
  ssr: false, // This ensures that Quill is only loaded on the client side
});

// export type NonTalentFormSchema = z.infer<typeof validationSchema>;

interface queryKeyTypes {
  pageParam: number;
  id: string | undefined;
}
interface Props {
  userDetails: userEmailProp | null;
}

const TalentForm = ({ userDetails }: Props) => {
  const [resumeFIle, setResumeFIle] = useState<File | null>(null);
  const [coverLetterFIle, setCoverLetterFIle] = useState<File | null>(null);
  const [customFiles, setCustomFiles] = useState<{ [id: number]: File | null }>(
    {}
  );
  const [countryList, setCountryList] = useState<ICountry[]>();
  const [stateList, setStateList] = useState<IState[]>();
  const [countryCode, setCountryCode] = useState('');

  const params: any = useParams();
  const { data } = useNonJobFetchJobDetail(params?.id);
  const [loadingUpload, setLoadingUpload] = useState<boolean[]>(
    data?.requirement_custom_fields?.custom_fields?.map(() => false) ?? []
  );
  const [showListCvModal, setshowListCvModal] = useState(false);
  const [showLetter, setShowLetter] = useState(false);
  const [coverLetterTalentData, setCoverLetterTalentData] = useState({
    id: '',
    name: '',
  });
  const [cvTalentData, setCvTalentData] = useState({ id: '', name: '' });
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  const [successData, setSuccessData] = useState<any>();
  const [coverLetterContent, setCoverLetterContent] = useState<any>();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const {
    register,
    control,
    handleSubmit,
    setValue,
    setError,
    clearErrors,
    watch,
    formState: { errors },
  } = useForm<FormDataPropsWithoutPasswords | any>({
    resolver: yupResolver(talentSchema),

    defaultValues: {
      email: userDetails?.user_details?.email,
      name: `${userDetails?.user_details?.first_name} ${userDetails?.user_details?.last_name}`,
      years_of_experience: userDetails?.talent_details?.year_of_work_experience,
      country: '',
      state: '',
      phone_number: userDetails?.user_details?.phone_number,
      cv: '',
      cv_and_resume_id: '',
      gender: userDetails?.user_details?.gender,
      cover_letter_file: null,
      cover_letter_text: '',
      cover_letter_id: coverLetterTalentData?.id || '',
      hasCoverLetter: data?.requirement_custom_fields?.accept_cover_letter,
      acceptCV: data?.requirement_custom_fields?.accept_resume,
      isMultiLocation: data?.is_multi_location && !!data?.preferred_locations.length,
      preferred_location: '',
      custom_job_requirements: [],
      portfolio_link: userDetails?.user_details?.portfolio_link || '',
      linkedin_link: userDetails?.user_details?.linkedin_link || '',
    },
    mode: 'onChange',
  });


  const docx = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';




  const { mutate: handleSubmitTalentJob, isLoading: isSubmitting } =
    useCreateTalentJob();

  const handleApplication = async (data: FormDataPropsWithoutPasswords) => {
    let isValid = true; // Track overall validation status

    // Validate custom fields
    data?.custom_job_requirements?.forEach(
      (requirement: any, index: number) => {
        const { custom_file, custom_files } = requirement;

        if (custom_file) {
          // Check if custom_files.custom_file_id is missing or empty
          if (
            !custom_files ||
            !custom_files.custom_file_id ||
            custom_files.custom_file_id === ''
          ) {
            setError(`custom_job_requirements.${index}.custom_files`, {
              type: 'required',
              message: 'Custom file is not provided.',
            });
            isValid = false; // Set overall validation status to false
          } else {
            // Clear any existing errors for custom_files.custom_file_id
            clearErrors(`custom_job_requirements.${index}.custom_files`);
          }
        }
      }
    );

    if (!isValid) {
      // Validation failed, do not submit form
      return;
    }

    // Validation passed, proceed to submit form
    // Prepare data for submission

    let non_talent_data = {
      current_location: `${data?.state},${data?.country}`,
      email: data?.email,
      name: data?.name,
      years_of_experience: data?.years_of_experience,
      phone_number: data?.phone_number,
      cv: data?.cv,
      gender: data?.gender,
      cv_and_resume_id: data?.cv_and_resume_id,
      cover_letter_file: data?.cover_letter_file,
      cover_letter_text: data?.cover_letter_text,
      preferred_location: data?.preferred_location,
      custom_job_requirements: data?.custom_job_requirements?.map(item => {
        // Create a copy of the item without the custom_file property
        const { custom_file, ...rest } = item;
        return rest; // Return the modified item without the custom_file property
      }),
      custom_job_requirements_id: data?.custom_job_requirements?.filter(
        item => item.custom_files?.custom_file_id
      ),
      portfolio_link: data?.portfolio_link,
      linkedin_link: data?.linkedin_link,
      hasCoverLetter: data?.hasCoverLetter,
      accept_cv: data?.acceptCV,
    };
    let mydata = {
      job_id: params?.id,
      non_talent_data,
    };

    handleSubmitTalentJob(mydata, {
      onSuccess: data => {
        setSuccessData(data);
        setShowSuccessModal(true);
      },
      onError: (error: any) => {
        mydata?.non_talent_data?.custom_job_requirements_id?.map(
          async (x: any) => {
            let id = x?.custom_file?.id !== undefined && x?.custom_file?.id;
            try {
              await deleteFromCloudinary(String(id));
            } catch (error: any) { }
          }
        );
        setShowErrorModal(prev => !prev);
        // console.log(error?.response?.data?.message, "error");
        if (error?.response) {
          const errorMessage =
            error?.response?.data?.message ||
            error?.response?.data?.non_field_errors[0] ||
            'Something went wrong.';
          setErrorMsg(errorMessage);
          // console.log(error?.response?.data?.non_field_errors[0]);
        } else {
          setErrorMsg('An unexpected error occurred.');
          console.error('Unexpected error:', error);
        }
      },
    });
  };

  const [coverLetterTextNotEmpty, setCoverLetterTextNotEmpty] = useState(false);
  const validateCVFields = async () => {
    const data = watch(); // Get current form data


    if (data?.acceptCV) {
      try {
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for 2 seconds

        const hasCvValue = !!data?.cv;
        const hasResumeIdValue = !!data?.cv_and_resume_id;

        if (hasCvValue && hasResumeIdValue) {
          setError('cv', {
            type: 'manual',
            message:
              'Only one of CV text or Resume ID is allowed to have a value',
          });
          setError('cv_and_resume_id', {
            type: 'manual',
            message:
              'Only one of CV text or Resume ID is allowed to have a value',
          });
        } else if (!hasCvValue && !hasResumeIdValue) {
          setError('cv', {
            type: 'manual',
            message: 'Either CV text or Resume ID is required',
          });
          setError('cv_and_resume_id', {
            type: 'manual',
            message: 'Either CV text or Resume ID is required',
          });
        } else {
          clearErrors(['cv', 'cv_and_resume_id']);

          // Allow submission if either hasCvValue or hasResumeIdValue is true
        }
      } catch (error) {
        console.error('Error occurred during CV validation:', error);
      }
    }
  };

  const validateCoverLetterFields = async () => {
    const data = watch(); // Get current form data
    if (data?.hasCoverLetter) {
      if (data?.cover_letter_text === '<p><br></p>') {
        setValue('cover_letter_text', '');
      }
      try {
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for 2 seconds
        const hasCoverLetterText = !!data?.cover_letter_text;
        const hasCoverLetterFile = !!data?.cover_letter_file;
        if (!hasCoverLetterText && !hasCoverLetterFile) {
          setError('cover_letter_text', {
            type: 'manual',
            message: 'Either cover letter text or file is required',
          });
          setError('cover_letter_file', {
            type: 'manual',
            message: 'Either cover letter text or file is required',
          });
        } else {
          clearErrors(['cover_letter_text', 'cover_letter_file']);
        }
      } catch (error) {
        console.error('Error occurred during cover letter validation:', error);
      }
    }
  };

  const validateForm = async () => {
    const data = watch(); // Get current form data
    // console.log(data?.cover, "textxtetxetetx");
    if (data?.cover_letter_text?.length > 0) {
      setCoverLetterTextNotEmpty(true);
    } else {
      setCoverLetterTextNotEmpty(false);
    }
    await validateCVFields();
    await validateCoverLetterFields();
  };

  useEffect(() => {
    validateForm();
  }, [watch()]);

  useEffect(() => {
    setCountryList(Country?.getAllCountries());
  }, []);

  useEffect(() => {
    setStateList(State?.getStatesOfCountry(String(countryCode)));
  }, [countryCode]);

  // locations
  const countryOptions = countryList?.map(country => ({
    value: country?.name,
    label: country.name,
    code: country?.isoCode,
  }));
  const stateOptions = React.useMemo(() => {
    return stateList?.map(state => ({
      value: state?.name,
      name: state.name,
      code: state?.isoCode,
    }));
  }, [stateList, countryCode]);

  let id = userDetails?.talent_details?.talent_id;
  // fetch talent cover letter

  // fetch list of created cover letters

  const fetchCoverLetterList = async ({ pageParam = 1, id }: queryKeyTypes) => {
    const response = await AxiosWithNoAuth.get(
      `/talent/fetch_cover_letter/${id}/?page=${pageParam}`
    );
    return response.data as coverLetteristTypes;
  };

  // fetch cover letters .......................................#################################################################################..........................................#####
  const {
    data: coverLetterData,
    error,
    fetchNextPage,
    hasNextPage,
    // isFetching,
    isFetchingNextPage,
    isLoading: loadingCoverLetter,
  } = useInfiniteQuery(
    ['fetch-cover-lists', id],
    ({ pageParam = 1 }) => fetchCoverLetterList({ pageParam, id }),
    {
      staleTime: 6000,
      getNextPageParam: (lastPage, pages) => {
        if (lastPage?.next !== null) {
          return pages.length + 1;
        } else {
          return;
        }
      },
    }
  );
  const nextPage = hasNextPage !== undefined;
  // infinity scroll
  const [sentryRef] = useInfiniteScroll({
    loading: isFetchingNextPage || loadingCoverLetter,
    hasNextPage: nextPage,
    onLoadMore: fetchNextPage,
    disabled: !!error,
    rootMargin: '0px 0px 400px 0px',
  });

  let hasCoverLetterData = Boolean(
    coverLetterData && coverLetterData?.pages[0]?.count > 0
  );
  const [hasCoverletterFile, setHasCoverletterFile] = useState<any | null>();
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (coverLetterData && coverLetterData?.pages[0]?.count > 0) {
        setCoverLetterContent(
          coverLetterData?.pages?.[0]?.results?.cover_letters[0]
        );
        setCoverLetterTalentData({
          id: String(
            coverLetterData?.pages?.[0]?.results?.cover_letters[0]?.id
          ),
          name:
            coverLetterData?.pages?.[0]?.results?.cover_letters[0]
              ?.talent_full_name || '',
        });
      }
    }, 5000); // 5 seconds delay

    return () => clearTimeout(timeout); // Cleanup function to clear the timeout on component unmount
  }, [coverLetterData]);

  useEffect(() => {
    setValue(
      'cover_letter_text',
      hasCoverLetterData ? coverLetterContent?.cover_letter_body : ''
    );
    // setValue("cover_letter_id", coverLetterTalentData?.id)
    setHasCoverletterFile(hasCoverLetterData);
  }, [coverLetterTalentData]);

  // fetch cover letters .............................##############################################################################################################################################....................................................

  // fetching resume for talent who already created or uploaded resume .............................................@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@..............................................

  const fetchResumeList = async ({ pageParam = 1, id }: queryKeyTypes) => {
    const response = await AxiosWithNoAuth.get(
      `/talent/fetch_cv_and_resume/${id}/?page=${pageParam}`
    );
    return response?.data as ResumeTypes; // Assuming ResumeListTypesProps is defined somewhere
  };

  const {
    data: cvData,
    error: cvErrors,
    fetchNextPage: fetchCvNextPage,
    hasNextPage: hasCvNextPage,
    isFetchingNextPage: isFetchingCvNextPage,
    isLoading: loading,
  } = useInfiniteQuery(
    ['fetch-resume-lists', id],
    ({ pageParam = 1 }) => fetchResumeList({ pageParam, id }),
    {
      staleTime: 6000,
      getNextPageParam: (lastPage, pages) => {
        return lastPage?.next !== null ? pages.length + 1 : undefined;
      },
    }
  );
  let hasResume = Boolean(cvData && cvData?.pages[0]?.count > 0);
  const [hasResumeFile, setHasResumeFile] = useState<any | null>();

  useEffect(() => {
    const timeout = setTimeout(() => {
      if (cvData && cvData?.pages[0]?.count > 0) {
        setCvTalentData({
          id: String(cvData?.pages?.[0]?.results?.resume_creation[0]?.id),
          name: cvData?.pages?.[0]?.results?.resume_creation[0]?.name || '',
        });
      }
    }, 5000); // 5 seconds delay

    return () => clearTimeout(timeout); // Cleanup function to clear the timeout on component unmount
  }, [cvData]);
  useEffect(() => {
    if (hasResume) {
      setValue('cv_and_resume_id', cvTalentData?.id);
      setHasResumeFile(hasResume);
    }
  }, [cvTalentData]);
  // fetching resume for talent who already created or uploaded resume ..................................................@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@..................................................

  // auto resize text area
  const textAreaRef = useRef<HTMLTextAreaElement>(null);
  useEffect(() => {
    if (textAreaRef && textAreaRef.current) {
      textAreaRef.current.style.height =
        textAreaRef.current.scrollHeight + 'px';
    }
  }, []);

  // const [loading, setLoading] = useState<boolean[]>(data?.requirement_custom_fields?.custom_fields?.map(() => false) ?? []);
  const watcxx = watch('custom_job_requirements');

  // Function to handle file selection
  const handleFileChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    const file = e.target.files && e.target.files[0];
    if (file) {
      removePictures(index);
      // Set the selected file in the state
      setCustomFiles(prevFiles => ({
        ...prevFiles,
        [index]: file,
      }));
      // Trigger upload to Cloudinary
      handleUploadToCloudinary(file, index);
    } else {
      // If no file is selected (e.g., user clicks cancel), remove the file from the state
      removePictures(index);
    }

    // Reset the value of the file input field to allow selecting the same file again
    e.target.value = '';
  };

  // Function to handle file upload to Cloudinary
  const handleUploadToCloudinary = async (file: File, index: number) => {
    try {
      const fieldType =
        data?.requirement_custom_fields?.custom_fields[index].fieldType;
      // Assuming fieldType indicates whether the field is a file upload field
      if (fieldType === 'FILE_UPLOAD') {
        setLoadingUpload(prevLoading => {
          const newLoading = [...prevLoading];
          newLoading[index] = true;
          return newLoading;
        });

        // Check if file is a valid instance of File
        if (file instanceof File) {
          // Call uploadToServer function to upload file
          const { secure_url, id } = await uploadToServer(file);

          // Update form values with uploaded file information
          setValue(
            `custom_job_requirements.${index}.custom_files.custom_file_url`,
            secure_url
          );
          setValue(
            `custom_job_requirements.${index}.custom_files.custom_file_id`,
            id
          );
          clearErrors(`custom_job_requirements.${index}.custom_files`);
        } else {
          console.error('Invalid file:', file);
        }
      }
    } catch (error) {
      console.error('Error uploading to Cloudinary:', error);
      toast.error("Failed to upload file(s) to cloudinary!")
    } finally {
      setLoadingUpload(prevLoading => {
        const newLoading = [...prevLoading];
        newLoading[index] = false;
        return newLoading;
      });
    }
  };

  //remove icons from cloudinery and dom
  const removePictures = async (index: number) => {
    setCustomFiles(prevFiles => {
      const updatedFiles = { ...prevFiles };
      delete updatedFiles[index];
      return updatedFiles;
    });

    let imgIdToDelete = watcxx[index]?.custom_files?.custom_file_id; //Access img_id at the specified index
    if (imgIdToDelete) {
      try {
        await deleteFromCloudinary(imgIdToDelete); // Pass the public_id for deletion
        setValue(
          `custom_job_requirements.${index}.custom_files.custom_file_url`,
          ''
        );
        setValue(
          `custom_job_requirements.${index}.custom_files.custom_file_id`,
          ''
        );
        setError(`custom_job_requirements.${index}.custom_files`, {
          type: 'required',
          message: 'Custom files must include a non-empty custom_file_id.',
        });
      } catch (error: any) {
        console.error('Error deleting image:', error.message);
      }
    } else {
      // console.error('img_id is missing');
    }

    if (fileInputRef?.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="mt-8 px-1">
      <form action="" onSubmit={handleSubmit(handleApplication)}>
        <Input
          {...register('email')}
          hasError={!!errors?.email}
          placeholder="<EMAIL>"
          errorMessage={errors.email?.message as string}
          type="email"
          variant="dark_showcase"
          label="Email"

        />
        <Input
          {...register('name')}
          placeholder="Joseph Abeokuta"
          hasError={!!errors?.name}
          errorMessage={errors.name?.message as string}
          type="text"
          label="Full Name"
          variant="dark_showcase"
        />




        <Select
          label="Years of Experience"
          {...register("years_of_experience")}
          onChange={val => setValue('years_of_experience', val)}
          options={experience_level}
          labelKey='name'
          valueKey='value'
          className="bg-[#f5f7f9]"
          placeholder="Select experience level"
          hasError={!!errors.years_of_experience}
          errorMessage={errors.years_of_experience?.message as string}
          variant="dark_showcase"
          fullWidth
        />

        <div className="mt-4 flex flex-col">
          <label className="text-sm text-header-text">Current Location</label>
          <div className="grid w-full flex-col items-center sm:grid-cols-2 sm:gap-4">
            <SelectSingleCombo
              name="country"
              placeholder="Select Country"
              value={watch('country')}
              onChange={val => {
                const chosen =
                  countryOptions &&
                  countryOptions.filter(
                    country =>
                      country.value.toLowerCase() == val.toLocaleLowerCase()
                  )[0];
                setValue('country', chosen?.value!);
                countryOptions && setCountryCode(chosen?.code!);
              }}
              className="bg-[#f5f7f9]"
              containerClass="max-sm:my-1"
              options={countryOptions! || []}
              valueKey="value"
              labelKey={"label"}
              hasError={!!errors?.country}
              errorMessage={errors?.country?.message as string}
            />

            <SelectSingleCombo
              name="state"
              placeholder="Select state"
              value={watch('state')}
              valueKey="value"
              labelKey={"name"}
              onChange={val => setValue('state', val)}
              className="bg-[#f5f7f9]"
              containerClass="max-sm:my-1"
              options={stateOptions || []}
              hasError={!!errors?.state}
              errorMessage={errors?.state?.message as string}
              isLoadingOptions={!stateOptions || watch('country') == undefined}
            />
          </div>
        </div>

        {
          data?.is_multi_location && data?.preferred_locations.length &&
          <div className="my-4">

            <SelectSingleCombo
              name="state"
              label="Preferred Location"
              placeholder="Select preferred location"
              value={watch('preferred_location')}
              valueKey="value"
              labelKey={"name"}
              onChange={val => setValue('preferred_location', val)}
              className="bg-[#f5f7f9]"
              variant="dark_showcase"
              containerClass="max-sm:my-1"
              options={data?.preferred_locations.map(loc => ({ name: loc, value: loc })) || []}
              hasError={!!errors?.state}
              errorMessage={errors?.state?.message as string}
              isLoadingOptions={!stateOptions || watch('country') == undefined}
              fullWidth
            />

            <div className="text-xs bg-primary-light text-primary p-2 px-4 mt-2 rounded-xl">
              This job has multiple locations set, which means it means that it can be performed from more than one office or city, giving you the flexibility to choose the one that suits you best. Select your preferred location—whether it's the closest to you, the most convenient, or simply your favorite!
            </div>
          </div>
        }

        <Select
          placeholder="Select gender"
          label="Gender"
          value={watch("gender")}
          {...register('gender')}
          valueKey='value'
          labelKey='name'
          onChange={(val) => setValue("gender", val)}
          options={genderArray}
          className='bg-[#f5f7f9]'
          containerClass='my-1.5'
          variant="dark_showcase"
          hasError={!!errors.gender}
          errorMessage={errors.gender?.message as string}
          fullWidth
        />


        <div className={`inputdiv`}>
          <label htmlFor="phone_number" className="non-talent-label text-sm">
            Phone Number
          </label>
          <div
            className={`flex items-center rounded-lg bg-[#F5F7F9] px-3 py-2 text-sm ${errors?.phone_number ? 'border-red-500' : ''
              } `}
          >
            <Controller
              control={control}
              name="phone_number"
              render={({ field: { ref, ...field } }) => (
                <PhoneInput
                  {...field}
                  containerStyle={{
                    display: 'flex',
                    alignItems: 'center',
                    height: '2rem',
                    width: '100%',
                  }}
                  // defaultMask='ng'
                  country={'ng'}
                  inputStyle={{
                    height: '100%',
                    width: '100%',
                    outline: 'none',
                    color: '#818181',
                    border: 'none',
                    backgroundColor: 'transparent',
                  }}
                  // countryCodeEditable={false}
                  placeholder="+234 9090003426"
                  specialLabel={'Mobile Number'}
                  buttonStyle={{
                    borderRadius: '100%',
                    height: '1.6rem',
                    width: '1.7rem',
                    padding: '0px',
                    marginBlock: 'auto',
                    border: 'none',
                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: '#ECE7EE',
                  }}
                  disableDropdown
                  dropdownStyle={{ border: '2px solid red' }}
                // disabled={!error?.phone_number?}
                />
              )}
            />
          </div>
        </div>
        <div className="inputdiv">
          <label htmlFor="email" className="">
            Portfolio link (Optional)
          </label>
          <input
            type="text"
            {...register('portfolio_link')}
            name=""
            placeholder="Enter portfolio link"
            className={cn('!bg-[#f5f7f9]')}
            id=""
          />
        </div>
        <div className="inputdiv">
          <label htmlFor="email" className="">
            LinkedIn (Optional)
          </label>
          <input
            type="text"
            {...register('linkedin_link')}
            name=""
            placeholder="Enter LinkedIn"
            className={cn('!bg-[#f5f7f9]')}
            id=""
          />
        </div>
        {data?.requirement_custom_fields?.accept_resume && (
          <>
            <div className={`mt-4 flex flex-col `}>
              <label
                htmlFor="upload-resume"
                className="text-sm text-header-text"
              >
                Upload Resume
              </label>

              {hasResumeFile ? (
                <div
                  className={` ${hasResumeFile
                    ? 'z-40 flex h-[7.3125rem] w-full shrink-0 items-center justify-between rounded-lg bg-[#F6F5FF] '
                    : 'bg-[#F5F7F9] '
                    }  `}
                >
                  <div
                    className={` cursor-pointer ${hasResumeFile
                      ? 'flex h-[6.3125rem] flex-wrap items-center justify-center gap-3  px-4 '
                      : 'flex items-center gap-x-[1.31rem] p-6 '
                      }`}
                  >
                    <div className="">
                      <UploadIcon />
                    </div>

                    <div className="mt-2 text-primary ">
                      <p className="non-talent-upload-text">
                        {' '}
                        {hasResumeFile
                          ? cvTalentData?.name
                          : 'Tap to upload document'}
                      </p>

                      <p
                        className="non-talent-upload-text w-full text-[0.875rem]"
                        onClick={() => setshowListCvModal(true)}
                      >
                        Tap on{' '}
                        <span className="font-medium text-primary">change</span>{' '}
                        to select other cv
                      </p>
                    </div>
                  </div>
                  {
                    <div className="h-full select-none  px-3 py-3">
                      <RemoveIcon
                        onClick={() => {
                          setHasResumeFile(null);
                          setValue('cv', null);
                          setValue('cv_and_resume_id', '');
                        }}
                        className="cursor-pointer"
                      />
                    </div>
                  }
                </div>
              ) : (
                <div
                  className={`w-full ${errors?.cv?.message ? 'showcase-input-error' : ''
                    }`}
                >
                  <Controller
                    name="cv"
                    control={control}
                    render={({ field }) => (
                      <div
                        className={` ${resumeFIle !== null
                          ? 'z-40 flex h-[7.3125rem] w-full shrink-0 items-center justify-between rounded-lg bg-[#F6F5FF] '
                          : 'bg-[#F5F7F9] '
                          }  `}
                      >
                        <label
                          htmlFor={'upload-resume'}
                          className={` cursor-pointer ${resumeFIle !== null
                            ? 'flex h-[6.3125rem] items-center justify-center gap-3  px-4 '
                            : 'flex items-center gap-x-[1.31rem] p-6 '
                            }`}
                        >
                          <div className="">
                            <UploadIcon />
                          </div>

                          <div className="mt-2 text-primary ">
                            <input
                              onChange={e => {
                                field?.onChange(
                                  e.target.files && e.target.files[0],
                                  setResumeFIle(
                                    e.target.files && e.target.files[0]
                                  )
                                );
                              }}
                              type="file"
                              id={'upload-resume'}
                              ref={fileInputRef}
                              className="hidden"
                              name="uploadddd"
                              accept={`${docx}, application/pdf`}
                            />
                            <p className="non-talent-upload-text">
                              {' '}
                              {resumeFIle !== null
                                ? resumeFIle?.name
                                : 'Tap to upload document'}
                            </p>
                            {resumeFIle !== null ? (
                              <p className="non-talent-upload-text w-full text-[0.875rem]">
                                Tap on{' '}
                                <span className="font-medium text-primary">
                                  change
                                </span>{' '}
                                to select other cv
                              </p>
                            ) : (
                              <p className=" non-talent-upload-subtext-text">
                                Files types: Pdf or Doc, Docx. RTF Max file size
                                10MB
                              </p>
                            )}
                          </div>
                        </label>
                        {resumeFIle !== null && (
                          <div className="h-full select-none  px-3 py-3">
                            <RemoveIcon
                              onClick={() => {
                                setResumeFIle(null);
                                setValue('cv', null);
                                setValue('cv_and_resume_id', '');
                                if (fileInputRef.current) {
                                  fileInputRef.current.value = '';
                                }
                              }}
                              className="cursor-pointer"
                            />
                          </div>
                        )}
                      </div>
                    )}
                  />
                </div>
              )}
            </div>
          </>
        )}

        {data?.requirement_custom_fields?.accept_cover_letter && (
          <div className={`mt-4 flex flex-col `}>
            <label
              htmlFor="upload-cover-letter"
              className="non-talent-label text-sm"
            >
              Upload Cover letter or write why you are a good fit in the field
              below
            </label>
            <div className="shrink-0 rounded-lg border-[0.3px] border-[#F5F7F9] p-2">
              {hasCoverletterFile ? (
                <div>
                  <div
                    className={` ${hasCoverletterFile
                      ? 'z-40 flex h-[7.3125rem] w-full shrink-0 items-center justify-between rounded-lg bg-[#F6F5FF] '
                      : 'bg-[#F5F7F9] '
                      }  `}
                  >
                    <label
                      htmlFor="upload-cover-letter"
                      className={` cursor-pointer ${hasCoverletterFile
                        ? 'flex h-[6.3125rem] items-center justify-center gap-3   px-4 '
                        : 'flex items-center gap-x-[1.31rem] p-6 '
                        }`}
                    >
                      <div className="">
                        <UploadIcon />
                      </div>

                      <div className="mt-2 text-primary ">
                        <p className="non-talent-upload-text">
                          {' '}
                          {hasCoverletterFile
                            ? coverLetterTalentData?.name
                            : 'Tap to upload document'}
                        </p>

                        <p
                          className="non-talent-upload-text w-full text-[0.875rem]"
                          onClick={() => setShowLetter(true)}
                        >
                          Tap on{' '}
                          <span className="font-medium text-primary">
                            change
                          </span>{' '}
                          to select other cover letter
                        </p>
                      </div>
                    </label>
                    {
                      <div className="h-full select-none  px-3 py-3">
                        <RemoveIcon
                          onClick={() => {
                            setHasCoverletterFile(null);
                            setValue('cover_letter_file', null);
                          }}
                          className="cursor-pointer"
                        />
                      </div>
                    }
                  </div>
                </div>
              ) : (
                <div
                  className={`${errors?.cover_letter_file &&
                    errors?.cover_letter_file?.message
                    ? 'showcase-input-error'
                    : ''
                    } ${coverLetterTextNotEmpty
                      ? 'pointer-events-none opacity-50'
                      : ''
                    }`}
                >
                  <Controller
                    name="cover_letter_file"
                    control={control}
                    render={({ field }) => (
                      <div
                        className={` ${coverLetterFIle !== null
                          ? 'z-40 flex h-[7.3125rem] w-full shrink-0 items-center justify-between rounded-lg bg-[#F6F5FF] '
                          : 'bg-[#F5F7F9] '
                          }  `}
                      >
                        <label
                          htmlFor="upload-cover-letter"
                          className={` cursor-pointer ${coverLetterFIle === null
                            ? 'flex h-[6.3125rem] items-center  gap-3  px-4 '
                            : 'flex items-center gap-x-[1.31rem] p-6 '
                            }`}
                        >
                          <div className="">
                            <UploadIcon />
                          </div>

                          <div className="mt-2 text-primary ">
                            <input
                              onChange={e => {
                                field?.onChange(
                                  e.target.files && e.target.files[0],
                                  setCoverLetterFIle(
                                    e.target.files && e.target.files[0]
                                  ),
                                  setValue('cover_letter_text', '')
                                );
                              }}
                              type="file"
                              ref={fileInputRef}
                              id={
                                coverLetterTextNotEmpty === false
                                  ? 'upload-cover-letter'
                                  : ''
                              }
                              className="hidden"
                              name="uploadddd"
                              accept={`${docx}, application/pdf`}
                            />
                            <p className="non-talent-upload-text">
                              {' '}
                              {coverLetterFIle !== null
                                ? coverLetterFIle?.name
                                : 'Tap to upload document'}
                            </p>
                            {coverLetterFIle !== null ? (
                              <p className="non-talent-upload-text w-full text-[0.875rem]">
                                Tap on{' '}
                                <span className="font-medium text-primary">
                                  change
                                </span>{' '}
                                to select other cover letter
                              </p>
                            ) : (
                              <p className=" non-talent-upload-subtext-text">
                                Files types: Pdf or Doc, Docx. RTF Max file size
                                10MB
                              </p>
                            )}
                          </div>
                        </label>
                        {coverLetterFIle !== null && (
                          <div className="h-full select-none  px-3 py-3">
                            <RemoveIcon
                              onClick={() => {
                                setCoverLetterFIle(null);
                                setValue('cover_letter_file', null);
                                if (fileInputRef.current) {
                                  fileInputRef.current.value = '';
                                }
                              }}
                              className="cursor-pointer"
                            />
                          </div>
                        )}
                      </div>
                    )}
                  />
                </div>
              )}

              <div className="">
                <div className="mt-2">
                  <label htmlFor="email" className="non-talent-label text-sm">
                    Or
                  </label>
                </div>
                <div
                  className={` ${errors?.cover_letter_text &&
                    errors?.cover_letter_text?.message
                    ? ''
                    : ''
                    }`}
                >
                  {/* <QuillToolbar /> */}
                  <Controller
                    name="cover_letter_text"
                    control={control}
                    defaultValue=""
                    disabled={coverLetterFIle !== null}
                    render={({ field }) => (
                      <div
                        className={
                          coverLetterFIle !== null
                            ? 'pointer-events-none cursor-not-allowed opacity-50'
                            : ''
                        }
                      >
                        <ReactQuill
                          theme="snow"
                          value={field?.value?.replace('<p><br></p>', '') || ''}
                          onBlur={field.onBlur}
                          onChange={(content, delta, source, editor) => {
                            const updatedContent = content.replace(
                              /<p><br><\/p>/g,
                              ''
                            );
                            field.onChange(updatedContent);
                          }}
                          modules={{
                            toolbar: {
                              container: [
                                [{ header: 1 }, { header: 2 }], // Header formats
                                [
                                  {
                                    size: [
                                      'extra-small',
                                      'small',
                                      'medium',
                                      'large',
                                    ],
                                  },
                                ], // Size format
                                ['bold', 'italic', 'underline', 'strike'], // Existing formats
                                [{ list: 'ordered' }, { list: 'bullet' }], // List formats
                                [{ indent: '-1' }, { indent: '+1' }], // Indent formats
                                [{ align: [] }], // Alignment format

                                ['link', 'image', 'video'], // Other formats
                                ['clean'], // Remove formatting
                              ],
                            },
                          }}
                          className={`mt-2 w-full rounded-lg bg-[#F5F7F9] p-4 outline-none ${errors?.cover_letter_text &&
                            errors?.cover_letter_text?.message
                            ? 'showcase-input-error'
                            : ''
                            }`}
                          placeholder="Why are you a good fit"
                          style={{ border: 'none' }}
                          id="myQuillEditor"
                        />
                      </div>
                    )}
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {data?.requirement_custom_fields?.custom_fields?.map(
          (custField, index: number) => {
            if (custField?.fieldType === 'FILE_UPLOAD') {
              return (
                <>
                  <div className="">
                    <Controller
                      name={`custom_job_requirements.${index}.custom_field_name`}
                      control={control}
                      defaultValue={custField?.fieldTitle}
                      // rules={{ required: true }}
                      render={({ field }) => (
                        <div className="">
                          {/* <input type="text" placeholder='' onChange={(e) => field?.onChange(e.target.value)} className={`non-talent-input`} */}
                          <ReactTextareaAutosize
                            {...field}
                            placeholder="write here"
                            className="hidden w-full resize-none rounded-lg bg-[#FAFAFA] p-3 text-sm outline-none"
                            minRows={3}
                            maxRows={7}
                          />
                        </div>
                      )}
                    />
                  </div>
                  <div className={`mt-4 flex flex-col `}>
                    <label
                      htmlFor={custField?.fieldTitle}
                      className="non-talent-label border-red-900 text-sm"
                    >
                      {custField?.fieldTitle}
                    </label>
                    {/* @ts-ignore */}
                    <div
                      className={` ${errors?.custom_job_requirements &&
                        Array.isArray(errors.custom_job_requirements) &&
                        errors.custom_job_requirements[index]?.custom_file
                          ?.message
                        ? 'border border-red-600'
                        : ''
                        }`}
                    >
                      <Controller
                        name={`custom_job_requirements.${index}.custom_file`}
                        control={control}
                        defaultValue={`${customFiles[index]}`}
                        render={({ field }) => (
                          <div
                            className={`relative ${customFiles[index] !== undefined
                              ? 'bg-[#F6F5FF]'
                              : 'bg-[#F5F7F9]'
                              }`}
                          >
                            {loadingUpload[index] && (
                              <div className="absolute inset-0 z-50 flex items-center justify-center bg-[#F6F5FF] bg-opacity-20">
                                {/* <p>loadingUpload...</p> */}
                                <ReactLoading
                                  className="z-20"
                                  type="cylon"
                                  color="#8b75e7"
                                  width={30}
                                  height={30}
                                />
                              </div>
                            )}
                            <div
                              className={`flex h-[7.3125rem] shrink-0 items-center justify-between rounded-lg ${customFiles[index] !== undefined ? 'p-4' : 'p-6'
                                }`}
                            >
                              <label
                                htmlFor={custField?.fieldTitle}
                                className={`cursor-pointer ${customFiles[index]
                                  ? 'flex h-[6.3125rem] items-center justify-center gap-3'
                                  : 'flex items-center gap-x-[1.31rem]'
                                  }`}
                              >
                                <div className="">
                                  <UploadIcon />
                                </div>
                                <div className="mt-2 text-primary">
                                  <input
                                    onChange={e => {
                                      handleFileChange(e, index);
                                      field.onChange(e);
                                    }}
                                    type="file"
                                    id={custField?.fieldTitle}
                                    disabled={loadingUpload[index]}
                                    ref={fileInputRef}
                                    className="hidden"
                                    name="uploadddd"
                                    accept={` application/pdf, image/*`}
                                  />
                                  {!loadingUpload[index] && (
                                    <>
                                      <p className="non-talent-upload-text">
                                        {' '}
                                        {customFiles[index]
                                          ? customFiles[index]?.name
                                          : 'Tap to upload document'}
                                      </p>
                                      {customFiles[index] ? (
                                        <p
                                          style={{ wordBreak: 'break-word' }}
                                          className="non-talent-upload-text w-full flex-wrap text-[0.875rem]"
                                        >
                                          Tap on{' '}
                                          <span className="font-medium text-primary">
                                            change
                                          </span>{' '}
                                          to select other{' '}
                                          {custField?.fieldTitle}
                                        </p>
                                      ) : (
                                        <p className="non-talent-upload-subtext-text">
                                          Files types: Pdf, png, jpg, jpeg. RTF
                                          Max file size 10MB
                                        </p>
                                      )}
                                    </>
                                  )}
                                </div>
                              </label>
                              {customFiles[index] && (
                                <div className="select-none px-3 py-3">
                                  <RemoveIcon
                                    onClick={() => {
                                      removePictures(index);
                                    }}
                                    className="cursor-pointer"
                                  />
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      />

                      {/* {customFiles[index] && customFiles[index] !== null && !watcxx[index]?.custom_files?.custom_file_url && (
                                                <>

                                                    <button
                                                        type="button"
                                                        disabled={loadingUpload[index]}
                                                        onClick={() => handleUploadToCloudinary(index)}
                                                        className='bg-primary py-2 my-3 text-white w-full  font-lato rounded-lg hover:bg-primary-dark-hover'
                                                    >
                                                        {loadingUpload[index] ? "Uploading..." : "Upload"}
                                                    </button>

                                                </>
                                            )} */}
                    </div>
                  </div>
                </>
              );
            } else if (custField?.fieldType === 'TEXT_INPUT') {
              return (
                <div className="mt-4 flex flex-col" key={index}>
                  <label
                    htmlFor={custField?.fieldTitle}
                    className="non-talent-label text-sm"
                  >
                    {custField?.fieldTitle}
                  </label>
                  {/* @ts-ignore */}
                  <div
                    className={` ${errors?.custom_job_requirements &&
                      Array.isArray(errors.custom_job_requirements) &&
                      errors.custom_job_requirements[index]?.custom_text
                        ?.message
                      ? 'border border-red-600'
                      : ''
                      }`}
                  >
                    <Controller
                      name={`custom_job_requirements.${index}.custom_text`}
                      control={control}
                      defaultValue={''}
                      // rules={{ required: true }}
                      render={({ field }) => (
                        <div className="">
                          {/* <input type="text" placeholder='' onChange={(e) => field?.onChange(e.target.value)} className={`non-talent-input`} */}
                          <ReactTextareaAutosize
                            id={custField?.fieldTitle}
                            {...field}
                            placeholder="write here"
                            className="w-full rounded-lg  bg-[#FAFAFA] p-3 text-sm outline-none"
                            minRows={3}
                          />
                        </div>
                      )}
                    />
                  </div>
                  <div className="">
                    <Controller
                      name={`custom_job_requirements.${index}.custom_field_name`}
                      control={control}
                      defaultValue={custField?.fieldTitle}
                      // rules={{ required: true }}
                      render={({ field }) => (
                        <div className="">
                          {/* <input type="text" placeholder='' onChange={(e) => field?.onChange(e.target.value)} className={`non-talent-input`} */}
                          <ReactTextareaAutosize
                            {...field}
                            placeholder="write here"
                            className="hidden w-full rounded-lg bg-[#FAFAFA] p-3 text-sm outline-none"
                            minRows={3}
                          />
                        </div>
                      )}
                    />
                  </div>
                </div>
              );
            }
          }
        )}
        <button
          className="mt-7 flex h-[3.375rem] w-full items-center justify-center gap-x-2 rounded-[0.4375rem] bg-black text-base text-white hover:border-2 hover:border-black hover:bg-white hover:text-black"
          type="submit"
        >
          Submit{' '}
          {isSubmitting && (
            <ReactLoading
              className="z-20"
              type="spin"
              color="#808080"
              width={18}
              height={18}
            />
          )}
        </button>
      </form>
      {showListCvModal && (
        <ListCreatedResume
          isOpen={showListCvModal}
          setIsOpen={setshowListCvModal}
          data={cvData}
          hasNextPage={hasCvNextPage}
          isFetchingNextPage={isFetchingCvNextPage}
          cvTalentData={cvTalentData}
          setCvTalentData={setCvTalentData}
          loading={loading}
          sentryRef={sentryRef}
        />
      )}
      {showLetter && (
        <ListCreatedCoverLetter
          setCoverLetterContent={setCoverLetterContent}
          isOpen={showLetter}
          setIsOpen={setShowLetter}
          id={userDetails?.talent_details?.talent_id}
          data={coverLetterData}
          hasNextPage={hasNextPage}
          coverLetterTalentData={coverLetterTalentData}
          isFetchingNextPage={isFetchingNextPage}
          loading={loadingCoverLetter}
          sentryRef={sentryRef}
          setCoverLetterTalentData={setCoverLetterTalentData}
        />
      )}
      {showErrorModal && (
        <TalentErrorModal
          data={errorMsg}
          setIsOpen={setShowErrorModal}
          isOpen={showErrorModal}
          setErrorMsg={setErrorMsg}
        />
      )}
      {showSuccessModal && (
        <TalentSuccessModal
          data={successData}
          password={''}
          setIsOpen={setShowSuccessModal}
          isOpen={showSuccessModal}
        />
      )}
    </div>
  );
};

export default TalentForm;
