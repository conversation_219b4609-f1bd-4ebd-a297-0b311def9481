interface amountRangesProp {
  name: string;
  value: string;
}
export const amountRanges: amountRangesProp[] = [
  { name: '20,000 - 30,000', value: '20,000 - 30,000' },
  { name: '30,000 - 40,000', value: '30,000 - 40,000' },
  { name: '40,000 - 50,000', value: '40,000 - 50,000' },
  { name: '50,000 - 60,000', value: '50,000 - 60,000' },
  { name: '60,000 - 70,000', value: '60,000 - 70,000' },
  { name: '70,000 - 80,000', value: '70,000 - 80,000' },
  { name: '80,000 - 90,000', value: '80,000 - 90,000' },
  { name: '90,000 - 100,000', value: '90,000 - 100,000' },
  { name: '100,000 - 120,000', value: '100,000 - 120,000' },
  { name: '120,000 - 140,000', value: '120,000 - 140,000' },
  { name: '140,000 - 160,000', value: '140,000 - 160,000' },
  { name: '160,000 - 180,000', value: '160,000 - 180,000' },
  { name: '180,000 - 200,000', value: '180,000 - 200,000' },
  { name: '200,000 - 250,000', value: '200,000 - 250,000' },
  { name: '250,000 - 300,000', value: '250,000 - 300,000' },
  { name: '300,000 - 400,000', value: '300,000 - 400,000' },
  { name: '400,000 - 500,000', value: '400,000 - 500,000' },
  { name: '500,000 - 750,000', value: '500,000 - 750,000' },
  { name: '750,000 - 1,000,000', value: '750,000 - 1,000,000' },
  { name: '1,000,000+', value: '1,000,000+' },
];

interface levelProps {
  id: number;
  name: string;
  value: string;
}
interface experProps {
  label: string;
  value: string;
}
export const experience_level: amountRangesProp[] = [
  { name: 'No experience', value: 'No experience' },
  { name: 'Less than 2 years', value: 'Less than 2 years' },
  { name: '2 to 4 years', value: '2 to 4 years' },
  { name: '5 to 7 years', value: '5 to 7 years' },
  { name: '8 to 10 years', value: '8 to 10 years' },
  { name: '10+ years', value: '10+ years' },
];

export const genderArray: amountRangesProp[] = [
  { name: 'Male', value: 'male' },
  { name: 'Female', value: 'female' },
  { name: 'Others', value: 'others' },
];

export const experienceLeveLSelect: experProps[] = [
  { label: 'No experience', value: 'No experience' },
  { label: 'Less than 2 years', value: 'Less than 2 years' },
  { label: '2 to 4 years', value: '2 to 4 years' },
  { label: '5 to 7 years', value: '5 to 7 years' },
  { label: '8 to 10 years', value: '8 to 10 years' },
  { label: '10+ years', value: '10+ years' },
];
