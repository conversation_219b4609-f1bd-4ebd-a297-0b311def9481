import React, { Fragment, SetStateAction, useEffect, useReducer, useState } from 'react'
import { isValid, z } from 'zod';
import { Controller, useForm, useWatch } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import 'react-phone-input-2/lib/style.css'
import { Dialog, RadioGroup } from '@headlessui/react';
import ReactLoading from "react-loading";
// import { generateLetterProp } from './CoverLetterDetails';
import { InfiniteData, useInfiniteQuery } from '@tanstack/react-query';
import useInfiniteScroll from 'react-infinite-scroll-hook';
import { getAccessToken } from '@/utils';
import axios from 'axios';

import { Loader } from '@/components/shared';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';
import { resumeListTypesProps } from '@/app/(website-main)/t/showcase/components/createCV/resume/types/cvTypes';
import { Axios, AxiosWithNoAuth } from '@/lib/api/axios';
import { useSaveContactStore } from '@/app/(website-main)/t/showcase/misc/store/useResuming';
import { RecommendedProps } from '@/app/(website-main)/t/showcase/misc/components/createCV/CvRecommendation';
import Template1 from '@/app/(website-main)/t/showcase/components/createCV/resume/cv-templates/Template1';
import Template2 from '@/app/(website-main)/t/showcase/components/createCV/resume/cv-templates/Template2';
import CheckedIcon from '@/app/(website-main)/t/showcase/misc/components/cv-upload/icons/CheckedIcon';
import { useSaveCoverLetterStore } from '@/app/(website-main)/t/showcase/misc/store/useCreateCorverLetterSore';
import { coverLetterListTypes } from '@/app/(website-main)/t/showcase/components/createCV/resume/types/coverLetterTypes';
import CoverLetterTemplate1 from '@/app/(website-main)/t/showcase/components/createCV/cover-letter/template/CoverLetterTemplate1';
import CoverLetterTemplate2 from '@/app/(website-main)/t/showcase/components/createCV/cover-letter/template/CoverLettertemplate2';
import CoverLetterTemplate3 from '@/app/(website-main)/t/showcase/components/createCV/cover-letter/template/CoverLetterTemplate3';
import { coverLetteristTypes } from '../../types/coverLetterTypes';
// 
interface queryKeyTypes {
    pageParam: number;
    id: string | undefined;

}

// export type generateCoverLetterProp = z.infer<typeof contactSchema>;
interface props {
    isOpen: boolean;
    setIsOpen: React.Dispatch<SetStateAction<boolean>>;
    id: string | number | undefined;
    data: InfiniteData<coverLetteristTypes> | undefined
    hasNextPage: boolean | undefined;
    isFetchingNextPage: boolean
    loading: boolean;
    sentryRef: any;
    setCoverLetterTalentData: React.Dispatch<React.SetStateAction<{
        name: string;
        id: string;
    }>>;
    coverLetterTalentData: {
        id: string;
        name: string;
    };
    setCoverLetterContent: any

}
const ListCreatedCoverLetter = ({ isOpen, setIsOpen, id, data, hasNextPage, isFetchingNextPage, loading, sentryRef, setCoverLetterTalentData, coverLetterTalentData, setCoverLetterContent }: props) => {
    const router = useRouter()

    // });
    const [selectTemp, setSelectTemp] = useState(`${coverLetterTalentData?.id} - ${coverLetterTalentData?.name}`)
    const removeCvItem = useSaveCoverLetterStore((state) => state?.removeLetterItem)
    const setStep = useSaveCoverLetterStore((state) => state?.addStep)
    // navigate to create new cv page


    const handleNewLetter = () => {
        removeCvItem()
        setStep(1)
        router.push(`/t/showcase/cover-letter?type=create`)
    }

    const cvDatas: RecommendedProps[] = [

        {
            id: 1,
            title: "Don’t have a cover letter ?",
            description: "Let’s build your professional resume together with few easy steps to take your first step towards your dream Job",
            button_text: "Get professional Cover Letter",
            barge: "Recommended",
            background: "#fff",
            barge_bg: "#A33A8C",
            button_bg: "#F2EFFF",
            link: "",
            border: false,
            function: handleNewLetter
        },


    ]


    const submit = () => {
        const [id, name] = selectTemp?.split(' - ') // Split the concatenated value into id and name
        setCoverLetterTalentData({ id, name })
        setIsOpen(false)
    }

    return (
        <div className="">
            <Dialog open={isOpen} onClose={() => setIsOpen(false)}>
                <div className="fixed inset-0 z-50 flex w-screen px-2 sm:px-0 items-center justify-center bg-[#000000B7]">
                    <Dialog.Panel
                        className={"rounded-2xl  bg-[#F5F3FF] overflow-y-auto w-[27.5rem]"}
                    >
                        {/* <DialogHeader> */}
                        <div className="flex h-[50px] w-full items-center justify-between rounded-t-2xl bg-[#755AE2] px-8 text-white sm:h-[65px] sm:pt-2">
                            <p className="text-sm sm:text-base">Select Resume</p>
                            <button
                                className="rounded-lg bg-[#FFFFFF20] px-3 py-1"
                                onClick={() => setIsOpen(false)}
                            >
                                <p className="text-sm sm:text-base">Close</p>
                            </button>
                        </div>
                        <Dialog.Description
                            className={
                                "px-3 pt-6 overflow-y-auto max-h-[35rem] lg:max-h-[42rem]"
                            }
                        >

                            {/* {data?.pages[0]?.message !== "No Resumes found for the talent" ? <p className='text-[#7E7F80] text-[0.875rem]'>Kindly select your resume to apply for this job</p> : ""} */}
                            <div className="mt-[1.25rem]">
                                {/* <form noValidate onSubmit={onsubmit}> */}

                                <RadioGroup value={selectTemp} onChange={setSelectTemp}>

                                    {loading && (
                                        <div className="">
                                            <Loader />
                                        </div>
                                    )}

                                    {
                                        data && data?.pages[0]?.count <= 0 &&
                                        <div className='flex flex-col p-[1.5rem] gap-[1.5rem] relative '>
                                            {
                                                cvDatas?.map((data: RecommendedProps) => (
                                                    <div className={`bg-[${data?.background}] py-[1.44rem] px-4 rounded-[0.625rem] relative ${data?.border ? "border-[0.4px] border-[#C5D2E5] " : ""}`} key={data?.id} onClick={data?.function}>
                                                        <p className='text-primary text-[1.125rem] font-medium'>{data?.title}</p>
                                                        <p className='text-[0.875rem] text-[#675E8B] mt-[0.5rem]'>{data?.description}</p>
                                                        <button className={`h-[3rem] w-full bg-[${data?.button_bg}] mt-[0.62rem] hover:scale-95  flex justify-center items-center rounded-[0.5rem] text-[0.875rem] text-[#755AE2] font-medium`}>{data?.button_text}</button>
                                                        <span style={{ background: data?.barge_bg, borderRadius: "0rem 0.625rem" }} className={`text-[0.625rem] text-white py-[0.25rem] px-[0.75rem] absolute top-0 right-0`}>{data?.barge}</span>
                                                    </div>
                                                ))
                                            }
                                        </div>
                                    }

                                    {!loading && <div className="grid grid-cols-1 justify-items-center   p-4">
                                        {data?.pages?.map((letters) =>
                                            letters?.results?.cover_letters?.map((letter, idx: number) => {

                                                return (
                                                    <RadioGroup.Option
                                                        as={Fragment}
                                                        key={letter?.id}
                                                        value={`${letter?.id} - ${letter?.talent_full_name}`}

                                                    >
                                                        {({ checked }) => {
                                                            // if (checked) {

                                                            // }
                                                            return (
                                                                <div
                                                                    className={`my-2  cursor-pointer                                             ${checked
                                                                        ? 'relative rounded-lg border border-[#755AE2] scale-[1]  transition-all overflow-hidden '
                                                                        : 'relative border border-transparent   scale-[1] transition-all overflow-hidden '}

                                                                        
    
                                                `}

                                                                    onClick={() => setCoverLetterContent(letter)}

                                                                >
                                                                    {/* <div className={`relative h-[350px] lg:w-[350px] w-[270px] border-[0.4px] border-[#C5D2E5] rounded-[0.1875rem] p-3`}> */}
                                                                    <div className={`relative grid grid-cols-1 gap-y-5 h-[650px] cursor-pointer w-full border-[0.4px] border-[#C5D2E5] rounded-[0.1875rem] p-3`}>
                                                                        <div className='mb-[2rem]' key={letter?.id}>
                                                                            <div className="" id={`div-to-download-${letter?.id}`} style={{ fontFamily: "roboto" }}>


                                                                                {Number(letter?.selected_template_to_cover) === 1 && <CoverLetterTemplate1 letterContent={letter} />}
                                                                                {Number(letter?.selected_template_to_cover) === 2 && <CoverLetterTemplate2 letterContent={letter} />}
                                                                                {Number(letter?.selected_template_to_cover) === 3 && <CoverLetterTemplate3 letterContent={letter} />}
                                                                            </div>




                                                                        </div>




                                                                    </div>
                                                                    {checked && (
                                                                        <>
                                                                            <div className="absolute left-1/2 top-1/2  z-10 -translate-x-1/2 -translate-y-1/2 scale-125">
                                                                                <CheckedIcon
                                                                                />
                                                                            </div>
                                                                            <div className="absolute inset-0 h-full w-full bg-[#FFFFFF77]"></div>
                                                                        </>
                                                                    )}
                                                                </div>
                                                            )
                                                        }
                                                        }


                                                    </RadioGroup.Option>
                                                );

                                            })
                                        )}
                                        {/* {data?.pages[0]?.message !== "No Resumes found for the talent" && <> */}
                                        {(isFetchingNextPage || hasNextPage) && (
                                            <div
                                                className=" w-full flex justify-center items-center"
                                                ref={sentryRef}
                                            >
                                                {isFetchingNextPage ? <ReactLoading
                                                    className="z-20"
                                                    type="bubbles"
                                                    color="#755ae2"
                                                    width={50}
                                                    height={50}
                                                /> : ""}
                                            </div>
                                        )}
                                        {/* </>} */}
                                    </div>}
                                </RadioGroup>







                                <div className=" sticky h-[7rem] bg-[#F5F3FF] flex justify-center items-center bottom-0 w-full">
                                    <button disabled={!selectTemp} onClick={submit} type="submit" className={` flex w-full text-white text-base font-sans justify-center items-center h-[3.375rem]  ${!selectTemp ? "bg-purple-300" : "hover:scale-95 bg-[#755AE2]"} rounded-[0.4375rem] `}>



                                        Continue

                                    </button>
                                </div>
                                {/* </form> */}
                            </div>
                        </Dialog.Description>
                    </Dialog.Panel>
                </div>
            </Dialog>
        </div>
    )
}

export default ListCreatedCoverLetter


