import React from 'react'
import { FilterDropdown } from '@/components/shared'
import { job_type, proficiency_levels, work_experiences, working_options } from '@/app/(website-main)/e/jobs/misc/constants';
import { cn } from '@/utils';

interface FilterOptionsProps {
    selectedProficiencyLevels: string[]
    setSelectedProficiencyLevels: (value: string[]) => void
    selectedExperiences: string[]
    setSelectedExperiences: (value: string[]) => void
    selectedJobTypes: string[]
    setSelectedJobTypes: (value: string[]) => void
    selectedWorkModes: string[]
    setSelectedWorkModes: (value: string[]) => void
    selectedStatus: "OPEN" | "CLOSED" | null
    setSelectedStatus: (value: "OPEN" | "CLOSED" | null) => void
}


const FilterOptions: React.FC<FilterOptionsProps> = ({ selectedExperiences, setSelectedExperiences, selectedJobTypes, setSelectedJobTypes, selectedProficiencyLevels, setSelectedProficiencyLevels, selectedWorkModes, setSelectedWorkModes }) => {
    return (
        <>
            <FilterDropdown
                options={proficiency_levels}
                onChange={(value) => setSelectedProficiencyLevels(value)}
                valueKey='value'
                labelKey="name"
                values={selectedProficiencyLevels}
                label='Proficiency'
                triggerClass="bg-white border border-[#E4E4E4] rounded-md"
                primaryColor='black'
                customFooterActions={
                    <div
                        className={cn(selectedProficiencyLevels.length < 1 && "opacity-50", 'py-2 px-[1.35rem] mt-6 text-xs text-center justify-center w-full text-black bg-white hover:bg-danger-light-active border border-[#E4E4E4] rounded-md cursor-pointer transition-colors duration-300')}
                        onClick={() => setSelectedProficiencyLevels([])}
                        aria-disabled={selectedProficiencyLevels.length < 1}
                    >
                        Clear
                    </div>
                }
            />
            <FilterDropdown
                options={work_experiences}
                onChange={(value) => setSelectedExperiences(value)}
                valueKey='value'
                labelKey="name"
                values={selectedExperiences}
                label='Experience'
                triggerClass="bg-white border border-[#E4E4E4] rounded-md"
                primaryColor='black'
                customFooterActions={
                    <div
                        className={cn(selectedExperiences.length < 1 && "opacity-50", 'py-2 px-[1.35rem] mt-6 text-xs text-center justify-center w-full text-black bg-white hover:bg-danger-light-active border border-[#E4E4E4] rounded-md cursor-pointer transition-colors duration-300')}
                        onClick={() => setSelectedExperiences([])}
                        aria-disabled={selectedExperiences.length < 1}
                    >
                        Clear
                    </div>
                }
            />
            <FilterDropdown
                options={job_type}
                onChange={(value) => setSelectedJobTypes(value)}
                valueKey='value'
                labelKey="name"
                values={selectedJobTypes}
                label='Employment Type'
                triggerClass="bg-white border border-[#E4E4E4] rounded-md"
                primaryColor='black'
                customFooterActions={
                    <div
                        className={cn(selectedJobTypes.length < 1 && "opacity-50", 'py-2 px-[1.35rem] mt-6 text-xs text-center justify-center w-full text-black bg-white hover:bg-danger-light-active border border-[#E4E4E4] rounded-md cursor-pointer transition-colors duration-300')}
                        onClick={() => setSelectedJobTypes([])}
                        aria-disabled={selectedJobTypes.length < 1}
                    >
                        Clear
                    </div>
                }
            />
            <FilterDropdown
                options={working_options}
                onChange={(value) => setSelectedWorkModes(value)}
                valueKey='value'
                labelKey="name"
                values={selectedWorkModes}
                label='Work Mode'
                triggerClass="bg-white border border-[#E4E4E4] rounded-md"
                primaryColor='black'
                customFooterActions={
                    <div
                        className={cn(selectedWorkModes.length < 1 && "opacity-50", 'py-2 px-[1.35rem] mt-6 text-xs text-center justify-center w-full text-black bg-white hover:bg-danger-light-active border border-[#E4E4E4] rounded-md cursor-pointer transition-colors duration-300')}
                        onClick={() => setSelectedWorkModes([])}
                        aria-disabled={selectedWorkModes.length < 1}
                    >
                        Clear
                    </div>
                }
            />
        </>
    )
}

export default FilterOptions