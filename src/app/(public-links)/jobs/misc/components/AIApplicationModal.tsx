'use client';

import { Dialog, Transition } from '@headlessui/react';
import { Arrow<PERSON><PERSON><PERSON>, MoreHorizontal, Star } from 'lucide-react';
import { Fragment } from 'react';
import MayaBot from '@/app/misc/icons/MayaBot';
import AIApplicationFlow from './AIApplicationFlow';

interface AIApplicationModalProps {
  isOpen: boolean;
  onClose: () => void;
  jobData?: any;
  applicantEmail?: string | null;
  onEmailSubmit?: (email: string) => void;
}

const AIApplicationModal: React.FC<AIApplicationModalProps> = ({
  isOpen,
  onClose,
  jobData,
  applicantEmail,
  onEmailSubmit,
}) => {
  const handleClose = () => {
    onClose();
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-[9999]" onClose={() => {}}>
        {/* Backdrop */}
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" />
        </Transition.Child>

        {/* Modal container */}
        <div className="fixed inset-0 overflow-hidden">
          <div className="absolute inset-0 overflow-hidden">
            <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-in-out duration-500"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in-out duration-500"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="pointer-events-auto relative w-screen max-w-md">
                  {/* Main content */}
                  <div className="flex h-full flex-col bg-white shadow-xl">
                    {/* Header - Updated to match design */}
                    <div className="flex items-center justify-between bg-primary px-4 py-3">
                      <button
                        type="button"
                        className="text-white transition-colors hover:text-white/80"
                        onClick={handleClose}
                      >
                        <ArrowLeft className="h-6 w-6" />
                      </button>

                      <div className="flex items-center gap-3">
                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-white/20">
                          <MayaBot />
                        </div>
                        <div className="text-center">
                          <Dialog.Title className="text-base font-medium text-white">
                            Maya (Bot)
                          </Dialog.Title>
                          <p className="text-xs text-white/80">Introduction</p>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <button
                          type="button"
                          className="text-white transition-colors hover:text-white/80"
                        >
                          <MoreHorizontal className="h-6 w-6" />
                        </button>
                        <button
                          type="button"
                          className="text-white transition-colors hover:text-white/80"
                        >
                          <Star className="h-6 w-6" />
                        </button>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="relative flex-1 overflow-y-auto bg-[#F1EFFCA3]">
                      <AIApplicationFlow
                        jobData={jobData}
                        onClose={handleClose}
                        applicantEmail={applicantEmail}
                        onEmailSubmit={onEmailSubmit}
                      />
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

export default AIApplicationModal;
