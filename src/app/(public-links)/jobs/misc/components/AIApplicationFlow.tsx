'use client';

import MayaBot from '@/app/misc/icons/MayaBot';
import { Button } from '@/components/shared';
import { cn } from '@/utils';
import {
  AlertCircle,
  CheckCircle,
  ChevronRight,
  FileText,
  RefreshCw,
  Sparkles,
  Upload,
} from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { useAIApplicationState } from '../hooks/useAIApplicationState';
import { ChatMessage, ConnectionStatus, TypingIndicator } from './ChatMessage';
import { FileUploadZone } from './FileUploadZone';
import InterviewConfirmationModal from './InterviewConfirmationModal';
import InterviewSchedulingModal from './InterviewSchedulingModal';

interface AIApplicationFlowProps {
  jobData?: any;
  onClose: () => void;
  applicantEmail?: string | null;
  onEmailSubmit?: (email: string) => void;
}

const AIApplicationFlow: React.FC<AIApplicationFlowProps> = ({
  jobData,
  onClose,
  applicantEmail: preFilledEmail,
  onEmailSubmit,
}) => {
  // Get job ID from jobData - this is required
  const jobId = jobData?.id;

  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Local state for email input in the email collection step
  const [emailInput, setEmailInput] = useState('');

  // Initialize application state with pre-filled email if provided
  const appState = useAIApplicationState({
    jobId: jobId || '',
    applicantEmail: preFilledEmail || undefined, // Use pre-filled email from URL if available
    jobData,
    onComplete: summary => {
      console.log('Application completed:', summary);
      // Could show a success modal or redirect
    },
    onError: error => {
      console.error('Application error:', error);
      // Error handling is managed within the state
    },
  });

  // Initialize email input when appState.applicantEmail changes
  useEffect(() => {
    if (appState.applicantEmail && !emailInput) {
      setEmailInput(appState.applicantEmail);
    }
  }, [appState.applicantEmail, emailInput]);

  // Log warning if no job ID (but don't break the component)
  useEffect(() => {
    if (!jobId) {
      console.error('Job ID is required for AI application flow');
    }
  }, [jobId]);

  const steps = [
    { id: 0, name: 'Email', icon: Sparkles },
    { id: 1, name: 'Introduction', icon: Sparkles },
    { id: 2, name: 'Upload Resume', icon: Upload },
    { id: 3, name: 'AI Chat Interview', icon: FileText },
    { id: 4, name: 'Complete', icon: CheckCircle },
  ];

  // Auto-scroll to bottom when messages change
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [appState.chat.messages]);

  // Map application state to current step for progress indicator
  const getCurrentStepIndex = () => {
    switch (appState.applicationState.currentStep) {
      case 'email_collection':
        return 0;
      case 'introduction':
        return 1;
      case 'upload':
        return 1; // Keep same as introduction since it's part of the chat flow
      case 'chat':
        return 2;
      case 'interview_scheduling':
        return 3;
      case 'interview_confirmation':
        return 3;
      case 'complete':
        return 4;
      case 'error':
        return 1; // Show introduction step for retry
      default:
        return 0;
    }
  };

  // Render step content based on application state
  const renderStepContent = () => {
    switch (appState.applicationState.currentStep) {
      case 'email_collection':
        return renderEmailCollection();
      case 'introduction':
        return renderIntroduction();
      case 'upload':
        return renderChatWithUpload(); // Show chat interface with upload capability
      case 'chat':
        return renderChat();
      case 'interview_scheduling':
        return renderInterviewScheduling();
      case 'interview_confirmation':
        return renderInterviewConfirmation();
      case 'complete':
        return renderComplete();
      case 'error':
        return renderError();
      default:
        return renderEmailCollection();
    }
  };

  // Render email collection step
  const renderEmailCollection = () => {
    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      appState.handleEmailSubmit(emailInput);
      // Notify parent component about email submission for URL sync
      if (onEmailSubmit) {
        onEmailSubmit(emailInput);
      }
    };

    return (
      <div className="p-6 text-center">
        <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-primary/10">
          <MayaBot />
        </div>
        <h3 className="mb-4 text-xl font-semibold text-gray-900">
          Welcome to AI-Powered Application
        </h3>
        <p className="mb-6 text-gray-600">
          To get started, please provide your email address. This will be used for your application and any follow-up communications.
        </p>
        <p className="mb-6 text-gray-600 font-bold">
          Make sure the email address you provide matches the one on your CV, as this will be used to verify and link your application details.
        </p>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="text-left">
            <label
              htmlFor="email"
              className="mb-2 block text-sm font-medium text-gray-700"
            >
              Email Address *
            </label>
            <input
              type="email"
              id="email"
              value={emailInput}
              onChange={e => setEmailInput(e.target.value)}
              placeholder="Enter your email address"
              className={`w-full rounded-lg border px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-primary ${
                appState.emailError
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-200'
                  : 'border-gray-300 focus:border-primary'
              }`}
              required
            />
            {appState.emailError && (
              <p className="mt-2 text-sm text-red-600">{appState.emailError}</p>
            )}
          </div>

          <button
            type="submit"
            disabled={!emailInput.trim() || appState.chat.isConnecting}
            className="flex w-full items-center justify-center gap-2 rounded-lg bg-primary px-4 py-3 text-white hover:bg-primary/90 disabled:cursor-not-allowed disabled:opacity-50"
          >
            {appState.chat.isConnecting ? (
              <>
                <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                Connecting...
              </>
            ) : (
              <>
                Continue
                <ChevronRight className="h-4 w-4" />
              </>
            )}
          </button>
        </form>

        <p className="mt-4 text-xs text-gray-500">
          Your email will be used to create your application and send you
          updates about your application status.
        </p>
      </div>
    );
  };

  // Render introduction step
  const renderIntroduction = () => (
    <div className="mt-[40%] p-6 text-center">
      {/* Tap to Speak Circle */}
      {/* <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full border-2 border-dashed border-primary/30 bg-white">
        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            className="text-primary"
          >
            <path
              d="M12 1L21 8V16L12 23L3 16V8L12 1Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M12 8V16"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M8 12H16"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>
      <p className="mb-6 text-xs text-gray-500">Tap to Speak</p> */}

      <h3 className="mb-4 text-xl font-semibold text-gray-900">
        Welcome to LibertyTech's recruitment process!
      </h3>
      <p className="mb-6 text-sm text-gray-600">
        I am Maya, an AI assistant, and I'll be guiding you through this process
        today. Shall we begin?
      </p>

      {/* WebSocket Connection Status - Only show when connecting or connected */}
      {appState.chat.isConnecting && (
        <div className="mb-4 rounded-lg border border-blue-200 bg-blue-50 p-3">
          <div className="flex items-center justify-center">
            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-blue-600"></div>
            <p className="text-sm text-blue-700">
              Connecting to application service...
            </p>
          </div>
        </div>
      )}

      {appState.chat.isConnected && !appState.chat.hasWelcomeResponse && (
        <div className="mb-4 rounded-lg border border-yellow-200 bg-yellow-50 p-3">
          <div className="flex items-center justify-center">
            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-yellow-600"></div>
            <p className="text-sm text-yellow-700">
              Initializing application session...
            </p>
          </div>
        </div>
      )}

      <div className="space-x-3 space-y-3">
        <Button
          onClick={() => {
            // Start the application process - this will move to upload step
            appState.startApplication();
            // Move to upload step to show chat interface with upload capability
            setTimeout(() => {
              appState.goToUpload();
            }, 500);
          }}
          disabled={
            appState.chat.isConnecting ||
            (appState.chat.isConnected && !appState.chat.hasWelcomeResponse)
          }
          className="rounded-lg px-2 py-2 text-sm font-medium text-primary hover:bg-primary/90 disabled:cursor-not-allowed disabled:opacity-50"
          variant="light"
        >
          {appState.chat.isConnecting
            ? 'Connecting...'
            : appState.chat.isConnected && !appState.chat.hasWelcomeResponse
            ? 'Initializing...'
            : "Yes, I'm ready"}
        </Button>

        {/* <Button
          className="rounded-lg px-2 py-2 text-sm font-medium text-primary hover:bg-gray-50"
          variant="light"
        >
          Wait a minute
        </Button>

        <Button
          onClick={appState.openSchedulingModal}
          className="px-1 py-1 text-sm font-medium text-primary hover:text-primary/80"
          variant="light"
        >
          Would like an interview with a recruiter
        </Button> */}
      </div>

      <p className="mt-6 text-xs text-gray-500">Powered by Getlinked.AI</p>
    </div>
  );

  // Render chat interface with upload capability
  const renderChatWithUpload = () => (
    <div className="flex h-full flex-col">
      {/* Offline Status */}
      {!appState.isOnline && (
        <div className="flex items-center justify-center border-b border-red-200 bg-red-50 p-3">
          <div className="flex items-center gap-2 text-sm text-red-800">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <span>You are offline. Please check your internet connection.</span>
          </div>
        </div>
      )}

      {/* Connection Status */}
      <ConnectionStatus
        isConnected={appState.chat.isConnected}
        isConnecting={appState.chat.isConnecting}
      />

      {/* Chat Messages */}
      <div className="flex-1 space-y-4 overflow-y-auto p-4">
        {appState.chat.messages.map((message, index) => (
          <ChatMessage
            key={message.id}
            message={message}
            isLatest={index === appState.chat.messages.length - 1}
            uploadedFile={appState.upload.uploadedFile}
          />
        ))}

        {appState.chat.isTyping && <TypingIndicator />}

        {/* Show file upload zone when Maya requests CV upload */}
        {appState.chat.hasWelcomeResponse &&
          appState.chat.messages.some(
            msg =>
              msg.type === 'bot' &&
              (msg.content.toLowerCase().includes('upload') ||
                msg.content.toLowerCase().includes('cv') ||
                msg.content.toLowerCase().includes('resume'))
          ) &&
          !appState.upload.uploadResult?.success && (
            <div className="px-4 py-3">
              <FileUploadZone
                isUploading={appState.upload.isUploading}
                uploadProgress={appState.upload.uploadProgress}
                uploadedFile={appState.upload.uploadedFile}
                uploadResult={appState.upload.uploadResult}
                onFileSelect={appState.upload.handleFileSelect}
                onFileDrop={appState.upload.handleFileDrop}
                onDragOver={appState.upload.handleDragOver}
                onDragEnter={appState.upload.handleDragEnter}
                onReset={appState.upload.resetUpload}
                disabled={
                  !appState.chat.isConnected ||
                  !appState.chat.hasWelcomeResponse
                }
              />
            </div>
          )}

        <div ref={messagesEndRef} />
      </div>

      {/* Chat Input */}
      <div className="bg-white p-4">
        {/* Contextual controls above input */}
        {(() => {
          const latestMessage = appState.chat.messages[appState.chat.messages.length - 1];
          const showSkip = !!(
            latestMessage &&
            latestMessage.type === 'bot' &&
            latestMessage.data?.skippable
          );
          if (!showSkip) return null;
          return (
            <div className="mb-2 flex w-full items-center justify-end">
              <button
                onClick={appState.chat.sendSkip}
                disabled={!appState.chat.isConnected || appState.chat.isTyping}
                className="rounded-lg border border-gray-300 px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
                aria-label="Skip this question"
                title="Skip this question"
              >
                Skip this question
              </button>
            </div>
          );
        })()}

        <div className="flex items-center gap-3">
          <button className="text-gray-400 hover:text-gray-600">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path
                d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>

          <input
            type="text"
            value={appState.userInput}
            onChange={e => appState.setUserInput(e.target.value)}
            onKeyDown={appState.handleKeyPress}
            placeholder="Type your message here..."
            disabled={!appState.chat.isConnected || appState.chat.isTyping}
            className="flex-1 border-0 bg-transparent text-sm placeholder:text-gray-400 focus:outline-none disabled:opacity-50"
          />

          <button className="text-gray-400 hover:text-gray-600">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path
                d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                stroke="currentColor"
                strokeWidth="2"
              />
              <path
                d="M8 12H16M12 8V16"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </button>

          <button className="text-gray-400 hover:text-gray-600">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path
                d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
          <button
            onClick={() => appState.sendMessage(appState.userInput)}
            disabled={
              !appState.userInput.trim() ||
              !appState.chat.isConnected ||
              appState.chat.isTyping
            }
            className="text-primary hover:text-primary/80 disabled:cursor-not-allowed disabled:opacity-50"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path
                d="M22 2L11 13"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M22 2L15 22L11 13L2 9L22 2Z"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>

        {/* Connection retry button */}
        {!appState.chat.isConnected && !appState.chat.isConnecting && (
          <div className="mt-2 text-center">
            <button
              onClick={appState.chat.retryConnection}
              className="mx-auto flex items-center gap-1 text-sm text-primary hover:text-primary/80"
            >
              <RefreshCw className="h-4 w-4" />
              Retry Connection
            </button>
          </div>
        )}
      </div>
    </div>
  );

  // Render interview scheduling step
  const renderInterviewScheduling = () => (
    <div className="p-6 text-center">
      <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-primary/10">
        <svg
          width="32"
          height="32"
          viewBox="0 0 24 24"
          fill="none"
          className="text-primary"
        >
          <path
            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </div>
      <h3 className="mb-4 text-xl font-semibold text-gray-900">
        Schedule Your Interview
      </h3>
      <p className="mb-6 text-gray-600">
        Great! Your CV has been analyzed. Let's schedule your interview with the
        LibertyTech team.
      </p>

      <button
        onClick={appState.openSchedulingModal}
        className="w-full rounded-lg bg-primary px-4 py-3 text-white hover:bg-primary/90"
      >
        Schedule Interview
      </button>
    </div>
  );

  // Render interview confirmation step
  const renderInterviewConfirmation = () => (
    <div className="p-6 text-center">
      <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-green-100">
        <CheckCircle className="h-10 w-10 text-green-600" />
      </div>
      <h3 className="mb-4 text-xl font-semibold text-gray-900">
        Interview Scheduled!
      </h3>
      <p className="mb-6 text-gray-600">
        Your interview has been scheduled. You'll receive a confirmation email
        with the details.
      </p>

      <div className="space-y-3">
        <div className="rounded-lg bg-blue-50 p-4 text-left">
          <h4 className="mb-2 font-medium text-blue-900">Interview Details</h4>
          <div className="space-y-1 text-sm text-blue-700">
            <p>
              <strong>Date:</strong> {appState.interviewScheduling.selectedDate}
            </p>
            <p>
              <strong>Time:</strong> {appState.interviewScheduling.selectedTime}
            </p>
            <p>
              <strong>Duration:</strong> 1hr 30mins
            </p>
            <p>
              <strong>Type:</strong> Video Interview
            </p>
          </div>
        </div>
      </div>

      <button
        onClick={() => {
          // Move to complete step - this should be handled by the state management
          onClose(); // For now, just close the modal
        }}
        className="mt-6 w-full rounded-lg bg-primary px-4 py-3 text-white hover:bg-primary/90"
      >
        Continue
      </button>
    </div>
  );

  // Render chat step
  const renderChat = () => (
    <div className="flex h-full flex-col">
      {/* Offline Status */}
      {!appState.isOnline && (
        <div className="flex items-center justify-center border-b border-red-200 bg-red-50 p-3">
          <div className="flex items-center gap-2 text-sm text-red-800">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <span>You are offline. Please check your internet connection.</span>
          </div>
        </div>
      )}

      {/* Connection Status */}
      <ConnectionStatus
        isConnected={appState.chat.isConnected}
        isConnecting={appState.chat.isConnecting}
      />

      {/* Chat Messages */}
      <div className="flex-1 space-y-4 overflow-y-auto p-4">
        {appState.chat.messages.map((message, index) => (
          <ChatMessage
            key={message.id}
            message={message}
            isLatest={index === appState.chat.messages.length - 1}
            uploadedFile={appState.upload.uploadedFile}
          />
        ))}

        {appState.chat.isTyping && <TypingIndicator />}

        <div ref={messagesEndRef} />
      </div>

      {/* Chat Input */}
      <div className="bg-white p-4">
        <div className="flex items-center gap-3">
          <button className="text-gray-400 hover:text-gray-600">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path
                d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>

          <input
            type="text"
            value={appState.userInput}
            onChange={e => appState.setUserInput(e.target.value)}
            onKeyDown={appState.handleKeyPress}
            placeholder="Type your message here..."
            disabled={!appState.chat.isConnected || appState.chat.isTyping}
            className="flex-1 border-0 bg-transparent text-sm placeholder:text-gray-400 focus:outline-none disabled:opacity-50"
          />

          <button className="text-gray-400 hover:text-gray-600">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path
                d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                stroke="currentColor"
                strokeWidth="2"
              />
              <path
                d="M8 12H16M12 8V16"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </button>

          <button className="text-gray-400 hover:text-gray-600">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path
                d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>


          <button
            onClick={() => appState.sendMessage(appState.userInput)}
            disabled={
              !appState.userInput.trim() ||
              !appState.chat.isConnected ||
              appState.chat.isTyping
            }
            className="text-primary hover:text-primary/80 disabled:cursor-not-allowed disabled:opacity-50"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path
                d="M22 2L11 13"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M22 2L15 22L11 13L2 9L22 2Z"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>

        {/* Connection retry button */}
        {!appState.chat.isConnected && !appState.chat.isConnecting && (
          <div className="mt-2 text-center">
            <button
              onClick={appState.chat.retryConnection}
              className="mx-auto flex items-center gap-1 text-sm text-primary hover:text-primary/80"
            >
              <RefreshCw className="h-4 w-4" />
              Retry Connection
            </button>
          </div>
        )}
      </div>
    </div>
  );

  // Render complete step
  const renderComplete = () => {
    // Determine if application was rejected based on status or threshold
    const isRejected =
      appState.applicationSummary?.status === 'rejected' ||
      appState.applicationSummary?.threshold_met === false;

    // Choose appropriate colors, icon, and messaging based on status
    const statusConfig = isRejected
      ? {
          iconBg: 'bg-red-100',
          iconColor: 'text-red-600',
          icon: AlertCircle,
          title: 'Application Not Successful',
          message:
            'Thank you for your interest. Unfortunately, your application did not meet the requirements for this position.',
          summaryBg: 'bg-red-50',
          summaryText: 'text-red-700',
          summaryHeading: 'text-red-900',
        }
      : {
          iconBg: 'bg-green-100',
          iconColor: 'text-green-600',
          icon: CheckCircle,
          title: 'Application Complete!',
          message:
            'Thank you for completing the AI-powered application process.',
          summaryBg: 'bg-green-50',
          summaryText: 'text-green-700',
          summaryHeading: 'text-green-900',
        };

    const StatusIcon = statusConfig.icon;

    return (
      <div className="p-6 text-center">
        <div
          className={`mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full ${statusConfig.iconBg}`}
        >
          <StatusIcon className={`h-10 w-10 ${statusConfig.iconColor}`} />
        </div>
        <h3 className="mb-4 text-xl font-semibold text-gray-900">
          {statusConfig.title}
        </h3>
        <p className="mb-6 text-gray-600">{statusConfig.message}</p>

        {appState.applicationSummary && (
          <div className="mb-6 space-y-4 text-left">
            <div className={`rounded-lg ${statusConfig.summaryBg} p-4`}>
              <h4 className={`mb-2 font-medium ${statusConfig.summaryHeading}`}>
                Application Summary
              </h4>
              <div className={`space-y-1 text-sm ${statusConfig.summaryText}`}>
                <p>
                  <strong>Position:</strong>{' '}
                  {appState.applicationSummary.job_title}
                </p>
                <p>
                  <strong>Applicant:</strong>{' '}
                  {appState.applicationSummary.applicant_name}
                </p>
                <p>
                  <strong>CV Score:</strong>{' '}
                  {appState.applicationSummary.cv_score}/100
                </p>
                <p>
                  <strong>Status:</strong> {appState.applicationSummary.status}
                </p>
                <p>
                  <strong>Threshold Met:</strong>{' '}
                  {appState.applicationSummary.threshold_met ? 'Yes' : 'No'}
                </p>
              </div>
            </div>

            {!isRejected && (
              <div className="rounded-lg bg-blue-50 p-4">
                <h4 className="mb-2 font-medium text-blue-900">What's Next?</h4>
                <ul className="space-y-1 text-sm text-blue-700">
                  <li>
                    • You'll receive an email confirmation with next steps
                  </li>
                  <li>
                    • Our team will contact you if any additional information is
                    needed
                  </li>
                  <li>• Keep an eye on your email for interview scheduling</li>
                </ul>
              </div>
            )}
          </div>
        )}

        <button
          onClick={onClose}
          className="w-full rounded-lg bg-primary px-4 py-3 text-white hover:bg-primary/90"
        >
          Close Application
        </button>
      </div>
    );
  };

  // Render error step
  const renderError = () => (
    <div className="p-6 text-center">
      <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-red-100">
        <AlertCircle className="h-10 w-10 text-red-600" />
      </div>
      <h3 className="mb-4 text-xl font-semibold text-gray-900">
        Something went wrong
      </h3>
      <p className="mb-6 text-gray-600">
        {appState.applicationState.error ||
          'An unexpected error occurred during the application process.'}
      </p>

      <div className="space-y-3">
        <button
          onClick={appState.retryConnection}
          className="flex w-full items-center justify-center gap-2 rounded-lg bg-primary px-4 py-3 text-white hover:bg-primary/90"
        >
          <RefreshCw className="h-4 w-4" />
          Try Again
        </button>

        <button
          onClick={appState.resetApplication}
          className="w-full rounded-lg border border-gray-300 px-4 py-3 text-gray-700 hover:bg-gray-50"
        >
          Start Over
        </button>

        <button
          onClick={onClose}
          className="w-full text-sm text-gray-500 hover:text-gray-700"
        >
          Close Application
        </button>
      </div>
    </div>
  );

  const currentStepIndex = getCurrentStepIndex();

  return (
    <div className="flex h-full flex-col">
      {/* Progress indicator - only show for non-chat and non-email steps */}
      {appState.applicationState.currentStep !== 'chat' &&
        appState.applicationState.currentStep !== 'upload' &&
        appState.applicationState.currentStep !== 'email_collection' && (
          <div className="border-b bg-gray-50 px-6 py-4">
            <div className="flex items-center justify-between">
              {steps
                .filter(step => step.name !== 'Email')
                .map((step, index, filteredSteps) => (
                  <div key={step.id} className="flex items-center">
                    <div
                      className={cn(
                        'flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium',
                        currentStepIndex >= step.id
                          ? 'bg-primary text-white'
                          : 'bg-gray-200 text-gray-600'
                      )}
                    >
                      {currentStepIndex > step.id ? (
                        <CheckCircle className="h-5 w-5" />
                      ) : (
                        <step.icon className="h-4 w-4" />
                      )}
                    </div>
                    {index < filteredSteps.length - 1 && (
                      <div
                        className={cn(
                          'ml-2 h-0.5 w-8',
                          currentStepIndex > step.id
                            ? 'bg-primary'
                            : 'bg-gray-200'
                        )}
                      />
                    )}
                  </div>
                ))}
            </div>
            <div className="mt-2 text-center">
              <p className="text-sm text-gray-600">
                Step {Math.max(1, currentStepIndex)} of {steps.length - 1}:{' '}
                {steps[currentStepIndex]?.name || 'Introduction'}
              </p>
            </div>
          </div>
        )}

      {/* Step content */}
      <div
        className={cn(
          'flex-1',
          appState.applicationState.currentStep === 'chat' ||
            appState.applicationState.currentStep === 'upload'
            ? 'flex flex-col'
            : 'overflow-y-auto'
        )}
      >
        {renderStepContent()}
      </div>

      {/* Interview Scheduling Modal */}
      <InterviewSchedulingModal
        isOpen={appState.interviewScheduling.isSchedulingModalOpen}
        onClose={appState.closeSchedulingModal}
        jobTitle={jobData?.title}
        onScheduleConfirm={appState.openConfirmationModal}
      />

      {/* Interview Confirmation Modal */}
      <InterviewConfirmationModal
        isOpen={appState.interviewScheduling.isConfirmationModalOpen}
        onClose={appState.closeConfirmationModal}
        jobTitle={jobData?.title}
        selectedDate={appState.interviewScheduling.selectedDate}
        selectedTime={appState.interviewScheduling.selectedTime}
        onConfirm={appState.confirmInterview}
      />
    </div>
  );
};

export default AIApplicationFlow;
