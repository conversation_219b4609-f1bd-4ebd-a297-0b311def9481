'use client';

import React from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { CheckCircle, Clock, Globe, Video, X } from 'lucide-react';

interface InterviewConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  jobTitle?: string;
  selectedDate?: string;
  selectedTime?: string;
  onConfirm?: () => void;
}

const InterviewConfirmationModal: React.FC<InterviewConfirmationModalProps> = ({
  isOpen,
  onClose,
  jobTitle = "Product Designer",
  selectedDate = "Tuesday, September 10, 2024",
  selectedTime = "10:00 - 11:30",
  onConfirm
}) => {
  const handleConfirm = () => {
    onConfirm?.();
    onClose();
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-[9999]" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white shadow-xl transition-all">
                {/* Header */}
                <div className="bg-primary px-6 py-4 text-white">
                  <div className="flex items-center justify-between">
                    <Dialog.Title className="text-lg font-medium">
                      Confirm date and time
                    </Dialog.Title>
                    <button
                      onClick={onClose}
                      className="text-white/80 hover:text-white"
                    >
                      <X className="h-6 w-6" />
                    </button>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6 space-y-6">
                  {/* Success Icon */}
                  <div className="flex justify-center">
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center border-2 border-dashed border-green-300">
                      <CheckCircle className="w-8 h-8 text-green-600" />
                    </div>
                  </div>

                  {/* Confirmation Message */}
                  <div className="text-center">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Are you sure you are available for the set date and time for this role "{jobTitle}"?
                    </h3>
                    <p className="text-sm text-gray-600">
                      Once you confirm, you will receive a confirmation email with the interview details and a link to join the video call. You can reschedule if needed via the confirmation email.
                    </p>
                  </div>

                  {/* Meeting Details */}
                  <div className="border-2 border-dashed border-gray-200 rounded-lg p-4 space-y-3">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Clock className="w-4 h-4" />
                      <span>1hr 30mins</span>
                      <Globe className="w-4 h-4 ml-4" />
                      <span>West Africa Time (5:30pm)</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Video className="w-4 h-4" />
                      <span>Meeting details will be provided upon confirmation</span>
                    </div>
                    <div className="bg-blue-50 p-3 rounded-lg">
                      <div className="flex items-center gap-2 text-sm text-blue-900">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <span>{selectedTime}, {selectedDate}.</span>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-3 pt-4">
                    <button
                      onClick={onClose}
                      className="flex-1 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 font-medium"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleConfirm}
                      className="flex-1 px-4 py-3 bg-primary text-white rounded-lg hover:bg-primary/90 font-medium"
                    >
                      Confirm Interview
                    </button>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

export default InterviewConfirmationModal;
