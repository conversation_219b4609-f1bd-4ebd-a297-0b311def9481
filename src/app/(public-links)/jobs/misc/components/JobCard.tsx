import Suitcase from '@/app/(website-main)/e/jobs/misc/icons/Suitcase'
import { ActiveJob } from '@/app/(website-main)/e/jobs/misc/types'
import { Money } from '@/components/shared/icons'
import { cn } from '@/utils'
import { addCommasToNumber } from '@/utils/numbers'
import { convertKebabAndSnakeToTitleCase, convertToTitleCase } from '@/utils/strings'
import moment from 'moment'
import Link from 'next/link'
import React from 'react'

interface JobCardProps {
    job: ActiveJob
}

const JobCard: React.FC<JobCardProps> = ({ job }) => {
    return (
        <Link href={`/jobs/${job?.unique_id}`} >
            <article className='flex max-md:flex-col items-start gap-x-3 overflow-x-hidden border border-[#E4E4E4] hover:border-black rounded-xl  p-5 min-h-max'>
                <div className="w-[2rem] h-[2rem] md:h-[2.25rem] max-md:mb-4 md:w-[2.25rem] rounded-full shrink-0">
                    <img src={job?.logo || job?.company?.logo || ""} alt={job?.company?.logo ?? "Logo"} className='text-[8px] w-full h-full rounded-full' />
                </div>

                <div className="flex flex-col gap-1.5 max-w-[92%]">
                    <h2 className="text-base md:text-[1.125rem] medium text-header-text truncate"> {job?.job_title}</h2>
                    <div className="flex text-sm text-body-text">
                        <p className=' mr-1'>
                            {job?.location} .
                        </p>
                        <p>
                            {moment(job?.created_at).fromNow(true) + " ago"}
                        </p>
                    </div>
                    <div className="flex items-center max-md:flex-wrap gap-x-5 gap-y-2 text-sm text-body-text">
                        <p className='flex items-center gap-1.5'>
                            <span>
                                <Money fill="#4A4A68" />
                            </span>
                            {
                                job.salary_type === "FIXED" ?
                                    job.fixed_salary :
                                    job.salary_type === "RANGE" ?
                                        `${job?.salary_currency}${addCommasToNumber(job?.min_salary ?? 0)} -  ${job?.salary_currency}${addCommasToNumber(job?.max_salary ?? 0)}`
                                        :
                                        "Undisclosed"
                            }
                        </p>

                        <p className='flex items-center gap-1.5'>
                            <Suitcase width={15} height={15} fill='#4A4A68' />
                            {convertKebabAndSnakeToTitleCase(job?.job_type)}
                        </p>
                    </div>

                    <div className='flex items-center gap-2.5 text-body-text'>
                        <span
                            className={cn("text-xs px-4 py-2 rounded-lg",
                                job.job_status == "OPEN" && "bg-[#12B6691A] text-green-500",
                                job.job_status == "CLOSED" && "bg-[#FF5F561A] text-[#FF5F56]",
                            )}
                        >
                            {convertToTitleCase(job.job_status)}
                        </span>

                        <span className='text-primary bg-[#F5F3FF] text-xs px-4 py-2 rounded-lg'>
                            {convertKebabAndSnakeToTitleCase(job.working_option)}
                        </span>

                        <span className='text-primary bg-[#F5F3FF] text-xs px-4 py-2 rounded-lg'>
                            {convertKebabAndSnakeToTitleCase(job.job_type)}
                        </span>
                    </div>
                </div>

            </article>
        </Link>
    )
}

export default JobCard