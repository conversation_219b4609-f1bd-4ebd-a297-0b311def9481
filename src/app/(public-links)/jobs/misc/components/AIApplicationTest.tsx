'use client';

import { But<PERSON> } from '@/components/shared/btn';
import { Card } from '@/components/shared/card';
import { Play, RotateCcw, Square, Wifi, WifiOff } from 'lucide-react';
import React, { useState } from 'react';
import { useAIApplicationState } from '../hooks/useAIApplicationState';
import { ChatMessage, ConnectionStatus, ProgressSummary, TypingIndicator } from './ChatMessage';
import { FileUploadZone } from './FileUploadZone';

// Test component for validating the AI Application flow
const AIApplicationTest: React.FC = () => {
  const [testJobId] = useState('test-job-123');
  const [testEmail, setTestEmail] = useState('');
  const [isTestMode, setIsTestMode] = useState(false);
  const [testLogs, setTestLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  const appState = useAIApplicationState({
    jobId: testJobId,
    applicantEmail: testEmail || undefined, // Only pass if provided
    jobData: {
      id: testJobId,
      unique_id: testJobId, // Add unique_id for testing (using same value as id for test)
      title: 'Senior Frontend Developer',
      company_name: 'Test Company',
      location: 'Remote'
    },
    onComplete: (summary) => {
      addLog(`✅ Application completed: ${JSON.stringify(summary)}`);
    },
    onError: (error) => {
      addLog(`❌ Error occurred: ${error}`);
    }
  });

  const startTest = () => {
    setIsTestMode(true);
    setTestLogs([]);
    addLog('🚀 Starting AI Application test...');
    appState.startApplication();
  };

  const stopTest = () => {
    setIsTestMode(false);
    appState.resetApplication();
    addLog('⏹️ Test stopped and reset');
  };

  const resetTest = () => {
    setTestLogs([]);
    appState.resetApplication();
    addLog('🔄 Test reset');
  };

  const simulateNetworkIssue = () => {
    addLog('📡 Simulating network disconnection...');
    appState.chat.disconnect();
  };

  const testFileUpload = () => {
    addLog('📄 Testing file upload...');
    // Create a mock file for testing
    const mockFile = new File(['test content'], 'test-resume.pdf', { type: 'application/pdf' });
    appState.upload.uploadFile(mockFile);
  };

  const sendTestMessage = (message: string) => {
    addLog(`💬 Sending test message: "${message}"`);
    appState.sendMessage(message);
  };

  const testWebSocketSequence = () => {
    addLog('🔄 Testing WebSocket sequence validation...');
    addLog('1. Attempting file upload without WebSocket connection...');
    const mockFile = new File(['test content'], 'test-resume.pdf', { type: 'application/pdf' });
    appState.upload.uploadFile(mockFile).then(result => {
      if (!result.success) {
        addLog('✅ Upload correctly blocked: ' + result.error);
      } else {
        addLog('❌ Upload should have been blocked!');
      }
    });
  };

  const testScenarios = [
    {
      name: 'Basic Flow',
      description: 'Test the complete application flow',
      action: startTest
    },
    {
      name: 'WebSocket Sequence',
      description: 'Test WebSocket connection sequence validation',
      action: testWebSocketSequence
    },
    {
      name: 'File Upload',
      description: 'Test CV upload functionality',
      action: testFileUpload
    },
    {
      name: 'Network Issue',
      description: 'Simulate connection problems',
      action: simulateNetworkIssue
    },
    {
      name: 'Chat Message',
      description: 'Send a test chat message',
      action: () => sendTestMessage('Hello, this is a test message!')
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            AI Application Flow Test Suite
          </h1>
          <p className="text-gray-600">
            Test and validate the WebSocket-based AI chat interview integration
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Test Controls */}
          <div className="lg:col-span-1">
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">Test Controls</h2>
              
              <div className="space-y-4">
                <div className="flex gap-2">
                  <Button
                    onClick={startTest}
                    disabled={isTestMode}
                    className="flex items-center gap-2"
                  >
                    <Play className="h-4 w-4" />
                    Start Test
                  </Button>
                  
                  <Button
                    onClick={stopTest}
                    disabled={!isTestMode}
                    variant="outlined"
                    className="flex items-center gap-2"
                  >
                    <Square className="h-4 w-4" />
                    Stop
                  </Button>
                  
                  <Button
                    onClick={resetTest}
                    variant="outlined"
                    className="flex items-center gap-2"
                  >
                    <RotateCcw className="h-4 w-4" />
                    Reset
                  </Button>
                </div>

                <div className="border-t pt-4">
                  <h3 className="font-medium mb-3">Test Scenarios</h3>
                  <div className="space-y-2">
                    {testScenarios.map((scenario, index) => (
                      <div key={index} className="flex flex-col gap-1">
                        <Button
                          onClick={scenario.action}
                          variant="outlined"
                          size="small"
                          className="justify-start"
                        >
                          {scenario.name}
                        </Button>
                        <p className="text-xs text-gray-500 px-2">
                          {scenario.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="border-t pt-4">
                  <h3 className="font-medium mb-3">Connection Status</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      {appState.isOnline ? (
                        <Wifi className="h-4 w-4 text-green-600" />
                      ) : (
                        <WifiOff className="h-4 w-4 text-red-600" />
                      )}
                      <span>Network: {appState.isOnline ? 'Online' : 'Offline'}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className={`h-2 w-2 rounded-full ${
                        appState.chat.isConnected ? 'bg-green-500' : 
                        appState.chat.isConnecting ? 'bg-yellow-500' : 'bg-red-500'
                      }`} />
                      <span>WebSocket: {
                        appState.chat.isConnected ? 'Connected' : 
                        appState.chat.isConnecting ? 'Connecting' : 'Disconnected'
                      }</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className={`h-2 w-2 rounded-full ${
                        appState.chat.hasWelcomeResponse ? 'bg-green-500' : 'bg-gray-300'
                      }`} />
                      <span className="text-xs">Welcome Response: {
                        appState.chat.hasWelcomeResponse ? 'Received' : 'Pending'
                      }</span>
                    </div>
                    <div className="text-xs text-gray-500">
                      Step: {appState.applicationState.currentStep}
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Test Logs */}
            <Card className="p-6 mt-6">
              <h2 className="text-xl font-semibold mb-4">Test Logs</h2>
              <div className="bg-gray-900 text-green-400 p-4 rounded-lg h-64 overflow-y-auto font-mono text-xs">
                {testLogs.length === 0 ? (
                  <div className="text-gray-500">No logs yet. Start a test to see activity.</div>
                ) : (
                  testLogs.map((log, index) => (
                    <div key={index} className="mb-1">
                      {log}
                    </div>
                  ))
                )}
              </div>
            </Card>
          </div>

          {/* Application Preview */}
          <div className="lg:col-span-2">
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">Application Preview</h2>
              
              {/* Current Step Display */}
              <div className="mb-6 p-4 bg-blue-50 rounded-lg">
                <h3 className="font-medium text-blue-900">
                  Current Step: {appState.applicationState.currentStep}
                </h3>
                {appState.applicationState.error && (
                  <p className="text-red-600 text-sm mt-1">
                    Error: {appState.applicationState.error}
                  </p>
                )}
              </div>

              {/* Step Content */}
              <div className="border rounded-lg min-h-96">
                {appState.applicationState.currentStep === 'introduction' && (
                  <div className="p-6 text-center">
                    <h3 className="text-xl font-semibold mb-4">Welcome to AI Application Test</h3>
                    <p className="text-gray-600 mb-6">
                      This is a test environment for the AI-powered application flow.
                    </p>
                    <Button onClick={appState.startApplication}>
                      Start Test Application
                    </Button>
                  </div>
                )}

                {appState.applicationState.currentStep === 'upload' && (
                  <div className="p-6">
                    <h3 className="text-xl font-semibold mb-4">File Upload Test</h3>

                    {/* WebSocket Connection Status for Upload */}
                    {!appState.chat.isConnected && (
                      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                          <p className="text-sm text-blue-700">
                            Connecting to application service...
                          </p>
                        </div>
                      </div>
                    )}

                    {appState.chat.isConnected && !appState.chat.hasWelcomeResponse && (
                      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600 mr-2"></div>
                          <p className="text-sm text-yellow-700">
                            Initializing application session...
                          </p>
                        </div>
                      </div>
                    )}

                    {appState.chat.isConnected && appState.chat.hasWelcomeResponse && (
                      <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center">
                          <div className="h-4 w-4 bg-green-600 rounded-full mr-2"></div>
                          <p className="text-sm text-green-700">
                            Ready for file upload!
                          </p>
                        </div>
                      </div>
                    )}

                    <FileUploadZone
                      isUploading={appState.upload.isUploading}
                      uploadProgress={appState.upload.uploadProgress}
                      uploadedFile={appState.upload.uploadedFile}
                      uploadResult={appState.upload.uploadResult}
                      onFileSelect={appState.upload.handleFileSelect}
                      onFileDrop={appState.upload.handleFileDrop}
                      onDragOver={appState.upload.handleDragOver}
                      onDragEnter={appState.upload.handleDragEnter}
                      onReset={appState.upload.resetUpload}
                      disabled={!appState.chat.isConnected || !appState.chat.hasWelcomeResponse}
                    />
                  </div>
                )}

                {appState.applicationState.currentStep === 'chat' && (
                  <div className="flex flex-col h-96">
                    <ConnectionStatus 
                      isConnected={appState.chat.isConnected} 
                      isConnecting={appState.chat.isConnecting} 
                    />
                    <ProgressSummary progress={appState.chat.progress} />
                    
                    <div className="flex-1 overflow-y-auto p-4 space-y-4">
                      {appState.chat.messages.map((message, index) => (
                        <ChatMessage 
                          key={message.id} 
                          message={message} 
                          isLatest={index === appState.chat.messages.length - 1}
                        />
                      ))}
                      {appState.chat.isTyping && <TypingIndicator />}
                    </div>

                    <div className="border-t p-4">
                      <div className="flex gap-2">
                        <input
                          type="text"
                          value={appState.userInput}
                          onChange={(e) => appState.setUserInput(e.target.value)}
                          onKeyDown={appState.handleKeyPress}
                          placeholder="Type a test message..."
                          className="flex-1 rounded-lg border border-gray-300 px-4 py-2 text-sm"
                        />
                        <Button
                          onClick={() => appState.sendMessage(appState.userInput)}
                          disabled={!appState.userInput.trim() || !appState.chat.isConnected}
                        >
                          Send
                        </Button>
                      </div>
                    </div>
                  </div>
                )}

                {appState.applicationState.currentStep === 'complete' && (
                  <div className="p-6 text-center">
                    <h3 className="text-xl font-semibold text-green-600 mb-4">
                      ✅ Test Completed Successfully!
                    </h3>
                    {appState.applicationSummary && (
                      <div className="bg-green-50 p-4 rounded-lg text-left">
                        <h4 className="font-medium mb-2">Application Summary:</h4>
                        <pre className="text-sm text-gray-700">
                          {JSON.stringify(appState.applicationSummary, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                )}

                {appState.applicationState.currentStep === 'error' && (
                  <div className="p-6 text-center">
                    <h3 className="text-xl font-semibold text-red-600 mb-4">
                      ❌ Test Error
                    </h3>
                    <p className="text-gray-600 mb-6">
                      {appState.applicationState.error}
                    </p>
                    <div className="space-x-2">
                      <Button onClick={appState.retryConnection}>
                        Retry Connection
                      </Button>
                      <Button onClick={appState.resetApplication} variant="outlined">
                        Reset Test
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIApplicationTest;
