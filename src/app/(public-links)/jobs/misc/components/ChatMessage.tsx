'use client';

import {
  Al<PERSON><PERSON>ircle,
  CheckCircle,
  Clock,
  FileText,
  Trash2,
} from 'lucide-react';
import React from 'react';
import MayaBot from '@/app/misc/icons/MayaBot';
import { cn } from '@/utils';
import { ChatMessage as ChatMessageType } from '../hooks/useAIApplicationChat';

// Utility function to determine if an application is rejected
const isApplicationRejected = (
  status?: string,
  thresholdMet?: boolean,
  passedThreshold?: boolean
) => {
  return (
    status === 'rejected' || thresholdMet === false || passedThreshold === false
  );
};

// Utility function to get status-based styling configuration
const getStatusStyling = (isRejected: boolean) => {
  return isRejected
    ? {
        border: 'border-red-200',
        bg: 'bg-red-50',
        text: 'text-red-800',
        heading: 'text-red-900',
        icon: AlertCircle,
      }
    : {
        border: 'border-green-200',
        bg: 'bg-green-50',
        text: 'text-green-800',
        heading: 'text-green-900',
        icon: CheckCircle,
      };
};

interface ChatMessageProps {
  message: ChatMessageType;
  isLatest?: boolean;
  uploadedFile?: File | null;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  isLatest = false,
  uploadedFile,
}) => {
  const isBot = message.type === 'bot';
  const isUser = message.type === 'user';

  // Format timestamp
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  };

  // Render progress information for AI responses
  // const renderProgressInfo = () => {
  //   if (!isBot || !message.data?.progress) return null;

  //   const progress = message.data.progress;
  //   const progressPercentage =
  //     progress.total_fields > 0
  //       ? Math.round((progress.collected_fields / progress.total_fields) * 100)
  //       : 0;

  //   return (
  //     <div className="mt-3 rounded-lg border border-blue-200 bg-blue-50 p-3">
  //       <div className="mb-2 flex items-center justify-between">
  //         <span className="text-sm font-medium text-blue-900">
  //           Application Progress
  //         </span>
  //         <span className="text-sm text-blue-700">{progressPercentage}%</span>
  //       </div>
  //       <div className="mb-2 h-2 w-full rounded-full bg-blue-200">
  //         <div
  //           className="h-2 rounded-full bg-blue-600 transition-all duration-300"
  //           style={{ width: `${progressPercentage}%` }}
  //         />
  //       </div>
  //       <div className="text-xs text-blue-700">
  //         {progress.collected_fields} of {progress.total_fields} fields
  //         collected
  //       </div>
  //       {progress.missing_fields && progress.missing_fields.length > 0 && (
  //         <div className="mt-2 text-xs text-blue-600">
  //           Missing: {progress.missing_fields.join(', ')}
  //         </div>
  //       )}
  //     </div>
  //   );
  // };

  // Render CV score information with detailed breakdown
  const renderCVScore = () => {
    if (!isBot || !message.data?.cv_score) return null;

    const score = message.data.cv_score;
    const passed = message.data.passed_threshold;
    const status = message.data.status;

    // Determine if application was rejected based on status or threshold
    const isRejected = isApplicationRejected(status, undefined, passed);

    return (
      <div className="mt-3 space-y-3">
        {/* CV Analysis Progress Circle */}
        <div className="flex justify-center">
          <div className="flex h-16 w-16 items-center justify-center rounded-full border-2 border-dashed border-purple-300 bg-purple-100">
            <div className="text-center">
              <div className="text-xs text-primary">100% done</div>
              <div className="text-xs text-primary">analyzing CV</div>
            </div>
          </div>
        </div>

        {/* Score Breakdown */}
        <div className="rounded-lg border border-gray-200 bg-white p-4">
          <div className="mb-3 text-center">
            <div className="text-sm font-medium text-gray-900">
              Based on the information in your CV and the role requirements,
              your CV has received a score of{' '}
              <span className="font-semibold text-primary">
                {score} out of 10
              </span>
              . Here's a breakdown:
            </div>
          </div>

          <div className="space-y-3 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-gray-700">
                • <strong>Experience: 9/10</strong> – Your work as a Product
                Designer at 2PalAfrica is highly relevant, especially your focus
                on UI/UX design.
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-700">
                • <strong>Technical Skills: 8/10</strong> – You have strong
                proficiency in tools like Figma, Adobe XD, and Sketch, which are
                essential for the role.
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-700">
                • <strong>Problem-Solving: 8/10</strong> – Your example of
                tackling the user drop-off rate was excellent and demonstrates
                strong analytical thinking.
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-700">
                • <strong>Communication & Collaboration: 8/10</strong> – Your
                work with cross-functional teams indicates you have solid
                collaboration skills, though more information on stakeholder
                management could be helpful.
              </span>
            </div>
          </div>

          <div
            className={cn(
              'mt-4 rounded-lg p-3 text-center',
              isRejected
                ? 'bg-red-50 text-red-800'
                : 'bg-green-50 text-green-800'
            )}
          >
            <div className="text-sm font-medium">
              {isRejected
                ? 'Unfortunately, your application did not meet the minimum requirements for this position.'
                : 'Congratulations! Your CV meets the requirements for this position.'}
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Render CV file representation for uploaded CV
  const renderCVFileRepresentation = () => {
    if (!isBot || message.messageType !== 'cv_uploaded') return null;

    // Use uploadedFile prop if available, otherwise fall back to message data
    const fileInfo = uploadedFile
      ? {
          filename: uploadedFile.name,
          size: uploadedFile.size,
          type: uploadedFile.type,
          status: 'Uploaded successfully',
        }
      : message.data?.file_info;

    if (!fileInfo) return null;

    const fileName = fileInfo.filename || fileInfo.name || 'Resume.pdf';
    const fileSize = fileInfo.size
      ? `${(fileInfo.size / 1024 / 1024).toFixed(2)} MB`
      : '';
    const uploadStatus = fileInfo.status || 'Uploaded successfully';

    return (
      <div className="mt-3 rounded-lg border border-gray-200 bg-gray-50 p-4">
        <div className="flex items-center gap-3">
          {/* Document Icon */}
          <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-lg bg-purple-100">
            <FileText className="h-5 w-5 text-primary" />
          </div>

          {/* File Details */}
          <div className="min-w-0 flex-1">
            <div className="flex items-center gap-2">
              <h4 className="truncate text-sm font-medium text-gray-900">
                {fileName.replace(/\.[^/.]+$/, '')} resume
              </h4>
            </div>

            <div className="mt-1">
              <p className="text-xs text-gray-500">
                Tap on{' '}
                <span className="cursor-pointer font-medium text-primary hover:underline">
                  change
                </span>{' '}
                to select other saved CVs
              </p>
            </div>
          </div>

          {/* Delete Action */}
          <button
            className="shrink-0 p-1 text-red-400 transition-colors hover:text-red-600"
            title="Remove CV"
          >
            <Trash2 className="h-4 w-4" />
          </button>
        </div>
      </div>
    );
  };

  // Render phase progression information
  const renderPhaseProgression = () => {
    if (
      !isBot ||
      message.messageType !== 'phase_complete' ||
      !message.data?.phase_progression
    )
      return null;

    const phases = message.data.phase_progression;

    return (
      <div className="mt-3 rounded-lg border border-blue-200 bg-blue-50 p-4">
        <h4 className="mb-3 flex items-center gap-2 text-sm font-semibold text-blue-900">
          <CheckCircle className="h-4 w-4" />
          Interview Phases
        </h4>
        <div className="space-y-2">
          {phases.map((phase: any, index: number) => {
            const isActive = phase.status === 'in_progress';
            const isCompleted = phase.status === 'completed';
            const isPending = phase.status === 'pending';

            return (
              <div key={index} className="flex items-center gap-3">
                <div
                  className={cn(
                    'flex h-6 w-6 items-center justify-center rounded-full text-xs font-medium',
                    isCompleted && 'bg-green-100 text-green-700',
                    isActive && 'bg-blue-100 text-blue-700',
                    isPending && 'bg-gray-100 text-gray-500'
                  )}
                >
                  {isCompleted ? '✓' : index + 1}
                </div>
                <div className="flex-1">
                  <div
                    className={cn(
                      'text-sm font-medium',
                      isActive && 'text-blue-900',
                      isCompleted && 'text-green-900',
                      isPending && 'text-gray-600'
                    )}
                  >
                    {phase.name}
                  </div>
                  <div className="text-xs capitalize text-gray-500">
                    {phase.status.replace('_', ' ')}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // Render application summary
  const renderApplicationSummary = () => {
    if (!isBot || !message.data?.application_summary) return null;

    const summary = message.data.application_summary;

    // Determine if application was rejected based on status or threshold
    const isRejected = isApplicationRejected(
      summary.status,
      summary.threshold_met
    );

    // Choose appropriate colors and icon based on status
    const statusColors = getStatusStyling(isRejected);
    const StatusIcon = statusColors.icon;

    return (
      <div
        className={`mt-3 rounded-lg border ${statusColors.border} ${statusColors.bg} p-4`}
      >
        <h4
          className={`mb-3 flex items-center gap-2 text-sm font-semibold ${statusColors.heading}`}
        >
          <StatusIcon className="h-4 w-4" />
          Application Summary
        </h4>
        <div className={`space-y-2 text-sm ${statusColors.text}`}>
          <div>
            <strong>Position:</strong> {summary.job_title}
          </div>
          <div>
            <strong>Applicant:</strong> {summary.applicant_name}
          </div>
          <div>
            <strong>CV Score:</strong> {summary.cv_score}/100
          </div>
          <div>
            <strong>Status:</strong> {summary.status}
          </div>
          <div>
            <strong>Threshold Met:</strong>{' '}
            {summary.threshold_met ? 'Yes' : 'No'}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div
      className={cn(
        'flex gap-3 duration-300 animate-in slide-in-from-bottom-2',
        isUser ? 'justify-end' : 'justify-start'
      )}
    >
      {/* Bot Avatar */}
      {isBot && (
        <div className="mt-1 flex h-8 w-8 shrink-0 items-center justify-center rounded-full bg-purple-100">
          <MayaBot />
        </div>
      )}

      {/* Message Content */}
      <div
        className={cn(
          'max-w-[80%] space-y-1',
          isUser && 'flex flex-col items-end'
        )}
      >
        {/* Message Bubble */}
        <div
          className={cn(
            'break-words rounded-2xl px-4 py-3 text-sm',
            isBot
              ? 'rounded-tl-none bg-white text-gray-900 shadow-sm'
              : 'rounded-tr-none bg-primary text-white',
            isLatest && 'shadow-sm'
          )}
        >
          {/* Message Content */}
          <div className="whitespace-pre-wrap">{message.content}</div>

          {/* Additional Data Rendering */}
          {/* {renderProgressInfo()} */}
          {renderCVFileRepresentation()}
          {renderCVScore()}
          {renderPhaseProgression()}
          {renderApplicationSummary()}
        </div>

        {/* Timestamp - Hidden for cleaner design */}
        {/* <div className={cn(
          "text-xs text-gray-500 px-2",
          isUser && "text-right"
        )}>
          {formatTime(message.timestamp)}
        </div> */}
      </div>

      {/* User Avatar */}
      {isUser && (
        <div className="mt-1 flex h-8 w-8 shrink-0 items-center justify-center rounded-full bg-gray-200">
          <span className="text-xs font-medium text-gray-600">U</span>
        </div>
      )}
    </div>
  );
};

// Typing Indicator Component
export const TypingIndicator: React.FC = () => {
  return (
    <div className="flex justify-start gap-3 duration-300 animate-in slide-in-from-bottom-2">
      <div className="flex h-8 w-8 shrink-0 items-center justify-center rounded-full bg-purple-100">
        <MayaBot />
      </div>
      <div className="rounded-2xl rounded-tl-none bg-white px-4 py-3 shadow-sm">
        <div className="flex space-x-1">
          <div className="h-2 w-2 animate-bounce rounded-full bg-gray-400"></div>
          <div
            className="h-2 w-2 animate-bounce rounded-full bg-gray-400"
            style={{ animationDelay: '0.1s' }}
          ></div>
          <div
            className="h-2 w-2 animate-bounce rounded-full bg-gray-400"
            style={{ animationDelay: '0.2s' }}
          ></div>
        </div>
      </div>
    </div>
  );
};

// Connection Status Component
interface ConnectionStatusProps {
  isConnected: boolean;
  isConnecting: boolean;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  isConnected,
  isConnecting,
}) => {
  if (isConnected) return null;

  return (
    <div className="flex items-center justify-center border-b border-yellow-200 bg-yellow-50 p-4">
      <div className="flex items-center gap-2 text-sm text-yellow-800">
        {isConnecting ? (
          <>
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-yellow-600 border-t-transparent"></div>
            <span>Connecting to chat service...</span>
          </>
        ) : (
          <>
            <AlertCircle className="h-4 w-4 text-yellow-600" />
            <span>Disconnected from chat service</span>
          </>
        )}
      </div>
    </div>
  );
};

// Progress Summary Component
interface ProgressSummaryProps {
  progress: {
    collected_fields: number;
    total_fields: number;
    cv_uploaded: boolean;
    cv_processed: boolean;
    cv_score?: number;
    status: string;
  };
}

export const ProgressSummary: React.FC<ProgressSummaryProps> = ({
  progress,
}) => {
  const progressPercentage =
    progress.total_fields > 0
      ? Math.round((progress.collected_fields / progress.total_fields) * 100)
      : 0;

  return (
    <div className="border-b border-blue-200 bg-blue-50 p-4">
      <div className="mb-2 flex items-center justify-between">
        <h4 className="text-sm font-medium text-blue-900">
          Application Progress
        </h4>
        <span className="text-sm text-blue-700">{progressPercentage}%</span>
      </div>

      <div className="mb-3 h-2 w-full rounded-full bg-blue-200">
        <div
          className="h-2 rounded-full bg-blue-600 transition-all duration-300"
          style={{ width: `${progressPercentage}%` }}
        />
      </div>

      <div className="grid grid-cols-2 gap-4 text-xs text-blue-700">
        <div className="flex items-center gap-2">
          {progress.cv_uploaded ? (
            <CheckCircle className="h-3 w-3 text-green-600" />
          ) : (
            <Clock className="h-3 w-3" />
          )}
          <span>CV Upload</span>
        </div>
        <div className="flex items-center gap-2">
          {progress.cv_processed ? (
            <CheckCircle className="h-3 w-3 text-green-600" />
          ) : (
            <Clock className="h-3 w-3" />
          )}
          <span>CV Analysis</span>
        </div>
      </div>

      {progress.cv_score && (
        <div className="mt-2 text-xs text-blue-700">
          CV Score: {progress.cv_score}/100
        </div>
      )}
    </div>
  );
};
