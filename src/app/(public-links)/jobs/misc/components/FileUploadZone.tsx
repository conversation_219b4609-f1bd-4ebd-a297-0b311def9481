'use client';

import React, { useRef } from 'react';
import { cn } from '@/utils';
import { Upload, FileText, CheckCircle, AlertCircle, X } from 'lucide-react';
import { UploadProgress, UploadResult } from '../hooks/useAIApplicationUpload';

interface FileUploadZoneProps {
  isUploading: boolean;
  uploadProgress: UploadProgress;
  uploadedFile: File | null;
  uploadResult: UploadResult | null;
  onFileSelect: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onFileDrop: (event: React.DragEvent<HTMLElement>) => void;
  onDragOver: (event: React.DragEvent<HTMLElement>) => void;
  onDragEnter: (event: React.DragEvent<HTMLElement>) => void;
  onReset?: () => void;
  disabled?: boolean;
  className?: string;
}

export const FileUploadZone: React.FC<FileUploadZoneProps> = ({
  isUploading,
  uploadProgress,
  uploadedFile,
  uploadResult,
  onFileSelect,
  onFileDrop,
  onDragOver,
  onDragEnter,
  onReset,
  disabled = false,
  className
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleClick = () => {
    if (!disabled && !isUploading) {
      fileInputRef.current?.click();
    }
  };

  const handleReset = () => {
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    onReset?.();
  };

  // Show success state
  if (uploadResult?.success && uploadedFile) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="rounded-lg border-2 border-green-300 bg-green-50 p-6">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <div className="flex-1">
              <h4 className="text-sm font-medium text-green-900">
                File uploaded successfully
              </h4>
              <p className="text-sm text-green-700 mt-1">
                {uploadedFile.name} ({(uploadedFile.size / 1024 / 1024).toFixed(2)} MB)
              </p>
              {uploadResult.message && (
                <p className="text-xs text-green-600 mt-1">
                  {uploadResult.message}
                </p>
              )}
            </div>
            {onReset && (
              <button
                onClick={handleReset}
                className="p-1 text-green-600 hover:text-green-800 transition-colors"
                title="Upload different file"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (uploadResult?.error) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="rounded-lg border-2 border-red-300 bg-red-50 p-6">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
            <div className="flex-1">
              <h4 className="text-sm font-medium text-red-900">
                Upload failed
              </h4>
              <p className="text-sm text-red-700 mt-1">
                {uploadResult.error}
              </p>
              {uploadedFile && (
                <p className="text-xs text-red-600 mt-1">
                  File: {uploadedFile.name}
                </p>
              )}
            </div>
            <button
              onClick={handleReset}
              className="p-1 text-red-600 hover:text-red-800 transition-colors"
              title="Try again"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
        
        {/* Show upload zone again for retry */}
        <div
          onClick={handleClick}
          onDrop={onFileDrop}
          onDragOver={onDragOver}
          onDragEnter={onDragEnter}
          className={cn(
            "rounded-lg border-2 border-dashed border-gray-300 p-6 text-center cursor-pointer transition-colors",
            "hover:border-primary hover:bg-primary/5",
            disabled && "opacity-50 cursor-not-allowed"
          )}
        >
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
            <Upload className="h-6 w-6 text-gray-600" />
          </div>
          <p className="text-sm font-medium text-gray-900 mb-1">
            Click to upload or drag and drop
          </p>
          <p className="text-xs text-gray-500">
            PDF, DOC, or DOCX (max 10MB)
          </p>
        </div>
      </div>
    );
  }

  // Show uploading state
  if (isUploading) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="rounded-lg border-2 border-blue-300 bg-blue-50 p-6">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
              <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            </div>
            <div className="flex-1">
              <h4 className="text-sm font-medium text-blue-900">
                Uploading file...
              </h4>
              {uploadedFile && (
                <p className="text-sm text-blue-700 mt-1">
                  {uploadedFile.name}
                </p>
              )}
              
              {/* Progress Bar */}
              <div className="mt-3">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs text-blue-700">Progress</span>
                  <span className="text-xs text-blue-700">{uploadProgress.percentage}%</span>
                </div>
                <div className="w-full bg-blue-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress.percentage}%` }}
                  />
                </div>
                {uploadProgress.total > 0 && (
                  <div className="text-xs text-blue-600 mt-1">
                    {(uploadProgress.loaded / 1024 / 1024).toFixed(2)} MB of {(uploadProgress.total / 1024 / 1024).toFixed(2)} MB
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show default upload zone
  return (
    <div className={cn("space-y-4", className)}>
      <input
        ref={fileInputRef}
        type="file"
        accept=".pdf,.doc,.docx"
        onChange={onFileSelect}
        className="hidden"
        disabled={disabled}
      />
      
      <div
        onClick={handleClick}
        onDrop={onFileDrop}
        onDragOver={onDragOver}
        onDragEnter={onDragEnter}
        className={cn(
          "rounded-lg border-2 border-dashed border-gray-300 p-6 text-center cursor-pointer transition-colors",
          "hover:border-primary hover:bg-primary/5",
          disabled && "opacity-50 cursor-not-allowed"
        )}
      >
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
          <Upload className="h-6 w-6 text-gray-600" />
        </div>
        <p className="text-sm font-medium text-gray-900 mb-1">
          Click to upload or drag and drop
        </p>
        <p className="text-xs text-gray-500">
          PDF, DOC, or DOCX (max 10MB)
        </p>
      </div>

      {/* File type info */}
      <div className="text-xs text-gray-500 text-center">
        Supported formats: PDF, Microsoft Word (.doc, .docx)
      </div>
    </div>
  );
};

// Compact File Upload Component for inline use
interface CompactFileUploadProps {
  uploadedFile: File | null;
  isUploading: boolean;
  uploadResult: UploadResult | null;
  onFileSelect: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onReset?: () => void;
  disabled?: boolean;
}

export const CompactFileUpload: React.FC<CompactFileUploadProps> = ({
  uploadedFile,
  isUploading,
  uploadResult,
  onFileSelect,
  onReset,
  disabled = false
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleClick = () => {
    if (!disabled && !isUploading) {
      fileInputRef.current?.click();
    }
  };

  return (
    <div className="flex items-center gap-3">
      <input
        ref={fileInputRef}
        type="file"
        accept=".pdf,.doc,.docx"
        onChange={onFileSelect}
        className="hidden"
        disabled={disabled}
      />
      
      <div className="flex items-center gap-2 flex-1">
        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100">
          {isUploading ? (
            <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
          ) : uploadResult?.success ? (
            <CheckCircle className="h-4 w-4 text-green-600" />
          ) : uploadResult?.error ? (
            <AlertCircle className="h-4 w-4 text-red-600" />
          ) : (
            <FileText className="h-4 w-4 text-gray-600" />
          )}
        </div>
        
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-900 truncate">
            {uploadedFile ? uploadedFile.name : 'No file selected'}
          </p>
          {uploadResult?.error && (
            <p className="text-xs text-red-600 truncate">
              {uploadResult.error}
            </p>
          )}
          {uploadResult?.success && uploadResult.message && (
            <p className="text-xs text-green-600 truncate">
              {uploadResult.message}
            </p>
          )}
        </div>
      </div>

      <button
        onClick={handleClick}
        disabled={disabled || isUploading}
        className={cn(
          "px-3 py-1 text-xs font-medium rounded border transition-colors",
          uploadedFile 
            ? "border-gray-300 text-gray-700 hover:bg-gray-50"
            : "border-primary text-primary hover:bg-primary/5",
          (disabled || isUploading) && "opacity-50 cursor-not-allowed"
        )}
      >
        {uploadedFile ? 'Change' : 'Upload'}
      </button>

      {uploadedFile && onReset && (
        <button
          onClick={onReset}
          className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
          title="Remove file"
        >
          <X className="h-4 w-4" />
        </button>
      )}
    </div>
  );
};
