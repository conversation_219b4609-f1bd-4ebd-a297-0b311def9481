import React, { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { Button, Checkbox, Drawer, DrawerContent, DrawerTrigger, FilterDropdown } from '@/components/shared';
import { Filter, Search } from '@/components/shared/icons';
import { cn } from '@/utils';
import FilterOptions from './FilterOptions';

interface FilterProps {
    filterUrl: string
    setFilterUrl: Dispatch<SetStateAction<string>>;
    searchParams: string
    setSearchParams: Dispatch<SetStateAction<string>>;
    draft?: boolean
}

const FilterJobList: React.FC<FilterProps> = ({ filterUrl, setFilterUrl, searchParams, setSearchParams }) => {
    const urlSearchParams = new URLSearchParams(filterUrl);
    const { handleSubmit, register, setValue, getValues, control } = useForm({
        defaultValues: {}
    });


    const [selectedExperiences, setSelectedExperiences] = useState<string[]>([]);
    const [selectedJobTypes, setSelectedJobTypes] = useState<string[]>([]);
    const [selectedWorkModes, setSelectedWorkModes] = useState<string[]>([]);
    const [selectedProficiencyLevels, setSelectedProficiencyLevels] = useState<string[]>([]);
    const [selectedStatus, setSelectedStatus] = useState<"OPEN" | "CLOSED" | null>(null)

    const submitHandler = () => {
        const experience = selectedExperiences.length > 0 ? `work_experience=${selectedExperiences.join('&work_experience=')}` : "";
        const proficiency = selectedProficiencyLevels.length > 0 ? `proficiency_level=${selectedProficiencyLevels.join('&proficiency_level=')}` : "";
        const jobType = selectedJobTypes.length > 0 ? `job_type=${selectedJobTypes.join('&job_type=')}` : "";
        const workMode = selectedWorkModes.length > 0 ? `working_option=${selectedWorkModes.join('&working_option=')}` : "";
        const status = selectedStatus !== null ? `job_status=${selectedStatus}` : ""
        const jointString = '&' + [experience, proficiency, jobType, workMode].filter((item) => item.length > 0).join('&') + `${status && status}`;


        setFilterUrl(jointString);
    };
    const reset = () => {
        setSelectedExperiences([]);
        setSelectedJobTypes([]);
        setSelectedWorkModes([]);
        setSelectedProficiencyLevels([]);
        setFilterUrl('');
        setSearchParams('');
    }








    return (
        <form onSubmit={handleSubmit(submitHandler)} className='flex items-center justify-between flex-wrap gap-4 py-6'>

            <section className='flex items-center  gap-2'>

                <div className='relative ml-auto'>
                    <Search className="absolute right-[5%] top-[25%]" />
                    <input
                        type="search"
                        placeholder="Search"
                        value={searchParams}
                        onChange={(e) => setSearchParams(e.target.value)}
                        className="px-2.5 py-2 sm:px-4 border-[1.75px] border-[#D6D6D6] rounded-lg text-xs md:text-[0.9rem] focus:border-black focus:outline-none transition-all"
                    />
                </div>

                <div className='flex items-center gap-2'>
                    <FilterOptions
                        selectedExperiences={selectedExperiences}
                        setSelectedExperiences={setSelectedExperiences}
                        selectedJobTypes={selectedJobTypes}
                        setSelectedJobTypes={setSelectedJobTypes}
                        selectedWorkModes={selectedWorkModes}
                        setSelectedWorkModes={setSelectedWorkModes}
                        selectedProficiencyLevels={selectedProficiencyLevels}
                        setSelectedProficiencyLevels={setSelectedProficiencyLevels}
                        selectedStatus={selectedStatus}
                        setSelectedStatus={setSelectedStatus}
                    />
                </div>
            </section>


            <div className="hidden lg:flex items-center gap-2.5 ml-auto">

                <Button type="submit" variant='unstyled' size='thin' className='py-2 px-[1.625rem] text-xs text-center justify-center min-w-max text-white hover:text-black bg-black hover:bg-black/20 border border-transparent !rounded-full cursor-pointer transition-colors duration-300'>
                    Search
                </Button>
                <Button type="submit" variant='unstyled' size='thin' className='py-2 px-[1.625rem] text-xs text-center justify-center min-w-max text-black bg-white hover:bg-danger-light-active !rounded-full cursor-pointer transition-colors duration-300'
                    onClick={reset}
                >
                    <Filter width={14} height={14} />
                    Clear
                </Button>
            </div>
        </form>
    );
};

export default FilterJobList;
