'use client';

import { User } from 'iconsax-react';
import Link from 'next/link';
import { useParams, usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import {
  Avatar,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/shared';
import { CaretDown, Settings } from '@/components/shared/icons';
import { useUser } from '@/lib/contexts/UserContext';
import { useNonJobFetchJobDetail } from '../../api/jobdetails';
import LinkArrow from '../../icons/LinkArrow';
import AIApplicationModal from '../AIApplicationModal';

const JobMediaHeader = () => {
  const [active, setActive] = useState(2);
  const { user, userInfo, isLoading: isLoadingUserData } = useUser();
  const [validSession, setValidSession] = useState(true);
  const [isAIModalOpen, setIsAIModalOpen] = useState(false);

  useEffect(() => {
    setActive(2);
  }, []);
  const router = useRouter();
  const params: any = useParams();
  const { data, isLoading } = useNonJobFetchJobDetail(
    params?.id,
    params?.id !== undefined
  );

  useEffect(() => {
    setActive(2);
  }, []);
  const copyJobLink = (link: string) => {
    if (typeof navigator !== undefined) {
      navigator.clipboard.writeText(link);
      toast.success('Job link copied successfully');
    }
  };
  const [jobURL, setjobURL] = useState(false);
  const pathName = usePathname();
  useEffect(() => {
    const isApplyPage = window.location.href.includes('apply');
    const isMoreJobListsPage = window.location.href.includes('more-joblists');
    const isJobListsPage = pathName === '/jobs';
    // Set jobURL to true if either "apply" or "more-joblists" is present in the URL
    setjobURL(isApplyPage || isMoreJobListsPage || isJobListsPage);
  }, [window.location.href]);

  return (
    <div
      className={`flex flex-col items-center justify-between border-b-[0.2px]  border-[#F0EDFE] px-6 py-4 md:mb-6 md:flex-row md:border-none md:px-[2.12rem] md:py-0 ${
        !jobURL ? 'h-[8rem]  ' : ''
      } shrink-0 bg-white md:h-full md:rounded-[0.625rem]`}
    >
      <div className="  w-full flex-col items-center justify-between md:flex md:flex-row">
        <Link href="/">
          <h2 className="text-2xl font-bold text-black">Getlinked.AI</h2>
        </Link>
        <div className="hidden items-center gap-x-[1.31rem] md:flex  ">
          <button className="text-[0.875rem] ">About</button>
          {!isLoadingUserData && user && userInfo && validSession ? (
            <Popover>
              <PopoverTrigger className="flex items-center justify-center gap-1">
                <Avatar
                  src={userInfo?.profile_picture}
                  alt={`${user?.first_name} ${user?.last_name} profile picture`}
                  fallback={`${user?.first_name?.slice(
                    0,
                    1
                  )}${user?.last_name?.slice(0, 1)}`}
                  fallbackClass="bg-black text-white"
                />
                <CaretDown fill="black" />
              </PopoverTrigger>

              <PopoverContent
                align="end"
                className="z-50 mt-4 flex !max-h-96 w-max flex-col gap-2 rounded-lg bg-white/70 p-1.5 text-[0.895rem]  text-[#272727] shadow-md "
              >
                <Link
                  href={
                    user?.user_type == 'TALENT'
                      ? '/t/dashboard'
                      : '/e/dashboard'
                  }
                  className="flex w-full items-center gap-2 rounded-lg bg-white/70 py-2 pl-3 pr-12 text-sm hover:bg-white hover:text-[#212121]"
                >
                  <User className="h-5 w-5 text-black" aria-hidden="true" />
                  Dashboard
                </Link>
                <Link
                  href={
                    user?.user_type == 'TALENT' ? '/t/profile' : '/e/profile'
                  }
                  className="flex w-full items-center gap-2 rounded-lg bg-white/70 py-2 pl-3 pr-12 text-sm hover:bg-white hover:text-[#212121]"
                >
                  <Settings
                    fill="black"
                    className="h-5 w-5"
                    aria-hidden="true"
                  />
                  Profile
                </Link>
              </PopoverContent>
            </Popover>
          ) : (
            <div className="flex h-[3rem] shrink-0 items-center gap-x-1 rounded-[3.125rem] bg-[#F8F9FB] p-1">
              <Link href="/login/talent">
                {' '}
                <button
                  className={`flex h-[2.5rem] w-[6.875rem] shrink-0 items-center justify-between rounded-[2.5rem]  px-4 text-[0.75rem] font-medium ${
                    active === 1 ? 'bg-primary' : 'bg-white'
                  }`}
                  onClick={() => setActive(1)}
                >
                  Signin <LinkArrow fill={active === 1 ? '#ffff' : '#0E0E2C'} />{' '}
                </button>
              </Link>
              <Link href={'/register/talent'}>
                <button
                  className={`flex h-[2.5rem] w-[6.875rem] shrink-0 items-center justify-between rounded-[2.5rem] px-4 text-[0.75rem] font-medium ${
                    active === 2 ? 'bg-black text-white' : 'bg-white text-black'
                  }`}
                  onClick={() => setActive(2)}
                >
                  Signup <LinkArrow fill={active === 2 ? '#fff' : '#0E0E2C'} />
                </button>
              </Link>
            </div>
          )}
        </div>
      </div>

      {!jobURL && (
        <div className="w-full py-2 md:hidden md:py-4">
          <div className=" flex w-full items-center gap-x-3">
            <button
              className="flex shrink-0 items-center gap-x-8 rounded-full border-2 border-black bg-white px-6 py-[0.45rem] text-[0.8125rem] font-medium text-black transition-all hover:border-black  hover:bg-black hover:text-white sm:text-[0.875rem]"
              onClick={() =>
                copyJobLink(`https://app.getlinked.ai/jobs/${params}/`)
              }
            >
              Copy link
            </button>

            {data?.configuration ? (
              <button
                onClick={() => setIsAIModalOpen(true)}
                className="group/button flex h-[2.75rem] shrink-0 items-center gap-x-8 rounded-[2.5rem] border-2 border-transparent bg-black px-4 py-[0.81rem] text-[0.875rem] font-medium text-white  transition-all hover:border-black hover:bg-white hover:text-black"
              >
                Apply for this role
                <LinkArrow className="fill-white group-hover/button:!fill-black" />
              </button>
            ) : (
              <Link href={`/jobs/${data?.unique_id}/apply`}>
                <button className="group/button flex shrink-0 items-center gap-x-8 rounded-full border-2 border-transparent bg-black px-4 py-[0.45rem] text-[0.8125rem] font-medium text-white transition-all  hover:border-black hover:bg-white hover:text-black sm:text-[0.875rem]">
                  <span className="flex">
                    Apply
                    <span className="max-md:hidden">for this role</span>
                  </span>
                  <LinkArrow className="fill-white group-hover/button:!fill-black" />
                </button>
              </Link>
            )}
          </div>
        </div>
      )}

      {/* AI Application Modal */}
      <AIApplicationModal
        isOpen={isAIModalOpen}
        onClose={() => setIsAIModalOpen(false)}
        jobData={data}
        applicantEmail={null}
        onEmailSubmit={() => {}}
      />
    </div>
  );
};

export default JobMediaHeader;
