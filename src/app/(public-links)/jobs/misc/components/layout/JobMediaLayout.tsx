'ue client'
import React, { ReactNode, useEffect, useState } from 'react'
import BannerImages from '../../icons/BannerImages'
import JobDetailSideBar from '../JobDetailSideBar'
import { useParams } from 'next/navigation'
import { useNonJobFetchJobDetail } from '../../api/jobdetails'
import { cn } from '@/utils'

interface props {
    children: ReactNode
}
const JobMediaLayout = ({ children }: props) => {

    const params: any = useParams()

    const { data: job, isLoading } = useNonJobFetchJobDetail(params?.id, params?.id !== undefined)
    const [publicUrl, setPublicUrl] = useState(false)
    useEffect(() => {
        if (window.location.href.indexOf("apply") != -1) {
            setPublicUrl(true)
        } else {
            setPublicUrl(false)
        }

    }, [])
    return (
        <div className='w-full  relative '>
            <div className={`w-full h-full  flex flex-col  ${publicUrl ? "" : ""}`}>
                <div className={`${publicUrl ? "my-2 h-[1.2rem]" : "my-2 h-[1rem]  "}  md:my-4  md:h-[9.6875rem] w-full bg-white rounded-[1.25rem] `}>
                    <div className="flex items-center justify-end md:pr-[2.12rem] h-full relative ">
                        <div className={`left-[1.5rem] w-[4rem] h-[4rem] md:h-[6.35rem] md:w-[6.25rem] rounded-full bg-blue-900 md:left-[3.5rem] absolute bottom-[-3.3rem] md:bottom-[-3rem]`}>
                            <img src={job?.logo || job?.company?.logo || ""} alt={job?.company?.logo ?? "Logo"} className=' text-[8px] w-full h-full rounded-full' />
                        </div>
                        <div className="hidden  md:block absolute bottom-0">
                            <BannerImages />
                        </div>
                    </div>
                </div>
                <div className={`flex-1 bg-white flex flex-col-reverse lg:flex-row py-[3rem]  ${publicUrl && "px-5"} px-5 md:max-xl:px-10 md:gap-4 xl:px-[3rem] lg:pr-[2rem]`}>
                    <div className="flex-1 md:overflow-y-scroll talent-scroll">{children}</div>
                    <div className={cn("w-full  inline-block  lg:w-[19rem] xl:w-[20.25rem]", job?.job_status === "CLOSED" && "!hidden")}>
                        <JobDetailSideBar />
                    </div>
                </div>
            </div>
        </div>
    )
}

export default JobMediaLayout