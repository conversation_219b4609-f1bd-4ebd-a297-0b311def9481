'use client';

import { cn } from '@/utils';
import { Dialog, Transition } from '@headlessui/react';
import { Calendar, Clock, Globe, Video, X } from 'lucide-react';
import React, { Fragment, useState } from 'react';

interface InterviewSchedulingModalProps {
  isOpen: boolean;
  onClose: () => void;
  jobTitle?: string;
  onScheduleConfirm?: (selectedDate: string, selectedTime: string) => void;
}

const InterviewSchedulingModal: React.FC<InterviewSchedulingModalProps> = ({
  isOpen,
  onClose,
  jobTitle = "Product Designer",
  onScheduleConfirm
}) => {
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [currentMonth, setCurrentMonth] = useState(new Date());

  // Generate calendar days for the current month
  const generateCalendarDays = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const days = [];
    const currentDate = new Date(startDate);

    for (let i = 0; i < 42; i++) {
      days.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return days;
  };

  const timeSlots = [
    '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
    '12:00', '12:30', '01:00', '01:30', '02:00', '02:30', '03:00', '03:30'
  ];

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const weekDays = ['M', 'T', 'W', 'T', 'F', 'S', 'S'];

  const handleDateSelect = (date: Date) => {
    const dateStr = date.toISOString().split('T')[0];
    setSelectedDate(dateStr);
  };

  const handleTimeSelect = (time: string) => {
    setSelectedTime(time);
  };

  const handleSchedule = () => {
    if (selectedDate && selectedTime) {
      onScheduleConfirm?.(selectedDate, selectedTime);
      onClose();
    }
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newMonth = new Date(currentMonth);
    if (direction === 'prev') {
      newMonth.setMonth(newMonth.getMonth() - 1);
    } else {
      newMonth.setMonth(newMonth.getMonth() + 1);
    }
    setCurrentMonth(newMonth);
  };

  const calendarDays = generateCalendarDays();
  const today = new Date();
  const isCurrentMonth = (date: Date) => date.getMonth() === currentMonth.getMonth();

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-[9999]" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white shadow-xl transition-all">
                {/* Header */}
                <div className="bg-primary px-6 py-4 text-white">
                  <div className="flex items-center justify-between">
                    <Dialog.Title className="text-lg font-medium">
                      Booking an introduction meeting
                    </Dialog.Title>
                    <button
                      onClick={onClose}
                      className="text-white/80 hover:text-white"
                    >
                      <X className="h-6 w-6" />
                    </button>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6 space-y-6">
                  {/* Meeting Info */}
                  <div className="border-2 border-dashed border-gray-200 rounded-lg p-4">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                        <Calendar className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">
                          Introduction meeting with LibertyTech
                        </h3>
                      </div>
                    </div>

                    <div className="space-y-2 text-sm text-gray-600">
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4" />
                        <span>1hr 30mins</span>
                        <Globe className="w-4 h-4 ml-4" />
                        <span>West Africa Time (5:30pm)</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Video className="w-4 h-4" />
                        <span>Meeting details will be provided upon confirmation</span>
                      </div>
                      <div className="bg-blue-50 p-2 rounded text-primary text-xs">
                        10:00 - 11:30, Tuesday, September 10, 2024.
                      </div>
                    </div>

                    <p className="text-xs text-gray-500 mt-3">
                      Please select a date and time for your interview with the LibertyTech design team. Available slots are shown below. Once confirmed, you'll receive an email with the interview details and a link to join the video call.
                    </p>
                  </div>

                  {/* Date Selection */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">
                      Choose preferred date for your interview.
                    </h4>

                    {/* Calendar Header */}
                    <div className="flex items-center justify-between mb-4">
                      <button
                        onClick={() => navigateMonth('prev')}
                        className="p-1 hover:bg-gray-100 rounded"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                        </svg>
                      </button>
                      <h3 className="font-medium">
                        {monthNames[currentMonth.getMonth()]}
                      </h3>
                      <button
                        onClick={() => navigateMonth('next')}
                        className="p-1 hover:bg-gray-100 rounded"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </button>
                    </div>

                    {/* Calendar Grid */}
                    <div className="grid grid-cols-7 gap-1 mb-4">
                      {weekDays.map((day) => (
                        <div key={day} className="text-center text-xs font-medium text-gray-500 py-2">
                          {day}
                        </div>
                      ))}
                      {calendarDays.map((date, index) => {
                        const dateStr = date.toISOString().split('T')[0];
                        const isSelected = selectedDate === dateStr;
                        const isToday = date.toDateString() === today.toDateString();
                        const isCurrentMonthDate = isCurrentMonth(date);
                        const isPast = date < today && !isToday;

                        return (
                          <button
                            key={index}
                            onClick={() => !isPast && handleDateSelect(date)}
                            disabled={isPast}
                            className={cn(
                              "h-8 w-8 text-sm rounded-full transition-colors",
                              isSelected && "bg-primary text-white",
                              !isSelected && isToday && "bg-blue-100 text-blue-600",
                              !isSelected && !isToday && isCurrentMonthDate && "hover:bg-gray-100",
                              !isCurrentMonthDate && "text-gray-300",
                              isPast && "text-gray-300 cursor-not-allowed"
                            )}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>

                  {/* Time Zone */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Time Zone
                    </label>
                    <select className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm">
                      <option>West Africa Time (5:30pm)</option>
                    </select>
                  </div>

                  {/* Time Selection */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">
                      Choose preferred time for your interview.
                    </h4>
                    <div className="grid grid-cols-4 gap-2">
                      {timeSlots.map((time) => (
                        <button
                          key={time}
                          onClick={() => handleTimeSelect(time)}
                          className={cn(
                            "px-3 py-2 text-sm rounded-lg border transition-colors",
                            selectedTime === time
                              ? "bg-primary text-white border-primary"
                              : "border-gray-300 hover:border-primary hover:bg-primary/5"
                          )}
                        >
                          {time}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-3 pt-4">
                    <button
                      onClick={onClose}
                      className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleSchedule}
                      disabled={!selectedDate || !selectedTime}
                      className="flex-1 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Schedule Interview
                    </button>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

export default InterviewSchedulingModal;
