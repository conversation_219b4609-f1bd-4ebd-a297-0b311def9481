'use client';

import { cn } from '@/utils';
import { addCommasToNumber } from '@/utils/numbers';
import { convertKebabAndSnakeToTitleCase } from '@/utils/strings';
import Link from 'next/link';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useNonJobFetchJobDetail } from '../api/jobdetails';
import ExperienceIcon from '../icons/ExperrienceIcon';
import IndustryIcon from '../icons/IndustryIcon';
import JobTypeicon from '../icons/JobTypeIcon';
import LinkArrow from '../icons/LinkArrow';
import SalaryIcons from '../icons/SalaryIcon';
import { isValidEmail } from '../utils/userContext';
import AIApplicationModal from './AIApplicationModal';

interface viewListProps {
  companyName: string;
  logo: string;
  location: string;
  time: string;
}

const JobDetailSideBar = () => {
  const params: any = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [publicUrl, setPublicUrl] = useState(false);
  const [isAIModalOpen, setIsAIModalOpen] = useState(false);
  const [applicantEmail, setApplicantEmail] = useState<string | null>(null);

  const { data, isLoading } = useNonJobFetchJobDetail(params?.id, true);

  // Read applicant_email from URL query parameters
  useEffect(() => {
    const emailParam = searchParams.get('applicant_email');
    if (emailParam && isValidEmail(emailParam)) {
      setApplicantEmail(emailParam);
      // Auto-open the AI chat sidebar when a valid email is present in URL
      setIsAIModalOpen(true);
    }
  }, [searchParams]);

  // Update URL when modal is opened/closed
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);

      if (isAIModalOpen && applicantEmail) {
        // Add applicant_email parameter when modal is open and we have an email
        url.searchParams.set('applicant_email', applicantEmail);
      } else {
        // Remove applicant_email parameter when modal is closed
        url.searchParams.delete('applicant_email');
      }

      // Update URL without refreshing the page
      router.replace(url.pathname + url.search, { scroll: false });
    }
  }, [isAIModalOpen, applicantEmail, router]);

  useEffect(() => {
    if (window.location.href.indexOf('apply') != -1) {
      setPublicUrl(true);
    } else {
      setPublicUrl(false);
    }
  }, []);

  const copyJobLink = (link: string) => {
    if (typeof navigator !== undefined) {
      navigator.clipboard.writeText(link);
      toast.success('Job link copied successfully');
    }
  };

  // Handle modal close - will trigger URL update via the useEffect
  const handleModalClose = () => {
    setIsAIModalOpen(false);
    setApplicantEmail(null);
  };

  // Handle email submission from the modal - update URL parameter
  const handleEmailSubmit = (email: string) => {
    if (isValidEmail(email)) {
      setApplicantEmail(email);
    }
  };

  return (
    <div className={cn(data?.job_status === 'CLOSED' && '!hidden')}>

      {!publicUrl && (
        <div className="hidden items-center justify-end  gap-x-[0.81rem] md:flex">
          <button
            className="px-4  text-[0.875rem] font-medium hover:text-primary"
            onClick={() =>
              copyJobLink(`https://app.getlinked.ai/jobs/${params}/`)
            }
          >
            Share
          </button>
          {data?.configuration ? (
            <button
              onClick={() => setIsAIModalOpen(true)}
              className="group/button flex h-[2.75rem] shrink-0 items-center gap-x-8 rounded-[2.5rem] border-2 border-transparent bg-black px-4 py-[0.81rem] text-[0.875rem] font-medium text-white  transition-all hover:border-black hover:bg-white hover:text-black"
            >
              Apply for this role
              <LinkArrow className="fill-white group-hover/button:!fill-black" />
            </button>
          ) : (
            <Link href={`/jobs/${data?.unique_id}/apply`}>
              <button
                onClick={() => router?.push(`/jobs/${data?.unique_id}/apply`)}
                className="group/button flex h-[2.75rem] shrink-0 items-center gap-x-8 rounded-[2.5rem] border-2 border-transparent bg-black px-4 py-[0.81rem] text-[0.875rem] font-medium text-white  transition-all hover:border-black hover:bg-white hover:text-black"
              >
                Apply for this role{' '}
                <LinkArrow className="fill-white group-hover/button:!fill-black" />
              </button>
            </Link>
          )}
        </div>
      )}
      <div
        className={`w-full shrink-0 rounded-[0.625rem] bg-[#FBFAFF] p-6  pb-12 ${
          !publicUrl && 'mt-4 md:mt-[3rem]'
        }`}
      >
        <div className="flex gap-x-[1.05rem]">
          <div className="flex h-[2.3125rem] w-[2.3125rem] shrink-0 items-center justify-center rounded-full border-[0.01px] border-[#8C8CA1]">
            <SalaryIcons />
          </div>
          {data?.salary_type === 'RANGE' ? (
            <div className="">
              <h2 className="text-[1rem] font-semibold text-[#0E0E2C]">
                {' '}
                {data?.salary_currency}
                {addCommasToNumber(data?.min_salary ?? 0)} -{' '}
                {data?.salary_currency}
                {addCommasToNumber(data?.max_salary ?? 0)}
              </h2>
              <p className="text-sm text-[#8C8CA1]">Salary range</p>
            </div>
          ) : data?.salary_type === 'FIXED' ? (
            <div className="">
              <h2 className="text-[1rem] font-semibold text-[#0E0E2C]">
                {data?.salary_currency}
                {addCommasToNumber(data?.fixed_salary ?? 0)}
              </h2>
              <p className="text-sm text-[#8C8CA1]">Fixed amount</p>
            </div>
          ) : (
            <div className="">
              <h2 className="text-[1rem] font-semibold text-[#0E0E2C]">
                Undisclosed
              </h2>
              <p className="text-sm text-[#8C8CA1]">Salary</p>
            </div>
          )}
        </div>
        {data?.work_experience && (
          <div className="mt-4 flex gap-x-[1.05rem]">
            <div className="flex h-[2.3125rem] w-[2.3125rem] shrink-0 items-center justify-center rounded-full border-[0.01px] border-[#8C8CA1]">
              <ExperienceIcon />
            </div>
            <div className="">
              <h2 className="text-[1rem] font-semibold text-[#0E0E2C]">
                {data?.work_experience}{' '}
              </h2>
              <p className="text-sm text-[#8C8CA1]">Experience</p>
            </div>
          </div>
        )}
        {data?.job_type && (
          <div className="mt-4 flex gap-x-[1.05rem]">
            <div className="flex h-[2.3125rem] w-[2.3125rem] shrink-0 items-center justify-center rounded-full border-[0.01px] border-[#8C8CA1]">
              <JobTypeicon />
            </div>
            <div className="">
              <h2 className="text-[1rem] font-semibold text-[#0E0E2C]">
                {convertKebabAndSnakeToTitleCase(data?.job_type)}
              </h2>
              <p className="text-sm text-[#8C8CA1]">Employment type</p>
            </div>
          </div>
        )}

        {data?.industry && (
          <div className="mt-4 flex gap-x-[1.05rem]">
            <div className="flex h-[2.3125rem] w-[2.3125rem] shrink-0 items-center justify-center rounded-full border-[0.01px] border-[#8C8CA1]">
              <IndustryIcon />
            </div>
            <div className="">
              <h2 className="text-[1rem] font-semibold text-[#0E0E2C]">
                {data?.industry}
              </h2>
              <p className="text-sm text-[#8C8CA1]">Industry</p>
            </div>
          </div>
        )}
      </div>

      {/* <div className="bg-[#FBFAFF] rounded-[0.625rem] w-full shrink-0 p-6 mt-[1.5rem] pb-12">
                <p className='text-base text-[#0E0E2C] font-medium'>People also viewed</p>
                <div className="">
                    {viewList?.map((list, idx: number) => (
                        <div className="mt-[1.63rem]" key={idx}>
                            <p className='text-[1.125rem] text-[#0E0E2C] font-medium'>{list?.companyName}</p>
                            <div className="flex items-center gap-x-[0.05rem]">
                                <div className="h-[1.7rem] w-[1.7rem] rounded-full shrink-0">
                                    <img src={list?.logo} alt={list?.logo} className='w-full h-full rounded-full' />
                                </div>
                                <div className="flex items-center gap-x-1 text-[0.83rem] text-[#4A4A68]">
                                    <p>{list?.location} {list?.time}</p>

                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div> */}

      {/* AI Application Modal */}
      <AIApplicationModal
        isOpen={isAIModalOpen}
        onClose={handleModalClose}
        jobData={data}
        applicantEmail={applicantEmail}
        onEmailSubmit={handleEmailSubmit}
      />
    </div>
  );
};

export default JobDetailSideBar;
