export interface coverLetteristTypes {
  page: null;
  next: null;
  previous: null;
  count: number;
  results: Results;
}

export interface Results {
  cover_letters: CoverLetter[];
}

export interface CoverLetter {
  id: number;
  talent_full_name: string;
  talent_email: string;
  talent_street: string;
  talent_city: string;
  talent_state: string;
  talent_country: string;
  talent_phone_number: string;
  recipient_full_name: string;
  recipient_email: string;
  recipient_street: string;
  recipient_city: string;
  recipient_state: string;
  recipient_country: string;
  recipient_phone_number: string;
  company_name: null;
  cover_letter_date: Date;
  cover_letter_subject: string;
  cover_letter_body: string;
  selected_template_to_cover: string;
  gpt_cover_letter_body: null;
  created_at: Date;
  updated_at2: Date;
  talent: string;
}
