import { projectsProps } from '@/app/(website-main)/t/showcase/misc/store';

export interface ResumeTypes {
  page: null;
  count: number;
  results: Results;
  next: null;
  previous: null;
  message: string;
}

export interface Results {
  cv_data: CvDatum[];
  resume_creation: ResumeCreation[];
}

export interface CvDatum {
  Name?: string;
  cv_data_id: string;
  Address?: string;
  Email?: string;
  Phone?: string;
  LinkedIn?: string;
  personal_website?: string;
  portfolio_website?: string;
  github?: null | string;
  behance?: null | string;
  Interests?: string[];
  Objective?: string;
  Education: Education[];
  WorkExperience: WorkExperience[] | any;
  Skills: Skill[];
  Achievements?: string[];
  Certifications: Array<CertificationClass>;
  Languages: Language[] | any;
  ExtraCurricularActivities?: any[];
  PersonalDetails?: PersonalDetails;
  References?: string;
  Score?: string;
  Contact_Information?: ContactInformation;
  Summary_or_Objective?: SummaryOrObjective;
  Professional_Certifications?: ProfessionalCertifications;
  Projects?: projectsProps[] | any;
  Awards_and_Honors: Array<AwardsAndHonorClass>;
  Professional_Memberships?: any[];
  Additional_Comments?: string;
  Dribble?: null | string;
  OtherLinks?: null | string;
  cv_template?: number;
    cv_background_color?: string | null;
    cv_header_text_color?: string;
    cv_body_text_color?: string;
    cv_right_header_text_color?: string;
    cv_right_body_text_color?: string;
    cv_header_font: string;
    cv_body_font: string;
    cv_header_font_size: string;
    cv_header_body_size: string;
    
  
}

export interface AwardsAndHonorClass {
  award: string;
  year: number;
  description: string;
}

export interface CertificationClass {
  organization: string;
  certification: string;
  date_earned: string;
}

export interface ContactInformation {
  'Full Name'?: string;
  'Phone Number'?: string;
  'Email Address'?: string;
  Location: string;
  is_present: boolean;
  recommendation: string;
  Full_Name?: string;
  Phone_Number?: string;
  Email_Address?: string;
}

export interface Education {
  school: string;
  location: string;
  degree: string;
  field_of_study: string;
  start_year?: string;
  end_year?: string;
  start_date?: string;
  end_date?: string;
}

export interface Language {
  Language: string;
  'Proficiency Level'?: ProficiencyLevel;
  Proficiency_Level?: string;
}

export enum ProficiencyLevel {
  Basic = 'Basic',
  Fluent = 'Fluent',
  Native = 'Native',
}

export interface PersonalDetails {
  Fathers: string;
  DateOfBirth: string;
  MaritalStatus: string;
  Native: string;
}

export interface ProfessionalCertifications {
  Certifications: string[];
  is_present: boolean;
  recommendation: string;
}

export interface Project {
  'Project Name'?: string;
  Description: string;
  project_title?: string;
  Dates?: string;
  Project_Name?: string;
  Responsibilities_Achievements?: string[];
  start_month?: string;
  start_year?: number;
}

export interface Skill {
  'Technical Skills'?: string[];
  'Soft Skills'?: string[];
  Certifications: any[];
  Tools: string[];
  Technical_Skills?: string[];
  Soft_Skills?: string[];
  'other expertise'?: any[];
}

export interface SummaryOrObjective {
  Summary: string;
  is_present: boolean;
  recommendation: string;
}

export interface WorkExperience {
  Job_Title: string;
  Company: string;
  Dates?: string;
  Responsibilities_Achievements: string[];
  Location?: string;
  start_date?: string;
  end_date?: string;
  time_period?: boolean | string;
}

export interface ResumeCreation {
  id: number;
  job_role: string;
  work_experience_level: string;
  name: string;
  address: string;
  city: string;
  state: string;
  country: string;
  email: string;
  phone_number: string;
  linkedin_profile: string;
  github_profile: string;
  behance_profile: string;
  dribble_profile: string;
  personal_website: string;
  portfolio_website: string;
  other_links: any[];
  projects: any[];
  objective: string;
  paraphrased_objective: null;
  skills: string[];
  certifications: any[];
  awards_and_honor: any[];
  languages: any[];
  professional_associations: null;
  hobbies: any[];
  education: Education[];
  work_experience: WorkExperienceElement[];
 cv_template?: number;
    cv_background_color?: string | null;
    cv_header_text_color?: string;
    cv_body_text_color?: string;
    cv_right_header_text_color?: string;
    cv_right_body_text_color?: string;
    cv_header_font: string;
    cv_body_font: string;
    cv_header_font_size: string;
    cv_header_body_size: string;
  created_at: Date;
  updated_at: Date;
  talent: string;
}

export interface Custum {
  cv_template: number;
  cv_background_color: string;
  cv_header_text_color: string;
  cv_body_text_color: string;
  cv_header_font: string;
  cv_body_font: string;
  cv_header_font_size: string;
  cv_header_body_size: string;
}

export interface WorkExperienceElement {
  company: string;
  location: string;
  title: string;
  description: string[];
  start_date: string;
  end_date: string;
}
