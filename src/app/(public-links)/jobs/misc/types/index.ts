export interface userEmailProp {
  message: string;
  user_details: UserDetails;
  talent_details: TalentDetails;
}

export interface TalentDetails {
  talent_id: string;
  year_of_work_experience: string;
  desired_role: string;
}

export interface UserDetails {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  gender: string;
  phone_number: string;
  linkedin_link?: string;
  portfolio_link?: string;
}

// if cover_letter_text contain data then cover_letter_id and cover_letter_file validation should be ignore or not to be validated
