import { <PERSON>Job } from "@/app/(website-main)/e/jobs/misc/types";

export interface JobListTypesProps {
  page: number;
  count: number;
  next: null;
  previous: null;
  results: ActiveJob[];
}

export interface ResultTypes {
  id: number;
  unique_id: string;
  job_title: string;
  description: null | string;
  proficiency_level: ProficiencyLevel;
  working_option: WorkingOption;
  work_experience: null | string;
  job_type: JobType;
  industry: null | string;
  industry_category: null;
  job_tags: null;
  location: string;
  salary_range: string;
  min_salary: number | null;
  max_salary: number | null;
  salary_currency: null | string;
  salary_negotiable: boolean;
  responsibilities: string[];
  qualifications: null;
  skills_required: null;
  application_deadline: Date;
  application_start_date: Date;
  accpting_applications: boolean;
  benefits: string[];
  job_status: string;
  show_in_career_page: boolean;
  type_of_assessment: TypeOfAssessment;
  job_custom_fields: JobCustomField[];
  company_overview: null | string;
  job_application_requirement_custom_fields: null;
  post_as_anonymous: boolean;
  created_at: Date;
  updated_at: Date;
  requirement_custom_fields: RequirementCustomFields;
  talent: any[];
  bookmarks: Bookmark[];
  team_member: any[];
  company: Company;
  recruiter: Recruiter;
  applicant: any[];
}

export interface Bookmark {
  id: string;
  created_at: Date;
  updated_at: Date;
  address: string;
  desired_role: string;
  proficiency_level: string;
  year_of_work_experience: string;
  stack: string[];
  job_type: string;
  currently_employed: boolean;
  willing_to_relocate: boolean;
  linkedin_profile: null;
  portfolio_website: null;
  skill_objective_score: number;
  skill_objective_question_percntage: number;
  skill_theory_score: number;
  skill_theory_question_percntage: number;
  skill_base_score: number;
  personality_role_specific_score: number;
  personality_description: string;
  big5_personality_data: Big5PersonalityData;
  profile_picture: null;
  background_picture: null;
  user: string;
}

export interface Big5PersonalityData {}

export interface Company {
  id: number;
  name: Name;
  address: Address;
  website: string;
  industry: Industry;
  size: string;
  description: string;
  logo: null;
  type_of_company: TypeOfCompany;
  company_domain: CompanyDomain;
  created_at: Date;
}

export enum Address {
  No6PeaceOfMindCity = 'No 6, peace of mind city.',
  The27Alara = '27 Alara',
}

export enum CompanyDomain {
  LibertyngCOM = 'libertyng.com',
}

export enum Industry {
  Fintech = 'Fintech',
  Hr = 'HR',
}

export enum Name {
  Getlinked = 'Getlinked',
  WinWise = 'Win Wise',
}

export enum TypeOfCompany {
  Cooperate = 'COOPERATE',
}

export interface JobCustomField {
  fieldTitle: string;
  fieldValue: string;
}

export enum JobType {
  Contract = 'CONTRACT',
  FullTime = 'FULL_TIME',
  PartTime = 'PART_TIME',
}

export enum ProficiencyLevel {
  EntryLevel = 'ENTRY_LEVEL',
  Intern = 'INTERN',
  MidLevel = 'MID_LEVEL',
}

export interface Recruiter {
  id: number;
  company_email: CompanyEmail;
  verified: boolean;
  type_of_recruiter: TypeOfRecruiter;
  profile_picture: null;
  created_at: Date;
  updated_at: Date;
  user: User;
  company: Company;
}

export enum CompanyEmail {
  DavidLibertyngCOM = '<EMAIL>',
  OnikhalidaLibertyngCOM = '<EMAIL>',
}

export enum TypeOfRecruiter {
  PersonalRecruiter = 'PERSONAL_RECRUITER',
}

export interface User {
  id: string;
  first_name: FirstName;
  middle_name: null;
  last_name: LastName;
  email: Email;
  phone_number: string;
  date_of_birth: null;
  gender: Gender;
  user_type: UserType;
}

export enum Email {
  DavidLibertyngCOM = '<EMAIL>',
  OnikhalidaGmailCOM = '<EMAIL>',
}

export enum FirstName {
  David = 'David',
  Khalid = 'Khalid',
}

export enum Gender {
  M = 'M',
}

export enum LastName {
  Olaniyi = 'Olaniyi',
  Oni = 'Oni',
}

export enum UserType {
  Recruiter = 'RECRUITER',
}

export interface RequirementCustomFields {
  id: number;
  accept_personal_details: boolean;
  accept_resume: boolean;
  accept_cover_letter: boolean;
  custom_fields: CustomField[];
  created_at: Date;
  updated_at: Date;
}

export interface CustomField {
  fieldType: string;
  fieldTitle: string;
}

export enum TypeOfAssessment {
  CustomAssessment = 'CUSTOM_ASSESSMENT',
  GenericAssessment = 'GENERIC_ASSESSMENT',
}

export enum WorkingOption {
  Onsite = 'ONSITE',
}
