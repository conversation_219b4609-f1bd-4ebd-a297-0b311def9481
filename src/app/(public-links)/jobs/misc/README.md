# AI-Powered Job Application Flow

This implementation provides a complete WebSocket-based AI chat interview system
for job applications, replacing the previous static conversation flow with
real-time communication.

## 🚀 Features

- **Real-time WebSocket Communication**: Direct connection to AI chat service
- **File Upload with Progress**: CV upload with real-time progress tracking
- **Comprehensive Error Handling**: Network issues, upload failures, and
  connection problems
- **Automatic Reconnection**: Smart reconnection with exponential backoff
- **Progress Tracking**: Visual progress indicators for application completion
- **Responsive UI**: Mobile-friendly interface with proper loading states
- **Network Status Monitoring**: Offline detection and user notifications

## 📁 File Structure

```
src/app/(public-links)/jobs/misc/
├── components/
│   ├── AIApplicationFlow.tsx      # Main application flow component
│   ├── AIApplicationModal.tsx     # Modal wrapper for the application
│   ├── ChatMessage.tsx           # Chat message components and indicators
│   ├── FileUploadZone.tsx        # File upload with drag-drop support
│   └── AIApplicationTest.tsx     # Test component for validation
├── hooks/
│   ├── useAIApplicationChat.ts   # WebSocket chat management
│   ├── useAIApplicationUpload.ts # File upload handling
│   └── useAIApplicationState.ts  # Application state management
└── utils/
    └── testValidation.ts         # Test utilities and validation
```

## 🔧 API Integration

### WebSocket Connection

- **URL**: `wss://apply.getlinked.live/ws/chat/{job_uuid}`
- **Protocol**: JSON message exchange
- **Auto-reconnection**: Up to 5 attempts with exponential backoff
- **Note**: Uses job UUID (unique_id) instead of job ID

### File Upload

- **URL**:
  `https://apply.getlinked.live/api/v1/files/upload-cv/{job_uuid}/{applicant_email}`
- **Method**: POST with FormData
- **Supported formats**: PDF, DOC, DOCX
- **Size limit**: 10MB
- **Note**: Uses job UUID (unique_id) instead of job ID

## 📨 Message Types

### Client to Server

#### Start Application

```json
{
  "type": "start_application",
  "data": {
    "applicant_email": "<EMAIL>"
  }
}
```

#### Chat Message

```json
{
  "type": "chat_message",
  "data": {
    "message": "Hello, I'm interested in this position"
  }
}
```

### Server to Client

#### Welcome Message

```json
{
  "type": "welcome",
  "data": {
    "message": "Welcome! I'm here to help you apply...",
    "application_key": "<EMAIL>",
    "status": "started",
    "job_title": "Software Developer"
  },
  "timestamp": "2025-01-09T10:30:00Z"
}
```

#### AI Response

```json
{
  "type": "ai_response",
  "data": {
    "message": "Great! Could you tell me about your experience?",
    "status": "collecting_info",
    "progress": {
      "collected_fields": 2,
      "total_fields": 9,
      "missing_fields": ["experience", "skills"]
    }
  }
}
```

#### CV Upload Confirmation

```json
{
  "type": "cv_uploaded",
  "data": {
    "message": "CV uploaded successfully! Processing now...",
    "status": "cv_processing",
    "processing_started": true
  }
}
```

#### CV Processing Complete

```json
{
  "type": "cv_processed",
  "data": {
    "message": "CV analysis complete!",
    "cv_score": 85.5,
    "status": "collecting_info",
    "passed_threshold": true
  }
}
```

#### Application Complete

```json
{
  "type": "application_complete",
  "data": {
    "message": "Application completed successfully!",
    "status": "completed",
    "application_summary": {
      "application_key": "<EMAIL>",
      "job_title": "Software Developer",
      "applicant_name": "John Doe",
      "cv_score": 85.5,
      "status": "completed",
      "threshold_met": true
    }
  }
}
```

## 🎯 Usage

### Basic Implementation

```tsx
import { AIApplicationModal } from './components/AIApplicationModal';

function JobPage() {
  const [showApplication, setShowApplication] = useState(false);

  return (
    <div>
      <button onClick={() => setShowApplication(true)}>
        Apply with AI
      </button>

      <AIApplicationModal
        isOpen={showApplication}
        onClose={() => setShowApplication(false)}
        jobData={{
          id: 'job-123', // Real job ID from your database
          title: 'Software Developer',
          company_name: 'Tech Corp',
          location: 'Remote'
        }}
      />
    </div>
  );
}
```

### Custom Hook Usage

```tsx
import { useAIApplicationState } from './hooks/useAIApplicationState';

function CustomApplication() {
  const appState = useAIApplicationState({
    jobId: 'job-123', // Real job ID from your database
    // applicantEmail is optional - will be collected if not provided
    onComplete: (summary) => {
      console.log('Application completed:', summary);
    },
    onError: (error) => {
      console.error('Application error:', error);
    }
  });

  return (
    <div>
      {/* Your custom UI using appState */}
    </div>
  );
}
```

### Email Collection

The system automatically handles email collection:

1. **Authenticated Users**: If user is logged in, their email is used
   automatically
2. **URL Parameters**: Email can be passed via `?email=<EMAIL>`
3. **Session Storage**: Previously entered emails are remembered
4. **Manual Entry**: Users are prompted to enter email if not available

```tsx
// To provide email upfront (skips collection step)
const appState = useAIApplicationState({
  jobId: 'job-123',
  applicantEmail: '<EMAIL>', // Pre-filled email
  // ...
});

// To let system handle email collection automatically
const appState = useAIApplicationState({
  jobId: 'job-123',
  // No applicantEmail - system will collect it
  // ...
});
```

## 🧪 Testing

### Test Page

Visit `/jobs/test` to access the comprehensive test suite that validates:

- WebSocket connection and message handling
- File upload functionality
- Error scenarios and recovery
- Network status monitoring
- Complete application flow

### Manual Testing Checklist

1. **Connection**: Verify WebSocket connects successfully
2. **File Upload**: Test drag-drop and click upload
3. **Chat Flow**: Send messages and verify responses
4. **Error Handling**: Simulate network issues
5. **Progress Tracking**: Verify progress indicators
6. **Completion**: Test full application flow

## 🔧 Configuration

### Environment Variables

```env
# AI Application Backend URL (defaults to https://apply.getlinked.live if not set)
NEXT_PUBLIC_AI_BACKEND_URL=https://apply.getlinked.live

# Your main backend URL (for other API calls)
NEXT_PUBLIC_BACKEND_URL=https://your-main-api.com
```

### WebSocket URL Generation

The system automatically converts HTTP URLs to WebSocket URLs:

- `https://apply.getlinked.live` → `wss://apply.getlinked.live`
- `http://localhost:8000` → `ws://localhost:8000`

## 🛠️ Error Handling

### Network Errors

- Automatic offline detection
- Connection retry with exponential backoff
- User-friendly error messages
- Graceful degradation

### Upload Errors

- File type validation
- Size limit enforcement
- Server error handling
- Progress tracking

### WebSocket Errors

- Connection loss detection
- Automatic reconnection attempts
- Message parsing error handling
- Timeout management

## 🎨 UI Components

### ChatMessage

Displays chat messages with:

- Bot/user differentiation
- Progress information
- CV scores
- Application summaries
- Timestamps

### FileUploadZone

Provides file upload with:

- Drag and drop support
- Progress indicators
- Error states
- File validation

### ConnectionStatus

Shows connection state:

- Online/offline status
- WebSocket connection
- Reconnection attempts

## 📱 Responsive Design

The interface is fully responsive and works on:

- Desktop browsers
- Tablet devices
- Mobile phones
- Various screen sizes

## 🔒 Security Considerations

- File type validation on client and server
- Size limits to prevent abuse
- Secure WebSocket connections (WSS in production)
- Input sanitization for chat messages
- Error message sanitization

## 🚀 Deployment

### Production Checklist

1. Set `NEXT_PUBLIC_AI_BACKEND_URL=https://apply.getlinked.live` for production
2. Ensure WebSocket server supports WSS (already configured)
3. Configure CORS for file uploads on apply.getlinked.live
4. Test with production data and real job IDs
5. Monitor error rates and connection stability

## 📊 Monitoring

Key metrics to monitor:

- WebSocket connection success rate
- File upload success rate
- Average application completion time
- Error rates by type
- User engagement metrics

## 🤝 Contributing

When contributing to this implementation:

1. Follow existing code patterns
2. Add comprehensive error handling
3. Include TypeScript types
4. Write tests for new features
5. Update documentation

## 📝 License

This implementation follows the project's existing license terms.
