// Utility functions for getting user context and email

export interface UserContext {
  email?: string;
  name?: string;
  isAuthenticated: boolean;
}

/**
 * Get user context from various sources
 * This function should be adapted based on your authentication system
 */
export const getUserContext = (): UserContext => {
  // Check if we're in a browser environment
  if (typeof window === 'undefined') {
    return { isAuthenticated: false };
  }

  // Check if user is authenticated via your auth system
  // This is a placeholder - replace with your actual auth logic

  // Example: Check localStorage for user data
  try {
    const userData = localStorage.getItem('user');
    if (userData) {
      const user = JSON.parse(userData);
      return {
        email: user.email,
        name: user.name,
        isAuthenticated: true
      };
    }
  } catch (error) {
    console.warn('Failed to parse user data from localStorage:', error);
  }

  // Example: Check for auth token or session
  try {
    const authToken = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
    if (authToken) {
      // You might want to decode the token or make an API call to get user info
      // For now, return that user is authenticated but email needs to be collected
      return {
        isAuthenticated: true
      };
    }
  } catch (error) {
    console.warn('Failed to access storage for auth token:', error);
  }

  // Example: Check for email in URL params (for email links)
  try {
    const urlParams = new URLSearchParams(window.location.search);
    const emailFromUrl = urlParams.get('email');
    if (emailFromUrl && isValidEmail(emailFromUrl)) {
      return {
        email: emailFromUrl,
        isAuthenticated: false // Email from URL doesn't mean authenticated
      };
    }
  } catch (error) {
    console.warn('Failed to parse URL parameters:', error);
  }

  return {
    isAuthenticated: false
  };
};

/**
 * Validate email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Get applicant email with fallback to user input
 * This function determines the best way to get the user's email
 */
export const getApplicantEmail = (): string | undefined => {
  const userContext = getUserContext();
  
  // Return email if available and valid
  if (userContext.email && isValidEmail(userContext.email)) {
    return userContext.email;
  }

  return undefined;
};

/**
 * Check if we should skip email collection
 * Returns true if we already have a valid email from authentication
 */
export const shouldSkipEmailCollection = (): boolean => {
  const userContext = getUserContext();
  return userContext.isAuthenticated && !!userContext.email && isValidEmail(userContext.email);
};

/**
 * Store user email for the session
 * This can be used to remember the email during the application process
 */
export const storeUserEmail = (email: string): void => {
  if (typeof window === 'undefined') return;

  if (isValidEmail(email)) {
    try {
      sessionStorage.setItem('applicationEmail', email);
    } catch (error) {
      console.warn('Failed to store email in sessionStorage:', error);
    }
  }
};

/**
 * Get stored user email from session
 */
export const getStoredUserEmail = (): string | undefined => {
  if (typeof window === 'undefined') return undefined;

  try {
    const email = sessionStorage.getItem('applicationEmail');
    if (email && isValidEmail(email)) {
      return email;
    }
  } catch (error) {
    console.warn('Failed to get email from sessionStorage:', error);
  }
  return undefined;
};

/**
 * Clear stored user email
 */
export const clearStoredUserEmail = (): void => {
  if (typeof window === 'undefined') return;

  try {
    sessionStorage.removeItem('applicationEmail');
  } catch (error) {
    console.warn('Failed to clear email from sessionStorage:', error);
  }
};

/**
 * Get the best available email for the application
 * Checks multiple sources in order of preference
 */
export const getBestAvailableEmail = (): string | undefined => {
  // 1. Check authenticated user email
  const userContext = getUserContext();
  if (userContext.email && isValidEmail(userContext.email)) {
    return userContext.email;
  }

  // 2. Check stored session email
  const storedEmail = getStoredUserEmail();
  if (storedEmail) {
    return storedEmail;
  }

  // 3. No email available - user needs to provide it
  return undefined;
};

export default {
  getUserContext,
  isValidEmail,
  getApplicantEmail,
  shouldSkipEmailCollection,
  storeUserEmail,
  getStoredUserEmail,
  clearStoredUserEmail,
  getBestAvailableEmail
};
