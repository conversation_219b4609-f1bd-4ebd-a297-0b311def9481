// Test validation utilities for AI Application Flow
// Production API: https://apply.getlinked.live/
// WebSocket: wss://apply.getlinked.live/ws/chat/{job_id}
// Upload: https://apply.getlinked.live/api/v1/files/upload-cv/{job_id}/{applicant_email}

export interface TestResult {
  passed: boolean;
  message: string;
  details?: any;
}

export interface TestSuite {
  name: string;
  tests: TestCase[];
}

export interface TestCase {
  name: string;
  description: string;
  test: () => Promise<TestResult> | TestResult;
}

// Mock WebSocket message types for testing
export const mockMessages = {
  welcome: {
    type: 'welcome',
    data: {
      message:
        "Welcome! I'm here to help you apply for the Software Developer position.",
      application_key: '<EMAIL>',
      status: 'started',
      job_title: 'Software Developer',
    },
    timestamp: new Date().toISOString(),
  },

  cvUploaded: {
    type: 'cv_uploaded',
    data: {
      message: "Thank you for uploading your CV! I'm processing it now.",
      status: 'cv_processing',
      processing_started: true,
    },
    timestamp: new Date().toISOString(),
  },

  cvProcessed: {
    type: 'cv_processed',
    data: {
      message: 'Great news! Your CV analysis is complete.',
      cv_score: 85.5,
      status: 'collecting_info',
      passed_threshold: true,
    },
    timestamp: new Date().toISOString(),
  },

  aiResponse: {
    type: 'ai_response',
    data: {
      message:
        'Great! Nice to meet you. Could you please provide your phone number?',
      status: 'collecting_info',
      progress: {
        collected_fields: 2,
        total_fields: 9,
        missing_fields: ['phone', 'location', 'experience_years'],
      },
      next_step: 'collecting_phone',
    },
    timestamp: new Date().toISOString(),
  },

  applicationComplete: {
    type: 'application_complete',
    data: {
      message:
        'Congratulations! Your application has been completed successfully.',
      status: 'completed',
      application_summary: {
        application_key: '<EMAIL>',
        job_title: 'Software Developer',
        applicant_name: 'Test User',
        cv_score: 85.5,
        status: 'completed',
        threshold_met: true,
      },
    },
    timestamp: new Date().toISOString(),
  },

  phaseComplete: {
    type: 'phase_complete',
    data: {
      message:
        'Congratulations New Guy! You have successfully completed the application phase and passed. You are now entering the Initial phase.',
      first_question: 'What is an API?',
      phase_progression: [
        {
          name: 'Initial',
          status: 'in_progress',
        },
        {
          name: 'Technical',
          status: 'pending',
        },
        {
          name: 'Final',
          status: 'pending',
        },
      ],
      available_questions: {
        initial: [
          'What is an API?',
          'Explain REST principles',
          'What is HTTP?',
        ],
        technical: ['Code a function', 'Debug this code', 'System design'],
        final: ['Tell us about yourself', 'Why this company?', 'Career goals'],
      },
    },
    timestamp: new Date().toISOString(),
  },

  error: {
    type: 'error',
    data: {
      message: 'An error occurred during processing.',
      error_code: 'PROCESSING_ERROR',
    },
    timestamp: new Date().toISOString(),
  },
};

// Test cases for WebSocket message handling
export const messageHandlingTests: TestSuite = {
  name: 'WebSocket Message Handling',
  tests: [
    {
      name: 'Welcome Message',
      description: 'Should handle welcome message correctly',
      test: () => {
        const message = mockMessages.welcome;
        // This would be tested with the actual hook
        return {
          passed: true,
          message: 'Welcome message structure is valid',
          details: message,
        };
      },
    },
    {
      name: 'CV Upload Confirmation',
      description: 'Should handle CV upload confirmation',
      test: () => {
        const message = mockMessages.cvUploaded;
        return {
          passed:
            message.type === 'cv_uploaded' && message.data.processing_started,
          message: 'CV upload message is valid',
        };
      },
    },
    {
      name: 'CV Processing Complete',
      description: 'Should handle CV processing completion',
      test: () => {
        const message = mockMessages.cvProcessed;
        return {
          passed:
            message.type === 'cv_processed' &&
            typeof message.data.cv_score === 'number',
          message: 'CV processing message is valid',
        };
      },
    },
    {
      name: 'AI Response with Progress',
      description: 'Should handle AI responses with progress tracking',
      test: () => {
        const message = mockMessages.aiResponse;
        const hasProgress =
          message.data.progress &&
          typeof message.data.progress.collected_fields === 'number';
        return {
          passed: hasProgress,
          message: 'AI response with progress is valid',
        };
      },
    },
    {
      name: 'Application Complete',
      description: 'Should handle application completion',
      test: () => {
        const message = mockMessages.applicationComplete;
        const hasSummary = !!(
          message.data.application_summary &&
          message.data.application_summary.application_key
        );
        return {
          passed: hasSummary,
          message: 'Application complete message is valid',
        };
      },
    },
    {
      name: 'Phase Complete',
      description: 'Should handle phase completion and transition to interview',
      test: () => {
        const message = mockMessages.phaseComplete;
        const hasPhaseProgression = !!(
          message.data.phase_progression &&
          Array.isArray(message.data.phase_progression)
        );
        const hasFirstQuestion = !!(
          message.data.first_question &&
          typeof message.data.first_question === 'string'
        );
        const hasAvailableQuestions = !!(
          message.data.available_questions &&
          typeof message.data.available_questions === 'object'
        );

        return {
          passed:
            hasPhaseProgression && hasFirstQuestion && hasAvailableQuestions,
          message: 'Phase complete message structure is valid',
          details: {
            hasPhaseProgression,
            hasFirstQuestion,
            hasAvailableQuestions,
            phaseCount: message.data.phase_progression?.length || 0,
          },
        };
      },
    },
  ],
};

// Test cases for error handling
export const errorHandlingTests: TestSuite = {
  name: 'Error Handling',
  tests: [
    {
      name: 'Network Error Detection',
      description: 'Should detect and handle network errors',
      test: () => {
        const networkErrors = [
          'Network error',
          'Connection failed',
          'Timeout occurred',
        ];

        const isNetworkError = (error: string) => {
          return networkErrors.some(ne =>
            error.toLowerCase().includes(ne.toLowerCase())
          );
        };

        return {
          passed: isNetworkError('Network error occurred'),
          message: 'Network error detection works',
        };
      },
    },
    {
      name: 'File Upload Error Handling',
      description: 'Should handle file upload errors gracefully',
      test: () => {
        const uploadErrors = [
          { status: 413, expected: 'too large' },
          { status: 415, expected: 'not supported' },
          { status: 400, expected: 'Invalid file' },
        ];

        // This would test the actual upload error handling
        return {
          passed: true,
          message: 'Upload error handling is implemented',
        };
      },
    },
    {
      name: 'WebSocket Reconnection',
      description: 'Should attempt to reconnect on connection loss',
      test: () => {
        // This would test the reconnection logic
        return {
          passed: true,
          message: 'Reconnection logic is implemented',
        };
      },
    },
  ],
};

// Test cases for file validation
export const fileValidationTests: TestSuite = {
  name: 'File Validation',
  tests: [
    {
      name: 'Valid File Types',
      description: 'Should accept PDF, DOC, and DOCX files',
      test: () => {
        const validTypes = [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ];

        return {
          passed: validTypes.length === 3,
          message: 'Valid file types are defined',
        };
      },
    },
    {
      name: 'File Size Limit',
      description: 'Should reject files larger than 10MB',
      test: () => {
        const maxSize = 10 * 1024 * 1024; // 10MB
        const testFileSize = 15 * 1024 * 1024; // 15MB

        return {
          passed: testFileSize > maxSize,
          message: 'File size validation logic is correct',
        };
      },
    },
    {
      name: 'Invalid File Types',
      description: 'Should reject unsupported file types',
      test: () => {
        const invalidTypes = ['image/jpeg', 'text/plain', 'application/zip'];

        const validTypes = [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ];

        const hasInvalidType = invalidTypes.some(type =>
          validTypes.includes(type)
        );

        return {
          passed: !hasInvalidType,
          message: 'Invalid file types are properly rejected',
        };
      },
    },
  ],
};

// Test runner utility
export const runTestSuite = async (
  testSuite: TestSuite
): Promise<{
  suiteName: string;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  results: Array<{ name: string; result: TestResult }>;
}> => {
  const results: Array<{ name: string; result: TestResult }> = [];

  for (const testCase of testSuite.tests) {
    try {
      const result = await testCase.test();
      results.push({ name: testCase.name, result });
    } catch (error) {
      results.push({
        name: testCase.name,
        result: {
          passed: false,
          message: `Test failed with error: ${error}`,
          details: error,
        },
      });
    }
  }

  const passedTests = results.filter(r => r.result.passed).length;
  const failedTests = results.length - passedTests;

  return {
    suiteName: testSuite.name,
    totalTests: results.length,
    passedTests,
    failedTests,
    results,
  };
};

// Run all test suites
export const runAllTests = async () => {
  const testSuites = [
    messageHandlingTests,
    errorHandlingTests,
    fileValidationTests,
  ];

  const allResults = [];

  for (const suite of testSuites) {
    const result = await runTestSuite(suite);
    allResults.push(result);
  }

  return allResults;
};

// Validation checklist for manual testing
export const validationChecklist = [
  {
    category: 'WebSocket Connection',
    items: [
      'WebSocket connects successfully to the correct URL',
      'Connection status is properly displayed',
      'Reconnection attempts work after disconnection',
      'Error messages are shown for connection failures',
    ],
  },
  {
    category: 'File Upload',
    items: [
      'File selection works via click and drag-drop',
      'Upload progress is displayed correctly',
      'Success/error states are handled properly',
      'File validation works for type and size',
    ],
  },
  {
    category: 'Chat Interface',
    items: [
      'Messages are displayed in correct order',
      'Typing indicator appears during AI responses',
      'User input is properly handled',
      'Message timestamps are accurate',
    ],
  },
  {
    category: 'Application Flow',
    items: [
      'Step progression works correctly',
      'Progress tracking is accurate',
      'Application completion is handled properly',
      'Error recovery allows continuation',
    ],
  },
  {
    category: 'Interview Phase Transitions',
    items: [
      'Phase completion messages are displayed correctly',
      'Phase progression UI shows current and pending phases',
      'Typing indicator is cleared after phase transition',
      'First interview question appears after phase completion',
      'Chat input is enabled after receiving first question',
      'Phase transition data is properly structured and displayed',
    ],
  },
  {
    category: 'Error Handling',
    items: [
      'Network errors are detected and reported',
      'Upload errors show appropriate messages',
      'WebSocket errors trigger reconnection',
      'User-friendly error messages are displayed',
    ],
  },
];

export default {
  mockMessages,
  messageHandlingTests,
  errorHandlingTests,
  fileValidationTests,
  runTestSuite,
  runAllTests,
  validationChecklist,
};
