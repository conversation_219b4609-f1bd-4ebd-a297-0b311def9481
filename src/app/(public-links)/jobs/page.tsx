'use client'
import Image from 'next/image';
import { useState } from 'react';
import useInfiniteScroll from 'react-infinite-scroll-hook';
import ReactLoading from "react-loading";

import { EmptyCard, Loader } from '@/components/shared';
import { cn } from '@/utils';

import { EmptyJobIcon } from '../../(website-main)/e/jobs/misc/icons';
import { useGetJobs } from './misc/api';
import { FilterJobList, JobCard } from './misc/components';
import JobMediaHeader from './misc/components/layout/JobMediaHeader';


interface queryKeyTypes {
    pageParam: number;
    filterUrl?: string;
}
const JobListings = () => {


    const [searchParams, setSearchParams] = useState<string>("")
    const [filterUrl, setFilterUrl] = useState("")



    const {
        data,
        error,
        fetchNextPage,
        hasNextPage,
        isFetchingNextPage,
        isLoading,
        isFetching,
    } = useGetJobs(filterUrl, searchParams);

    const nextPage = hasNextPage !== undefined;

    const [sentryRef] = useInfiniteScroll({
        loading: isFetchingNextPage || isLoading,
        hasNextPage: nextPage,
        onLoadMore: fetchNextPage,
        disabled: !!error || !nextPage,
        rootMargin: "0px 0px 400px 0px",
    });




    if (isLoading) {
        return (
            <div className='flex items-center justify-center w-screen h-screen'>
                <Loader />
            </div>
        )
    }


    return (
        <div className="w-screen bg-[#F8F9FB] md:px-5 md:py-4 grow">
            <div className="lg:max-xl:w-[90vw] xl:w-[72rem] mx-auto">
                <div className="   md:h-[3.875rem]  sticky top-0 z-50 bg-[#FBFAFF]"><JobMediaHeader /></div>

                <article className='relative flex max-sm:flex-col-reverse md:items-center justify-between bg-black rounded-2xl text-white p-4 md:p-7 md:pb-0 mt-6 max-md:mx-4 max-md:my-6 overflow-y-hidden'>
                    <div className='md:pb-6'>
                        <h1 className='font-medium text-xl md:text-2xl'>
                            Find your dream Job today!
                        </h1>
                        <p className='text-white/80 max-w-[250px] sm:max-w-md text-[0.8rem] md:text-[0.9rem]'>
                            We make it easy to find your dream jon with ease. Browse over 400,000 jobs from top companies around the globe
                        </p>
                    </div>

                    <div className='absolute max-md:top-0 max-md:right-0 md:relative w-full max-w-[125px] max-md:[transform:scaleX(-1)] max-md:mt-4 max-md:opacity-70 md:max-w-[250px] max-h-[120px] overflow-hidden '>
                        <Image
                            src='/assets/char11.png'
                            alt='job search'
                            width={200}
                            height={100}
                            objectFit='cover'
                        />
                    </div>
                </article>

                <div className={cn("bg-[#F8F9FB] h-1 w-full overflow-hidden md:mt-6 ",
                    isFetching && !isLoading && 'bg-black/20'
                )}>

                    <div className={cn("h-full w-full origin-[0_50%] animate-indeterminate-progress rounded-full bg-black opacity-0 transition-opacity", isFetching && !isLoading && 'opacity-100')}></div>
                </div>
                <section className="bg-white pb-4 gap-4 px-4 md:px-[3rem] ">

                    <FilterJobList
                        filterUrl={filterUrl} setFilterUrl={setFilterUrl} searchParams={searchParams} setSearchParams={setSearchParams}
                    />

                    <div className="overflow-x-auto">
                        <div className="">
                            <div className="grid grid-cols-1 md:grid-cols-2 items-stretch gap-5">
                                {
                                    !isLoading && data?.pages?.map((jobs) => {
                                        return (
                                            jobs?.results?.map((job) => (
                                                <JobCard job={job} key={job?.id} />
                                            ))
                                        )
                                    })
                                }

                            </div>
                            {(isFetchingNextPage || hasNextPage) && (


                                <div
                                    className=" w-full mt-5 flex justify-center items-center"
                                    ref={sentryRef}
                                >
                                    {
                                        hasNextPage && hasNextPage ?
                                            <ReactLoading
                                                className="z-20"
                                                type="bubbles"
                                                color="#000"
                                                width={70}
                                                height={70}
                                            />
                                            :
                                            ""
                                    }
                                </div>
                            )}
                        </div>
                    </div>


                    {
                        !isLoading && data?.pages?.map((jobs) => {
                            return (
                                jobs?.results?.length === 0 && (
                                    <EmptyCard
                                        title="No Jobs Found"
                                        titleClass='mt-1.5'
                                        icon={<EmptyJobIcon />}
                                        content={
                                            <div className='text-center pt-4 pb-8'>
                                                {
                                                    (filterUrl.trim() == "" && searchParams.trim() == "") ?
                                                        <p className='text-[0.875rem] font-normal'>There are no job posting available right now, Check back later.</p>
                                                        :
                                                        <p className='text-[0.875rem] font-normal'>No jobs matches your filter parameters, change or clear them and try again.</p>
                                                }
                                            </div>
                                        }
                                    />

                                ))
                        })
                    }
                </section>

            </div>
        </div>
    )
}

export default JobListings