import {
  Briefcase,
  Building2,
  Calendar,
  Clock,
  Eye,
  GraduationCap,
  MapPin,
  Star,
  Users,
} from 'lucide-react';
import React from 'react';
import Logo from '../icon/Logo';
import { JobDetails } from '../types';

const JobDetailsComponent = ({ job }: { job: JobDetails }) => {
  interface FormatDateOptions {
    year: 'numeric' | '2-digit';
    month: 'long' | 'short' | 'narrow' | 'numeric' | '2-digit';
    day: 'numeric' | '2-digit';
  }

  const formatDate = (
    dateString: string,
    options?: FormatDateOptions
  ): string => {
    return new Date(dateString).toLocaleDateString(
      'en-US',
      options ?? {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }
    );
  };

  interface FormatJobType {
    (type: string): string;
  }

  const formatJobType: FormatJobType = type => {
    return type
      .replace('_', ' ')
      .toLowerCase()
      .replace(/\b\w/g, l => l.toUpperCase());
  };

  interface StatusColorMap {
    [key: string]: string;
  }

  type JobStatus = 'OPEN' | 'CLOSED' | string;

  const getStatusColor = (status: JobStatus): string => {
    switch (status) {
      case 'OPEN':
        return ' text-green-800 border-green-200';
      case 'CLOSED':
        return ' text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className=" max-w-[100vw] bg-white">
      {/* Header Section */}
      <div className=" px-8 py-6">
        <div className="mb-4 flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-white/20 backdrop-blur-sm">
              <Logo />
            </div>
            <div>
              <h1 className="text-2xl font-bold capitalize">{job.title}</h1>
              <p className="text-lg ">{job.company}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Eye className="h-4 w-4" />
            <span className="text-sm">{job.viewers_count} views</span>
          </div>
        </div>

        <div className="flex flex-wrap gap-3">
          <span
            className={`rounded-full border px-3 py-1 text-sm font-medium ${getStatusColor(
              job.job_status
            )}`}
          >
            {job.job_status}
          </span>
          <span className={`rounded-full border px-3 py-1 text-sm font-medium`}>
            {formatJobType(job.proficiency_level)}
          </span>
          <span className="rounded-full border  px-3 py-1 text-sm font-medium ">
            {job.industry}
          </span>
        </div>
      </div>

      {/* Quick Info Bar */}
      <div className="border-b border-gray-100 bg-white px-8 py-4">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div className="flex items-center space-x-2 text-gray-600">
            <span className="text-sm">{job.location}</span>
          </div>
          <div className="flex items-center space-x-2 text-gray-600">
            <span className="text-sm">{formatJobType(job.job_type)}</span>
          </div>
          <div className="flex items-center space-x-2 text-gray-600">
            <span className="text-sm">
              Deadline: {formatDate(job.application_deadline)}
            </span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="space-y-8 px-8 py-6">
        {/* Description */}
        <section>
          <h2 className="mb-4 flex items-center text-xl font-semibold text-gray-900">
            Job Description
          </h2>
          <div className="rounded-lg bg-gray-50 p-6">
            <p className="leading-relaxed text-gray-700">{job.description}</p>
          </div>
        </section>

        {/* Responsibilities */}
        {job.responsibilities && (
          <section>
            <h2 className="mb-4 flex items-center text-xl font-semibold text-gray-900">
              Key Responsibilities
            </h2>
            <div className="rounded-lg bg-gray-50 p-6">
              <ul className="space-y-3">
                {job.responsibilities
                  .split('\n')
                  .map((responsibility, index) => (
                    <li key={index} className="flex items-start space-x-3">
                      <span className="text-gray-700">{responsibility}</span>
                    </li>
                  ))}
              </ul>
            </div>
          </section>
        )}

        {/* Requirements */}
        {job.requirements && (
          <section>
            <h2 className="mb-4 flex items-center text-xl font-semibold text-gray-900">
              Requirements
            </h2>
            <div className="rounded-lg p-6">
              <ul className="space-y-3">
                {job.requirements.split('\n').map((requirement, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <span className="text-gray-700">{requirement}</span>
                  </li>
                ))}
              </ul>
            </div>
          </section>
        )}
      </div>

      {/* Apply Button */}
      <div className="border-t border-gray-100 bg-gray-50 px-8 py-6">
        <div className="flex flex-col items-center justify-between gap-4 sm:flex-row">
          <div className="text-sm text-gray-600">
            Posted on {formatDate(job.created_at)} • Job ID: {job.id}
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobDetailsComponent;
