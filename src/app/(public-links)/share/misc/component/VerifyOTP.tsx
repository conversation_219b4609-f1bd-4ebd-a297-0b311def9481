'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import toast from 'react-hot-toast';
import Logo from '../../../../(website-main)/e/my-clients/misc/icons/Logo';
import { useVerifyOTP } from '../api/postverifyOTP';

export default function VerifyOTP() {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [timer, setTimer] = useState(56);
  const inputRefs = useRef<Array<HTMLInputElement | null>>([]);

  const searchParams = useSearchParams();

  const token = searchParams.get('token');
  const router = useRouter();
  const {
    mutate: verifyOTP,
    isLoading,
    isError,
    isSuccess,
    error,
  } = useVerifyOTP();

  type InputRef = HTMLInputElement | null;

  const handleInputChange = (index: number, value: string) => {
    if (value.length > 1) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    if (value && index < 5) {
      (inputRefs.current[index + 1] as InputRef)?.focus();
    }
  };

  interface OTPInputKeyDownEvent
    extends React.KeyboardEvent<HTMLInputElement> {}

  const handleKeyDown = (index: number, e: OTPInputKeyDownEvent) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  interface OTPPasteEvent extends React.ClipboardEvent<HTMLInputElement> {}

  const handlePaste = (e: OTPPasteEvent): void => {
    e.preventDefault();
    const pastedData: string = e.clipboardData.getData('text').slice(0, 6);
    const newOtp: string[] = pastedData.split('').slice(0, 6);

    while (newOtp.length < 6) {
      newOtp.push('');
    }

    setOtp(newOtp);

    const lastFilledIndex: number = newOtp.findIndex(
      (val: string) => val === ''
    );
    const focusIndex: number = lastFilledIndex === -1 ? 5 : lastFilledIndex;
    inputRefs.current[focusIndex]?.focus();
  };

  const handleSubmit = () => {
    const otpCode = otp.join('');
    if (otpCode.length === 6) {
      verifyOTP(
        {
          otp: otpCode,
          token: token || '',
        },
        {
          onSuccess: () => {
            router.push(`/share/job?token=${token}`);
            toast.success('OTP verified successfully!');
          },
          onError: err => {
            toast.error(
              err && typeof err === 'object' && 'message' in err
                ? ` ${(err as any).message}`
                : ''
            );
          },
        }
      );
    }
  };

  const isOtpComplete = otp.every(digit => digit !== '');
  interface FormatTime {
    (seconds: number): string;
  }

  const formatTime: FormatTime = seconds =>
    `0:${seconds.toString().padStart(2, '0')}`;

  return (
    <div className="flex min-h-screen w-full items-center justify-center bg-gray-50 p-4 md:absolute">
      <div
        className="relative flex items-center bg-white p-4  md:h-[80%] md:w-[80%]"
        style={{
          backgroundImage: "url('/images/share-job/otpbg.png')",
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      >
        <div className="w-full max-w-md rounded-2xl  p-8 shadow-sm">
          {/* Header */}
          <div className="mb-8 flex items-center gap-3 ">
            <Logo />
            <div>
              <h1 className="text-lg font-semibold text-gray-900">
                Backend developer
              </h1>
              <p className="text-sm text-gray-500">Skill assessment test</p>
            </div>
          </div>

          {/* Main Content */}
          <div className="space-y-6">
            <div>
              <h2 className="mb-2 text-xl font-semibold text-gray-900">
                Access candidate details
              </h2>
              <p className="text-sm leading-relaxed text-gray-600">
                Please enter the 6-digits otp code sent to your email below to
                continue
              </p>
            </div>

            {/* OTP Input */}
            <div className="space-y-4">
              <div className="flex justify-between gap-3">
                {otp.map((digit, index) => (
                  <input
                    key={index}
                    ref={el => {
                      inputRefs.current[index] = el;
                    }}
                    type="text"
                    inputMode="numeric"
                    maxLength={1}
                    value={digit}
                    onChange={e =>
                      handleInputChange(
                        index,
                        e.target.value.replace(/\D/g, '')
                      )
                    }
                    onKeyDown={e => handleKeyDown(index, e)}
                    onPaste={handlePaste}
                    className="h-8 w-8 rounded-lg border border-gray-200 text-center text-lg font-semibold transition-all duration-200 hover:border-gray-300 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary md:h-12 md:w-12"
                    disabled={isLoading}
                  />
                ))}
              </div>
            </div>

            {/* Submit Button */}
            <button
              onClick={handleSubmit}
              disabled={!isOtpComplete || isLoading}
              className="flex w-full items-center justify-center gap-2 rounded-lg bg-primary px-4 py-3 font-medium text-white transition-all duration-200 hover:bg-primary-normal-hover disabled:cursor-not-allowed disabled:bg-gray-300"
            >
              {isLoading ? (
                <>
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                  Verifying...
                </>
              ) : (
                'Proceed to candidates'
              )}
            </button>
          </div>

          {/* Footer */}
        </div>
      </div>
      <div className="absolute  bottom-6 left-6 mt-8 border-t border-gray-100 pt-6 text-center">
        <p className="flex items-center justify-center gap-1 text-xs text-gray-400">
          POWERED BY
          <span className="font-semibold text-gray-600">Getlinked.AI</span>
        </p>
      </div>
    </div>
  );
}
