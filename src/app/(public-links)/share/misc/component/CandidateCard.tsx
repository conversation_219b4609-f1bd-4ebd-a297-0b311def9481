'use client';

import { Message } from 'iconsax-react';
import { MoreVertical } from 'lucide-react';
import { <PERSON><PERSON>, Button } from '@/components/shared';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/components/shared/avatar';
import { Card, CardContent } from '@/components/shared/card';
import { Application } from '../types';

interface CandidateCardProps {
  permissions: string[];
  application: Application;
  onViewDetails: (application: Application) => void;
}

export function CandidateCard({
  permissions,
  application,
  onViewDetails,
}: CandidateCardProps) {
  const candidate = application['candidate-details'];

  const canViewCandidateDetails = permissions.includes(
    'view_candidate_details'
  );

  const getMatchColor = (percentage: number) => {
    if (percentage >= 80) return 'text-emerald-500';
    if (percentage >= 70) return 'text-yellow-500';
    return 'text-red-500';
  };

  return (
    <Card className="rounded-xl border-0 bg-white shadow-sm transition-all duration-200 hover:shadow-md">
      <CardContent className="p-4 sm:p-6">
        {/* Header Row */}
        <div className="mb-4 flex items-start justify-between">
          {/* Left side - Name and Match */}
          <div className="min-w-0 flex-1">
            <div className="mb-2 flex items-center justify-between">
              <h3 className="truncate text-sm  text-gray-900 sm:text-base">
                {candidate.name}
              </h3>
              <div className="flex flex-shrink-0 items-center gap-2">
                <span className="hidden text-sm text-gray-500 sm:inline">
                  Match:
                </span>
                <span
                  className={`rounded-full text-base   ${getMatchColor(
                    application.percentage_match
                  )}`}
                >
                  {application.percentage_match}%
                </span>
              </div>
            </div>

            {/* Experience and Location */}
            <div className="mb-4 flex flex-col gap-1 text-sm text-gray-500 sm:flex-row sm:items-center sm:justify-between sm:gap-4">
              <div>
                Experience:{' '}
                <span className="text-black">
                  {candidate.years_of_experience}{' '}
                </span>
              </div>
              <div>
                Location:{' '}
                <span className="text-black">{candidate.location}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Row */}
        <div className="flex items-center justify-between">
          {/* Left side - Avatars */}
          <div className="flex items-center">
            <Avatar className="h-8 w-8 border-2 border-white shadow-sm sm:h-10 sm:w-10">
              <AvatarImage src={application.avatar} alt={candidate.name} />
              <AvatarFallback className="bg-orange-200 text-sm font-medium text-orange-700">
                {candidate.name
                  .split(' ')
                  .map(n => n[0])
                  .join('')}
              </AvatarFallback>
            </Avatar>

            {/* Overlapping second avatar with chat icon */}
            <div className="relative -ml-2">
              {/* <div className="flex h-8 w-8 items-center justify-center rounded-full border-2 border-white bg-gray-100 shadow-sm sm:h-10 sm:w-10">
                <Message className="h-3 w-3 text-gray-500 sm:h-4 sm:w-4" />
          
                <div className="absolute -right-0.5 -top-0.5 h-3 w-3 rounded-full border border-white bg-emerald-500">
                  <span className="flex h-full items-center justify-center text-[8px] font-bold text-white">
                    2
                  </span>
                </div>
              </div> */}
            </div>
          </div>

          {/* Right side - View Details Button */}
          {canViewCandidateDetails && (
            <Button
              variant="light"
              size="tiny"
              onClick={() => onViewDetails(application)}
            >
              View details
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
