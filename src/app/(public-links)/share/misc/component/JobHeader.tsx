'use client';

import { CopyIcon, Loader2 } from 'lucide-react';
import toast from 'react-hot-toast';
import { Button } from '@/components/shared';
import Logo from '../icon/Logo';
import { JobDetails } from '../types';

interface JobHeaderProps {
  job: JobDetails;
  handleExport: () => Promise<void>;
  isDownloading: boolean;
  permissions: string[];
}

export function JobHeader({
  job,
  handleExport,
  isDownloading,
  permissions,
}: JobHeaderProps) {
  const copyJobLink = (link: string) => {
    if (typeof navigator !== 'undefined') {
      navigator.clipboard.writeText(link);
      toast.success('Job link copied successfully');
    }
  };

  // Permissions
  const canExportCandidateFiles = permissions.includes(
    'export_candidates_files'
  );
  const canExportAllCandidates = permissions.includes('export_all_candidates');

  return (
    <div
      className="border-b border-gray-200 bg-white px-4 py-6 sm:px-6"
      data-pdf-content
    >
      <div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
        {/* Left side - Icon and Job Info */}
        <div className="flex items-center gap-4">
          {/* Icon */}
          <Logo />

          {/* Job Details */}
          <div className="min-w-0 flex-1">
            <div className="flex flex-col gap-1 sm:flex-row sm:items-center sm:gap-3">
              <h1 className="text-xl font-semibold text-gray-900 sm:text-xl">
                {job.title}
              </h1>
            </div>

            <p className="mt-1 text-sm text-gray-500 sm:text-base">
              {job.company}
            </p>
          </div>

          <div className="flex flex-col  gap-2">
            <p className=" text-xs text-gray-400 sm:text-sm">
              Date created{' '}
              {new Date(job.created_at).toLocaleDateString('en-US', {
                month: 'numeric',
                day: 'numeric',
                year: 'numeric',
              })}
            </p>
            <div className="text-xs text-gray-400 sm:text-sm">
              <span
                className={
                  job.job_status === 'closed'
                    ? 'text-red-500'
                    : job.job_status === 'unknown'
                    ? 'text-gray-400'
                    : 'text-green-500'
                }
              >
                {job.job_status}
              </span>
            </div>
          </div>
        </div>

        {/* Right side - Action Buttons */}
        <div className="flex flex-row gap-2 sm:flex-shrink-0">
          <Button
            variant="light"
            size="tiny"
            className="!border-[1.25px] py-2"
            onClick={(_e: React.MouseEvent) => {
              copyJobLink(
                `${process.env.NEXT_PUBLIC_FRONTEND_URL}/jobs/${job.uniqueid}`
              );
            }}
          >
            Copy job link <CopyIcon className="h-4 w-4" />
          </Button>

          {canExportAllCandidates && (
            <Button
              variant="light"
              size="tiny"
              type="button"
              onClick={handleExport}
              disabled={isDownloading}
            >
              <span className="text-xs sm:text-sm">
                {isDownloading ? (
                  <span className="flex items-center gap-1">
                    <Loader2 size={16} className="animate-spin" />
                    Exporting...
                  </span>
                ) : (
                  'Export'
                )}
              </span>
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
