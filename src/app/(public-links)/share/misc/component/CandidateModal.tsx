'use client';

import { ChevronLeft, ChevronRight, Shield, X } from 'lucide-react';
import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerHeader,
} from '@/components/shared';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/components/shared/avatar';
import { useWindowWidth } from '@/hooks';
import { Application, JobResponse } from '../types';

interface CandidateModalProps {
  permissions: string[];
  application: Application | null;
  isOpen: boolean;
  onClose: () => void;
  onNext?: () => void;
  onPrev?: () => void;
  hasNext?: boolean;
  hasPrev?: boolean;
}

type TabType = 'About' | 'Assessment';

export function CandidateModal({
  permissions,
  application,
  isOpen,
  onClose,
  onNext,
  onPrev,
  hasNext,
  hasPrev,
}: CandidateModalProps) {
  const [activeTab, setActiveTab] = useState<TabType>('About');
  const [markAsSeen, setMarkAsSeen] = useState<'Yes' | 'No' | null>(null);

  const windowWidth = useWindowWidth();

  const canViewAssessmentList = permissions.includes('view_assessments');

  if (!application) return null;

  const candidate = application['candidate-details'];
  const assessment = application.assessment;

  const getMatchColor = (percentage: number) => {
    if (percentage >= 80) return 'text-primary';
    if (percentage >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const CircularProgress = ({
    percentage,
    size = 80,
    color = '#8B5CF6',
  }: {
    percentage: number;
    size?: number;
    color?: string;
  }) => {
    const radius = (size - 8) / 2;
    const circumference = 2 * Math.PI * radius;
    const strokeDasharray = circumference;
    const strokeDashoffset = circumference - (percentage / 100) * circumference;

    return (
      <div className="relative" style={{ width: size, height: size }}>
        <svg width={size} height={size} className="-rotate-90 transform">
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="#E5E7EB"
            strokeWidth="6"
            fill="none"
          />
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={color}
            strokeWidth="6"
            fill="none"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className="transition-all duration-300 ease-in-out"
          />
        </svg>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <div className="text-lg font-bold" style={{ color }}>
              {percentage}%
            </div>
            <div className="text-xs text-gray-500">score</div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Drawer
      open={isOpen}
      onOpenChange={open => !open && onClose()}
      direction={windowWidth < 720 ? 'bottom' : 'right'}
    >
      <DrawerContent className="!m-0 h-[90vh] w-full overflow-hidden rounded-l-2xl border-none bg-white !p-0 md:left-auto md:right-0 md:h-screen md:w-[50%] md:max-w-[750px]">
        {/* Header */}
        <DrawerHeader className="flex items-center justify-between bg-primary p-6 text-white">
          <h2 className="text-lg font-semibold">View details</h2>
          <DrawerClose asChild>
            <Button
              size="tiny"
              className="bg-white/30 px-3 py-1.5 text-sm transition-colors hover:bg-white/20 sm:px-4 sm:py-2"
            >
              Close
            </Button>
          </DrawerClose>
        </DrawerHeader>

        <div className="overflow-y-auto p-6">
          {/* Candidate Info */}
          <div className="mb-6 flex items-center gap-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src={application.avatar} alt={candidate.name} />
              <AvatarFallback className="bg-purple-100 text-lg text-primary">
                {candidate.name
                  .split(' ')
                  .map(n => n[0])
                  .join('')}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="mb-1 flex items-center gap-2">
                <h3 className="text-xl font-semibold text-gray-900">
                  {candidate.name}
                </h3>
                <Shield className="h-5 w-5 text-green-600" />
              </div>
              <p className="mb-1 flex gap-4 text-gray-600">
                {candidate.email.replace(/^(.{4}).*(@.*)$/, '$1****$2')}
                <Badge
                  variant="light"
                  className={`${getMatchColor(
                    application.percentage_match
                  )} rounded-md p-2 text-sm font-normal`}
                >
                  Match: {application.percentage_match}%
                </Badge>
              </p>
            </div>
          </div>

          {/* Tabs */}
          <div className="mb-6 flex gap-1 rounded-lg bg-gray-100 p-1">
            {(
              [
                'About',
                ...(canViewAssessmentList ? ['Assessment'] : []),
              ] as TabType[]
            ).map(tab => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`rounded-md px-4 py-2 transition-colors ${
                  activeTab === tab
                    ? 'bg-primary text-white'
                    : 'text-gray-600 hover:bg-gray-200'
                }`}
              >
                {tab}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          {activeTab === 'About' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div>
                  <h4 className="mb-2 font-medium text-gray-900">Name</h4>
                  <p className="text-gray-600">{candidate.name}</p>
                </div>
                <div>
                  <h4 className="mb-2 font-medium text-gray-900">Email</h4>
                  <p className="text-gray-600">
                    {candidate.email.replace(/^(.{4}).*(@.*)$/, '$1****$2')}
                  </p>
                </div>
                <div>
                  <h4 className="mb-2 font-medium text-gray-900">
                    Current location
                  </h4>
                  <p className="text-gray-600">{candidate.location}</p>
                </div>
                <div>
                  <h4 className="mb-2 font-medium text-gray-900">
                    Years of experience
                  </h4>
                  <p className="text-gray-600">
                    {candidate.years_of_experience} years
                  </p>
                </div>
                <div>
                  <h4 className="mb-2 font-medium text-gray-900">
                    LinkedIn link
                  </h4>
                  <p className="text-primary">{application.linkedin_url}</p>
                </div>
                <div>
                  <h4 className="mb-2 font-medium text-gray-900">
                    Portfolio link
                  </h4>
                  <p className="text-primary">{application.portfolio_url}</p>
                </div>
              </div>

              <div>
                <h4 className="mb-2 font-medium text-gray-900">Role</h4>
                <p className="text-gray-600">{application.role}</p>
              </div>

              <div>
                <h4 className="mb-3 font-medium text-gray-900">Skills</h4>
                <div className="flex flex-wrap gap-2">
                  {application.skills?.map((skill, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="rounded-xl bg-primary-light p-2 text-primary"
                    >
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'Assessment' && assessment && (
            <div className="space-y-6">
              <div className="border-b pb-4">
                <h3 className="mb-2 text-lg font-semibold">Assessment</h3>
                <p className="mb-4 text-gray-600">
                  Here you will see details of the assessment you've set for the
                  candidate
                </p>
              </div>

              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div>
                  <h4 className="mb-2 font-medium text-gray-900">
                    Assessment name:
                  </h4>
                  <p className="font-semibold text-gray-900">
                    {assessment.assessment_name}
                  </p>
                </div>
                <div>
                  <h4 className="mb-2 font-medium text-gray-900">
                    Assessment score:
                  </h4>
                  <p className="text-xl font-medium text-green-600">
                    {assessment.overall_score}%
                  </p>
                </div>
              </div>

              <div>
                <h4 className="mb-4 font-semibold text-gray-900">
                  Performance breakdown:
                </h4>
                <div className="space-y-4">
                  {assessment.sections.map((section, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between"
                    >
                      <span className="text-gray-700">
                        Section {index + 1}: {section.name}
                      </span>
                      <span className="font-semibold">{section.score}%</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="border-t pt-6">
                <h4 className="mb-6 font-semibold text-gray-900">
                  Getlinked talent assessment
                </h4>
                <p className="mb-6 text-gray-600">
                  Here, you will find details of the assessment conducted by
                  Getlinked to evaluate and showcase a talent's abilities. This
                  process aims to facilitate seamless talent selection for you.
                </p>

                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                  <div className="text-center">
                    <CircularProgress
                      percentage={assessment.skill_assessment}
                    />
                    <h5 className="mt-3 font-medium">Skill assessment</h5>
                  </div>
                  <div className="text-center">
                    <CircularProgress
                      percentage={assessment.personality_assessment}
                      color="#4F46E5"
                    />
                    <h5 className="mt-3 font-medium">Personality Assessment</h5>
                  </div>
                  <div className="text-center">
                    <CircularProgress
                      percentage={assessment.technical_assessment}
                      color="#06B6D4"
                    />
                    <h5 className="mt-3 font-medium">Technical Assessment</h5>
                  </div>
                </div>

                <div className="mt-8">
                  <h5 className="mb-3 font-semibold text-gray-900">
                    Personality test files
                  </h5>
                  <p className="mb-4 text-gray-600">
                    Watch how talent performed during the Getlinked personality
                    assessment test
                  </p>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div className="flex aspect-video items-center justify-center rounded-lg bg-gray-100">
                      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-white shadow-lg">
                        <div className="ml-1 h-0 w-0 border-b-2 border-l-4 border-t-2 border-b-transparent border-l-gray-800 border-t-transparent"></div>
                      </div>
                    </div>
                    <div className="flex aspect-video items-center justify-center rounded-lg bg-gray-100">
                      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-white shadow-lg">
                        <div className="ml-1 h-0 w-0 border-b-2 border-l-4 border-t-2 border-b-transparent border-l-gray-800 border-t-transparent"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Mark as Seen Section */}
          {/* <div className="mt-8 border-t pb-20">
            <div className="flex items-center justify-between">
              <span className="text-gray-700">Mark as Seen ?</span>
              <div className="flex gap-2">
                {(['Yes', 'No'] as const).map(option => (
                  <Button
                    key={option}
                    variant={markAsSeen === option ? 'default' : 'outlined'}
                    size="tiny"
                    onClick={() => setMarkAsSeen(option)}
                    className={
                      markAsSeen === option
                        ? 'bg-primary hover:bg-purple-700'
                        : ''
                    }
                  >
                    {option}
                  </Button>
                ))}
              </div>
            </div>
          </div> */}

          {/* Navigation */}
          <div className="fixed bottom-0 left-0 right-0 flex items-center justify-between border-t bg-primary-light px-4 sm:h-20">
            <Button
              variant="outlined"
              size="tiny"
              onClick={onPrev}
              disabled={!hasPrev}
              className="flex items-center gap-2"
            >
              <ChevronLeft className="h-4 w-4" />
              Prev
            </Button>
            <Button
              variant="outlined"
              size="tiny"
              onClick={onNext}
              disabled={!hasNext}
              className="flex items-center gap-2"
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
