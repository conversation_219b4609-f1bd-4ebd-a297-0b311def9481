'use client';

import { div } from '@tensorflow/tfjs';
import { SearchNormal1 } from 'iconsax-react';
import { Search } from 'lucide-react';
import { useState } from 'react';
import { Badge, Input } from '@/components/shared';
import { Application, PipelineStage } from '../types';
import { CandidateCard } from './CandidateCard';

interface PipelineStagesProps {
  permissions: string[];
  stages: {
    stage: PipelineStage;
    count: number;
    applications: Application[];
  }[];
  onViewDetails: (
    application: Application,
    stageApplications: Application[]
  ) => void;
}

export function PipelineStages({
  stages,
  onViewDetails,
  permissions,
}: PipelineStagesProps) {
  const [searchQueries, setSearchQueries] = useState<
    Record<PipelineStage, string>
  >({
    New: '',
    Qualified: '',
    Assessment: '',
    Interview: '',
    Hired: '',
  });

  //PERMISSIONS
  const canViewAllCandidates = permissions.includes('view_applications');

  const handleSearchChange = (stage: PipelineStage, query: string) => {
    setSearchQueries(prev => ({ ...prev, [stage]: query }));
  };

  const [openSearch, setOpenSearch] = useState<PipelineStage | null>(null);

  const getFilteredApplications = (
    stage: PipelineStage,
    applications: Application[]
  ) => {
    const query = searchQueries[stage].toLowerCase();
    if (!query) return applications;

    return applications?.filter(
      app =>
        app['candidate-details'].name.toLowerCase().includes(query) ||
        app['candidate-details'].email.toLowerCase().includes(query)
    );
  };

  return (
    <div className="">
      {/* Horizontal flex container for stages */}
      <div className="flex gap-6" style={{ minWidth: 'max-content' }}>
        {stages.map(({ stage, count, applications }) => {
          const filteredApplications = getFilteredApplications(
            stage,
            applications
          );

          return (
            <div
              key={stage}
              className="flex h-full flex-col"
              style={{ minWidth: '320px' }}
            >
              {/* Stage header */}
              <div className={`rounded-t-lg p-4`}>
                <div className="flex items-center justify-between font-semibold">
                  <div className="flex items-center gap-2">
                    <span className={`text-primary`}>{stage}</span>
                    <Badge
                      variant="secondary"
                      className="flex h-6 w-auto items-center justify-center rounded-lg border border-gray-300 bg-white text-xs text-primary"
                    >
                      {filteredApplications?.length > 0
                        ? filteredApplications?.length
                        : 0}
                    </Badge>
                    {stage === 'New' && (
                      <div className="flex items-center gap-2 text-primary">
                        <span className=""> Qualified</span>
                        <Badge
                          variant="secondary"
                          className="flex h-6 w-auto items-center justify-center rounded-lg border border-gray-300 bg-white text-xs text-primary"
                        >
                          {filteredApplications?.length > 0
                            ? filteredApplications?.length
                            : 0}
                        </Badge>
                      </div>
                    )}
                  </div>
                  <button
                    className="rounded-full bg-white p-2 text-gray-500 hover:text-gray-700"
                    onClick={() =>
                      setOpenSearch(openSearch === stage ? null : stage)
                    }
                  >
                    <SearchNormal1 className="h-4 w-4" />
                  </button>
                </div>
                {/* Search */}
                {openSearch === stage && (
                  <div className="mt-3">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 h-3 w-3 -translate-y-1/2 transform text-gray-400" />
                      <Input
                        placeholder="Search..."
                        value={searchQueries[stage]}
                        onChange={e =>
                          handleSearchChange(stage, e.target.value)
                        }
                        className="h-8 w-full pl-8 text-xs"
                      />
                    </div>
                  </div>
                )}
              </div>

              <div className="max-h-[500px] flex-1 overflow-y-auto rounded-b-lg bg-gray-50 p-4">
                {filteredApplications?.length === 0 ? (
                  <div className="flex h-32 items-center justify-center">
                    <p className="text-sm text-gray-500">
                      {searchQueries[stage]
                        ? `No matches for "${searchQueries[stage]}"`
                        : `No candidates in ${stage}`}
                    </p>
                  </div>
                ) : canViewAllCandidates ? (
                  <div className="space-y-4">
                    {filteredApplications?.map(application => (
                      <CandidateCard
                        key={application.id}
                        permissions={permissions}
                        application={application}
                        onViewDetails={() =>
                          onViewDetails(application, filteredApplications)
                        }
                      />
                    ))}
                  </div>
                ) : (
                  <div className="space-y-4">
                    <span>
                      You do not have permission to view all candidates
                    </span>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
