import { Application, AssessmentDetails, JobResponse } from '../types';

const mockAssessment: AssessmentDetails = {
  assessment_name: 'Backend developers assessment',
  overall_score: 64,
  sections: [
    { name: 'Skill and competency', score: 70 },
    { name: 'Emotional intelligence', score: 30 },
    { name: 'Cognitive & critical thinking', score: 34 },
    { name: 'Language proficiency', score: 54 },
  ],
  skill_assessment: 45,
  personality_assessment: 45,
  technical_assessment: 45,
};

const mockApplications: Application[] = [
  {
    id: '1',
    date_applied: '2023-12-10T10:00:00Z',
    current_stage: 'New',
    status: 'Active',
    screening_status: 'Pending',
    percentage_match: 90,
    'candidate-details': {
      name: '<PERSON>',
      email: '<EMAIL>',
      years_of_experience: '2',
      location: 'Ajah',
      contact_number: '+234801234567',
    },
    documents: {
      cv_available: true,
      cover_letter_available: true,
    },
    assessment: mockAssessment,
    skills: ['Python', 'Node.js', 'PostgreSQL', 'MySQL'],
    linkedin_url: 'https://linkedin.com/in/dexter',
    portfolio_url: 'https://dexter.dev',
    role: 'Backend developer',
    avatar:
      'https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?auto=compress&cs=tinysrgb&w=150',
  },
  {
    id: '2',
    date_applied: '2023-12-09T14:30:00Z',
    current_stage: 'New',
    status: 'Active',
    screening_status: 'Pending',
    percentage_match: 90,
    'candidate-details': {
      name: 'Joseph Abeokuta',
      email: '<EMAIL>',
      years_of_experience: '3',
      location: 'Lagos',
      contact_number: '+234801234568',
    },
    documents: {
      cv_available: true,
      cover_letter_available: false,
    },
    assessment: mockAssessment,
    skills: ['Python', 'Node.js', 'PostgreSQL', 'MySQL', 'Docker'],
    linkedin_url: 'https://linkedin.com/in/joseph',
    portfolio_url: 'https://joseph.dev',
    role: 'Backend developer',
    avatar:
      'https://images.pexels.com/photos/1040881/pexels-photo-1040881.jpeg?auto=compress&cs=tinysrgb&w=150',
  },
  {
    id: '3',
    date_applied: '2023-12-08T09:15:00Z',
    current_stage: 'Qualified',
    status: 'Active',
    screening_status: 'Passed',
    percentage_match: 85,
    'candidate-details': {
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      years_of_experience: '4',
      location: 'Lekki',
      contact_number: '+234801234569',
    },
    documents: {
      cv_available: true,
      cover_letter_available: true,
    },
    assessment: mockAssessment,
    skills: ['JavaScript', 'React', 'Node.js', 'MongoDB'],
    linkedin_url: 'https://linkedin.com/in/sarah',
    portfolio_url: 'https://sarah.dev',
    role: 'Full Stack Developer',
    avatar:
      'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=150',
  },
  {
    id: '4',
    date_applied: '2023-12-07T16:45:00Z',
    current_stage: 'Assessment',
    status: 'Active',
    screening_status: 'In Progress',
    percentage_match: 92,
    'candidate-details': {
      name: 'Michael Chen',
      email: '<EMAIL>',
      years_of_experience: '5',
      location: 'Victoria Island',
      contact_number: '+234801234570',
    },
    documents: {
      cv_available: true,
      cover_letter_available: true,
    },
    assessment: mockAssessment,
    skills: ['Python', 'Django', 'PostgreSQL', 'AWS', 'Docker'],
    linkedin_url: 'https://linkedin.com/in/michael',
    portfolio_url: 'https://michael.dev',
    role: 'Senior Backend Developer',
    avatar:
      'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=150',
  },
  {
    id: '5',
    date_applied: '2023-12-06T11:20:00Z',
    current_stage: 'Interview',
    status: 'Active',
    screening_status: 'Passed',
    percentage_match: 88,
    'candidate-details': {
      name: 'Emily Rodriguez',
      email: '<EMAIL>',
      years_of_experience: '3',
      location: 'Ikoyi',
      contact_number: '+234801234571',
    },
    documents: {
      cv_available: true,
      cover_letter_available: true,
    },
    assessment: mockAssessment,
    skills: ['Java', 'Spring Boot', 'MySQL', 'Kubernetes'],
    linkedin_url: 'https://linkedin.com/in/emily',
    portfolio_url: 'https://emily.dev',
    role: 'Backend Developer',
    avatar:
      'https://images.pexels.com/photos/1181424/pexels-photo-1181424.jpeg?auto=compress&cs=tinysrgb&w=150',
  },
  {
    id: '6',
    date_applied: '2023-12-05T13:00:00Z',
    current_stage: 'Hired',
    status: 'Hired',
    screening_status: 'Completed',
    percentage_match: 95,
    'candidate-details': {
      name: 'David Thompson',
      email: '<EMAIL>',
      years_of_experience: '6',
      location: 'Yaba',
      contact_number: '+234801234572',
    },
    documents: {
      cv_available: true,
      cover_letter_available: true,
    },
    assessment: mockAssessment,
    skills: ['Python', 'FastAPI', 'PostgreSQL', 'Redis', 'Docker'],
    linkedin_url: 'https://linkedin.com/in/david',
    portfolio_url: 'https://david.dev',
    role: 'Senior Backend Developer',
    avatar:
      'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=150',
  },
];

// Generate more applications for different stages
const generateMoreApplications = (): Application[] => {
  const locations = [
    'Lagos',
    'Abuja',
    'Port Harcourt',
    'Kano',
    'Ibadan',
    'Benin City',
  ];
  const names = [
    'Alex Johnson',
    'Maria Garcia',
    'James Wilson',
    'Lisa Wang',
    'Robert Brown',
    'Anna Davis',
  ];
  const skills = [
    ['Python', 'Django', 'PostgreSQL'],
    ['JavaScript', 'React', 'Node.js'],
    ['Java', 'Spring Boot', 'MySQL'],
    ['C#', '.NET', 'SQL Server'],
    ['PHP', 'Laravel', 'MySQL'],
    ['Ruby', 'Rails', 'PostgreSQL'],
  ];

  const additionalApps: Application[] = [];

  for (let i = 7; i <= 150; i++) {
    const stage =
      i <= 101
        ? 'New'
        : i <= 141
        ? 'Qualified'
        : i <= 149
        ? 'Assessment'
        : i <= 153
        ? 'Interview'
        : 'Hired';

    additionalApps.push({
      id: 'i',
      date_applied: new Date(
        2023,
        11,
        Math.floor(Math.random() * 30) + 1
      ).toISOString(),
      current_stage: stage,
      status: 'Active',
      screening_status: stage === 'New' ? 'Pending' : 'Passed',
      percentage_match: Math.floor(Math.random() * 30) + 70,
      'candidate-details': {
        name: names[i % names.length] + ` ${i}`,
        email: `candidate${i}@gmail.com`,
        years_of_experience: Math.floor(Math.random() * 8 + 1).toString(),
        location: locations[i % locations.length],
        contact_number: `+23480123456${i % 10}`,
      },
      documents: {
        cv_available: true,
        cover_letter_available: Math.random() > 0.5,
      },
      assessment: mockAssessment,
      skills: skills[i % skills.length],
      linkedin_url: `https://linkedin.com/in/candidate${i}`,
      portfolio_url: `https://candidate${i}.dev`,
      role: 'Backend Developer',
      avatar: `https://images.pexels.com/photos/${
        1040881 + (i % 100)
      }/pexels-photo-${
        1040881 + (i % 100)
      }.jpeg?auto=compress&cs=tinysrgb&w=150`,
    });
  }

  return additionalApps;
};

const allApplications = [...mockApplications, ...generateMoreApplications()];

export const mockJobData: JobResponse = {
  job: {
    id: '1',
    uniqueid: 'job-1-unique',
    title: 'Backend developer',
    company: 'GetLinked.AI',
    location: 'Lagos, Nigeria',
    description: 'Skill assessment test',
    job_type: 'FULL_TIME',
    proficiency_level: 'MID_LEVEL',
    created_at: '2023-12-12T00:00:00Z',
    industry: 'Technology',
    industry_category: 'Software Development',
    job_tags: ['Backend', 'Node.js', 'Python'],
    application_start_date: '',
    responsibilities: 'Develop and maintain backend systems.',
    qualifications: "Bachelor's degree in Computer Science or related field.",
    job_application_requirement_custom_fields: null,
    job_status: 'OPEN',
    viewers_count: 100,
    requirements: '',

    application_deadline: '2024-01-31T23:59:59Z',
  },
  assessments: null,
  applications: {
    count: allApplications.length,
    applications: allApplications,
    stats: {
      by_status: {
        Active: allApplications.filter(a => a.status === 'Active').length,
        Hired: allApplications.filter(a => a.status === 'Hired').length,
      },
      by_stage: {
        New: allApplications.filter(a => a.current_stage === 'New').length,
        Qualified: allApplications.filter(a => a.current_stage === 'Qualified')
          .length,
        Assessment: allApplications.filter(
          a => a.current_stage === 'Assessment'
        ).length,
        Interview: allApplications.filter(a => a.current_stage === 'Interview')
          .length,
        Hired: allApplications.filter(a => a.current_stage === 'Hired').length,
      },
      by_screening: {
        Pending: allApplications.filter(a => a.screening_status === 'Pending')
          .length,
        Passed: allApplications.filter(a => a.screening_status === 'Passed')
          .length,
        Failed: allApplications.filter(a => a.screening_status === 'Failed')
          .length,
      },
    },
  },
  permissions: [
    'view_applications',
    'view_candidate_details',
    'view_assessments',
    'export_all_candidates',
    'export_candidates_files',
  ],
  share_expires: '2024-12-12T00:00:00Z',
};

// Mock API functions
export const fetchJobData = async (): Promise<JobResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  return mockJobData;
};

export const updateApplicationStage = async (
  applicationId: string,
  newStage: string
): Promise<Application> => {
  await new Promise(resolve => setTimeout(resolve, 500));
  const application = allApplications.find(app => app.id === applicationId);
  if (!application) throw new Error('Application not found');

  application.current_stage = newStage;
  return application;
};
