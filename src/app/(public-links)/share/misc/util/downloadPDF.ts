import PreparePDFContent from './PreparePDFContent';

export const downloadPipeline = async (
  candidateName: string,
  companyName: string,
  options: any = {}
): Promise<boolean> => {
  try {
    // Prepare HTML content
    const htmlContent = PreparePDFContent();

    // Send to server for PDF generation
    const response = await fetch('/api/generate-pdf', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        htmlContent,
        candidateName,
        companyName,
        options,
      }),
    });

    if (!response.ok) {
      throw new Error(`PDF generation failed: ${response.statusText}`);
    }

    // Download the PDF
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;

    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `${candidateName.replace(
      /\s+/g,
      '_'
    )}_${companyName.replace(/\s+/g, '_')}_Report_${timestamp}.pdf`;

    a.download = filename;
    document.body.appendChild(a);
    a.click();

    // Cleanup
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    return true;
  } catch (error) {
    console.error('PDF download failed:', error);
    throw error;
  }
};
