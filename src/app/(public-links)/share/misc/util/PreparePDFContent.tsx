const PreparePDFContent = (): string => {
  const element = document.querySelector('[data-pdf-content]') as HTMLElement;

  if (!element) {
    throw new Error('PDF content element not found');
  }

  // Clone the element to avoid modifying the original
  const clonedElement = element.cloneNode(true) as HTMLElement;

  // Remove elements that shouldn't appear in PDF
  const elementsToRemove = [
    '[data-pdf-exclude]',
    '.pdf-exclude',
    '[data-pdf-header]',
    '[data-pdf-buttons]',
    'button',
    '[role="button"]',
    '[data-dialog-close]',
    '.dialog-close',
  ];

  elementsToRemove.forEach(selector => {
    const elements = clonedElement.querySelectorAll(selector);
    elements.forEach(el => el.remove());
  });

  // Get all styles from the page
  const styles = Array.from(document.styleSheets)
    .map(styleSheet => {
      try {
        return Array.from(styleSheet.cssRules)
          .map(rule => rule.cssText)
          .join('\n');
      } catch (e) {
        return '';
      }
    })
    .join('\n');

  // Create complete HTML document
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Job Pipeline</title>
      <style>
        /* Page setup */
        @page {
          size: A4;
          margin: 1cm;
        }
        
        /* Reset and base styles */
        * {
          box-sizing: border-box;
        }
        
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          margin: 0;
          padding: 0;
        }
        
        /* Page break controls */
        .page-break-before {
          page-break-before: always;
          break-before: page;
        }
        
        .page-break-after {
          page-break-after: always;
          break-after: page;
        }
        
        .page-break-avoid {
          page-break-inside: avoid;
          break-inside: avoid;
        }
        
        /* Section styling */
        .candidate-section,
        .report-section,
        .assessment-section,
        .data-table,
        .chart-container {
          page-break-inside: avoid;
          break-inside: avoid;
          margin-bottom: 20px;
        }
        
        /* Typography */
        h1, h2, h3, h4, h5, h6 {
          page-break-after: avoid;
          break-after: avoid;
          margin-top: 0;
          margin-bottom: 10px;
        }
        
        h1 {
          font-size: 24px;
          border-bottom: 2px solid #333;
          padding-bottom: 5px;
        }
        
        h2 {
          font-size: 20px;
          color: #444;
        }
        
        h3 {
          font-size: 16px;
          color: #555;
        }
        
        p {
          margin: 10px 0;
          orphans: 3;
          widows: 3;
        }
        
        /* Tables */
        table {
          width: 100%;
          border-collapse: collapse;
          margin: 10px 0;
          page-break-inside: avoid;
        }
        
        th, td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: left;
        }
        
        th {
          background-color: #f5f5f5;
          font-weight: bold;
        }
        
        tr {
          page-break-inside: avoid;
        }
        
        /* Lists */
        ul, ol {
          margin: 10px 0;
          padding-left: 20px;
        }
        
        li {
          margin: 5px 0;
        }
        
        /* Images */
        img {
          max-width: 100%;
          height: auto;
          page-break-inside: avoid;
        }
        
        /* Charts and graphs */
        .chart-container {
          text-align: center;
          margin: 20px 0;
        }
        
        /* Custom styles from your app */
        ${styles}
        
        /* Override any problematic styles for PDF */
        .pdf-content {
          max-width: none !important;
          width: 100% !important;
          height: auto !important;
          overflow: visible !important;
        }
      </style>
    </head>
    <body>
      <div class="pdf-content">
        ${clonedElement.innerHTML}
      </div>
    </body>
    </html>
  `;

  return htmlContent;
};

export default PreparePDFContent;
