'use client';

import {
  QueryFunctionContext,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { AxiosWithNoAuth } from '@/lib/api/axios';
import { fetchJobData, updateApplicationStage } from '../lib/mock.data';
import { JobResponse } from '../types';

const getJobData = async ({ queryKey }: QueryFunctionContext) => {
  const [, token] = queryKey as [string, string];
  const response = await AxiosWithNoAuth.get<JobResponse>(
    `/client/shared-job/${token}`
  );
  return response.data;
};

export function useJobData(token: string) {
  return useQuery<JobResponse, Error>({
    queryKey: ['jobData', token],
    queryFn: getJobData,
    enabled: !!token,
  });
}

export function useUpdateApplicationStage() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      applicationId,
      newStage,
    }: {
      applicationId: string;
      newStage: string;
    }) => updateApplicationStage(applicationId, newStage),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['jobData'] });
    },
  });
}
