import { useMutation } from '@tanstack/react-query';
import { AxiosWithNoAuth } from '@/lib/api/axios';

const verifyOTP = async (otp: string, token: string) => {
  try {
    const response = await AxiosWithNoAuth.post(
      `/client/client-shares/${token}/verify/`,
      {
        otp,
      }
    );
    return response.data;
  } catch (error: any) {
    throw new Error('Invalid OTP code. Please try again.');
  }
};

export const useVerifyOTP = () => {
  return useMutation({
    mutationFn: ({
      otp,

      token,
    }: {
      otp: string;
      token: string;
    }) => verifyOTP(otp, token),
  });
};
