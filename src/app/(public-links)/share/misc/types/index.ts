import { Permission } from '@/app/(website-main)/e/my-clients/misc/types';

export interface JobDetails {
  id: string;
  uniqueid: string;
  title: string;
  company: string;
  location: string;
  description: string;
  job_type:
    | 'FULL_TIME'
    | 'PART_TIME'
    | 'CONTRACT'
    | 'INTERNSHIP'
    | 'TEMPORARY'
    | string;
  proficiency_level: 'ENTRY_LEVEL' | 'MID_LEVEL' | 'SENIOR_LEVEL' | string;
  industry: string;
  industry_category: string | null;
  job_tags: string[] | null;
  responsibilities: string;
  qualifications: string | null;
  requirements: string;
  application_deadline: string;
  application_start_date: string;
  job_status: 'OPEN' | 'CLOSED' | 'DRAFT' | string;
  job_application_requirement_custom_fields: Record<string, any> | null;
  viewers_count: number;
  created_at: string;
}

export interface CandidateDetails {
  name: string;
  email: string;
  years_of_experience: string;
  location: string | null;
  contact_number: string | null;
}

export interface CandidateDocuments {
  cv_available: boolean;
  cover_letter_available: boolean;
}

export interface AssessmentScore {
  name: string;
  score: number;
}

export interface AssessmentDetails {
  assessment_name: string;
  overall_score: number;
  sections: AssessmentScore[];
  skill_assessment: number;
  personality_assessment: number;
  technical_assessment: number;
}

export interface Application {
  id: string;
  date_applied: string;
  current_stage: string | null;
  status: string | null;
  screening_status: string;
  percentage_match: number;
  'candidate-details': CandidateDetails;
  documents: CandidateDocuments;
  assessment?: AssessmentDetails;
  skills?: string[];
  linkedin_url?: string;
  portfolio_url?: string;
  role?: string;
  avatar?: string;
}

export interface ApplicationStats {
  by_status: Record<string, number>;
  by_stage: Record<string, number>;
  by_screening: Record<string, number>;
}

export interface Applications {
  count: number;
  applications: Application[];
  stats: ApplicationStats;
}

export interface JobResponse {
  job: JobDetails;
  assessments: unknown | null;
  applications: Applications;
  permissions: Permission[];
  share_expires: string;
}

export type PipelineStage =
  | 'New'
  | 'Qualified'
  | 'Assessment'
  | 'Interview'
  | 'Hired';

export interface PipelineStageData {
  stage: PipelineStage;
  count: number;
  applications: Application[];
}
