'use client';

import { useSearchParams } from 'next/navigation';
import { useMemo, useState } from 'react';
import { Loader } from '@/components/shared';
import { useJobData } from '../misc/api/getJobData';
import { CandidateModal } from '../misc/component/CandidateModal';
import JobDetailPage from '../misc/component/JobDetails';
import { JobHeader } from '../misc/component/JobHeader';
import { PipelineStages } from '../misc/component/PipelineStages';
import { Application, PipelineStage, PipelineStageData } from '../misc/types';
import { downloadPipeline } from '../misc/util/downloadPDF';

export default function Home() {
  const searchParams = useSearchParams();

  const token = searchParams.get('token');
  const { data: jobResponse, isLoading, error } = useJobData(token || '');
  const [isDownloading, setIsDownloading] = useState(false);
  const [selectedApplication, setSelectedApplication] =
    useState<Application | null>(null);
  const [currentApplicationIndex, setCurrentApplicationIndex] = useState(0);
  const [currentStageApplications, setCurrentStageApplications] = useState<
    Application[]
  >([]);
  const [tab, setTab] = useState<'candidates' | 'details'>('candidates');

  const pipelineData = useMemo((): PipelineStageData[] => {
    if (!jobResponse) return [];

    const stages: PipelineStage[] = ['New', 'Assessment', 'Interview', 'Hired'];

    return stages.map(stage => {
      const applications = jobResponse?.applications?.applications?.filter(
        app => app.current_stage === stage
      );

      return {
        stage,
        count: applications?.length,
        applications,
      };
    });
  }, [jobResponse]);

  const handleViewDetails = (
    application: Application,
    stageApplications: Application[]
  ) => {
    const index = stageApplications.findIndex(app => app.id === application.id);
    setCurrentApplicationIndex(index);
    setCurrentStageApplications(stageApplications);
    setSelectedApplication(application);
  };

  const handleNext = () => {
    if (currentApplicationIndex < currentStageApplications.length - 1) {
      const nextIndex = currentApplicationIndex + 1;
      setCurrentApplicationIndex(nextIndex);
      setSelectedApplication(currentStageApplications[nextIndex]);
    }
  };

  const handlePrev = () => {
    if (currentApplicationIndex > 0) {
      const prevIndex = currentApplicationIndex - 1;
      setCurrentApplicationIndex(prevIndex);
      setSelectedApplication(currentStageApplications[prevIndex]);
    }
  };

  const handleDownloadPDF = async () => {
    setIsDownloading(true);
    try {
      await downloadPipeline(
        jobResponse?.job.title || 'Getlinked',
        jobResponse?.job.company || 'company'
      );
    } catch (error) {
      console.error('Error downloading PDF:', error);
    } finally {
      setIsDownloading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="absolute flex min-h-screen w-full items-center justify-center bg-gray-50">
        <Loader />
      </div>
    );
  }

  if (error || !jobResponse) {
    return (
      <div className="absolute flex min-h-screen w-full items-center justify-center bg-gray-50">
        <div className="text-center">
          <h2 className="mb-2 text-xl font-semibold text-gray-900">
            Error Loading Data
          </h2>
          <p className="text-gray-600">Please try again later.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50" data-pdf-content>
      <JobHeader
        permissions={jobResponse?.permissions || []}
        job={jobResponse?.job}
        handleExport={handleDownloadPDF}
        isDownloading={isDownloading}
      />
      <div className="px-6 py-4">
        <div className="mb-6 flex items-center gap-6 border-b border-gray-200">
          <button
            className={`pb-2 font-medium ${
              tab === 'candidates'
                ? 'border-b-2 border-primary text-primary'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setTab('candidates')}
          >
            Candidates
          </button>
          <button
            className={`pb-2 font-medium ${
              tab === 'details'
                ? 'border-b-2 border-primary text-primary'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setTab('details')}
          >
            Job details
          </button>
        </div>
      </div>

      {tab === 'candidates' && (
        <div className="w-[100vw] overflow-x-auto px-6">
          <PipelineStages
            permissions={jobResponse?.permissions || []}
            stages={pipelineData}
            onViewDetails={handleViewDetails}
          />
        </div>
      )}

      {tab === 'details' && (
        <div className="">
          <JobDetailPage job={jobResponse?.job} />
        </div>
      )}

      <CandidateModal
        permissions={jobResponse?.permissions || []}
        application={selectedApplication}
        isOpen={!!selectedApplication}
        onClose={() => setSelectedApplication(null)}
        onNext={handleNext}
        onPrev={handlePrev}
        hasNext={currentApplicationIndex < currentStageApplications?.length - 1}
        hasPrev={currentApplicationIndex > 0}
      />

      <div className="fixed bottom-4 left-6 text-xs text-gray-400">
        POWERED BY <span className="font-semibold">GetLinked.AI</span>
      </div>
    </div>
  );
}
