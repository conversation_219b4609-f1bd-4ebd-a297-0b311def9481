"use client"
import Image from 'next/image'
import React from 'react'
import { CreateRequisitionsForm, PublicHeader } from './misc/components'

const PublicTalentRequisition = () => {
    return (
        <div className="w-screen bg-[#F8F9FB] md:px-5 md:py-4 grow">
            <div className="relative lg:max-xl:w-[86vw] xl:w-[72rem] mx-auto">

                <PublicHeader />

                <article className='relative flex max-sm:flex-col-reverse md:items-center justify-between bg-[#0A0048] md:rounded-2xl max-md:p-5 text-white pl-8 md:pl-8 lg:pl-12 md:py-0  max-md:m-0 overflow-y-hidden'>
                    <div className='relative md:py-6 max-md:py-10 max-md:z-10 md:max-lg:basis-1/2'>
                        <h1 className='font-medium text-2xl md:text-3xl max-w-[400px]'>
                            Ready to elevate your team with exceptional talent?
                        </h1>
                        <p className='text-white max-w-[300px] sm:max-w-md text-[0.85rem] md:text-[0.95rem] md:pt-2 md:pb-6 font-light leading-tight max-md:mt-6'>
                            Submit your talent requisition by filling out the form below and get linked to rigorously vetted professionals from around the world in no time. Discover the ease of connecting with the best global talent and watch your team thrive!
                        </p>
                    </div>

                    <div className='absolute top-0 left-0 bottom-0 right-0 md:relative max-md:top-0   max-md:right-0 w-full md:max-lg:basis-1/2 min-h-[150px] md:min-h-[280px] h-full md:max-w-[500px] max-h-[400px] max-md:[transform:scaleX(-1)] max-md:opacity-70 overflow-hidden '>
                        <Image
                            src='/assets/requistion-form-illustration.png'
                            alt='job search'
                            // width={250}
                            // height={150}
                            className='max-md:-scale-x-100'
                            fill
                            objectFit='cover'
                        />
                    </div>
                </article>

                <CreateRequisitionsForm />
            </div>
        </div>
    )
}

export default PublicTalentRequisition