import { useMutation } from '@tanstack/react-query';
import { Axios, AxiosWithNoAuth } from '@/lib/api/axios';
import { getAccessToken } from '@/utils';
import { CreateTalentRequisition } from '../types/RequisitionTypes';

const token = getAccessToken();

const RequestTalent = async (data:CreateTalentRequisition) => {
  const response = await AxiosWithNoAuth.post(`/recruiter/existing_recruiter_talent_request/`, data, {
    // headers: {
    //   Authorization: `Bearer ${token}`,
    // },
  });

  return response?.data;
};

const useRequestTalent = () => {
  return useMutation({
    mutationFn: RequestTalent,
  });
};

export default useRequestTalent