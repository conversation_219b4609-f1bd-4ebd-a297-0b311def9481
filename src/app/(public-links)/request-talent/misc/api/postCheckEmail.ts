import { AxiosWithNoAuth } from '@/lib/api/axios';
import { useMutation } from '@tanstack/react-query';
import axios from 'axios';

const checkUserEmail = async (email: string) => {
  const formData = new FormData();
  formData.append('email', email);
  const response = await AxiosWithNoAuth.post(`/recruiter/check_user_status/?email=${email}`,
    formData
  );
  return response.data;
};

 const useCheckEmail = () => {
  return useMutation({
    mutationFn: checkUserEmail,
    mutationKey: ['check-recruiter-talent-request-email'],
  });
};

export  default useCheckEmail