

export interface CreateTalentRequisition {
    email?: string;
    create_password?: string;
    name?: string;
    job_title: string;
    role_description_responsibilities: string;
    proficiency_level: "JUNIOR_LEVEL" | "MID_LEVEL" | "SENIOR_LEVEL";
    working_option: "REMOTE" | "ONSITE" | "HYBRID";
    employment_type: "FULL_TIME" | "PART_TIME" | "CONTRACT";
    location: string;
    number_of_talent: number;
    salary: number;
    requirements: string;
    industry: string;
    company_name?: string;
    company_domain?: string;
    logo?: string;
}
export interface CreateTalentRequisitionForm {
    currentStep: number;
    newUser: boolean;
    email: string;
    create_password: string;
    name: string;
    job_title: string;
    role_description_responsibilities: string;
    proficiency_level: "JUNIOR_LEVEL" | "MID_LEVEL" | "SENIOR_LEVEL";
    working_option: "REMOTE" | "ONSITE" | "HYBRID";
    employment_type: "FULL_TIME" | "PART_TIME" | "CONTRACT";
    country: string;
    state: string;
    number_of_talent: number;
    salary: number;
    requirements: string;
    industry: string;
    phone_number?: string;
    company_name?: string;
    company_domain?: string;
    logo?: File;
}


