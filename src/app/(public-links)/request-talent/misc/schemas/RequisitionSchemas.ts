import { z, ZodError, ZodIssueCode } from 'zod';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export const RequisitionSchemas = z.object({
    newUser: z.boolean(),
    currentStep: z.number(),
    name: z.string().optional(),
    phone_number: z.string().optional(),
    email: z.string().email().optional(),
    company_name: z.string().optional(),
    company_domain: z.string().optional(),
    create_password: z.string().optional(),

    logo: z.any().nullable().optional(),
    job_title: z
        .string({ required_error: 'Please enter role' })
        .min(1, { message: 'Please enter role' }),
    role_description_responsibilities: z
        .string({ required_error: 'Please enter role description and responsibilities' })
        .min(1, { message: 'Please enter role description and responsibilities' }),
    proficiency_level: z
        .enum(['ENTRY_LEVEL', 'MID_LEVEL', 'SENIOR_LEVEL', 'INTERN'], { required_error: 'Please select a proficiency level' }),
    working_option: z
        .enum(['REMOTE', 'ONSITE', 'HYBRID'], { required_error: 'Please select a working option' }),
    employment_type: z
        .enum(['FULL_TIME', 'PART_TIME', 'CONTRACT', 'VOLUNTEER', 'INTERNSHIP', 'OTHER'], { required_error: 'Please select an employment type' }),
    country: z
        .string().optional(),
    state: z.string().optional(),
    number_of_talent: z
        .number({ required_error: 'Please enter the number of talent' })
        .min(1, { message: 'Number of talent must be at least 1' }),
    salary: z
        .number({ required_error: 'Please enter a salary' })
        .min(0, { message: 'Salary must be a positive number' }),
    requirements: z
        .string({ required_error: 'Please enter job requirements' })
        .min(1, { message: 'Please enter job requirements' }),
    industry: z
        .string({ required_error: 'Please select an industry' })
        .min(1, { message: 'Please select an industry' }),
}).superRefine((data, ctx) => {
    if (data.newUser) {
        if (!data.name) {
            ctx.addIssue({
                code: ZodIssueCode.custom,
                path: ['name'],
                message: 'Name is required for new users.',
            });
        }
        if (!data.phone_number) {
            ctx.addIssue({
                code: ZodIssueCode.custom,
                path: ['phone_number'],
                message: 'Phone number is required for new users.',
            });
        }
        if (data.phone_number && data.phone_number.length < 10) {
            ctx.addIssue({
                code: ZodIssueCode.custom,
                path: ['phone_number'],
                message: 'Phone number must be at least 10 digits.',
            });
        }
        if (!data.email) {
            ctx.addIssue({
                code: ZodIssueCode.custom,
                path: ['email'],
                message: 'Email is required for new users.',
            });
        }
        if (!data.company_name) {
            ctx.addIssue({
                code: ZodIssueCode.custom,
                path: ['company_name'],
                message: 'Company name is required for new users.',
            });
        }
        if (!data.company_domain) {
            ctx.addIssue({
                code: ZodIssueCode.custom,
                path: ['company_domain'],
                message: 'Company domain is required for new users.',
            });
        }
        if (!data.create_password) {
            ctx.addIssue({
                code: ZodIssueCode.custom,
                path: ['create_password'],
                message: 'Password is required for new users.',
            });
        }
        if (!data.logo) {
            ctx.addIssue({
                code: ZodIssueCode.custom,
                path: ['logo'],
                message: 'Logo is required for new users.',
            });
        }
        if (!data.logo) {
            ctx.addIssue({
                code: ZodIssueCode.custom,
                path: ['logo'],
                message: 'Logo is required for new users.',
            });
        } else if (data.logo) {
            if (!data.logo.type.startsWith('image/')) {
                ctx.addIssue({
                    code: ZodIssueCode.custom,
                    path: ['logo'],
                    message: 'Please select an image file.',
                });
            } else if (data.logo.size > MAX_FILE_SIZE) {
                ctx.addIssue({
                    code: ZodIssueCode.custom,
                    path: ['logo'],
                    message: 'Max image size is 10MB.',
                });
            }
        }
    }

    if (data.number_of_talent <= 0) {
        ctx.addIssue({
            code: ZodIssueCode.custom,
            path: ['number_of_talent'],
            message: 'Number of talent must be greater than 0.',
        });
    }

    if (data.salary <= 0) {
        ctx.addIssue({
            code: ZodIssueCode.custom,
            path: ['salary'],
            message: 'Salary must be greater than 0.',
        });
    }
    return true
});

