"use client"
import { MouseE<PERSON> } from 'react';
import { useFormContext } from 'react-hook-form';

import { Button, LoaderBtn } from '@/components/shared';
import { cn } from '@/utils';


interface Props {
    onNext:  (e: MouseEvent<HTMLButtonElement, globalThis.MouseEvent>) => void;
    isChecking: boolean;
}

const Step1 = ({ onNext, isChecking }: Props) => {
    const { register, formState: { errors } } = useFormContext();


    return (
        <div className=''>
            <div className="inputdiv">
                <label htmlFor="name">Email</label>
                <input
                    type="text"
                    placeholder="Enter your email"
                    autoCapitalize='off'
                    id="email"
                    maxLength={100}
                    {...register('email', {
                        required: 'Email is required',
                        pattern: {
                            value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                            message: 'Invalid email address'
                        }
                    })}
                    className={cn('!bg-[#F5F7F9] !lowercase', errors.email && 'error')}
                />
                {errors.email && <p className='formerror'>{errors.email.message as string}</p>}
            </div>


            <Button onClick={(e)=>onNext(e)} className='px-10'>
                Proceed
                {
                    isChecking && <LoaderBtn />
                }
            </Button>
        </div >
    );
};

export default Step1;