"use client"

import React, { useState, useEffect, MouseEvent } from 'react';
import { useForm, FormProvider, } from 'react-hook-form';
import Link from 'next/link';

import { Button, ErrorModal, LinkButton, LoadingOverlay, Modal } from '@/components/shared';
import { zodResolver } from '@hookform/resolvers/zod';
import { useBooleanStateControl, useCloudinary, useErrorModalState } from '@/hooks';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { useLoading } from '@/lib/contexts/LoadingContext';
import { SuitcaseWhite } from '@/app/(website-main)/e/jobs/misc/icons';
import { RightUpArrow } from '@/app/(auth)/misc/icons';
import { uploadToServer } from '@/app/(public-links)/jobs/misc/components/application/upload';

import Step1 from './CreateRequisitionsFormStep1';
import Step2 from './CreateRequisitionsFormStep2';
import { RequisitionSchemas } from '../schemas/RequisitionSchemas';
import { CreateTalentRequisition, CreateTalentRequisitionForm } from '../types/RequisitionTypes';
import { useCheckEmail, useRequestTalentExistingRecruiter, useRequestTalentNewRecruiter } from '../api';
import CreateRequisitionSummaryModal from './CreateRequisitionSummaryModal';



interface CreateRequisitionsFormProps {
    editData?: CreateTalentRequisition
}

const CreateRequisitionsForm: React.FC<CreateRequisitionsFormProps> = ({ editData }) => {
    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();
    const {
        state: isSuccessModalOpen,
        setTrue: openSuccessModal,
        setFalse: closeSuccessModal,
    } = useBooleanStateControl()
    const {
        state: isSummaryModalOpen,
        setTrue: openSummaryModal,
        setFalse: closeSummaryModal,
    } = useBooleanStateControl()
    const checkBeforeSummaryOpen = async (e: any) => {
        e.preventDefault()
        const isValid = await methods.trigger();
        console.log(errors)
        if (isValid) {
            openSummaryModal()
        }
    }



    const methods = useForm<CreateTalentRequisitionForm>({
        defaultValues: {
            currentStep: 1,
            newUser: true
        },
        resolver: zodResolver(RequisitionSchemas),
    });
    const { handleSubmit, formState: { errors }, watch, getValues, trigger, setValue, setError, clearErrors } = methods;
    const currentStep = watch('currentStep');




    const { mutate: requestTalentNewRecruiter, isLoading: isRequestingTalentNewRecruiter } = useRequestTalentNewRecruiter()
    const { mutate: requestTalentExistingRecruiter, isLoading: isRequestingTalentExistingRecruiter } = useRequestTalentExistingRecruiter()
    const { uploadToServer, deleteFromCloudinary } = useCloudinary();
    const { isUploading, isDeleting } = useLoading();
    const onSubmit = async (data: CreateTalentRequisitionForm) => {
        let logoUrl
        const logoFile = watch('logo')
        let PublicId: string | undefined
        if (logoFile) {
            const data = await uploadToServer(logoFile)
            logoUrl = data.secure_url
            PublicId = data.id
        }

        const newUser = watch('newUser')
        if (newUser) {
            const dataToSubmitNewUser = {
                email: data.email,
                create_password: data.create_password,
                name: data.name,
                job_title: data.job_title,
                role_description_responsibilities: data.role_description_responsibilities,
                proficiency_level: data.proficiency_level,
                working_option: data.working_option,
                employment_type: data.employment_type,
                location: `${data.state}, ${data.country}`,
                number_of_talent: data.number_of_talent,
                salary: data.salary,
                requirements: data.requirements,
                industry: data.industry,
                company_name: data.company_name,
                company_domain: data.company_domain,
                logo: logoUrl,
            }
            requestTalentNewRecruiter(dataToSubmitNewUser, {
                onSuccess(data, variables, context) {
                    openSuccessModal()
                },
                onError(error: any) {
                    const errorMessage = formatAxiosErrorMessage(error)
                    openErrorModalWithMessage(errorMessage)
                    deleteFromCloudinary(PublicId!)
                }
            })
        }
        else {
            const dataToSubmitExistingUser = {
                email: data.email,
                job_title: data.job_title,
                role_description_responsibilities: data.role_description_responsibilities,
                proficiency_level: data.proficiency_level,
                working_option: data.working_option,
                employment_type: data.employment_type,
                location: `${data.state}, ${data.country}`,
                number_of_talent: data.number_of_talent,
                salary: data.salary,
                requirements: data.requirements,
                industry: data.industry,
            }

            requestTalentExistingRecruiter(dataToSubmitExistingUser, {
                onSuccess(data, variables, context) {
                    openSuccessModal()
                },
                onError(error: any) {
                    const errorMessage = formatAxiosErrorMessage(error)
                    openErrorModalWithMessage(errorMessage)
                }
            })
        }
    };







    const { mutate: checkEmail, isLoading: isChecking } = useCheckEmail()
    const nextStep = (e?: MouseEvent<HTMLButtonElement, globalThis.MouseEvent>) => {
        if (e) {
            e.preventDefault()
        }
        const email = watch('email')
        if (email && email.trim() !== "" && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {

            checkEmail(email, {
                onSuccess: (data) => {
                    if (data.message == "User does not exist. Kindly create an account.") {
                    }
                    else {
                        setValue('newUser', false)
                    }
                    clearErrors('email')
                    setValue('currentStep', 2)
                },
                onError: (error: any) => {
                    const errorMessage = formatAxiosErrorMessage(error)
                    openErrorModalWithMessage(errorMessage)
                }
            })
        }
        else {
            setError('email', { message: "Enter email", type: "required" })
        }

    }

    useEffect(() => {
        const subscription = watch((value, { name }) => {
            if (name === 'email' && watch('currentStep') == 2) {
                nextStep();
            }
        });
        return () => subscription.unsubscribe();
    }, [watch]);





    return (
        <FormProvider {...methods} >
            <div className='grow flex flex-col lg:grid lg:grid-cols-[minmax(0,1fr)_minmax(10rem,0.7fr)] bg-[#F8F9FB] md:bg-white gap-6  mt-1 px-3 md:px-8 md:py-4 xl:overflow-y-hidden'>
                <form onSubmit={handleSubmit(onSubmit)} className='bg-white rounded-2xl overflow-y-visible p-4 md:pb-0 ' id="create-job-form">
                    {currentStep == 1 && <Step1 onNext={nextStep} isChecking={isChecking} />}
                    {currentStep == 2 && <Step2 openSummaryModal={checkBeforeSummaryOpen} />}
                </form>
                <div className='flex flex-col gap-3 bg-[#F8F9FB] max-md:bg-white p-4 max-md:mt-10 rounded-2xl overflow-y-scroll'>
                    <div className='max-md:bg-[#F8F9FB] bg-white rounded-2xl h-max max-h-[300px] md:p-2.5'>
                        <article className='bg-gradient-to-b from-[#E5EAFF] to-[#FFEBF3] h-full rounded-2xl p-4 md:p-6'>
                            <h1 className='font-medium text-lg'>
                                Post jobs to numerous boards
                            </h1>
                            <p className='text-[0.785rem] text-[#62717A]'>
                                Create job, get them posted to numerous job boards and move applicants within your job pipeline, conduct interviews, assessments and hire with ease.
                            </p>
                            <Link href="/register/recruiter" className='flex items-center justify-between mt-12 md:mt-10 '>
                                <p className='underline text-lg hover:[text-decoration-thickness:2.05px]'>
                                    Get Started
                                </p>
                                <span className='flex items-center justify-center h-8 w-8 p-1 bg-white rounded-full'>
                                    <RightUpArrow fill="black" />
                                </span>
                            </Link>
                        </article>
                    </div>

                    <div className='max-md:bg-[#F8F9FB] bg-white rounded-2xl h-max max-h-[300px] md:p-2.5'>
                        <article className='bg-gradient-to-b from-[#E5EAFF] to-[#FFEBF3] h-full rounded-2xl p-4 md:p-6'>
                            <h1 className='font text-lg'>
                                Post jobs to numerous boards
                            </h1>
                            <p className='text-[0.785rem] text-[#62717A]'>
                                Create and conduct highly proctored assessment  on Getlinked with well curated questions and send out assessment invite to candidates to get the best talent for every position
                            </p>
                            <Link href="/register/recruiter" className='flex items-center justify-between mt-12 md:mt-10 '>
                                <p className='underline text-lg hover:[text-decoration-thickness:2.05px]'>
                                    Get Started
                                </p>
                                <span className='flex items-center justify-center h-8 w-8 p-1 bg-white rounded-full'>
                                    <RightUpArrow fill="black" />
                                </span>
                            </Link>
                        </article>
                    </div>

                    <div className='max-md:bg-[#F8F9FB] bg-white rounded-2xl h-max max-h-[300px] md:p-2.5'>
                        <article className='bg-gradient-to-b from-[#FFEAFC] to-[#E8FFFA] h-full rounded-2xl p-4 md:p-6'>
                            <h1 className='font text-lg'>
                                Get access to top professionals
                            </h1>
                            <p className='text-[0.785rem] text-[#62717A]'>
                                Employee with ease when you onboard and get access to pick well vetted candidates from the Getlinked global talent pool</p>
                            <Link href="/register/recruiter" className='flex items-center justify-between mt-12 md:mt-10 '>
                                <p className='underline text-lg hover:[text-decoration-thickness:2.05px]'>
                                    Get Started
                                </p>
                                <span className='flex items-center justify-center h-8 w-8 p-1 bg-white rounded-full'>
                                    <RightUpArrow fill="black" />
                                </span>
                            </Link>
                        </article>
                    </div>
                </div>
            </div>



            <Modal
                isModalOpen={isSuccessModalOpen}
                heading='Success'
                closeModal={() => { closeSummaryModal(); closeSuccessModal() }}
                color='white'
                body={
                    <div className='flex flex-col items-center justify-center gap-4 '>
                        <div className='self-start  rounded-full bg-primary p-4'>
                            <SuitcaseWhite />
                        </div>
                        <div>
                            <h3 className='font-medium text-xl text-primary'>Requisition submitted successfully</h3>
                            <p className='text-[#8C8CA1] text-[0.875rem]'>
                                Your talent requisition has been submitted successfully. A confirmation
                                mail have be sent to <span className='font-bold text-[#0E0E2C]'>{getValues().email}</span>  to confirm requisition and
                                commence sourcing.
                            </p>
                            <div className='text-helper-text text-[0.8125rem]'>
                                <p>Want to learn more about our service offering ?</p>
                                <p>Visit: <span className='text-header-text'>Getlinked.AI</span></p>
                                <p>Contact us: <span className='text-header-text'><EMAIL></span></p>
                            </div>
                        </div>
                    </div>
                }

                footer={
                    <div className='w-full flex items-center justify-end gap-4 ml-auto bg-white p-5 rounded-[1rem]'>
                        <Button onClick={() => { closeSummaryModal(); closeSuccessModal() }} variant="white">
                            New requisition
                        </Button>
                        <LinkButton href="/" variant='light' size='thin' className='rounded-[0.6rem] !py-[0.325rem] focus:outline-none outline-none'>
                            Okay
                        </LinkButton>
                    </div>
                }
            />

            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={
                    errorModalMessage || 'Please check your inputs and try again.'
                }
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button
                        className="grow bg-red-950 hover:border-red-950 hover:text-red-950 px-1.5 sm:text-sm md:px-6"
                        type="button"
                        onClick={closeErrorModal}
                    >
                        Okay
                    </Button>
                </div>
            </ErrorModal>


            <CreateRequisitionSummaryModal isModalOpen={isSummaryModalOpen} closeModal={closeSummaryModal} confirmSubmit={handleSubmit(onSubmit)} values={getValues()} />
            <LoadingOverlay isOpen={isRequestingTalentNewRecruiter || isRequestingTalentExistingRecruiter || isUploading} />
        </FormProvider>
    );
};

export default CreateRequisitionsForm;