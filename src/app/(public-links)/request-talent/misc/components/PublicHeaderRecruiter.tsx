'use client'
import React, { useEffect, useState } from 'react'
import Link from 'next/link'
import { useParams, usePathname, useRouter, useSearchParams } from 'next/navigation'
import toast from 'react-hot-toast'

import { useUser } from '@/lib/contexts/UserContext'
import { Avatar, Button, Collapsible, CollapsibleContent, CollapsibleTrigger, Drawer, Drawer<PERSON>ontent, DrawerTrigger, LinkButton, Popover, PopoverContent, PopoverTrigger } from '@/components/shared'
import { CaretDown, GetLinkedWhiteLogo, Hamburger, Settings } from '@/components/shared/icons'
import { RightUpArrow, UserIcon } from '@/app/(auth)/misc/icons'
import Suitcase from '@/app/(website-main)/e/jobs/misc/icons/Suitcase'


const JobMediaHeader = () => {
  const { user, userInfo, isLoading: isLoadingUserData } = useUser()
  const [validSession, setValidSession] = useState(true)


  const router = useRouter()
  const params: any = useParams()



  return (
    <div className={`sticky top-0 z-20 flex flex-col border-b-[0.2px] border-[#F0EDFE] md:border-none shadow-sm  md:flex-row justify-between items-center md:mb-6 py-4 md:py-5 px-6 md:px-[2.12rem] md:h-full bg-white md:rounded-[0.625rem] shrink-0`}>
      <div className="flex w-full justify-between items-center">
        <Link href='/'>
          <h2 className='text-black text-2xl font-bold'>Getlinked.AI</h2>
        </Link>
        {/* <div className="hidden md:flex items-center gap-x-[1.31rem]  ">
          <button className='text-[0.875rem] '>About</button>
          {
            !isLoadingUserData && user && userInfo && validSession ? (
              <Popover>
                <PopoverTrigger className='flex items-center justify-center gap-1'>
                  <Avatar
                    src={userInfo?.profile_picture}
                    alt={`${user?.first_name} ${user?.last_name} profile picture`}
                    fallback={`${user?.first_name?.slice(0, 1)}${user?.last_name?.slice(0, 1)}`}
                    fallbackClass="bg-black text-white"
                  />
                  <CaretDown fill="black" />
                </PopoverTrigger>

                <PopoverContent align="end" className='z-50 flex flex-col gap-2 mt-4 p-1.5 w-max !max-h-96 text-[#272727] bg-white/70 shadow-md  rounded-lg text-[0.895rem] '>
                  <Link href={user?.user_type == "TALENT" ? "/t/dashboard" : "/e/dashboard"} className='flex w-full items-center gap-2 rounded-lg pl-3 pr-12 py-2 text-sm bg-white/70 hover:bg-white hover:text-[#212121]'>
                    <ProfileIcon stroke='black' className="h-5 w-5" aria-hidden="true" />
                    Dashboard
                  </Link>
                  <Link href={user?.user_type == "TALENT" ? "/t/profile" : "/e/profile"} className='flex w-full items-center gap-2 rounded-lg pl-3 pr-12 py-2 text-sm bg-white/70 hover:bg-white hover:text-[#212121]'>
                    <Settings fill='black' className="h-5 w-5" aria-hidden="true" />
                    Profile
                  </Link>
                </PopoverContent>
              </Popover>
            )
              :
              (
                <div className="flex items-center gap-x-1 bg-[#F8F9FB] h-[3rem] shrink-0 rounded-[3.125rem] p-1">
                  <LinkButton
                    href="/login/talent"
                    icon={<span className='p-0.5'><RightUpArrow width={12} height={12} fill="white" /></span>}
                    iconPosition='after'
                    size="capsule"
                    variant="default"
                  >
                    <span className='pr-2 py-0.5'>

                      Signin
                    </span>
                  </LinkButton>
                  <Link href={"/register/talent"} >
                    <button className={`w-[6.875rem] h-[2.5rem] rounded-full text-[0.75rem] flex items-center justify-between px-4 font-medium shrink-0 active:bg-black active:text-white bg-white text-black}`} >Signup <RightUpArrow fill={"#0E0E2C"} /></button>
                  </Link>
                </div>
              )
          }

        </div> */}

        <Drawer>
          <DrawerTrigger className='lg:hidden' asChild>
            <Button size='capsule' variant='unstyled'>
              <Hamburger/>
            </Button>
          </DrawerTrigger>

          <DrawerContent className='bg-black text-white px-6 py-4'>
            <section className='flex items-center text-white font-clash md:text-lg max-sm:!w-[150px] mb-8'>
              <GetLinkedWhiteLogo />
            </section>

            <Collapsible
              className='group/collapsible1'
            >
              <CollapsibleTrigger className="flex items-center w-full gap-4 rounded-lg py-3 text-left text-[1.05rem] font-medium text-white">
                Register
                <CaretDown className='group-data-[state="open"]/collapsible1:rotate-180 ml-auto' height={20} fill='white' />
              </CollapsibleTrigger>
              <CollapsibleContent className="flex flex-col gap-3">
                <Link href='/register/recruiter' className='flex items-start gap-4 p-4 rounded-lg hover:text-white bg-[#212121] hover:backdrop-blur-lg '>
                  <div className='block grow items-start justify-center bg-white p-3 rounded-full'>
                    <Suitcase width={18} height={18} fill='#272727' />
                  </div>

                  <div className='flex flex-col'>
                    <h3 className='text-white font-medium text-[0.92rem]'>Recruiter</h3>
                    <p className='text-sm text-white/80'>
                      Gain access to a vast talent pool of top professionals,
                      coupled with world-class tools designed to streamline your entire recruitment process.
                    </p>
                  </div>
                </Link>
                <Link href='/register/talent' className='flex items-start gap-4 p-4 rounded-lg hover:text-white bg-[#212121] hover:backdrop-blur-lg '>
                  <div className='block grow items-start justify-center bg-white p-3 rounded-full'>
                    <UserIcon width={18} height={18} stroke='#272727' />
                  </div>
                  <div className='flex flex-col'>
                    <h3 className='text-white font-medium text-[0.92rem]'>Talent</h3>
                    <p className='text-sm text-white/80'>
                      Create a talent account and gain access to exciting career openings tailored to your unique skills and aspirations,
                      with tools to showcase your talents, review your resume and prepare for interviews.
                    </p>
                  </div>
                </Link>

              </CollapsibleContent>
            </Collapsible>

            <Collapsible
              className='group/collapsible1'
            >
              <CollapsibleTrigger className="flex items-center w-full gap-4 rounded-lg py-3 text-left text-[1.05rem] font-medium text-white">
                Login
                <CaretDown className='group-data-[state="open"]/collapsible1:rotate-180 ml-auto' height={20} fill='white' />
              </CollapsibleTrigger>
              <CollapsibleContent className="flex flex-col gap-3">
                <Link href='/login/recruiter' className='flex items-start gap-4 p-4 rounded-lg hover:text-white bg-[#212121] hover:backdrop-blur-lg '>
                  <div className='block items-start justify-center bg-white p-3 rounded-full'>
                    <Suitcase width={18} height={18} fill='#272727' />
                  </div>

                  <div className='flex flex-col'>
                    <h3 className='text-white font-medium text-[0.92rem]'>Recruiter</h3>
                    <p className='text-sm text-white/80'>
                      Login to your recruiter account.
                    </p>
                  </div>
                </Link>
                <Link href='/login/talent' className='flex items-start gap-4 p-4 rounded-lg hover:text-white bg-[#212121] hover:backdrop-blur-lg '>
                  <div className='block items-start justify-center bg-white p-3 rounded-full'>
                    <UserIcon width={18} height={18} stroke='#272727' />
                  </div>
                  <div className='flex flex-col'>
                    <h3 className='text-white font-medium text-[0.92rem]'>Talent</h3>
                    <p className='text-sm text-white/80'>
                      Login to your talent account.
                    </p>
                  </div>
                </Link>

              </CollapsibleContent>
            </Collapsible>


          </DrawerContent>

        </Drawer>
      </div>


    </div>
  )
}

export default JobMediaHeader