import { Button, Modal } from '@/components/shared'
import React from 'react'
import { CreateTalentRequisitionForm } from '../types/RequisitionTypes';
import { convertKebabAndSnakeToTitleCase } from '@/utils/strings';
import ReactTextareaAutosize from 'react-textarea-autosize';

interface CreateRequisitionSummaryModalProps {
    isModalOpen: boolean;
    closeModal: () => void;
    confirmSubmit: () => void;
    values: CreateTalentRequisitionForm;
}

const CreateRequisitionSummaryModal: React.FC<CreateRequisitionSummaryModalProps> = ({ isModalOpen, closeModal, confirmSubmit, values }) => {
    const { currentStep, newUser, email, phone_number, create_password, name, job_title, role_description_responsibilities, proficiency_level, working_option, employment_type, country, state, number_of_talent, salary, requirements, industry, company_name, company_domain, logo } = values
    return (
        <Modal heading='Confirmation' isModalOpen={isModalOpen} closeModal={closeModal} color='purple' bodyClass="!px-0 !pt-0">
            <section className='mb-2.5 p-6 bg-white'>
                <h3 className='text-header-text font-medium text-xl'>Confirm request detail</h3>
                <p className='text-body-text text-sm'>Kindly confirm request detail before submitting as details provided would play a vital role in the sourcing process</p>
            </section>

            <section className='bg-white p-4 md:px-8 md:py-6 md:rounded-lg md:mx-6' >
                <div className='grid md:grid-cols-2 gap-6 '>


                    <p className='flex flex-col text-[0.925rem]'>
                        <span className='text-header-text font-medium'>{job_title}</span>
                        <span className='text-helper-text text-xs'>Role</span>
                    </p>
                    <p className='flex flex-col text-[0.925rem]'>
                        <span className='text-header-text font-medium'>{convertKebabAndSnakeToTitleCase(proficiency_level)}</span>
                        <span className='text-helper-text text-xs'>Proficiency level</span>
                    </p>
                    <p className='flex flex-col text-[0.925rem]'>
                        <span className='text-header-text font-medium'>{convertKebabAndSnakeToTitleCase(working_option)}</span>
                        <span className='text-helper-text text-xs'>Work mode</span>
                    </p>
                    <p className='flex flex-col text-[0.925rem]'>
                        <span className='text-header-text font-medium'>{convertKebabAndSnakeToTitleCase(employment_type)}</span>
                        <span className='text-helper-text text-xs'>Employment type</span>
                    </p>
                    <p className='flex flex-col text-[0.925rem]'>
                        <span className='text-header-text font-medium'>{`${state}${state && `, ${country}`}`}</span>
                        <span className='text-helper-text text-xs'>Location</span>
                    </p>
                    <p className='flex flex-col text-[0.925rem]'>
                        <span className='text-header-text font-medium'>{number_of_talent}</span>
                        <span className='text-helper-text text-xs'>No of talents</span>
                    </p>
                    <p className='flex flex-col text-[0.925rem]'>
                        <span className='text-header-text font-medium'>{industry}</span>
                        <span className='text-helper-text text-xs'>Industry</span>
                    </p>

                    {
                        newUser &&
                        <>
                            <p className='flex flex-col text-[0.925rem]'>
                                <span className='text-header-text font-medium'>{name}</span>
                                <span className='text-helper-text text-xs'>Name</span>
                            </p>
                            <p className='flex flex-col text-[0.925rem]'>
                                <span className='text-header-text font-medium'>{phone_number}</span>
                                <span className='text-helper-text text-xs'>Phone number</span>
                            </p>
                            <p className='flex flex-col text-[0.925rem]'>
                                <span className='text-header-text font-medium'>{company_name}</span>
                                <span className='text-helper-text text-xs'>Company Name</span>
                            </p>
                            <p className='flex flex-col text-[0.925rem]'>
                                <span className='text-header-text font-medium'>{email}</span>
                                <span className='text-helper-text text-xs'>Email</span>
                            </p>
                        </>
                    }

                </div>
                {
                    role_description_responsibilities &&
                    <div className=" mt-6 mb-1">
                        <p className='text-header-text text-[0.925rem] font-medium'>Description</p>
                        <ReactTextareaAutosize value={role_description_responsibilities!} className='w-full bg-transparent text-[0.8rem] text-body-text resize-none' disabled />
                    </div>
                }
                {
                    requirements &&
                    <div className=" mt-6 mb-1">
                        <p className='text-header-text text-[0.925rem] font-medium'>Requirements </p>
                        <ReactTextareaAutosize value={requirements!} className='w-full bg-transparent text-[0.8rem] text-body-text resize-none' disabled />
                    </div>
                }



                <div className='flex items-center gap-5 mt-10 w-full' >
                    <Button size="thin" className='px-8 min-w-max' onClick={confirmSubmit} type='submit'>
                        Complete request
                    </Button>

                    <Button variant="unstyled" className="text-primary hover:bg-grey px-8 " size="thin" onClick={closeModal}>
                        Edit
                    </Button>
                </div>
            </section>
        </Modal>
    )
}

export default CreateRequisitionSummaryModal