"use client"
import { MouseEvent, useEffect, useState } from 'react';
import TextareaAutosize from 'react-textarea-autosize';
import { useFormContext } from 'react-hook-form';

import { Select, RadioGroup, Button, LoaderBtn, SelectSingleCombo, FileUploader } from '@/components/shared';
import { EyeSlash } from '@/components/shared/icons';
import { cn } from '@/utils';
import { useUser } from '@/lib/contexts/UserContext';
import { removeSpecialCharacters } from '@/utils/numbers';

import { industries, job_type, proficiency_levels, work_experiences, working_options } from '@/app/(website-main)/e/jobs/misc/constants';
import { useGenerateResponsibilities } from '@/app/(website-main)/e/jobs/misc/api';
import { Country, State, ICountry, IState } from 'country-state-city';
import { ViewIcon } from '@/app/(auth)/misc/icons';


interface CustomFormErrors {
    job_custom_fields?: Array<{ fieldTitle?: { message?: string }, fieldValue?: { message: string }, }>;
}
interface Props {
    openSummaryModal: (e: any) => void;
}


const Step2: React.FC<Props> = ({ openSummaryModal }) => {
    const { userInfo } = useUser()
    const [showPassword, setShowPassword] = useState(false)
    const [countryList, setCountryList] = useState<ICountry[]>()
    const [countryCode, setCountryCode] = useState("")
    const [stateList, setStateList] = useState<IState[]>()
    useEffect(() => {
        setCountryList(Country?.getAllCountries())
    }, [])

    useEffect(() => {
        setStateList(State?.getStatesOfCountry(String(countryCode)))
    }, [countryCode])



    const countryOptions = countryList?.map(country => ({ value: country?.name, label: country.name, code: country?.isoCode }))
    const stateOptions = stateList?.map(state => ({ value: state?.name, label: state.name, code: state?.isoCode }))



    const { formState: { errors }, register, setValue, clearErrors, setError, watch, control, trigger } = useFormContext();
    const customErrors = useFormContext().formState.errors as CustomFormErrors;

    const { mutate: generateResponsibilities, isLoading: isGeneratingResponsibilities } = useGenerateResponsibilities();
    const generateJobDescription = async (e: MouseEvent<HTMLButtonElement, globalThis.MouseEvent>) => {
        e.preventDefault()
        const job_title = watch('job_title')
        const industry = watch('industry')
        if (!job_title || !industry) {
            if (job_title == undefined || job_title == "") {
                setError('job_title', { message: 'Please enter role to generate role description' })
            }
            if (industry == undefined || industry == "") {
                setError('industry', { message: 'Please select industry to generate role description' })
            }
            setError('role_description_responsibilities', { message: 'Please select industry and enter role name to generate role description' })
            return
        }
        else {

            const data = { job_title, industry }
            clearErrors(["industry, name, role_description_responsibilities", "requirements"])
            generateResponsibilities(data, {
                onSuccess: (data) => {
                    const { job_description, job_requirements, job_responsibilities } = data
                    console.log(job_description, job_requirements, job_responsibilities)
                    setValue('role_description_responsibilities', job_description);
                    setValue('requirements', job_requirements.join("\n"));
                    clearErrors(["industry, name, role_description_responsibilities", "requirements", "responsibilities"])
                }
            })
        }
    }


    const [editedFields, setEditedFields] = useState<Set<string>>(new Set());
    useEffect(() => {
        const subscription = watch(() => {
            const editedFieldsArray = Array.from(editedFields);
            editedFieldsArray.forEach(async (fieldName) => {
                await trigger(fieldName);
            });
        });
        return () => subscription.unsubscribe();
    }, [watch, trigger, editedFields]);

    function handleChange(fieldName: string, value: string | Date) {
        setValue(fieldName, value);
        setEditedFields((prevEditedFields) => {
            const newEditedFields = new Set(prevEditedFields);
            newEditedFields.add(fieldName);
            return newEditedFields;
        });
        trigger(fieldName); // Trigger validation for the changed field
    }





    return (
        <div className=''>
            <header className='mb-8'>
                <h4 className='text-header-text text-[1.125rem] font-medium'>Create talent request</h4>
                <p className='text-body-text  text-[0.875rem]'>Enter details of the type of talent you would like us to source for you</p>
            </header>

            <div className="inputdiv">
                <label htmlFor="job_title">Role</label>
                <input
                    type="text"
                    placeholder="Enter role name"
                    id="name"
                    maxLength={100}
                    value={watch("job_title")}
                    onChange={(e) => handleChange('job_title', e.target.value)}
                    className={cn('!bg-[#F5F7F9]', errors.job_title && 'error')}
                />
                {errors.job_title && <p className='formerror'>{errors.job_title.message as string}</p>}
            </div>


            <fieldset className='max-md:space-y-[0.875rem] md:grid grid-cols-2 gap-x-12'>
                <SelectSingleCombo
                    name="industry"
                    label="Industry"
                    value={watch('industry')}
                    onChange={(value) => handleChange('industry', value)}
                    options={industries! || []}
                    valueKey='value'
                    labelKey='name'
                    className='!bg-[#F5F7F9]'
                    placeholder="Select industry"
                    hasError={!!errors.industry}
                    errorMessage={errors.industry?.message as string}
                    containerClass='!mt-2'
                    itemClass="text-[0.8rem]"
                    fullWidth
                />

                <Select
                    label="Experience"
                    {...register("work_experience")}
                    onChange={(value) => handleChange('work_experience', value)}
                    options={work_experiences}
                    labelKey='name'
                    valueKey='value'
                    placeholder="Select required experience"
                    containerClass='!mt-2'
                    hasError={!!errors.work_experience}
                    errorMessage={errors.work_experience?.message as string}
                />
            </fieldset>


            <div className='inputdiv'>
                <div className='flex items-center justify-between flex-wrap'>
                    <label htmlFor="name">Role Description</label>
                    <Button variant='light' size='capsule' type='button'
                        onClick={(e) => generateJobDescription(e)} icon={isGeneratingResponsibilities && <LoaderBtn />}
                    >
                        Generate Role Description/Requirements
                    </Button>
                </div>

                <TextareaAutosize
                    className={cn('!bg-[#F5F7F9] resize-none', errors.role_description_responsibilities && 'error')}
                    minRows={5}
                    maxRows={16}
                    value={watch('role_description_responsibilities')}
                    onChange={(e) => handleChange('role_description_responsibilities', e.target.value)}
                    placeholder='Enter role description'
                    disabled={isGeneratingResponsibilities}
                    title={isGeneratingResponsibilities ? "generating role description..." : ""}
                />
                {errors.role_description_responsibilities && <p className='formerror'>{String(errors.role_description_responsibilities.message)}</p>}
            </div>



            <div className='inputdiv'>
                <label htmlFor="name">Requirements </label>
                <TextareaAutosize
                    className={cn('!bg-[#F5F7F9] resize-none', errors.requirements && 'error')}
                    minRows={5}
                    maxRows={16}
                    value={watch('requirements')}
                    onChange={(e) => handleChange('requirements', e.target.value)}
                    placeholder='State the requirements you want in your preffered candidates'
                />
                {errors.requirements && <p className='formerror'>{String(errors.requirements.message)}</p>}
            </div>





            <fieldset className='max-md:space-y-[0.875rem] md:grid grid-cols-2 gap-x-12'>
                <Select
                    label="Proficiency Level"
                    {...register("proficiency_level")}
                    onChange={(value) => handleChange('proficiency_level', value)}
                    options={proficiency_levels}
                    labelKey='name'
                    valueKey='value'
                    placeholder="Select proficiency level"
                    hasError={!!errors.proficiency_level}
                    errorMessage={errors.proficiency_level?.message as string}
                    containerClass='!mt-2'
                    fullWidth
                />

                <Select
                    label="Working Option"
                    {...register("working_option")}
                    onChange={(value) => handleChange('working_option', value)}
                    options={working_options}
                    labelKey='name'
                    valueKey='value'
                    placeholder="Select work option"
                    hasError={!!errors.working_option}
                    errorMessage={errors.working_option?.message as string}
                    containerClass='!mt-2'
                    fullWidth
                />
            </fieldset>


            <div className="flex flex-col mt-4">
                <label className='text-header-text text-sm'>Location you want to hire from</label>
                <div className="grid w-full flex-col sm:grid-cols-2 items-center sm:gap-x-12">
                    <SelectSingleCombo
                        name='country'
                        placeholder="Select Country"
                        value={watch("country")}
                        onChange={(val) => {
                            const chosen = countryOptions && countryOptions.filter((country) => country.value.toLowerCase() == val.toLocaleLowerCase())[0]
                            setValue("country", chosen?.value!)
                            countryOptions && setCountryCode(chosen?.code!)
                        }}
                        className='bg-[#f5f7f9]'
                        options={countryOptions! || []}
                        labelKey={'label'}
                        valueKey='value'
                        hasError={!!errors.country}
                        errorMessage={errors.country?.message as string}
                    />

                    <SelectSingleCombo
                        name='state'
                        placeholder="Select state"
                        value={watch("state")}
                        valueKey='value'
                        labelKey='label'
                        onChange={(val) =>
                            setValue("state", val)
                        }
                        className='bg-[#f5f7f9]'
                        options={stateOptions || []}
                        hasError={!!errors.state}
                        errorMessage={errors.state?.message as string}
                        isLoadingOptions={!stateOptions || watch('country') == undefined}

                    />
                </div>
            </div>


            <RadioGroup
                options={job_type}
                onChange={(value) => setValue('employment_type', value)}
                label="Employment Type"
                errors={errors}
                value={watch('employment_type')}
                name='employment_type'
                variant="light"
                arrangement='row'
            />


            <div className="inputdiv">
                <label className='' htmlFor="salary">Salary</label>
                <input
                    type="number"
                    id="fixed_salary"
                    placeholder="Enter salary amount"
                    onChange={(e) => {
                        if (!isNaN(parseInt(removeSpecialCharacters(e.target.value)))) {
                            setValue('salary', parseInt(removeSpecialCharacters(e.target.value)))
                        }
                        else {
                            setValue('salary', 0)
                        }
                        trigger('salary')
                    }}
                    className={cn('!bg-[#F5F7F9]', errors.salary && 'error')}
                />
                {errors.salary && <p className='formerror'>{String(errors.salary.message)}</p>}
            </div>



            <div className="inputdiv">
                <label htmlFor="number_of_talent">How many talents do you want for this role ?</label>
                <input
                    type="number"
                    placeholder="Enter number of talents needed"
                    id="number_of_talent"
                    maxLength={100}
                    value={watch("number_of_talent")}
                    onChange={(e) => {
                        if (!isNaN(parseInt(removeSpecialCharacters(e.target.value)))) {
                            setValue('number_of_talent', parseInt(removeSpecialCharacters(e.target.value)))
                        }
                        else {
                            setValue('number_of_talent', 0)
                        }
                        trigger('number_of_talent')
                    }}
                    className={cn('!bg-[#F5F7F9]', errors.number_of_talent && 'error')}
                />
                {errors.number_of_talent && <p className='formerror'>{errors.number_of_talent.message as string}</p>}
            </div>





            {
                watch('newUser') == true && (
                    <section>
                        <header className='mt-10 mb-4'>
                            <h3 className='text-lg font-medium'>Your Info</h3>
                            <p className='text-sm text-body-text'>Provide details below so we can notify you of your requisition status</p>
                        </header>

                        <div>
                            <div className="inputdiv">
                                <label htmlFor="name">Name</label>
                                <input
                                    type="text"
                                    placeholder="Enter your name"
                                    id="name"
                                    maxLength={100}
                                    value={watch("name")}
                                    onChange={(e) => handleChange('name', e.target.value)}
                                    className={cn('!bg-[#F5F7F9]', errors.name && 'error')}
                                />
                                {errors.name && <p className='formerror'>{errors.name.message as string}</p>}
                            </div>
                            <div className="inputdiv">
                                <label htmlFor="phone_number">Phone number</label>
                                <input
                                    type="number"
                                    placeholder="Enter your phone number"
                                    id="phone_number"
                                    maxLength={100}
                                    value={watch("phone_number")}
                                    onChange={(e) => handleChange('phone_number', e.target.value)}
                                    // onChange={(e) => {
                                    //     if (!isNaN(parseInt(removeSpecialCharacters(e.target.value)))) {
                                    //         setValue('number_of_talent', parseInt(removeSpecialCharacters(e.target.value)))
                                    //     }
                                    //     else {
                                    //         setValue('number_of_talent', 0)
                                    //     }
                                    //     trigger('salary')
                                    // }}
                                    className={cn('!bg-[#F5F7F9]', errors.phone_number && 'error')}
                                />
                                {errors.phone_number && <p className='formerror'>{errors.phone_number.message as string}</p>}
                            </div>
                            <div className="inputdiv">
                                <label htmlFor="name">Email</label>
                                <input
                                    type="email"
                                    placeholder="Enter your email"
                                    id="email"
                                    maxLength={100}
                                    value={watch("email")}
                                    onChange={(e) => handleChange('email', e.target.value)}
                                    className={cn('!bg-[#F5F7F9]', errors.email && 'error')}
                                />
                                {errors.email && <p className='formerror'>{errors.email.message as string}</p>}
                            </div>
                            <div className="inputdiv">
                                <label htmlFor="name">Company Name</label>
                                <input
                                    type="text"
                                    placeholder="Enter your company name"
                                    id="company_name"
                                    maxLength={100}
                                    value={watch("company_name")}
                                    onChange={(e) => handleChange('company_name', e.target.value)}
                                    className={cn('!bg-[#F5F7F9]', errors.company_name && 'error')}
                                />
                                {errors.company_name && <p className='formerror'>{errors.company_name.message as string}</p>}
                            </div>
                            <div className="inputdiv">
                                <label htmlFor="name">Company Domain</label>
                                <input
                                    type="text"
                                    placeholder="Enter your company email domain e.g getlinked.com"
                                    id="company_domain"
                                    maxLength={100}
                                    value={watch("company_domain")}
                                    onChange={(e) => handleChange('company_domain', e.target.value)}
                                    className={cn('!bg-[#F5F7F9]', errors.company_domain && 'error')}
                                />
                                {errors.company_domain && <p className='formerror'>{errors.company_domain.message as string}</p>}
                            </div>

                            <div className="inputdiv">
                                <p className="text-sm text-header-text">Company Logo</p>

                                <FileUploader
                                    control={control}
                                    name="logo"
                                    label="logo"
                                    acceptedFormats={`image/*`}
                                    acceptedFileExtensions={"png, jpg, gif"}
                                    maxSize={10}
                                    color="default"
                                    className='my-2'
                                    uploadTextClassName="text-xs"
                                    errors={errors}
                                    mini
                                />
                            </div>
                        </div>


                        <div className='inputdiv '>
                            <label className='flex flex-col' htmlFor="password">
                                <span>
                                    Create password
                                </span>
                                <span className='text-[#4A4A68] text-xs mb-2'>
                                    Create a password to access Getlinked and view your requisition status
                                </span>
                            </label>
                            <div className='relative'>
                                <input
                                    type={showPassword ? 'text' : 'password'}
                                    placeholder="min 8 characters"
                                    className={cn("!bg-[#f5f7f9]", errors.create_password && "error", "")}
                                    onChange={(e) => handleChange('create_password', e.target.value)}
                                    id="password"
                                />
                                <span className="absolute right-[3%] top-[25%] cursor-pointer" onClick={() => setShowPassword(!showPassword)} >
                                    {
                                        showPassword ?
                                            <EyeSlash fill='black' />
                                            :
                                            <ViewIcon fill='black' />
                                    }
                                </span>
                            </div>
                            {errors.create_password && <p className='formerror'>{errors.create_password.message as string}</p>}
                        </div>

                    </section>
                )
            }

            <Button className='px-10 mt-8' onClick={openSummaryModal}>
                Submit Request
            </Button>

        </div >
    );
};

export default Step2;