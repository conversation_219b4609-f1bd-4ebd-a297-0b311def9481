{"name": "getlinked", "version": "0.1.0", "engines": {"node": "20.x"}, "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@floating-ui/react": "^0.26.4", "@headlessui/react": "^1.7.17", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.3.4", "@mediapipe/camera_utils": "^0.3.1675466862", "@mediapipe/face_detection": "^0.4.1646425229", "@monaco-editor/react": "^4.6.0", "@mui/material": "^7.3.2", "@mui/system": "^7.3.2", "@mui/x-date-pickers": "^8.11.0", "@progress/kendo-drawing": "^1.20.1", "@progress/kendo-file-saver": "^1.1.2", "@progress/kendo-licensing": "^1.7.1", "@progress/kendo-react-common": "^12.0.1", "@progress/kendo-react-pdf": "^8.0.0", "@progress/kendo-svg-icons": "^4.5.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^1.0.7", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.0.7", "@react-pdf/renderer": "^3.3.8", "@sentry/nextjs": "^8.54.0", "@smastrom/react-rating": "^1.5.0", "@tailwindcss/container-queries": "^0.1.1", "@tanstack/react-query": "^4.35.3", "@tanstack/react-query-devtools": "^4.35.3", "@tanstack/react-table": "^8.20.5", "@tensorflow-models/coco-ssd": "^2.2.3", "@tensorflow-models/face-detection": "^1.0.2", "@tensorflow/tfjs": "^4.20.0", "@tiptap/extension-color": "^2.9.1", "@tiptap/extension-link": "^2.9.1", "@tiptap/extension-placeholder": "^2.9.1", "@tiptap/extension-text-align": "^2.9.1", "@tiptap/extension-underline": "^2.9.1", "@tiptap/react": "^2.9.1", "@tiptap/starter-kit": "^2.9.1", "@types/dompurify": "^3.0.5", "@types/react-color": "^3.0.12", "@types/react-datepicker": "^4.19.4", "@types/react-responsive-masonry": "^2.1.3", "@types/react-slider": "^1.3.3", "@types/react-star-rating-component": "^1.4.3", "@vladmandic/face-api": "^1.7.14", "autoprefixer": "10.4.15", "axios": "^1.7.2", "class-variance-authority": "^0.7.0", "cloudinary": "^1.41.3", "cloudinary-react": "^1.8.1", "clsx": "^2.0.0", "cmdk": "^0.2.0", "country-state-city": "^3.2.1", "crypto": "^1.0.1", "date-fns": "^3.1.0", "date-fns-tz": "^3.2.0", "dayjs": "^1.11.18", "dexie": "^4.0.11", "dexie-react-hooks": "^1.1.7", "dompurify": "^3.0.6", "dotenv": "^16.4.5", "eslint": "8.49.0", "eslint-config-next": "13.4.19", "face-api.js": "^0.22.2", "file-saver": "^2.0.5", "filesize": "^10.0.12", "framer-motion": "^11.11.17", "iconsax-react": "^0.0.8", "idb-keyval": "^6.2.1", "js-cookie": "^3.0.5", "jspdf": "^2.5.1", "jszip": "^3.10.1", "localforage": "^1.10.0", "lodash": "^4.17.21", "lucide-react": "^0.454.0", "mammoth": "^1.7.0", "marked": "^16.0.0", "moment": "^2.29.4", "monaco-editor": "^0.49.0", "mqtt": "^5.10.3", "next": "^14.2.32", "nextjs-toploader": "^1.4.2", "pdfjs-dist": "^4.0.379", "postcss": "8.4.29", "puppeteer": "^24.17.0", "puppeteer-core": "^24.17.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-color": "^2.19.3", "react-countdown": "^2.3.5", "react-data-table-component": "^7.7.0", "react-datepicker": "^4.25.0", "react-datetime": "^3.2.0", "react-day-picker": "^8.10.0", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-dropzone": "^14.2.3", "react-file-download": "^0.3.5", "react-flags-select": "^2.2.3", "react-google-autocomplete": "^2.7.3", "react-hook-form": "^7.46.1", "react-hot-toast": "^2.4.1", "react-infinite-scroll-hook": "^4.1.1", "react-intersection-observer": "^9.16.0", "react-loading": "^2.0.3", "react-loading-skeleton": "^3.3.1", "react-multi-carousel": "^2.8.5", "react-pdf": "^8.0.2", "react-pdftotext": "^1.3.3", "react-phone-input-2": "^2.15.1", "react-quill": "^2.0.0", "react-rating": "^2.0.5", "react-rating-stars-component": "^2.2.0", "react-responsive-carousel": "^3.2.23", "react-responsive-masonry": "^2.1.7", "react-select": "^5.7.5", "react-slick": "^0.30.2", "react-slider": "^2.0.6", "react-speech-recognition": "^3.10.0", "react-spinners": "^0.15.0", "react-switch": "^7.1.0", "react-textarea-autosize": "^8.5.3", "react-to-pdf": "^1.0.1", "react-toastify": "^9.1.3", "react-use": "^17.4.2", "react-use-face-detection": "^1.0.1", "react-webcam": "^7.1.1", "react-window": "^1.8.11", "read-excel-file": "^5.7.1", "recharts": "^2.8.0", "regenerator-runtime": "^0.14.0", "slick-carousel": "^1.8.1", "socket.io-client": "^4.7.2", "tailwind-merge": "^1.14.0", "tailwindcss": "3.3.3", "use-debounce": "^10.0.0", "uuid": "^11.0.5", "vaul": "^0.9.0", "yup": "^1.3.3", "zod": "^3.25.76", "zustand": "^4.4.1"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.1.0", "@next/bundle-analyzer": "^13.4.19", "@types/cookie": "^0.6.0", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.14.200", "@types/node": "^24.3.0", "@types/puppeteer": "^5.4.7", "@types/react": "^18.3.3", "@types/react-beautiful-dnd": "^13.1.7", "@types/react-slick": "^0.23.13", "@types/react-speech-recognition": "^3.9.2", "@types/react-window": "^1.8.8", "@types/regenerator-runtime": "^0.13.2", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-check-file": "^2.6.2", "eslint-plugin-tailwindcss": "^3.13.0", "prettier": "^3.0.3", "prettier-plugin-tailwindcss": "^0.5.4", "rakkasjs": "^0.6.20", "tailwindcss-animate": "^1.0.7", "typescript": "^5.9.2", "worker-loader": "^3.0.8"}}