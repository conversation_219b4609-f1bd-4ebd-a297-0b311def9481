<svg width="412" height="160" viewBox="0 0 412 160" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_b_4031_48543)">
<rect width="412" height="160" rx="9" fill="white" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_b_4031_48543" x="-2" y="-2" width="416" height="164" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="1"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_4031_48543"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_4031_48543" result="shape"/>
</filter>
</defs>
</svg>
